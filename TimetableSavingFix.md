# Timetable Saving Fix

## Problem Identified

After analyzing the code, I've identified the issue with saving timetables. The problem is that the frontend is making API requests to two different endpoints:

1. It generates the timetable using `https://timetable.jeridschool.tech/generate-schedule`
2. It attempts to save the timetable using the `dashboardTimetableService.saveTimetable()` function

The issue is in the `dashboardTimetableService.saveTimetable()` function, which is making a POST request to `/timetable` endpoint through the `api` axios instance. This instance is configured to use `http://localhost:3000` as the base URL.

## Solution

### 1. Fix the API URL Configuration

The main issue is that the API requests need to be properly directed to the correct backend service. Here's how to fix it:

1. Ensure the `VITE_API_URL` environment variable is correctly set to `http://localhost:3000`
2. Verify that the axios instance is correctly configured to include authentication tokens

### 2. Update the `dashboardTimetableService.saveTimetable()` Function

The current implementation in `src/lib/api/services/dashboard-timetable-service.ts` needs to be fixed:

```typescript
// Current implementation
saveTimetable: async (timetableData: {
  data: any
  description: string
  academicYear?: string
  isActive?: boolean
}): Promise<Timetable> => {
  try {
    const response = await api.post('/timetable', timetableData)
    return response.data
  } catch (error) {
    // Error handling
  }
}
```

Update it to include more detailed logging and ensure the request is properly formatted:

```typescript
saveTimetable: async (timetableData: {
  data: any
  description: string
  academicYear?: string
  isActive?: boolean
}): Promise<Timetable> => {
  try {
    console.log('Saving timetable with data:', {
      dataSize: JSON.stringify(timetableData.data).length,
      description: timetableData.description,
      academicYear: timetableData.academicYear,
      isActive: timetableData.isActive,
    })

    // Get the token for logging purposes
    const token = localStorage.getItem('access_token')
    console.log(
      'Using token for saving timetable:',
      token ? 'Token exists' : 'No token'
    )

    // Make the request to the NestJS endpoint
    const response = await api.post('/timetable', timetableData)
    console.log('Timetable saved successfully, ID:', response.data.id)
    return response.data
  } catch (error) {
    console.error('Error saving timetable:', error)
    if (error instanceof AxiosError) {
      console.error('Server response:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      })
    }
    throw error
  }
}
```

### 3. Add Debugging to the TimetableGenerator Component

In the `TimetableGenerator.tsx` component, add more detailed logging around the timetable saving process:

```typescript
// After successful generation, save to internal API
try {
  console.log('Attempting to save timetable to database...');
  console.log('Timetable data size:', JSON.stringify(result).length);

  const savedTimetable = await timetableService.saveTimetable({
    name: timetableData.name || 'Generated Timetable',
    data: result,
    // Include the original input data that was sent to port 8000
    originalInputData: externalParams,
    constraints: JSON.stringify(timetableData.constraints || {}),
  });

  console.log('Timetable saved successfully with ID:', savedTimetable.id);

  // Timetable saved to internal API
  toast({
    title: 'Timetable Saved',
    description: 'Your timetable has been saved to the database.',
  });

  // Store the timetable ID in localStorage for reference
  localStorage.setItem('lastSavedTimetableId', savedTimetable.id);

  // Suggest viewing the timetable dashboard
  toast({
    title: 'View All Timetables',
    description: 'Go to the Timetable Dashboard to view all your saved timetables.',
    action: (
      <Button
        variant="outline"
        size="sm"
        onClick={() => (window.location.href = '/admin/timetable-dashboard')}
      >
        Go to Dashboard
      </Button>
    ),
  });
} catch (saveError) {
  console.error('Error saving timetable to internal API:', saveError);
  console.error('Error details:', {
    message: saveError.message,
    response: saveError.response?.data,
    status: saveError.response?.status
  });

  toast({
    title: 'Error Saving Timetable',
    description: 'The timetable was generated but could not be saved to the database. Please try again.',
    variant: 'destructive',
  });
  // Continue even if saving fails
}
```

### 4. Verify the API Endpoint in the Backend

Make sure the backend controller is correctly set up to handle the POST request to `/timetable`. The controller should:

1. Receive the request with the timetable data
2. Extract the user information from the JWT token
3. Save the timetable to the database
4. Return the saved timetable with its ID

### 5. Test the Fix

1. Add console logs to track the request/response flow
2. Use browser developer tools to monitor network requests
3. Check for any errors in the browser console
4. Verify that the timetable is saved in the database

## Implementation Steps

1. Update the `dashboardTimetableService.saveTimetable()` function with the improved implementation
2. Add detailed logging to the `TimetableGenerator.tsx` component
3. Test the timetable generation and saving process
4. Verify that the timetable appears in the timetable dashboard after saving

## Additional Considerations

1. **Data Size**: Make sure the timetable data isn't too large for the API to handle. If it's very large, consider compressing it or splitting it into smaller chunks.

2. **Authentication**: Ensure the JWT token is valid and has the necessary permissions to create timetables.

3. **CORS**: Verify that CORS is properly configured on the backend to allow requests from the frontend.

4. **Error Handling**: Improve error handling to provide more specific error messages to the user.

5. **Retry Logic**: Consider adding retry logic for saving timetables in case of temporary network issues.

By implementing these changes, the timetable saving functionality should work correctly, and timetables should be properly saved to the database.
