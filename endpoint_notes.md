## 📦 Notes Data Structure

Each note is an independent draggable item with its own position and rich content. Notes are stored in PostgreSQL.

### Example Schema:

```ts
{
  id: UUID,
  title: string,
  content: string, // Not JSONB, store as plain TEXT string
  positionX: number,
  positionY: number,
  collectionId: UUID,
  order: number,
  createdAt: timestamp,
  updatedAt: timestamp,
  createdBy: UUID,
  updatedBy: UUID
}
```

✅ **Important:** Do **not** use `JSONB` for the `content` field. Use `TEXT` and store the serialized JSON as a **string** like this:

```json
"content": "
[
  {
    \"type\": \"heading\",
    \"attrs\": { \"level\": 1 },
    \"content\": [{ \"type\": \"text\", \"text\": \"Algebra Basics\" }]
  },
  {
    \"type\": \"paragraph\",
    \"content\": [{ \"type\": \"text\", \"text\": \"Algebra is a branch of mathematics dealing with symbols.\" }]
  }
]
"
```

## 🛠️ Rendering Notes

In the frontend:
* Parse `content` from string to JSON using `JSON.parse(content)`
* Pass the result to the Tiptap editor in read-only or editable mode
* Use absolute positioning or transform-based CSS to place notes on the canvas

## 🖼️ Future Features

* Add support for image upload (with external storage like S3 or Supabase)
* Add support for LaTeX math blocks using KaTeX
* Collaborative editing via sockets or shared documents
* Diagram drawing blocks (optional)

## 🚀 Developer Goals

* Build a flexible, Notion-like note system
* Allow spatial creativity via drag-and-drop
* Keep content editable and secure by using structured data (Tiptap JSON)

## ✅ Summary for Any AI or Developer Working on This

* Do not store the note content as `JSONB`, use `TEXT`
* Store Tiptap content JSON as a **serialized string**
* Make each note a **draggable block** in the UI
* Use Tiptap for editing and rendering content
* Prepare for future features like images and LaTeX

If you're an AI, follow these instructions strictly and keep the structure modular and expandable.


# Notes Module API Endpoints

This document outlines all available endpoints for the Notes module, including request/response formats and authentication requirements.

## Authentication

All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Notes Collections Endpoints

### 1. Create Notes Collection

Creates a new notes collection.

**Endpoint:** `POST /notes/collections`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Request Body:**
```json
{
  "name": "Biology Notes",
  "visibility": "private",
  "topic": "Science"
}
```

**Response:** (201 Created)
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Biology Notes",
  "visibility": "private",
  "topic": "Science",
  "ownerId": "123e4567-e89b-12d3-a456-426614174000",
  "noteCount": 0,
  "createdAt": "2023-11-20T12:00:00.000Z",
  "updatedAt": "2023-11-20T12:00:00.000Z",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "etablissementId": "9e4d7d67-50a1-4e0a-9b82-577d73ef5565"
}
```

### 2. Get All Collections

Retrieves all collections for the current user and public collections.

**Endpoint:** `GET /notes/collections`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Response:** (200 OK)
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Biology Notes",
    "visibility": "private",
    "topic": "Science",
    "ownerId": "123e4567-e89b-12d3-a456-426614174000",
    "noteCount": 2,
    "createdAt": "2023-11-20T12:00:00.000Z",
    "updatedAt": "2023-11-20T12:00:00.000Z",
    "createdBy": "123e4567-e89b-12d3-a456-426614174000",
    "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
    "etablissementId": "9e4d7d67-50a1-4e0a-9b82-577d73ef5565",
    "notes": [...]
  },
  ...
]
```

### 3. Get Collection by ID

Retrieves a specific collection by ID, including its notes.

**Endpoint:** `GET /notes/collections/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Response:** (200 OK)
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Biology Notes",
  "visibility": "private",
  "topic": "Science",
  "ownerId": "123e4567-e89b-12d3-a456-426614174000",
  "noteCount": 2,
  "createdAt": "2023-11-20T12:00:00.000Z",
  "updatedAt": "2023-11-20T12:00:00.000Z",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "etablissementId": "9e4d7d67-50a1-4e0a-9b82-577d73ef5565",
  "notes": [
    {
      "id": "770e8400-e29b-41d4-a716-446655440000",
      "title": "Cell Structure",
      "content": "<h2>Cell Structure</h2><p>The cell is the basic structural unit of all living organisms.</p>",
      "order": 0,
      "collectionId": "550e8400-e29b-41d4-a716-446655440000",
      "createdBy": "123e4567-e89b-12d3-a456-426614174000",
      "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
      "createdAt": "2023-11-20T12:30:00.000Z",
      "updatedAt": "2023-11-20T12:30:00.000Z"
    },
    ...
  ]
}
```

### 4. Update Collection

Updates an existing collection.

**Endpoint:** `PATCH /notes/collections/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Request Body:**
```json
{
  "name": "Updated Biology Notes",
  "visibility": "public",
  "topic": "Biology"
}
```

**Response:** (200 OK)
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Updated Biology Notes",
  "visibility": "public",
  "topic": "Biology",
  "ownerId": "123e4567-e89b-12d3-a456-426614174000",
  "noteCount": 2,
  "createdAt": "2023-11-20T12:00:00.000Z",
  "updatedAt": "2023-11-20T13:00:00.000Z",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "etablissementId": "9e4d7d67-50a1-4e0a-9b82-577d73ef5565"
}
```

### 5. Delete Collection

Deletes a collection and all its notes.

**Endpoint:** `DELETE /notes/collections/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Response:** (200 OK)
No content returned.

## Notes Endpoints

### 1. Create Note

Creates a new note in a collection.

**Endpoint:** `POST /notes/notes`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Request Body:**
```json
{
  "title": "Cell Structure",
  "content": "<h2>Cell Structure</h2><p>The cell is the basic structural unit of all living organisms.</p>",
  "collectionId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Response:** (201 Created)
```json
{
  "id": "770e8400-e29b-41d4-a716-446655440000",
  "title": "Cell Structure",
  "content": "<h2>Cell Structure</h2><p>The cell is the basic structural unit of all living organisms.</p>",
  "order": 0,
  "collectionId": "550e8400-e29b-41d4-a716-446655440000",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "createdAt": "2023-11-20T12:30:00.000Z",
  "updatedAt": "2023-11-20T12:30:00.000Z"
}
```

### 2. Get Note by ID

Retrieves a specific note by ID.

**Endpoint:** `GET /notes/notes/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Response:** (200 OK)
```json
{
  "id": "770e8400-e29b-41d4-a716-446655440000",
  "title": "Cell Structure",
  "content": "<h2>Cell Structure</h2><p>The cell is the basic structural unit of all living organisms.</p>",
  "order": 0,
  "collectionId": "550e8400-e29b-41d4-a716-446655440000",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "createdAt": "2023-11-20T12:30:00.000Z",
  "updatedAt": "2023-11-20T12:30:00.000Z"
}
```

### 3. Update Note

Updates an existing note.

**Endpoint:** `PATCH /notes/notes/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Request Body:**
```json
{
  "title": "Updated Cell Structure",
  "content": "<h2>Cell Structure</h2><p>Updated content with <strong>bold text</strong>.</p>"
}
```

**Response:** (200 OK)
```json
{
  "id": "770e8400-e29b-41d4-a716-446655440000",
  "title": "Updated Cell Structure",
  "content": "<h2>Cell Structure</h2><p>Updated content with <strong>bold text</strong>.</p>",
  "order": 0,
  "collectionId": "550e8400-e29b-41d4-a716-446655440000",
  "createdBy": "123e4567-e89b-12d3-a456-426614174000",
  "updatedBy": "123e4567-e89b-12d3-a456-426614174000",
  "createdAt": "2023-11-20T12:30:00.000Z",
  "updatedAt": "2023-11-20T13:15:00.000Z"
}
```

### 4. Delete Note

Deletes a note.

**Endpoint:** `DELETE /notes/notes/:id`

**Roles:** TEACHER, STUDENT, PARENT, ADMIN, SUPER_ADMIN

**Response:** (200 OK)
No content returned.

## Important Notes for Frontend Implementation

1. **Authentication**: All requests must include a valid JWT token in the Authorization header.

2. **Error Handling**: The API returns appropriate HTTP status codes and error messages:
   - 400: Bad Request (invalid input)
   - 401: Unauthorized (missing or invalid token)
   - 403: Forbidden (insufficient permissions)
   - 404: Not Found (resource doesn't exist)
   - 500: Internal Server Error

3. **Collection Limits**: Users can create a maximum of 10 collections.

4. **Note Limits**: Each collection can contain a maximum of 100 notes.

5. **Note Ordering**: Notes are ordered by the `order` field. When a note is deleted, all subsequent notes in the same collection have their order decremented by 1 to maintain a continuous sequence.

6. **Rich Text Content**: The `content` field of notes supports HTML for rich text formatting.

7. **Visibility**: Collections can be set to "private" (visible only to the owner) or "public" (visible to all users).

8. **Establishment ID**: The API automatically associates collections with the user's establishment.