# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from 'eslint-plugin-react'

export default tseslint.config({
  // Set the react version
  settings: { react: { version: '18.3' } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs['jsx-runtime'].rules,
  },
})
```

## Subdomain Access for Schools

JeridSchool supports accessing schools via subdomains, both in development and production environments.

### Local Development with Subdomains

To access schools via subdomains in local development:

1. Run the setup script to configure your hosts file (requires admin/root privileges):

```bash
# On Linux/macOS
sudo ./setup-local-subdomains.sh

# On Windows (run Command Prompt as Administrator)
bash setup-local-subdomains.sh
```

2. Start the development server:

```bash
npm run dev
```

3. Access your schools using the subdomain format:

```
http://[school-url].localhost:5173
```

For example, if your school has a URL of "myschool", you can access it at:

```
http://myschool.localhost:5173
```

### Production Subdomain Setup

In production, schools are accessed via subdomains of the main domain:

```
https://[school-url].jeridschool.tech
```

The Vercel configuration in `vercel.json` handles routing requests to the appropriate school based on the subdomain.

### Troubleshooting

- **Browser Support**: Some browsers may have issues with wildcard subdomains. If you encounter problems, add specific subdomains to your hosts file:

  ```
  127.0.0.1 myschool.localhost
  ```

- **HTTPS in Development**: If you need HTTPS in development, you'll need to set up a local certificate. Tools like [mkcert](https://github.com/FiloSottile/mkcert) can help with this.

- **DNS Configuration**: For production, ensure your DNS provider supports wildcard subdomains and that you've configured a wildcard DNS record (e.g., `*.jeridschool.tech`) to point to your Vercel deployment.

### Subdomain Debugging Tools

JeridSchool includes built-in tools to help debug subdomain access issues:

1. **Subdomain Test Page**: Navigate to `/subdomain-test` to access a dedicated testing page that allows you to:

   - Check your current authentication status
   - View information about the current URL and subdomain detection
   - Test specific school URLs
   - Test API endpoints directly (both public and protected)

2. **Debug Mode**: Press `Ctrl+Shift+D` on any school page to toggle debug information, which shows:

   - Hostname and subdomain detection status
   - Extracted school name
   - API base URL
   - Authentication token availability
   - API endpoint status (public and protected)

3. **Authentication Handling**: The system will:
   - Try to access the public endpoint first (`/public/etablissement/url/{schoolName}`)
   - If that fails and you're authenticated, it will try the protected endpoint (`/etablissement/url/{schoolName}`)
   - If authentication is required, you'll be prompted to log in

### API Endpoint Access

The application uses two endpoints to fetch school data:

1. **Public Endpoint**: `/public/etablissement/url/{schoolName}`

   - Accessible without authentication
   - Limited school information

2. **Protected Endpoint**: `/etablissement/url/{schoolName}`
   - Requires authentication with a valid JWT token
   - Provides complete school information

The system is designed to gracefully handle both endpoints, prioritizing the public endpoint for better performance and falling back to the protected endpoint when necessary and when authentication is available.

# JeridSchool Main Interface

This is the main interface for the JeridSchool application, built with React, Vite, and TypeScript.

## Environment Setup

Create a `.env` file in the root directory with the following variables:

```
VITE_API_URL=http://localhost:3000
VITE_CDN_UPLOAD_URL=http://localhost:5173/cdn-proxy/cdn/upload
VITE_CDN_BASE_URL=http://localhost:5173/cdn-proxy/cdn
VITE_CDN_TOKEN=5ffaccd4ae0ee9afc5077f30ee32264dab8ca247d86eaed2b42d4dac1e954d9d
VITE_ENABLE_MOCK_CDN=true
VITE_FORCE_MOCK_CDN=false
VITE_FORCE_HTTP_PROTOCOL=true
```

## File Upload Functionality

The application includes a file upload system that allows users to upload images (avatars, logos, etc.) to a CDN server. Here's how it works:

### Components and Services

1. **FileUploadService** (`src/services/fileUploadService.ts`):

   - Handles the actual file uploads to the CDN server
   - Manages authentication with the CDN token
   - Includes fallback to mock uploads when the CDN is unavailable
   - Provides intelligent error detection for CORS and mixed content issues

2. **FileUploader Component** (`src/components/shared/FileUploader.tsx`):
   - Provides a user-friendly UI for file uploads
   - Shows upload progress and success/error states
   - Displays helpful messages when CORS issues are detected
   - Handles retry logic if uploads fail

### CDN Upload Configuration

The file upload system supports these configuration options:

1. **Protocol Configuration**:

   - `VITE_FORCE_HTTP_PROTOCOL=true`: Forces HTTP protocol for CDN URLs (fixes HTTPS mixed content errors)
   - `VITE_FORCE_HTTP_PROTOCOL=false`: Tries to match the protocol of your application

2. **Mock Mode Configuration**:

   - `VITE_ENABLE_MOCK_CDN=true`: Enables fallback to mock CDN if real upload fails
   - `VITE_FORCE_MOCK_CDN=true`: Always uses mock CDN (skips real uploads)
   - Both set to `false`: Always attempts real CDN with no fallback

3. **CORS Proxy Configuration**:
   - The application includes a built-in CORS proxy for the CDN server
   - This proxy is automatically enabled when using the default configuration
   - The proxy adds the necessary CORS headers to requests and responses
   - No additional configuration is needed

### API Response Format

The CDN server returns responses in the following format:

```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "id": "98c502d2-208a-4a19-97bc-f23b617fa40c",
    "filename": "example.png",
    "url": "http://**************/cdn/files/98c502d2-208a-4a19-97bc-f23b617fa40c",
    "size": 45652,
    "mime_type": "image/png",
    "created_at": "2023-03-22T23:48:43.360Z"
  }
}
```

The mock system generates responses that exactly match this format.

### CORS and Mixed Content Issues

The file upload system includes several features to handle common issues:

#### Automatic Error Detection

The system automatically detects:

- CORS errors (Cross-Origin Resource Sharing issues)
- Mixed content errors (HTTP vs HTTPS conflicts)
- Network errors

When these errors are detected, the system:

1. Shows a user-friendly error message
2. Falls back to mock CDN mode if enabled
3. Provides specific console messages for developers

#### Configuration Options for CORS Issues:

1. **Built-in CORS Proxy (Recommended)**:

   - Use the default configuration with CORS proxy enabled
   - CDN URLs use the format: `http://localhost:5173/cdn-proxy/cdn/upload`
   - This allows the application to handle CORS headers automatically
   - Works in both development and production environments

2. **Enable HTTP Protocol Forcing**:

   - Set `VITE_FORCE_HTTP_PROTOCOL=true` in your `.env` file
   - This ensures CDN URLs always use HTTP protocol
   - Helps avoid mixed content errors in browsers with HTTPS-only mode

3. **Enable Fallback Mode**:

   - Set `VITE_ENABLE_MOCK_CDN=true` in your `.env` file
   - The system will attempt real uploads but fall back to mock if CORS issues occur
   - Users will see a "Server connection issue detected" message but can continue working

4. **Force Mock Mode**:

   - Set `VITE_FORCE_MOCK_CDN=true` in your `.env` file
   - This completely bypasses the CDN server
   - Best for development environments where CDN access is not available

5. **Fix the CDN Server** (if you have access):
   - Add these CORS headers to your CDN server:
     ```
     Access-Control-Allow-Origin: *  (or specific origins)
     Access-Control-Allow-Methods: POST, OPTIONS
     Access-Control-Allow-Headers: Content-Type, Authorization
     ```
   - Ensure the CDN server handles OPTIONS preflight requests properly

### Production Setup

For production environments:

1. Configure the production proxy server to add CORS headers for CDN requests
2. Update the `.env` file with production URLs that include your proxy:
   ```
   VITE_CDN_UPLOAD_URL=https://your-domain.com/cdn-proxy/cdn/upload
   VITE_CDN_BASE_URL=https://your-domain.com/cdn-proxy/cdn
   ```
3. Set `VITE_ENABLE_MOCK_CDN=true` for fallback support and `VITE_FORCE_MOCK_CDN=false` to prioritize real uploads
4. Consider setting up HTTPS for your CDN if your application uses HTTPS

## Development Mode

To run the application in development mode:

```
npm run dev
```

## Production Build

To build the application for production:

```
npm run build
```

To preview the production build:

```
npm run preview
```
