#!/bin/bash

# This script helps set up local subdomains for testing
# It adds entries to your hosts file for *.localhost to point to 127.0.0.1

echo "Setting up local subdomains for JeridSchool development..."

# Check if running as root/admin
if [ "$(id -u)" != "0" ]; then
   echo "This script must be run as root/administrator" 
   echo "Please try again with sudo: sudo $0"
   exit 1
fi

# Hosts file location
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    HOSTS_FILE="/etc/hosts"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    HOSTS_FILE="/etc/hosts"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    # Windows
    HOSTS_FILE="/c/Windows/System32/drivers/etc/hosts"
else
    echo "Unsupported operating system"
    exit 1
fi

# Check if hosts file exists
if [ ! -f "$HOSTS_FILE" ]; then
    echo "Hosts file not found at $HOSTS_FILE"
    exit 1
fi

# Check if entry already exists
if grep -q "# JeridSchool local subdomains" "$HOSTS_FILE"; then
    echo "JeridSchool subdomain entries already exist in hosts file"
else
    # Add entries to hosts file
    echo "" >> "$HOSTS_FILE"
    echo "# JeridSchool local subdomains" >> "$HOSTS_FILE"
    echo "127.0.0.1 localhost" >> "$HOSTS_FILE"
    
    # Some browsers don't support wildcard DNS entries, so we'll add specific examples
    echo "127.0.0.1 *.localhost" >> "$HOSTS_FILE"
    echo "127.0.0.1 myschool.localhost" >> "$HOSTS_FILE"
    echo "127.0.0.1 dreamschool.localhost" >> "$HOSTS_FILE"
    echo "127.0.0.1 brightfuture.localhost" >> "$HOSTS_FILE"
    echo "127.0.0.1 ksqdsqd.localhost" >> "$HOSTS_FILE"
    echo "# End of JeridSchool local subdomains" >> "$HOSTS_FILE"
    echo "Added JeridSchool subdomain entries to hosts file"
fi

echo ""
echo "Setup complete! You can now access your schools using subdomains:"
echo "http://[school-url].localhost:5173"
echo ""
echo "Example subdomains that are ready to use:"
echo "- http://myschool.localhost:5173"
echo "- http://dreamschool.localhost:5173"
echo "- http://brightfuture.localhost:5173"
echo "- http://ksqdsqd.localhost:5173"
echo ""
echo "Note: Some browsers may require you to add specific subdomains to your hosts file."
echo "If you need to add more subdomains, edit your hosts file and add lines like:"
echo "127.0.0.1 newschool.localhost"
echo ""
echo "Happy developing!" 