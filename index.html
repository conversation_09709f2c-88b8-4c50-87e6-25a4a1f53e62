<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/LogoV2.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Allow mixed content (HTTP resources on HTTPS pages) -->
    <meta
      http-equiv="Content-Security-Policy"
      content="upgrade-insecure-requests"
    />
    <title>JeridSchool - K12 Education Management System</title>
    <meta
      name="description"
      content="Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication."
    />
    <meta
      name="keywords"
      content="education management, K12, school ERP, student management, teacher management, EdTech, school administration"
    />
    <meta name="author" content="JeridSchool" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://jeridschool.tech/" />
    <meta
      property="og:title"
      content="JeridSchool - K12 Education Management System"
    />
    <meta
      property="og:description"
      content="Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication."
    />
    <meta property="og:image" content="https://jeridschool.tech/og-image.jpg" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://jeridschool.tech/" />
    <meta
      name="twitter:title"
      content="JeridSchool - K12 Education Management System"
    />
    <meta
      name="twitter:description"
      content="Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication."
    />
    <meta
      name="twitter:image"
      content="https://jeridschool.tech/og-image.jpg"
    />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://jeridschool.tech/" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "JeridSchool",
        "applicationCategory": "EducationalApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "description": "Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication.",
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.8",
          "ratingCount": "256"
        }
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script>
      // Check for subdomain in query parameters
      const urlParams = new URLSearchParams(window.location.search)
      const subdomain = urlParams.get('subdomain')

      if (subdomain) {
        // Store the subdomain in sessionStorage for the app to use
        sessionStorage.setItem('subdomain', subdomain)
        console.log('Subdomain detected in query parameters:', subdomain)

        // If we're on the root path with a subdomain parameter, redirect to the school page
        if (
          window.location.pathname === '/' &&
          !sessionStorage.getItem('redirectedFromSubdomain')
        ) {
          sessionStorage.setItem('redirectedFromSubdomain', 'true')
          window.location.href = `/school/${subdomain}`
        }
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
