# Teacher Timetable Backend Requirements

## Overview

The teacher timetable page at `http://localhost:5173/teacher/timetable` displays a teacher's weekly schedule. This document outlines the backend API requirements needed to support this functionality.

## API Endpoint

The frontend makes a GET request to fetch the teacher's timetable data:

```
GET http://localhost:3000/teacher/:teacherId
```

Where `:teacherId` is the UUID of the teacher whose timetable is being requested. This should be a valid UUID that comes from the API, not a string representation.

## Authentication

- The request should include the JWT token in the Authorization header
- The backend should validate that the user has appropriate permissions (Teacher role)
- If the request is made by a teacher, they should only be able to view their own timetable
- If the request is made by an admin or super admin, they should be able to view any teacher's timetable

## Response Format

The API should return a JSON object with the following structure:

```json
{
  "Monday": [
    {
      "time": "08:00 => 09:00",
      "subject": "Mathematics",
      "class": "Grade 10A",
      "salle": "Room 101",
      "teacherID": "5a345976-2189-4431-a665-244b4f4f3a29",
      "teacher": "<PERSON>"
    },
    {
      "time": "10:00 => 11:00",
      "subject": "Physics",
      "class": "Grade 11B",
      "salle": "Lab 3",
      "teacherID": "5a345976-2189-4431-a665-244b4f4f3a29",
      "teacher": "John Doe"
    }
  ],
  "Tuesday": [
    {
      "time": "09:00 => 10:00",
      "subject": "Mathematics",
      "class": "Grade FC",
      "salle": "Room 102",
      "teacherID": "5a345976-2189-4431-a665-244b4f4f3a29",
      "teacher": "John Doe"
    }
  ]
  // Other days of the week...
}
```

### Response Properties

Each lesson object should include:

| Property  | Type   | Description                               |
| --------- | ------ | ----------------------------------------- |
| time      | string | Time slot in format "HH:MM => HH:MM"      |
| subject   | string | Name of the subject                       |
| class     | string | Name of the class                         |
| salle     | string | Room/location where the class takes place |
| teacherID | string | UUID of the teacher (not a number)        |
| teacher   | string | Name of the teacher                       |

## Error Handling

The API should return appropriate HTTP status codes:

- 200 OK: Successful request
- 400 Bad Request: Invalid UUID format for teacher ID
- 401 Unauthorized: Missing or invalid authentication token
- 403 Forbidden: User doesn't have permission to view this timetable
- 404 Not Found: Teacher ID not found
- 500 Internal Server Error: Server-side error

## Implementation Details

### Data Source

The timetable data should be retrieved from the timetable database collection/table. The backend should:

1. Identify the teacher based on the provided ID
2. Query the timetable collection for all lessons assigned to this teacher
3. Organize the lessons by day of the week
4. Return the formatted response

### CRITICAL: Teacher ID Format

It is **extremely important** that the teacher ID used in the timetable data is the actual UUID from the API, not a numeric index.

**INCORRECT** (Do NOT use numeric IDs like this):

```json
{
  "classes": [
    {
      "className": "Grade 3 A",
      "subjects": [
        {
          "subject": "CI/CD",
          "hours": 2,
          "teacherID": 1 // WRONG! This is a numeric index
        }
      ]
    }
  ],
  "teachers": [
    {
      "teacherId": 1, // WRONG! This is a numeric index
      "teacherName": "Khaled Rouissi",
      "subjects": ["Math"]
    }
  ]
}
```

**CORRECT** (Use UUIDs like this):

```json
{
  "classes": [
    {
      "className": "Grade 3 A",
      "subjects": [
        {
          "subject": "CI/CD",
          "hours": 2,
          "teacherID": "5a345976-2189-4431-a665-244b4f4f3a29" // CORRECT! This is a UUID
        }
      ]
    }
  ],
  "teachers": [
    {
      "teacherId": "5a345976-2189-4431-a665-244b4f4f3a29", // CORRECT! This is a UUID
      "teacherName": "Khaled Rouissi",
      "subjects": ["Math"]
    }
  ]
}
```

The UUID must be obtained from the API at `http://localhost:3000` and must match the actual teacher records in the database.

### Dynamic Teacher ID

Currently, the frontend code uses a hardcoded teacher ID:

```javascript
const TEACHER_ID = '5a345976-2189-4431-a665-244b4f4f3a29' // TODO: Replace with dynamic ID if needed
```

This needs to be updated to use the actual UUID of the teacher from the API. The backend should be prepared to handle:

1. Requests with a specific teacher UUID in the URL
2. Requests without a teacher ID, in which case it should use the UUID from the authenticated user's JWT token

**Important:** The teacherId must be a valid UUID format that matches the teacher records in the database, not a string or numeric ID.

### Performance Considerations

- The endpoint should be optimized for quick response times
- Consider implementing caching for timetable data that doesn't change frequently
- Ensure database queries are efficient, using appropriate indexes

## Example Implementation (Pseudo-code)

```javascript
// Express.js example
router.get('/teacher/:teacherId?', authenticateJWT, async (req, res) => {
  try {
    // Get teacher UUID from params or from authenticated user
    const teacherId = req.params.teacherId || req.user.id

    // Validate UUID format
    if (!isValidUUID(teacherId)) {
      return res
        .status(400)
        .json({ error: 'Invalid teacher ID format. Must be a valid UUID.' })
    }

    // Check permissions
    if (
      req.user.role !== 'Admin' &&
      req.user.role !== 'SuperAdmin' &&
      req.user.id !== teacherId
    ) {
      return res
        .status(403)
        .json({ error: 'Forbidden: You can only view your own timetable' })
    }

    // Fetch timetable data from database
    const timetableData = await TimetableModel.findByTeacherId(teacherId)

    if (!timetableData) {
      return res
        .status(404)
        .json({ error: 'Timetable not found for this teacher' })
    }

    // Format data by day of week
    const formattedData = formatTimetableByDay(timetableData)

    // Return formatted data
    return res.status(200).json(formattedData)
  } catch (error) {
    console.error('Error fetching teacher timetable:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
})

// Helper function to validate UUID format
function isValidUUID(uuid) {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

// IMPORTANT: When fetching timetable data, ensure all teacher references use UUIDs, not numeric IDs
// For example, when building the response:
function formatTimetableByDay(timetableData) {
  // WRONG: Using numeric IDs
  // lesson.teacherID = 1;
  // CORRECT: Using UUIDs from the database
  // lesson.teacherID = "5a345976-2189-4431-a665-244b4f4f3a29";
  // Format and return the data...
}
```

## Timetable Generation Payload

When generating timetables, the payload sent to the timetable generation service must use UUIDs for teacher IDs. Here's an example of the correct format:

```json
{
  "timeSlots": {
    "Monday": {
      "studyTimes": [
        "8 => 9",
        "9 => 10",
        "10 => 11",
        "11 => 12",
        "13 => 14",
        "14 => 15",
        "15 => 16"
      ],
      "restTime": "12 => 13"
    },
    "Tuesday": {
      "studyTimes": [
        "8 => 9",
        "9 => 10",
        "10 => 11",
        "11 => 12",
        "13 => 14",
        "14 => 15",
        "15 => 16"
      ],
      "restTime": "12 => 13"
    }
    // Other days...
  },
  "salles": [
    {
      "name": "LAB210",
      "type": "room"
    },
    {
      "name": "salle1",
      "type": "room"
    }
  ],
  "classes": [
    {
      "className": "Grade 3 A",
      "subjects": [
        {
          "subject": "CI/CD",
          "hours": 2,
          "teacherID": "5a345976-2189-4431-a665-244b4f4f3a29" // UUID, not a number
        }
      ]
    }
  ],
  "teachers": [
    {
      "teacherId": "5a345976-2189-4431-a665-244b4f4f3a29", // UUID, not a number
      "teacherName": "Khaled Rouissi",
      "subjects": ["Math"],
      "unavailableTimes": {
        "Monday": [],
        "Tuesday": []
        // Other days...
      },
      "minimumHours": 10,
      "maximumHours": 20
    }
  ]
}
```

## Testing

The backend implementation should be tested for:

1. Authentication and authorization
2. Correct data formatting
3. Error handling
4. Performance under load
5. Proper handling of UUID teacher IDs

## Integration with Frontend

The frontend expects the data in the exact format specified above. Any changes to the response format will require corresponding updates to the frontend code in `src/components/teacherPage/Timetable.tsx`.

### Required Frontend Changes

The current frontend implementation uses a hardcoded teacher ID. This needs to be updated to:

1. Get the teacher ID from the authenticated user's context or profile
2. Update the API call to use this dynamic ID:

```javascript
// Current implementation (needs to be changed)
axios.get <
  TimetableAPIResponse >
  `http://localhost:3000/teacher/${TEACHER_ID}`

// Recommended implementation
const { user } = useAuth() // Or similar auth hook
axios.get <
  TimetableAPIResponse >
  `http://localhost:3000/teacher/${user.id}`
```

This ensures that the teacher sees their own timetable based on their authenticated user ID.
