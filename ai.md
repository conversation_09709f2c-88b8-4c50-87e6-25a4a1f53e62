# 🚀 JeridSchool API Endpoints Quick Reference

## 📍 Base Configuration
- **API Base URL**: `http://localhost:8000`
- **Documentation**: `http://localhost:8000/docs`
- **Authentication**: Bearer Token in Authorization header 

## 🔑 Core API Endpoints

### 1. 🧠 Main AI Processing
```
POST /api/process_ai
```
**Purpose**: Process YouTube URLs with AI to generate educational content
**Request Body**:
```json
{
  "youtube_url": "https://www.youtube.com/watch?v=...",
  "custom_prompt": "Create a quiz focusing on...",
  "user_id": "user123"
}
```
**Response**:
```json
{
  "quiz": [
    {
      "question": "What is...?",
      "options": ["A", "B", "C", "D"],
      "answer": "A"
    }
  ],
  "flashcards": [
    {
      "front": "Term",
      "back": "Definition"
    }
  ],
  "notes": "Comprehensive notes...",
  "status": "success"
}
```

### 2. 📁 File Processing
```
POST /api/process_ai/file
```
**Purpose**: Process uploaded files with AI
**Content-Type**: `multipart/form-data`
**Form Data**:
- `file`: File upload (PDF, DOC, DOCX, TXT, CSV, JPG, PNG)
- `user_id`: User identifier
- `custom_prompt`: Optional custom prompt

### 3. 💬 Chatbot
```
POST /api/chatbot
```
**Purpose**: Interactive AI assistant for content questions
**Request Body**:
```json
{
  "user_id": "user123",
  "message": "Explain the main concepts",
  "session_id": "optional-session-id"
}
```
**Response**:
```json
{
  "reply": "AI response...",
  "session_id": "session-uuid",
  "status": "success"
}
```

### 4. 🎤 Transcription Services

#### Basic Transcription
```
POST /api/transcribe/
```
**Request Body**:
```json
{
  "youtube_url": "https://www.youtube.com/watch?v=...",
  "save_transcript": true
}
```

#### Transcription with Notes
```
POST /api/transcribe/with_notes
```
**Response includes**: transcript + AI-generated notes

### 5. 📄 Document Processing (RAG)

#### PDF Processing
```
POST /api/rag/pdf
```
**Content-Type**: `multipart/form-data`

#### Image Processing
```
POST /api/rag/image
```
**Request Body**:
```json
{
  "image_data": "base64-encoded-image",
  "image_format": "png"
}
```

#### Document Query
```
POST /api/rag/query
```
**Request Body**:
```json
{
  "question": "What does the document say about...?"
}
```

#### JSON Processing
```
POST /api/rag/json
```
**Request Body**:
```json
{
  "content": {
    "key": "value"
  }
}
```

## 🔍 Additional Endpoints

### Health Check
```
GET /
```
**Response**:
```json
{
  "message": "Welcome to JeridSchool PDF-to-AI API",
  "version": "1.0.0",
  "status": "running"
}
```

### Chat Session Management
```
GET /api/chatbot/sessions/{user_id}
DELETE /api/chatbot/sessions/{session_id}
```

### Document Status
```
GET /api/rag/status
```

## 🛡️ Authentication Headers
```javascript
{
  "Authorization": "Bearer <your-token>",
  "Content-Type": "application/json" // or multipart/form-data for files
}
```

## 📊 Response Status Codes
- **200**: Success
- **400**: Bad Request (missing required fields)
- **401**: Unauthorized (invalid token)
- **422**: Validation Error
- **500**: Internal Server Error

## 🎯 Frontend Integration Tips

1. **Always include Authorization header** with Bearer token
2. **Handle loading states** - AI processing can take 30-60 seconds
3. **Implement error handling** for network and API errors
4. **Use FormData** for file uploads, JSON for other requests
5. **Store session IDs** for chatbot continuity
6. **Validate file types** before upload
7. **Show progress indicators** for long-running operations

## 🔄 Typical Workflow

1. **User uploads file or enters YouTube URL**
2. **Frontend calls** `/api/process_ai` or `/api/process_ai/file`
3. **Backend processes** content with AI (30-60 seconds)
4. **Frontend displays** quiz, flashcards, and notes
5. **User can chat** about content using `/api/chatbot`
6. **Session persists** for continued interaction

## 🧪 Testing
- Use the test interface at `/ui/index.html`
- Check API documentation at `/docs`
- Monitor logs for debugging
- Test with various file types and YouTube URLs

---
**Note**: All endpoints require authentication. Ensure your NestJS middleware properly forwards the Bearer token to the FastAPI backend.
# 🎓 JeridSchool Frontend Implementation Guide

## 📋 Overview

This guide provides comprehensive information for implementing the frontend features for the JeridSchool PDF-to-AI application. The backend API is built with FastAPI and provides AI-powered educational content generation from multiple input sources.

## 🏗️ System Architecture

```
Frontend (React/Next.js) → NestJS Middleware → FastAPI Backend → MongoDB
                                           ↓
                                    Azure OpenAI GPT-3.5-Turbo
                                    OpenAI Whisper (Transcription)
                                    IBM Docling (Document Processing)
```

## 🔗 API Base Configuration

- **Base URL**: `http://localhost:8000`
- **API Documentation**: `http://localhost:8000/docs`
- **Authentication**: Bearer Token (passed from NestJS middleware)
- **Content-Type**: `application/json` for most endpoints, `multipart/form-data` for file uploads

## 🛡️ Authentication

All API requests require:
- **Authorization Header**: `Bearer <token>`
- **User ID**: Included in request body or as parameter
- Token validation is handled by the backend middleware

Example headers:
```javascript
const headers = {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json'
}
```

## 📊 Core Features to Implement

### 1. 🧠 AI Content Processing

#### Main Processing Endpoint
- **Endpoint**: `POST /api/process_ai`
- **Purpose**: Process YouTube URLs to generate educational content
- **Features**: Quiz generation, flashcards creation, notes summarization

#### Request Schema:
```typescript
interface ProcessAIRequest {
  youtube_url?: string;
  custom_prompt?: string;
  user_id: string;
}
```

#### Response Schema:
```typescript
interface QuizItem {
  question: string;
  options: string[];
  answer: string;
}

interface FlashcardItem {
  front: string;
  back: string;
}

interface ProcessAIResponse {
  quiz: QuizItem[];
  flashcards: FlashcardItem[];
  notes: string;
  status: string;
  message?: string;
}
```

#### Implementation Example:
```javascript
const processYouTubeContent = async (youtubeUrl, customPrompt, userId, token) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/process_ai`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        youtube_url: youtubeUrl,
        custom_prompt: customPrompt,
        user_id: userId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error processing YouTube content:', error);
    throw error;
  }
};
```

### 2. 📁 File Upload Processing

#### File Processing Endpoint
- **Endpoint**: `POST /api/process_ai/file`
- **Purpose**: Process uploaded files (PDF, DOC, images) with AI
- **Supported Formats**: PDF, DOC, DOCX, TXT, CSV, JPG, PNG

#### Request Schema:
```typescript
interface FileProcessRequest {
  file: File;
  custom_prompt?: string;
  user_id: string;
}
```

#### Implementation Example:
```javascript
const processFile = async (file, customPrompt, userId, token) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId);
    if (customPrompt) {
      formData.append('custom_prompt', customPrompt);
    }

    const response = await fetch(`${API_BASE_URL}/api/process_ai/file`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
        // Don't set Content-Type for FormData, let browser set it
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error processing file:', error);
    throw error;
  }
};
```

### 3. 💬 Interactive Chatbot

#### Chatbot Endpoint
- **Endpoint**: `POST /api/chatbot`
- **Purpose**: Interactive AI assistant for content-related questions
- **Features**: Session management, context awareness, conversation history

#### Request Schema:
```typescript
interface ChatbotRequest {
  user_id: string;
  message: string;
  session_id?: string;
}
```

#### Response Schema:
```typescript
interface ChatbotResponse {
  reply: string;
  session_id: string;
  status: string;
  message?: string;
}
```

#### Implementation Example:
```javascript
const sendChatMessage = async (message, userId, sessionId, token) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/chatbot`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: userId,
        message: message,
        session_id: sessionId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error sending chat message:', error);
    throw error;
  }
};
```

### 4. 🎤 Transcription Services

#### Basic Transcription
- **Endpoint**: `POST /api/transcribe/`
- **Purpose**: Transcribe YouTube videos only

#### Transcription with Notes
- **Endpoint**: `POST /api/transcribe/with_notes`
- **Purpose**: Transcribe and generate AI notes

#### Request Schema:
```typescript
interface TranscribeRequest {
  youtube_url: string;
  save_transcript: boolean;
}
```

#### Response Schema:
```typescript
interface TranscribeResponse {
  text: string;
  success: boolean;
  video_id: string;
  json_file?: string;
  pdf_file?: string;
}
```

### 5. 📄 Document Processing (RAG)

#### PDF Processing
- **Endpoint**: `POST /api/rag/pdf`
- **Purpose**: Process PDF documents for RAG queries

#### Image Processing
- **Endpoint**: `POST /api/rag/image`
- **Purpose**: Process images with OCR

#### Document Query
- **Endpoint**: `POST /api/rag/query`
- **Purpose**: Query processed documents

#### JSON Processing
- **Endpoint**: `POST /api/rag/json`
- **Purpose**: Process JSON content

## 🎨 UI Components to Implement

### 1. Main Dashboard
- **Features**: Navigation, API status indicator, user info
- **Components**: Header, sidebar navigation, status badges

### 2. AI Processing Interface
- **Input Fields**:
  - YouTube URL input
  - Custom prompt textarea
  - File upload dropzone
- **Output Display**:
  - Quiz questions with multiple choice
  - Interactive flashcards (flip animation)
  - Formatted notes display
- **Status Indicators**: Loading states, progress bars

### 3. Chatbot Interface
- **Components**:
  - Chat message container
  - Message input with send button
  - Session management
  - Chat history display
- **Features**:
  - Real-time messaging
  - Message timestamps
  - Session persistence
  - Clear chat functionality

### 4. File Upload Component
- **Features**:
  - Drag & drop interface
  - File type validation
  - Upload progress indicator
  - File preview
- **Supported Types**: PDF, DOC, DOCX, TXT, CSV, JPG, PNG

### 5. Results Display Components

#### Quiz Component
```jsx
const QuizComponent = ({ quizItems }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [showResult, setShowResult] = useState(false);

  return (
    <div className="quiz-container">
      <div className="question-header">
        <h5>Question {currentQuestion + 1} of {quizItems.length}</h5>
      </div>
      <div className="question-content">
        <p>{quizItems[currentQuestion].question}</p>
        <div className="options">
          {quizItems[currentQuestion].options.map((option, index) => (
            <button
              key={index}
              className={`option-btn ${selectedAnswer === option ? 'selected' : ''}`}
              onClick={() => setSelectedAnswer(option)}
            >
              {option}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
```

#### Flashcard Component
```jsx
const FlashcardComponent = ({ flashcards }) => {
  const [currentCard, setCurrentCard] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);

  return (
    <div className="flashcard-container">
      <div className={`flashcard ${isFlipped ? 'flipped' : ''}`}
           onClick={() => setIsFlipped(!isFlipped)}>
        <div className="flashcard-front">
          <p>{flashcards[currentCard].front}</p>
        </div>
        <div className="flashcard-back">
          <p>{flashcards[currentCard].back}</p>
        </div>
      </div>
      <div className="flashcard-controls">
        <button onClick={() => setCurrentCard(Math.max(0, currentCard - 1))}>
          Previous
        </button>
        <span>{currentCard + 1} / {flashcards.length}</span>
        <button onClick={() => setCurrentCard(Math.min(flashcards.length - 1, currentCard + 1))}>
          Next
        </button>
      </div>
    </div>
  );
};
```

## 🎯 State Management Recommendations

### React Context for Global State
```javascript
const AppContext = createContext();

const AppProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [apiStatus, setApiStatus] = useState('disconnected');
  const [chatSessions, setChatSessions] = useState({});
  const [processedContent, setProcessedContent] = useState(null);

  return (
    <AppContext.Provider value={{
      user, setUser,
      apiStatus, setApiStatus,
      chatSessions, setChatSessions,
      processedContent, setProcessedContent
    }}>
      {children}
    </AppContext.Provider>
  );
};
```

### Local Storage for Persistence
```javascript
// Save chat sessions
const saveChatSession = (sessionId, messages) => {
  localStorage.setItem(`chat_${sessionId}`, JSON.stringify(messages));
};

// Save user preferences
const saveUserPreferences = (preferences) => {
  localStorage.setItem('user_preferences', JSON.stringify(preferences));
};
```

## 🔄 Error Handling

### API Error Handler
```javascript
const handleApiError = (error, context) => {
  console.error(`Error in ${context}:`, error);

  if (error.status === 401) {
    // Handle authentication error
    redirectToLogin();
  } else if (error.status === 429) {
    // Handle rate limiting
    showToast('Too many requests. Please wait.', 'warning');
  } else if (error.status >= 500) {
    // Handle server errors
    showToast('Server error. Please try again later.', 'error');
  } else {
    // Handle other errors
    showToast(error.message || 'An error occurred', 'error');
  }
};
```

### Loading States
```javascript
const useApiCall = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const makeApiCall = async (apiFunction, ...args) => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiFunction(...args);
      return result;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { loading, error, makeApiCall };
};
```

## 📱 Responsive Design Guidelines

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Mobile-First Approach
```css
/* Mobile styles first */
.quiz-container {
  padding: 1rem;
  margin: 0.5rem;
}

/* Tablet styles */
@media (min-width: 768px) {
  .quiz-container {
    padding: 2rem;
    margin: 1rem;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .quiz-container {
    padding: 3rem;
    margin: 2rem;
    max-width: 1200px;
  }
}
```

## 🚀 Performance Optimization

### Code Splitting
```javascript
// Lazy load components
const ChatbotComponent = lazy(() => import('./components/Chatbot'));
const QuizComponent = lazy(() => import('./components/Quiz'));
const FlashcardComponent = lazy(() => import('./components/Flashcard'));
```

### API Caching
```javascript
// Simple cache implementation
const apiCache = new Map();

const cachedApiCall = async (url, options, cacheTime = 5 * 60 * 1000) => {
  const cacheKey = `${url}_${JSON.stringify(options)}`;
  const cached = apiCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < cacheTime) {
    return cached.data;
  }

  const data = await fetch(url, options).then(res => res.json());
  apiCache.set(cacheKey, { data, timestamp: Date.now() });

  return data;
};
```

## 🧪 Testing Recommendations

### Unit Tests
```javascript
// Test API functions
describe('API Functions', () => {
  test('processYouTubeContent should return valid response', async () => {
    const mockResponse = {
      quiz: [{ question: 'Test?', options: ['A', 'B'], answer: 'A' }],
      flashcards: [{ front: 'Test', back: 'Answer' }],
      notes: 'Test notes'
    };

    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
    );

    const result = await processYouTubeContent('test-url', null, 'user123', 'token');
    expect(result).toEqual(mockResponse);
  });
});
```

### Integration Tests
```javascript
// Test component integration
describe('Quiz Component Integration', () => {
  test('should display quiz questions and handle answers', () => {
    const mockQuiz = [
      { question: 'Test question?', options: ['A', 'B', 'C'], answer: 'A' }
    ];

    render(<QuizComponent quizItems={mockQuiz} />);

    expect(screen.getByText('Test question?')).toBeInTheDocument();
    expect(screen.getByText('A')).toBeInTheDocument();
  });
});
```

## 📋 Implementation Checklist

### Phase 1: Core Setup
- [ ] Set up React/Next.js project structure
- [ ] Configure API client with authentication
- [ ] Implement basic routing and navigation
- [ ] Create reusable UI components
- [ ] Set up state management (Context/Redux)

### Phase 2: Main Features
- [ ] AI Processing interface (YouTube URL + file upload)
- [ ] Results display (Quiz, Flashcards, Notes)
- [ ] Chatbot interface with session management
- [ ] File upload with drag & drop
- [ ] Error handling and loading states

### Phase 3: Advanced Features
- [ ] Transcription services integration
- [ ] Document RAG functionality
- [ ] User preferences and settings
- [ ] Offline support and caching
- [ ] Performance optimization

### Phase 4: Polish & Testing
- [ ] Responsive design implementation
- [ ] Accessibility improvements
- [ ] Unit and integration tests
- [ ] User experience enhancements
- [ ] Documentation and deployment

## 🔧 Development Tools

### Recommended Libraries
- **UI Framework**: React with Next.js or Create React App
- **Styling**: Tailwind CSS or Material-UI
- **State Management**: React Context API or Redux Toolkit
- **HTTP Client**: Axios or native fetch
- **Form Handling**: React Hook Form
- **Testing**: Jest + React Testing Library
- **Animation**: Framer Motion or React Spring

### Development Environment
- **Node.js**: v18+ recommended
- **Package Manager**: npm or yarn
- **Code Editor**: VS Code with React extensions
- **Browser DevTools**: React Developer Tools

## 📞 API Health Check

### Health Check Endpoint
```javascript
const checkApiHealth = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/`);
    const data = await response.json();

    if (data.status === 'running') {
      setApiStatus('connected');
      return true;
    }
  } catch (error) {
    setApiStatus('disconnected');
    return false;
  }
};
```

## 🎯 Next Steps

1. **Review this guide** with your development team
2. **Set up the development environment** with recommended tools
3. **Start with Phase 1** implementation (core setup)
4. **Test each feature** against the running FastAPI backend
5. **Iterate and improve** based on user feedback

## 📚 Additional Resources

- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **React Documentation**: https://react.dev/
- **API Testing**: Use the existing UI at `http://localhost:3000` for reference
- **Backend Code**: Review the `/app` directory for implementation details

---

**Note**: This guide assumes the FastAPI backend is running on `http://localhost:8000`. Adjust the base URL according to your deployment environment.

For questions or clarifications, refer to the backend API documentation at `/docs` endpoint or review the existing test interface in the `/ui` directory.
