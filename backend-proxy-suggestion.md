# Backend Proxy Endpoint for CORS Issues

To resolve the CORS issues with images from cdn.jeridschool.tech, we recommend adding a simple proxy endpoint to your backend API.

## Implementation Suggestion

Add the following endpoint to your backend API:

```typescript
// In your Express.js or NestJS backend
import axios from 'axios'

// Add this endpoint to your routes
app.get('/proxy', async (req, res) => {
  try {
    const url = req.query.url as string

    // Validate the URL to prevent abuse
    if (!url || typeof url !== 'string') {
      return res.status(400).send('Invalid URL parameter')
    }

    // Only allow proxying from trusted domains
    if (!url.startsWith('https://cdn.jeridschool.tech/')) {
      return res
        .status(403)
        .send('Forbidden: Only cdn.jeridschool.tech URLs are allowed')
    }

    // Fetch the resource
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
    })

    // Set appropriate headers
    const contentType = response.headers['content-type']
    res.setHeader('Content-Type', contentType)
    res.setHeader('Access-Control-Allow-Origin', '*')

    // Send the proxied content
    res.send(response.data)
  } catch (error) {
    console.error('Proxy error:', error)
    res.status(500).send('Error proxying resource')
  }
})
```

## Usage in Frontend

The frontend can then use this proxy by replacing:

```
https://cdn.jeridschool.tech/path/to/image.jpg
```

with:

```
http://localhost:3000/proxy?url=https://cdn.jeridschool.tech/path/to/image.jpg
```

## Alternative: Configure CORS on CDN Server

A better long-term solution would be to configure the CDN server to include the proper CORS headers:

```
Access-Control-Allow-Origin: https://jeridschool.tech
```

This would allow direct access to the CDN resources without needing a proxy.
