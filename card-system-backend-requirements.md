# Backend Requirements for Card System

## Card Entity Structure

```typescript
// Card entity in TypeORM
interface Card {
  id: string // UUID primary key
  themeNumber: number // Theme number (1, 2, 3, etc.)
  colorNumber: number // Color number (1, 2, 3, etc.)
  preferences: {
    theme: {
      id: string // e.g., 'red-classic', 'blue-professional'
      name: string // e.g., 'Red Classic', 'Blue Professional'
      primaryColor: string // Hex color code
      secondaryColor: string // Hex color code
      accentColor: string // Hex color code
      textColor: string // Hex color code
    }
    fontStyle?: string // e.g., 'modern', 'classic'
    additionalInfo?: string // e.g., 'Valid until DEC 2024'
    customFields?: {
      cardLayout: 'horizontal' | 'vertical'
      showQRCode: boolean
      [key: string]: any // Other custom fields
    }
  }
  etablissement: Establishment // Many-to-one relationship with Establishment
}
```

## API Endpoints

### 1. Get Card for Establishment

- `GET /establishments/:establishmentId/card`
- Returns the card configuration for a specific establishment

### 2. Create Card for Establishment

- `POST /establishments/:establishmentId/card`
- Creates a new card configuration for an establishment
- Request body:
  ```json
  {
    "themeNumber": 1,
    "colorNumber": 1,
    "preferences": {
      "theme": {
        "id": "red-classic",
        "name": "Red Classic",
        "primaryColor": "#c41e3a",
        "secondaryColor": "#f5f5f5",
        "accentColor": "#ffcc00",
        "textColor": "#ffffff"
      },
      "fontStyle": "modern",
      "additionalInfo": "Valid until DEC 2024",
      "customFields": {
        "cardLayout": "horizontal",
        "showQRCode": true
      }
    }
  }
  ```

### 3. Update Card for Establishment

- `PATCH /establishments/:establishmentId/card`
- Updates an existing card configuration
- Request body: Same as create endpoint

### 4. Delete Card for Establishment

- `DELETE /establishments/:establishmentId/card`
- Deletes a card configuration

## Card Generation Endpoints

### 1. Generate Card for User

- `GET /users/:userId/card`
- Generates a card for a specific user based on their establishment's card configuration
- Returns HTML that can be rendered or converted to PDF

### 2. Download Card as PDF

- `GET /users/:userId/card/download`
- Generates and returns a PDF version of the user's card

## Database Relationships

- Each establishment can have one card configuration
- The card configuration is used to generate cards for all users in that establishment
- The card entity has a many-to-one relationship with the establishment entity
