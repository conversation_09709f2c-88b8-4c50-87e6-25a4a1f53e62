// Test script to verify subject PATCH functionality
// Run with: node test-subject-patch.js

const fetch = require('node-fetch');

// Configuration
const API_URL = 'http://localhost:3000'; // Change to your API URL
const SUBJECT_ID = '989d2c21-3006-42e5-8c19-937027fadc65'; // Change to your subject ID
const ACCESS_TOKEN = 'YOUR_ACCESS_TOKEN'; // Replace with your access token

// Data to send in the PATCH request
const updateData = {
  name: 'Physic',
  teacherIds: []
};

// Make the PATCH request
async function testPatchSubject() {
  try {
    console.log(`Testing PATCH request to ${API_URL}/subject/${SUBJECT_ID}`);
    console.log('Request data:', JSON.stringify(updateData, null, 2));
    
    const response = await fetch(`${API_URL}/subject/${SUBJECT_ID}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ACCESS_TOKEN}`
      },
      body: JSON.stringify(updateData)
    });
    
    const responseStatus = response.status;
    console.log(`Response status: ${responseStatus}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
      console.log('PATCH request successful!');
    } else {
      const errorText = await response.text();
      console.error('PATCH request failed:', errorText);
    }
  } catch (error) {
    console.error('Error making PATCH request:', error);
  }
}

// Run the test
testPatchSubject();
