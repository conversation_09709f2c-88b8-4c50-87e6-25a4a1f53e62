You are a frontend developer AI assistant. I need you to generate a web interface using the following stack:

**Tech stack**:
- React + TypeScript
- TanStack Router (v1+)
- TanStack Form (if there are forms)
- Tailwind CSS (Primary color: Blue; Secondary color: Yellow)
- shadcn UI components
- Use TanStack Query for any async mock data handling

**Folder structure and routes**:
Each role has its own route file:
- `/routes/teacher/exam.tsx`
- `/routes/student/exam.tsx`
- `/routes/admin/exam.tsx`

**Goal**:
Create a simple educational exam dashboard for three user roles:
1. **Teacher**
2. **Student**
3. **Admin**

Use mock data (array of students, subjects, and scores). Respect the routes and split components properly.

---

### 👨‍🏫 Teacher UI

Route: `/routes/teacher/exam.tsx`

1. Display a grid of **classes as cards**.
2. On clicking a class, show **subjects taught**.
3. On subject click, show **students of that class** and allow **editing scores** via double click.

#### Table format:

| Name | Control 1 | Control 2 | Syntas 1 | Syntas 2 | TP1 | TP2 | Oral 1 | Oral 2 |
|------|-----------|-----------|----------|----------|-----|-----|--------|--------|
| x    | 19        | 15        | 16       | bull     | 14  |     |        |        |

Editable cells on double click (use shadcn editable table or custom solution).

---

### 👨‍🎓 Student UI

Route: `/routes/student/exam.tsx`

Show a static table of subjects and scores.

#### Table format:

| Subject | Control 1 | Control 2 | Syntas 1 | Syntas 2 | TP1 | TP2 | Oral 1 | Oral 2 |
|---------|-----------|-----------|----------|----------|-----|-----|--------|--------|
| Math    | 19        | 15        | 16       | bull     | 14  |     |        |        |
| Art     | ...       | ...       | ...      | ...      | ... | ... | ...    | ...    |

Read-only table (shadcn table component).

---

### 🧑‍💼 Admin UI

Route: `/routes/admin/exam.tsx`

1. Select class (cards like Teacher).
2. Select subject.
3. See the **editable score table like Teacher**, but with an **extra column for "Moyenne"**.

| Name | Control 1 | Control 2 | Syntas 1 | Syntas 2 | TP1 | TP2 | Oral 1 | Oral 2 | Moyenne |
|------|-----------|-----------|----------|----------|-----|-----|--------|--------|---------|
| x    | 19        | 15        | 16       | bull     | 14  |     |        |        | 17      |

- Admin sees a **form field** per row to enter the "Moyenne".
- Auto-calculation supported with a "Calculate All" button.

---

### 🧩 Components

Split into reusable components:
- `ClassCard`
- `SubjectSelector`
- `StudentScoreTable`
- `EditableCell` (with TanStack Form)
- `ScoreFormProvider` (TanStack Form context)

Use ShadCN components where possible (e.g., table, input, card, select).

---

### 📦 Mock Data

Generate dummy data:
```ts
// example for teacher route
const classes = [{ id: 'class-a', name: 'Class A' }, { id: 'class-b', name: 'Class B' }];

const subjects = [{ id: 'math', name: 'Math' }, { id: 'art', name: 'Art' }];

const students = [
  { id: 'stu1', name: 'Alice', scores: { control1: 19, control2: 15, syntas1: 16, syntas2: 'bull', tp1: 14 } },
  { id: 'stu2', name: 'Bob', scores: { control1: 14, control2: 10, syntas1: 12 } },
];
