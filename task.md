# i18n Implementation Task

## Objective
Update all React components to use i18n translation variables instead of hardcoded text strings.

## Instructions

For each file in the codebase, please:

1. Import the necessary i18n hooks at the top of the file:
   ```javascript
   import { useTranslation } from 'react-i18next';
   ```

2. Add the translation hook inside the component:
   ```javascript
   const { t } = useTranslation();
   ```

3. Replace all hardcoded text strings with t() function calls using appropriate namespaced keys.
   For example, change:
   ```javascript
   <h1>Access Denied</h1>
   ```
   to:
   ```javascript
   <h1>{t('routes.unauthorized.title')}</h1>
   ```

4. Use the translation keys we've already defined in our translation files. The keys should follow the structure we've established in our JSON files.

5. For text that includes variables, use the interpolation syntax:
   ```javascript
   {t('keyName', { variableName: variableValue })}
   ```

6. Maintain all existing functionality, component structure, and styling.

7. If you need to add new translation keys, follow our existing naming convention and structure.

8. For each file, also provide the corresponding JSON translation entries that should be added to our translation files.

## Example Transformation

### Before:
```jsx
return (
  <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
    <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
      <div className="flex justify-center mb-4">
        <ShieldAlert size={64} className="text-red-500" />
      </div>

      <h1 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h1>
      <p className="text-gray-600 mb-6">
        Sorry, you don't have permission to access this page. This area
        requires different user privileges.
      </p>

      <div className="p-4 bg-gray-50 rounded-md mb-6 text-left">
        <p className="text-sm font-medium text-gray-700">
          Current Role:{' '}
          <span className="font-bold text-blue-600">{role}</span>
        </p>
        <p className="text-sm text-gray-500 mt-1">
          If you believe you should have access to this page, please contact
          your administrator.
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={goBack}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
        >
          <ArrowLeft size={16} />
          Go Back
        </button>

        <a
          href="/"
          className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center justify-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Home size={16} />
          Home Page
        </a>
      </div>
    </div>
  </div>
)
```

### After:
```jsx
import { useTranslation } from 'react-i18next';
import { ShieldAlert, ArrowLeft, Home } from 'lucide-react';

const AccessDeniedPage = ({ role, goBack }) => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <div className="flex justify-center mb-4">
          <ShieldAlert size={64} className="text-red-500" />
        </div>

        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          {t('common.accessDenied.title')}
        </h1>

        <p className="text-gray-600 mb-6">
          {t('common.accessDenied.description')}
        </p>

        <div className="p-4 bg-gray-50 rounded-md mb-6 text-left">
          <p className="text-sm font-medium text-gray-700">
            {t('common.accessDenied.currentRole')}{' '}
            <span className="font-bold text-blue-600">{role}</span>
          </p>
          <p className="text-sm text-gray-500 mt-1">
            {t('common.accessDenied.noAccessNote')}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={goBack}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
          >
            <ArrowLeft size={16} />
            {t('common.accessDenied.goBack')}
          </button>

          <a
            href="/"
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center justify-center gap-2 hover:bg-blue-700 transition-colors"
          >
            <Home size={16} />
            {t('common.accessDenied.homePage')}
          </a>
        </div>
      </div>
    </div>
  );
};

export default AccessDeniedPage;
```

### Translation JSON to add:
```json
{
  "common": {
    "accessDenied": {
      "title": "Access Denied",
      "description": "Sorry, you don't have permission to access this page. This area requires different user privileges.",
      "currentRole": "Current Role:",
      "noAccessNote": "If you believe you should have access to this page, please contact your administrator.",
      "goBack": "Go Back",
      "homePage": "Home Page"
    }
  }
}
```

## Deliverables
1. Updated React components with i18n integration
2. Complete translation JSON files for all supported languages (en, fr)  
3. Documentation of any new translation keys added

## Timeline
Please complete this task by [DEADLINE].
