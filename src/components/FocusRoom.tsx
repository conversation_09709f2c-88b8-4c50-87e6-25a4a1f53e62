"use client";

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { useTranslation } from 'react-i18next';

export default function FocusRoom() {
  const { t } = useTranslation('common');
  
  // Tasks state
  const [tasks, setTasks] = useState<{text: string, completed: boolean}[]>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('focus-room-tasks');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });
  const [newTask, setNewTask] = useState('');

  // Timer states
  const [workMinutes, setWorkMinutes] = useState(25);
  const [restMinutes, setRestMinutes] = useState(5);
  const [workTime, setWorkTime] = useState(25 * 60);
  const [restTime, setRestTime] = useState(5 * 60);
  const [isWorkTimerRunning, setIsWorkTimerRunning] = useState(false);
  const [isRestTimerRunning, setIsRestTimerRunning] = useState(false);
  const [activeTimer, setActiveTimer] = useState<'work' | 'rest' | null>(null);
  const [editingTimer, setEditingTimer] = useState<'work' | 'rest' | null>(null);
  
  const workInputRef = useRef<HTMLInputElement>(null);
  const restInputRef = useRef<HTMLInputElement>(null);

  // Save tasks to localStorage
  useEffect(() => {
    localStorage.setItem('focus-room-tasks', JSON.stringify(tasks));
  }, [tasks]);

  // Reset timers when minutes change
  useEffect(() => {
    setWorkTime(workMinutes * 60);
  }, [workMinutes]);

  useEffect(() => {
    setRestTime(restMinutes * 60);
  }, [restMinutes]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isWorkTimerRunning && activeTimer === 'work') {
      interval = setInterval(() => {
        setWorkTime((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setIsWorkTimerRunning(false);
            setActiveTimer('rest');
            setIsRestTimerRunning(true);
            return workMinutes * 60;
          }
          return prev - 1;
        });
      }, 1000);
    } else if (isRestTimerRunning && activeTimer === 'rest') {
      interval = setInterval(() => {
        setRestTime((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            setIsRestTimerRunning(false);
            return restMinutes * 60;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isWorkTimerRunning, isRestTimerRunning, activeTimer, workMinutes, restMinutes]);

  // Focus input when editing
  useEffect(() => {
    if (editingTimer === 'work' && workInputRef.current) {
      workInputRef.current.focus();
    } else if (editingTimer === 'rest' && restInputRef.current) {
      restInputRef.current.focus();
    }
  }, [editingTimer]);

  const addTask = () => {
    if (newTask.trim()) {
      setTasks([...tasks, {text: newTask.trim(), completed: false}]);
      setNewTask('');
    }
  };

  const deleteTask = (index: number) => {
    setTasks(tasks.filter((_, i) => i !== index));
  };

  const toggleTaskCompletion = (index: number) => {
    setTasks(tasks.map((task, i) => 
      i === index ? {...task, completed: !task.completed} : task
    ));
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startWorkTimer = () => {
    setActiveTimer('work');
    setIsWorkTimerRunning(true);
    setIsRestTimerRunning(false);
  };

  const startRestTimer = () => {
    setActiveTimer('rest');
    setIsRestTimerRunning(true);
    setIsWorkTimerRunning(false);
  };

  const pauseTimer = () => {
    setIsWorkTimerRunning(false);
    setIsRestTimerRunning(false);
  };

  const resetTimer = () => {
    pauseTimer();
    setWorkTime(workMinutes * 60);
    setRestTime(restMinutes * 60);
    setActiveTimer(null);
  };

  const handleTimerDoubleClick = (timerType: 'work' | 'rest') => {
    pauseTimer();
    setEditingTimer(timerType);
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>, timerType: 'work' | 'rest') => {
    const value = Math.max(1, Math.min(99, Number(e.target.value)));
    if (timerType === 'work') {
      setWorkMinutes(value);
    } else {
      setRestMinutes(value);
    }
  };

  const handleTimeBlur = () => {
    setEditingTimer(null);
  };

  const handleTimeKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setEditingTimer(null);
    }
  };

  // Calculate circle progress (0-100)
  const workProgress = 100 - (workTime / (workMinutes * 60)) * 100;
  const restProgress = 100 - (restTime / (restMinutes * 60)) * 100;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 h-screen">
      {/* Left side - Tasks */}
      <div className="md:col-span-2 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>{t('focusRoom.tasks.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Input
                value={newTask}
                onChange={(e) => setNewTask(e.target.value)}
                placeholder={t('focusRoom.tasks.addPlaceholder')}
                onKeyDown={(e) => e.key === 'Enter' && addTask()}
              />
              <Button onClick={addTask}>{t('focusRoom.tasks.addButton')}</Button>
            </div>
            <div className="mt-4 space-y-2">
              {tasks.map((task, index) => (
                <div key={index} className="flex items-center gap-3 p-2 border rounded">
                  <Checkbox
                    checked={task.completed}
                    onCheckedChange={() => toggleTaskCompletion(index)}
                    aria-label={task.completed ? 
                      t('focusRoom.accessibility.taskComplete') : 
                      t('focusRoom.accessibility.taskIncomplete')}
                  />
                  <span className={`flex-1 ${task.completed ? 'line-through text-gray-500' : ''}`}>
                    {task.text}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteTask(index)}
                    aria-label={t('focusRoom.accessibility.deleteTask')}
                  >
                    ×
                  </Button>
                </div>
              ))}
              {tasks.length === 0 && (
                <p className="text-sm text-muted-foreground">{t('focusRoom.tasks.noTasks')}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right side - Circular Timers */}
      <div className="space-y-4">
        {/* Work Timer */}
        <Card>
          <CardHeader>
            <CardTitle>{t('focusRoom.workTimer.title')}</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div 
              className="relative w-48 h-48 my-4 cursor-pointer"
              onDoubleClick={() => handleTimerDoubleClick('work')}
              aria-label={t('focusRoom.accessibility.timerEdit')}
            >
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="8"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray="283"
                  strokeDashoffset={283 - (283 * workProgress / 100)}
                  transform="rotate(-90 50 50)"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                {editingTimer === 'work' ? (
                  <Input
                    ref={workInputRef}
                    type="number"
                    min="1"
                    max="99"
                    value={workMinutes}
                    onChange={(e) => handleTimeChange(e, 'work')}
                    onBlur={handleTimeBlur}
                    onKeyDown={(e) => handleTimeKeyDown(e)}
                    className="w-20 text-3xl text-center font-bold [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  />
                ) : (
                  <span className="text-3xl font-bold">{formatTime(workTime)}</span>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={startWorkTimer}
                disabled={isWorkTimerRunning}
              >
                {t('focusRoom.workTimer.startButton')}
              </Button>
              <Button
                variant="outline"
                onClick={pauseTimer}
                disabled={!isWorkTimerRunning}
              >
                {t('focusRoom.workTimer.pauseButton')}
              </Button>
              <Button
                variant="outline"
                onClick={resetTimer}
              >
                {t('focusRoom.workTimer.resetButton')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Rest Timer */}
        <Card>
          <CardHeader>
            <CardTitle>{t('focusRoom.restTimer.title')}</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div 
              className="relative w-48 h-48 my-4 cursor-pointer"
              onDoubleClick={() => handleTimerDoubleClick('rest')}
              aria-label={t('focusRoom.accessibility.timerEdit')}
            >
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="8"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray="283"
                  strokeDashoffset={283 - (283 * restProgress / 100)}
                  transform="rotate(-90 50 50)"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                {editingTimer === 'rest' ? (
                  <Input
                    ref={restInputRef}
                    type="number"
                    min="1"
                    max="99"
                    value={restMinutes}
                    onChange={(e) => handleTimeChange(e, 'rest')}
                    onBlur={handleTimeBlur}
                    onKeyDown={(e) => handleTimeKeyDown(e)}
                    className="w-20 text-3xl text-center font-bold [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  />
                ) : (
                  <span className="text-3xl font-bold">{formatTime(restTime)}</span>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={startRestTimer}
                disabled={isRestTimerRunning}
              >
                {t('focusRoom.restTimer.startButton')}
              </Button>
              <Button
                variant="outline"
                onClick={pauseTimer}
                disabled={!isRestTimerRunning}
              >
                {t('focusRoom.restTimer.pauseButton')}
              </Button>
              <Button
                variant="outline"
                onClick={resetTimer}
              >
                {t('focusRoom.restTimer.resetButton')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
