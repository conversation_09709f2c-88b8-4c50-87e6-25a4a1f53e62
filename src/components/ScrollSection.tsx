import { useRef, useEffect, ReactNode } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

interface ScrollSectionProps {
  children: ReactNode;
  className?: string;
  onProgress?: (progress: number) => void;
}

/**
 * A component that creates a scroll-triggered section with GSAP
 * It tracks scroll progress and can trigger animations based on scroll position
 */
const ScrollSection = ({ children, className = '', onProgress }: ScrollSectionProps) => {
  const sectionRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    if (!sectionRef.current) return
    
    // Create ScrollTrigger for the section
    const trigger = ScrollTrigger.create({
      trigger: sectionRef.current,
      start: 'top top',
      end: 'bottom bottom',
      scrub: true,
      pin: true,
      anticipatePin: 1,
      onUpdate: (self) => {
        // Call the onProgress callback with current scroll progress
        if (onProgress) {
          onProgress(self.progress)
        }
      }
    })
    
    // Clean up ScrollTrigger on component unmount
    return () => {
      trigger.kill()
    }
  }, [onProgress])
  
  return (
    <div ref={sectionRef} className={`min-h-screen ${className}`}>
      {children}
    </div>
  )
}

export default ScrollSection
