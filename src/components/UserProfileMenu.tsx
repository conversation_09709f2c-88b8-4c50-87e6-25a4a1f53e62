import React, { useState, useEffect, useRef } from 'react'
import { User, Settings, LogOut, ChevronDown, HelpCircle,IdCardIcon } from 'lucide-react'
import { fetchUserProfile, UserProfile } from '@/lib/user/profile'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Link } from '@tanstack/react-router'
import { useToast } from '@/components/ui/use-toast'

interface UserProfileMenuProps {
  onLogout: () => void
}

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({ onLogout }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [userData, setUserData] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showTourButton, setShowTourButton] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Fetch user data and expose for event handler
  const fetchData = async () => {
    try {
      const jwt = localStorage.getItem('access_token') || ''
      if (jwt) {
        const profile = await fetchUserProfile(jwt)
        setUserData(profile)
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchData()

    // Check if any tours are in progress
    const checkTourStatus = () => {
      const adminTourActive =
        localStorage.getItem('tour_progress_admin') !== null
      const dataStorageTourActive =
        localStorage.getItem('tour_progress_dataStorage') !== null
      const timetableTourActive =
        localStorage.getItem('tour_progress_timetable') !== null
      const crossRouteTourActive =
        localStorage.getItem('cross_route_tour_progress') !== null

      setShowTourButton(
        adminTourActive ||
          dataStorageTourActive ||
          timetableTourActive ||
          crossRouteTourActive
      )
    }

    // Check tour status initially
    checkTourStatus()

    // Listen for custom login event to refresh user data
    const handleUserLogin = () => {
      setIsLoading(true)
      fetchData()
    }

    // Listen for tour status changes
    const handleTourStatusChange = () => {
      checkTourStatus()
    }

    window.addEventListener('user-login', handleUserLogin)
    window.addEventListener('tour-status-change', handleTourStatusChange)

    return () => {
      window.removeEventListener('user-login', handleUserLogin)
      window.removeEventListener('tour-status-change', handleTourStatusChange)
    }
  }, [])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const toggleDropdown = () => setIsOpen(!isOpen)

  // Function to finish all tours
  const handleFinishTour = () => {
    // Complete all tours
    localStorage.removeItem('tour_progress_admin')
    localStorage.removeItem('tour_progress_dataStorage')
    localStorage.removeItem('tour_progress_timetable')
    localStorage.removeItem('cross_route_tour_progress')

    // Mark all tours as completed
    localStorage.setItem('tour_completed_admin', 'true')
    localStorage.setItem('tour_completed_dataStorage', 'true')
    localStorage.setItem('tour_completed_timetable', 'true')
    localStorage.setItem('cross_route_tour_completed', 'true')
    localStorage.setItem('onboardingCompleted', 'true')

    // Dispatch event to notify other components
    window.dispatchEvent(new CustomEvent('tour-status-change'))

    // Close dropdown
    setIsOpen(false)

    // Show toast
    toast({
      title: 'All Tours Completed',
      description:
        'You have completed all tours. You can restart them anytime from the dashboard.',
      duration: 3000,
    })

    // Hide tour button
    setShowTourButton(false)
  }

  if (isLoading) {
    return (
      <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse flex-shrink-0"></div>
    )
  }

  if (!userData) {
    return null
  }

  return (
    <div className="relative admin-profile-menu" ref={dropdownRef}>
      <button
        id="admin-profile-button"
        onClick={toggleDropdown}
        className="profile-button flex items-center space-x-2 focus:outline-none"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* Avatar using Radix UI Avatar component */}
        <div>
          <Avatar className="h-10 w-10 border-2 border-gray-200">
            <AvatarImage
              src={userData.avatar || undefined}
              alt={userData.fullname || 'User'}
              className="h-full w-full object-cover"
            />
            <AvatarFallback className="bg-blue-500 text-white font-bold">
              {(
                (userData.firstname?.[0] || '') + (userData.lastname?.[0] || '')
              ).toUpperCase() ||
                userData.fullname?.substring(0, 2).toUpperCase() ||
                'US'}
            </AvatarFallback>
          </Avatar>
        </div>
        <span className="hidden sm:block text-sm font-medium text-gray-700">
          {userData.fullname || userData.firstname || 'User'}
        </span>
        <ChevronDown
          className={`h-4 w-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1">
            {/* User info section */}
            <div className="px-4 py-3 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-900">
                {userData.fullname ||
                  `${userData.firstname || ''} ${userData.lastname || ''}`.trim()}
              </p>
              <p className="text-sm text-gray-500 truncate">{userData.email}</p>
              <p className="text-xs text-gray-400 mt-1 capitalize">
                {userData.role}
              </p>
            </div>

            {/* Navigation links */}
            <Link
              to="/profile"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <User className="mr-3 h-4 w-4 text-gray-500" />
              Profile
            </Link>
            <Link
              to="/card"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <IdCardIcon className="mr-3 h-4 w-4 text-gray-500" />
              card
            </Link>
            <Link
              to="/settings"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Settings className="mr-3 h-4 w-4 text-gray-500" />
              Settings
            </Link>

            <div className="border-t border-gray-100"></div>

            {showTourButton && (
              <button
                onClick={handleFinishTour}
                className="flex w-full items-center px-4 py-2 text-sm text-blue-600 hover:bg-gray-100"
              >
                <HelpCircle className="mr-3 h-4 w-4 text-blue-500" />
                Finish Tour
              </button>
            )}

            <button
              onClick={onLogout}
              className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
            >
              <LogOut className="mr-3 h-4 w-4 text-red-500" />
              Logout
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserProfileMenu
