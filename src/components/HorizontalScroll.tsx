import { useRef, useEffect, ReactNode, useState } from 'react'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { Button } from './ui/button'

interface HorizontalScrollProps {
  children: ReactNode;
  className?: string;
  sections: number; // Number of horizontal sections
  onSectionChange?: (index: number) => void;
}

/**
 * A component that creates a pure CSS-based horizontal scrolling section
 * using scroll-snap and manual navigation controls
 */
const HorizontalScroll = ({
  children,
  className = '',
  sections,
  onSectionChange
}: HorizontalScrollProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [currentSection, setCurrentSection] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const [showScrollDown, setShowScrollDown] = useState(false)
  const [isLastSectionVisible, setIsLastSectionVisible] = useState(false)
  // Removed unused state variable
  const [, setInitialized] = useState(false)

  // Function to scroll to a specific section
  const scrollToSection = (index: number) => {
    if (!scrollContainerRef.current) return

    // Prevent scrolling past the bounds
    const targetIndex = Math.max(0, Math.min(index, sections - 1))

    // Set scrolling state
    setIsScrolling(true)

    // Calculate the scroll position
    const sectionWidth = window.innerWidth
    const scrollLeft = targetIndex * sectionWidth

    // Scroll to the section
    scrollContainerRef.current.scrollTo({
      left: scrollLeft,
      behavior: 'smooth'
    })

    // Update current section
    setCurrentSection(targetIndex)

    // Notify parent component
    if (onSectionChange) {
      onSectionChange(targetIndex)
    }

    // Check if we've reached the last section
    if (targetIndex === sections - 1) {
      setIsLastSectionVisible(true)
      setTimeout(() => {
        setShowScrollDown(true)
      }, 500)
    } else {
      setIsLastSectionVisible(false)
      setShowScrollDown(false)
    }

    // Reset scrolling state after animation
    setTimeout(() => {
      setIsScrolling(false)
    }, 500)
  }

  // Handle navigation
  const handlePrev = () => {
    scrollToSection(currentSection - 1)
  }

  const handleNext = () => {
    scrollToSection(currentSection + 1)
  }

  // Initialize to first section on mount
  useEffect(() => {
    // Force scroll to first section on mount
    const initializeScroll = () => {
      if (scrollContainerRef.current) {
        // Force scroll to the beginning
        scrollContainerRef.current.scrollLeft = 0

        // Update state
        setCurrentSection(0)
        setInitialized(true)

        // Notify parent
        if (onSectionChange) {
          onSectionChange(0)
        }
      }
    }

    // Execute immediately
    initializeScroll()

    // And also with a delay to ensure it happens after any layout changes
    const timeout = setTimeout(initializeScroll, 100)

    return () => {
      clearTimeout(timeout)
    }
  }, [])

  // Set up scroll event listener to track current section
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (!scrollContainer) return

    const handleScroll = () => {
      if (isScrolling) return

      const scrollLeft = scrollContainer.scrollLeft
      const sectionWidth = window.innerWidth
      const newSection = Math.round(scrollLeft / sectionWidth)

      if (newSection !== currentSection) {
        setCurrentSection(newSection)
        if (onSectionChange) {
          onSectionChange(newSection)
        }

        // Check if we've reached the last section
        if (newSection === sections - 1) {
          setIsLastSectionVisible(true)
          setTimeout(() => {
            setShowScrollDown(true)
          }, 500)
        } else {
          setIsLastSectionVisible(false)
          setShowScrollDown(false)
        }
      }
    }

    // Add a scroll event listener to the window to detect when the about section comes into view
    const handleWindowScroll = () => {
      const aboutSection = document.getElementById('about')
      if (!aboutSection) return

      const aboutRect = aboutSection.getBoundingClientRect()

      // If the about section is coming into view from below
      if (aboutRect.top < window.innerHeight && aboutRect.top > 0) {
        // Reset to the last section when scrolling up from below
        if (currentSection !== sections - 1) {
          scrollToSection(sections - 1)
        }
      }
    }

    scrollContainer.addEventListener('scroll', handleScroll)
    window.addEventListener('scroll', handleWindowScroll)

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll)
      window.removeEventListener('scroll', handleWindowScroll)
    }
  }, [currentSection, isScrolling, onSectionChange, sections])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        handlePrev()
      } else if (e.key === 'ArrowRight') {
        handleNext()
      }
    }

    window.addEventListener('keydown', handleKeyDown)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [currentSection])

  // Handle wheel events to prevent vertical scrolling until horizontal is complete
  useEffect(() => {
    // Get the about section to capture wheel events for the entire section
    const aboutSection = document.getElementById('about') as HTMLElement
    const container = containerRef.current

    if (!container) return

    // Track if we're coming from below the section
    let comingFromBelow = false
    let lastScrollY = window.scrollY

    // Function to check if we're scrolling up from below the section
    const checkScrollDirection = () => {
      const currentScrollY = window.scrollY
      const aboutRect = aboutSection?.getBoundingClientRect()

      if (aboutRect) {
        // If we're below the about section and scrolling up
        if (aboutRect.bottom < 0 && currentScrollY < lastScrollY) {
          comingFromBelow = true
          // When coming from below, start at the last section
          if (currentSection !== sections - 1) {
            scrollToSection(sections - 1)
          }
        } else if (aboutRect.top >= 0) {
          comingFromBelow = false
        }
      }

      lastScrollY = currentScrollY
    }

    // Add scroll event listener to track direction
    window.addEventListener('scroll', checkScrollDirection)

    const handleWheel = (e: WheelEvent) => {
      // Check if we're in the about section
      const aboutRect = aboutSection?.getBoundingClientRect()
      if (!aboutRect) return

      // If the about section is in view or partially in view
      if ((aboutRect.top <= window.innerHeight && aboutRect.bottom >= 0) ||
          (aboutRect.top <= 0 && aboutRect.bottom >= -window.innerHeight)) {

        // If we're at the last section and the scroll down indicator is shown,
        // allow normal vertical scrolling
        if (currentSection === sections - 1 && showScrollDown) {
          // Don't prevent default - allow normal scrolling
          // Make sure the about section has the class to allow vertical scrolling
          if (aboutSection) {
            aboutSection.classList.add('can-scroll-vertical')
          }
          return;
        }

        // Otherwise prevent default wheel behavior when in the about section
        // This ensures horizontal scrolling works anywhere in the section
        e.preventDefault()

        // If we're not at the last section or we are but haven't shown the scroll down indicator
        // OR if we're coming from below and need to scroll through all sections first
        if ((!isLastSectionVisible || (isLastSectionVisible && !showScrollDown)) ||
            (comingFromBelow && currentSection > 0)) {

          // Use the wheel delta to scroll horizontally
          const delta = Math.abs(e.deltaY) > Math.abs(e.deltaX) ? e.deltaY : e.deltaX

          if (comingFromBelow) {
            // If coming from below, we need to go through all sections from last to first
            if (delta < 0) {
              // Scrolling up, go to previous section
              if (currentSection > 0) {
                handlePrev()
              } else {
                // At first section, allow normal scrolling
                comingFromBelow = false
              }
            } else if (delta > 0) {
              // If scrolling down while coming from above, just let normal scrolling happen
              comingFromBelow = false
            }
          } else {
            // Normal horizontal scrolling behavior
            if (delta > 0 && currentSection < sections - 1) {
              // Scroll right/down
              handleNext()
            } else if (delta < 0 && currentSection > 0) {
              // Scroll left/up
              handlePrev()
            } else if (delta > 0 && currentSection === sections - 1 && !showScrollDown) {
              // At last section, show scroll down indicator
              setShowScrollDown(true)

              // No delay needed - immediately allow vertical scrolling
              comingFromBelow = false

              // Add class to about section to allow vertical scrolling
              if (aboutSection) {
                aboutSection.classList.add('can-scroll-vertical')
              }
            }
          }
        }
      }
    }

    // Add wheel event listener to the container
    container.addEventListener('wheel', handleWheel as EventListener, { passive: false })

    // Add wheel event listener to the entire about section if it exists
    if (aboutSection) {
      // Add the event listener to the about section with capture phase to ensure it gets all wheel events
      aboutSection.addEventListener('wheel', handleWheel as EventListener, { passive: false, capture: true })
    }

    // Return cleanup function
    return () => {
      window.removeEventListener('scroll', checkScrollDirection)
      container.removeEventListener('wheel', handleWheel as EventListener)

      // Only try to remove event listeners if aboutSection exists
      if (aboutSection) {
        try {
          aboutSection.removeEventListener('wheel', handleWheel as EventListener, { capture: true } as EventListenerOptions)
        } catch (error) {
          console.error('Error removing wheel event listener:', error)
        }
      }
    }
  }, [currentSection, sections, isLastSectionVisible, showScrollDown])

  return (
    <div
      ref={containerRef}
      className={`relative wheel-capture h-full ${className}`}
      style={{ height: '100%' }}
    >
      {/* Horizontal scroll container */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-hidden scroll-smooth h-full horizontal-scroll-container wheel-capture"
        style={{
          scrollSnapType: 'x mandatory',
          WebkitOverflowScrolling: 'touch'
        }}
      >
        {/* Wrap each child in a scroll snap container */}
        {Array.isArray(children) ?
          children.map((child, index) => (
            <div
              key={index}
              className="min-w-full w-full flex-shrink-0 h-full"
              style={{ scrollSnapAlign: 'start' }}
            >
              {child}
            </div>
          )) :
          <div className="min-w-full w-full flex-shrink-0 h-full" style={{ scrollSnapAlign: 'start' }}>
            {children}
          </div>
        }
      </div>

      {/* Navigation controls */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center items-center space-x-3 z-30">
        {/* Previous button */}
        <Button
          variant="outline"
          size="icon"
          onClick={handlePrev}
          disabled={currentSection === 0 || isScrolling}
          className="rounded-full hover:bg-blue-50 transition-colors bg-white shadow-md border-blue-200 h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4 text-blue-600" />
        </Button>

        {/* Section indicators */}
        <div className="flex space-x-2 bg-white/90 backdrop-blur-sm rounded-full px-3 py-2 shadow-md">
          {Array.from({ length: sections }).map((_, i) => (
            <button
              key={i}
              onClick={() => scrollToSection(i)}
              disabled={isScrolling}
              className={`h-2 rounded-full transition-all ${
                i === currentSection ? 'bg-blue-600 w-6' : 'bg-gray-300 w-2 hover:bg-gray-400'
              }`}
              aria-label={`Go to section ${i + 1}`}
            />
          ))}
        </div>

        {/* Next button */}
        <Button
          variant="outline"
          size="icon"
          onClick={handleNext}
          disabled={currentSection === sections - 1 || isScrolling}
          className="rounded-full hover:bg-blue-50 transition-colors bg-white shadow-md border-blue-200 h-8 w-8"
        >
          <ArrowRight className="h-4 w-4 text-blue-600" />
        </Button>
      </div>


    </div>
  )
}

export default HorizontalScroll
