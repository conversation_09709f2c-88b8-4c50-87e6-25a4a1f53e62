import React, { useEffect, useState } from 'react'
import PaymentRequired from './PaymentRequired'

interface PaymentCheckProps {
  isPaid: boolean
  studentName?: string
  children: React.ReactNode
}

/**
 * A component that checks if a student has paid and conditionally renders content
 * If isPaid is false, it shows a payment required message
 * If isPaid is true, it renders the children components
 *
 * This component also checks localStorage for payment status as a fallback
 */
export default function PaymentCheck({
  isPaid,
  studentName,
  children,
}: PaymentCheckProps) {
  // State to track effective payment status
  const [effectiveIsPaid, setEffectiveIsPaid] = useState(isPaid)

  // Check localStorage for payment status
  useEffect(() => {
    // Update from props
    setEffectiveIsPaid(isPaid)

    // Also check localStorage as a fallback
    const isPaidFromStorage = localStorage.getItem('isPaid') === 'true'
    if (isPaidFromStorage) {
      console.log('Using payment status from localStorage: PAID')
      setEffectiveIsPaid(true)
    }
  }, [isPaid])

  // One-time check for localStorage changes
  useEffect(() => {
    const isPaidFromStorage = localStorage.getItem('isPaid') === 'true'
    console.log(
      `One-time check of localStorage for payment status: ${isPaidFromStorage ? 'PAID' : 'NOT PAID'}`
    )
    if (isPaidFromStorage) {
      setEffectiveIsPaid(true)
    }
  }, [])

  // Only log on changes to reduce console spam
  useEffect(() => {
    console.log(
      `Payment status updated: Props=${isPaid}, Effective=${effectiveIsPaid}`
    )
  }, [isPaid, effectiveIsPaid])

  if (!effectiveIsPaid) {
    // Payment required
    return <PaymentRequired studentName={studentName} />
  }

  // Payment verified, show content
  return <>{children}</>
}
