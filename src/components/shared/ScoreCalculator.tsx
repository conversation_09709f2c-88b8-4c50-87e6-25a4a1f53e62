import { Student, Subject, ExamType, StudentSubject } from '@/interface/types'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useState } from 'react'

// Define FlexibleId locally
type FlexibleId = string | number

// Helper function for ID comparison
function isSameId(
  id1: FlexibleId | undefined,
  id2: FlexibleId | undefined
): boolean {
  if (id1 === undefined || id2 === undefined) return false
  return String(id1) === String(id2)
}

interface ScoreCalculatorProps {
  student: Student
  subjects: Subject[]
  onScoreUpdate?: (studentId: string, subjectId: string, score: number) => void
  readOnly?: boolean
}

/**
 * A reusable component for calculating and displaying student scores
 * @param {ScoreCalculatorProps} props - The props for the ScoreCalculator component
 * @returns {JSX.Element} The rendered ScoreCalculator component
 */
export function ScoreCalculator({
  student,
  subjects,
  onScoreUpdate,
  readOnly = false,
}: ScoreCalculatorProps): JSX.Element {
  const [localSubjects, setLocalSubjects] = useState<Subject[]>(subjects)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState('')

  // Filter subjects based on search
  const filteredSubjects = localSubjects.filter((subject) =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Pagination setup
  const subjectsPerPage = 5
  const totalPages = Math.ceil(filteredSubjects.length / subjectsPerPage)
  const indexOfLastSubject = currentPage * subjectsPerPage
  const indexOfFirstSubject = indexOfLastSubject - subjectsPerPage
  const currentSubjects = filteredSubjects.slice(
    indexOfFirstSubject,
    indexOfLastSubject
  )

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const calculateSubjectGrade = (grades: any[], formula: string) => {
    const gradeMap: Record<string, number> = {}
    grades.forEach((grade) => {
      gradeMap[grade.examType] = grade.value
    })

    try {
      const formulaWithValues = formula.replace(
        /\b(ex|cr|ex2|cr2|TP|TP2|oral)\b/g,
        (match) => {
          return gradeMap[match] !== undefined
            ? gradeMap[match].toString()
            : '0'
        }
      )
      // eslint-disable-next-line no-eval
      const result = eval(formulaWithValues)
      return result || 0
    } catch (error) {
      console.error('Invalid formula:', formula)
      return 0
    }
  }

  const updateFormula = (subjectId: FlexibleId, newFormula: string) => {
    if (readOnly) return
    setLocalSubjects((prev) =>
      prev.map((subject) =>
        isSameId(subject.id, subjectId)
          ? { ...subject, formula: newFormula }
          : subject
      )
    )
  }

  const updateCoefficient = (
    subjectId: FlexibleId,
    examTypeId: FlexibleId,
    newCoefficient: number
  ) => {
    if (readOnly) return
    setLocalSubjects((prev) =>
      prev.map((subject) =>
        isSameId(subject.id, subjectId)
          ? {
              ...subject,
              examTypes: subject.examTypes?.map((exam: ExamType) =>
                isSameId(exam.id, examTypeId)
                  ? { ...exam, coefficient: newCoefficient }
                  : exam
              ),
            }
          : subject
      )
    )
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="mb-4">
        <Label htmlFor="search-subject">Search Subject</Label>
        <Input
          id="search-subject"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search by subject name"
          className="max-w-sm"
        />
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Subject</TableHead>
            {!readOnly && <TableHead>Formula</TableHead>}
            <TableHead>Grade</TableHead>
            {!readOnly && <TableHead>Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentSubjects.map((subject) => {
            const studentSubject = student.subjects?.find(
              (s: StudentSubject) => s.subjectId === subject.id
            )
            const subjectGrade =
              studentSubject && 'grades' in studentSubject
                ? calculateSubjectGrade(
                    studentSubject.grades,
                    subject.formula ?? ''
                  )
                : 0

            return (
              <TableRow key={subject.id}>
                <TableCell>{subject.name}</TableCell>
                {!readOnly && (
                  <TableCell>
                    <Input
                      type="text"
                      value={subject.formula}
                      onChange={(e) =>
                        updateFormula(String(subject.id), e.target.value)
                      }
                      placeholder="Enter formula"
                    />
                    <div className="mt-2 space-y-2">
                      {subject.examTypes?.map((exam: ExamType) => (
                        <div key={exam.id} className="flex items-center gap-2">
                          <span>{exam.name}:</span>
                          <Input
                            type="number"
                            step="0.1"
                            value={exam.coefficient}
                            onChange={(e) =>
                              updateCoefficient(
                                String(subject.id),
                                String(exam.id),
                                parseFloat(e.target.value)
                              )
                            }
                            className="w-20"
                          />
                        </div>
                      ))}
                    </div>
                  </TableCell>
                )}
                <TableCell>{subjectGrade.toFixed(2)}</TableCell>
                {!readOnly && onScoreUpdate && (
                  <TableCell>
                    <Button
                      onClick={() =>
                        onScoreUpdate(
                          String(student.id),
                          String(subject.id),
                          subjectGrade
                        )
                      }
                    >
                      Update Score
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            )
          })}
        </TableBody>
      </Table>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-4 flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? 'default' : 'outline'}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </Button>
          ))}
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
