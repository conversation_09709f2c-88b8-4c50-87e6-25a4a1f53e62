// src/components/shared/ActivityLogList.tsx
import { ActivityLog, ActivityLogItem } from './ActivityLog'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'

interface ActivityLogListProps {
  activities: ActivityLogItem[]
  isLoading?: boolean
  error?: boolean
  title?: string
  description?: string
  onViewAll?: () => void
  className?: string
}

export const ActivityLogList = ({
  activities,
  isLoading = false,
  error = false,
  title = 'Recent Activities',
  description = 'Latest updates from across the platform',
  className,
}: ActivityLogListProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="divide-y">
            {isLoading ? (
              <div className="py-4 text-center">
                <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  Loading activities...
                </p>
              </div>
            ) : error ? (
              <div className="py-4 text-center">
                <p className="text-sm text-red-500">
                  Failed to load activities
                </p>
              </div>
            ) : activities.length > 0 ? (
              activities
                .slice(0, 5)
                .map((activity) => (
                  <ActivityLog key={activity.id} activity={activity} />
                ))
            ) : (
              <div className="py-4 text-center">
                <p className="text-sm text-muted-foreground">
                  No recent activities
                </p>
              </div>
            )}
          </div>
        </CardContent>
        {!isLoading && !error && activities.length > 0 && (
          <CardFooter className="flex justify-center">
            <Button
              variant="ghost"
              className="text-primary hover:text-primary/80"
              onClick={() => setIsModalOpen(true)}
            >
              View All Activities
            </Button>
          </CardFooter>
        )}
      </Card>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-h-[80vh] overflow-hidden sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>All Activities</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[60vh]">
            <div className="divide-y px-1">
              {activities.map((activity) => (
                <ActivityLog key={activity.id} activity={activity} />
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  )
}
