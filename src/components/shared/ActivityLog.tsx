// src/components/shared/ActivityLog.tsx
import { Clock, FileEdit, Trash2, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

export type ActivityAction = 'create' | 'update' | 'delete'

export interface ActivityLogItem {
  id: string
  type: string
  action: ActivityAction
  entityId: string
  entityName: string
  timestamp: string
  details?: string
  performedBy?: {
    id: string
    name: string
    role?: string
  }
}

interface ActivityLogProps {
  activity: ActivityLogItem
  className?: string
}

// Function to format timestamp
const formatTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp)
    return new Intl.DateTimeFormat('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    }).format(date)
  } catch (error) {
    console.error('Error formatting timestamp:', error)
    return timestamp
  }
}

export const ActivityLog = ({ activity, className }: ActivityLogProps) => {
  // Determine the icon and color based on the action type
  const getActionIcon = () => {
    switch (activity.action) {
      case 'create':
        return <Plus className="h-4 w-4" />
      case 'update':
        return <FileEdit className="h-4 w-4" />
      case 'delete':
        return <Trash2 className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  // Get color class based on action
  const getActionColorClass = () => {
    switch (activity.action) {
      case 'create':
        return 'bg-green-100 text-green-600'
      case 'update':
        return 'bg-blue-100 text-blue-600'
      case 'delete':
        return 'bg-red-100 text-red-600'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  // Format the activity title
  const getActivityTitle = () => {
    const actionText =
      activity.action === 'create'
        ? 'New'
        : activity.action === 'update'
          ? 'Updated'
          : 'Deleted'

    return `${actionText} ${activity.type.toLowerCase()}: ${activity.entityName}`
  }

  const icon = getActionIcon()
  const colorClass = getActionColorClass()
  const title = getActivityTitle()
  const time = formatTime(activity.timestamp)

  return (
    <div className={cn('flex items-start space-x-3 py-3', className)}>
      <div className={cn('flex-shrink-0 rounded-full p-1 mr-3', colorClass)}>
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900">{title}</p>
        {activity.details && (
          <p className="text-sm text-gray-500">{activity.details}</p>
        )}
      </div>
      <div className="flex items-center text-xs text-gray-500">
        <Clock className="mr-1 h-3 w-3" />
        {time}
      </div>
    </div>
  )
}
