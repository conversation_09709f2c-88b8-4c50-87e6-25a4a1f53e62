import { useState, useRef, useEffect, ChangeEvent, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import CdnImage from './CdnImage'
import {
  Loader2,
  Upload,
  X,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Server,
  ShieldAlert,
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

// Import FileUploadService properly
import FileUploadService from '@/services/fileUploadService'

interface FileUploaderProps {
  onFileUploaded: (url: string) => void
  onError?: (error: Error) => void
  className?: string
  buttonText?: string
  previewUrl?: string | null
  accept?: string
  defaultPreview?: string
  label?: string
  isAvatar?: boolean
  previewComponent?: (previewUrl: string) => ReactNode
  maxFileSizeMB?: number
}

/**
 * Error alert component with troubleshooting information
 */
function ErrorAlert({ error }: { error: string }) {
  const isAuthError = error.includes('Authentication') || error.includes('401')

  return (
    <Alert className="py-2 mb-2 bg-red-50 border-red-300">
      {isAuthError ? (
        <ShieldAlert className="h-4 w-4 mr-2 text-red-600" />
      ) : (
        <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
      )}
      <div>
        <AlertTitle className="text-sm font-semibold text-red-800">
          {isAuthError ? 'Authentication Error' : 'Upload Error'}
        </AlertTitle>
        <AlertDescription className="text-xs text-red-800">
          {error}
          {isAuthError && (
            <div className="mt-1 pt-1 border-t border-red-200 text-xs">
              <p className="font-semibold">Troubleshooting:</p>
              <ul className="list-disc list-inside mt-1">
                <li>Verify the CDN token in your .env file</li>
                <li>Check if the CDN service is running</li>
                <li>Contact your administrator for a valid token</li>
              </ul>
            </div>
          )}
        </AlertDescription>
      </div>
    </Alert>
  )
}

/**
 * A reusable component for uploading files to the CDN server
 */
export default function FileUploader({
  onFileUploaded,
  onError,
  className,
  previewUrl: initialPreviewUrl = null,
  accept = 'image/*',
  label = 'Upload Image',
  isAvatar = false,
  previewComponent,
  defaultPreview,
  maxFileSizeMB = 20,
}: FileUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState('')
  // Initialize with defaultPreview if provided
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialPreviewUrl)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [currentFile, setCurrentFile] = useState<File | null>(null)
  const [isCorsError, setIsCorsError] = useState(false)
  const [fileSizeError, setFileSizeError] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Effect to update preview URL when initialPreviewUrl or defaultPreview changes
  useEffect(() => {
    // Update from initialPreviewUrl (passed as prop)
    if (initialPreviewUrl && initialPreviewUrl !== previewUrl) {
      console.log('Updating preview URL from prop:', initialPreviewUrl)
      setPreviewUrl(initialPreviewUrl)
      setUploadSuccess(true) // Mark as successful since it's a pre-existing image
    }
    // Fallback to defaultPreview if no initialPreviewUrl and no current preview
    else if (defaultPreview && !previewUrl) {
      console.log('Using default preview:', defaultPreview)
      setPreviewUrl(defaultPreview)
    }
  }, [initialPreviewUrl, defaultPreview, previewUrl])

  /**
   * Handle file change event
   */
  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    try {
      // Reset states
      setIsUploading(true)
      setError('')
      setUploadSuccess(false)
      setFileSizeError(false)
      setCurrentFile(files[0])

      const file = files[0]
      
      // Check file size (20MB limit)
      const fileSizeInMB = file.size / (1024 * 1024)
      if (fileSizeInMB > maxFileSizeMB) {
        setFileSizeError(true)
        setError(`File size exceeds the ${maxFileSizeMB}MB limit. Please select a smaller file.`)
        setIsUploading(false)
        
        // Create local preview anyway to show the user what they selected
        const localPreview = URL.createObjectURL(file)
        setPreviewUrl(localPreview)
        return
      }

      // Create local preview
      const localPreview = URL.createObjectURL(file)
      setPreviewUrl(localPreview)

      // Start upload process
      console.log('Starting file upload process...')
      const fileUploadService = new FileUploadService()

      // Upload file to CDN
      const response = await fileUploadService.uploadFile(file)
      console.log('Upload completed, response:', response)

      // Handle upload success
      if (response.success && response.data) {
        const fileUrl = fileUploadService.getFileUrl(response)

        // Log the image URL in a more visible way
        console.log(
          '%c Image URL:',
          'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;',
          fileUrl
        )
        console.log('Preview this image at:', fileUrl)

        // Keep the preview URL but update it to the actual CDN URL for display reliability
        // This ensures we're showing the actual uploaded image, not just the local preview
        setPreviewUrl(fileUrl)

        // Mark upload as successful
        setUploadSuccess(true)

        // Update the parent form state without triggering a save
        // This only updates the form field value, the actual save happens when the form is submitted
        if (onFileUploaded) {
          console.log('Calling onFileUploaded callback with URL:', fileUrl)

          // Format the URL to use the health proxy for CDN URLs
          let formattedUrl = fileUrl

          // If it's already a proxy URL, don't proxy it again
          if (fileUrl.includes('/health/proxy/image')) {
            console.log('URL is already proxied, using as is')
            formattedUrl = fileUrl
          }
          // If it's a CDN URL, use the health proxy
          else if (fileUrl.includes('cdn.jeridschool.tech')) {
            // Use the health proxy endpoint that works with the profile component
            formattedUrl = `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(fileUrl)}`
          } else if (!fileUrl.startsWith('http')) {
            // Ensure it has a protocol
            formattedUrl = `https://${fileUrl}`
          }

          console.log('Formatted URL for callback:', formattedUrl)
          onFileUploaded(formattedUrl)
          console.log('Callback completed successfully')

          // Force a refresh of the preview by updating the state
          setPreviewUrl(null)
          setTimeout(() => {
            setPreviewUrl(formattedUrl)
          }, 50)

          // Prevent any potential form submission
          setTimeout(() => {
            const activeElement = document.activeElement as HTMLElement
            if (activeElement && activeElement.blur) {
              activeElement.blur()
            }
          }, 0)
        }

        console.log('Upload successful')
      } else {
        // Handle upload failure
        if (response.message?.includes('401')) {
          throw new Error(
            'Authentication failed: CDN token is missing or invalid'
          )
        } else {
          throw new Error(response.message || 'Upload failed')
        }
      }
    } catch (err: any) {
      console.error('Upload error:', err)

      // Set error message with more details for specific errors
      if (err.message?.includes('401')) {
        setError('Authentication failed: CDN token is missing or invalid')
      } else if (err.message?.includes('Network Error')) {
        setError('Network error: Could not connect to CDN server')
      } else {
        setError(err.message || 'An error occurred during upload')
      }

      // Keep the local preview if available, may still want to use it
      if (onError) {
        onError(err)
      }
    } finally {
      console.log('Upload process completed, isUploading set to false')
      setIsUploading(false)
    }
  }

  /**
   * Trigger the file input click
   */
  const handleButtonClick = (e: React.MouseEvent) => {
    // Prevent the default form submission
    e.preventDefault()
    e.stopPropagation()

    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  /**
   * Trigger the file input directly
   */
  const triggerFileInput = (e: React.MouseEvent) => {
    // Prevent the default form submission
    e.preventDefault()
    e.stopPropagation()

    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const clearPreview = (e?: React.MouseEvent) => {
    // Prevent the default form submission if event is provided
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }

    setPreviewUrl(null)
    setUploadSuccess(false)
    setError('')
    setIsCorsError(false)
    setFileSizeError(false)
    setCurrentFile(null)

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{label}</span>
        {previewUrl && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearPreview}
            className="h-6 px-2 text-red-500 hover:text-red-600 hover:bg-red-50"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      {isCorsError && (
        <Alert className="py-2 mb-2 bg-yellow-50 border-yellow-300">
          <Server className="h-4 w-4 mr-2 text-yellow-600" />
          <AlertDescription className="text-xs text-yellow-800">
            Server connection issue detected. Using fallback mode to save your
            file.
          </AlertDescription>
        </Alert>
      )}
      
      {fileSizeError && (
        <Alert className="py-2 mb-2 bg-red-50 border-red-300">
          <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
          <div>
            <AlertTitle className="text-sm font-semibold text-red-800">
              File Size Error
            </AlertTitle>
            <AlertDescription className="text-xs text-red-800">
              File size exceeds the {maxFileSizeMB}MB limit. Please select a smaller file.
            </AlertDescription>
          </div>
        </Alert>
      )}

      <div className="flex items-center gap-4">
        {/* Hidden file input */}
        <Input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept={accept}
          onChange={handleFileChange}
        />

        {/* Preview container - Make it larger for better visibility */}
        <div
          className={`relative border-2 border-dashed border-gray-300 bg-gray-50 flex items-center justify-center overflow-hidden cursor-pointer hover:bg-gray-100 transition-colors ${
            isAvatar ? 'h-20 w-20 rounded-full' : 'h-32 w-40 rounded-md'
          }`}
          onClick={handleButtonClick}
        >
          {isUploading ? (
            <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
          ) : previewUrl ? (
            <>
              {previewComponent ? (
                // Render custom preview component if provided
                <div className="h-full w-full">
                  {previewComponent(previewUrl as string)}
                </div>
              ) : (
                // Default image preview - ensure we're using the CdnImage component which now handles proxying
                <CdnImage
                  src={previewUrl}
                  alt="Preview"
                  className={`h-full w-full ${isAvatar ? 'object-cover' : 'object-contain'}`}
                />
              )}
              {uploadSuccess && (
                <div className="absolute bottom-1 right-1 bg-green-500 rounded-full p-0.5">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              )}
              {error && (
                <div className="absolute bottom-1 right-1 bg-red-500 rounded-full p-0.5">
                  <AlertCircle className="h-4 w-4 text-white" />
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center text-gray-400">
              <ImageIcon className="h-8 w-8 mb-1" />
              <span className="text-xs">Click to browse</span>
            </div>
          )}
        </div>

        <div className="flex-1">
          {error ? (
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={handleButtonClick}
                disabled={isUploading || !currentFile}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry Upload
                  </>
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full"
                onClick={triggerFileInput}
                disabled={isUploading}
              >
                <Upload className="h-4 w-4 mr-2" />
                Choose Different File
              </Button>
            </div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={triggerFileInput}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  {previewUrl ? 'Change Image' : 'Select Image'}
                </>
              )}
            </Button>
          )}

          {/* Error alert */}
          {error && !isCorsError && <ErrorAlert error={error} />}

          {uploadSuccess && (
            <>
              <div className="mt-1 flex items-center text-xs text-green-500">
                <CheckCircle className="h-3 w-3 mr-1" />
                {isCorsError
                  ? 'Saved using fallback mode'
                  : 'Upload successful'}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
