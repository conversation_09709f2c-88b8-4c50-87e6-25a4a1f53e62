"use client"

import React, { useRef, useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { jsPDF } from "jspdf"
import html2canvas from "html2canvas"
import { Printer, Download, RotateCw } from "lucide-react"

const fetchTemplate = async () => {
  const accessToken = localStorage.getItem("access_token")
  if (!accessToken) throw new Error("No access token found")

  const res = await fetch("http://localhost:3000/card-templates/user", {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })

  if (!res.ok) throw new Error("Failed to fetch template")
  return res.json()
}

const CardView: React.FC = () => {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  const {
    data,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["card-template"],
    queryFn: fetchTemplate,
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  })

  const waitForImagesToLoad = async (container: HTMLElement) => {
    const images = Array.from(container.querySelectorAll("img"))
    await Promise.all(images.map(img => {
      if (img.complete) return Promise.resolve()
      return new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
      })
    }))
  }

  const exportPDF = async () => {
    if (!cardRef.current) return alert("Card not ready")

    setIsGenerating(true)
    try {
      await waitForImagesToLoad(cardRef.current)

      const canvas = await html2canvas(cardRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: null,
      })

      const pdf = new jsPDF({
        orientation: canvas.width > canvas.height ? "landscape" : "portrait",
        unit: "mm",
        format: "a4",
      })

      const pageWidth = pdf.internal.pageSize.getWidth() - 20
      const imgWidth = pageWidth
      const imgHeight = (canvas.height * imgWidth) / canvas.width

      pdf.addImage(canvas.toDataURL("image/png"), "PNG", 10, 10, imgWidth, imgHeight)
      pdf.save("card-template.pdf")
    } catch (err) {
      console.error(err)
      alert("PDF generation failed.")
    } finally {
      setIsGenerating(false)
    }
  }

  const printCard = async () => {
    if (!cardRef.current) return alert("Card not ready")

    try {
      const canvas = await html2canvas(cardRef.current, {
        scale: 2,
        useCORS: true,
        backgroundColor: null,
      })

      const printWindow = window.open("", "_blank")
      if (!printWindow) return alert("Pop-up blocked")

      printWindow.document.write(`
        <html>
        <head>
          <title>Print</title>
          <style>
            body {
              margin: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              background: white;
            }
            img {
              max-width: 100%;
              max-height: 100%;
            }
          </style>
        </head>
        <body>
          <img src="${canvas.toDataURL("image/png")}" />
          <script>
            window.onload = () => {
              setTimeout(() => {
                window.print()
                window.close()
              }, 300)
            }
          </script>
        </body>
        </html>
      `)
      printWindow.document.close()
    } catch (err) {
      console.error(err)
      alert("Print failed.")
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 bg-white p-8 rounded-lg shadow">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4"></div>
        <p>Loading your template...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white p-8 border-l-4 border-red-500 rounded-lg shadow">
        <h2 className="text-red-600 text-lg font-semibold mb-2">Error</h2>
        <p className="mb-4">{(error as Error).message}</p>
        <button
          onClick={() => refetch()}
          className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded"
        >
          <RotateCw size={16} />
          Retry
        </button>
      </div>
    )
  }

  const html = data?.config?.html

  if (!html) {
    return (
      <div className="bg-white p-6 text-gray-500 text-center rounded-lg shadow">
        No template found.
      </div>
    )
  }

  return (
    <div className="flex flex-col lg:flex-row gap-6 p-6 max-w-7xl mx-auto">
      <div className="flex-1">
        <h1 className="text-xl font-bold text-gray-800 mb-4">Your Template</h1>
        <div
          ref={cardRef}
          className="bg-white shadow-md rounded-lg p-4"
          dangerouslySetInnerHTML={{ __html: html }}
        />
      </div>

      <div className="flex flex-col gap-4 w-full max-w-xs">
        <button
          onClick={exportPDF}
          disabled={isGenerating}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
        >
          {isGenerating ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <Download size={18} />
          )}
          {isGenerating ? "Generating..." : "Download PDF"}
        </button>

        <button
          onClick={printCard}
          className="flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition"
        >
          <Printer size={18} />
          Print
        </button>

        <div className="text-sm text-gray-500 mt-2">
          <ul className="list-disc pl-4 space-y-1">
            <li>High quality export</li>
            <li>Cross-browser support</li>
            <li>CORS image handling</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default CardView
