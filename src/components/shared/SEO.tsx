import { useEffect } from 'react'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  ogImage?: string
  ogUrl?: string
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image'
  canonicalUrl?: string
  noIndex?: boolean
  structuredData?: object
}

/**
 * SEO Component for managing document head metadata
 *
 * This component dynamically updates meta tags in the document head
 * for better search engine optimization and social media sharing.
 */
export default function SEO({
  title = 'JeridSchool - K12 Education Management System',
  description = 'Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication.',
  keywords = 'education management, K12, school ERP, student management, teacher management, EdTech, school administration',
  ogImage = 'https://jeridschool.tech/og-image.jpg',
  ogUrl,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  noIndex = false,
  structuredData,
}: SEOProps) {
  useEffect(() => {
    // Set the document title
    document.title = title

    // Helper function to create or update meta tags
    const updateMeta = (name: string, content: string) => {
      let meta = document.querySelector(
        `meta[name="${name}"]`
      ) as HTMLMetaElement

      if (!meta) {
        meta = document.createElement('meta')
        meta.name = name
        document.head.appendChild(meta)
      }

      meta.content = content
    }

    // Helper function for Open Graph and Twitter tags
    const updateProperty = (property: string, content: string) => {
      let meta = document.querySelector(
        `meta[property="${property}"]`
      ) as HTMLMetaElement

      if (!meta) {
        meta = document.createElement('meta')
        meta.setAttribute('property', property)
        document.head.appendChild(meta)
      }

      meta.content = content
    }

    // Set basic meta tags
    updateMeta('description', description)
    updateMeta('keywords', keywords)

    // Set robots meta tag
    updateMeta('robots', noIndex ? 'noindex, nofollow' : 'index, follow')

    // Set Open Graph meta tags
    updateProperty('og:title', title)
    updateProperty('og:description', description)
    updateProperty('og:image', ogImage)
    updateProperty('og:type', ogType)
    if (ogUrl) updateProperty('og:url', ogUrl)

    // Set Twitter Card meta tags
    updateMeta('twitter:card', twitterCard)
    updateMeta('twitter:title', title)
    updateMeta('twitter:description', description)
    updateMeta('twitter:image', ogImage)

    // Set canonical URL if provided
    let canonicalTag = document.querySelector(
      'link[rel="canonical"]'
    ) as HTMLLinkElement
    if (canonicalUrl) {
      if (!canonicalTag) {
        canonicalTag = document.createElement('link')
        canonicalTag.rel = 'canonical'
        document.head.appendChild(canonicalTag)
      }
      canonicalTag.href = canonicalUrl
    } else if (canonicalTag) {
      canonicalTag.remove()
    }

    // Add structured data if provided
    let scriptTag = document.querySelector('script[type="application/ld+json"]')
    if (structuredData) {
      if (!scriptTag) {
        scriptTag = document.createElement('script')
        scriptTag.setAttribute('type', 'application/ld+json')
        document.head.appendChild(scriptTag)
      }
      scriptTag.textContent = JSON.stringify(structuredData)
    } else if (scriptTag) {
      scriptTag.remove()
    }

    // Cleanup function
    return () => {
      // We don't remove the meta tags on cleanup as they should persist
      // between route changes, but we could reset them to defaults if needed
    }
  }, [
    title,
    description,
    keywords,
    ogImage,
    ogUrl,
    ogType,
    twitterCard,
    canonicalUrl,
    noIndex,
    structuredData,
  ])

  // This component doesn't render anything visible
  return null
}
