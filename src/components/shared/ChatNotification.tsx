import { MessageSquare } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useUnreadMessages } from '@/hooks/useUnreadMessages'
import { NotificationBadge } from '@/components/ui/notification-badge'
import { Button } from '@/components/ui/button'

interface ChatNotificationProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
}

/**
 * A chat button with notification badge showing unread message count
 */
export function ChatNotification({
  variant = 'outline',
  size = 'default',
  className = '',
}: ChatNotificationProps) {
  const navigate = useNavigate()
  // Get total unread message count across all conversations
  const { unreadCount } = useUnreadMessages()

  return (
    <Button
      variant={variant}
      size={size}
      onClick={() => navigate('/chat')}
      className={`relative ${className}`}
    >
      <MessageSquare className="h-4 w-4 mr-2" />
      <span>Messages</span>
      {unreadCount > 0 && <NotificationBadge count={unreadCount} />}
    </Button>
  )
}
