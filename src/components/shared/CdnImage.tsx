import { useState, useEffect } from 'react'

// Simple constant definition right in this file
const DEFAULT_AVATAR = 'https://api.dicebear.com/7.x/avataaars/svg'

export interface CdnImageProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  src: string | null | undefined
  alt?: string
  width?: number | string
  height?: number | string
  className?: string
  fallbackSrc?: string
  onLoad?: () => void
  onError?: () => void
}

/**
 * Renders an image from the CDN with error handling and fallback
 */
export default function CdnImage({
  src,
  alt,
  width,
  height,
  className,
  fallbackSrc,
  onLoad,
  onError,
  ...props
}: CdnImageProps) {
  const [error, setError] = useState(false)
  const [imageUrl, setImageUrl] = useState<string>('')

  useEffect(() => {
    if (!src) {
      setImageUrl(fallbackSrc || DEFAULT_AVATAR)
      return
    }

    // Check if this is a CDN URL that needs proxying
    if (
      src.includes('cdn.jeridschool.tech') ||
      src.includes('**************')
    ) {
      // If it's not already proxied, proxy it
      if (!src.includes('/health/proxy/image')) {
        console.log('Proxying CDN URL for preview:', src)
        setImageUrl(
          `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(src)}`
        )
        return
      }
    }

    // For all other URLs, use the source directly
    setImageUrl(src)
  }, [src, fallbackSrc])

  /**
   * Handle image load error by showing fallback
   */
  const handleError = () => {
    console.error('Failed to load image:', imageUrl)
    setError(true)

    // Call the user provided error handler
    if (onError) {
      onError()
    }
  }

  /**
   * Handle successful image load
   */
  const handleLoad = () => {
    // Call the user provided load handler
    if (onLoad) {
      onLoad()
    }
  }

  // Show fallback image if there is an error
  if (error) {
    return (
      <img
        src={fallbackSrc || DEFAULT_AVATAR}
        alt={alt || 'Default Avatar'}
        width={width}
        height={height}
        className={className}
        {...props}
      />
    )
  }

  // Show the actual image
  return (
    <img
      src={imageUrl}
      alt={alt || 'Image'}
      width={width}
      height={height}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
      {...props}
    />
  )
}
