import React from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { Card, CardContent } from '@/components/ui/card'

interface QRCodeGeneratorProps {
  value: string
  size?: number
  level?: 'L' | 'M' | 'Q' | 'H'
  bgColor?: string
  fgColor?: string
  includeMargin?: boolean
  className?: string
  logoUrl?: string
}

/**
 * Enhanced QR Code Generator component
 * Supports custom styling, error correction levels, and logo overlay
 */
const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({
  value,
  size = 128,
  level = 'M',
  bgColor = '#ffffff',
  fgColor = '#000000',
  includeMargin = true,
  className = '',
  logoUrl,
}) => {
  // Generate the base URL for student profile
  const getPublicUrl = (path: string): string => {
    // In development, use localhost
    if (import.meta.env.DEV) {
      return `http://localhost:5173${path}`
    }
    // In production, use the actual domain
    return `https://jeridschool.tech${path}`
  }

  // Format the value if it's a student ID to create a proper URL
  const formattedValue = value.startsWith('/') ? getPublicUrl(value) : value

  return (
    <Card className={`inline-block ${className}`}>
      <CardContent className="p-2 flex justify-center items-center">
        <div className="relative">
          <QRCodeSVG
            value={formattedValue}
            size={size}
            level={level}
            bgColor={bgColor}
            fgColor={fgColor}
            includeMargin={includeMargin}
          />

          {logoUrl && (
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                width: size,
                height: size,
              }}
            >
              <div
                className="bg-white rounded-full p-1 flex items-center justify-center"
                style={{
                  width: size * 0.25,
                  height: size * 0.25,
                }}
              >
                <img
                  src={logoUrl}
                  alt="Logo"
                  className="w-full h-full object-contain rounded-full"
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default QRCodeGenerator
