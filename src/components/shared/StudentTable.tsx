import { Student, Subject } from '@/interface/StudentInteface'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'

interface StudentTableProps {
  students: Student[]
  subjects?: Subject[]
  onProfileClick?: (student: Student) => void
  onAbsenceClick?: (student: Student) => void
  onScoreClick?: (student: Student) => void
  showActions?: boolean
  showScore?: boolean
}

/**
 * A reusable table component for displaying student information
 * @param {StudentTableProps} props - The props for the StudentTable component
 * @returns {JSX.Element} The rendered StudentTable component
 */
export function StudentTable({
  students,
  subjects = [],
  onProfileClick,
  onAbsenceClick,
  onScoreClick,
  showActions = true,
  showScore = false,
}: StudentTableProps): JSX.Element {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Full Name</TableHead>
          <TableHead>CIN</TableHead>
          {showScore &&
            subjects.map((subject) => (
              <TableHead key={subject.id}>{subject.name}</TableHead>
            ))}
          {showActions && <TableHead>Actions</TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {students.map((student) => (
          <TableRow key={student.id}>
            <TableCell>
              {student.firstname} {student.lastname}
            </TableCell>
            <TableCell>{student.cin}</TableCell>
            {showScore &&
              subjects.map((subject) => {
                const studentSubject = student.subjects?.find(
                  (s) => 'subjectId' in s && s.subjectId === subject.id
                )
                const score =
                  studentSubject && 'grades' in studentSubject
                    ? studentSubject.grades.reduce(
                        (acc, grade) => acc + grade.value,
                        0
                      ) / studentSubject.grades.length
                    : 0
                return (
                  <TableCell key={subject.id}>{score.toFixed(2)}</TableCell>
                )
              })}
            {showActions && (
              <TableCell>
                <div className="flex space-x-2">
                  {onProfileClick && (
                    <Button onClick={() => onProfileClick(student)}>
                      Profile
                    </Button>
                  )}
                  {onAbsenceClick && (
                    <Button onClick={() => onAbsenceClick(student)}>
                      Absences
                    </Button>
                  )}
                  {onScoreClick && (
                    <Button onClick={() => onScoreClick(student)}>
                      Scores
                    </Button>
                  )}
                </div>
              </TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
