import React from 'react'
import CdnImage from './CdnImage'
import { DEFAULT_AVATAR } from '@/constants'

interface CorsProxyImageProps {
  src: string | null
  alt: string
  className?: string
  fallback?: React.ReactNode
}

/**
 * A component that handles CORS issues with images by using the CdnImage component
 * which has proper handling for CDN URLs.
 */
const CorsProxyImage: React.FC<CorsProxyImageProps> = ({
  src,
  alt,
  className = '',
  fallback,
}) => {
  // If there's no source, show the fallback
  if (!src) {
    return fallback ? <>{fallback}</> : null
  }

  // Use the CdnImage component which has proper handling for CDN URLs
  return (
    <CdnImage
      src={src}
      alt={alt}
      className={className}
      fallbackSrc={DEFAULT_AVATAR}
      onError={() => {
        console.warn('Image failed to load:', src)
        // The fallback will be shown by CdnImage component
      }}
    />
  )
}

export default CorsProxyImage
