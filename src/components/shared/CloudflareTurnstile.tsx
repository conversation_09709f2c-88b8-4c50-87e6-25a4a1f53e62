import { forwardRef, useImperativeHandle, useRef } from 'react'
import { Turnstile } from '@marsidev/react-turnstile'
import { toast } from 'sonner'

export interface CloudflareTurnstileProps {
  /**
   * Callback function that is called when the user successfully completes the challenge
   * @param token The token returned by Cloudflare Turnstile
   */
  onSuccess?: (token: string) => void
  
  /**
   * Callback function that is called when the challenge expires
   */
  onExpire?: () => void
  
  /**
   * Callback function that is called when an error occurs
   * @param error The error message
   */
  onError?: (error: string) => void
  
  /**
   * The site key provided by Cloudflare
   * Default: Uses the environment variable VITE_CLOUDFLARE_SITE_KEY or a fallback value
   */
  siteKey?: string
  
  /**
   * Additional CSS class names to apply to the container
   */
  className?: string
  
  /**
   * Whether to show a toast message on error
   * Default: true
   */
  showToasts?: boolean
}

export interface CloudflareTurnstileRef {
  /**
   * Reset the captcha widget
   */
  reset: () => void
  
  /**
   * Get the current token value
   */
  getToken: () => string | null
}

/**
 * Cloudflare Turnstile Captcha component
 * 
 * This component wraps the @marsidev/react-turnstile package to provide
 * a consistent interface for using Cloudflare Turnstile in the application.
 * 
 * @example
 * ```tsx
 * const captchaRef = useRef<CloudflareTurnstileRef>(null)
 * const [token, setToken] = useState<string | null>(null)
 * 
 * <CloudflareTurnstile
 *   ref={captchaRef}
 *   onSuccess={setToken}
 *   onError={() => console.error('Captcha error')}
 * />
 * 
 * // Later, to reset the captcha:
 * captchaRef.current?.reset()
 * ```
 */
const CloudflareTurnstile = forwardRef<CloudflareTurnstileRef, CloudflareTurnstileProps>(
  ({ 
    onSuccess, 
    onExpire, 
    onError, 
    siteKey = import.meta.env.VITE_CLOUDFLARE_SITE_KEY || '0x4AAAAAABd-mE5vPUgrVDgc',
    className = '',
    showToasts = true
  }, ref) => {
    const turnstileRef = useRef<any>(null)
    const tokenRef = useRef<string | null>(null)

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      reset: () => {
        if (turnstileRef.current) {
          turnstileRef.current.reset()
          tokenRef.current = null
        }
      },
      getToken: () => tokenRef.current
    }))

    const handleSuccess = (token: string) => {
      tokenRef.current = token
      if (onSuccess) onSuccess(token)
    }

    const handleError = (error: string) => {
      tokenRef.current = null
      if (showToasts) toast.error('Captcha validation failed')
      if (onError) onError(error)
    }

    const handleExpire = () => {
      tokenRef.current = null
      if (showToasts) toast.error('Captcha expired. Please verify again')
      if (onExpire) onExpire()
    }

    return (
      <div className={`flex justify-center ${className}`}>
        <Turnstile
          siteKey={siteKey}
          onSuccess={handleSuccess}
          onError={handleError}
          onExpire={handleExpire}
          ref={turnstileRef}
        />
      </div>
    )
  }
)

CloudflareTurnstile.displayName = 'CloudflareTurnstile'

export default CloudflareTurnstile
