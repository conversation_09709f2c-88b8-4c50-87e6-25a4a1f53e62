import { useState } from 'react'
import { Student } from '@/interface/types'
import { Table } from '../ui/table'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'

interface Note {
  id: number
  content: string
  date: string
  subject: string
}

interface StudentNotesProps {
  userRole: 'student' | 'teacher' | 'parent' | 'admin'
  student?: Student
  onNoteAdd?: (studentId: string | number, note: Omit<Note, 'id'>) => void
}

export default function StudentNotes({
  userRole,
  student,
  onNoteAdd,
}: StudentNotesProps) {
  const [notes] = useState<Note[]>([])
  const [newNote, setNewNote] = useState('')

  const canAddNotes = userRole === 'teacher' || userRole === 'admin'

  return (
    <div className="p-4">
      <Table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Subject</th>
            <th>Note</th>
          </tr>
        </thead>
        <tbody>
          {notes.map((note) => (
            <tr key={note.id}>
              <td>{note.date}</td>
              <td>{note.subject}</td>
              <td>{note.content}</td>
            </tr>
          ))}
        </tbody>
      </Table>

      {canAddNotes && (
        <div className="mt-4">
          <Textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Add a new note..."
          />
          <Button
            className="mt-2"
            onClick={() => {
              if (newNote.trim() && student) {
                onNoteAdd?.(student.id, {
                  content: newNote,
                  date: new Date().toISOString(),
                  subject: 'General',
                })
                setNewNote('')
              }
            }}
          >
            Add Note
          </Button>
        </div>
      )}
    </div>
  )
}
