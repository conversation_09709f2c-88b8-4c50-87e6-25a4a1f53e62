import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { UserProfile } from '@/lib/user/profile'
import { QRCodeSVG } from 'qrcode.react'
import { getProxiedPath } from '@/lib/corsProxy'

interface EstablishmentInfo {
  id: string
  name: string
  logo?: string
}

interface EstablishmentCardProps {
  userProfile: UserProfile
  className?: string
}

const EstablishmentCard: React.FC<EstablishmentCardProps> = ({
  userProfile,
  className,
}) => {
  const [establishment, setEstablishment] = useState<EstablishmentInfo | null>(
    null
  )
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchEstablishmentData = async () => {
      try {
        setLoading(true)
        // Get the API base URL from environment or use a fallback
        const apiBaseUrl =
          import.meta.env.VITE_API_URL || 'http://localhost:3000'
        const IS_DEV = import.meta.env.DEV

        // Construct the endpoint URL
        const endpointPath = '/public/etablissement/info/b32ab2d3-afeb-4db1-8738-19cb45ddd9ea'

        // Use the proxy in development mode
        const requestUrl = IS_DEV
          ? getProxiedPath(endpointPath, 'api')
          : `${apiBaseUrl}${endpointPath}`

        console.log('Fetching establishment data from:', requestUrl)

        // Try direct fetch first
        try {
          const response = await fetch(requestUrl, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
          })

          if (response.ok) {
            const data = await response.json()
            console.log('Establishment data received:', data)
            setEstablishment(data)
            setLoading(false)
            return // Exit early if successful
          }
        } catch (directFetchError) {
          console.warn(
            'Direct fetch failed, using fallback data:',
            directFetchError
          )
        }

        // If direct fetch fails, use hardcoded data
        setEstablishment({
          id: 'b32ab2d3-afeb-4db1-8738-19cb45ddd9ea',
          name: 'khjaled',
          logo: 'https://cdn.jeridschool.tech/files/eaed44d9-e6c4-4f7b-a559-15c6f3021b8b',
        })
      } catch (error) {
        console.error('Error in fetchEstablishmentData:', error)
        // Fallback already handled in the inner try-catch
      } finally {
        setLoading(false)
      }
    }

    fetchEstablishmentData()
  }, [])

  return (
    <Card
      className={`establishment-card overflow-hidden ${className} border-2 border-primary/20`}
    >
      <div className="relative">
        {/* Card Header with Establishment Branding */}
        <div className="bg-primary h-24 sm:h-28 flex items-center justify-between px-4 sm:px-6">
          {/* Establishment Logo */}
          <div className="flex items-center">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white rounded-full p-1 flex items-center justify-center">
              {loading ? (
                <div className="w-10 h-10 sm:w-14 sm:h-14 bg-gray-200 rounded-full animate-pulse" />
              ) : establishment?.logo ? (
                <img
                  src="https://cdn.jeridschool.tech/cdn/files/eaed44d9-e6c4-4f7b-a559-15c6f3021b8b"
                  alt={`${establishment.name} logo`}
                  className="w-10 h-10 sm:w-14 sm:h-14 object-contain rounded-full"
                  onError={(e) => {
                    console.log('Logo failed to load, showing initials')
                    e.currentTarget.style.display = 'none'
                    const parent = e.currentTarget.parentElement
                    if (parent) {
                      const div = document.createElement('div')
                      div.className =
                        'w-10 h-10 sm:w-14 sm:h-14 bg-gray-100 rounded-full flex items-center justify-center text-primary font-bold'
                      div.textContent =
                        establishment.name?.substring(0, 2).toUpperCase() ||
                        'KH'
                      parent.appendChild(div)
                    }
                  }}
                />
              ) : (
                <div className="w-10 h-10 sm:w-14 sm:h-14 bg-gray-100 rounded-full flex items-center justify-center text-primary font-bold">
                  {establishment?.name?.substring(0, 2).toUpperCase() || 'KH'}
                </div>
              )}
            </div>

            {/* Establishment Name */}
            <div className="ml-3 sm:ml-4 text-white">
              <h3 className="font-bold text-base sm:text-lg uppercase tracking-wide">
                {loading ? 'Loading...' : establishment?.name || 'KHJALED'}
              </h3>
              <p className="text-xs opacity-80 font-medium bg-white/20 px-2 py-0.5 rounded-sm mt-0.5">
                OFFICIAL
              </p>
            </div>
          </div>

          {/* QR Code */}
          <div className="bg-white p-1 rounded-md">
            <QRCodeSVG
              value={`${userProfile.id}|${userProfile.role}|${userProfile.email}`}
              size={40}
              level="M"
            />
          </div>
        </div>

        {/* User Photo - Overlapping the header */}
        <div className="absolute -bottom-14 sm:-bottom-16 left-1/2 transform -translate-x-1/2">
          <div className="h-28 w-28 sm:h-32 sm:w-32 bg-[#d8e562] rounded-full border-4 border-white shadow-lg flex items-center justify-center overflow-hidden">
            {userProfile.avatar ? (
              <img
                src={userProfile.avatar}
                alt={`${userProfile.fullname || 'User'} avatar`}
                className="h-full w-full object-cover rounded-full"
                onError={(e) => {
                  // Fallback to initials if image fails to load
                  e.currentTarget.style.display = 'none'
                  const parent = e.currentTarget.parentElement
                  if (parent) {
                    const span = document.createElement('span')
                    span.className = 'text-5xl font-bold'
                    span.textContent =
                      (userProfile.firstname?.[0] || '') +
                        (userProfile.lastname?.[0] || '') ||
                      userProfile.fullname?.substring(0, 2).toUpperCase() ||
                      'XX'
                    parent.appendChild(span)
                  }
                }}
              />
            ) : (
              <span className="text-5xl font-bold">
                {(userProfile.firstname?.[0] || '') +
                  (userProfile.lastname?.[0] || '') ||
                  userProfile.fullname?.substring(0, 2).toUpperCase() ||
                  'XX'}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Card Content */}
      <CardContent className="pt-16 sm:pt-20 pb-4 sm:pb-6 relative">
        <div className="text-center mb-4 sm:mb-6">
          <div className="mb-3 text-[#6366f1] text-sm font-semibold uppercase tracking-wider bg-[#6366f1]/5 py-1 rounded-full">
            {establishment?.name || 'KHJALED'}
          </div>
          <h2 className="text-lg sm:text-xl font-bold">xl_sudo xl_sudo</h2>
          <p className="text-sm text-muted-foreground capitalize">Admin</p>
        </div>

        <div className="space-y-2 sm:space-y-3">
          {/* User Details */}
          <div className="flex flex-col items-center gap-2 text-xs sm:text-sm">
            <div className="flex items-center gap-1">
              <div className="text-muted-foreground">Email:</div>
              <div className="font-medium"><EMAIL></div>
            </div>

            <div className="flex items-center gap-1 mt-1">
              <div className="text-muted-foreground">Valid Until:</div>
              <div className="font-medium text-[#6366f1]">
                <span className="text-[#6366f1]">Apr 20, 2026</span>
              </div>
            </div>
          </div>
        </div>

        {/* Card Footer */}
        <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t text-center text-xs text-muted-foreground">
          <p>This card is the property of {establishment?.name || 'khjaled'}</p>
          <p className="text-[10px] sm:text-xs">
            If found, please return to the administration office
          </p>

          {/* Signature line */}
          <div className="mt-3 flex items-center justify-center">
            <div className="w-24 border-t border-dashed border-gray-400 mx-2"></div>
            <span className="text-[10px] text-gray-500">Signature</span>
          </div>

          {/* Download button */}
          <button
            onClick={() => {
              // Simple print function for the ID card
              window.print()
            }}
            className="print-button mt-4 text-xs bg-[#6366f1]/10 hover:bg-[#6366f1]/20 text-[#6366f1] py-1.5 px-4 rounded-full transition-colors duration-200 inline-flex items-center gap-1.5"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Download ID Card
          </button>
        </div>
      </CardContent>
    </Card>
  )
}

export default EstablishmentCard
