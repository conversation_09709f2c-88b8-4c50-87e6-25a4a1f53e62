// components/shared/ActivityModal.tsx
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ActivityItem, ActivityItemProps, ActivityAction } from './ActivityItem'
import { cn } from '@/lib/utils'

interface ActivityModalProps {
  activities: (ActivityItemProps & { action?: ActivityAction })[]
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  className?: string
}

export const ActivityModal = ({
  activities,
  open,
  onOpenChange,
  title = 'All Activities',
  className,
}: ActivityModalProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn('max-h-[80vh] overflow-y-auto sm:max-w-2xl', className)}
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="divide-y">
          {activities.map((activity, index) => (
            <ActivityItem
              key={index}
              title={activity.title}
              time={activity.time}
              status={activity.status}
              action={activity.action}
              description={activity.description}
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}
