import { useState, useEffect } from 'react'
import { Calendar } from 'lucide-react'
import { api } from '@/lib/api/axios-instance'

interface Event {
  id: string
  title: string
  date: string
  time?: string
  type: string
}

export default function EventsList() {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)
        const response = await api.get<Event[]>('/events/upcoming')

        // Axios responses don't have an 'ok' property, but we can check the status
        if (
          response.status < 200 ||
          (response.status >= 300 && response.status !== 404)
        ) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        setEvents(response.data)
        setError(null)
      } catch (err) {
        console.error('Error fetching upcoming events:', err)
        setError('Failed to load upcoming events')
        // Fallback to empty array
        setEvents([])
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [])

  if (loading) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        Loading events...
      </div>
    )
  }

  if (error) {
    return <div className="py-4 text-center text-red-500">{error}</div>
  }

  if (events.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        No upcoming events
      </div>
    )
  }

  // Format date for display
  const formatEventDate = (dateStr: string, timeStr?: string) => {
    const date = new Date(dateStr)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Check if it's today or tomorrow
    if (date.toDateString() === today.toDateString()) {
      return `Today${timeStr ? `, ${timeStr}` : ''}`
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow${timeStr ? `, ${timeStr}` : ''}`
    } else {
      // Format as day of week + date
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      }
      return `${date.toLocaleDateString('en-US', options)}${timeStr ? `, ${timeStr}` : ''}`
    }
  }

  // Get color based on event type
  const getEventColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'meeting':
        return 'text-blue-600'
      case 'exam':
        return 'text-red-600'
      case 'activity':
        return 'text-purple-600'
      case 'holiday':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-4">
      {events.map((event) => (
        <div key={event.id} className="flex items-start space-x-3">
          <Calendar className={`h-5 w-5 ${getEventColor(event.type)} mt-0.5`} />
          <div>
            <p className="font-medium">{event.title}</p>
            <p className="text-sm text-gray-500">
              {formatEventDate(event.date, event.time)}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}
