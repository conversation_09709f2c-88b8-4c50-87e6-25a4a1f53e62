import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Teacher, Subject, Establishment } from '@/interface/types'
import { useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import FileUploader from '@/components/shared/FileUploader'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import { FormDatePicker } from '@/components/ui/form-date-picker'

const teacherSchema = z.object({
  cin: z.string().min(1, 'CIN is required'),
  firstname: z.string().min(1, 'First name is required'),
  lastname: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email format'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .optional(),
  title: z.string().min(1, 'Title is required'),
  gender: z.enum(['male', 'female'] as const),
  birthday: z.date().optional(),
  avatar: z.string().url().optional(),
  isActive: z.boolean().default(true),
})

type TeacherFormValues = z.infer<typeof teacherSchema>
export type { TeacherFormValues }

interface TeacherFormProps {
  initialData?: Partial<Teacher>
  subjects?: Subject[]
  establishments?: Establishment[]
  onSubmit: (data: TeacherFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing teachers
 * @param {TeacherFormProps} props - The props for the TeacherForm component
 * @returns {JSX.Element} The rendered TeacherForm component
 */
export function TeacherForm({
  initialData,
  onSubmit,
  onCancel,
  submitLabel,
}: TeacherFormProps) {
  const [formError, setFormError] = useState<string | null>(null)

  // Normalize the gender value from server
  const normalizeGender = (gender: string | undefined): 'male' | 'female' => {
    if (!gender) return 'male' // Default to male if no gender is provided
    return gender.toLowerCase() as 'male' | 'female'
  }

  const form = useForm<TeacherFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      cin: initialData?.cin || '',
      firstname: initialData?.firstname || '',
      lastname: initialData?.lastname || '',
      email: initialData?.email || '',
      password: '',
      title: initialData?.title || '',
      gender: normalizeGender(initialData?.gender),
      birthday: initialData?.birthday
        ? new Date(initialData.birthday)
        : undefined,
      avatar: initialData?.avatar || '',
      isActive: initialData?.isActive ?? true,
    },
    onSubmit: async ({ value }) => {
      try {
        setFormError(null)
        await onSubmit(value)
      } catch (error: any) {
        console.error('Form submission error:', error)
        if (error.response?.data?.message) {
          setFormError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(', ')
              : error.response.data.message
          )
        } else {
          setFormError('An error occurred while submitting the form')
        }
      }
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      {formError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{formError}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="cin"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'CIN is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="cin">CIN</Label>
              <Input
                id="cin"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter CIN"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="firstname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'First name is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="firstname">First Name</Label>
              <Input
                id="firstname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter first name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="lastname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Last name is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="lastname">Last Name</Label>
              <Input
                id="lastname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter last name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
                return 'Invalid email format'
              }
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter email"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        {!initialData && (
          <form.Field
            name="password"
            validators={{
              onChange: ({ value }) => {
                if (!value) return 'Password is required'
                if (value.length < 6) {
                  return 'Password must be at least 6 characters'
                }
                return undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Enter password"
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>
        )}

        <form.Field
          name="title"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Title is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Select
                value={field.state.value}
                onValueChange={field.handleChange}
              >
                <SelectTrigger id="title">
                  <SelectValue placeholder="Select title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Professor">Professor</SelectItem>
                  <SelectItem value="Dr.">Dr.</SelectItem>
                  <SelectItem value="Assistant Professor">
                    Assistant Professor
                  </SelectItem>
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="gender"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Gender is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={field.state.value}
                onValueChange={(value: 'male' | 'female') =>
                  field.handleChange(value)
                }
              >
                <SelectTrigger id="gender">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="birthday">
          {(field) => (
            <FormDatePicker
              id="birthday"
              label="Birthday"
              value={field.state.value}
              onChange={(date) => field.handleChange(date)}
              onBlur={field.handleBlur}
              placeholder="Select birthday"
            />
          )}
        </form.Field>

        <form.Field name="avatar">
          {(field) => (
            <FileUploader
              label="Teacher Avatar"
              defaultPreview={field.state.value || DEFAULT_AVATAR}
              onFileUploaded={(url: string) => field.handleChange(url)}
              isAvatar={true}
              previewComponent={(previewUrl: string) => (
                <CdnImage
                  src={previewUrl}
                  alt="Teacher avatar"
                  className="h-28 w-28 rounded-full object-cover border-2 border-gray-200 shadow-sm"
                />
              )}
            />
          )}
        </form.Field>

        <form.Field name="isActive">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <Select
                value={field.state.value.toString()}
                onValueChange={(value) => field.handleChange(value === 'true')}
              >
                <SelectTrigger id="isActive">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
