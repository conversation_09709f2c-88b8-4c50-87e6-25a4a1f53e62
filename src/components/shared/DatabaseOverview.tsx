import { ReactNode, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Users,
  UserCheck,
  GraduationCap,
  BookOpen,
  Database,
  Layers,
  FileText,
  BarChart3,
  ArrowRight,
  Calendar,
  Loader2,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  DashboardStats,
  RecentActivity,
  SystemHealth,
} from '@/lib/api/services/dashboard-service-new'
import { useDashboard } from '@/hooks/useDashboard'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ActivityLog, ActivityLogItem } from './ActivityLog'
import { ActivityLogList } from './ActivityLogList'

interface DatabaseCardProps {
  title: string
  count: number | string
  icon: ReactNode
  description?: string
  color?: 'blue' | 'green' | 'purple' | 'amber' | 'rose' | 'indigo' | 'slate'
  onClick?: () => void
}

const colorVariants = {
  blue: 'bg-blue-50 border-blue-200 text-blue-700',
  green: 'bg-green-50 border-green-200 text-green-700',
  purple: 'bg-purple-50 border-purple-200 text-purple-700',
  amber: 'bg-amber-50 border-amber-200 text-amber-700',
  rose: 'bg-rose-50 border-rose-200 text-rose-700',
  indigo: 'bg-indigo-50 border-indigo-200 text-indigo-700',
  slate: 'bg-slate-50 border-slate-200 text-slate-700',
}

const iconColorVariants = {
  blue: 'bg-blue-100 text-blue-700',
  green: 'bg-green-100 text-green-700',
  purple: 'bg-purple-100 text-purple-700',
  amber: 'bg-amber-100 text-amber-700',
  rose: 'bg-rose-100 text-rose-700',
  indigo: 'bg-indigo-100 text-indigo-700',
  slate: 'bg-slate-100 text-slate-700',
}

const DatabaseCard = ({
  title,
  count,
  icon,
  description,
  color = 'blue',
  onClick,
}: DatabaseCardProps) => {
  return (
    <Card
      className={cn(
        'overflow-hidden border transition-all duration-300 hover:shadow-md',
        colorVariants[color],
        onClick && 'cursor-pointer hover:scale-[1.02]'
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <div className={cn('p-2 rounded-full', iconColorVariants[color])}>
            {icon}
          </div>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold">{count}</div>
      </CardContent>
      {onClick && (
        <CardFooter className="pt-0">
          <Button
            variant="ghost"
            className="p-0 h-auto text-sm font-medium hover:bg-transparent hover:underline"
          >
            View details <ArrowRight className="ml-1 h-3 w-3" />
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

interface RecentItemProps {
  activity: RecentActivity
}

const RecentItem = ({ activity }: RecentItemProps) => {
  // Convert RecentActivity to ActivityLogItem
  const activityLogItem: ActivityLogItem = {
    id: activity.id,
    type: activity.type,
    action: activity.action.toLowerCase() as 'create' | 'update' | 'delete',
    entityId: activity.entityId,
    entityName: activity.entityName,
    timestamp: activity.timestamp,
    details: activity.details,
    performedBy: activity.performedBy,
  }

  return <ActivityLog activity={activityLogItem} />
}

interface DatabaseOverviewProps {
  role: 'admin' | 'super_admin' | 'teacher'
  onCardClick?: (section: string) => void
}

interface DashboardHookReturn {
  stats: DashboardStats
  activities: RecentActivity[]
  systemHealth: SystemHealth
  teacherStats?: any
  isLoading: boolean
  error: string | null
}

export function DatabaseOverview({ role, onCardClick }: DatabaseOverviewProps) {
  const [isActivitiesModalOpen, setIsActivitiesModalOpen] = useState(false)
  const [allActivities, setAllActivities] = useState<RecentActivity[]>([])
  const [isLoadingAllActivities, setIsLoadingAllActivities] = useState(false)

  // Default values for the dashboard data
  const defaultDashboardData: DashboardHookReturn = {
    stats: {} as DashboardStats,
    activities: [],
    systemHealth: {
      storage: { used: 0, total: 1, unit: 'GB' },
      records: { total: 0, students: 0, teachers: 0, parents: 0, other: 0 },
      backups: {
        last: new Date().toISOString(),
        next: new Date().toISOString(),
        status: 'ok',
      },
      performance: { responseTime: 0, unit: 'ms', status: 'good' },
      scheduledTasks: [],
    },
    isLoading: false,
    error: null,
  }

  // Use the hook and provide proper type
  const dashboardData = useDashboard(role) || defaultDashboardData

  const {
    stats,
    activities: recentActivities,
    systemHealth,
    isLoading,
    error,
    teacherStats,
  } = dashboardData

  const handleViewAllActivities = async () => {
    setIsActivitiesModalOpen(true)
    setIsLoadingAllActivities(true)

    try {
      const response = await fetch('/api/database/activities', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setAllActivities(data)
    } catch (error) {
      console.error('Error fetching all activities:', error)
      setAllActivities(recentActivities)
    } finally {
      setIsLoadingAllActivities(false)
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-red-500">
        <p>Error loading dashboard data</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold tracking-tight">
            Database Overview
          </h2>
          <Badge variant="outline" className="flex items-center gap-1">
            <Database className="h-3 w-3" />
            Last updated: {new Date().toLocaleTimeString()}
          </Badge>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {role === 'admin' && (
            <>
              <DatabaseCard
                title="Students"
                count={(stats as DashboardStats).students.total}
                icon={<Users className="h-5 w-5" />}
                color="blue"
                onClick={() => onCardClick?.('StudentManagement')}
              />

              <DatabaseCard
                title="Teachers"
                count={(stats as DashboardStats).teachers.total}
                icon={<UserCheck className="h-5 w-5" />}
                color="green"
                onClick={() => onCardClick?.('TeacherManagement')}
              />

              <DatabaseCard
                title="Parents"
                count={(stats as DashboardStats).parents.total}
                icon={<Users className="h-5 w-5" />}
                color="purple"
                onClick={() => onCardClick?.('ParentManagement')}
              />

              <DatabaseCard
                title="Classes"
                count={(stats as DashboardStats).classes.total}
                icon={<GraduationCap className="h-5 w-5" />}
                color="amber"
                onClick={() => onCardClick?.('ClassManagement')}
              />

              <DatabaseCard
                title="Subjects"
                count={(stats as DashboardStats).subjects.total}
                icon={<BookOpen className="h-5 w-5" />}
                color="indigo"
                onClick={() => onCardClick?.('SubjectManagement')}
              />

              <DatabaseCard
                title="Classrooms"
                count={(stats as DashboardStats).classrooms.total}
                icon={<Layers className="h-5 w-5" />}
                color="rose"
                onClick={() => onCardClick?.('ClassroomManagement')}
              />
            </>
          )}

          {role === 'super_admin' && (
            <>
              <DatabaseCard
                title="Admins"
                count={(stats as DashboardStats).admins.total}
                icon={<UserCheck className="h-5 w-5" />}
                color="blue"
                onClick={() => onCardClick?.('AdminManagement')}
              />

              {/* Modified School Card */}
              {/* <DatabaseCard
                title="Schools"
                count="View Schools"
                icon={<School className="h-5 w-5" />}
                color="green"
                onClick={() => {
                  // Get the establishment URL from stats or context
                  const establishment = stats?.establishment?.url || 'aa'; // fallback to 'aa' if not found
                  // Construct the full URL with the subdomain
                  const schoolUrl = `https://${establishment}.jeridschool.tech`;
                  window.location.href = schoolUrl;
                }}
              /> */}

              <DatabaseCard
                title="Total Students"
                count={(stats as DashboardStats).students.total}
                icon={<Users className="h-5 w-5" />}
                color="purple"
              />

              <DatabaseCard
                title="Total Teachers"
                count={(stats as DashboardStats).teachers.total}
                icon={<UserCheck className="h-5 w-5" />}
                color="amber"
              />
            </>
          )}

          {role === 'teacher' && (
            <>
              <DatabaseCard
                title="My Classes"
                count={(teacherStats as any)?.classes || 0}
                icon={<GraduationCap className="h-5 w-5" />}
                color="blue"
                onClick={() => onCardClick?.('MyClasses')}
              />

              <DatabaseCard
                title="My Students"
                count={(teacherStats as any)?.students || 0}
                icon={<Users className="h-5 w-5" />}
                color="green"
                onClick={() => onCardClick?.('MyStudents')}
              />

              <DatabaseCard
                title="Subjects"
                count={(teacherStats as any)?.subjects || 0}
                icon={<BookOpen className="h-5 w-5" />}
                color="purple"
                onClick={() => onCardClick?.('MySubjects')}
              />

              <DatabaseCard
                title="Assignments"
                count={(teacherStats as any)?.assignments || 0}
                icon={<FileText className="h-5 w-5" />}
                color="amber"
                onClick={() => onCardClick?.('Assignments')}
              />
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <ActivityLogList
          activities={recentActivities.map((activity) => ({
            id: activity.id,
            type: activity.type,
            action: activity.action.toLowerCase() as
              | 'create'
              | 'update'
              | 'delete',
            entityId: activity.entityId,
            entityName: activity.entityName,
            timestamp: activity.timestamp,
            details: activity.details,
            performedBy: activity.performedBy,
          }))}
          title="Recent Database Activities"
          description="Latest changes to the database records"
          onViewAll={handleViewAllActivities}
          className="lg:col-span-2 shadow-md"
        />

        <div className="space-y-6">
          <Card className="shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                // Type assertion to help TypeScript understand the structure
                const typedHealth = systemHealth as SystemHealth
                if (!typedHealth) {
                  return (
                    <div className="py-4 text-center text-muted-foreground">
                      System health data not available
                    </div>
                  )
                }

                return (
                  <>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">
                          Storage Usage
                        </span>
                        <span className="text-sm font-medium">
                          {Math.round(
                            (typedHealth.storage.used /
                              typedHealth.storage.total) *
                              100
                          )}
                          %
                        </span>
                      </div>
                      <Progress
                        value={Math.round(
                          (typedHealth.storage.used /
                            typedHealth.storage.total) *
                            100
                        )}
                        className="h-2 bg-blue-100"
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">
                          Records Count
                        </span>
                        <span className="text-sm font-medium">
                          {typedHealth.records.total.toLocaleString()}
                        </span>
                      </div>
                      <Progress
                        value={75} // This is just a visual indicator
                        className="h-2 bg-green-100"
                      />
                    </div>

                    <div className="pt-2">
                      <div className="flex justify-between text-sm">
                        <span>Last Backup:</span>
                        <span className="font-medium">
                          {new Date(typedHealth.backups.last).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Next Backup:</span>
                        <span className="font-medium">
                          {new Date(typedHealth.backups.next).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm mt-2">
                        <span>System Status:</span>
                        <Badge
                          variant={
                            typedHealth.performance.status === 'good'
                              ? 'default'
                              : 'destructive'
                          }
                        >
                          {typedHealth.performance.status.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </>
                )
              })()}
            </CardContent>
          </Card>

          {systemHealth &&
          typeof systemHealth === 'object' &&
          'scheduledTasks' in systemHealth &&
          Array.isArray((systemHealth as SystemHealth).scheduledTasks) &&
          ((systemHealth as SystemHealth).scheduledTasks?.length ?? 0) > 0 ? (
            <Card className="shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Scheduled Tasks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(systemHealth as SystemHealth).scheduledTasks?.map(
                    (
                      task: {
                        name: string
                        description: string
                        frequency: string
                      },
                      index: number
                    ) => (
                      <div
                        key={index}
                        className="flex justify-between items-center"
                      >
                        <div>
                          <p className="font-medium">{task.name}</p>
                          <p className="text-xs text-gray-500">
                            {task.description}
                          </p>
                        </div>
                        <Badge
                          variant={
                            task.frequency === 'daily' ? 'default' : 'outline'
                          }
                        >
                          {task.frequency.charAt(0).toUpperCase() +
                            task.frequency.slice(1)}
                        </Badge>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          ) : null}
        </div>
      </div>

      <Dialog
        open={isActivitiesModalOpen}
        onOpenChange={setIsActivitiesModalOpen}
      >
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              All Database Activities
            </DialogTitle>
          </DialogHeader>

          {isLoadingAllActivities ? (
            <div className="py-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading activities...</p>
            </div>
          ) : (
            <div className="divide-y overflow-y-auto max-h-[60vh] pr-2">
              {(allActivities.length > 0
                ? allActivities
                : recentActivities
              ).map((activity) => (
                <RecentItem key={activity.id} activity={activity} />
              ))}
              {allActivities.length === 0 && recentActivities.length === 0 && (
                <p className="py-4 text-center text-muted-foreground">
                  No activities found.
                </p>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
