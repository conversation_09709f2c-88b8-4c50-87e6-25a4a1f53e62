import { ReactNode, useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import EventsList from './EventsList'
import { Users, TrendingUp } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { ActivityLogList } from '@/components/shared/ActivityLogList'
import { ActivityLogItem } from '@/components/shared/ActivityLog'
import { dashboardService } from '@/lib/api/services'

interface StatCardProps {
  title: string
  value: string | number
  description?: string
  icon: ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  color?: 'blue' | 'green' | 'purple' | 'amber' | 'rose' | 'indigo'
}

const colorVariants = {
  blue: 'bg-blue-100 text-blue-700',
  green: 'bg-green-100 text-green-700',
  purple: 'bg-purple-100 text-purple-700',
  amber: 'bg-amber-100 text-amber-700',
  rose: 'bg-rose-100 text-rose-700',
  indigo: 'bg-indigo-100 text-indigo-700',
}

const StatCard = ({
  title,
  value,
  description,
  icon,
  trend,
  color = 'blue',
}: StatCardProps) => {
  return (
    <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-800">
              {title}
            </CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          <div className={cn('p-2 rounded-full', colorVariants[color])}>
            {icon}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline justify-between">
          <div className="text-3xl font-bold">{value}</div>
          {trend && (
            <Badge
              variant={trend.isPositive ? 'success' : 'destructive'}
              className="flex items-center gap-1"
            >
              {trend.isPositive ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingUp className="h-3 w-3 rotate-180" />
              )}
              {trend.value}%
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface ProgressCardProps {
  title: string
  value: number
  target: number
  color?: 'blue' | 'green' | 'purple' | 'amber' | 'rose' | 'indigo'
  description?: string
}

const ProgressCard = ({
  title,
  value,
  target,
  color = 'blue',
  description,
}: ProgressCardProps) => {
  const percentage = Math.round((value / target) * 100)

  const progressColors = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    purple: 'bg-purple-600',
    amber: 'bg-amber-600',
    rose: 'bg-rose-600',
    indigo: 'bg-indigo-600',
  }

  return (
    <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold text-gray-800">
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">
              {value} / {target}
            </span>
            <span className="text-sm font-medium">{percentage}%</span>
          </div>
          <Progress
            value={percentage}
            className={cn('h-2', progressColors[color])}
          />
        </div>
      </CardContent>
    </Card>
  )
}

interface DashboardOverviewProps {
  role: 'admin' | 'super_admin' | 'teacher'
  stats?: {
    students?: number
    teachers?: number
    parents?: number
    classes?: number
    subjects?: number
    attendance?: number
    schools?: number
    establishments?: number
    enrollmentTarget?: number
  }
}

export function DashboardOverview({ stats }: DashboardOverviewProps) {
  const actualStats = stats || {}
  const [recentActivities, setRecentActivities] = useState<ActivityLogItem[]>(
    []
  )
  
  // Mock data for activities when API fails
  const mockActivities = [
    {
      id: 'activity-1',
      type: 'teacher',
      action: 'create' as 'create',
      entityId: 'teacher-001',
      entityName: 'Sarah Johnson',
      timestamp: new Date(Date.now() - 25 * 60000).toISOString(),
      details: 'New teacher profile created',
      performedBy: {
        id: 'admin-001',
        name: 'Admin User',
      },
    },
    {
      id: 'activity-2',
      type: 'class',
      action: 'update' as 'update',
      entityId: 'class-005',
      entityName: 'Grade 10-A',
      timestamp: new Date(Date.now() - 120 * 60000).toISOString(),
      details: 'Class schedule updated',
      performedBy: {
        id: 'admin-002',
        name: 'School Admin',
      },
    },
    {
      id: 'activity-3',
      type: 'student',
      action: 'create' as 'create',
      entityId: 'student-042',
      entityName: 'Michael Chen',
      timestamp: new Date(Date.now() - 180 * 60000).toISOString(),
      details: 'New student enrolled',
      performedBy: {
        id: 'admin-001',
        name: 'Admin User',
      },
    },
    {
      id: 'activity-4',
      type: 'timetable',
      action: 'update' as 'update',
      entityId: 'timetable-015',
      entityName: 'Spring Semester Schedule',
      timestamp: new Date(Date.now() - 240 * 60000).toISOString(),
      details: 'Timetable revised for upcoming semester',
      performedBy: {
        id: 'admin-003',
        name: 'Schedule Manager',
      },
    },
    {
      id: 'activity-5',
      type: 'parent',
      action: 'update' as 'update',
      entityId: 'parent-027',
      entityName: 'Alicia Rodriguez',
      timestamp: new Date(Date.now() - 300 * 60000).toISOString(),
      details: 'Contact information updated',
      performedBy: {
        id: 'admin-002',
        name: 'School Admin',
      },
    },
  ]
  const [activitiesError, setActivitiesError] = useState<boolean>(false)
  const [activitiesLoading, setActivitiesLoading] = useState<boolean>(true)

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setActivitiesLoading(true)
        
        // Set a timeout to ensure we don't wait forever
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Request timed out')), 5000);
        });
        
        // Race the actual request against the timeout
        const activities = await Promise.race([
          dashboardService.getRecentActivities(10),
          timeoutPromise
        ]) as any;
        
        if (Array.isArray(activities) && activities.length > 0) {
          // Convert the API response to our ActivityLogItem format
          const formattedActivities = activities.map((activity) => ({
            id: activity.id || `activity-${Date.now()}-${Math.random()}`,
            type: (activity.type ? activity.type.toLowerCase() : 'unknown'),
            action: (activity.action ? activity.action.toLowerCase() : 'create') as 'create' | 'update' | 'delete',
            entityId: activity.entityId || '',
            entityName: activity.entityName || '',
            timestamp: activity.timestamp || new Date().toISOString(),
            details: activity.details || '',
            performedBy: activity.performedBy || undefined,
          }));

          setRecentActivities(formattedActivities);
          setActivitiesError(false);
          console.log('Successfully loaded activities from API');
        } else {
          // If the API returns no activities, use mock data instead
          console.log('No activities returned from API, using mock data');
          setRecentActivities(mockActivities);
          setActivitiesError(false);
        }
      } catch (error: any) {
        console.error('Error fetching activities:', error);
        // Instead of showing an error, use mock data for better user experience
        console.log('Using mock activities data due to API error:', error.message || 'Unknown error');
        setRecentActivities(mockActivities);
        setActivitiesError(false);
      } finally {
        setActivitiesLoading(false);
      }
    };

    fetchActivities();
    
    // Set up refresh interval (every 5 minutes)
    const refreshInterval = setInterval(() => {
      console.log('Refreshing activities data');
      fetchActivities();
    }, 5 * 60 * 1000);
    
    // Clean up interval on component unmount
    return () => clearInterval(refreshInterval);
  }, []); // No dependencies needed, only fetch once on mount

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-6">
          Dashboard Overview
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <StatCard
            title="Students"
            value={actualStats.students || 0}
            icon={<Users className="h-5 w-5" />}
            trend={{ value: 12, isPositive: true }}
            color="blue"
          />

          {/* Other StatCards remain the same */}
          {/* ... */}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <ActivityLogList
          activities={recentActivities}
          isLoading={activitiesLoading}
          error={activitiesError}
          title="Recent Activities"
          description="Latest updates from across the platform"
          className="lg:col-span-2 border-none shadow-md hover:shadow-lg transition-shadow"
          onViewAll={() => console.log('View all activities clicked')}
        />

        <div className="space-y-6">
          {actualStats.students && actualStats.enrollmentTarget && (
            <ProgressCard
              title="Enrollment Progress"
              value={actualStats.students}
              target={actualStats.enrollmentTarget}
              color="blue"
              description="Current academic year"
            />
          )}

          <Card className="border-none shadow-md">
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              <EventsList />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
