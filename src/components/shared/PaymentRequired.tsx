interface PaymentRequiredProps {
  studentName?: string
}

export default function PaymentRequired({ studentName }: PaymentRequiredProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
      <div className="bg-primary/10 border-l-4 border-secondary text-primary p-6 rounded shadow-md max-w-lg text-center">
        <h2 className="text-2xl font-bold mb-4">Payment Required</h2>
        <p className="mb-4">
          {studentName
            ? `Hello ${studentName}, your school fees have not been paid.`
            : 'Your school fees have not been paid.'}
        </p>
        <p className="mb-6">
          Please contact an administrator to complete your payment and regain
          access to this page.
        </p>
      </div>
    </div>
  )
}
