// components/shared/ActivityItem.tsx
import {
  Clock,
  CheckCircle2,
  AlertCircle,
  FileEdit,
  Trash2,
  Plus,
} from 'lucide-react'
import { cn } from '@/lib/utils'

export type ActivityStatus = 'pending' | 'completed' | 'warning'
export type ActivityAction = 'create' | 'update' | 'delete'

export interface ActivityItemProps {
  title: string
  time: string
  status?: ActivityStatus
  action?: ActivityAction
  description?: string
  className?: string
}

const statusIcons = {
  pending: <Clock className="h-5 w-5 text-amber-500" />,
  completed: <CheckCircle2 className="h-5 w-5 text-green-500" />,
  warning: <AlertCircle className="h-5 w-5 text-rose-500" />,
}

const actionIcons = {
  create: <Plus className="h-5 w-5 text-green-500" />,
  update: <FileEdit className="h-5 w-5 text-blue-500" />,
  delete: <Trash2 className="h-5 w-5 text-red-500" />,
}

export const ActivityItem = ({
  title,
  time,
  status = 'pending',
  action,
  description,
  className,
}: ActivityItemProps) => {
  // Determine which icon to use - prefer action icon if available, otherwise use status icon
  const icon = action ? actionIcons[action] : statusIcons[status]

  // Get background color class based on action
  const getBgColorClass = () => {
    if (!action) return ''

    switch (action) {
      case 'create':
        return 'bg-green-50'
      case 'update':
        return 'bg-blue-50'
      case 'delete':
        return 'bg-red-50'
      default:
        return ''
    }
  }

  return (
    <div
      className={cn(
        'flex items-start space-x-3 py-3',
        getBgColorClass(),
        className
      )}
    >
      <div className="flex-shrink-0 mt-1">{icon}</div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900">{title}</p>
        {description && <p className="text-sm text-gray-500">{description}</p>}
      </div>
      <div className="flex-shrink-0 flex items-center">
        <Clock className="mr-1 h-3 w-3 text-gray-500" />
        <p className="text-xs text-gray-500">{time}</p>
      </div>
    </div>
  )
}
