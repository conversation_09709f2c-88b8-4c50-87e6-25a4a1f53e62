import React from 'react'
import { UserProfile } from '@/lib/user/profile'
import { CardContent } from '@/components/ui/card'
import { Download } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { jsPDF } from 'jspdf'

interface ProfileCardProps {
  userProfile: UserProfile
  className?: string
}

const ProfileCard: React.FC<ProfileCardProps> = ({
  userProfile,
  className,
}) => {
  // Get API URL from environment variables with fallback
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'
  // Default theme colors - blue theme (matching the screenshot)
  const theme = {
    primaryColor: '#4338ca', // Darker indigo color from the screenshot
    secondaryColor: '#ffffff', // White background for the avatar area
    textColor: '#ffffff',
    accentColor: '#ffeb3b',
  }

  // Generate initials for avatar placeholder
  const getInitials = () => {
    // If we have a fullname with a space, use first letter of first and last name
    if (userProfile.fullname && userProfile.fullname.includes(' ')) {
      const nameParts = userProfile.fullname.split(' ')
      const firstInitial = nameParts[0].charAt(0).toUpperCase()
      const lastInitial = nameParts[nameParts.length - 1]
        .charAt(0)
        .toUpperCase()
      return `${firstInitial}${lastInitial}`
    }

    // If we have firstname and lastname, use those
    if (userProfile.firstname && userProfile.lastname) {
      return `${userProfile.firstname.charAt(0).toUpperCase()}${userProfile.lastname.charAt(0).toUpperCase()}`
    }

    // If we only have firstname
    if (userProfile.firstname) {
      // If firstname has multiple characters, use first two
      if (userProfile.firstname.length > 1) {
        return userProfile.firstname.substring(0, 2).toUpperCase()
      }
      return `${userProfile.firstname.charAt(0).toUpperCase()}U`
    }

    // If we only have fullname but no space
    if (userProfile.fullname) {
      // If fullname has multiple characters, use first two
      if (userProfile.fullname.length > 1) {
        return userProfile.fullname.substring(0, 2).toUpperCase()
      }
      return `${userProfile.fullname.charAt(0).toUpperCase()}U`
    }

    // Default fallback
    return 'JS'
  }

  // Function to get establishment initials
  const getEstablishmentInitials = (name: string): string => {
    if (!name) return 'JS'

    // Split by spaces and get first letter of each word
    const words = name.split(/\s+/)
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase()
    }

    // If only one word, return first two letters
    if (name.length >= 2) {
      return name.substring(0, 2).toUpperCase()
    }

    // Fallback
    return name[0].toUpperCase() + 'S'
  }

  // Function to load image as base64
  const loadImageAsBase64 = async (url: string): Promise<string | null> => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result as string)
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.error('Error loading image:', error)
      return null
    }
  }

  // Function to export card as PDF using jsPDF directly
  const exportToPDF = async () => {
    // Show loading state
    const downloadButton = document.getElementById('download-button')
    if (downloadButton) {
      downloadButton.textContent = 'Preparing PDF...'
      downloadButton.setAttribute('disabled', 'true')
    }

    try {
      // Get establishment info from user profile
      const establishment = userProfile.etablissement || {
        name: userProfile.etablissementName || 'JeridSchool',
        logo: userProfile.etablissementLogo || null,
      }

      // Load images as base64
      const [avatarImage, qrCodeImage, establishmentLogoImage] =
        await Promise.all([
          userProfile.avatar
            ? loadImageAsBase64(
                `${API_URL}/health/proxy/image?url=${encodeURIComponent(userProfile.avatar)}`
              )
            : null,
          loadImageAsBase64(
            `${API_URL}/health/qr-code?data=${encodeURIComponent(`https://jeridschool.tech/public/card/${userProfile.id}`)}`
          ),
          establishment.logo
            ? loadImageAsBase64(
                `${API_URL}/health/proxy/image?url=${encodeURIComponent(establishment.logo)}`
              )
            : null,
        ])

      // Create a PDF document
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a6',
      })

      // Set background color for the header
      pdf.setFillColor(94, 84, 227)
      pdf.rect(0, 0, 105, 30, 'F')

      // Add establishment logo
      if (establishmentLogoImage) {
        // Draw the black circle background
        pdf.setFillColor(0, 0, 0) // Black
        pdf.circle(15, 15, 6, 'F')

        // Draw the white circle for the logo background
        pdf.setFillColor(255, 255, 255) // White
        pdf.circle(15, 15, 4.5, 'F')

        // Add the logo image
        pdf.addImage(
          establishmentLogoImage,
          'JPEG',
          10.5,
          10.5,
          9,
          9,
          undefined,
          'FAST'
        )
      } else {
        // Draw the black circle background
        pdf.setFillColor(0, 0, 0) // Black
        pdf.circle(15, 15, 6, 'F')

        // Draw the white circle for initials
        pdf.setFillColor(255, 255, 255) // White
        pdf.circle(15, 15, 4.5, 'F')

        // Add establishment initials
        pdf.setTextColor(0, 0, 0)
        pdf.setFontSize(8)
        pdf.setFont('helvetica', 'bold')
        const establishmentInitials = getEstablishmentInitials(
          establishment.name
        )
        pdf.text(
          establishmentInitials,
          15 - establishmentInitials.length * 1.5,
          17
        )
      }

      // Add establishment name and OFFICIAL text
      pdf.setTextColor(255, 255, 255)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'bold')
      pdf.text(establishment.name.substring(0, 20), 25, 14)
      pdf.setFontSize(6)
      pdf.text('OFFICIAL', 25, 18)

      // Add QR code
      if (qrCodeImage) {
        pdf.addImage(qrCodeImage, 'JPEG', 80, 7.5, 15, 15)
      } else {
        pdf.setFillColor(255, 255, 255)
        pdf.rect(80, 7.5, 15, 15, 'F')
      }

      // Add avatar
      if (avatarImage) {
        pdf.addImage(avatarImage, 'JPEG', 37.5, 25, 30, 30, undefined, 'FAST')
      } else {
        pdf.setFillColor(230, 230, 235)
        pdf.circle(52.5, 40, 15, 'F')
        pdf.setTextColor(75, 85, 99)
        pdf.setFontSize(16)
        pdf.setFont('helvetica', 'bold')
        pdf.text(getInitials(), 52.5 - getInitials().length * 3, 45)
      }

      // Add badge
      pdf.setFillColor(249, 250, 251)
      pdf.roundedRect(42, 65, 20, 8, 4, 4, 'F')
      pdf.setTextColor(94, 84, 227)
      pdf.setFontSize(8)
      pdf.text(establishment.name.substring(0, 2).toUpperCase(), 52, 70, {
        align: 'center',
      })

      // Add name and role
      const fullName =
        userProfile.fullname ||
        `${userProfile.firstname || ''} ${userProfile.lastname || ''}`
      pdf.setTextColor(0, 0, 0)
      pdf.setFontSize(12)
      pdf.setFont('helvetica', 'bold')
      pdf.text(fullName, 52.5, 80, { align: 'center', maxWidth: 90 })
      pdf.setTextColor(107, 114, 128)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'normal')
      pdf.text(userProfile.role || '—', 52.5, 85, { align: 'center' })

      // Add details
      pdf.setDrawColor(229, 231, 235)

      const startY = 95
      const lineHeight = 7
      const labelX = 20
      const valueX = 85

      // Email
      pdf.setTextColor(107, 114, 128)
      pdf.setFontSize(8)
      pdf.text('Email:', labelX, startY)
      pdf.setTextColor(55, 65, 81)
      pdf.text(userProfile.email || '—', valueX, startY, { align: 'right' })

      // CIN
      pdf.setTextColor(107, 114, 128)
      pdf.text('CIN:', labelX, startY + lineHeight)
      pdf.setTextColor(55, 65, 81)
      pdf.text(userProfile.cin || '—', valueX, startY + lineHeight, {
        align: 'right',
      })

      // Birthday
      pdf.setTextColor(107, 114, 128)
      pdf.text('Birthday:', labelX, startY + lineHeight * 2)
      pdf.setTextColor(55, 65, 81)
      pdf.text(userProfile.birthday || '—', valueX, startY + lineHeight * 2, {
        align: 'right',
      })

      // Gender
      pdf.setTextColor(107, 114, 128)
      pdf.text('Gender:', labelX, startY + lineHeight * 3)
      pdf.setTextColor(55, 65, 81)
      pdf.text(userProfile.gender || '—', valueX, startY + lineHeight * 3, {
        align: 'right',
      })

      // Phone
      pdf.setTextColor(107, 114, 128)
      pdf.text('Phone:', labelX, startY + lineHeight * 4)
      pdf.setTextColor(55, 65, 81)
      pdf.text(userProfile.phone || '—', valueX, startY + lineHeight * 4, {
        align: 'right',
      })

      // Address (truncated if too long)
      pdf.setTextColor(107, 114, 128)
      pdf.text('Address:', labelX, startY + lineHeight * 5)
      pdf.setTextColor(55, 65, 81)
      const address = userProfile.address || '—';
      const truncatedAddress = address.length > 30 ? address.substring(0, 27) + '...' : address;
      pdf.text(truncatedAddress, valueX, startY + lineHeight * 5, {
        align: 'right',
      })

      // Valid Until
      pdf.setTextColor(107, 114, 128)
      pdf.text('Valid Until:', labelX, startY + lineHeight * 6)
      pdf.setTextColor(55, 65, 81)
      pdf.text(
        userProfile.validUntil
          ? new Date(userProfile.validUntil).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: '2-digit',
            })
          : 'Apr 20, 2026',
        valueX,
        startY + lineHeight * 6,
        { align: 'right' }
      )

      // Add footer
      pdf.line(20, startY + lineHeight * 7, 85, startY + lineHeight * 7)
      pdf.setTextColor(107, 114, 128)
      pdf.setFontSize(6)
      pdf.text(
        `This card is the property of ${establishment.name}`,
        52.5,
        startY + lineHeight * 7.5,
        { align: 'center' }
      )
      pdf.setFontSize(5)
      pdf.text(
        'If found, please return to the administration office',
        52.5,
        startY + lineHeight * 8,
        { align: 'center' }
      )

      // Add signature line
      pdf.setLineDashPattern([1, 1], 0)
      pdf.line(40, startY + lineHeight * 9, 65, startY + lineHeight * 9)
      pdf.setFontSize(5)
      pdf.text('Signature', 52.5, startY + lineHeight * 9.5, {
        align: 'center',
      })

      // Save the PDF with user's name
      const fileName = `${fullName.replace(/\s+/g, '_')}_ID_Card.pdf`
      pdf.save(fileName)

      console.log('PDF generated successfully')
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF. Please try again.')
    } finally {
      // Reset button state
      if (downloadButton) {
        downloadButton.textContent = 'Download ID Card'
        downloadButton.removeAttribute('disabled')
      }
    }
  }

  // Get establishment info from user profile
  const establishment = userProfile.etablissement || {
    name: userProfile.etablissementName || 'JeridSchool',
    logo: userProfile.etablissementLogo || null,
  }

  return (
    <div className="flex flex-col items-center">
      <div
        id="profile-card-to-export"
        className={`w-[320px] bg-white rounded-lg overflow-hidden shadow-lg ${className}`}
        style={{ fontFamily: 'Inter, sans-serif' }}
      >
        {/* Header */}
        <div
          className="w-full p-4 flex items-center justify-between"
          style={{
            backgroundColor: theme.primaryColor,
            color: theme.textColor,
          }}
        >
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-black flex items-center justify-center logo-container">
              <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                {establishment.logo ? (
                  <img
                    src={`${API_URL}/health/proxy/image?url=${encodeURIComponent(establishment.logo)}`}
                    alt="Establishment Logo"
                    className="w-full h-full object-cover rounded-full"
                    onError={(e) => {
                      console.error('Failed to load establishment logo')
                      e.currentTarget.onerror = null
                      e.currentTarget.style.display = 'none'
                      // Fall back to initials
                      e.currentTarget.parentElement!.innerHTML = `
                        <span class="text-xs font-bold text-black">
                          ${establishment.name ? establishment.name.substring(0, 2).toUpperCase() : 'JS'}
                        </span>
                      `
                    }}
                  />
                ) : (
                  <span className="text-xs font-bold text-black">
                    {establishment.name
                      ? establishment.name.substring(0, 2).toUpperCase()
                      : 'JS'}
                  </span>
                )}
              </div>
            </div>
            <div>
              <div className="font-bold uppercase text-xs">
                {establishment.name}
              </div>
              <div className="text-[10px] uppercase">OFFICIAL</div>
            </div>
          </div>

          {/* QR Code */}
          <div className="bg-white p-1 rounded-md">
            <img
              src={`${API_URL}/health/qr-code?data=${encodeURIComponent(`https://jeridschool.tech/public/card/${userProfile.id}`)}`}
              alt="QR Code"
              width={50}
              height={50}
              className="qr-code"
              onError={(e) => {
                console.error('Failed to load QR code')
                e.currentTarget.onerror = null
                e.currentTarget.style.display = 'none'
                e.currentTarget.parentElement?.classList.add('border')
                e.currentTarget.parentElement?.classList.add('border-gray-200')
              }}
            />
          </div>
        </div>

        {/* Avatar */}
        <div className="flex justify-center -mt-6 avatar-container">
          <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-white bg-gray-200">
            {userProfile.avatar ? (
              <img
                src={`${API_URL}/health/proxy/image?url=${encodeURIComponent(userProfile.avatar)}`}
                alt={userProfile.fullname || 'User Avatar'}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Failed to load avatar image')
                  e.currentTarget.onerror = null
                  // Fall back to initials
                  e.currentTarget.style.display = 'none'
                  e.currentTarget.parentElement!.innerHTML = `
                    <div class="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600 text-xl font-bold">
                      ${getInitials()}
                    </div>
                  `
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600 text-xl font-bold">
                {getInitials()}
              </div>
            )}
          </div>
        </div>

        {/* User Info */}
        <CardContent className="pt-8 pb-4 relative">
          <div className="text-center mb-6">
            <div className="mb-1 text-[#6366f1] text-xs font-semibold uppercase tracking-wider bg-gray-100 py-1 px-4 rounded-full">
              {establishment.name}
            </div>
            <h2 className="text-lg font-bold mt-2">
              {userProfile.fullname ||
                `${userProfile.firstname || ''} ${userProfile.lastname || ''}`}
            </h2>
            <p className="text-xs text-gray-500 capitalize">
              {userProfile.role}
            </p>
          </div>

          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Email:</span>
              <span className="text-gray-700 text-xs">{userProfile.email}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">CIN:</span>
              <span className="text-gray-700 text-xs">
                {userProfile.cin || '—'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Birthday:</span>
              <span className="text-gray-700 text-xs">
                {userProfile.birthday || '—'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Gender:</span>
              <span className="text-gray-700 text-xs">
                {userProfile.gender || '—'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Phone:</span>
              <span className="text-gray-700 text-xs">
                {userProfile.phone || '—'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Address:</span>
              <span className="text-gray-700 text-xs text-right max-w-[180px]">
                {userProfile.address || '—'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Valid Until:</span>
              <span className="text-gray-700 text-xs">
                {userProfile.validUntil
                  ? new Date(userProfile.validUntil).toLocaleDateString(
                      'en-US',
                      {
                        year: 'numeric',
                        month: 'short',
                        day: '2-digit',
                      }
                    )
                  : 'Apr 20, 2026'}
              </span>
            </div>
          </div>

          {/* Card Footer */}
          <div className="mt-4 pt-3 border-t text-center text-[10px] text-gray-500">
            <p>This card is the property of {establishment.name}</p>
            <p className="text-[9px]">
              If found, please return to the administration office
            </p>

            {/* Signature line */}
            <div className="mt-2 flex items-center justify-center">
              <div className="w-24 border-t border-dashed border-gray-400 mx-2"></div>
              <span className="text-[9px] text-gray-500">Signature</span>
            </div>
          </div>
        </CardContent>
      </div>

      {/* Download Button */}
      <Button
        id="download-button"
        onClick={exportToPDF}
        className="mt-4 flex items-center gap-2 text-xs"
        variant="outline"
        size="sm"
      >
        <Download className="h-3 w-3 mr-1" />
        Download ID Card
      </Button>
    </div>
  )
}

export default ProfileCard
