import { useState } from 'react'
import { Button } from '../ui/button'
import { Schedule, Course } from '@/interface/types'

interface ScheduleViewProps {
  userRole: 'student' | 'teacher' | 'parent' | 'admin'
  initialSchedule: Schedule
  onScheduleUpdate?: (schedule: Schedule) => void
}

export default function ScheduleView({
  userRole,
  initialSchedule,
  onScheduleUpdate,
}: ScheduleViewProps) {
  const [schedule, setSchedule] = useState<Schedule>(initialSchedule)

  const handleScheduleChange = (newSchedule: Schedule) => {
    setSchedule(newSchedule)
    onScheduleUpdate?.(newSchedule)
  }

  return (
    <div className="p-4">
      <div className="grid grid-cols-6 gap-4">
        <div className="col-span-1"></div>
        {Object.keys(schedule).map((day) => (
          <div key={day} className="col-span-1 text-center font-bold">
            {day}
          </div>
        ))}
      </div>
      <div className="mt-4">
        {schedule &&
          Object.entries(schedule).map(([day, courses]) => (
            <div key={day} className="grid grid-cols-6 gap-4 mb-2">
              {courses.map((course: Course, index: number) => (
                <div key={index} className="col-span-1 p-2 border rounded">
                  <p>{course.subject}</p>
                  <p>{course.teacher}</p>
                  <p>{course.time}</p>
                </div>
              ))}
            </div>
          ))}
      </div>
      {userRole === 'admin' && (
        <Button onClick={() => handleScheduleChange(schedule)}>
          Modify Schedule
        </Button>
      )}
    </div>
  )
}
