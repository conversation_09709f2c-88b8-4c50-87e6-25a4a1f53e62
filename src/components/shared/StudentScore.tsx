import { useState } from 'react'
import { Student, Score } from '@/interface/types'
import { Table } from '../ui/table'
import { Button } from '../ui/button'

interface StudentScoreProps {
  userRole: 'student' | 'teacher' | 'parent' | 'admin'
  student?: Student
  onScoreUpdate?: (studentId: string | number, newScore: Score) => void
}

export default function StudentScore({
  userRole,
  student,
  onScoreUpdate,
}: StudentScoreProps) {
  const [scores] = useState<Score[]>(student?.scores || [])

  const canEdit = userRole === 'teacher' || userRole === 'admin'

  return (
    <div className="p-4">
      <Table>
        <thead>
          <tr>
            <th>Subject</th>
            <th>Score</th>
            <th>Total</th>
            <th>Date</th>
            {canEdit && <th>Actions</th>}
          </tr>
        </thead>
        <tbody>
          {scores.map((score, index) => (
            <tr key={index}>
              <td>{score.subject}</td>
              <td>{score.score}</td>
              <td>{score.totalScore}</td>
              <td>{score.date}</td>
              {canEdit && (
                <td>
                  <Button
                    variant="ghost"
                    onClick={() => onScoreUpdate?.(student?.id || 0, score)}
                  >
                    Edit
                  </Button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  )
}
