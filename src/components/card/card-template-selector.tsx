"use client"

import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"

// Template definitions
export interface CardTemplate {
  id: string
  name: string
  description: string
  style: "modern" | "classic" | "professional" | "creative"
  orientation: "horizontal" | "vertical"
  features: string[]
  preview: string
  config: {
    html: string
  }
}

export const CARD_TEMPLATES: CardTemplate[] = [
  {
    id: "modern-vertical",
    name: "Modern Vertical Premium",
    description: "Stunning vertical design with glassmorphism effects and premium gradients",
    style: "modern",
    orientation: "vertical",
    features: ["QR Code", "Glassmorphism", "Gradient Background", "Verification Badge", "Premium Design"],
    preview: "/placeholder.svg?height=280&width=420",
    config: {
      html: `
        <div style="width: 280px; height: 420px; background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%); border-radius: 16px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25); overflow: hidden; position: relative; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
          
          <!-- Background Pattern -->
          <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1;">
            <div style="position: absolute; top: 40px; right: 40px; width: 128px; height: 128px; border-radius: 50%; background: rgba(255, 255, 255, 0.1);"></div>
            <div style="position: absolute; bottom: 80px; left: 32px; width: 96px; height: 96px; border-radius: 50%; background: rgba(255, 255, 255, 0.1);"></div>
          </div>

          <!-- Header with Logo and QR Code -->
          <div style="position: relative; z-index: 10; padding: 20px; display: flex; justify-content: space-between; align-items: flex-start;">
            <!-- Logo and Establishment Name -->
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 40px; height: 40px; border-radius: 12px; background: linear-gradient(135deg, #60a5fa, #a855f7); padding: 2px;">
                <div style="width: 100%; height: 100%; border-radius: 10px; background: white; display: flex; align-items: center; justify-content: center;">
                  <img src="{establishmentLogo}" alt="Logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 10px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                  <div style="color: #7c3aed; font-weight: bold; font-size: 12px; display: none;">{establishmentName ? establishmentName.substring(0, 2).toUpperCase() : 'UN'}</div>
                </div>
              </div>
              <div>
                <h2 style="color: white; font-weight: bold; font-size: 14px; margin: 0; line-height: 1.2;">{establishmentName}</h2>
                <p style="color: #c4b5fd; font-size: 10px; font-weight: 500; letter-spacing: 1px; margin: 0;">STUDENT ID</p>
              </div>
            </div>
            
            <!-- QR Code -->
            <div style="background: white; border-radius: 6px; padding: 6px;">
              <img src="https://api.qrserver.com/v1/create-qr-code/?size=40x40&data=https://www.jeridschool.tech/public/card/{user.id}" alt="QR Code" style="width: 40px; height: 40px; display: block;" />
            </div>
          </div>

          <!-- Profile Section -->
          <div style="position: relative; z-index: 10; padding: 0 24px; text-align: center;">
            <div style="width: 96px; height: 96px; margin: 0 auto 16px; border-radius: 50%; background: linear-gradient(135deg, #60a5fa, #a855f7); padding: 4px;">
              <div style="width: 100%; height: 100%; border-radius: 50%; overflow: hidden; background: white;">
                <img src="{userAvatar}" alt="{user.firstname} {user.lastname}" style="width: 100%; height: 100%; object-fit: cover;" />
              </div>
            </div>
            <h3 style="color: white; font-weight: bold; font-size: 20px; margin: 0 0 4px 0;">{user.firstname} {user.lastname}</h3>
            <p style="color: #c4b5fd; font-size: 14px; margin: 0 0 8px 0;">{user.role}</p>
            <p style="color: #c4b5fd; font-size: 12px; margin: 0 0 4px 0;">{user.email}</p>
            <p style="color: #c4b5fd; font-size: 12px; margin: 0 0 4px 0;">{user.address}</p>
            <p style="color: #c4b5fd; font-size: 12px; margin: 0 0 8px 0;">{user.phone}</p>
            
          </div>

          

          <!-- Bottom Accent -->
          <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #60a5fa, #a855f7, #ec4899);"></div>
        </div>
      `,
    },
  },
  {
    id: "creative-gradient",
    name: "Creative Gradient",
    description: "Bold and creative design with vibrant gradients and modern elements",
    style: "creative",
    orientation: "vertical",
    features: ["Vibrant Colors", "Creative Layout", "Modern Typography", "Unique Design", "Eye-catching"],
    preview: "/placeholder.svg?height=300&width=280",
    config: {
      html: `
        <div style="width: 280px; height: 380px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; box-shadow: 0 30px 60px rgba(102, 126, 234, 0.4); overflow: hidden; position: relative; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
          
          <!-- Animated Background Elements -->
          <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; border-radius: 50%; background: rgba(255, 255, 255, 0.1);"></div>
          <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; border-radius: 50%; background: rgba(255, 255, 255, 0.1);"></div>

          <!-- Header with Logo and QR Code -->
          <div style="padding: 20px; display: flex; justify-content: space-between; align-items: flex-start; position: relative; z-index: 10;">
            <!-- Logo and Establishment Name -->
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px); border: 2px solid rgba(255, 255, 255, 0.3);">
                <img src="{establishmentLogo}" alt="Logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 10px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                <div style="color: white; font-weight: bold; font-size: 12px; display: none;">{establishmentName ? establishmentName.substring(0, 2).toUpperCase() : 'UN'}</div>
              </div>
              <div>
                <h2 style="color: white; font-weight: 800; font-size: 14px; margin: 0 0 2px 0; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{establishmentName}</h2>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 10px; font-weight: 600; letter-spacing: 2px; margin: 0;">CREATIVE ID</p>
              </div>
            </div>
            
            <!-- QR Code -->
            <div style="background: rgba(255, 255, 255, 0.9); border-radius: 6px; padding: 4px;">
              <img src="https://api.qrserver.com/v1/create-qr-code/?size=35x35&data=https://www.jeridschool.tech/public/card/{user.id}" alt="QR Code" style="width: 35px; height: 35px; display: block;" />
            </div>
          </div>

          <!-- Profile -->
          <div style="padding: 0 20px 20px; text-align: center; position: relative; z-index: 10;">
            <div style="width: 80px; height: 80px; margin: 0 auto 12px; border-radius: 50%; background: linear-gradient(45deg, #ff6b6b, #feca57); padding: 3px;">
              <div style="width: 100%; height: 100%; border-radius: 50%; overflow: hidden; background: white;">
                <img src="{userAvatar}" alt="{user.firstname} {user.lastname}" style="width: 100%; height: 100%; object-fit: cover;" />
              </div>
            </div>
            <h3 style="color: white; font-weight: 700; font-size: 18px; margin: 0 0 4px 0; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{user.firstname} {user.lastname}</h3>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 13px; margin: 0 0 4px 0;">{user.role}</p>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 11px; margin: 0 0 4px 0;">{user.email}</p>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 11px; margin: 0 0 4px 0;">{user.address}</p>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 11px; margin: 0 0 12px 0;">{user.phone}</p>

          </div>

        </div>
      `,
    },
  },
]

interface CardTheme {
  primaryColor: string
  secondaryColor: string
  textColor: string
}

interface CardTemplateSelectorProps {
  selectedTemplate: CardTemplate
  onTemplateChange: (template: CardTemplate) => void
  theme: CardTheme
}

const CardTemplateSelector: React.FC<CardTemplateSelectorProps> = ({ selectedTemplate, onTemplateChange, theme }) => {
  const handleTemplateSelect = (templateId: string) => {
    const template = CARD_TEMPLATES.find((t) => t.id === templateId)
    if (template) {
      onTemplateChange(template)
    }
  }
  console.log(theme)
  const getStyleColor = (style: string) => {
    switch (style) {
      case "modern":
        return "bg-blue-100 text-blue-800"
      case "classic":
        return "bg-green-100 text-green-800"
      case "professional":
        return "bg-purple-100 text-purple-800"
      case "creative":
        return "bg-orange-100 text-orange-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Premium Card Templates</CardTitle>
        <CardDescription>Choose from our collection of professionally designed ID card templates</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="max-h-[500px] overflow-y-auto pr-2">
          <RadioGroup
            value={selectedTemplate.id}
            onValueChange={handleTemplateSelect}
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {CARD_TEMPLATES.map((template) => (
              <div key={template.id} className="relative">
                <RadioGroupItem value={template.id} id={template.id} className="sr-only" />
                <Label htmlFor={template.id} className="cursor-pointer">
                  <Card
                    className={`overflow-hidden border-2 transition-all hover:shadow-lg ${
                      selectedTemplate.id === template.id
                        ? "border-primary ring-2 ring-primary shadow-xl"
                        : "border-border hover:border-primary/50"
                    }`}
                  >
                    {/* Template Preview */}
                    <div className="relative h-40 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
                      <div
                        className="transform scale-75 origin-center shadow-lg"
                        dangerouslySetInnerHTML={{
                          __html: template.config.html
                            .replace(/{establishmentName}/g, "Sample University")
                            .replace(/{establishmentLogo}/g, "/placeholder.svg?height=40&width=40&text=SU")
                            .replace(/{user\.firstname}/g, "John")
                            .replace(/{user\.lastname}/g, "Doe")
                            .replace(/{user\.role}/g, "Student")
                            .replace(/{user\.id}/g, "STU123456")
                            .replace(/{user\.email}/g, "<EMAIL>")
                            .replace(/{user\.phone}/g, "+****************")
                            .replace(/{user\.address}/g, "123 University Ave")
                            .replace(/{userAvatar}/g, "/placeholder.svg?height=100&width=100&text=JD")
                            .replace(/{qrCodeSize}/g, "40")
                            .replace(
                              /https:\/\/api\.qrserver\.com\/v1\/create-qr-code\/\?size=40x40&data=https:\/\/www\.jeridschool\.tech\/public\/card\/STU123456/g,
                              "/placeholder.svg?height=40&width=40&text=QR",
                            ),
                        }}
                      />

                      {/* Orientation indicator */}
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="text-xs">
                          {template.orientation}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-sm">{template.name}</h3>
                          <Badge className={`text-xs ${getStyleColor(template.style)}`}>{template.style}</Badge>
                        </div>

                        <p className="text-xs text-muted-foreground line-clamp-2">{template.description}</p>

                        <div className="flex flex-wrap gap-1 mt-2">
                          {template.features.slice(0, 3).map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {template.features.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{template.features.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Selected template details */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Selected Template: {selectedTemplate.name}</h4>
          <p className="text-sm text-muted-foreground mb-3">{selectedTemplate.description}</p>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Style:</span>
              <Badge className={getStyleColor(selectedTemplate.style)}>{selectedTemplate.style}</Badge>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Orientation:</span>
              <Badge variant="secondary">{selectedTemplate.orientation}</Badge>
            </div>

            <div className="space-y-1">
              <span className="text-sm font-medium">Features:</span>
              <div className="flex flex-wrap gap-1">
                {selectedTemplate.features.map((feature, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardTemplateSelector
