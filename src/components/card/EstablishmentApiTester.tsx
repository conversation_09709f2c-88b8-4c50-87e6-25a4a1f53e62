import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const EstablishmentApiTester = () => {
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    description: "",
    phone: "",
    email: "",
    config: `<style>
      .custom-card {
        border: 2px dashed #ccc;
        padding: 1rem;
        border-radius: 10px;
        background: #f9f9f9;
      }
    </style>
    <div class='custom-card'>
      <h2>{{name}}</h2>
      <p>{{description}}</p>
      <p>{{address}}</p>
      <p>{{phone}}</p>
      <p>{{email}}</p>
    </div>`
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async () => {
    const response = await fetch("http://localhost:3000", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(formData)
    });
    const data = await response.json();
    console.log("Created establishment:", data);
  };

  const renderTemplate = (template: string) => {
    return template
      .replace(/{{name}}/g, formData.name)
      .replace(/{{address}}/g, formData.address)
      .replace(/{{description}}/g, formData.description)
      .replace(/{{phone}}/g, formData.phone)
      .replace(/{{email}}/g, formData.email);
  };

  return (
    <Card className="w-full max-w-3xl mx-auto mt-8">
      <CardContent className="space-y-4">
        <input
          type="text"
          name="name"
          placeholder="Name"
          value={formData.name}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="address"
          placeholder="Address"
          value={formData.address}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="description"
          placeholder="Description"
          value={formData.description}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="phone"
          placeholder="Phone"
          value={formData.phone}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="email"
          placeholder="Email"
          value={formData.email}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <textarea
          name="config"
          placeholder="HTML/CSS Template"
          value={formData.config}
          onChange={handleChange}
          className="w-full h-40 p-2 border rounded font-mono"
        />
        <Button onClick={handleSubmit}>Create Establishment</Button>

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Live Preview</h3>
          <div
            className="border rounded p-4"
            dangerouslySetInnerHTML={{ __html: renderTemplate(formData.config) }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default EstablishmentApiTester;
