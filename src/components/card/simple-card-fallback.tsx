"use client"

import type React from "react"
import type { User } from "@/types/user"
import type { CardTheme } from "./CardThemeSelector"

interface SimpleCardFallbackProps {
  user: User
  establishmentName: string
  establishmentLogo?: string
  validUntil?: string
  theme: CardTheme
  profileUrl?: string
}

const SimpleCardFallback: React.FC<SimpleCardFallbackProps> = ({
  user,
  establishmentName,
  // establishmentLogo,
  validUntil = "DEC 2024",
  theme,
}) => {
  const getInitials = () => {
    const firstInitial = user.firstname ? user.firstname.charAt(0).toUpperCase() : "S"
    const lastInitial = user.lastname ? user.lastname.charAt(0).toUpperCase() : "S"
    return `${firstInitial}${lastInitial}`
  }

  return (
    <div className="w-[350px] h-[220px] bg-white rounded-lg overflow-hidden shadow-lg border">
      {/* Header */}
      <div
        className="w-full p-4 flex items-center justify-between"
        style={{
          backgroundColor: theme.primaryColor,
          color: theme.textColor,
        }}
      >
        <div className="flex items-center gap-2">
          <img src="/jeridoo.png" alt={establishmentName} className="w-8 h-8 rounded-full bg-white p-1" />
          <div>
            <div className="font-bold text-sm">{establishmentName}</div>
            <div className="text-xs">ID CARD</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 flex items-center gap-4">
        {/* Avatar */}
        <div
          className="w-16 h-16 rounded-full flex items-center justify-center text-lg font-bold"
          style={{
            backgroundColor: theme.secondaryColor,
            color: theme.textColor,
          }}
        >
          {user.avatar ? (
            <img
              src={user.avatar || "/jeridoo.png"}
              alt={user.firstname}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            getInitials()
          )}
        </div>

        {/* User Info */}
        <div className="flex-1">
          <h3 className="font-bold text-lg">
            {user.firstname} {user.lastname}
          </h3>
          <p className="text-sm text-gray-600">{user.role || "Student"}</p>
          <p className="text-xs text-gray-500">Valid until: {validUntil}</p>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 pb-2">
        <div className="text-xs text-gray-500 text-center">
          {user.email && <div>Email: {user.email}</div>}
          <div>ID: {user.cin || user.id?.substring(0, 8)}</div>
        </div>
      </div>
    </div>
  )
}

export default SimpleCardFallback
