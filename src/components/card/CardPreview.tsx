import React from 'react'
import { CardTheme } from './CardThemeSelector'
import { Student } from '@/interface/types'
import { User } from '@/types/user'
import ModernCardTemplate from './ModernCardTemplate'

interface CardPreviewProps {
  theme: CardTheme
  user?: User | Student
  student?: Student // Added for backward compatibility
  establishmentName: string
  establishmentLogo?: string
  validUntil?: string
  cardLayout?: 'horizontal' | 'vertical' // Added for backward compatibility
  showExportButton?: boolean
}

const CardPreview: React.FC<CardPreviewProps> = ({
  theme,
  user,
  establishmentName,
  establishmentLogo,
  validUntil = `DEC ${new Date().getFullYear() + 1}`,
  showExportButton = false,
}) => {
  // Default user data for preview
  const defaultUser: User = {
    id: 'preview-user',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    cin: '12345',
    role: 'STUDENT',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    class: {
      id: 'class-1',
      name: 'Class 10-A',
    },
  }

  // Use provided user or default
  const displayUser = user || defaultUser

  // Generate profile URL for QR code based on user role
  const getProfileUrl = () => {
    const userId = displayUser.id
    const role = displayUser.role?.toUpperCase()

    switch (role) {
      case 'STUDENT':
        return `/student-profile/${userId}`
      case 'TEACHER':
        return `/teacher-profile/${userId}`
      case 'ADMIN':
        return `/admin-profile/${userId}`
      case 'PARENT':
        return `/parent-profile/${userId}`
      default:
        return `/profile/${userId}`
    }
  }

  const profileUrl = getProfileUrl()

  // Use the modern card template
  return (
    <ModernCardTemplate
      user={
        {
          ...displayUser,
          role: displayUser.role || 'STUDENT',
          avatar: displayUser.avatar || undefined,
        } as User
      }
      establishmentName={establishmentName}
      establishmentLogo={establishmentLogo}
      validUntil={validUntil}
      theme={theme}
      profileUrl={profileUrl}
      showExportButton={showExportButton}
    />
  )
}

export default CardPreview
