"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ExternalLink } from "lucide-react"
import type { Establishment } from "@/interface/types"
import { useNavigate } from "@tanstack/react-router"
import CorsProxyImage from "@/components/shared/CorsProxyImage"
import CardTemplateSelector, { CARD_TEMPLATES, type CardTemplate } from "./card-template-selector"
import { FileText } from "lucide-react"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import EnhancedCardPreview from "./enhanced-card-preview"
import { useCreateCardTemplate } from "@/services/cardTemplateService"

interface EstablishmentServiceCardProps {
  establishment: Establishment
}

const EstablishmentServiceCard: React.FC<EstablishmentServiceCardProps> = ({ establishment }) => {
  const navigate = useNavigate()
  const createCardTemplateMutation = useCreateCardTemplate()

  const handleEdit = () => {
    navigate({
      to: "/super_admin/edit-establishment/$id",
      params: { id: String(establishment.id) },
    })
  }

  const handleCreateCard = () => {
    const etablissementId = localStorage.getItem("etablissementId")
    if (!etablissementId) {
      alert("No establishment ID found")
      return
    }

    const formattedHtml = selectedTemplate.config.html
      .replace("{establishmentName}", establishment.name)
      .replace("{establishmentLogo}", establishment.logo || "")
      .replace("{validUntil}", `DEC ${new Date().getFullYear() + 1}`)

    const templateData = {
      name: `${establishment.name} Card Template`,
      previewImageUrl: `/preview/${selectedTemplate.id}`,
      config: { html: formattedHtml },
      description: selectedTemplate.description,
      etablissementId,
    }

    createCardTemplateMutation.mutate(templateData, {
      onSuccess: (data) => {
        console.log("Card template created:", data)
        setIsDialogOpen(false)
        alert("Card template created successfully!")
      },
      onError: (error) => {
        console.error("Error creating card template:", error)
        alert("Failed to create card template. Please try again.")
      },
    })
  }

  const [selectedTemplate, setSelectedTemplate] = useState(CARD_TEMPLATES[0])
  const [theme, ] = useState({
    id: "blue",
    name: "Blue Professional",
    primaryColor: "#1a237e",
    secondaryColor: "#3949ab",
    textColor: "#ffffff",
    accentColor: "#ffeb3b",
  })

  const handleTemplateChange = (template: CardTemplate) => {
    setSelectedTemplate(template)
  }


  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Create dynamic user data based on establishment
  const createSampleUser = () => {
    return {
      id: "sample-123",
      firstname: "John",
      lastname: "Doe",
      email: `john.doe@${establishment.name.toLowerCase().replace(/\s+/g, "")}.edu`,
      cin: "STU123456",
      role: "STUDENT" as const,
      avatar: "/jeridoo.png", // Always use jeridoo.png as specified in enhanced-card-preview
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      class: {
        id: "class-1",
        name: "Computer Science 2024",
      },
      phone: "+216 12 345 678",
    }
  }

  // Get the school URL
  const getSchoolUrl = (url: string) => {
    if (url.startsWith("http")) return url
    return `https://${url}.jeridschool.tech`
  }

  return (
    <>
      <Card className="overflow-hidden border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800">{establishment.name}</CardTitle>
              <CardDescription className="text-gray-500">{establishment.address}</CardDescription>
            </div>
            <Badge
              variant={establishment.isActive ? "default" : "destructive"}
              className={`${establishment.isActive ? "bg-green-100 text-green-800 hover:bg-green-200" : ""}`}
            >
              {establishment.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {establishment.logo && (
              <div className="h-40 w-full overflow-hidden rounded-lg border border-gray-100 bg-white p-2 flex items-center justify-center">
                <CorsProxyImage
                  src={establishment.logo}
                  alt={`${establishment.name} logo`}
                  className="h-full max-h-36 object-contain"
                />
              </div>
            )}

            {/* Custom Services */}
            {establishment.services && establishment.services.length > 0 && (
              <div className="pt-2">
                <div className="flex items-center mb-3">
                  <div className="h-px flex-1 bg-gray-200"></div>
                  <p className="text-sm font-medium text-gray-500 px-3">Custom Services</p>
                  <div className="h-px flex-1 bg-gray-200"></div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {establishment.services.map((service: string, index: number) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 transition-colors px-3 py-1"
                    >
                      {service}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <div className="flex flex-col space-y-3 w-full">
            <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
              <FileText className="mr-2 h-4 w-4" />
              View Card Templates
            </Button>
            <div className="flex space-x-3 w-full">
              <Button
                variant="outline"
                className="flex-1 border-gray-200 hover:bg-gray-50 hover:text-gray-900 transition-all"
                onClick={handleEdit}
              >
                <span className="mr-1">Edit</span>
              </Button>
              {establishment.url && (
                <a
                  href={getSchoolUrl(establishment.url)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-md hover:from-blue-600 hover:to-blue-700 transition-all shadow-sm"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Site
                </a>
              )}
            </div>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">Card Templates for {establishment.name}</DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-[400px_1fr] gap-6">
            {/* Left: Template Selector */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Select Template</h3>
                <CardTemplateSelector
                  selectedTemplate={selectedTemplate}
                  onTemplateChange={handleTemplateChange}
                  theme={theme}
                />
              </div>
            </div>

            {/* Right: Live Preview */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Live Preview</h3>
                <Badge variant="outline" className="text-xs">
                  {selectedTemplate.orientation} • {selectedTemplate.style}
                </Badge>
              </div>

              <div className="border rounded-lg p-6 bg-gray-50 flex items-center justify-center min-h-[400px]">
                <EnhancedCardPreview
                  theme={theme}
                  template={selectedTemplate}
                  establishmentName={establishment.name}
                  establishmentLogo={establishment.logo || "/jeridoo.png"}
                  user={createSampleUser()}
                  validUntil={`DEC ${new Date().getFullYear() + 1}`}
                  showExportButton={false}
                />
              </div>

              {/* Template Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">{selectedTemplate.name}</h4>
                <p className="text-sm text-blue-700 mb-3">{selectedTemplate.description}</p>
                <div className="flex flex-wrap gap-1">
                  {selectedTemplate.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-500">
              Preview shows sample data. Actual cards will use real student information.
            </div>
            <Button
              onClick={handleCreateCard}
              disabled={createCardTemplateMutation.isPending}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {createCardTemplateMutation.isPending ? "Creating..." : "Create Card Template"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default EstablishmentServiceCard
