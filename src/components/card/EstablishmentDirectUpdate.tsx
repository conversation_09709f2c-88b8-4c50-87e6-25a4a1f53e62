import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { updateEstablishmentDirect } from '@/utils/establishmentUpdater';
import { directPatchRequest, xhrPatchRequest } from '@/utils/directFetch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface EstablishmentDirectUpdateProps {
  establishmentId: string;
}

const EstablishmentDirectUpdate: React.FC<EstablishmentDirectUpdateProps> = ({ establishmentId }) => {
  const [jsonData, setJsonData] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleUpdate = async () => {
    try {
      setIsLoading(true);

      // Parse the JSON data
      let data;
      try {
        data = JSON.parse(jsonData);
      } catch (error) {
        toast({
          title: 'Invalid JSON',
          description: 'Please enter valid JSON data',
          variant: 'destructive',
        });
        return;
      }

      // Try the XHR approach first (most compatible with Postman)
      try {
        console.log('Attempting update with XHR...');
        const url = `http://localhost:3000/etablissement/${establishmentId}`;
        const result = await xhrPatchRequest(url, data);

        console.log('XHR update successful:', result);

        toast({
          title: 'Success',
          description: 'Establishment updated successfully using XHR',
        });

        // Refresh the page after successful update
        setTimeout(() => {
          window.location.reload();
        }, 1500);

        return result;
      } catch (xhrError) {
        console.error('XHR update failed, trying direct fetch:', xhrError);

        // Try direct fetch as a fallback
        try {
          console.log('Attempting update with direct fetch...');
          const url = `http://localhost:3000/etablissement/${establishmentId}`;
          const result = await directPatchRequest(url, data);

          console.log('Direct fetch update successful:', result);

          toast({
            title: 'Success',
            description: 'Establishment updated successfully using direct fetch',
          });

          // Refresh the page after successful update
          setTimeout(() => {
            window.location.reload();
          }, 1500);

          return result;
        } catch (directFetchError) {
          console.error('Direct fetch update failed, falling back to axios:', directFetchError);
        }
      }

      // If direct fetch fails, fall back to the original method
      console.log('Falling back to original update method...');

      // Clean the data before sending
      // Remove properties that shouldn't be sent to the API
      const propertiesToRemove = [
        'superAdminId',
        'superAdminProfileId',
        'etablissementId',
        'id',
        'createdAt',
        'updatedAt',
        'createdBy',
        'updatedBy',
        'adminProfiles',
        'teacherProfiles',
        'studentProfiles',
        'superAdminProfile',
        'cardDesign' // This property is causing the 400 error
      ];

      propertiesToRemove.forEach(prop => {
        if (prop in data) {
          delete data[prop];
          console.log(`Removed ${prop} from update data in component`);
        }
      });

      // Call the original update function
      const result = await updateEstablishmentDirect(establishmentId, data);

      console.log('Fallback update result:', result);

      toast({
        title: 'Success',
        description: 'Establishment updated successfully using fallback method',
      });

      // Refresh the page after successful update
      setTimeout(() => {
        window.location.reload();
      }, 1500);

      return result;

    } catch (error) {
      console.error('Error updating establishment:', error);

      // Provide more detailed error message
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error Updating Establishment',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Tabs defaultValue="standard" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="standard">Standard Update</TabsTrigger>
        <TabsTrigger value="api-tester">API Tester</TabsTrigger>
      </TabsList>

      <TabsContent value="standard">
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Direct Establishment Update</CardTitle>
            <CardDescription>
              Update the establishment directly using the API. Paste the JSON data below.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const exampleData = `{
  "name": "aaaaaaaaaaaaaa",
  "address": "123 Education Avenue, Jerid, Tunisia",
  "logo": "./jeridoo.png",
  "url": "jerid",
  "CMSContent": "<h1>Welcome to Jerid International School</h1><p>A premier educational institution dedicated to excellence.</p>",
  "description": "Jerid International School is a leading educational institution offering comprehensive programs from kindergarten through high school. Our mission is to provide a nurturing environment where students can excel academically and develop into well-rounded individuals.",
  "heroImage": "https://example.com/hero-image.jpg",
  "galleryImages": [
    "https://example.com/gallery1.jpg",
    "https://example.com/gallery2.jpg",
    "https://example.com/gallery3.jpg"
  ],
  "contactEmail": "<EMAIL>",
  "contactPhone": "+21612345678",
  "socialLinks": {
    "facebook": "https://facebook.com/jerid-international",
    "twitter": "https://twitter.com/jerid_intl",
    "instagram": "https://instagram.com/jerid_international",
    "linkedin": "https://linkedin.com/company/jerid-international"
  },
  "stats": {
    "students": 850,
    "teachers": 65,
    "courses": 42,
    "awards": 28
  },
  "services": [
    "Academic Excellence",
    "Sports Programs",
    "Arts & Music",
    "STEM Education",
    "Language Immersion"
  ],
  "isActive": true
}`;
                  setJsonData(exampleData);
                }}
              >
                Load Example Data
              </Button>
            </div>
            <Textarea
              placeholder="Paste your JSON data here..."
              value={jsonData}
              onChange={(e) => setJsonData(e.target.value)}
              className="min-h-[200px] font-mono"
            />
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleUpdate}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Updating...' : 'Update Establishment'}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default EstablishmentDirectUpdate;
