"use client"

import type React from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Predefined themes
const CARD_THEMES = [
  {
    id: "blue",
    name: "Blue Professional",
    primaryColor: "#1a237e",
    secondaryColor: "#3949ab",
    textColor: "#ffffff",
    accentColor: "#ffeb3b",
  },
  {
    id: "green",
    name: "Green Academic",
    primaryColor: "#1b5e20",
    secondaryColor: "#388e3c",
    textColor: "#ffffff",
    accentColor: "#ffc107",
  },
  {
    id: "red",
    name: "Red Classic",
    primaryColor: "#b71c1c",
    secondaryColor: "#e53935",
    textColor: "#ffffff",
    accentColor: "#f5f5f5",
  },
  {
    id: "purple",
    name: "Purple Elite",
    primaryColor: "#4a148c",
    secondaryColor: "#7b1fa2",
    textColor: "#ffffff",
    accentColor: "#e1bee7",
  },
  {
    id: "dark",
    name: "Dark Modern",
    primaryColor: "#212121",
    secondaryColor: "#424242",
    textColor: "#ffffff",
    accentColor: "#ffd600",
  },
  {
    id: "custom",
    name: "Custom Theme",
    primaryColor: "#1976d2",
    secondaryColor: "#2196f3",
    textColor: "#ffffff",
    accentColor: "#ffeb3b",
  },
]

export interface CardTheme {
  id: string
  name: string
  primaryColor: string
  secondaryColor: string
  textColor: string
  accentColor: string
}

interface CardThemeSelectorProps {
  selectedTheme: CardTheme
  onThemeChange: (theme: CardTheme) => void
  onCustomThemeChange: (field: keyof CardTheme, value: string) => void
}

const CardThemeSelector: React.FC<CardThemeSelectorProps> = ({ selectedTheme, onThemeChange, onCustomThemeChange }) => {
  const handleThemeSelect = (themeId: string) => {
    const theme = CARD_THEMES.find((t) => t.id === themeId)
    if (theme) {
      onThemeChange(theme)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Theme</CardTitle>
        <CardDescription>Select a theme for the student ID card</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Select Theme</Label>

          <div className="max-h-[400px] overflow-y-auto pr-2">
            <RadioGroup
              value={selectedTheme.id}
              onValueChange={handleThemeSelect}
              className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4"
            >
              {CARD_THEMES.filter((t) => t.id !== "custom").map((theme) => (
                <div key={theme.id} className="relative">
                  <RadioGroupItem value={theme.id} id={theme.id} className="sr-only" />
                  <Label htmlFor={theme.id} className="cursor-pointer">
                    <Card
                      className={`overflow-hidden border-2 transition-all ${
                        selectedTheme.id === theme.id
                          ? "border-primary ring-2 ring-primary"
                          : "border-border hover:border-primary/50"
                      }`}
                    >
                      <div className="h-12" style={{ backgroundColor: theme.primaryColor }}></div>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: theme.primaryColor }}></div>
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: theme.secondaryColor }}></div>
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: theme.accentColor }}></div>
                        </div>
                        <p className="mt-2 font-medium">{theme.name}</p>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Custom Theme Option */}
          <div className="pt-4">
            <RadioGroup value={selectedTheme.id} onValueChange={handleThemeSelect}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom" />
                <Label htmlFor="custom" className="cursor-pointer">
                  Custom Theme
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        {selectedTheme.id === "custom" && (
          <div className="space-y-4 mt-4 p-4 border rounded-md max-h-[300px] overflow-y-auto">
            <h3 className="font-medium">Custom Theme Colors</h3>

            <div className="space-y-2">
              <Label htmlFor="primary-color">Primary Color</Label>
              <div className="flex gap-2">
                <div className="w-10 h-10 rounded border" style={{ backgroundColor: selectedTheme.primaryColor }} />
                <Input
                  id="primary-color"
                  type="color"
                  value={selectedTheme.primaryColor}
                  onChange={(e) => onCustomThemeChange("primaryColor", e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondary-color">Secondary Color</Label>
              <div className="flex gap-2">
                <div className="w-10 h-10 rounded border" style={{ backgroundColor: selectedTheme.secondaryColor }} />
                <Input
                  id="secondary-color"
                  type="color"
                  value={selectedTheme.secondaryColor}
                  onChange={(e) => onCustomThemeChange("secondaryColor", e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="text-color">Text Color</Label>
              <div className="flex gap-2">
                <div className="w-10 h-10 rounded border" style={{ backgroundColor: selectedTheme.textColor }} />
                <Input
                  id="text-color"
                  type="color"
                  value={selectedTheme.textColor}
                  onChange={(e) => onCustomThemeChange("textColor", e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accent-color">Accent Color</Label>
              <div className="flex gap-2">
                <div className="w-10 h-10 rounded border" style={{ backgroundColor: selectedTheme.accentColor }} />
                <Input
                  id="accent-color"
                  type="color"
                  value={selectedTheme.accentColor}
                  onChange={(e) => onCustomThemeChange("accentColor", e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default CardThemeSelector
export { CARD_THEMES }
