"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import cardService from "@/services/cardService";
import "./jeridoo.png"
interface CardTemplate {
  id: string;
  name: string;
  previewImageUrl: string;
  config: {
    html: string;
  };
  description: string;
  etablissementId: string;
  style: string;
  orientation: string;
  features: string[];  
  themeNumber: 1, // Add this
  colorNumber: 1, // Add this
  preferences: {} 
}

const EstablishmentApiTester = () => {
  const [formData, setFormData] = useState<Omit<CardTemplate, 'id'>>({
    name: "Student ID Card Template",
    previewImageUrl: "./jeridoo.png",
    config: {
      html: `<div style="width:300px;height:200px;border:1px solid #ccc;padding:20px;">
        <div style="text-align:center;margin-bottom:20px;">
          <h2>Student ID</h2>
          <img src="\${userDetails.photoUrl}" style="width:100px;height:120px;border-radius:50%;">
        </div>
        <div style="margin-top:20px;">
          <p><strong>Name:</strong> \${userDetails.fullName}</p>
          <p><strong>ID Number:</strong> \${userDetails.id}</p>
          <p><strong>Class:</strong> \${userDetails.class}</p>
          <p><strong>Valid Until:</strong> \${new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}</p>
        </div>
      </div>`
    },
    description: "Standard student ID card template with photo and basic information",
    etablissementId: "e9721bcc-c9d9-4e4c-a184-1a7b781c3d48",
    style: "modern",
    orientation: "portrait",
    features: ["photo", "qr-code", "barcode"],
    themeNumber: 1, // Add this
    colorNumber: 1, // Add this
    preferences: {} 
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleConfigChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        html: e.target.value
      }
    }));
  };

  const handleSubmit = async () => {
    try {
      const card = await cardService.createCard(formData);
      console.log("Created card:", card);
    } catch (error) {
      console.error("Error creating card:", error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardContent className="space-y-4">
        <input
          type="text"
          name="name"
          placeholder="Template Name"
          value={formData.name}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="previewImageUrl"
          placeholder="Preview Image URL"
          value={formData.previewImageUrl}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="description"
          placeholder="Description"
          value={formData.description}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <input
          type="text"
          name="etablissementId"
          placeholder="Establishment ID"
          value={formData.etablissementId}
          onChange={handleChange}
          className="w-full p-2 border rounded"
        />
        <textarea
          name="config.html"
          placeholder="HTML Template"
          value={formData.config.html}
          onChange={handleConfigChange}
          className="w-full h-40 p-2 border rounded font-mono"
        />
        <Button onClick={handleSubmit}>Create Card Template</Button>
      </CardContent>
    </Card>
  );
};

export default EstablishmentApiTester;

export const EstablishmentCardService = () => {
  const [templates, setTemplates] = useState<CardTemplate[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const cards = await cardService.getAllCards();
        // Map CardEntity[] to CardTemplate[]
        const templates: CardTemplate[] = cards.map((card: any) => ({
          id: card.id,
          name: card.name,
          previewImageUrl: card.previewImageUrl,
          config: card.config,
          description: card.description,
          etablissementId: card.etablissementId,
          style: card.style,
          orientation: card.orientation,
          features: card.features,
          themeNumber: 1, // Add this
          colorNumber: 1, // Add this
          preferences: {} 
        }));
        setTemplates(templates);
      } catch (error) {
        console.error("Error fetching cards:", error);
      }
    };
    fetchData();
  }, []);

  return (
    <div className="space-y-6 mt-8">
      {templates.map((template) => (
        <div key={template.id} className="border p-4 rounded shadow">
          <h3 className="text-xl font-bold">{template.name}</h3>
          <p>{template.description}</p>
          <div className="w-full max-w-sm mx-auto bg-white rounded-lg shadow-lg p-4" 
            dangerouslySetInnerHTML={{ 
              __html: template.config.html
                .replace('{establishmentName}', "Sample University")
                .replace('{user.firstname}', "John")
                .replace('{user.lastname}', "Doe")
                .replace('{user.role}', "Student")
                .replace('{user.class}', "CS-2024")
                .replace('{user.id}', "STU123456")
                .replace('{validUntil}', "DEC 2026")
                .replace('{userAvatar}', "/placeholder.svg?height=100&width=100&text=JD")
                .replace('{qrCode}', "/placeholder.svg?height=50&width=50&text=QR")
                .replace('{barcode}', "/placeholder.svg?height=20&width=80&text=|||")
            }}
          />
        </div>
      ))}
    </div>
  );
};