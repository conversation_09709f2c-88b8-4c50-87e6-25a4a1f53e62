import React from 'react'
import { Card } from '@/components/ui/card'
import QRCodeGenerator from '@/components/shared/QRCodeGenerator'
import { User } from '@/types/user'

interface CardTemplateProps {
  user: User
  establishmentName: string
  establishmentLogo?: string
  validUntil: string
  theme: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    textColor: string
  }
  profileUrl: string
}

// Template 1: Red University Style (Horizontal)
export const CardTemplate1: React.FC<CardTemplateProps> = ({
  user,
  establishmentName,
  establishmentLogo,
  validUntil,
  theme,
  profileUrl,
}) => {
  const avatarUrl = user.avatar || '/assets/default-avatar.png'
  const userRole = user.role?.toLowerCase() || 'member'

  return (
    <Card className="w-[350px] h-[220px] overflow-hidden shadow-lg relative">
      {/* Top curved background */}
      <div
        className="w-full h-full absolute"
        style={{
          backgroundColor: theme.primaryColor,
          clipPath:
            'polygon(0 0, 100% 0, 100% 100%, 0 100%, 0 0, 100% 0, 95% 10%, 100% 20%, 95% 30%, 100% 40%, 95% 50%, 100% 60%, 95% 70%, 100% 80%, 95% 90%, 100% 100%, 0 100%, 0 0)',
        }}
      />

      {/* White content area */}
      <div className="absolute inset-0 m-1 bg-white rounded-sm overflow-hidden">
        {/* Header with establishment logo */}
        <div className="h-12 flex items-center px-4">
          {establishmentLogo ? (
            <div className="flex items-center">
              <img
                src={establishmentLogo}
                alt={establishmentName}
                className="h-10 w-10 object-contain mr-2"
              />
              <div
                className="text-sm font-bold uppercase"
                style={{ color: theme.primaryColor }}
              >
                {establishmentName}
              </div>
            </div>
          ) : (
            <div
              className="text-sm font-bold uppercase"
              style={{ color: theme.primaryColor }}
            >
              {establishmentName}
            </div>
          )}
        </div>

        {/* ID Card Title */}
        <div
          className="mx-4 py-1 px-2 text-center text-white font-bold uppercase text-sm"
          style={{ backgroundColor: theme.primaryColor }}
        >
          {userRole} ID CARD
        </div>

        {/* Main content */}
        <div className="flex p-3">
          {/* Left side - Photo */}
          <div className="mr-4">
            <div
              className="w-[80px] h-[80px] rounded-full overflow-hidden border-2"
              style={{ borderColor: theme.primaryColor }}
            >
              <img
                src={avatarUrl}
                alt={user.firstname}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Right side - User info */}
          <div className="flex-1">
            <h3
              className="text-lg font-bold uppercase"
              style={{ color: theme.primaryColor }}
            >
              {user.firstname} {user.lastname}
            </h3>

            <div className="grid grid-cols-[80px_auto] gap-y-1 mt-1">
              <span className="text-xs font-semibold">ID</span>
              <span className="text-xs">
                : {user.cin || user.id?.substring(0, 8)}
              </span>

              {user.class && (
                <>
                  <span className="text-xs font-semibold">Class</span>
                  <span className="text-xs">
                    :{' '}
                    {typeof user.class === 'object'
                      ? user.class.name
                      : user.class}
                  </span>
                </>
              )}

              {user.email && (
                <>
                  <span className="text-xs font-semibold">Email</span>
                  <span className="text-xs">: {user.email}</span>
                </>
              )}

              <span className="text-xs font-semibold">Valid Until</span>
              <span className="text-xs">: {validUntil}</span>
            </div>
          </div>
        </div>

        {/* Barcode/QR at bottom */}
        <div className="absolute bottom-2 right-4">
          <QRCodeGenerator
            value={profileUrl}
            size={50}
            logoUrl={establishmentLogo}
          />
        </div>
      </div>
    </Card>
  )
}

// Template 2: Yellow-Black Modern Style (Double-sided)
export const CardTemplate2: React.FC<CardTemplateProps> = ({
  user,
  establishmentName,
  establishmentLogo,
  validUntil,
  theme,
  profileUrl,
}) => {
  const avatarUrl = user.avatar || '/assets/default-avatar.png'
  const userRole = user.role?.toLowerCase() || 'member'

  // Use theme colors or default to yellow-black if not provided
  const primaryColor = theme.primaryColor || '#333333'
  const secondaryColor = theme.secondaryColor || '#ffc107'
  const textColor = theme.textColor || '#ffffff'

  return (
    <div className="flex flex-col gap-4">
      {/* Front side */}
      <Card className="w-[320px] h-[200px] rounded-xl overflow-hidden shadow-lg relative">
        {/* Top black section with wave */}
        <div
          className="w-full h-[80px] relative"
          style={{ backgroundColor: primaryColor }}
        >
          {/* Logo */}
          <div className="absolute top-3 left-3 flex items-center">
            {establishmentLogo ? (
              <img
                src={establishmentLogo}
                alt={establishmentName}
                className="h-8 w-8 object-contain mr-1"
              />
            ) : (
              <div
                className="h-8 w-8 rounded-full flex items-center justify-center"
                style={{ backgroundColor: secondaryColor }}
              >
                <span
                  className="text-xs font-bold"
                  style={{ color: primaryColor }}
                >
                  LOGO
                </span>
              </div>
            )}
            <span className="text-sm font-bold" style={{ color: textColor }}>
              LOGO
            </span>
          </div>

          {/* Wave shape */}
          <div
            className="absolute bottom-0 w-full h-[40px]"
            style={{
              backgroundColor: secondaryColor,
              clipPath:
                'polygon(0 100%, 100% 100%, 100% 0, 75% 50%, 50% 0, 25% 50%, 0 0)',
            }}
          />
        </div>

        {/* White content area */}
        <div className="w-full h-[120px] bg-white p-4 flex">
          {/* Circle for photo that overlaps sections */}
          <div className="relative -mt-16 mr-4">
            <div
              className="w-[90px] h-[90px] rounded-full overflow-hidden border-4 bg-white"
              style={{ borderColor: 'white' }}
            >
              <img
                src={avatarUrl}
                alt={user.firstname}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* User info */}
          <div className="flex-1 mt-2">
            <h3 className="font-bold text-gray-800">
              <span className="text-lg">{user.firstname}</span>{' '}
              <span style={{ color: secondaryColor }}>{user.lastname}</span>
            </h3>
            <p className="text-sm text-gray-600">{userRole}</p>

            <div className="mt-2 text-xs text-gray-700">
              <div className="flex">
                <span className="w-20 font-semibold">ID no:</span>
                <span>{user.cin || user.id?.substring(0, 8)}</span>
              </div>

              <div className="flex">
                <span className="w-20 font-semibold">Join Date:</span>
                <span>
                  {new Date(user.createdAt || Date.now()).toLocaleDateString()}
                </span>
              </div>

              {user.phone && (
                <div className="flex">
                  <span className="w-20 font-semibold">Phone:</span>
                  <span>{user.phone}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Barcode at bottom */}
        <div
          className="absolute bottom-0 w-full h-6 flex justify-center items-center"
          style={{ backgroundColor: 'white' }}
        >
          <div className="w-3/4 h-4 bg-[url('/assets/barcode.png')] bg-contain bg-no-repeat bg-center"></div>
        </div>
      </Card>

      {/* Back side (optional) */}
      <Card className="w-[320px] h-[200px] rounded-xl overflow-hidden shadow-lg relative">
        {/* Top black section with wave */}
        <div
          className="w-full h-[80px] relative"
          style={{ backgroundColor: primaryColor }}
        >
          {/* Logo */}
          <div className="absolute top-3 left-3 flex items-center">
            {establishmentLogo ? (
              <img
                src={establishmentLogo}
                alt={establishmentName}
                className="h-8 w-8 object-contain mr-1"
              />
            ) : (
              <div
                className="h-8 w-8 rounded-full flex items-center justify-center"
                style={{ backgroundColor: secondaryColor }}
              >
                <span
                  className="text-xs font-bold"
                  style={{ color: primaryColor }}
                >
                  LOGO
                </span>
              </div>
            )}
            <span className="text-sm font-bold" style={{ color: textColor }}>
              LOGO
            </span>
          </div>

          {/* Wave shape */}
          <div
            className="absolute bottom-0 w-full h-[40px]"
            style={{
              backgroundColor: secondaryColor,
              clipPath:
                'polygon(0 100%, 100% 100%, 100% 0, 75% 50%, 50% 0, 25% 50%, 0 0)',
            }}
          />
        </div>

        {/* White content area */}
        <div className="w-full h-[120px] bg-white p-4">
          <h3
            className="text-sm font-bold mb-2"
            style={{ color: secondaryColor }}
          >
            Terms And Conditions:
          </h3>

          <ul className="text-xs space-y-2">
            <li className="flex">
              <span className="text-yellow-500 mr-2">•</span>
              <span>
                This card is the property of {establishmentName} and must be
                returned upon request.
              </span>
            </li>
            <li className="flex">
              <span className="text-yellow-500 mr-2">•</span>
              <span>If found, please return to the administration office.</span>
            </li>
          </ul>

          <div className="mt-3">
            <h3
              className="text-sm font-bold mb-1"
              style={{ color: secondaryColor }}
            >
              Contact us
            </h3>
            <div className="flex justify-between items-end">
              <div className="text-xs">
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-1">•</span>
                  <span>
                    www.{establishmentName.toLowerCase().replace(/\s+/g, '')}
                    .com
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-1">•</span>
                  <span>Valid until: {validUntil}</span>
                </div>
              </div>

              <QRCodeGenerator
                value={profileUrl}
                size={50}
                logoUrl={establishmentLogo}
              />
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Template 3: Blue Hexagon Style
export const CardTemplate3: React.FC<CardTemplateProps> = ({
  user,
  establishmentName,
  establishmentLogo,
  validUntil,
  theme,
}) => {
  const avatarUrl = user.avatar || '/assets/default-avatar.png'
  const userRole = user.role?.toLowerCase() || 'member'

  // Use theme colors or default to blue if not provided
  const primaryColor = theme.primaryColor || '#0047AB'
  const secondaryColor = theme.secondaryColor || '#FFB81C'
  const accentColor = theme.accentColor || '#6C757D'

  return (
    <Card className="w-[350px] h-[220px] overflow-hidden shadow-lg relative">
      {/* Background with diagonal sections */}
      <div className="absolute inset-0">
        {/* Top blue section */}
        <div
          className="w-full h-[40px]"
          style={{ backgroundColor: primaryColor }}
        />

        {/* Diagonal gray section */}
        <div
          className="absolute top-[40px] left-0 w-[150px] h-[180px]"
          style={{
            backgroundColor: accentColor,
            clipPath: 'polygon(0 0, 100% 0, 0 100%, 0 0)',
          }}
        />

        {/* Diagonal yellow accent */}
        <div
          className="absolute bottom-0 right-0 w-[100px] h-[40px]"
          style={{
            backgroundColor: secondaryColor,
            clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0 100%)',
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 p-4 flex h-full">
        {/* Left side - Photo in hexagon */}
        <div className="w-[120px] flex items-center justify-center">
          <div
            className="w-[110px] h-[110px] relative"
            style={{
              clipPath:
                'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
              backgroundColor: primaryColor,
            }}
          >
            <div
              className="absolute inset-[3px] overflow-hidden"
              style={{
                clipPath:
                  'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
                backgroundColor: 'white',
              }}
            >
              <img
                src={avatarUrl}
                alt={user.firstname}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* Right side - School info and user details */}
        <div className="flex-1 ml-4 text-white">
          {/* School name and logo */}
          <div className="flex items-center mb-2">
            {establishmentLogo ? (
              <img
                src={establishmentLogo}
                alt={establishmentName}
                className="h-8 w-8 object-contain mr-2"
              />
            ) : null}
            <h2 className="text-lg font-bold" style={{ color: primaryColor }}>
              {establishmentName}
            </h2>
          </div>

          {/* ID Card title */}
          <h1
            className="text-3xl font-bold mb-4"
            style={{ color: primaryColor }}
          >
            {userRole} Card
          </h1>

          {/* User details */}
          <div className="space-y-1 text-gray-800">
            <div className="flex">
              <span className="w-24 font-semibold">Name</span>
              <span>
                : {user.firstname} {user.lastname}
              </span>
            </div>

            <div className="flex">
              <span className="w-24 font-semibold">ID Number</span>
              <span>: {user.cin || user.id?.substring(0, 8)}</span>
            </div>

            {user.email && (
              <div className="flex">
                <span className="w-24 font-semibold">Email</span>
                <span>: {user.email}</span>
              </div>
            )}

            <div className="flex">
              <span className="w-24 font-semibold">Expire Date</span>
              <span>: {validUntil}</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}

export const getCardTemplate = (templateNumber: number) => {
  switch (templateNumber) {
    case 1:
      return CardTemplate1
    case 2:
      return CardTemplate2
    case 3:
      return CardTemplate3
    default:
      return CardTemplate1
  }
}

export default getCardTemplate
