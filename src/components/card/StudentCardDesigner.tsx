"use client"

import type React from "react"
import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useQuery } from "@tanstack/react-query"
import { studentService } from "@/lib/api/services"
import type { Student } from "@/interface/types"
import CardThemeSelector, { type CardTheme, CARD_THEMES } from "./CardThemeSelector"
import CardTemplateSelector, { type CardTemplate, CARD_TEMPLATES } from "./card-template-selector"
import EnhancedCardPreview from "./enhanced-card-preview"
import { toast } from "sonner"
import { DatePicker } from "@/components/ui/date-picker"
import { format, addYears } from "date-fns"
import { User } from "@/types/user"

interface StudentCardDesignerProps {
  establishmentId: string
  establishmentName: string
  establishmentLogo?: string
  initialSettings?: CardDesignSettings
  onSave: (settings: CardDesignSettings) => void
}

export interface CardDesignSettings {
  template: CardTemplate
  theme: CardTheme
  layout: "horizontal" | "vertical"
  validUntilDate: string
  showQrCode: boolean
  showLogo: boolean
  customFields: {
    enabled: boolean
    field1Label: string
    field2Label: string
  }
}

const StudentCardDesigner: React.FC<StudentCardDesignerProps> = ({
  establishmentId,
  establishmentName,
  establishmentLogo,
  initialSettings,
  onSave,
}) => {
  // Default settings
  const defaultSettings: CardDesignSettings = {
    template: CARD_TEMPLATES[0],
    theme: CARD_THEMES[0],
    layout: "horizontal",
    validUntilDate: format(addYears(new Date(), 1), "MMM yyyy"),
    showQrCode: true,
    showLogo: true,
    customFields: {
      enabled: false,
      field1Label: "Parent",
      field2Label: "Contact",
    },
  }

  // State for card design settings
  const [settings, setSettings] = useState<CardDesignSettings>(initialSettings || defaultSettings)

  // State for selected student (for preview)
  const [selectedStudentId, setSelectedStudentId] = useState<string>("")

  // Fetch students for the preview dropdown
  const { data: students = [] } = useQuery({
    queryKey: ["students", establishmentId],
    queryFn: () => studentService.getAll(),
    enabled: !!establishmentId,
  })

  // Find the selected student
  const selectedStudent = students.find((s) => s.id === selectedStudentId)

  // Handle theme change
  const handleThemeChange = (theme: CardTheme) => {
    setSettings((prev) => ({
      ...prev,
      theme,
    }))
  }

  // Handle custom theme color change
  const handleCustomThemeChange = (field: keyof CardTheme, value: string) => {
    setSettings((prev) => ({
      ...prev,
      theme: {
        ...prev.theme,
        [field]: value,
      },
    }))
  }

  // Handle layout change
  const handleLayoutChange = (layout: "horizontal" | "vertical") => {
    setSettings((prev) => ({
      ...prev,
      layout,
    }))
  }

  // Handle valid until date change
  const handleValidUntilChange = (date: Date | undefined) => {
    if (date) {
      setSettings((prev) => ({
        ...prev,
        validUntilDate: format(date, "MMM yyyy"),
      }))
    }
  }

  // Handle toggle changes
  const handleToggleChange = (field: "showQrCode" | "showLogo") => {
    setSettings((prev) => ({
      ...prev,
      [field]: !prev[field],
    }))
  }

  // Handle custom fields toggle
  const handleCustomFieldsToggle = () => {
    setSettings((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        enabled: !prev.customFields.enabled,
      },
    }))
  }

  // Handle custom field label change
  const handleCustomFieldLabelChange = (field: "field1Label" | "field2Label", value: string) => {
    setSettings((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        [field]: value,
      },
    }))
  }

  // Handle save
  const handleSave = () => {
    onSave(settings)
    toast.success("Card design settings saved successfully")
  }

  // Handle reset to defaults
  const handleReset = () => {
    setSettings(defaultSettings)
    toast.info("Card design settings reset to defaults")
  }

  // Handle template change
  const handleTemplateChange = (template: CardTemplate) => {
    setSettings((prev) => ({
      ...prev,
      template,
    }))
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Student ID Card Designer</CardTitle>
        <CardDescription>Customize the appearance of student ID cards for your establishment</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="preview">
          <TabsList className="mb-4">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="template">Template</TabsTrigger>
            <TabsTrigger value="design">Design</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
          </TabsList>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-4">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Card Preview */}
              <div className="flex-1 flex justify-center items-start">
                <EnhancedCardPreview
                  theme={settings.theme}
                  template={settings.template}
                  user={selectedStudent as User}
                  establishmentName={establishmentName}
                  establishmentLogo={settings.showLogo ? establishmentLogo : undefined}
                  validUntil={settings.validUntilDate}
                  showExportButton={false}
                />
              </div>

              {/* Preview Controls */}
              <div className="w-full md:w-[300px] space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="student-select">Preview with Student</Label>
                  <Select value={selectedStudentId} onValueChange={setSelectedStudentId}>
                    <SelectTrigger id="student-select">
                      <SelectValue placeholder="Select a student" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Default Preview</SelectItem>
                      {students.map((student: Student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.firstname} {student.lastname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Card Layout</Label>
                  <div className="flex gap-2">
                    <Button
                      variant={settings.layout === "horizontal" ? "default" : "outline"}
                      onClick={() => handleLayoutChange("horizontal")}
                      className="flex-1"
                    >
                      Horizontal
                    </Button>
                    <Button
                      variant={settings.layout === "vertical" ? "default" : "outline"}
                      onClick={() => handleLayoutChange("vertical")}
                      className="flex-1"
                    >
                      Vertical
                    </Button>
                  </div>
                </div>

                <div className="pt-4">
                  <Button onClick={handleSave} className="w-full">
                    Save Card Design
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Template Tab */}
          <TabsContent value="template">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Template Selector */}
              <div className="flex-1">
                <CardTemplateSelector
                  selectedTemplate={settings.template}
                  onTemplateChange={handleTemplateChange}
                  theme={settings.theme}
                />
              </div>

              {/* Card Preview (smaller) */}
              <div className="w-full md:w-[300px] flex justify-center">
                <div className="transform scale-75 origin-top">
                  <EnhancedCardPreview
                    theme={settings.theme}
                    template={settings.template}
                    establishmentName={establishmentName}
                    establishmentLogo={settings.showLogo ? establishmentLogo : undefined}
                    validUntil={settings.validUntilDate}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Design Tab */}
          <TabsContent value="design">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Theme Selector */}
              <div className="flex-1">
                <CardThemeSelector
                  selectedTheme={settings.theme}
                  onThemeChange={handleThemeChange}
                  onCustomThemeChange={handleCustomThemeChange}
                />
              </div>

              {/* Card Preview (smaller) */}
              <div className="w-full md:w-[300px] flex justify-center">
                <div className="transform scale-75 origin-top">
                  <EnhancedCardPreview
                    theme={settings.theme}
                    template={settings.template}
                    establishmentName={establishmentName}
                    establishmentLogo={settings.showLogo ? establishmentLogo : undefined}
                    validUntil={settings.validUntilDate}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Content Settings */}
              <div className="flex-1 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="valid-until">Valid Until</Label>
                  <DatePicker
                    date={new Date(settings.validUntilDate)}
                    setDate={handleValidUntilChange}
                    // showMonthYearPicker prop removed to fix TypeScript error
                  />
                  <p className="text-sm text-muted-foreground">
                    This date will appear on the card as the expiration date
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-qr">Show QR Code</Label>
                  <Switch
                    id="show-qr"
                    checked={settings.showQrCode}
                    onCheckedChange={() => handleToggleChange("showQrCode")}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-logo">Show Establishment Logo</Label>
                  <Switch
                    id="show-logo"
                    checked={settings.showLogo}
                    onCheckedChange={() => handleToggleChange("showLogo")}
                  />
                </div>

                <div className="pt-2 pb-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="custom-fields">Enable Custom Fields</Label>
                    <Switch
                      id="custom-fields"
                      checked={settings.customFields.enabled}
                      onCheckedChange={handleCustomFieldsToggle}
                    />
                  </div>
                </div>

                {settings.customFields.enabled && (
                  <div className="space-y-4 pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="field1-label">Custom Field 1 Label</Label>
                      <Input
                        id="field1-label"
                        value={settings.customFields.field1Label}
                        onChange={(e) => handleCustomFieldLabelChange("field1Label", e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="field2-label">Custom Field 2 Label</Label>
                      <Input
                        id="field2-label"
                        value={settings.customFields.field2Label}
                        onChange={(e) => handleCustomFieldLabelChange("field2Label", e.target.value)}
                      />
                    </div>
                  </div>
                )}

                <div className="pt-4 flex gap-2">
                  <Button onClick={handleSave} className="flex-1">
                    Save Settings
                  </Button>
                  <Button onClick={handleReset} variant="outline" className="flex-1">
                    Reset to Defaults
                  </Button>
                </div>
              </div>

              {/* Card Preview (smaller) */}
              <div className="w-full md:w-[300px] flex justify-center">
                <div className="transform scale-75 origin-top">
                  <EnhancedCardPreview
                    theme={settings.theme}
                    template={settings.template}
                    establishmentName={establishmentName}
                    establishmentLogo={settings.showLogo ? establishmentLogo : undefined}
                    validUntil={settings.validUntilDate}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default StudentCardDesigner
