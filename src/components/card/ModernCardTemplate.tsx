"use client"

import type React from "react"
import type { User } from "@/types/user"
import QRCodeGenerator from "@/components/shared/QRCodeGenerator"
import type { CardTheme } from "./CardThemeSelector"
// @ts-ignore
import html2pdf from "html2pdf.js"

interface ModernCardTemplateProps {
  user: User
  establishmentName: string
  establishmentLogo?: string
  validUntil?: string
  theme: CardTheme
  profileUrl?: string
  showExportButton?: boolean
}

const ModernCardTemplate: React.FC<ModernCardTemplateProps> = ({
  user,
  establishmentName,
  establishmentLogo,
  validUntil = "Apr 20, 2026",
  theme,
  profileUrl,
  showExportButton = false,
}) => {
  const defaultAvatar = "/jeridoo.png"
  const avatarUrl = user.avatar || defaultAvatar
  const logoUrl = establishmentLogo || "/jeridoo.png" // Use establishment logo or fallback

  // Function to export card as PDF
  const exportToPDF = () => {
    const cardElement = document.getElementById("modern-card-to-export")
    if (!cardElement) return

    const opt = {
      margin: 0,
      filename: `${user.firstname}_${user.lastname}_ID_Card.pdf`,
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: "mm", format: "a6", orientation: "portrait" },
    }

    html2pdf()
      .set({
        ...opt,
        jsPDF: { ...opt.jsPDF, orientation: "portrait" as const },
      })
      .from(cardElement)
      .save()
  }

  // Generate initials for avatar placeholder
  // const getInitials = () => {
  //   const firstInitial = user.firstname ? user.firstname.charAt(0).toUpperCase() : "S"
  //   const lastInitial = user.lastname ? user.lastname.charAt(0).toUpperCase() : "S"
  //   return `${firstInitial}${lastInitial}`
  // }

  return (
    <div className="flex flex-col items-center">
      <div
        id="modern-card-to-export"
        className="w-[350px] bg-white rounded-lg overflow-hidden shadow-lg"
        style={{ fontFamily: "Inter, sans-serif" }}
      >
        {/* Header */}
        <div
          className="w-full p-4 flex items-center justify-between"
          style={{
            backgroundColor: theme.primaryColor,
            color: theme.textColor,
          }}
        >
          <div className="flex items-center gap-2">
            <img
              src={logoUrl || "/placeholder.svg"}
              alt={establishmentName}
              className="w-10 h-10 rounded-full bg-white p-1"
            />
            <div>
              <div className="font-bold uppercase text-sm">{user.firstname}</div>
              <div className="text-xs uppercase">OFFICIAL</div>
            </div>
          </div>

          <QRCodeGenerator value={profileUrl || `Email: ${user.email || ""}`} size={50} logoUrl={logoUrl} />
        </div>

        {/* Avatar */}
        <div className="flex justify-center -mt-6">
          <div
            className="w-24 h-24 rounded-full flex items-center justify-center text-3xl font-bold"
            style={{
              backgroundColor: theme.secondaryColor || "#e9e02f",
              color: "#333",
              border: "4px solid white",
            }}
          >
            <img
              src={avatarUrl || "/placeholder.svg"}
              alt={user.firstname}
              className="w-full h-full rounded-full object-cover"
            />
          </div>
        </div>

        {/* User Info */}
        <div className="p-6 pt-2">
          <div className="text-center mb-4">
            <div className="bg-gray-100 py-1 px-2 rounded-md mb-2">{establishmentName}</div>
            <h2 className="text-xl font-bold">
              {user.firstname} {user.lastname}
            </h2>
            <p className="text-gray-600">{user.role || "Admin"}</p>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex">
              <span className="font-semibold w-24">Email:</span>
              <span className="text-gray-600">{user.email || "<EMAIL>"}</span>
            </div>

            <div className="flex">
              <span className="font-semibold w-24">Valid Until:</span>
              <span className="text-gray-600">{validUntil}</span>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200 text-xs text-center text-gray-500">
            <p>This card is the property of {establishmentName}</p>
            <p>If found, please return to the administration office</p>
            <div className="mt-2 border-t border-dashed pt-2 text-center">
              <span>Signature</span>
            </div>
          </div>
        </div>
      </div>

      {showExportButton && (
        <button
          onClick={exportToPDF}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
          Download ID Card
        </button>
      )}
    </div>
  )
}

export default ModernCardTemplate
