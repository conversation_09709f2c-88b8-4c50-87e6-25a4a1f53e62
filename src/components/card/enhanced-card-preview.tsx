"use client"

import type React from "react"
import type { CardTheme } from "./CardThemeSelector"
import type { CardTemplate } from "./card-template-selector"
import type { User } from "@/types/user"
import ModernCardTemplate from "./ModernCardTemplate"
import { CardTemplate1, CardTemplate2, CardTemplate3 } from "./CardTemplates"
import SimpleCardFallback from "./simple-card-fallback"

interface EnhancedCardPreviewProps {
  theme: CardTheme
  template: CardTemplate
  user?: User
  establishmentName: string
  establishmentLogo?: string
  validUntil?: string
  showExportButton?: boolean
}

const EnhancedCardPreview: React.FC<EnhancedCardPreviewProps> = ({
  theme,
  template,
  user,
  establishmentName,
  establishmentLogo,
  validUntil = `DEC ${new Date().getFullYear() + 1}`,
  showExportButton = false,
}) => {
  // Default user data for preview with jeridoo.png avatar
  const defaultUser: User = {
    id: "preview-user",
    firstname: "<PERSON>",
    lastname: "<PERSON><PERSON>",
    email: "<EMAIL>",
    cin: "STU123456",
    role: "STUDENT",
    avatar: "/jeridoo.png", // Always use jeridoo.png for user avatar
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    class: {
      id: "class-1",
      name: "Computer Science 2024",
    },
    phone: "+216 12 345 678",
  }

  // Use provided user or default, but always override avatar to use jeridoo.png
  const displayUser = user ? { ...user, avatar: "/jeridoo.png" } : defaultUser

  // Generate profile URL for QR code
  const getProfileUrl = () => {
    const userId = displayUser.id
    const role = displayUser.role?.toUpperCase()

    switch (role) {
      case "STUDENT":
        return `/student-profile/${userId}`
      case "TEACHER":
        return `/teacher-profile/${userId}`
      case "ADMIN":
        return `/admin-profile/${userId}`
      case "PARENT":
        return `/parent-profile/${userId}`
      default:
        return `/profile/${userId}`
    }
  }

  const profileUrl = getProfileUrl()

  // Render the appropriate template based on selection
  const renderTemplate = () => {
    const commonProps = {
      user: displayUser,
      establishmentName,
      establishmentLogo: establishmentLogo || "/jeridoo.png",
      validUntil,
      theme,
      profileUrl,
    }

    try {
      // Use the template's HTML config for rendering
      if (template.config?.html) {
        return (
          <div className="relative">
            <div
              dangerouslySetInnerHTML={{
                __html: template.config.html
                  .replace(/{establishmentName}/g, establishmentName)
                  .replace(/{user\.firstname}/g, displayUser.firstname)
                  .replace(/{user\.lastname}/g, displayUser.lastname)
                  .replace(/{user\.role}/g, displayUser.role || "STUDENT")
                  .replace(
                    /{user\.class}/g,
                    typeof displayUser.class === "object" ? displayUser.class.name : displayUser.class || "N/A",
                  )
                  .replace(/{user\.id}/g, displayUser.id || "N/A")
                  .replace(/{user\.email}/g, displayUser.email || "N/A")
                  .replace(/{user\.phone}/g, displayUser.phone || "N/A")
                  .replace(/{user\.cin}/g, displayUser.cin || displayUser.id?.substring(0, 8) || "N/A")
                  .replace(/{validUntil}/g, validUntil)
                  .replace(/{userAvatar}/g, "/jeridoo.png")
                  .replace(/{qrCode}/g, "/placeholder.svg?height=50&width=50&text=QR")
                  .replace(/{barcode}/g, "/placeholder.svg?height=20&width=80&text=|||"),
              }}
            />
          </div>
        )
      }

      // Fallback to specific template components
      switch (template.id) {
        case "modern":
        case "modern-vertical":
          return <ModernCardTemplate {...commonProps} showExportButton={showExportButton} />

        case "classic-horizontal":
          try {
            return <CardTemplate1 {...commonProps} />
          } catch (error) {
            console.warn("CardTemplate1 not available, using ModernCardTemplate as fallback")
            return <ModernCardTemplate {...commonProps} showExportButton={showExportButton} />
          }

        case "creative-wave":
          try {
            return <CardTemplate2 {...commonProps} />
          } catch (error) {
            console.warn("CardTemplate2 not available, using ModernCardTemplate as fallback")
            return <ModernCardTemplate {...commonProps} showExportButton={showExportButton} />
          }

        case "hexagon-tech":
          try {
            return <CardTemplate3 {...commonProps} />
          } catch (error) {
            console.warn("CardTemplate3 not available, using ModernCardTemplate as fallback")
            return <ModernCardTemplate {...commonProps} showExportButton={showExportButton} />
          }

        default:
          return <ModernCardTemplate {...commonProps} showExportButton={showExportButton} />
      }
    } catch (error) {
      console.error("Error rendering card template:", error)
      // Use SimpleCardFallback instead of the error message
      return (
        <SimpleCardFallback
          user={displayUser}
          establishmentName={establishmentName}
          establishmentLogo={establishmentLogo}
          validUntil={validUntil}
          theme={theme}
          profileUrl={profileUrl}
        />
      )
    }
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="relative">
        {renderTemplate()}

        {/* Template indicator */}
        <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium">
          {template.name}
        </div>
      </div>

      {/* Template info */}
      <div className="text-center space-y-1">
        <p className="text-sm font-medium">{template.name}</p>
        <p className="text-xs text-muted-foreground">{template.description}</p>
      </div>
    </div>
  )
}

export default EnhancedCardPreview
