import { useEffect, useState } from 'react'
import { schoolService } from '@/services/schoolService'

export function SubdomainDebugger() {
  const [debugInfo, setDebugInfo] = useState({
    hostname: '',
    isSubdomain: false,
    schoolName: '',
    queryParams: '',
    fullUrl: '',
    apiBaseUrl: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  })

  useEffect(() => {
    // Get information about the current URL
    const hostname = window.location.hostname
    const isSubdomain = schoolService.isSubdomainUrl()
    const schoolName = schoolService.getCurrentSchoolFromSubdomain() || 'none'
    const queryParams = window.location.search
    const fullUrl = window.location.href

    setDebugInfo({
      hostname,
      isSubdomain,
      schoolName,
      queryParams,
      fullUrl,
      apiBaseUrl:
        import.meta.env.VITE_API_URL || 'http://localhost:3000',
    })
  }, [])

  return (
    <div className="p-4 bg-gray-100 rounded-lg mb-4">
      <h2 className="text-lg font-bold mb-2">Subdomain Debug Info</h2>
      <div className="space-y-1 text-sm font-mono">
        <p>
          <span className="font-bold">Hostname:</span> {debugInfo.hostname}
        </p>
        <p>
          <span className="font-bold">Is Subdomain:</span>{' '}
          {debugInfo.isSubdomain ? 'Yes' : 'No'}
        </p>
        <p>
          <span className="font-bold">School Name from Subdomain:</span>{' '}
          {debugInfo.schoolName}
        </p>
        <p>
          <span className="font-bold">Query Parameters:</span>{' '}
          {debugInfo.queryParams || '(none)'}
        </p>
        <p>
          <span className="font-bold">Full URL:</span> {debugInfo.fullUrl}
        </p>
        <p>
          <span className="font-bold">API Base URL:</span>{' '}
          {debugInfo.apiBaseUrl}
        </p>
      </div>
    </div>
  )
}
