import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Bold, Italic, Underline } from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  onBlur,
  placeholder = 'Enter text here...',
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const handleFormatting = (type: 'bold' | 'italic' | 'underline') => {
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    
    let prefix = '';
    let suffix = '';
    let defaultText = '';
    
    switch (type) {
      case 'bold':
        prefix = '**';
        suffix = '**';
        defaultText = 'Bold text';
        break;
      case 'italic':
        prefix = '*';
        suffix = '*';
        defaultText = 'Italic text';
        break;
      case 'underline':
        prefix = '__';
        suffix = '__';
        defaultText = 'Underlined text';
        break;
    }
    
    let newText = '';
    let newCursorPos = 0;
    
    if (start !== end) {
      // Text is selected, wrap it with formatting
      const selectedText = text.substring(start, end);
      newText = text.substring(0, start) + prefix + selectedText + suffix + text.substring(end);
      newCursorPos = end + prefix.length + suffix.length;
    } else {
      // No text selected, insert placeholder
      newText = text.substring(0, start) + prefix + defaultText + suffix + text.substring(end);
      newCursorPos = start + prefix.length;
    }
    
    setEditValue(newText);
    onChange(newText);
    
    // Set focus and selection after state update
    setTimeout(() => {
      textarea.focus();
      if (start !== end) {
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      } else {
        textarea.setSelectionRange(newCursorPos, newCursorPos + defaultText.length);
      }
    }, 0);
  };

  if (isEditing) {
    return (
      <div className="space-y-2">
        <div className="flex items-center space-x-2 mb-2 border-b pb-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handleFormatting('bold')}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handleFormatting('italic')}
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handleFormatting('underline')}
          >
            <Underline className="h-4 w-4" />
          </Button>
        </div>
        <Textarea
          ref={textareaRef}
          id="rich-text-editor"
          value={editValue}
          onChange={(e) => {
            setEditValue(e.target.value);
            onChange(e.target.value);
          }}
          onBlur={() => {
            setIsEditing(false);
            if (onBlur) onBlur();
          }}
          placeholder={placeholder}
          className="min-h-[100px]"
          dir="ltr"
          style={{ direction: 'ltr', textAlign: 'left' }}
        />
      </div>
    );
  }

  return (
    <div 
      className="border rounded-md p-3 min-h-[100px] cursor-text"
      onClick={() => setIsEditing(true)}
    >
      {value ? (
        <ReactMarkdown>{value}</ReactMarkdown>
      ) : (
        <span className="text-muted-foreground">{placeholder}</span>
      )}
    </div>
  );
};

export default RichTextEditor;
