import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import {
  Send,
  X,
  Lightbulb,
  Bold,
  Minimize2,
  BookO<PERSON>,
  <PERSON>rk<PERSON>,
} from 'lucide-react'
import { FlashcardDeckType, Flashcard } from './FlashcardTypes'

interface FlashcardAIPrompt {
  icon: React.ReactNode
  text: string
  action: (deck: FlashcardDeckType, card?: Flashcard) => void
}

interface FlashcardAIChatProps {
  deck: FlashcardDeckType | null
  currentCard?: Flashcard
  onUpdateDeck: (updatedDeck: FlashcardDeckType) => void
  onClose: () => void
}

export default function FlashcardAIChat({
  deck,

  onClose,
}: FlashcardAIChatProps) {
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<
    { role: 'user' | 'assistant'; content: string }[]
  >([])
  const [isMinimized, setIsMinimized] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Predefined prompts
  const prompts: FlashcardAIPrompt[] = [
    {
      icon: <BookOpen className="h-4 w-4" />,
      text: 'Create similar flashcards',
      action: (deck) => {
        if (!deck) return
        handleSendMessage(
          'Create 3 more flashcards similar to the ones in my deck.'
        )
      },
    },
    {
      icon: <Minimize2 className="h-4 w-4" />,
      text: 'Make them more concise',
      action: (deck) => {
        if (!deck) return
        handleSendMessage('Make my flashcards more concise and to the point.')
      },
    },
    {
      icon: <Bold className="h-4 w-4" />,
      text: 'Make keywords bold',
      action: (deck) => {
        if (!deck) return
        handleSendMessage(
          'Identify and make important keywords bold in my flashcards.'
        )
      },
    },
    {
      icon: <Sparkles className="h-4 w-4" />,
      text: 'Reduce number of cards',
      action: (deck) => {
        if (!deck) return
        handleSendMessage(
          'Combine similar flashcards to reduce the total number.'
        )
      },
    },
  ]

  // Handle sending a message
  const handleSendMessage = (text: string = message) => {
    if (!text.trim()) return

    // Add user message
    const userMessage = { role: 'user' as const, content: text }
    setMessages((prev) => [...prev, userMessage])

    // Clear input
    setMessage('')

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      let response = ''

      if (text.includes('similar flashcards')) {
        response =
          'Here are 3 more flashcards similar to yours:\n\n1. Front: What is the capital of France?\nBack: Paris\n\n2. Front: What is the largest ocean?\nBack: Pacific Ocean\n\n3. Front: Who wrote Romeo and Juliet?\nBack: William Shakespeare'
      } else if (text.includes('more concise')) {
        response =
          "I've analyzed your flashcards and here are more concise versions:\n\n- Remove unnecessary articles and prepositions\n- Focus on key terms and definitions\n- Use bullet points instead of full sentences\n- Limit each card to one main concept"
      } else if (text.includes('keywords bold')) {
        response =
          "I've identified these important keywords that should be bold:\n\n- **Key terms** at the beginning of definitions\n- **Dates** and **numbers** in historical or scientific cards\n- **Names** of important people, places, or concepts\n- **Technical terms** specific to the subject"
      } else if (text.includes('reduce')) {
        response =
          'To reduce the number of cards, consider combining these related concepts:\n\n- Cards about similar historical events\n- Definition cards that relate to the same broader concept\n- Cards with overlapping information\n- Sequential process cards into a single card with steps'
      } else {
        response =
          "I'm your flashcard assistant! I can help you create better flashcards, make them more concise, highlight important information, or optimize your deck. What would you like help with?"
      }

      setMessages((prev) => [...prev, { role: 'assistant', content: response }])
    }, 1000)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSendMessage()
  }

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full h-12 w-12 flex items-center justify-center bg-primary text-white"
        >
          <Lightbulb className="h-6 w-6" />
        </Button>
      </div>
    )
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 md:w-96 shadow-lg z-50 overflow-hidden">
      <div className="bg-primary text-white p-3 flex justify-between items-center">
        <div className="flex items-center">
          <Lightbulb className="h-5 w-5 mr-2" />
          <h3 className="font-medium">Chat with AI</h3>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-white hover:bg-primary-foreground/20"
            onClick={() => setIsMinimized(true)}
          >
            <Minimize2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-white hover:bg-primary-foreground/20"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <CardContent className="p-0">
        <div className="flex flex-col h-96">
          {/* Messages area */}
          <div className="flex-1 overflow-y-auto p-3 space-y-3">
            {messages.length === 0 ? (
              <div className="text-center text-muted-foreground py-4">
                <Lightbulb className="h-8 w-8 mx-auto mb-2 text-primary/50" />
                <p>Ask me anything about your flashcards!</p>
              </div>
            ) : (
              messages.map((msg, index) => (
                <div
                  key={index}
                  className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      msg.role === 'user' ? 'bg-primary text-white' : 'bg-muted'
                    }`}
                  >
                    <p className="whitespace-pre-wrap text-sm">{msg.content}</p>
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Suggestions */}
          {messages.length === 0 && (
            <div className="p-3 border-t">
              <h4 className="text-sm font-medium mb-2">Suggestions</h4>
              <div className="grid grid-cols-1 gap-2">
                {prompts.map((prompt, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="justify-start h-auto py-2 text-left"
                    onClick={() => prompt.action(deck!)}
                  >
                    <div className="flex items-center">
                      {prompt.icon}
                      <span className="ml-2 text-sm">{prompt.text}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Input area */}
          <form onSubmit={handleSubmit} className="p-3 border-t flex space-x-2">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Ask about your flashcards..."
              className="flex-1"
            />
            <Button type="submit" size="icon" disabled={!message.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  )
}
