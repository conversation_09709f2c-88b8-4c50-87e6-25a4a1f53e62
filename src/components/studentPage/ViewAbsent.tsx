import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'

// Import your interfaces
import { MockStudentSubject, Absent } from '@/interface/StudentInteface'
import { FlexibleId } from '@/interface/types/SharedTypes'

// Define a mock student interface
interface MockStudent {
  id: FlexibleId
  firstname: string
  lastname?: string
  fullname?: string
  email: string
  classId?: string
  subjects: MockStudentSubject[]
}

// Hardcoded student data grouped by subject
const studentData: MockStudent = {
  id: '101', // Use string to ensure type compatibility
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  fullname: '<PERSON>',
  email: '<EMAIL>',
  classId: '10th Grade',
  subjects: [
    {
      subjectId: '1', // Use string to ensure type compatibility
      absences: 5,
      isEliminated: false,
      grades: [], // Add required field
    },
    {
      subjectId: '2', // Use string to ensure type compatibility
      absences: 3,
      isEliminated: false,
      grades: [], // Add required field
    },
  ],
}

// Hardcoded absence data for each subject
const absenceData: Absent[] = [
  {
    id: '1', // Use string to ensure type compatibility
    studentId: '101',
    subject: 'Mathematics',
    date: '2024-12-25',
    reason: 'Sick Leave',
    status: 'approved',
  },
  {
    id: '2', // Use string to ensure type compatibility
    studentId: '101',
    subject: 'Mathematics',
    date: '2024-12-20',
    reason: 'Family Event',
    status: 'approved',
  },
  {
    id: '3', // Use string to ensure type compatibility
    studentId: '101',
    subject: 'Physics',
    date: '2024-12-18',
    reason: 'Sick Leave',
    status: 'approved',
  },
]

// Sample subject mapping
const subjectMap = {
  '1': 'Mathematics',
  '2': 'Physics',
}

export default function ViewAbsent() {
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false)
  const [absenceDetails, setAbsenceDetails] = useState<Absent[]>([])

  // Update the getSubjectName function to properly handle undefined values
  const getSubjectName = (subjectId: FlexibleId | undefined): string => {
    if (subjectId === undefined) return 'Unknown Subject'

    // Look up subject name using subject map
    return (
      subjectMap[String(subjectId) as keyof typeof subjectMap] ||
      'Unknown Subject'
    )
  }

  // Update the handleViewDetails function to handle undefined values
  const handleViewDetails = (subjectId: FlexibleId | undefined) => {
    if (subjectId === undefined) return

    // Convert subjectId to string for consistent comparison
    const subjectIdStr = String(subjectId)

    // Get the subject name from our mapping
    const subjectName =
      subjectMap[subjectIdStr as keyof typeof subjectMap] || 'Unknown'

    // Filter absences by subject name
    const subjectAbsences = absenceData.filter(
      (absence) => absence.subject === subjectName
    )

    setAbsenceDetails(subjectAbsences)
    setSelectedSubject(subjectName)
    setIsDialogOpen(true)
  }

  const handleAbsentRequest = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    // In a real app, you would submit the form data to an API
    alert('Absence request submitted successfully! Awaiting approval.')
    setIsRequestDialogOpen(false)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center">My Absence Record</h1>
        <div className="mt-4 text-center">
          <p className="text-lg">Student Name: {studentData.fullname}</p>
          <p className="text-gray-600">Student ID: {studentData.id}</p>
        </div>
        <Button onClick={() => setIsRequestDialogOpen(true)} className="mt-4">
          Request Absence
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              <TableHead>Total Absences</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {studentData.subjects?.map((subject) => {
              if ('absences' in subject) {
                return (
                  <TableRow key={subject.subjectId}>
                    <TableCell className="font-medium">
                      {getSubjectName(subject.subjectId)}
                    </TableCell>
                    <TableCell>
                      {typeof subject.absences === 'number'
                        ? `${subject.absences} lessons`
                        : Array.isArray(subject.absences)
                          ? `${subject.absences.length} lessons`
                          : '0 lessons'}
                    </TableCell>
                    <TableCell>
                      <Button
                        onClick={() =>
                          subject.subjectId !== undefined
                            ? handleViewDetails(subject.subjectId)
                            : null
                        }
                        variant="outline"
                        size="sm"
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                )
              }
              return null // Skip rendering for items that are not StudentSubject
            })}
          </TableBody>
        </Table>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedSubject} Absence Details</DialogTitle>
          </DialogHeader>

          {selectedSubject && (
            <div className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Reason</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {absenceDetails.map((absence) => (
                    <TableRow key={absence.id}>
                      <TableCell>{absence.date}</TableCell>
                      <TableCell>{absence.reason}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isRequestDialogOpen} onOpenChange={setIsRequestDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Request Absence</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAbsentRequest}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="date" className="text-right">
                  Date
                </Label>
                <Input id="date" type="date" className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reason" className="text-right">
                  Reason
                </Label>
                <Textarea id="reason" className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file" className="text-right">
                  File
                </Label>
                <Input id="file" type="file" className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">Submit Request</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
