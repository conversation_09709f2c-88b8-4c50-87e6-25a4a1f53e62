import {
  Flashcard as ApiFlashcard,
  FlashcardDeck as ApiFlashcardDeck,
} from '@/services/flashcardService'
// We're extending the API types with our own UI-specific types

// Define the front and back types to make them compatible
export interface CardSide {
  text?: string
  image?: string
}

// Export the types from the service
export type FlashcardDeckType = ApiFlashcardDeck
export interface Flashcard extends ApiFlashcard {
  front?: CardSide
  back?: CardSide
}

// Add adapter functions to convert between formats
export const adaptCardForUI = (card: any): Flashcard => {
  // If the card already has front/back properties, return it as is
  if (card.front && card.back) {
    return card as Flashcard
  }

  // Handle temporary cards that might be missing some properties
  const adaptedCard: Flashcard = {
    id: card.id || `temp-${Date.now()}`,
    frontText: card.frontText || '',
    backText: card.backText || '',
    frontImage: card.frontImage || null,
    backImage: card.backImage || null,
    deckId: card.deckId || '',
    order: card.order || 0,
    createdAt: card.createdAt || new Date().toISOString(),
    updatedAt: card.updatedAt || new Date().toISOString(),
    front: {
      text: card.frontText || '',
      image: card.frontImage || null,
    },
    back: {
      text: card.backText || '',
      image: card.backImage || null,
    },
  }

  return adaptedCard
}

export const adaptDeckForUI = (deck: FlashcardDeckType): FlashcardDeckType => {
  return {
    ...deck,
    cards: Array.isArray(deck.cards) ? deck.cards.map(adaptCardForUI) : [],
  }
}
