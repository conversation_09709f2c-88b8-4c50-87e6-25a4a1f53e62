import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  ArrowLeft,
  ArrowRight,
  RotateCw,
  Check,
  X,
  Shuffle,
  RefreshCw,
} from 'lucide-react'
import { FlashcardDeckType, Flashcard, adaptCardForUI } from './FlashcardTypes'
import { Progress } from '@/components/ui/progress'
import './Flashcard.css'
import { formatText } from './formatText'

interface FlashcardStudyProps {
  deck: FlashcardDeckType
  onBack: () => void
}

export default function FlashcardStudy({ deck, onBack }: FlashcardStudyProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [showingFront, setShowingFront] = useState(true)
  const [studyCards, setStudyCards] = useState<Flashcard[]>(
    [...(deck.cards || [])].map((card) => adaptCardForUI(card))
  )
  const [knownCards, setKnownCards] = useState<string[]>([])
  const [unknownCards, setUnknownCards] = useState<string[]>([])
  const [studyComplete, setStudyComplete] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentCardStatus, setCurrentCardStatus] = useState<
    'known' | 'unknown' | null
  >(null)

  // Calculate progress
  useEffect(() => {
    if (studyCards.length === 0) {
      setProgress(100)
      return
    }

    const totalResponses = knownCards.length + unknownCards.length
    const totalCards = deck.cards?.length || 0
    const progressValue =
      totalCards > 0 ? (totalResponses / totalCards) * 100 : 0
    setProgress(progressValue)
  }, [knownCards, unknownCards, studyCards.length, deck.cards?.length])

  // Check if study is complete
  useEffect(() => {
    if (currentIndex >= studyCards.length && studyCards.length > 0) {
      setStudyComplete(true)
    }
  }, [currentIndex, studyCards.length])

  // Get current card
  const currentCard = studyCards[currentIndex]

  // Handle card flip
  const handleFlip = () => {
    setShowingFront(!showingFront)

    // If we're flipping to the front and the card is already marked, reset the status
    if (!showingFront && currentCardStatus) {
      // Don't reset the status, keep it visible on both sides
    }
  }

  // Move to next card
  const handleNext = () => {
    setShowingFront(true)
    setCurrentIndex(currentIndex + 1)
    setCurrentCardStatus(null) // Reset the card status
  }

  // Move to previous card
  const handlePrevious = () => {
    if (currentIndex > 0) {
      setShowingFront(true)
      setCurrentIndex(currentIndex - 1)
      setCurrentCardStatus(null) // Reset the card status
    }
  }

  // Mark card as known
  const handleKnown = () => {
    if (currentCard) {
      // If we're showing the front, flip to show the back first
      if (showingFront) {
        setShowingFront(false)
        // Add to known cards but don't move to next card yet
        setKnownCards([...knownCards, currentCard.id])
        // Set the current card status
        setCurrentCardStatus('known')
      } else {
        // If we're already showing the back, move to the next card
        handleNext()
      }
    }
  }

  // Mark card as unknown
  const handleUnknown = () => {
    if (currentCard) {
      // If we're showing the front, flip to show the back first
      if (showingFront) {
        setShowingFront(false)
        // Add to unknown cards but don't move to next card yet
        setUnknownCards([...unknownCards, currentCard.id])
        // Set the current card status
        setCurrentCardStatus('unknown')
      } else {
        // If we're already showing the back, move to the next card
        handleNext()
      }
    }
  }

  // Restart study session
  const handleRestart = () => {
    setCurrentIndex(0)
    setShowingFront(true)
    setKnownCards([])
    setUnknownCards([])
    setStudyComplete(false)
  }

  // Shuffle cards
  const handleShuffle = () => {
    const shuffled = [...studyCards]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    setStudyCards(shuffled.map((card) => adaptCardForUI(card)))
    setCurrentIndex(0)
    setShowingFront(true)
  }

  // Study only unknown cards
  const handleStudyUnknown = () => {
    const unknownCardsToStudy = studyCards
      .filter((card) => unknownCards.includes(card.id))
      .map((card) => adaptCardForUI(card))
    setStudyCards(unknownCardsToStudy)
    setCurrentIndex(0)
    setShowingFront(true)
    setKnownCards([])
    setUnknownCards([])
    setStudyComplete(false)
  }

  // Render study complete screen
  const renderStudyComplete = () => (
    <Card className="max-w-md mx-auto">
      <CardContent className="pt-6 pb-6">
        <div className="text-center space-y-6">
          <div className="rounded-full bg-primary/10 p-3 w-16 h-16 mx-auto flex items-center justify-center">
            <Check className="h-8 w-8 text-primary" />
          </div>

          <h2 className="text-2xl font-bold">Study Complete!</h2>

          <div className="space-y-2">
            <p>
              <span className="font-medium">Total Cards:</span>{' '}
              {deck.cards?.length || 0}
            </p>
            <p>
              <span className="font-medium">Known:</span> {knownCards.length} (
              {deck.cards?.length
                ? Math.round((knownCards.length / deck.cards.length) * 100)
                : 0}
              %)
            </p>
            <p>
              <span className="font-medium">Need Review:</span>{' '}
              {unknownCards.length} (
              {deck.cards?.length
                ? Math.round((unknownCards.length / deck.cards.length) * 100)
                : 0}
              %)
            </p>
          </div>

          <div className="flex flex-col space-y-2">
            <Button onClick={handleRestart}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Restart with All Cards
            </Button>

            {unknownCards.length > 0 && (
              <Button variant="outline" onClick={handleStudyUnknown}>
                <RotateCw className="mr-2 h-4 w-4" />
                Study Unknown Cards
              </Button>
            )}

            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Deck
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // Render empty deck message
  const renderEmptyDeck = () => (
    <Card className="max-w-md mx-auto">
      <CardContent className="pt-6 pb-6">
        <div className="text-center space-y-6">
          <h2 className="text-xl font-bold">No Cards to Study</h2>
          <p className="text-muted-foreground">
            This deck doesn't have any flashcards yet. Add some cards to start
            studying.
          </p>
          <Button onClick={onBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Deck
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  // Render flashcard
  const renderFlashcard = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex justify-between items-center mb-4">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Deck
        </Button>

        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handleShuffle}>
            <Shuffle className="mr-2 h-4 w-4" />
            Shuffle
          </Button>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>
            Card {currentIndex + 1} of {studyCards.length}
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <div
        className="relative w-full aspect-[3/2] perspective-1000 cursor-pointer mb-6"
        onClick={handleFlip}
      >
        <div
          className={`absolute w-full h-full transition-transform duration-500 transform-style-3d ${
            showingFront ? '' : 'rotate-y-180'
          }`}
        >
          {/* Front of card */}
          <div className="absolute w-full h-full backface-hidden bg-white border rounded-xl shadow-md p-6 flex flex-col items-center justify-center">
            {currentCard?.front?.image && (
              <div className="mb-4 max-h-40 flex items-center justify-center">
                <img
                  src={currentCard.front?.image}
                  alt="Front"
                  className="max-h-40 max-w-full object-contain"
                />
              </div>
            )}
            <div
              className="text-center text-xl font-medium"
              dangerouslySetInnerHTML={{
                __html: formatText(currentCard?.front?.text || ''),
              }}
            />
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
              Click to flip
            </div>
            {currentCardStatus && (
              <div
                className={`absolute top-2 right-2 rounded-full p-1 ${currentCardStatus === 'known' ? 'bg-green-100' : 'bg-red-100'}`}
              >
                {currentCardStatus === 'known' ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <X className="h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>

          {/* Back of card */}
          <div className="absolute w-full h-full backface-hidden bg-white border rounded-xl shadow-md p-6 flex flex-col items-center justify-center rotate-y-180">
            {currentCard?.back?.image && (
              <div className="mb-4 max-h-40 flex items-center justify-center">
                <img
                  src={currentCard.back?.image}
                  alt="Back"
                  className="max-h-40 max-w-full object-contain"
                />
              </div>
            )}
            <div
              className="text-center text-xl font-medium"
              dangerouslySetInnerHTML={{
                __html: formatText(currentCard?.back?.text || ''),
              }}
            />
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
              Click to flip
            </div>
            {currentCardStatus && (
              <div
                className={`absolute top-2 right-2 rounded-full p-1 ${currentCardStatus === 'known' ? 'bg-green-100' : 'bg-red-100'}`}
              >
                {currentCardStatus === 'known' ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <X className="h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentIndex === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="bg-red-50 hover:bg-red-100 border-red-200 text-red-600"
            onClick={handleUnknown}
          >
            <X className="mr-2 h-4 w-4 text-red-500" />
            {showingFront ? "Don't Know" : 'Next Card'}
          </Button>

          <Button
            variant="outline"
            className="bg-green-50 hover:bg-green-100 border-green-200 text-green-600"
            onClick={handleKnown}
          >
            <Check className="mr-2 h-4 w-4 text-green-500" />
            {showingFront ? 'Know' : 'Next Card'}
          </Button>
        </div>

        {showingFront && (
          <Button variant="outline" onClick={handleNext}>
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )

  // Main render
  return (
    <div className="container mx-auto p-6">
      {studyCards.length === 0
        ? renderEmptyDeck()
        : studyComplete
          ? renderStudyComplete()
          : renderFlashcard()}
    </div>
  )
}
