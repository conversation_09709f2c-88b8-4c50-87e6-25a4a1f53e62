import React, { useEffect, useState } from 'react'
import { TimetableAssignment } from '../../types/timetable'
import { timetableService } from '../../lib/api/services/timetable-service'
import { useCurrentStudent } from '@/hooks/useCurrentStudent'

// Define the response type for the student timetable API
interface StudentTimetableResponse {
  scheduleData: Array<{
    class: string
    [day: string]: any[] | string // Can be either an array of assignments or the class name
  }>
}

const DAYS_ORDER = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
]

const StudentTimetable: React.FC = () => {
  const { isLoading: studentLoading, error: studentError } = useCurrentStudent()
  const [data, setData] = useState<StudentTimetableResponse | null>(null)
  const [processedData, setProcessedData] = useState<Record<string, TimetableAssignment[]>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [className, setClassName] = useState<string | null>(null)

  useEffect(() => {
    const fetchStudentTimetable = async () => {
      setLoading(true)
      try {
        // Get the student timetable using the timetable service
        const response = await timetableService.getStudentTimetable()
        console.log('Student timetable data:', response)
        setData(response)

        // Process the data for display
        if (response && response.scheduleData && response.scheduleData.length > 0) {
          const classData = response.scheduleData[0]
          setClassName(classData.class)

          // Create a processed data structure organized by day
          const processed: Record<string, TimetableAssignment[]> = {}

          // Process each day's data
          DAYS_ORDER.forEach(day => {
            if (Array.isArray(classData[day])) {
              processed[day] = classData[day].map((lesson: any) => ({
                subject: lesson.subject || '',
                teacher: lesson.teacher || '',
                teacherID: lesson.teacherID || lesson.teacherId || '',
                salle: lesson.salle || lesson.classroomId || '',
                time: lesson.time || lesson.timeSlot || '',
                day: lesson.day || day,
                class: lesson.className || classData.class || '',
                group1: lesson.group1,
                group2: lesson.group2
              }));
            } else {
              processed[day] = [];
            }
          });

          setProcessedData(processed)
        }

        setLoading(false)
        setError(null)
      } catch (err) {
        console.error('Error fetching student timetable:', err)
        setError('Failed to load timetable. Please try again later.')
        setLoading(false)
      }
    }

    fetchStudentTimetable()
  }, [])

  // Get current day name
  const today = new Date()
  const todayName = DAYS_ORDER[today.getDay()]

  if (studentLoading || loading) return <div className="p-4">Loading timetable...</div>
  if (studentError) return <div className="p-4 text-red-500">Failed to load student info.</div>
  if (error) return <div className="p-4 text-red-500">{error}</div>
  if (!data) return <div className="p-4">No timetable data available. Please contact your administrator.</div>
  if (!data.scheduleData || !Array.isArray(data.scheduleData) || data.scheduleData.length === 0) {
    return <div className="p-4">No schedule data found in your timetable. Please contact your administrator.</div>
  }

  return (
    <div className="p-4">
      <button
        onClick={() => window.history.back()}
        className="mb-4 flex items-center gap-2 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded shadow transition"
        aria-label="Back"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            d="M15 19l-7-7 7-7"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        Back
      </button>
      <h2 className="text-2xl font-extrabold mb-6 text-blue-700 flex items-center gap-2">
        <svg
          className="w-7 h-7 text-blue-500"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <rect
            x="3"
            y="4"
            width="18"
            height="18"
            rx="2"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
          />
          <path
            d="M16 2v4M8 2v4M3 10h18"
            stroke="currentColor"
            strokeWidth="2"
          />
        </svg>
        Timetable for <span className="ml-1 text-blue-900">{className}</span>
      </h2>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300 shadow-lg rounded-lg overflow-hidden bg-white">
          <thead>
            <tr>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Day
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Time
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Subject
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Teacher
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Room
              </th>
            </tr>
          </thead>
          <tbody>
            {DAYS_ORDER.map((day) => {
              const lessons = processedData[day] || []
              const isToday = day === todayName
              if (!lessons.length) {
                return (
                  <tr
                    key={day}
                    className={isToday ? 'bg-yellow-50' : 'bg-gray-50'}
                  >
                    <td
                      className={`border px-3 py-2 font-semibold ${isToday ? 'text-yellow-700' : 'text-gray-700'}`}
                    >
                      {day}
                    </td>
                    <td className="border px-3 py-2 text-gray-400" colSpan={4}>
                      No lessons
                    </td>
                  </tr>
                )
              }
              return lessons.map((lesson, idx) => (
                <tr
                  key={day + idx}
                  className={
                    isToday
                      ? 'bg-yellow-50'
                      : idx % 2 === 0
                        ? 'bg-white'
                        : 'bg-gray-50'
                  }
                >
                  {idx === 0 && (
                    <td
                      className={`border px-3 py-2 font-semibold ${isToday ? 'text-yellow-700' : 'text-gray-700'}`}
                      rowSpan={lessons.length}
                    >
                      {day}
                    </td>
                  )}
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-blue-50 text-blue-700 rounded px-2 py-0.5 font-mono text-sm">
                      {lesson.time}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-green-100 text-green-800 rounded px-2 py-0.5 font-semibold">
                      {lesson.subject}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-purple-100 text-purple-800 rounded px-2 py-0.5">
                      {lesson.teacher}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-gray-200 text-gray-700 rounded px-2 py-0.5">
                      {lesson.salle}
                    </span>
                  </td>
                </tr>
              ))
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default StudentTimetable
