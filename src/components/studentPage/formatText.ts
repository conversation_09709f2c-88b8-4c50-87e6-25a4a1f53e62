// Helper function to format text with markdown-like syntax
export function formatText(text: string): string {
  if (!text) return ''

  // Replace **text** with <strong>text</strong> (bold)
  let formattedText = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

  // Replace *text* with <em>text</em> (italic)
  formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>')

  // Replace __text__ with <u>text</u> (underline)
  formattedText = formattedText.replace(/__(.*?)__/g, '<u>$1</u>')

  return formattedText
}

// Helper function to ensure image URLs are properly formatted
export function formatImageUrl(url: string | null | undefined): string {
  if (!url) return ''

  // If it's already a proxy URL, don't proxy it again
  if (url.includes('/health/proxy/image')) {
    return url
  }

  // If it's a CDN URL, use the health proxy
  if (url.includes('cdn.jeridschool.tech')) {
    return `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(url)}`
  }

  // If URL doesn't start with http/https, add https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }

  // Convert http:// to https://
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://')
  }

  return url
}
