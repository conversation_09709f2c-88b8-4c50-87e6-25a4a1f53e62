import { useState, useEffect } from 'react'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  PlusCircle,
  BookOpen,
  Edit3,
  Trash2,
  MessageSquare,
  AlertCircle,
  Sparkles,
} from 'lucide-react'
import FlashcardDeck from './FlashcardDeck'
import FlashcardStudy from './FlashcardStudy'
import FlashcardAIChat from './FlashcardAIChat'
import FlashcardAIGenerator from './FlashcardAIGenerator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useFlashcards } from '@/hooks/useFlashcards'
import { CreateDeckFormValues } from '@/schemas/flashcardSchemas'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zod<PERSON><PERSON><PERSON><PERSON> } from '@hookform/resolvers/zod'
import { createDeckSchema } from '@/schemas/flashcardSchemas'
import { FlashcardDeck as FlashcardDeckType } from '@/services/flashcardService'
import { useFlashcardAdapter } from '@/hooks/useFlashcardAdapter'
import { adaptDeckForUI } from './FlashcardTypes'
import { useQueryClient } from '@tanstack/react-query'

// Main component
export default function FlashcardGenerator() {
  const [activeDeck, setActiveDeck] = useState<FlashcardDeckType | null>(null)
  const [isCreatingDeck, setIsCreatingDeck] = useState(false)
  const [mode, setMode] = useState<'list' | 'edit' | 'study'>('list')
  const [showAIChat, setShowAIChat] = useState(false)
  const [showAIGenerator, setShowAIGenerator] = useState(false)

  // Get query client for direct cache updates
  const queryClient = useQueryClient()

  // Get adapter
  const { ensureCardsArray } = useFlashcardAdapter()

  // Get flashcard hooks
  const {
    useDecks,
    useCreateDeck,
    useUpdateDeck,
    useDeleteDeck,
    useMigrateFromLocalStorage,
  } = useFlashcards()

  // Query hooks
  const { data: rawDecks = [], isLoading, error } = useDecks()

  // Adapt decks for UI
  const decks = rawDecks.map(adaptDeckForUI)
  const createDeckMutation = useCreateDeck()
  const updateDeckMutation = useUpdateDeck(activeDeck?.id || '')
  const deleteDeckMutation = useDeleteDeck()
  const migrateFromLocalStorageMutation = useMigrateFromLocalStorage()

  // Form for creating a new deck
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateDeckFormValues>({
    resolver: zodResolver(createDeckSchema),
    defaultValues: {
      name: '',
      visibility: 'private',
    },
  })

  // Silently migrate localStorage data if it exists
  useEffect(() => {
    const migrateLocalStorage = async () => {
      const savedDecks = localStorage.getItem('flashcard_decks')
      if (savedDecks) {
        try {
          const localDecks = JSON.parse(savedDecks)
          if (localDecks.length > 0) {
            // Silently migrate without asking
            migrateFromLocalStorageMutation.mutate()
          }
        } catch (error) {
          console.error('Error parsing localStorage flashcards:', error)
        }
      }
    }

    migrateLocalStorage()
  }, [])

  // Create a new deck
  const handleCreateDeck = (data: CreateDeckFormValues) => {
    createDeckMutation.mutate(data, {
      onSuccess: (newDeck) => {
        // Ensure the deck has a cards array and adapt it for UI
        const deckWithCards = adaptDeckForUI(
          ensureCardsArray(newDeck) as FlashcardDeckType
        )
        setActiveDeck(deckWithCards)
        setMode('edit')
        setIsCreatingDeck(false)
        reset()
      },
    })
  }

  // Delete a deck
  const handleDeleteDeck = (deckId: string) => {
    if (window.confirm('Are you sure you want to delete this deck?')) {
      deleteDeckMutation.mutate(deckId, {
        onSuccess: () => {
          if (activeDeck?.id === deckId) {
            setActiveDeck(null)
            setMode('list')
          }
        },
      })
    }
  }

  // Update a deck
  const handleUpdateDeck = (updatedDeck: FlashcardDeckType) => {
    // Make sure the deck is properly adapted for UI
    const adaptedDeck = adaptDeckForUI(updatedDeck)

    console.log('Updating deck with adapted data:', adaptedDeck)

    // Update the active deck in the state first
    setActiveDeck(adaptedDeck)

    // Update the deck in the decks array to ensure the left panel is updated
    const updatedDecks = decks.map((deck) =>
      deck.id === adaptedDeck.id
        ? {
            ...adaptedDeck,
            // Make sure the cardCount is updated to reflect the actual number of cards
            cardCount: adaptedDeck.cards?.length || 0,
          }
        : deck
    )

    // Force a re-render of the deck list
    queryClient.setQueryData(['flashcards', 'decks'], updatedDecks)

    // Invalidate the decks query to ensure we get fresh data from the server
    queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })

    // Then send the update to the server (only name and visibility)
    updateDeckMutation.mutate({
      name: updatedDeck.name,
      visibility: updatedDeck.visibility,
    })
  }

  // Render the deck creation form
  const renderDeckCreationForm = () => (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>New Flashcard Deck</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleCreateDeck)} className="space-y-4">
          <div>
            <label
              htmlFor="deck-name"
              className="block text-sm font-medium mb-1"
            >
              Name
            </label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  id="deck-name"
                  placeholder="Flashcard deck name"
                  {...field}
                  className={errors.name ? 'border-red-500' : ''}
                />
              )}
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="deck-visibility"
              className="block text-sm font-medium mb-1"
            >
              Visibility
            </label>
            <Controller
              name="visibility"
              control={control}
              render={({ field }) => (
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private">Private</SelectItem>
                    <SelectItem value="public">Public</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.visibility && (
              <p className="text-red-500 text-xs mt-1">
                {errors.visibility.message}
              </p>
            )}
          </div>

          <div className="pt-4 flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreatingDeck(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={createDeckMutation.isPending}>
              {createDeckMutation.isPending ? 'Creating...' : 'Create'}
            </Button>
          </div>

          <p className="text-sm text-muted-foreground text-center">
            You can create up to 3 decks with a maximum of 50 cards each.
          </p>
        </form>
      </CardContent>
    </Card>
  )

  // Render the deck list
  const renderDeckList = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">My Flashcard Decks</h1>
        <Button
          onClick={() => setIsCreatingDeck(true)}
          disabled={decks.length >= 3}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          New Deck
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error instanceof Error && error.message.includes('401')
              ? "Authentication error. Please log out and log back in to refresh your session."
              : "Error loading flashcard decks. Please try again later."}
          </AlertDescription>
          <div className="mt-4">
            <Button
              variant="outline"
              onClick={() => {
                // Force refresh the token by logging out
                localStorage.removeItem('access_token');
                window.location.href = '/login';
              }}
            >
              Log Out and Refresh Session
            </Button>
          </div>
        </Alert>
      ) : decks.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-primary/10 p-3">
              <BookOpen className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">No flashcard decks yet</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Create your first flashcard deck to start studying. You can add
              text and images to both sides of your cards.
            </p>
            <Button
              onClick={() => setIsCreatingDeck(true)}
              disabled={decks.length >= 3}
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Your First Deck
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {decks.map((deck) => (
            <Card key={deck.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{deck.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-muted-foreground mb-4">
                  {deck.cardCount} cards • Last updated{' '}
                  {new Date(deck.updatedAt).toLocaleDateString()}
                  <div className="mt-1">
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${deck.visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}
                    >
                      {deck.visibility === 'public' ? 'Public' : 'Private'}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setActiveDeck(
                        adaptDeckForUI(
                          ensureCardsArray(deck) as FlashcardDeckType
                        )
                      )
                      setMode('edit')
                    }}
                  >
                    <Edit3 className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setActiveDeck(
                        adaptDeckForUI(
                          ensureCardsArray(deck) as FlashcardDeckType
                        )
                      )
                      setMode('study')
                    }}
                    disabled={deck.cardCount === 0}
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Study
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteDeck(deck.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {decks.length >= 3 && (
        <Alert className="bg-yellow-50 border-yellow-200">
          <AlertCircle className="h-4 w-4 text-yellow-800" />
          <AlertDescription className="text-yellow-800">
            You have reached the maximum limit of 3 flashcard decks. Delete an
            existing deck to create a new one.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )

  // Main render
  console.log('FlashcardGenerator rendering, mode:', mode)
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="container mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-primary">Flashcards</h1>
          {(mode === 'edit' || mode === 'study') && activeDeck && (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIGenerator(true)}
                className="flex items-center gap-2"
              >
                <Sparkles className="h-4 w-4" />
                Generate Cards
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIChat(!showAIChat)}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                AI Assistant
              </Button>
            </div>
          )}
        </div>

        {isCreatingDeck ? (
          renderDeckCreationForm()
        ) : mode === 'list' ? (
          renderDeckList()
        ) : mode === 'edit' && activeDeck ? (
          <FlashcardDeck
            deck={activeDeck}
            onUpdate={handleUpdateDeck}
            onBack={() => {
              setMode('list')
              setActiveDeck(null)
            }}
            onStudy={() => setMode('study')}
          />
        ) : mode === 'study' && activeDeck ? (
          <FlashcardStudy deck={activeDeck} onBack={() => setMode('edit')} />
        ) : (
          renderDeckList()
        )}

        {showAIChat && activeDeck && (
          <FlashcardAIChat
            deck={activeDeck}
            onUpdateDeck={handleUpdateDeck}
            onClose={() => setShowAIChat(false)}
          />
        )}

        {showAIGenerator && activeDeck && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="w-full max-w-2xl">
              <FlashcardAIGenerator
                deck={activeDeck}
                onUpdateDeck={handleUpdateDeck}
                onClose={() => setShowAIGenerator(false)}
              />
            </div>
          </div>
        )}
      </div>
    </DndProvider>
  )
}
