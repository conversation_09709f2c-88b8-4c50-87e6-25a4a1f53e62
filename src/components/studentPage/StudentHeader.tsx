import React from 'react'

interface StudentHeaderProps {
  onLogout: () => void
}

const StudentHeader: React.FC<StudentHeaderProps> = ({}) => {
  return (
    <header className="bg-white shadow-sm mb-6 p-4">
      <div className="container mx-auto flex justify-between items-center">
        <div className="text-xl font-bold">
          <span className="text-[#525FE1]">Student</span> Dashboard
        </div>
      </div>
    </header>
  )
}

export default StudentHeader
