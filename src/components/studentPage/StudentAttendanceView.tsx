import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import {
  Calendar,
  RefreshCw,
  Users,
  GraduationCap,
  BookOpen,
} from 'lucide-react'
import {
  AttendanceStatus,
  Attendance,
} from '@/lib/api/services/attendance-service'
// import { studentService } from '@/lib/api/services/student-service'
import { api } from '@/lib/api/axios-instance'
import { useToast } from '@/components/ui/use-toast'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { useAttendance } from '@/hooks/useAttendance'
import { useQuery } from '@tanstack/react-query'

// Define interfaces for student and class data
// interface StudentData {
//   id: string
//   firstname: string
//   lastname: string
//   classId: string
//   class?: ClassData
//   isPaid?: boolean
//}

interface ClassData {
  id: string
  name: string
  gradeId: string
  grade?: {
    id: string
    name: string
  }
  students?: any[]
  teachers?: TeacherData[]
}

interface TeacherData {
  id: string
  firstname: string
  lastname: string
  email: string
  subjects?: string[]
}

interface TeacherSubject {
  teacherId: string
  teacherName: string
  subjectName: string
}

export default function StudentAttendanceView() {
  const [filteredRecords, setFilteredRecords] = useState<Attendance[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedRecord, setSelectedRecord] = useState<Attendance | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [teacherSubjects, setTeacherSubjects] = useState<TeacherSubject[]>([])
  const { toast } = useToast()

  // Get student ID from localStorage
  const studentId = localStorage.getItem('id') || ''

  // Date range for filtering
  const [selectedDateRange, setSelectedDateRange] = useState({
    startDate: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    endDate: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
  })

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    present: 0,
    absent: 0,
    leftEarly: 0,
    presentPercentage: 0,
  })

  // Get hooks from useAttendance
  const { useStudent, useStudentAttendance } = useAttendance()

  // Fetch student data with React Query
  const {
    data: studentData,
    isLoading: isLoadingStudent,
    isError: isStudentError,
    error: studentError
  } = useStudent(studentId)

  // Fetch attendance records with React Query
  const {
    data: attendanceRecords = [],
    isLoading: isLoadingAttendance,
    isError: isAttendanceError,
    error: attendanceError,
    refetch: refetchAttendance
  } = useStudentAttendance(
    studentId,
    selectedDateRange.startDate,
    selectedDateRange.endDate
  )

  // Function to refresh data
  const refreshData = () => {
    refetchAttendance()
  }

  // Fetch class data with React Query
  const {
    data: classData,
    error: classError
  } = useQuery({
    queryKey: ['class', studentData?.class?.id],
    queryFn: async () => {
      if (!studentData?.class?.id) return null
      const response = await api.get<ClassData>(`/class/${studentData.class.id}`)
      return response.data
    },
    enabled: !!studentData?.class?.id,
    staleTime: 1000 * 60 * 10, // 10 minutes
  })

  // Fetch timetable data with React Query
  const {
    data: timetableData
  } = useQuery({
    queryKey: ['timetable', classData?.id],
    queryFn: async () => {
      if (!classData?.id) return null
      try {
        const response = await api.get(`/timetable/class/${classData.id}`)
        return response.data
      } catch (err: any) {
        if (err.response && err.response.status === 404) {
          // Gracefully handle missing timetable
          return { scheduleData: [] }
        }
        throw err
      }
    },
    enabled: !!classData?.id,
    staleTime: 1000 * 60 * 10, // 10 minutes
  })

  // Process timetable data to extract teacher-subject relationships
  useEffect(() => {
    if (timetableData && timetableData.scheduleData) {
      // Extract unique teacher-subject combinations
      const teacherSubjectMap = new Map<string, TeacherSubject>()

      // Process each day's schedule
      Object.values(timetableData.scheduleData).forEach(
        (daySchedule: any) => {
          if (Array.isArray(daySchedule)) {
            daySchedule.forEach((slot: any) => {
              if (slot.teacherId && slot.teacherName && slot.subject) {
                const key = `${slot.teacherId}-${slot.subject}`
                if (!teacherSubjectMap.has(key)) {
                  teacherSubjectMap.set(key, {
                    teacherId: slot.teacherId,
                    teacherName: slot.teacherName,
                    subjectName: slot.subject,
                  })
                }
              }
            })
          }
        }
      )

      setTeacherSubjects(Array.from(teacherSubjectMap.values()))
    }
  }, [timetableData])

  // Show error toast if student data fetch fails
  useEffect(() => {
    if (isStudentError && studentError) {
      console.error('Error fetching student data:', studentError)
      toast({
        title: 'Error',
        description: String(studentError),
        variant: 'destructive',
      })
    }
  }, [isStudentError, studentError, toast])

  // Show error toast if attendance fetch fails
  useEffect(() => {
    if (isAttendanceError && attendanceError) {
      console.error('Error fetching attendance records:', attendanceError)
      toast({
        title: 'Error',
        description: String(attendanceError),
        variant: 'destructive',
      })
    }
  }, [isAttendanceError, attendanceError, toast])

  // Use React Query for filtering and calculating statistics
  const { data: filteredData } = useQuery({
    queryKey: ['filteredStudentAttendance', attendanceRecords, selectedStatus],
    queryFn: () => {
      if (attendanceRecords.length === 0) {
        return {
          records: [],
          stats: {
            total: 0,
            present: 0,
            absent: 0,
            leftEarly: 0,
            presentPercentage: 0,
          }
        }
      }

      const total = attendanceRecords.length
      const present = attendanceRecords.filter(
        (record: Attendance) => (record.status || '').toLowerCase() === 'present'
      ).length
      const absent = attendanceRecords.filter(
        (record: Attendance) => (record.status || '').toLowerCase() === 'absent'
      ).length
      const leftEarly = attendanceRecords.filter(
        (record: Attendance) => (record.status || '').toLowerCase() === 'left_early'
      ).length

      const presentPercentage =
        total > 0 ? Math.round((present / total) * 100) : 0

      // Calculate statistics
      const stats = {
        total,
        present,
        absent,
        leftEarly,
        presentPercentage,
      }

      // Filter records
      let filtered = [...attendanceRecords]

      // Filter by status (case-insensitive)
      if (selectedStatus !== 'all') {
        filtered = filtered.filter((record: Attendance) => (record.status || '').toLowerCase() === selectedStatus.toLowerCase())
      }

      // Sort by date (newest first)
      filtered.sort(
        (a: Attendance, b: Attendance) => new Date(b.date).getTime() - new Date(a.date).getTime()
      )

      return {
        records: filtered,
        stats
      }
    },
    enabled: true,
  })

  // Update state with filtered data
  useEffect(() => {
    if (filteredData) {
      setFilteredRecords(filteredData.records)
      setStats(filteredData.stats)
    }
  }, [filteredData])

  // Loading state for the component
  const loading = isLoadingStudent || isLoadingAttendance

  // View attendance record details
  const viewRecordDetails = (record: Attendance) => {
    setSelectedRecord(record)
    setIsDialogOpen(true)
  }

  // Function to get status badge color
  const getStatusBadgeColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'bg-green-100 text-green-800'
      case AttendanceStatus.ABSENT:
        return 'bg-red-100 text-red-800'
      case AttendanceStatus.LEFT_EARLY:
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Function to get status label
  const getStatusLabel = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'Present'
      case AttendanceStatus.ABSENT:
        return 'Absent'
      case AttendanceStatus.LEFT_EARLY:
        return 'Left Early'
      default:
        return status
    }
  }

  // Date range options
  const dateRangeOptions = [
    {
      label: 'This Month',
      startDate: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
      endDate: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
    },
    {
      label: 'Last 7 Days',
      startDate: format(subDays(new Date(), 7), 'yyyy-MM-dd'),
      endDate: format(new Date(), 'yyyy-MM-dd'),
    },
    {
      label: 'Last 30 Days',
      startDate: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
      endDate: format(new Date(), 'yyyy-MM-dd'),
    },
    {
      label: 'Last 90 Days',
      startDate: format(subDays(new Date(), 90), 'yyyy-MM-dd'),
      endDate: format(new Date(), 'yyyy-MM-dd'),
    },
  ]

  // Handle date range change
  const handleDateRangeChange = (value: string) => {
    const option = dateRangeOptions.find((option) => option.label === value)
    if (option) {
      setSelectedDateRange({
        startDate: option.startDate,
        endDate: option.endDate,
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Student and Class Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>My Class Information</CardTitle>
          <CardDescription>
            Details about your class and teachers
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && !studentData ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium flex items-center">
                    <Users className="mr-2 h-4 w-4" /> Class
                  </h3>
                  <p className="text-sm mt-1">
                    {classData?.name || 'Not assigned to a class'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium flex items-center">
                    <GraduationCap className="mr-2 h-4 w-4" /> Grade
                  </h3>
                  <p className="text-sm mt-1">
                    {classData?.grade?.name || 'Not available'}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium flex items-center mb-2">
                  <BookOpen className="mr-2 h-4 w-4" /> My Subjects and Teachers
                </h3>
                {teacherSubjects.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {teacherSubjects.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center p-2 border rounded"
                      >
                        <div>
                          <p className="text-sm font-medium">
                            {item.subjectName}
                          </p>
                          <p className="text-xs text-gray-500">
                            Teacher: {item.teacherName}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    No subject or teacher information available
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attendance Records Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>My Attendance Records</CardTitle>
              <CardDescription>
                View your attendance records and statistics
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Select
                defaultValue={dateRangeOptions[0].label}
                onValueChange={handleDateRangeChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  {dateRangeOptions.map((option) => (
                    <SelectItem key={option.label} value={option.label}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={refreshData}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.total}</div>
                  <p className="text-xs text-muted-foreground">
                    Total Sessions
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {stats.present}
                  </div>
                  <p className="text-xs text-muted-foreground">Present</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats.absent}
                  </div>
                  <p className="text-xs text-muted-foreground">Absent</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {stats.presentPercentage}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Attendance Rate
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="all" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all" onClick={() => setSelectedStatus('all')}>
                All
              </TabsTrigger>
              <TabsTrigger
                value={AttendanceStatus.PRESENT}
                onClick={() => setSelectedStatus(AttendanceStatus.PRESENT)}
              >
                Present
              </TabsTrigger>
              <TabsTrigger
                value={AttendanceStatus.ABSENT}
                onClick={() => setSelectedStatus(AttendanceStatus.ABSENT)}
              >
                Absent
              </TabsTrigger>
              <TabsTrigger
                value={AttendanceStatus.LEFT_EARLY}
                onClick={() => setSelectedStatus(AttendanceStatus.LEFT_EARLY)}
              >
                Left Early
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-0">
              {renderAttendanceTable()}
            </TabsContent>
            <TabsContent value={AttendanceStatus.PRESENT} className="mt-0">
              {renderAttendanceTable()}
            </TabsContent>
            <TabsContent value={AttendanceStatus.ABSENT} className="mt-0">
              {renderAttendanceTable()}
            </TabsContent>
            <TabsContent value={AttendanceStatus.LEFT_EARLY} className="mt-0">
              {renderAttendanceTable()}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Attendance Record Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Attendance Record Details</DialogTitle>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium">Date</h4>
                  <p>{format(new Date(selectedRecord.date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Day</h4>
                  <p>{selectedRecord.day}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Time Slot</h4>
                  <p>{selectedRecord.timeSlot}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Status</h4>
                  <Badge className={getStatusBadgeColor(selectedRecord.status)}>
                    {getStatusLabel(selectedRecord.status)}
                  </Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Class</h4>
                  <p>{selectedRecord.className}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Subject</h4>
                  <p>{selectedRecord.subjectName}</p>
                  {teacherSubjects.find(
                    (t) => t.subjectName === selectedRecord.subjectName
                  ) && (
                    <p className="text-xs text-gray-500 mt-1">
                      Teacher:{' '}
                      {
                        teacherSubjects.find(
                          (t) => t.subjectName === selectedRecord.subjectName
                        )?.teacherName
                      }
                    </p>
                  )}
                </div>
              </div>

              {(selectedRecord.notes || selectedRecord.reason) && (
                <div>
                  <h4 className="text-sm font-medium">Notes/Reason</h4>
                  <p className="text-sm">
                    {selectedRecord.notes || selectedRecord.reason}
                  </p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )

  // Helper function to render the attendance table
  function renderAttendanceTable() {
    // Use the most relevant error from attendance, student, or class queries
    const error = attendanceError || studentError || classError;
    if (loading) {
      return (
        <div className="space-y-2">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </div>
      )
    }

    if (error) {
      return (
        <div className="text-center py-8">
          <div className="text-red-500 mb-4">
            <h3 className="text-sm font-semibold">Error loading data</h3>
            <p className="text-xs">{String(error)}</p>
          </div>
          <Button onClick={refreshData} variant="outline" className="mt-2">
            <RefreshCw className="mr-2 h-4 w-4" /> Try Again
          </Button>
        </div>
      )
    }

    if (filteredRecords.length === 0) {
      return (
        <div className="text-center py-8">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">
            No attendance records
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            No attendance records found for the selected filters.
          </p>
        </div>
      )
    }

    return (
      <ScrollArea className="h-[400px]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Day</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Subject</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  {format(new Date(record.date), 'MMM d, yyyy')}
                </TableCell>
                <TableCell>{record.day}</TableCell>
                <TableCell>{record.timeSlot}</TableCell>
                <TableCell>
                  {record.subjectName}
                  {teacherSubjects.find(
                    (t) => t.subjectName === record.subjectName
                  ) && (
                    <div className="text-xs text-gray-500">
                      Teacher:{' '}
                      {
                        teacherSubjects.find(
                          (t) => t.subjectName === record.subjectName
                        )?.teacherName
                      }
                    </div>
                  )}
                </TableCell>
                <TableCell>{record.className}</TableCell>
                <TableCell>
                  <Badge className={getStatusBadgeColor(record.status)}>
                    {getStatusLabel(record.status)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => viewRecordDetails(record)}
                  >
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
    )
  }
}
