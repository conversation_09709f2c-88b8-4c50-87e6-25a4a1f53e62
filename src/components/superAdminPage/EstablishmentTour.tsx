import { useState, useEffect } from 'react'
import Joyride, { Step, CallBackProps, STATUS } from 'react-joyride'
import { useTourStore } from '../../store/tourStore'

export function EstablishmentTour() {
  const [steps, setSteps] = useState<Step[]>([])

  // Use Zustand store for tour state
  const {
    establishmentTourActive: run,
    establishmentTourStep,
    establishmentTourCompleted,
    establishmentTourSkipped,
    startEstablishmentTour,
    completeEstablishmentTour,
    skipEstablishmentTour,
    setEstablishmentTourStep,
  } = useTourStore()

  useEffect(() => {
    // Define the steps for the establishment tour with more detailed guidance
    const tourSteps: Step[] = [
      {
        target: 'body',
        content:
          'Welcome to the Establishments page. Here you can manage your school establishments.',
        placement: 'center',
        disableBeacon: true,
        title: 'Establishments Page',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: '.create-establishment-btn, button:nth-of-type(1)',
        content:
          'Click here to create a new establishment. This is required before you can access other services.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Create Establishment',
        disableOverlay: false,
        disableScrolling: true,
        spotlightClicks: true,
      },
      {
        target: 'form, .card',
        content:
          "This form allows you to enter all the details for your new establishment. Let's go through each field.",
        placement: 'top',
        disableBeacon: true,
        title: 'Establishment Form Overview',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'input[id="name"]',
        content:
          "Enter your establishment name here. This is a required field and will be displayed prominently on your school's page.",
        placement: 'bottom',
        disableBeacon: true,
        title: 'Establishment Name',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'input[id="address"]',
        content:
          "Enter your establishment's physical address here. This helps students and parents locate your school.",
        placement: 'bottom',
        disableBeacon: true,
        title: 'Establishment Address',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: '.file-uploader, div:has(> label:contains("School Logo"))',
        content:
          'Upload your school logo here. Click "Select Image" to choose a file from your computer.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'School Logo',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'input[id="url"]',
        content:
          'If your school has a website, enter the URL here. This will allow students and parents to visit your website directly.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Website URL',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("CMS Content"))',
        content:
          "Select a template for your school's content management system. This will determine the layout of your school's page.",
        placement: 'top',
        disableBeacon: true,
        title: 'CMS Content',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'textarea[id="description"]',
        content:
          'Write a detailed description of your school here. This will help parents and students understand what makes your school special.',
        placement: 'top',
        disableBeacon: true,
        title: 'School Description',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Hero Image"))',
        content:
          "Upload a hero image for your school. This large banner image will appear at the top of your school's page.",
        placement: 'bottom',
        disableBeacon: true,
        title: 'Hero Image',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Contact Email"))',
        content:
          'Enter the main contact email for your school. This will be used for inquiries from parents and students.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Contact Email',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Contact Phone"))',
        content:
          "Enter the main contact phone number for your school. This will be displayed on your school's page.",
        placement: 'bottom',
        disableBeacon: true,
        title: 'Contact Phone',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Social Links"))',
        content:
          "Add links to your school's social media profiles. This helps parents and students connect with your school online.",
        placement: 'top',
        disableBeacon: true,
        title: 'Social Links',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Services"))',
        content:
          'Add the services your school offers. Type a service and click the plus button to add it to the list.',
        placement: 'top',
        disableBeacon: true,
        title: 'Services',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'div:has(> label:contains("Active Status"))',
        content:
          'Toggle whether your establishment is active. Active establishments are visible to users.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Active Status',
        disableOverlay: false,
        disableScrolling: true,
      },
      {
        target: 'button[type="submit"]',
        content:
          'After filling out the form, click here to save your establishment. You can always edit these details later.',
        placement: 'top',
        disableBeacon: true,
        title: 'Save Establishment',
        disableOverlay: false,
        disableScrolling: true,
      },
    ]

    setSteps(tourSteps)

    // Listen for the start-establishment-tour event
    const handleStartTour = () => {
      console.log('EstablishmentTour: Starting tour from event')

      // Reset tour completion status
      localStorage.removeItem('tour_completed_establishment')
      localStorage.removeItem('tour_skipped_establishment')

      // Start the tour with a delay to ensure DOM is ready
      setTimeout(() => {
        console.log('EstablishmentTour: Starting tour via Zustand')
        startEstablishmentTour()
      }, 500)
    }

    window.addEventListener('start-establishment-tour', handleStartTour)

    // Check if we should start the tour automatically
    const tourCompleted =
      establishmentTourCompleted ||
      localStorage.getItem('tour_completed_establishment') === 'true'
    const tourSkipped =
      establishmentTourSkipped ||
      localStorage.getItem('tour_skipped_establishment') === 'true'
    const userOnboarding = localStorage.getItem('userOnboarding') === 'true'

    // Check if this is triggered from localStorage or if we're in onboarding
    const triggerTour =
      localStorage.getItem('trigger_establishment_tour') === 'true'

    // Check if we're on the form page
    const isFormPage =
      window.location.pathname.includes('etablisments') &&
      document.querySelector('form') !== null

    console.log('EstablishmentTour: Checking tour status', {
      tourCompleted,
      tourSkipped,
      triggerTour,
      userOnboarding,
      isFormPage,
      establishmentTourStep,
    })

    if (!tourCompleted && !tourSkipped && (triggerTour || userOnboarding)) {
      console.log('EstablishmentTour: Auto-starting tour')

      // Clear the trigger flag
      localStorage.removeItem('trigger_establishment_tour')

      // Add a longer delay to ensure the DOM is fully ready
      setTimeout(() => {
        console.log('EstablishmentTour: Starting tour via Zustand after delay')

        // If we're already on the form page, start from step 2
        if (isFormPage) {
          console.log(
            'EstablishmentTour: Already on form page, setting initial step to 2'
          )
          setEstablishmentTourStep(2)
        } else {
          // Start from the beginning
          setEstablishmentTourStep(0)
        }

        startEstablishmentTour()
      }, 2000)
    }

    return () => {
      window.removeEventListener('start-establishment-tour', handleStartTour)
    }
  }, [])

  // Handle tour events
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { action, index, status, type } = data

    // Debug what's happening with the tour
    console.log('EstablishmentTour callback:', {
      action,
      index,
      status,
      type,
      step: steps[index],
    })

    // Handle actions based on the callback type
    switch (type) {
      case 'step:before':
        // Log when a step is about to be shown
        console.log(
          `EstablishmentTour: Step ${index + 1}/${steps.length} is about to be shown`
        )
        break

      case 'step:after':
        // Log when a step has been shown
        console.log(
          `EstablishmentTour: Step ${index + 1}/${steps.length} has been shown`
        )

        // Update the current step in Zustand
        if (action === 'next' || action === 'prev') {
          const nextIndex = action === 'next' ? index + 1 : index - 1
          if (nextIndex >= 0 && nextIndex < steps.length) {
            console.log(
              `EstablishmentTour: Updating Zustand step to ${nextIndex}`
            )
            setEstablishmentTourStep(nextIndex)
          }
        }

        // Handle navigation or other actions based on step content
        if (action === 'next' && index === 1) {
          // After the "Create Establishment" step, we might need to click the button
          console.log('EstablishmentTour: After Create Establishment step')

          // Check if we're already on the form page
          const formExists = document.querySelector('form')
          if (!formExists) {
            // Try to find and click the create button
            setTimeout(() => {
              const createButton = document.querySelector(
                '.create-establishment-btn'
              )
              if (createButton && createButton instanceof HTMLElement) {
                console.log(
                  'EstablishmentTour: Found create button, clicking it'
                )
                createButton.click()
              } else {
                // Try alternative selectors
                const allButtons = document.querySelectorAll('button')
                for (let i = 0; i < allButtons.length; i++) {
                  if (
                    allButtons[i].textContent?.includes(
                      'Create New Establishment'
                    )
                  ) {
                    console.log(
                      'EstablishmentTour: Found create button by text, clicking it'
                    )
                    ;(allButtons[i] as HTMLElement).click()
                    break
                  }
                }
              }
            }, 500)
          } else {
            console.log(
              'EstablishmentTour: Already on form page, continuing tour'
            )
          }
        }

        // Help fill out the form by focusing on the current field
        if (action === 'next' && index >= 3 && index <= 14) {
          // Get the current step's target
          const targetSelector = steps[index]?.target
          if (typeof targetSelector === 'string') {
            setTimeout(() => {
              // Try to find the input field
              const inputField = document.querySelector(targetSelector)
              if (
                inputField instanceof HTMLInputElement ||
                inputField instanceof HTMLTextAreaElement
              ) {
                console.log(
                  `EstablishmentTour: Focusing on field ${targetSelector}`
                )
                inputField.focus()
              }
            }, 300)
          }
        }
        break

      case 'tour:start':
        // Log when the tour starts
        console.log('EstablishmentTour: Tour started')

        // Check if we're already on the form page
        const isFormPage =
          window.location.pathname.includes('etablisments') &&
          document.querySelector('form') !== null

        // Check if we need to start at a specific step (from Zustand)
        if (establishmentTourStep > 0) {
          console.log(
            `EstablishmentTour: Starting at step ${establishmentTourStep} from Zustand state`
          )
          setTimeout(() => {
            // Cast data to include joyride property
            const joyrideData = data as CallBackProps & {
              joyride: { goToStep: (step: number) => void }
            }
            joyrideData.joyride.goToStep(establishmentTourStep)
          }, 300)
        } else if (isFormPage) {
          console.log(
            'EstablishmentTour: Already on form page, skipping to step 2'
          )
          // Skip to step 2 (form overview)
          setTimeout(() => {
            // We need to skip the first two steps (welcome and create button)
            console.log('EstablishmentTour: Skipping to form overview step')
            // Use index 2 for the form overview step
            // Cast data to include joyride property
            const joyrideData = data as CallBackProps & {
              joyride: { goToStep: (step: number) => void }
            }
            joyrideData.joyride.goToStep(2)
            // Update Zustand state
            setEstablishmentTourStep(2)
          }, 300)
        }
        break

      case 'tour:end':
        // Log when the tour ends
        console.log('EstablishmentTour: Tour ended with status:', status)

        // If the tour is finished or skipped, mark it as completed
        if (status === STATUS.FINISHED) {
          console.log('EstablishmentTour: Tour completed')
          completeEstablishmentTour() // Use Zustand action
          localStorage.setItem('tour_completed_establishment', 'true')

          // Dispatch an event to notify other components
          window.dispatchEvent(new CustomEvent('tour-status-change'))

          // Continue with the main tour if we're in onboarding
          if (localStorage.getItem('userOnboarding') === 'true') {
            console.log('EstablishmentTour: Continuing with main tour')

            // Navigate back to the main dashboard
            setTimeout(() => {
              window.location.href = '/super_admin'
            }, 1000)
          }
        } else if (status === STATUS.SKIPPED) {
          console.log('EstablishmentTour: Tour skipped')
          skipEstablishmentTour() // Use Zustand action
          localStorage.setItem('tour_skipped_establishment', 'true')

          // Dispatch an event to notify other components
          window.dispatchEvent(new CustomEvent('tour-status-change'))
        }
        break

      case 'error:target_not_found':
        // Handle case where target element is not found
        console.error(
          `EstablishmentTour: Target element "${steps[index]?.target}" not found`
        )

        // Try to recover by moving to the next step
        if (index < steps.length - 1) {
          console.log(
            'EstablishmentTour: Attempting to recover by moving to the next step'
          )
          // We can't directly set the step index in Joyride, but we can restart the tour
          skipEstablishmentTour() // Stop the tour
          setTimeout(() => {
            setEstablishmentTourStep(index + 1) // Set the next step
            startEstablishmentTour() // Restart the tour
          }, 500)
        } else {
          // If we're at the last step, just end the tour
          console.log('EstablishmentTour: At last step with error, ending tour')
          completeEstablishmentTour()
          localStorage.setItem('tour_completed_establishment', 'true')
        }
        break
    }
  }

  return (
    <Joyride
      steps={steps}
      run={run}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      disableOverlayClose={true}
      disableCloseOnEsc={true}
      spotlightClicks={true}
      scrollToFirstStep={true}
      scrollOffset={100}
      hideBackButton={false}
      disableScrolling={false}
      disableScrollParentFix={false}
      disableOverlay={false}
      spotlightPadding={10}
      stepIndex={establishmentTourStep}
      callback={handleJoyrideCallback}
      debug={true}
      locale={{
        back: 'Back',
        close: 'Close',
        last: 'Finish',
        next: 'Next',
        skip: 'Skip',
      }}
      floaterProps={{
        disableAnimation: true,
        hideArrow: false,
        offset: 0,
        styles: {
          arrow: {
            length: 8,
            spread: 12,
          },
          floater: {
            filter: 'drop-shadow(0 0 10px rgba(0, 0, 0, 0.5))',
          },
        },
      }}
      styles={{
        options: {
          zIndex: 10000,
          primaryColor: '#007bff',
          backgroundColor: '#ffffff',
          arrowColor: '#ffffff',
          overlayColor: 'rgba(0, 0, 0, 0.85)',
          textColor: '#333',
        },
        tooltip: {
          borderRadius: '8px',
          fontSize: '16px',
          padding: '20px',
          backgroundColor: '#ffffff',
          color: '#333',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        },
        tooltipContainer: {
          textAlign: 'center',
          padding: '10px',
        },
        tooltipTitle: {
          fontSize: '20px',
          fontWeight: 'bold',
          marginBottom: '15px',
          color: '#007bff',
          textAlign: 'center',
        },
        tooltipContent: {
          fontSize: '16px',
          lineHeight: '1.6',
          marginBottom: '15px',
          textAlign: 'center',
          color: '#333',
          maxWidth: '400px',
          padding: '5px',
        },
        buttonNext: {
          backgroundColor: '#007bff',
          fontSize: '16px',
          padding: '10px 20px',
          borderRadius: '4px',
          color: '#fff',
          fontWeight: 'bold',
          border: 'none',
          cursor: 'pointer',
        },
        buttonBack: {
          color: '#555',
          marginRight: '15px',
          fontSize: '16px',
          padding: '10px 20px',
          fontWeight: 'bold',
        },
        buttonSkip: {
          color: '#999',
          fontSize: '16px',
          fontWeight: 'bold',
        },
        spotlight: {
          backgroundColor: 'transparent',
          borderRadius: 0,
          boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.85)',
        },
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.85)',
          mixBlendMode: 'normal',
        },
        beaconInner: {
          backgroundColor: '#007bff',
        },
        beaconOuter: {
          backgroundColor: 'rgba(0, 123, 255, 0.2)',
          borderColor: '#007bff',
        },
      }}
    />
  )
}
