import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Admin, CreateAdminDTO, UpdateAdminDTO } from '@/interface/types/user'
import { services } from '@/lib/api/index'
import { api } from '@/lib/api/axios-instance'
import FileUploader from '@/components/shared/FileUploader'
import { Eye, EyeOff, Trash2 } from 'lucide-react'
import { FormYearMonthPicker } from '@/components/ui/form-year-month-picker'
import { parseConflictError, formatErrorMessage } from '@/utils/error-handlers'
import { useToast } from '@/components/ui/use-toast'
import { Toaster } from '@/components/ui/toaster'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, ChevronLeft, ChevronRight } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import Loading from '@/components/shared/Loading'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'

interface AdminFilters {
  cin: string
  name: string
}

interface ActiveFilter {
  key: keyof AdminFilters
  value: string
  label: string
}

export default function SuperAdminManagement(): JSX.Element {
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState<boolean>(false)
  const [isAddAdminOpen, setIsAddAdminOpen] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState(1)

  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isPasswordFocused, setIsPasswordFocused] = useState(false)
  const [adminToDelete, setAdminToDelete] = useState<Admin | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Queries
  const {
    data: admins = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['admins'],
    queryFn: services.admin.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const createAdminMutation = useMutation({
    mutationFn: (data: CreateAdminDTO) => services.admin.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admins'] })
      setIsAddAdminOpen(false)
      toast({
        title: "Admin Created",
        description: "The new admin has been successfully created.",
        variant: "default",
      })
    },
  })

  const toggleActiveMutation = useMutation({
    mutationFn: (id: string) => services.admin.toggleActive(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['admins'] })
      // Find the admin to get their current status
      const admin = admins.find(a => a.id === id)
      const newStatus = admin?.isActive ? 'deactivated' : 'activated'
      toast({
        title: "Status Updated",
        description: `The admin has been ${newStatus}.`,
        variant: "default",
      })
    },
    onError: () => {
      toast({
        title: "Status Update Failed",
        description: "Failed to update admin status. Please try again.",
        variant: "destructive",
      })
    }
  })

  const deleteAdminMutation = useMutation({
    mutationFn: (id: string) => services.admin.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admins'] })
      setIsDeleteDialogOpen(false)
      setAdminToDelete(null)
      toast({
        title: "Admin Deleted",
        description: "The admin has been successfully deleted.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error('Error deleting admin:', error)
      toast({
        title: "Deletion Failed",
        description: "Failed to delete admin. Please try again.",
        variant: "destructive",
      })
      setIsDeleteDialogOpen(false)
    },
  })

  // Filter and search admins
  const filteredAdmins = admins.filter((admin) => {
    // Search query matching
    const searchMatches =
      searchQuery === '' ||
      `${admin.firstname} ${admin.lastname} ${admin.cin}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())

    // All active filters must match
    const filtersMatch = activeFilters.every((filter) => {
      switch (filter.key) {
        case 'name':
          return `${admin.firstname} ${admin.lastname}`
            .toLowerCase()
            .includes(filter.value.toLowerCase())
        case 'cin':
          return admin.cin.toLowerCase().includes(filter.value.toLowerCase())
        default:
          return true
      }
    })

    return searchMatches && filtersMatch
  })

  // Pagination setup
  const adminsPerPage = 5
  const totalPages = Math.ceil(filteredAdmins.length / adminsPerPage)
  const indexOfLastAdmin = currentPage * adminsPerPage
  const indexOfFirstAdmin = indexOfLastAdmin - adminsPerPage
  const currentAdmins = filteredAdmins.slice(
    indexOfFirstAdmin,
    indexOfLastAdmin
  )

  const handleShowProfile = (admin: Admin) => {
    setSelectedAdmin(admin)
    setIsProfileModalOpen(true)
  }

  const handleFilterChange = (
    key: keyof AdminFilters,
    value: string,
    label: string
  ) => {
    setActiveFilters((prev) => {
      const filtered = prev.filter((f) => f.key !== key)
      if (value !== 'all') {
        return [...filtered, { key, value, label }]
      }
      return filtered
    })
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setSearchQuery('')
    setCurrentPage(1)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const togglePasswordVisibility = () => {
    setIsPasswordVisible((prev) => !prev)
  }

  const handleDeleteClick = (admin: Admin) => {
    setAdminToDelete(admin)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (adminToDelete) {
      deleteAdminMutation.mutate(adminToDelete.id)
    }
  }

  const handleUpdateAdmin = async (data: Partial<Admin>) => {
    if (!selectedAdmin?.id) return
    try {
      // Get the birthday from the form
      const formData = new FormData(
        document.querySelector('form') as HTMLFormElement
      )
      const birthday = formData.get('birthday') as string

      // Create update data object
      const updateData: UpdateAdminDTO = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        cin: selectedAdmin.cin,
        avatar: data.avatar || DEFAULT_AVATAR,
        birthday: birthday ? new Date(birthday).toISOString() : undefined,
        gender: (data.gender?.toUpperCase() as 'MALE' | 'FEMALE') || 'MALE',
        address: data.address || '',
        phone: data.phone || '',
        isActive: data.isActive,
        updatedBy: localStorage.getItem('id') || null,
      }

      // Only include password if it was provided
      const password = (
        document.getElementById('password-edit') as HTMLInputElement
      )?.value
      if (password && password.trim() !== '') {
        updateData.password = password
      }

      // Do not include etablissementId as it will be automatically handled by the backend
      // The API will use the etablissementId from the JWT token

      console.log('Sending update data:', {
        ...updateData,
        password: updateData.password ? '***HIDDEN***' : undefined,
      })

      const response = await api.patch<Admin>(
        `/admin/${selectedAdmin.id}`,
        updateData
      )

      console.log('Update successful:', response.data)
      queryClient.invalidateQueries({ queryKey: ['admins'] })
      setIsProfileModalOpen(false)
      toast({
        title: "Admin Updated",
        description: "The admin profile has been successfully updated.",
        variant: "default",
      })
    } catch (error) {
      console.error('Failed to update admin:', error)

      // Check for 409 Conflict error specifically
      if (error && typeof error === 'object' && 'response' in error &&
          error.response && typeof error.response === 'object' && 'status' in error.response &&
          error.response.status === 409) {

        // Use the imported parseConflictError function
        const errorMessage = parseConflictError(error);
        toast({
          title: "Admin Update Failed",
          description: errorMessage,
          variant: "destructive",
        });
      } else if (error && typeof error === 'object' && 'response' in error) {
        const errorResponse = error.response as { data?: { message?: any } }
        console.error('Detailed error response:', errorResponse)
        const message = errorResponse.data?.message;

        // Format the error message using our utility function
        const formattedMessage = formatErrorMessage(message);

        toast({
          title: "Error Updating Admin",
          description: formattedMessage,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: 'An error occurred while updating the admin. Please try again.',
          variant: "destructive",
        });
      }
    }
  }

  const handleCreateAdmin = async (data: CreateAdminDTO) => {
    try {
      // Get the birthday from the form
      const formData = new FormData(
        document.querySelector('form') as HTMLFormElement
      )
      const birthday = formData.get('birthday') as string

      console.log("Birthday from form:", birthday);

      // Do not include etablissementId as it will be automatically handled by the backend
      // The API will use the etablissementId from the JWT token

      // Update the birthday in the data
      data.birthday = birthday
        ? new Date(birthday).toISOString()
        : new Date().toISOString()

      console.log("Birthday after processing:", data.birthday);

      // Ensure gender is uppercase
      data.gender = data.gender.toUpperCase() as 'MALE' | 'FEMALE'

      // Ensure avatar is not undefined
      if (!data.avatar) {
        data.avatar = DEFAULT_AVATAR
      }

      // Add createdBy and updatedBy fields
      data.createdBy = localStorage.getItem('id') || null
      data.updatedBy = localStorage.getItem('id') || null

      console.log('Creating admin with data:', {
        ...data,
        password: '***HIDDEN***',
      })

      await createAdminMutation.mutateAsync(data)
    } catch (error) {
      console.error('Failed to create admin:', error)

      // Check for 409 Conflict error specifically
      if (error && typeof error === 'object' && 'response' in error &&
          error.response && typeof error.response === 'object' && 'status' in error.response &&
          error.response.status === 409) {

        // Use the imported parseConflictError function
        const errorMessage = parseConflictError(error);
        toast({
          title: "Admin Creation Failed",
          description: errorMessage,
          variant: "destructive",
        });
      } else if (error && typeof error === 'object' && 'response' in error) {
        // Handle other types of errors
        const errorResponse = error.response as { data?: { message?: any } };
        const message = errorResponse.data?.message;

        // Format the error message using our utility function
        const formattedMessage = formatErrorMessage(message);

        toast({
          title: "Error Creating Admin",
          description: formattedMessage,
          variant: "destructive",
        });
      } else {
        // Generic error message as a last resort
        toast({
          title: "Error",
          description: 'An error occurred while creating the admin. Please try again.',
          variant: "destructive",
        });
      }
    }
  }

  // Simple function to display CIN value
  const displayCin = (admin: Admin): string => {
    // Just return the CIN directly from the admin object
    return admin.cin || 'N/A'
  }

  // Replace the virtualized render method with a standard table row render function
  const renderAdminRow = (admin: Admin) => {
    // Log the admin data to help debug
    // Log the admin data for debugging

    console.log('Rendering admin row:', {
      id: admin.id,
      name: `${admin.firstname} ${admin.lastname}`,
      cin: admin.cin || 'No CIN',
      avatar: admin.avatar || 'No avatar',
      email: admin.email,
      fullAdmin: admin, // Log the full admin object to see all properties
    })

    return (
      <tr key={admin.id} className="border-b border-gray-100">
        <td className="py-3 px-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-100">
              {/* Use CdnImage component for avatar to ensure it displays correctly */}
              <CdnImage
                src={admin.avatar || DEFAULT_AVATAR}
                alt={`${admin.firstname} ${admin.lastname}`}
                className="w-full h-full object-cover"
                fallbackSrc={DEFAULT_AVATAR}
              />
            </div>
            <div>
              <div className="font-medium">
                {admin.firstname} {admin.lastname}
              </div>
              <div className="text-sm text-gray-500">{admin.email}</div>
            </div>
          </div>
        </td>
        <td className="py-3 px-4">
          {/* Display the CIN value */}
          {displayCin(admin)}
        </td>
        <td className="py-3 px-4">
          <Badge variant={admin.isActive ? 'default' : 'destructive'}>
            {admin.isActive ? 'Active' : 'Inactive'}
          </Badge>
        </td>
        <td className="py-3 px-4 text-right">
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => handleShowProfile(admin)}>
              Profile
            </Button>
            <Button
              variant="outline"
              onClick={() => toggleActiveMutation.mutate(admin.id)}
            >
              {admin.isActive ? 'Deactivate' : 'Activate'}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteClick(admin)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </td>
      </tr>
    )
  }

  if (isLoading) return <Loading />
  if (isError)
    return (
      <p>
        Error:{' '}
        {error instanceof Error ? error.message : 'An unknown error occurred'}
      </p>
    )

  return (
    <div>
      <Toaster />
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Admins Table</h2>
        <Button onClick={() => setIsAddAdminOpen(true)}>Add New Admin</Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search admins by name or CIN..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Name</Label>
                <Input
                  placeholder="Filter by name..."
                  onChange={(e) =>
                    handleFilterChange('name', e.target.value, 'Name')
                  }
                />
              </div>

              <div className="space-y-2">
                <Label>CIN</Label>
                <Input
                  placeholder="Filter by CIN..."
                  onChange={(e) =>
                    handleFilterChange('cin', e.target.value, 'CIN')
                  }
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex justify-between items-center mt-6">
        <div className="flex-1">
          <Badge variant="secondary" className="text-sm">
            {filteredAdmins.length} Admins
          </Badge>
        </div>
        <div className="flex-1 text-right">
          <Badge variant="secondary" className="text-sm">
            Page {currentPage} of {totalPages}
          </Badge>
        </div>
      </div>

      <div className="flex justify-center mt-6">
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {activeFilters.length > 0 && (
        <div className="flex gap-2 mt-4">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {filter.label}: {filter.value}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange(filter.key, 'all', filter.label)
                }
              />
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            className="h-6"
            onClick={clearAllFilters}
          >
            Clear all
          </Button>
        </div>
      )}

      <div className="mt-6">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="text-left py-3 px-4">Admin</th>
              <th className="text-left py-3 px-4">CIN</th>
              <th className="text-left py-3 px-4">Status</th>
              <th className="text-right py-3 px-4">Actions</th>
            </tr>
          </thead>
          <tbody>{currentAdmins.map(renderAdminRow)}</tbody>
        </table>
      </div>

      {/* Profile Modal */}
      <Dialog open={isProfileModalOpen} onOpenChange={setIsProfileModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Admin Profile</DialogTitle>
          </DialogHeader>
          {selectedAdmin && (
            <form
              onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.currentTarget)
                const gender = formData.get('gender') as string
                console.log('Form Gender:', gender)
                console.log('Current Admin Gender:', selectedAdmin.gender)

                // Get the avatar URL from the hidden input
                const avatarInput = document.getElementById(
                  'edit-avatar'
                ) as HTMLInputElement
                const avatarUrl = avatarInput
                  ? avatarInput.value
                  : selectedAdmin.avatar || DEFAULT_AVATAR

                // Create a date object to format it properly
                const birthdayStr = formData.get('birthday') as string
                const birthdayDate = new Date(birthdayStr)
                // Format as ISO string
                const birthdayISO = birthdayDate.toISOString()

                const data = {
                  firstname: formData.get('firstname') as string,
                  lastname: formData.get('lastname') as string,
                  email: formData.get('email') as string,
                  avatar: avatarUrl || DEFAULT_AVATAR,
                  // Send birthday as ISO string, not as Date object
                  birthday: birthdayISO,
                  gender: gender?.toUpperCase() as 'MALE' | 'FEMALE',
                  isActive: formData.get('isActive') === 'true',
                  // Include CIN since it seems to be required by the backend validation
                  cin: selectedAdmin.cin,
                  // Include a password field with a valid value to pass validation
                  password: 'Temporary@Password123!',
                }

                console.log('Updating admin with data:', data)
                handleUpdateAdmin(data)
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstname">First Name</Label>
                  <Input
                    id="firstname"
                    name="firstname"
                    defaultValue={selectedAdmin.firstname}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastname">Last Name</Label>
                  <Input
                    id="lastname"
                    name="lastname"
                    defaultValue={selectedAdmin.lastname}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  defaultValue={selectedAdmin.email}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cin">CIN</Label>
                <Input
                  id="cin"
                  name="cin"
                  defaultValue={selectedAdmin.cin}
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <FileUploader
                  label="Avatar (Optional)"
                  onFileUploaded={(url) => {
                    // Store the uploaded file URL in a hidden input
                    const avatarInput = document.getElementById(
                      'edit-avatar'
                    ) as HTMLInputElement
                    if (avatarInput) {
                      avatarInput.value = url
                    }
                  }}
                  isAvatar={true}
                  defaultPreview={selectedAdmin.avatar || DEFAULT_AVATAR}
                />
                <Input
                  id="edit-avatar"
                  name="avatar"
                  type="hidden"
                  defaultValue={selectedAdmin.avatar || DEFAULT_AVATAR}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="birthday">Birthday</Label>
                <FormYearMonthPicker
                  id="birthday"
                  label="Birthday"
                  value={
                    selectedAdmin?.birthday
                      ? new Date(selectedAdmin.birthday)
                      : undefined
                  }
                  onChange={(date: Date | undefined) => {
                    // The picker will now automatically update the hidden input
                    const formData = new FormData(
                      document.querySelector('form') as HTMLFormElement
                    )
                    console.log(
                      'Selected date:',
                      date,
                      'Form data birthday:',
                      formData.get('birthday')
                    )
                  }}
                  placeholder="Select birthday"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="gender">Gender</Label>
                <Select
                  name="gender"
                  defaultValue={selectedAdmin.gender?.toUpperCase() || 'MALE'}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  name="address"
                  defaultValue={selectedAdmin.address || ''}
                  placeholder="Enter address"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  defaultValue={selectedAdmin.phone || ''}
                  placeholder="Enter phone number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="isActive">Status</Label>
                <Select
                  name="isActive"
                  defaultValue={selectedAdmin.isActive ? 'true' : 'false'}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Active</SelectItem>
                    <SelectItem value="false">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password-edit">Password (Optional)</Label>
                <div className="relative">
                  <Input
                    id="password-edit"
                    name="password"
                    type={isPasswordVisible ? 'text' : 'password'}
                    placeholder="Leave blank to keep current password"
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => setIsPasswordFocused(false)}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </button>
                </div>

                {/* Password requirements - only show when field is focused */}
                {isPasswordFocused && (
                  <div className="mt-2 text-xs text-gray-500">
                    <p>Password must contain:</p>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li>At least 6 characters</li>
                      <li>At least one uppercase letter</li>
                      <li>At least one number</li>
                      <li>At least one special character</li>
                    </ul>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsProfileModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Admin</Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Add New Admin Modal */}
      <Dialog open={isAddAdminOpen} onOpenChange={setIsAddAdminOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Admin</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)

              // Get the avatar URL from the hidden input
              const avatarInput = document.getElementById(
                'avatar'
              ) as HTMLInputElement

              // Make sure we get the actual avatar URL
              const avatarUrl =
                avatarInput && avatarInput.value !== DEFAULT_AVATAR
                  ? avatarInput.value
                  : DEFAULT_AVATAR

              console.log('Avatar URL for form submission:', avatarUrl)

              // Get the birthday and format it as ISO string
              const birthdayStr = formData.get('birthday') as string
              console.log("Birthday string from form:", birthdayStr);

              // Create a date object to format it properly
              let birthdayISO = '';
              if (birthdayStr && birthdayStr.trim() !== '') {
                const birthdayDate = new Date(birthdayStr);
                // Format as ISO string
                birthdayISO = birthdayDate.toISOString();
                console.log("Birthday ISO after processing:", birthdayISO);
              } else {
                // Default to current date if no birthday is provided
                birthdayISO = new Date().toISOString();
                console.log("Using default birthday:", birthdayISO);
              }

              // Get the etablissementId from localStorage if available
              const etablissementId = localStorage.getItem('etablissementId')

              // Create the admin data object according to the required DTO structure
              const data: CreateAdminDTO = {
                firstname: formData.get('firstname') as string,
                lastname: formData.get('lastname') as string,
                email: formData.get('email') as string,
                password: formData.get('password') as string,
                cin: formData.get('cin') as string,
                // Send birthday as ISO string, not as Date object
                birthday: birthdayISO,
                gender: (formData.get('gender') as string)?.toUpperCase() as
                  | 'MALE'
                  | 'FEMALE',
                address: formData.get('address') as string || '',
                phone: formData.get('phone') as string || '',
                avatar: avatarUrl || DEFAULT_AVATAR,
                // Always set isActive to true by default
                isActive: true,
                // Add createdBy and updatedBy fields
                createdBy: localStorage.getItem('id') || null,
                updatedBy: localStorage.getItem('id') || null,
              }

              // Add etablissementId if available
              if (etablissementId) {
                data.etablissementId = etablissementId
              }

              console.log('Form data collected:', {
                ...data,
                password: '***HIDDEN***',
              })

              console.log('Form data:', data)
              handleCreateAdmin(data)
            }}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstname">First Name</Label>
                <Input id="firstname" name="firstname" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastname">Last Name</Label>
                <Input id="lastname" name="lastname" required />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" name="email" type="email" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={isPasswordVisible ? 'text' : 'password'}
                  required
                  onFocus={() => setIsPasswordFocused(true)}
                  onBlur={() => setIsPasswordFocused(false)}
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                >
                  {isPasswordVisible ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </button>
              </div>

              {/* Password requirements - only show when field is focused */}
              {isPasswordFocused && (
                <div className="mt-2 text-xs text-gray-500">
                  <p>Password must contain:</p>
                  <ul className="list-disc pl-5 space-y-1 mt-1">
                    <li id="length-check" className="">
                      At least 6 characters
                    </li>
                    <li id="uppercase-check" className="">
                      At least one uppercase letter
                    </li>
                    <li id="number-check" className="">
                      At least one number
                    </li>
                    <li id="special-check" className="">
                      At least one special character
                    </li>
                  </ul>
                </div>
              )}

              {/* Password strength indicator */}
              <div id="password-strength-container" className="mt-2 hidden">
                <div className="flex justify-between mb-1">
                  <span className="text-xs">Password strength:</span>
                  <span id="strength-text" className="text-xs">
                    Weak
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div
                    id="strength-bar"
                    className="h-1 rounded-full bg-red-500 w-1/4"
                  ></div>
                </div>
              </div>

              <script>
                {`
                document.addEventListener('DOMContentLoaded', function() {
                  const passwordInput = document.getElementById('password');
                  const lengthCheck = document.getElementById('length-check');
                  const uppercaseCheck = document.getElementById('uppercase-check');
                  const numberCheck = document.getElementById('number-check');
                  const specialCheck = document.getElementById('special-check');
                  const strengthContainer = document.getElementById('password-strength-container');
                  const strengthBar = document.getElementById('strength-bar');
                  const strengthText = document.getElementById('strength-text');

                  passwordInput.addEventListener('input', function() {
                    const value = passwordInput.value;

                    // Check requirements
                    const hasLength = value.length >= 6;
                    const hasUpper = /[A-Z]/.test(value);
                    const hasNumber = /[0-9]/.test(value);
                    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);

                    // Update requirement checks
                    lengthCheck.className = hasLength ? 'text-green-500' : '';
                    uppercaseCheck.className = hasUpper ? 'text-green-500' : '';
                    numberCheck.className = hasNumber ? 'text-green-500' : '';
                    specialCheck.className = hasSpecial ? 'text-green-500' : '';

                    // Calculate strength
                    const score = [hasLength, hasUpper, hasNumber, hasSpecial].filter(Boolean).length;

                    // Show strength indicator if password has content
                    if (value) {
                      strengthContainer.classList.remove('hidden');

                      // Update strength bar and text
                      if (score === 4) {
                        strengthBar.className = 'h-1 rounded-full bg-green-500 w-full';
                        strengthText.textContent = 'Strong';
                      } else if (score === 3) {
                        strengthBar.className = 'h-1 rounded-full bg-blue-500 w-3/4';
                        strengthText.textContent = 'Good';
                      } else if (score === 2) {
                        strengthBar.className = 'h-1 rounded-full bg-yellow-500 w-2/4';
                        strengthText.textContent = 'Fair';
                      } else {
                        strengthBar.className = 'h-1 rounded-full bg-red-500 w-1/4';
                        strengthText.textContent = 'Weak';
                      }
                    } else {
                      strengthContainer.classList.add('hidden');
                    }
                  });
                });
                `}
              </script>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cin">CIN</Label>
              <Input id="cin" name="cin" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="birthday">Birthday</Label>
              <FormYearMonthPicker
                id="birthday"
                label="Birthday"
                value={undefined}
                onChange={(date: Date | undefined) => {
                  console.log("Date selected in picker:", date);

                  // Update the hidden input directly
                  if (date) {
                    const birthdayInput = document.querySelector(
                      'input[name="birthday"]'
                    ) as HTMLInputElement;

                    if (birthdayInput) {
                      birthdayInput.value = date.toISOString().split('T')[0];
                      console.log("Updated birthday input value to:", birthdayInput.value);
                    }
                  }
                }}
                placeholder="Select birthday"
                required={true}
              />

            </div>
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select name="gender" defaultValue="MALE">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="MALE">Male</SelectItem>
                  <SelectItem value="FEMALE">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                placeholder="Enter address"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                placeholder="Enter phone number"
              />
            </div>
            <div className="space-y-2">
              <FileUploader
                label="Avatar (Required)"
                onFileUploaded={(url) => {
                  // Store the uploaded file URL in a hidden input
                  const avatarInput = document.getElementById(
                    'avatar'
                  ) as HTMLInputElement
                  if (avatarInput) {
                    avatarInput.value = url
                    console.log('Avatar URL set to:', url)

                    // Dispatch an input event to ensure the form recognizes the change
                    const event = new Event('input', { bubbles: true })
                    avatarInput.dispatchEvent(event)

                    // Also log the current value to verify it was set
                    setTimeout(() => {
                      console.log(
                        'Current avatar input value:',
                        avatarInput.value
                      )
                    }, 100)
                  }
                }}
                onError={(error) => {
                  console.error('Avatar upload error:', error)
                }}
                isAvatar={true}
                defaultPreview={DEFAULT_AVATAR}
              />
              <Input
                id="avatar"
                name="avatar"
                type="hidden"
                defaultValue={DEFAULT_AVATAR}
                required
              />
              <div className="text-xs text-gray-500 mt-1">
                Note: Avatar image is required and must be uploaded to display
                correctly.
              </div>
            </div>
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddAdminOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Create Admin</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              admin
              {adminToDelete && (
                <span className="font-semibold">
                  {' '}
                  {adminToDelete.firstname} {adminToDelete.lastname}
                </span>
              )}{' '}
              and remove their data from the server.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
