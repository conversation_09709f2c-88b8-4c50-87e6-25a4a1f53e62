import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'

export function SuperAdminTourRestartButton() {
  const [visible, setVisible] = useState(true)

  useEffect(() => {
    // Show the button when the tour is not active
    const tourActive =
      localStorage.getItem('super_admin_tour_active') === 'true'
    setVisible(!tourActive)

    // Add a listener for tour status changes
    const handleTourStatusChange = () => {
      const tourActive =
        localStorage.getItem('super_admin_tour_active') === 'true'
      setVisible(!tourActive)
    }

    window.addEventListener('tour-status-change', handleTourStatusChange)

    return () => {
      window.removeEventListener('tour-status-change', handleTourStatusChange)
    }
  }, [])

  const handleRestartClick = () => {
    console.log('SuperAdminTourRestartButton: Restarting tour')

    // Reset tour state in localStorage
    localStorage.removeItem('tour_completed_super_admin')
    localStorage.setItem('userOnboarding', 'true')
    localStorage.removeItem('tour_step_index')

    // Set the tour as active
    localStorage.setItem('super_admin_tour_active', 'true')

    // Dispatch the event to start the tour
    window.dispatchEvent(new CustomEvent('start-super-admin-tour'))

    // Double-check by dispatching again after a short delay
    setTimeout(() => {
      console.log(
        'SuperAdminTourRestartButton: Dispatching start event again after delay'
      )
      window.dispatchEvent(new CustomEvent('start-super-admin-tour'))
    }, 500)
  }

  if (!visible) {
    return null
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 10001,
      }}
    >
      <Button
        onClick={handleRestartClick}
        variant="outline"
        size="icon"
        className="bg-white hover:bg-gray-100 text-blue-500 rounded-full shadow-md h-10 w-10"
        title="Restart Tour"
      >
        <RefreshCw className="h-5 w-5" />
      </Button>
    </div>
  )
}
