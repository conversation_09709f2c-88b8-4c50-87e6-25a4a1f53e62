import { DatabaseOverview } from '@/components/shared/DatabaseOverview'
import { useNavigate } from '@tanstack/react-router'

export default function SuperAdminOverview() {
  const navigate = useNavigate({ from: '/super_admin/database' })

  const handleCardClick = (section: string) => {
    // This function will be called when a card is clicked in the DatabaseOverview
    // We'll update the URL parameters to show the selected section
    navigate({
      search: (prev) => ({
        ...prev,
        section: section,
      }),
    })
  }

  return <DatabaseOverview role="super_admin" onCardClick={handleCardClick} />
}
