import { useEffect, useState } from 'react'
import { schoolService } from '@/services/schoolService'

export function SubdomainTest() {
  const [info, setInfo] = useState({
    hostname: '',
    isSubdomain: false,
    schoolName: '',
    fullUrl: '',
  })

  useEffect(() => {
    // Get information about the current URL
    const hostname = window.location.hostname
    const isSubdomain = schoolService.isSubdomainUrl()
    const schoolName = schoolService.getCurrentSchoolFromSubdomain() || 'none'
    const fullUrl = window.location.href

    setInfo({
      hostname,
      isSubdomain,
      schoolName,
      fullUrl,
    })
  }, [])

  return (
    <div className="p-4 bg-gray-100 rounded-lg mb-4">
      <h2 className="text-lg font-bold mb-2">Subdomain Access Test</h2>
      <div className="space-y-1 text-sm font-mono">
        <p>
          <span className="font-bold">Hostname:</span> {info.hostname}
        </p>
        <p>
          <span className="font-bold">Is Subdomain:</span>{' '}
          {info.isSubdomain ? 'Yes' : 'No'}
        </p>
        <p>
          <span className="font-bold">School Name from Subdomain:</span>{' '}
          {info.schoolName}
        </p>
        <p>
          <span className="font-bold">Full URL:</span> {info.fullUrl}
        </p>
      </div>
    </div>
  )
}
