import { SchoolData } from '@/routes/school/$schoolName'
import { useState, useEffect } from 'react'
import {
  Facebook,
  Instagram,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Twitter,
} from 'lucide-react'
import { SubdomainTest } from './SubdomainTest'
import { SubdomainDebug } from './SubdomainDebug'
import CdnImage from '@/components/shared/CdnImage'
import { SubdomainDebugger } from '@/components/SubdomainDebugger'
import { useSEO } from '@/contexts/SEOContext'
import { generateSchoolStructuredData } from '@/utils/structuredData'

interface SchoolLandingPageProps {
  school: SchoolData
}

export function SchoolLandingPage({ school }: SchoolLandingPageProps) {
  const [logoError, setLogoError] = useState(false)
  const [showDebug, setShowDebug] = useState(false)
  const { updateSEO } = useSEO()

  // Reset logo error state if school changes
  useEffect(() => {
    setLogoError(false)
  }, [school.logo])

  // Update SEO metadata when school data changes
  useEffect(() => {
    // Get the current hostname for canonical URL
    const hostname = window.location.hostname
    const subdomain = hostname.split('.')[0]

    // Generate structured data for the school
    const structuredData = generateSchoolStructuredData({
      name: school.name,
      description:
        school.description || `${school.name} - K12 Educational Institution`,
      url: `https://${subdomain}.jeridschool.tech`,
      logo: school.logo,
      telephone: school.contactPhone,
      email: school.contactEmail,
      address: {
        streetAddress: school.address,
      },
    })

    // Update SEO metadata
    updateSEO({
      title: `${school.name} - K12 Education Management | JeridSchool`,
      description:
        school.description ||
        `${school.name} - A leading K12 educational institution using JeridSchool management system`,
      keywords: `${school.name}, K12 education, school management, education institution, student management`,
      ogImage:
        school.logo ||
        school.heroImage ||
        'https://jeridschool.tech/og-images/school-default.jpg',
      ogUrl: `https://${subdomain}.jeridschool.tech`,
      canonicalUrl: `https://${subdomain}.jeridschool.tech`,
      structuredData,
    })
  }, [school, updateSEO])

  // Enable debug mode with keyboard shortcut (Ctrl+Shift+D)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setShowDebug((prev) => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  return (
    <div className="min-h-screen bg-white">
      {/* Debug Information (hidden by default) */}
      {showDebug && (
        <div className="container mx-auto px-4 py-2">
          <SubdomainDebugger />
          <SubdomainDebug />
          <SubdomainTest />
        </div>
      )}

      {/* Header */}
      <header className="sticky top-0 z-50 bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            {school.logo && !logoError ? (
              <CdnImage
                src={school.logo}
                alt={`${school.name} Logo`}
                className="h-10 w-auto object-contain"
              />
            ) : (
              <div className="h-10 px-3 bg-gray-200 rounded flex items-center justify-center">
                <span className="text-gray-700 font-semibold">
                  {school.name}
                </span>
              </div>
            )}
          </div>
          <nav className="hidden md:flex space-x-8">
            <a
              href="#home"
              className="text-gray-700 hover:text-primary font-medium"
            >
              Home
            </a>
            <a
              href="#about"
              className="text-gray-700 hover:text-primary font-medium"
            >
              About
            </a>
            <a
              href="#stats"
              className="text-gray-700 hover:text-primary font-medium"
            >
              Success
            </a>
            <a
              href="#gallery"
              className="text-gray-700 hover:text-primary font-medium"
            >
              Gallery
            </a>
            <a
              href="#contact"
              className="text-gray-700 hover:text-primary font-medium"
            >
              Contact
            </a>
          </nav>
          <div className="md:hidden">
            <button className="text-gray-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section id="home" className="relative bg-gray-100 py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Welcome to {school.name}
              </h1>
              <p className="text-lg text-gray-700 mb-8">
                {school.description ||
                  `A place where education meets excellence.`}
              </p>
              <div className="flex space-x-4">
                <a
                  href="#contact"
                  className="px-6 py-3 bg-primary text-white font-medium rounded-md hover:bg-primary-dark transition-colors"
                >
                  Contact Us
                </a>
                <a
                  href="#about"
                  className="px-6 py-3 border border-primary text-primary font-medium rounded-md hover:bg-primary hover:text-white transition-colors"
                >
                  Learn More
                </a>
              </div>
            </div>
            <div className="md:w-1/2">
              <CdnImage
                src={
                  school.heroImage ||
                  `https://placehold.co/800x600?text=${encodeURIComponent(school.name)}`
                }
                alt={`${school.name} Campus`}
                className="rounded-lg shadow-lg w-full h-auto"
                onError={() => {
                  const fallbackSrc = `https://placehold.co/800x600?text=${encodeURIComponent(school.name)}`
                  const imgElements = document.querySelectorAll(
                    `img[alt="${school.name} Campus"]`
                  )
                  if (imgElements.length > 0) {
                    ;(imgElements[0] as HTMLImageElement).src = fallbackSrc
                  }
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">About Us</h2>
            <div className="w-20 h-1 bg-primary mx-auto"></div>
          </div>
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <p className="text-gray-700 mb-4">
                {school.description ||
                  `At ${school.name}, we believe in providing a nurturing environment where students can thrive academically, socially, and emotionally. Our dedicated faculty and staff are committed to excellence in education and to helping each student reach their full potential.`}
              </p>
              <p className="text-gray-700 mb-4">
                Founded with a vision to create a dynamic learning community, we
                offer a comprehensive curriculum that prepares students for
                success in an ever-changing world.
              </p>
              <h3 className="text-xl font-semibold text-gray-900 mt-6 mb-3">
                Our Services
              </h3>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {school.services &&
                  school.services.map((service, index) => (
                    <li key={index} className="flex items-center">
                      <svg
                        className="h-5 w-5 text-primary mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>{service}</span>
                    </li>
                  ))}
              </ul>
            </div>
            <div className="md:w-1/2 md:pl-8">
              <div className="bg-gray-100 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Why Choose Us?
                </h3>
                <div className="space-y-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                        <svg
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-medium text-gray-900">
                        Safe Environment
                      </h4>
                      <p className="mt-2 text-gray-600">
                        We prioritize student safety and well-being in all
                        aspects of school life.
                      </p>
                    </div>
                  </div>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                        <svg
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-medium text-gray-900">
                        Quality Education
                      </h4>
                      <p className="mt-2 text-gray-600">
                        Our curriculum is designed to challenge and inspire
                        students to excel.
                      </p>
                    </div>
                  </div>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                        <svg
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-medium text-gray-900">
                        Dedicated Faculty
                      </h4>
                      <p className="mt-2 text-gray-600">
                        Our teachers are experienced professionals committed to
                        student success.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section id="stats" className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Success in Numbers</h2>
            <div className="w-20 h-1 bg-white mx-auto"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-5xl font-bold mb-2">
                {school.stats?.students || '1000'}+
              </div>
              <div className="text-xl">Students</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-2">
                {school.stats?.teachers || '50'}+
              </div>
              <div className="text-xl">Teachers</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-2">
                {school.stats?.courses || '30'}+
              </div>
              <div className="text-xl">Courses</div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-2">
                {school.stats?.awards || '20'}+
              </div>
              <div className="text-xl">Awards</div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              School Gallery
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto"></div>
            <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
              Take a visual tour of our campus, facilities, and student
              activities.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {(
              school.galleryImages || [
                'https://placehold.co/600x400?text=Gallery+Image+1',
                'https://placehold.co/600x400?text=Gallery+Image+2',
                'https://placehold.co/600x400?text=Gallery+Image+3',
                'https://placehold.co/600x400?text=Gallery+Image+4',
                'https://placehold.co/600x400?text=Gallery+Image+5',
                'https://placehold.co/600x400?text=Gallery+Image+6',
              ]
            ).map((image, index) => (
              <div
                key={index}
                className="overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
              >
                <CdnImage
                  src={image}
                  alt={`${school.name} Gallery Image ${index + 1}`}
                  className="w-full h-64 object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Contact Us
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto"></div>
            <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
              Have questions? We're here to help. Reach out to us using the
              contact information below.
            </p>
          </div>
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Send us a message
                </h3>
                <form className="space-y-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      placeholder="Your name"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      placeholder="Your email"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      placeholder="Subject"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Message
                    </label>
                    <textarea
                      id="message"
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      placeholder="Your message"
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full px-6 py-3 bg-primary text-white font-medium rounded-md hover:bg-primary-dark transition-colors"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            </div>
            <div className="md:w-1/2 md:pl-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Contact Information
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <MapPin className="h-6 w-6 text-primary mr-3 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Address</h4>
                      <p className="text-gray-600">{school.address}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Phone className="h-6 w-6 text-primary mr-3 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Phone</h4>
                      <p className="text-gray-600">
                        {school.contactPhone || 'Contact phone not available'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Mail className="h-6 w-6 text-primary mr-3 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Email</h4>
                      <p className="text-gray-600">
                        {school.contactEmail || 'Contact email not available'}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-8">
                  <h4 className="font-medium text-gray-900 mb-3">Follow Us</h4>
                  <div className="flex space-x-4">
                    {school.socialLinks?.facebook && (
                      <a
                        href={school.socialLinks.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-primary"
                      >
                        <Facebook className="h-6 w-6" />
                      </a>
                    )}
                    {school.socialLinks?.twitter && (
                      <a
                        href={school.socialLinks.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-primary"
                      >
                        <Twitter className="h-6 w-6" />
                      </a>
                    )}
                    {school.socialLinks?.instagram && (
                      <a
                        href={school.socialLinks.instagram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-primary"
                      >
                        <Instagram className="h-6 w-6" />
                      </a>
                    )}
                    {school.socialLinks?.linkedin && (
                      <a
                        href={school.socialLinks.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-primary"
                      >
                        <Linkedin className="h-6 w-6" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
              <div className="mt-6 bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  School Hours
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monday - Friday</span>
                    <span className="font-medium">8:00 AM - 3:30 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Saturday</span>
                    <span className="font-medium">9:00 AM - 12:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sunday</span>
                    <span className="font-medium">Closed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-8 md:mb-0">
              <div className="flex items-center space-x-2 mb-4">
                {school.logo ? (
                  <CdnImage
                    src={school.logo}
                    alt={`${school.name} Logo`}
                    className="h-8 w-auto brightness-0 invert"
                  />
                ) : (
                  <span className="text-xl font-bold">{school.name}</span>
                )}
              </div>
              <p className="text-gray-400 max-w-md">
                {school.description?.substring(0, 120) ||
                  `${school.name} is dedicated to providing quality education in a nurturing environment.`}
                {school.description && school.description.length > 120
                  ? '...'
                  : ''}
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                <ul className="space-y-2">
                  <li>
                    <a
                      href="#home"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <a
                      href="#about"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      About Us
                    </a>
                  </li>
                  <li>
                    <a
                      href="#gallery"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      Gallery
                    </a>
                  </li>
                  <li>
                    <a
                      href="#contact"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      Contact
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Services</h3>
                <ul className="space-y-2">
                  {(school.services || []).slice(0, 4).map((service, index) => (
                    <li key={index}>
                      <a
                        href="#about"
                        className="text-gray-400 hover:text-white transition-colors"
                      >
                        {service}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="col-span-2 md:col-span-1">
                <h3 className="text-lg font-semibold mb-4">Contact</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <MapPin className="h-5 w-5 text-gray-400 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-400">{school.address}</span>
                  </li>
                  {school.contactPhone && (
                    <li className="flex items-center">
                      <Phone className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" />
                      <span className="text-gray-400">
                        {school.contactPhone}
                      </span>
                    </li>
                  )}
                  {school.contactEmail && (
                    <li className="flex items-center">
                      <Mail className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" />
                      <span className="text-gray-400">
                        {school.contactEmail}
                      </span>
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} {school.name}. All rights
              reserved.
            </p>
            <div className="flex space-x-4">
              {school.socialLinks?.facebook && (
                <a
                  href={school.socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white"
                >
                  <Facebook className="h-5 w-5" />
                </a>
              )}
              {school.socialLinks?.twitter && (
                <a
                  href={school.socialLinks.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white"
                >
                  <Twitter className="h-5 w-5" />
                </a>
              )}
              {school.socialLinks?.instagram && (
                <a
                  href={school.socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white"
                >
                  <Instagram className="h-5 w-5" />
                </a>
              )}
              {school.socialLinks?.linkedin && (
                <a
                  href={school.socialLinks.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
              )}
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
