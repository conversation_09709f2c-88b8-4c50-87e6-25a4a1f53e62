import { useState, useEffect } from 'react'
import { schoolService } from '@/services/schoolService'
import axios from 'axios'
import { getProxiedPath } from '@/lib/corsProxy'

const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:3000'
const IS_DEV = import.meta.env.DEV

export function SubdomainDebug() {
  const [info, setInfo] = useState({
    hostname: '',
    isSubdomain: false,
    schoolName: '',
    fullUrl: '',
    apiBaseUrl: API_BASE_URL,
    authToken: 'Not available',
  })

  const [apiStatus, setApiStatus] = useState({
    publicEndpoint: 'Not tested',
    protectedEndpoint: 'Not tested',
    protectedEndpointWithAuth: 'Not tested',
  })

  useEffect(() => {
    // Get information about the current URL
    const hostname = window.location.hostname
    const isSubdomain = schoolService.isSubdomainUrl()
    const schoolName = schoolService.getCurrentSchoolFromSubdomain() || 'none'
    const fullUrl = window.location.href
    const token = localStorage.getItem('access_token')

    setInfo({
      hostname,
      isSubdomain,
      schoolName,
      fullUrl,
      apiBaseUrl: API_BASE_URL,
      authToken: token ? 'Available' : 'Not available',
    })

    // Test the API endpoints
    const testEndpoints = async () => {
      // Extract school name from hostname or URL
      let testSchoolName = ''
      if (hostname.includes('.localhost')) {
        testSchoolName = hostname.split('.')[0]
      } else if (window.location.pathname.includes('/school/')) {
        testSchoolName = window.location.pathname.split('/school/')[1]
      } else {
        testSchoolName = 'test'
      }

      // Test public endpoint
      try {
        // Construct the endpoint URL
        const publicEndpointPath = `/public/etablissement/url/${testSchoolName}`

        // Use the proxy in development mode
        const publicRequestUrl = IS_DEV
          ? getProxiedPath(publicEndpointPath, 'api')
          : `${API_BASE_URL}${publicEndpointPath}`

        console.log('Testing public endpoint:', publicRequestUrl)

        await axios.get(publicRequestUrl, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          }
        })
        setApiStatus((prev) => ({ ...prev, publicEndpoint: 'Accessible' }))
      } catch (error: any) {
        console.error('Public endpoint error:', error)
        setApiStatus((prev) => ({
          ...prev,
          publicEndpoint: `Error: ${error.response?.status || 'Unknown'} - ${error.message}`,
        }))
      }

      // Test protected endpoint without auth
      try {
        // Construct the endpoint URL
        const protectedEndpointPath = `/etablissement/url/${testSchoolName}`

        // Use the proxy in development mode
        const protectedRequestUrl = IS_DEV
          ? getProxiedPath(protectedEndpointPath, 'api')
          : `${API_BASE_URL}${protectedEndpointPath}`

        console.log('Testing protected endpoint (no auth):', protectedRequestUrl)

        await axios.get(protectedRequestUrl, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          }
        })
        setApiStatus((prev) => ({ ...prev, protectedEndpoint: 'Accessible' }))
      } catch (error: any) {
        console.error('Protected endpoint error:', error)
        setApiStatus((prev) => ({
          ...prev,
          protectedEndpoint: `Error: ${error.response?.status || 'Unknown'} - ${error.message}`,
        }))
      }

      // Test protected endpoint with auth if token is available
      if (token) {
        try {
          // Construct the endpoint URL
          const authEndpointPath = `/etablissement/url/${testSchoolName}`

          // Use the proxy in development mode
          const authRequestUrl = IS_DEV
            ? getProxiedPath(authEndpointPath, 'api')
            : `${API_BASE_URL}${authEndpointPath}`

          console.log('Testing protected endpoint (with auth):', authRequestUrl)

          await axios.get(authRequestUrl, {
            headers: {
              Authorization: `Bearer ${token}`,
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
          })
          setApiStatus((prev) => ({
            ...prev,
            protectedEndpointWithAuth: 'Accessible',
          }))
        } catch (error: any) {
          console.error('Protected endpoint with auth error:', error)
          setApiStatus((prev) => ({
            ...prev,
            protectedEndpointWithAuth: `Error: ${error.response?.status || 'Unknown'} - ${error.message}`,
          }))
        }
      } else {
        setApiStatus((prev) => ({
          ...prev,
          protectedEndpointWithAuth: 'No auth token available',
        }))
      }
    }

    testEndpoints()
  }, [])

  return (
    <div className="p-4 bg-gray-100 rounded-lg mb-4">
      <h2 className="text-lg font-bold mb-2">Subdomain Debug Info</h2>
      <div className="space-y-1 text-sm font-mono">
        <p>
          <span className="font-bold">Hostname:</span> {info.hostname}
        </p>
        <p>
          <span className="font-bold">Is Subdomain:</span>{' '}
          {info.isSubdomain ? 'Yes' : 'No'}
        </p>
        <p>
          <span className="font-bold">School Name from Subdomain:</span>{' '}
          {info.schoolName}
        </p>
        <p>
          <span className="font-bold">Full URL:</span> {info.fullUrl}
        </p>
        <p>
          <span className="font-bold">API Base URL:</span> {info.apiBaseUrl}
        </p>
        <p>
          <span className="font-bold">Auth Token:</span> {info.authToken}
        </p>
        <div className="mt-2 pt-2 border-t border-gray-300">
          <p className="font-bold">API Endpoint Status:</p>
          <p>
            <span className="font-bold">Public Endpoint:</span>{' '}
            {apiStatus.publicEndpoint}
          </p>
          <p>
            <span className="font-bold">Protected Endpoint (No Auth):</span>{' '}
            {apiStatus.protectedEndpoint}
          </p>
          <p>
            <span className="font-bold">Protected Endpoint (With Auth):</span>{' '}
            {apiStatus.protectedEndpointWithAuth}
          </p>
        </div>
      </div>
    </div>
  )
}
