import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Users, GraduationCap, BookOpen, Award } from 'lucide-react'

interface Stats {
  students?: number
  teachers?: number
  courses?: number
  awards?: number
  [key: string]: number | undefined
}

interface StatsEditorProps {
  value: Stats
  onChange: (stats: Stats) => void
}

const StatsEditor: React.FC<StatsEditorProps> = ({ value = {}, onChange }) => {
  const [stats, setStats] = useState<Stats>(
    value || {
      students: 0,
      teachers: 0,
      courses: 0,
      awards: 0,
    }
  )

  // Update local state when value prop changes
  useEffect(() => {
    setStats(
      value || {
        students: 0,
        teachers: 0,
        courses: 0,
        awards: 0,
      }
    )
  }, [value])

  const handleChange = (key: string, value: string) => {
    // Convert to number and ensure it's not negative
    const numValue = Math.max(0, parseInt(value) || 0)
    const newStats = { ...stats, [key]: numValue }
    setStats(newStats)
    // Only update the parent form state without triggering a save
    onChange(newStats)
  }

  return (
    <div className="space-y-3">
      <Label>School Statistics</Label>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Students */}
        <div className="flex items-center space-x-2">
          <div className="bg-blue-600 p-2 rounded-md">
            <Users className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <Label htmlFor="students" className="text-xs">
              Students
            </Label>
            <Input
              id="students"
              type="number"
              min="0"
              placeholder="Number of students"
              value={stats.students || 0}
              onChange={(e) => handleChange('students', e.target.value)}
              className="mt-1"
            />
          </div>
        </div>

        {/* Teachers */}
        <div className="flex items-center space-x-2">
          <div className="bg-green-600 p-2 rounded-md">
            <GraduationCap className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <Label htmlFor="teachers" className="text-xs">
              Teachers
            </Label>
            <Input
              id="teachers"
              type="number"
              min="0"
              placeholder="Number of teachers"
              value={stats.teachers || 0}
              onChange={(e) => handleChange('teachers', e.target.value)}
              className="mt-1"
            />
          </div>
        </div>

        {/* Courses */}
        <div className="flex items-center space-x-2">
          <div className="bg-amber-600 p-2 rounded-md">
            <BookOpen className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <Label htmlFor="courses" className="text-xs">
              Courses
            </Label>
            <Input
              id="courses"
              type="number"
              min="0"
              placeholder="Number of courses"
              value={stats.courses || 0}
              onChange={(e) => handleChange('courses', e.target.value)}
              className="mt-1"
            />
          </div>
        </div>

        {/* Awards */}
        <div className="flex items-center space-x-2">
          <div className="bg-purple-600 p-2 rounded-md">
            <Award className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <Label htmlFor="awards" className="text-xs">
              Awards
            </Label>
            <Input
              id="awards"
              type="number"
              min="0"
              placeholder="Number of awards"
              value={stats.awards || 0}
              onChange={(e) => handleChange('awards', e.target.value)}
              className="mt-1"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatsEditor
