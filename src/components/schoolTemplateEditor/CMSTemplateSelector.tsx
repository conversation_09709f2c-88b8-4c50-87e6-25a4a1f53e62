import React, { useState } from 'react'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent } from '@/components/ui/card'

// Define CMS templates
const cmsTemplates = [
  {
    id: 'welcome',
    name: 'Welcome Template',
    preview: 'Welcome to our school - A place for learning and growth',
    content: `<div class="cms-content">
      <h1>Welcome to Our School</h1>
      <p>A premier educational institution dedicated to excellence.</p>
      <div class="features">
        <div class="feature">
          <h3>Academic Excellence</h3>
          <p>Our curriculum is designed to challenge and inspire students to reach their full potential.</p>
        </div>
        <div class="feature">
          <h3>Dedicated Faculty</h3>
          <p>Our teachers are experienced professionals committed to student success.</p>
        </div>
        <div class="feature">
          <h3>Modern Facilities</h3>
          <p>Our campus features state-of-the-art classrooms, laboratories, and recreational areas.</p>
        </div>
      </div>
    </div>`,
  },
  {
    id: 'about',
    name: 'About Us Template',
    preview: 'About our school - Our history, mission and values',
    content: `<div class="cms-content">
      <h1>About Our School</h1>
      <p>Founded in 2010, our school has a rich history of academic excellence and community engagement.</p>
      <h2>Our Mission</h2>
      <p>To provide a nurturing environment where students can excel academically and develop into well-rounded individuals.</p>
      <h2>Our Values</h2>
      <ul>
        <li><strong>Excellence:</strong> We strive for excellence in all we do.</li>
        <li><strong>Integrity:</strong> We act with honesty and ethical behavior.</li>
        <li><strong>Respect:</strong> We value diversity and treat everyone with dignity.</li>
        <li><strong>Innovation:</strong> We embrace new ideas and approaches to learning.</li>
      </ul>
    </div>`,
  },
  {
    id: 'programs',
    name: 'Programs Template',
    preview: 'Our educational programs and curriculum',
    content: `<div class="cms-content">
      <h1>Our Educational Programs</h1>
      <p>We offer comprehensive programs designed to meet the needs of all students.</p>
      <div class="programs">
        <div class="program">
          <h3>Elementary Education</h3>
          <p>A strong foundation in core subjects with emphasis on creativity and critical thinking.</p>
        </div>
        <div class="program">
          <h3>Middle School</h3>
          <p>Expanding knowledge and developing independent learning skills.</p>
        </div>
        <div class="program">
          <h3>High School</h3>
          <p>College preparatory curriculum with advanced placement options.</p>
        </div>
        <div class="program">
          <h3>Special Programs</h3>
          <p>Including arts, athletics, STEM, and language immersion.</p>
        </div>
      </div>
    </div>`,
  },
  {
    id: 'custom',
    name: 'Custom Template',
    preview: 'Create your own custom content',
    content: '',
  },
]

interface CMSTemplateSelectorProps {
  value: string
  onChange: (value: string) => void
  onCustomChange?: (value: string) => void
}

const CMSTemplateSelector: React.FC<CMSTemplateSelectorProps> = ({
  value,
  onChange,
  onCustomChange,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState(() => {
    // Find if the current value matches any template
    const template = cmsTemplates.find((t) => t.content === value)
    return template ? template.id : 'custom'
  })

  const [customContent, setCustomContent] = useState(() => {
    // If no template matches or custom is selected, use the current value
    const template = cmsTemplates.find((t) => t.content === value)
    return template ? '' : value
  })

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)

    const template = cmsTemplates.find((t) => t.id === templateId)
    if (template) {
      if (templateId === 'custom') {
        // For custom template, keep the current content
        onChange(customContent)
      } else {
        // For predefined templates, use the template content
        onChange(template.content)
      }
    }
  }

  const handleCustomContentChange = (content: string) => {
    setCustomContent(content)
    if (selectedTemplate === 'custom' && onCustomChange) {
      onCustomChange(content)
    }
  }

  return (
    <div className="space-y-4">
      <Label>Select CMS Template</Label>
      <RadioGroup
        value={selectedTemplate}
        onValueChange={handleTemplateChange}
        className="space-y-2"
      >
        <ScrollArea className="h-60 w-full border rounded-md p-4">
          <div className="space-y-4">
            {cmsTemplates.map((template) => (
              <Card
                key={template.id}
                className={`cursor-pointer transition-all ${selectedTemplate === template.id ? 'border-primary' : 'border-gray-200'}`}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-2">
                    <RadioGroupItem
                      value={template.id}
                      id={`template-${template.id}`}
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor={`template-${template.id}`}
                        className="font-medium cursor-pointer"
                      >
                        {template.name}
                      </Label>
                      <p className="text-sm text-gray-500 mt-1">
                        {template.preview}
                      </p>

                      {template.id === 'custom' &&
                        selectedTemplate === 'custom' && (
                          <textarea
                            className="w-full mt-2 p-2 border rounded-md min-h-[100px]"
                            value={customContent}
                            onChange={(e) =>
                              handleCustomContentChange(e.target.value)
                            }
                            placeholder="Enter your custom HTML content here"
                          />
                        )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </RadioGroup>

      {selectedTemplate !== 'custom' && (
        <div className="mt-4">
          <Label>Template Preview</Label>
          <div className="border rounded-md p-4 mt-2 bg-gray-50">
            <div
              className="prose max-w-none"
              dangerouslySetInnerHTML={{
                __html:
                  cmsTemplates.find((t) => t.id === selectedTemplate)
                    ?.content || '',
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default CMSTemplateSelector
