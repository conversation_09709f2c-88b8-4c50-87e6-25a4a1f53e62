import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Plus, X } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import FileUploader from '../shared/FileUploader'

interface GalleryImagesUploaderProps {
  value: string[]
  onChange: (urls: string[]) => void
  maxImages?: number
}

export default function SimpleGalleryUploader({
  value = [],
  onChange,
  maxImages = 10,
}: GalleryImagesUploaderProps) {
  const [images, setImages] = useState<string[]>(value)
  const [isAddingImage, setIsAddingImage] = useState(false)

  // Handle adding a new image
  const handleAddImage = (url: string) => {
    try {
      console.log('Adding image:', url)

      // Create a copy of the current images array
      const newImages = [...images, url]

      // Update the local state
      setImages(newImages)

      // Update the form state
      onChange(newImages)

      // Reset the adding state
      setIsAddingImage(false)

      console.log('Image added successfully')
    } catch (error) {
      console.error('Error adding image:', error)
    }
  }

  // Handle removing an image
  const handleRemoveImage = (index: number, e?: React.MouseEvent) => {
    try {
      // Prevent default form submission if event is provided
      if (e) {
        e.preventDefault()
        e.stopPropagation()

        // Stop the event from bubbling up to parent elements
        if (e.nativeEvent) {
          e.nativeEvent.stopImmediatePropagation()
        }
      }

      console.log('Removing image at index:', index)

      // Create a copy of the current images array
      const newImages = [...images]

      // Remove the image at the specified index
      newImages.splice(index, 1)

      // Update the local state
      setImages(newImages)

      // Update the form state
      onChange(newImages)

      // Prevent any potential form submission
      setTimeout(() => {
        // Blur any active element to prevent accidental form submission
        const activeElement = document.activeElement as HTMLElement
        if (activeElement && activeElement.blur) {
          activeElement.blur()
        }
      }, 0)

      console.log('Image removed successfully')
    } catch (error) {
      console.error('Error removing image:', error)
    }

    // Return false to prevent default behavior
    return false
  }

  return (
    <div className="space-y-2">
      <Alert className="bg-blue-50 border-blue-200 mb-2">
        <AlertDescription>
          Changes to gallery images will only be saved when you submit the form.
        </AlertDescription>
      </Alert>

      <div className="flex items-center justify-between">
        <div className="font-medium">Gallery Images</div>
        {!isAddingImage && images.length < maxImages && (
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(true)
            }}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Image
          </Button>
        )}
      </div>

      {isAddingImage ? (
        <div className="border rounded-md p-4 bg-gray-50">
          <FileUploader
            label="Upload Gallery Image"
            onFileUploaded={handleAddImage}
            onError={() => {}}
            isAvatar={false}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(false)
            }}
            className="mt-2"
          >
            Cancel
          </Button>
        </div>
      ) : images.length === 0 ? (
        <div className="border rounded-md p-8 flex flex-col items-center justify-center text-center">
          <p className="text-gray-500 mb-4">No images added yet</p>
          <Button
            variant="outline"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(true)
            }}
            className="mt-2"
          >
            Add your first image
          </Button>
        </div>
      ) : (
        <ScrollArea className="h-60 border rounded-md p-2">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image}
                  alt={`Gallery image ${index + 1}`}
                  className="h-32 w-full object-cover rounded-md"
                  onError={(e) => {
                    ;(e.target as HTMLImageElement).src =
                      'https://placehold.co/400x300?text=Image+Error'
                  }}
                />
                <div
                  className="absolute top-1 right-1 h-6 w-6 bg-red-500 rounded-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                  onClick={(e) => handleRemoveImage(index, e)}
                >
                  <X className="h-4 w-4 text-white" />
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  )
}
