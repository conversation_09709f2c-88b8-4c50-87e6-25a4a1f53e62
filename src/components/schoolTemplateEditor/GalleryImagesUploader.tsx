import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, Plus, Image as ImageIcon, Info } from 'lucide-react'
import FileUploader from '../shared/FileUploader'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface GalleryImagesUploaderProps {
  value: string[]
  onChange: (urls: string[]) => void
  maxImages?: number
}

const GalleryImagesUploader: React.FC<GalleryImagesUploaderProps> = ({
  value = [],
  onChange,
  maxImages = 10,
}) => {
  const [images, setImages] = useState<string[]>(value || [])
  const [isAddingImage, setIsAddingImage] = useState(false)
  const [imageToDelete, setImageToDelete] = useState<number | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Update local state when value prop changes
  useEffect(() => {
    setImages(value || [])
  }, [value])

  const handleAddImage = (url: string) => {
    // Create a copy of the current images array and add the new URL
    const newImages = [...images, url]
    // Update the local state
    setImages(newImages)
    // Update the form state (this should not trigger a save until form submission)
    onChange(newImages)
    // Close the add image form
    setIsAddingImage(false)

    // Prevent any potential form submission
    setTimeout(() => {
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && activeElement.blur) {
        activeElement.blur()
      }
    }, 0)
  }

  const handleRemoveImage = (e?: React.MouseEvent) => {
    try {
      // Prevent default form submission if event is provided
      if (e) {
        e.preventDefault()
        e.stopPropagation()

        // Stop the event from bubbling up to parent elements
        if (e.nativeEvent) {
          e.nativeEvent.stopImmediatePropagation()
        }
      }

      if (imageToDelete === null) return false

      console.log('Removing image at index:', imageToDelete)

      // Create a copy of the current images array
      const newImages = [...images]
      // Remove the image at the specified index
      newImages.splice(imageToDelete, 1)
      // Update the local state
      setImages(newImages)
      // Update the form state (this should not trigger a save until form submission)
      onChange(newImages)
      // Reset state
      setImageToDelete(null)
      setIsDeleteDialogOpen(false)

      // Prevent any potential form submission
      setTimeout(() => {
        // Blur any active element to prevent accidental form submission
        const activeElement = document.activeElement as HTMLElement
        if (activeElement && activeElement.blur) {
          activeElement.blur()
        }

        // Ensure we're not navigating away
        if (e) {
          e.preventDefault()
          e.stopPropagation()
        }
      }, 0)

      console.log('Image removed successfully')

      // Return false to prevent default behavior
      return false
    } catch (error) {
      console.error('Error removing image:', error)
      return false
    }
  }

  return (
    <div className="space-y-2">
      <Alert className="bg-blue-50 border-blue-200 mb-2">
        <div className="flex items-center gap-2">
          <Info className="h-4 w-4 text-blue-500" />
          <AlertDescription className="text-sm text-blue-700">
            Changes to gallery images will only be saved when you submit the
            form.
          </AlertDescription>
        </div>
      </Alert>
      <div className="flex items-center justify-between">
        <Label>Gallery Images</Label>
        {images.length < maxImages && !isAddingImage && (
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(true)
            }}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Add Image
          </Button>
        )}
      </div>

      {isAddingImage ? (
        <div className="border rounded-md p-4 bg-gray-50">
          <FileUploader
            label="Upload Gallery Image"
            onFileUploaded={handleAddImage}
            onError={() => {}}
            isAvatar={false}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(false)
            }}
            className="mt-2"
          >
            Cancel
          </Button>
        </div>
      ) : images.length === 0 ? (
        <div className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center text-gray-400">
          <ImageIcon className="h-10 w-10 mb-2" />
          <p className="text-sm">No gallery images added yet</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsAddingImage(true)
            }}
            className="mt-2"
          >
            Add your first image
          </Button>
        </div>
      ) : (
        <ScrollArea className="h-60 border rounded-md p-2">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image}
                  alt={`Gallery image ${index + 1}`}
                  className="h-32 w-full object-cover rounded-md"
                  onError={(e) => {
                    ;(e.target as HTMLImageElement).src =
                      'https://placehold.co/400x300?text=Image+Error'
                  }}
                />
                <div
                  className="absolute top-1 right-1 h-6 w-6 bg-red-500 rounded-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    if (e.nativeEvent) {
                      e.nativeEvent.stopImmediatePropagation()
                    }
                    // Directly set the state instead of using a function
                    setImageToDelete(index)
                    setIsDeleteDialogOpen(true)
                    return false
                  }}
                >
                  <X className="h-4 w-4 text-white" />
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}

      {/* Confirmation Dialog for Deleting Images */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={(open) => {
          // Prevent default behavior
          setTimeout(() => {
            setIsDeleteDialogOpen(open)
            if (!open) {
              setImageToDelete(null)
            }
          }, 0)
          return false
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the image from the gallery. The change will only
              be saved when you submit the form.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                if (e.nativeEvent) {
                  e.nativeEvent.stopImmediatePropagation()
                }
                setImageToDelete(null)
                return false
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                if (e.nativeEvent) {
                  e.nativeEvent.stopImmediatePropagation()
                }
                handleRemoveImage(e)
                return false
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              <div className="flex items-center gap-2">
                <X className="h-4 w-4" />
                Remove
              </div>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default GalleryImagesUploader
