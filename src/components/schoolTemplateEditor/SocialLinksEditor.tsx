import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Facebook, Twitter, Instagram, Linkedin, Info } from 'lucide-react'

interface SocialLinks {
  facebook?: string
  twitter?: string
  instagram?: string
  linkedin?: string
  [key: string]: string | undefined
}

interface SocialLinksEditorProps {
  value: SocialLinks
  onChange: (links: SocialLinks) => void
}

const SocialLinksEditor: React.FC<SocialLinksEditorProps> = ({
  value = {},
  onChange,
}) => {
  const [links, setLinks] = useState<SocialLinks>(
    value || {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: '',
    }
  )

  // Update local state when value prop changes
  useEffect(() => {
    setLinks(
      value || {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: '',
      }
    )
  }, [value])

  const handleChange = (platform: string, url: string) => {
    const newLinks = { ...links, [platform]: url }
    setLinks(newLinks)
    // Only update the parent form state without triggering a save
    onChange(newLinks)
  }

  return (
    <div className="space-y-3">
      <Alert className="bg-blue-50 border-blue-200 mb-2">
        <div className="flex items-center gap-2">
          <Info className="h-4 w-4 text-blue-500" />
          <AlertDescription className="text-sm text-blue-700">
            Changes to social links will only be saved when you submit the form.
          </AlertDescription>
        </div>
      </Alert>
      <Label>Social Media Links</Label>

      <div className="space-y-3">
        {/* Facebook */}
        <div className="flex items-center space-x-2">
          <div className="bg-blue-600 p-2 rounded-md">
            <Facebook className="h-4 w-4 text-white" />
          </div>
          <Input
            placeholder="Facebook URL"
            value={links.facebook || ''}
            onChange={(e) => handleChange('facebook', e.target.value)}
            className="flex-1"
          />
        </div>

        {/* Twitter */}
        <div className="flex items-center space-x-2">
          <div className="bg-sky-500 p-2 rounded-md">
            <Twitter className="h-4 w-4 text-white" />
          </div>
          <Input
            placeholder="Twitter URL"
            value={links.twitter || ''}
            onChange={(e) => handleChange('twitter', e.target.value)}
            className="flex-1"
          />
        </div>

        {/* Instagram */}
        <div className="flex items-center space-x-2">
          <div className="bg-pink-600 p-2 rounded-md">
            <Instagram className="h-4 w-4 text-white" />
          </div>
          <Input
            placeholder="Instagram URL"
            value={links.instagram || ''}
            onChange={(e) => handleChange('instagram', e.target.value)}
            className="flex-1"
          />
        </div>

        {/* LinkedIn */}
        <div className="flex items-center space-x-2">
          <div className="bg-blue-700 p-2 rounded-md">
            <Linkedin className="h-4 w-4 text-white" />
          </div>
          <Input
            placeholder="LinkedIn URL"
            value={links.linkedin || ''}
            onChange={(e) => handleChange('linkedin', e.target.value)}
            className="flex-1"
          />
        </div>
      </div>
    </div>
  )
}

export default SocialLinksEditor
