import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import { Calendar, RefreshCw} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useAttendance } from '@/hooks/useAttendance'
import { Attendance } from '@/lib/api/services/attendance-service'

// Define attendance status as string literals to handle all possible values
type AttendanceStatus = 'present' | 'absent' | 'late' | 'left_early'

// Define constants for attendance status values
const ATTENDANCE_STATUS = {
  PRESENT: 'present' as AttendanceStatus,
  ABSENT: 'absent' as AttendanceStatus,
  LATE: 'late' as AttendanceStatus,
  LEFT_EARLY: 'left_early' as AttendanceStatus,
}

// Function to get attendance status badge color
const getStatusBadgeColor = (status: AttendanceStatus) => {
  switch (status) {
    case ATTENDANCE_STATUS.PRESENT:
      return 'bg-green-100 text-green-800'
    case ATTENDANCE_STATUS.ABSENT:
      return 'bg-red-100 text-red-800'
    case ATTENDANCE_STATUS.LATE:
      return 'bg-yellow-100 text-yellow-800'
    case ATTENDANCE_STATUS.LEFT_EARLY:
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Function to get attendance status label
const getStatusLabel = (status: AttendanceStatus) => {
  switch (status) {
    case ATTENDANCE_STATUS.PRESENT:
      return 'Present'
    case ATTENDANCE_STATUS.ABSENT:
      return 'Absent'
    case ATTENDANCE_STATUS.LATE:
      return 'Late'
    case ATTENDANCE_STATUS.LEFT_EARLY:
      return 'Left Early'
    default:
      return status
  }
}

export default function ChildAttendanceView() {
  const { toast } = useToast()
  const [selectedChild, setSelectedChild] = useState<string>('')
  const [selectedDateRange, setSelectedDateRange] = useState<{
    startDate: string
    endDate: string
  }>({
    startDate: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    endDate: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
  })
  const [filteredRecords, setFilteredRecords] = useState<Attendance[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedRecord, setSelectedRecord] = useState<Attendance | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [attendanceSummary, setAttendanceSummary] = useState<{
    total: number
    present: number
    absent: number
    late: number
    leftEarly: number
    presentPercentage: number
  }>({
    total: 0,
    present: 0,
    absent: 0,
    late: 0,
    leftEarly: 0,
    presentPercentage: 0,
  })

  // Get parent ID from localStorage
  const parentId = localStorage.getItem('id') || ''

  // Get hooks from useAttendance
  const { useParentChildren, useChildAttendance } = useAttendance()

  // Fetch parent's children with React Query
  const {
    data: children = [],
    isLoading: isLoadingChildren,
    isError: isChildrenError,
    error: childrenError,
    refetch: refetchChildren
  } = useParentChildren(parentId)

  // Set the first child as selected when children data is loaded
  useEffect(() => {
    if (children.length > 0 && !selectedChild) {
      setSelectedChild(children[0].id)
    }
  }, [children, selectedChild])

  // Fetch attendance records for the selected child with React Query
  const {
    data: attendanceRecords = [],
    isLoading: isLoadingAttendance,
    isError: isAttendanceError,
    error: attendanceError,
    refetch: refetchAttendance
  } = useChildAttendance(
    selectedChild,
    selectedDateRange.startDate,
    selectedDateRange.endDate
  )

  // Show error toast if children data fetch fails
  useEffect(() => {
    if (isChildrenError && childrenError) {
      console.error('Error fetching children data:', childrenError)
      toast({
        title: 'Error',
        description: 'Could not fetch children data. Please try again later.',
        variant: 'destructive',
      })
    }
  }, [isChildrenError, childrenError, toast])

  // Show error toast if attendance data fetch fails
  useEffect(() => {
    if (isAttendanceError && attendanceError) {
      console.error('Error fetching attendance records:', attendanceError)
      toast({
        title: 'Error',
        description: 'Could not fetch attendance records. Please try again later.',
        variant: 'destructive',
      })
    }
  }, [isAttendanceError, attendanceError, toast])

  // Apply filters to attendance records and calculate statistics
  useEffect(() => {
    let filtered = [...attendanceRecords]

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter((record) => record.status === selectedStatus)
    }

    // Sort by date (newest first)
    filtered.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    )

    setFilteredRecords(filtered)

    // Calculate attendance summary
    const total = filtered.length
    const present = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.PRESENT
    ).length
    const absent = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.ABSENT
    ).length
    const late = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.LATE
    ).length
    const leftEarly = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.LEFT_EARLY
    ).length
    const presentPercentage =
      total > 0 ? Math.round((present / total) * 100) : 0

    setAttendanceSummary({
      total,
      present,
      absent,
      late,
      leftEarly,
      presentPercentage,
    })
  }, [attendanceRecords, selectedStatus])

  // Set date range to last 7 days
  const setLastWeek = () => {
    const end = new Date()
    const start = subDays(end, 7)

    setSelectedDateRange({
      startDate: format(start, 'yyyy-MM-dd'),
      endDate: format(end, 'yyyy-MM-dd'),
    })
  }

  // Set date range to current month
  const setCurrentMonth = () => {
    const now = new Date()

    setSelectedDateRange({
      startDate: format(startOfMonth(now), 'yyyy-MM-dd'),
      endDate: format(endOfMonth(now), 'yyyy-MM-dd'),
    })
  }

  // View attendance record details
  const viewRecordDetails = (record: Attendance) => {
    setSelectedRecord(record)
    setIsDialogOpen(true)
  }

  // Get the selected child's name
  const getSelectedChildName = () => {
    const child = children.find((c) => c.id === selectedChild)
    return child ? `${child.firstname} ${child.lastname}` : 'Select a child'
  }

  // Get the selected child's class name
  const getSelectedChildClassName = () => {
    const child = children.find((c) => c.id === selectedChild)
    if (!child) return ''

    // Get class name from child data
    return child.class?.name || ''
  }

  // Function to refresh data
  const refreshData = () => {
    refetchChildren()
    if (selectedChild) {
      refetchAttendance()
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center">
          Child Attendance Records
        </h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Attendance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          {children.length === 0 && !isLoadingChildren ? (
            <div className="text-center py-8">
              <h3 className="text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
              <Button onClick={refreshData} variant="outline" className="mt-4">
                Refresh Data
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center mb-4">
                  <label htmlFor="child-select" className="mr-2 font-medium">
                    Select Child:
                  </label>
                  <Select
                    value={selectedChild}
                    onValueChange={(value) => setSelectedChild(value)}
                    disabled={isLoadingChildren || children.length === 0}
                  >
                    <SelectTrigger className="w-[200px]">
                      <SelectValue
                        placeholder={isLoadingChildren ? 'Loading...' : 'Select a child'}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {children.map((child) => (
                        <SelectItem key={child.id} value={child.id}>
                          {child.firstname} {child.lastname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center mb-4">
                  <label htmlFor="date-range" className="mr-2 font-medium">
                    Date Range:
                  </label>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={setLastWeek}>
                      Last 7 Days
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={setCurrentMonth}
                    >
                      This Month
                    </Button>
                  </div>
                </div>

                <div className="text-sm text-gray-600 mb-4">
                  {format(new Date(selectedDateRange.startDate), 'MMM d, yyyy')}{' '}
                  - {format(new Date(selectedDateRange.endDate), 'MMM d, yyyy')}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-lg mb-2">
                  {getSelectedChildName()} - Attendance Stats
                </h3>
                {getSelectedChildClassName() && (
                  <p className="text-sm text-gray-600 mb-2">
                    Class: {getSelectedChildClassName()}
                  </p>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-3xl font-bold text-blue-600">
                      {attendanceSummary.presentPercentage}%
                    </div>
                    <div className="text-sm text-gray-600">Attendance Rate</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-gray-800">
                      {attendanceSummary.total}
                    </div>
                    <div className="text-sm text-gray-600">Total Sessions</div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-2 mt-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      {attendanceSummary.present}
                    </div>
                    <div className="text-xs text-gray-600">Present</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-red-600">
                      {attendanceSummary.absent}
                    </div>
                    <div className="text-xs text-gray-600">Absent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-600">
                      {attendanceSummary.late + attendanceSummary.leftEarly}
                    </div>
                    <div className="text-xs text-gray-600">Late/Left Early</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Attendance Records</CardTitle>
            {children.length > 0 && (
              <Select
                value={selectedStatus}
                onValueChange={(value) => setSelectedStatus(value)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.PRESENT}>
                    Present
                  </SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.ABSENT}>
                    Absent
                  </SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.LATE}>Late</SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.LEFT_EARLY}>
                    Left Early
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingChildren || isLoadingAttendance ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                Loading attendance records...
              </h3>
            </div>
          ) : isChildrenError || isAttendanceError ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-4">
                <h3 className="text-sm font-semibold">Error loading data</h3>
                <p className="text-xs">{String(childrenError || attendanceError)}</p>
              </div>
              <Button onClick={refreshData} variant="outline" className="mt-2">
                <RefreshCw className="mr-2 h-4 w-4" /> Try Again
              </Button>
            </div>
          ) : children.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No attendance records found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No attendance records found for the selected filters.
              </p>
            </div>
          ) : (
            <div>
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Class</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          {format(new Date(record.date), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell>{record.className}</TableCell>
                        <TableCell>{record.subject}</TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                              record.status as AttendanceStatus
                            )}`}
                          >
                            {getStatusLabel(record.status as AttendanceStatus)}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewRecordDetails(record)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attendance Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Attendance Details</DialogTitle>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium">Date</h3>
                  <p className="text-sm mt-1">
                    {format(new Date(selectedRecord.date), 'MMMM d, yyyy')}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Status</h3>
                  <p className="text-sm mt-1">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                        selectedRecord.status as AttendanceStatus
                      )}`}
                    >
                      {getStatusLabel(selectedRecord.status as AttendanceStatus)}
                    </span>
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Class</h3>
                  <p className="text-sm mt-1">{selectedRecord.className}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Subject</h3>
                  <p className="text-sm mt-1">{selectedRecord.subject}</p>
                </div>
              </div>

              {selectedRecord.notes && (
                <div>
                  <h3 className="text-sm font-medium">Notes</h3>
                  <p className="text-sm mt-1 p-2 bg-gray-50 rounded">
                    {selectedRecord.notes}
                  </p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}