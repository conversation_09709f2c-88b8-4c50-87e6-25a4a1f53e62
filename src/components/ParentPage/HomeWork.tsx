import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON> } from '@/components/ui/card'
import { Check, X } from 'lucide-react'

// Import the mock types
import { MockSubject, toMockSubject, MockHomework } from '@/interface/types'

// Sample data - replace with your actual data
const subjects: MockSubject[] = [
  toMockSubject({ id: 1, name: 'Mathematics' }),
  toMockSubject({ id: 2, name: 'Physics' }),
  toMockSubject({ id: 3, name: 'Chemistry' }),
]

const homeworkData: MockHomework[] = [
  {
    id: 1,
    subjectId: 1,
    title: 'Algebra Exercise Set 3',
    dueDate: '2024-12-28',
    description: 'Complete exercises 15-20 from Chapter 4',
    allowFileSubmission: true,
    allowedFileTypes: ['pdf', 'jpg', 'png'],
    status: 'pending', // pending, submitted, late
    teacherName: 'Dr. <PERSON>',
  },
  {
    id: 2,
    subjectId: 2,
    title: 'Physics Lab Report',
    dueDate: '2024-12-30',
    description: 'Write a report on the pendulum experiment',
    allowFileSubmission: true,
    allowedFileTypes: ['pdf'],
    status: 'submitted',
    teacherName: '<PERSON>. <PERSON>',
  },
  // Add more homework assignments as needed
]

export default function HomeworkComponent() {
  const [selectedSubject, setSelectedSubject] = useState<
    string | number | null
  >(null) // Define type for selectedSubject

  const handleSubjectClick = (subjectId: string | number) => {
    setSelectedSubject(subjectId)
  }

  const getStatusColor = (status: 'pending' | 'submitted' | 'late'): string => {
    switch (status) {
      case 'submitted':
        return 'text-green-600'
      case 'late':
        return 'text-red-600'
      default:
        return 'text-yellow-600'
    }
  }

  const renderHomeworkList = () => {
    const filteredHomework = selectedSubject
      ? homeworkData.filter((hw) => hw.subjectId === selectedSubject)
      : homeworkData

    if (filteredHomework.length === 0) {
      return (
        <div className="alert alert-warning">
          <strong>No Homework</strong>
          <p>No homework assignments found for this subject.</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {filteredHomework.map((homework) => (
          <Card key={homework.id} className="p-4">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold">{homework.title}</h3>
                <p className="text-sm text-gray-600">
                  Teacher: {homework.teacherName}
                </p>
                <p className="text-sm text-gray-600">
                  Due Date: {homework.dueDate}
                </p>
                <p className="mt-2">{homework.description}</p>
                {homework.allowFileSubmission && (
                  <p className="text-sm text-gray-600 mt-1">
                    Accepted formats: {homework.allowedFileTypes.join(', ')}
                  </p>
                )}
              </div>
              <span
                className={`font-medium ${getStatusColor(homework.status)}`}
              >
                {homework.status.charAt(0).toUpperCase() +
                  homework.status.slice(1)}
              </span>
            </div>

            {/* Show status only */}
            <div className="mt-4">
              {homework.status === 'submitted' && (
                <div className="flex items-center gap-2 text-green-600">
                  <Check className="w-4 h-4" />
                  <span>Homework submitted successfully</span>
                </div>
              )}
              {homework.status === 'late' && (
                <div className="flex items-center gap-2 text-red-600">
                  <X className="w-4 h-4" />
                  <span>Homework is late</span>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <Card className="p-4">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Child's Homework Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <Card
            className={`p-4 cursor-pointer transition-all ${
              !selectedSubject ? 'border-primary bg-primary/5' : ''
            }`}
            onClick={() => setSelectedSubject(null)}
          >
            <CardTitle className="text-center">All Subjects</CardTitle>
          </Card>
          {subjects.map((subject) => (
            <Card
              key={subject.id}
              className={`p-4 cursor-pointer transition-all ${
                selectedSubject === subject.id
                  ? 'border-primary bg-primary/5'
                  : ''
              }`}
              onClick={() => handleSubjectClick(subject.id)}
            >
              <CardTitle className="text-center">{subject.name}</CardTitle>
            </Card>
          ))}
        </div>
        {renderHomeworkList()}
      </CardContent>
    </Card>
  )
}
