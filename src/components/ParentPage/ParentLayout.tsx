import { useState } from 'react'
import {
  LayoutDashboard,
  Calendar,
  BookOpen,
  GraduationCap,
  Calculator,
  ClipboardCheck,
} from 'lucide-react'
import ViewAbsent from './ViewAbsent'
import Homework from './HomeWork'
import ChildAttendanceView from './ChildAttendanceView'
import { ScheduleView, StudentScore, StudentNotes } from '../shared'

const navigation = [
  {
    name: 'Schedule View',
    icon: Calendar,
    component: () => <ScheduleView userRole="parent" initialSchedule={{}} />,
  },
  { name: 'View Absent', icon: BookOpen, component: ViewAbsent },
  {
    name: 'Child Attendance',
    icon: ClipboardCheck,
    component: ChildAttendanceView,
  },
  {
    name: 'Student Notes',
    icon: GraduationCap,
    component: () => <StudentNotes userRole="parent" />,
  },
  {
    name: 'Student Score',
    icon: Calculator,
    component: () => <StudentScore userRole="parent" />,
  },
  { name: 'Student Homework', icon: LayoutDashboard, component: Homework },
]

export default function ParentLayout() {
  const [currentPage, setCurrentPage] = useState('Schedule View')

  const Component =
    navigation.find((item) => item.name === currentPage)?.component ||
    navigation[0].component

  return (
    <div className="flex h-screen">
      <nav className="w-64 bg-gray-100 p-4">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => setCurrentPage(item.name)}
            className={`flex items-center w-full p-2 rounded ${
              currentPage === item.name ? 'bg-blue-500 text-white' : ''
            }`}
          >
            <item.icon className="mr-2 h-5 w-5" />
            {item.name}
          </button>
        ))}
      </nav>
      <main className="flex-1 p-4">
        <Component />
      </main>
    </div>
  )
}
