import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'

import { Absent, MockStudentSubject } from '@/interface/StudentInteface'
import { FlexibleId } from '@/interface/types/SharedTypes'

// Define custom interface for our mock data
interface MockStudentData {
  id: FlexibleId
  fullname: string
  studentId?: string
  subjects: MockStudentSubject[]
}

// Hardcoded student data grouped by subject
const studentData: MockStudentData = {
  id: 101,
  fullname: '<PERSON>',
  studentId: 'STU2024001',
  subjects: [
    {
      id: 1,
      name: 'Mathematics',
      totalAbsences: 5,
      absences: [
        {
          id: 1,
          date: '2024-12-25',
          lesson: 'Algebra - Functions',
          reason: 'Sick Leave',
          status: 'approved',
          teacherName: 'Dr<PERSON> <PERSON>',
          notes: 'Medical certificate submitted',
        },
        {
          id: 2,
          date: '2024-12-20',
          lesson: 'Calculus - Derivatives',
          reason: 'Family Event',
          status: 'approved',
          teacherName: 'Dr. Smith',
          notes: "Parent's letter received",
        },
        {
          id: 3,
          date: '2024-12-18',
          lesson: 'Geometry - Circles',
          reason: 'Medical Appointment',
          status: 'pending',
          teacherName: 'Dr. Smith',
          notes: 'Awaiting documentation',
        },
        {
          id: 4,
          date: '2024-12-15',
          lesson: 'Statistics - Probability',
          reason: 'Sick Leave',
          status: 'approved',
          teacherName: 'Dr. Smith',
          notes: 'Medical certificate submitted',
        },
        {
          id: 5,
          date: '2024-12-10',
          lesson: 'Algebra - Matrices',
          reason: 'Sports Event',
          status: 'approved',
          teacherName: 'Dr. Smith',
          notes: 'School representative',
        },
      ],
    },
    {
      id: 2,
      name: 'Physics',
      totalAbsences: 3,
      absences: [
        {
          id: 6,
          date: '2024-12-22',
          lesson: 'Mechanics - Forces',
          reason: 'Sick Leave',
          status: 'approved',
          teacherName: 'Prof. Johnson',
          notes: 'Medical certificate submitted',
        },
        {
          id: 7,
          date: '2024-12-17',
          lesson: 'Thermodynamics',
          reason: 'Family Event',
          status: 'approved',
          teacherName: 'Prof. Johnson',
          notes: "Parent's letter received",
        },
        {
          id: 8,
          date: '2024-12-12',
          lesson: 'Optics - Reflection',
          reason: 'Medical Appointment',
          status: 'pending',
          teacherName: 'Prof. Johnson',
          notes: 'Awaiting documentation',
        },
      ],
    },
  ],
}

// Sample absence data - replace with your API call
const absenceData: Absent[] = [
  {
    id: 1,
    studentId: 101,
    subject: 'Mathematics',
    date: '2024-12-25',
    reason: 'Sick Leave',
    status: 'approved',
  },
  {
    id: 2,
    studentId: 101,
    subject: 'Mathematics',
    date: '2024-12-20',
    reason: 'Family Event',
    status: 'approved',
  },
  {
    id: 3,
    studentId: 101,
    subject: 'Physics',
    date: '2024-12-18',
    reason: 'Sick Leave',
    status: 'approved',
  },
]

export default function ViewAbsent() {
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false)
  const [absenceDetails, setAbsenceDetails] = useState<Absent[]>([])

  const handleViewDetails = (subjectId: FlexibleId) => {
    // Find the subject name
    const subject = studentData.subjects.find((s) => s.id === subjectId)
    if (!subject) return

    // Filter absences by subject name
    const subjectName = subject.name || ''
    const subjectAbsences = absenceData.filter(
      (absence) => absence.subject === subjectName
    )

    setAbsenceDetails(subjectAbsences)
    setSelectedSubject(subjectName)
    setIsDialogOpen(true)
  }

  const handleAbsentRequest = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    // Here you would typically send the form data to your backend
    console.log('Absent request submitted')
    setIsRequestDialogOpen(false)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center">My Absence Record</h1>
        <div className="mt-4 text-center">
          <p className="text-lg">Student Name: {studentData.fullname}</p>
          <p className="text-gray-600">Student ID: {studentData.studentId}</p>
        </div>
        <Button onClick={() => setIsRequestDialogOpen(true)} className="mt-4">
          Request Absence
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              <TableHead>Total Absences</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {studentData.subjects.map((subject) => (
              <TableRow key={subject.id}>
                <TableCell className="font-medium">{subject.name}</TableCell>
                <TableCell>{subject.totalAbsences} lessons</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      subject.id !== undefined
                        ? handleViewDetails(subject.id)
                        : null
                    }
                  >
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedSubject} Absence Details</DialogTitle>
          </DialogHeader>

          {selectedSubject && (
            <div className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Lesson</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {absenceDetails.map((absence) => (
                    <TableRow key={absence.id}>
                      <TableCell>{absence.date}</TableCell>
                      <TableCell>{absence.lesson}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-sm ${
                            absence.status === 'approved'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {absence.status}
                        </span>
                      </TableCell>
                      <TableCell>{absence.reason}</TableCell>
                      <TableCell className="max-w-xs truncate">
                        {absence.notes}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isRequestDialogOpen} onOpenChange={setIsRequestDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Request Absence</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleAbsentRequest}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="date" className="text-right">
                  Date
                </Label>
                <Input id="date" type="date" className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reason" className="text-right">
                  Reason
                </Label>
                <Textarea id="reason" className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file" className="text-right">
                  File
                </Label>
                <Input id="file" type="file" className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">Submit Request</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
