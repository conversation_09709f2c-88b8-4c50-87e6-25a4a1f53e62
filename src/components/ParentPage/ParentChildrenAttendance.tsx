import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import { Calendar } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

// Define attendance status enum
enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  LEFT_EARLY = 'left_early',
}

// Define attendance record interface
interface AttendanceRecord {
  id: string
  studentId: string
  student?: {
    id: string
    firstname: string
    lastname: string
  }
  sessionId?: string
  className: string
  subjectName: string
  day: string
  timeSlot: string
  date: string
  status: AttendanceStatus
  notes?: string
  reason?: string
  createdAt: string
  updatedAt: string
}

// Define child interface
interface Child {
  id: string
  firstname: string
  lastname: string
  classId: string
  className: string
}

// Function to get attendance status badge color
const getStatusBadgeColor = (status: AttendanceStatus) => {
  switch (status) {
    case AttendanceStatus.PRESENT:
      return 'bg-green-100 text-green-800'
    case AttendanceStatus.ABSENT:
      return 'bg-red-100 text-red-800'
    case AttendanceStatus.LATE:
      return 'bg-yellow-100 text-yellow-800'
    case AttendanceStatus.LEFT_EARLY:
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Function to get attendance status label
const getStatusLabel = (status: AttendanceStatus) => {
  switch (status) {
    case AttendanceStatus.PRESENT:
      return 'Present'
    case AttendanceStatus.ABSENT:
      return 'Absent'
    case AttendanceStatus.LATE:
      return 'Late'
    case AttendanceStatus.LEFT_EARLY:
      return 'Left Early'
    default:
      return status
  }
}

export default function ParentChildrenAttendance() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [children, setChildren] = useState<Child[]>([])
  const [selectedChild, setSelectedChild] = useState<string>('')
  const [selectedDateRange, setSelectedDateRange] = useState<{
    startDate: string
    endDate: string
  }>({
    startDate: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    endDate: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
  })
  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([])
  const [filteredRecords, setFilteredRecords] = useState<AttendanceRecord[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(
    null
  )
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [attendanceSummary, setAttendanceSummary] = useState<{
    total: number
    present: number
    absent: number
    late: number
    leftEarly: number
    presentPercentage: number
  }>({
    total: 0,
    present: 0,
    absent: 0,
    late: 0,
    leftEarly: 0,
    presentPercentage: 0,
  })
  const [allAttendanceRecords, setAllAttendanceRecords] = useState<{
    [key: string]: AttendanceRecord[]
  }>({})

  // Mock data for children - this will be replaced with real data in production
  const mockChildren = [
    {
      id: '1',
      firstname: 'John',
      lastname: 'Doe',
      classId: 'class1',
      className: 'Class 1A',
    },
    {
      id: '2',
      firstname: 'Jane',
      lastname: 'Doe',
      classId: 'class2',
      className: 'Class 2B',
    },
  ]

  // Mock data for attendance records - this will be replaced with real data in production
  const mockAttendanceRecords: Record<string, AttendanceRecord[]> = {
    '1': [
      {
        id: 'att1',
        studentId: '1',
        className: 'Class 1A',
        subjectName: 'Mathematics',
        day: 'Monday',
        timeSlot: '08:00 - 09:00',
        date: '2025-04-15',
        status: AttendanceStatus.PRESENT,
        createdAt: '2025-04-15T08:00:00Z',
        updatedAt: '2025-04-15T09:00:00Z',
      },
      {
        id: 'att2',
        studentId: '1',
        className: 'Class 1A',
        subjectName: 'Science',
        day: 'Monday',
        timeSlot: '09:00 - 10:00',
        date: '2025-04-15',
        status: AttendanceStatus.ABSENT,
        reason: 'Sick',
        createdAt: '2025-04-15T09:00:00Z',
        updatedAt: '2025-04-15T10:00:00Z',
      },
    ],
    '2': [
      {
        id: 'att3',
        studentId: '2',
        className: 'Class 2B',
        subjectName: 'English',
        day: 'Monday',
        timeSlot: '08:00 - 09:00',
        date: '2025-04-15',
        status: AttendanceStatus.LATE,
        notes: 'Arrived 15 minutes late',
        createdAt: '2025-04-15T08:00:00Z',
        updatedAt: '2025-04-15T09:00:00Z',
      },
    ],
  }

  // Fetch children data
  useEffect(() => {
    const fetchChildren = async () => {
      setLoading(true)

      try {
        // In a real implementation, we would fetch the children from the API
        // For now, we'll use mock data
        setChildren(mockChildren)

        if (mockChildren.length > 0) {
          const firstChildId = mockChildren[0].id
          setSelectedChild(firstChildId)

          // Set mock attendance records
          setAllAttendanceRecords(mockAttendanceRecords)

          // Set attendance records for the first child
          const firstChildRecords = mockAttendanceRecords[firstChildId] || []
          setAttendanceRecords(firstChildRecords)
          setFilteredRecords(firstChildRecords)
        }
      } catch (error) {
        console.error('Error fetching children data:', error)
        toast({
          title: 'Error',
          description:
            "Could not fetch your children's data. Please try again later.",
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchChildren()
  }, [])

  // Update attendance records when selected child changes
  useEffect(() => {
    if (!selectedChild) return

    // If we already have attendance records for this child, use them
    if (allAttendanceRecords[selectedChild]) {
      setAttendanceRecords(allAttendanceRecords[selectedChild])
    }
  }, [selectedChild, allAttendanceRecords])

  // Apply filters to attendance records
  useEffect(() => {
    let filtered = [...attendanceRecords]

    // Filter by date range
    filtered = filtered.filter(
      (record) =>
        record.date >= selectedDateRange.startDate &&
        record.date <= selectedDateRange.endDate
    )

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter((record) => record.status === selectedStatus)
    }

    // Sort by date (newest first)
    filtered.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    )

    setFilteredRecords(filtered)

    // Calculate attendance summary
    const total = filtered.length
    const present = filtered.filter(
      (record) => record.status === AttendanceStatus.PRESENT
    ).length
    const absent = filtered.filter(
      (record) => record.status === AttendanceStatus.ABSENT
    ).length
    const late = filtered.filter(
      (record) => record.status === AttendanceStatus.LATE
    ).length
    const leftEarly = filtered.filter(
      (record) => record.status === AttendanceStatus.LEFT_EARLY
    ).length
    const presentPercentage =
      total > 0 ? Math.round((present / total) * 100) : 0

    setAttendanceSummary({
      total,
      present,
      absent,
      late,
      leftEarly,
      presentPercentage,
    })
  }, [attendanceRecords, selectedDateRange, selectedStatus])

  // Set date range to last 7 days
  const setLastWeek = () => {
    const end = new Date()
    const start = subDays(end, 7)

    setSelectedDateRange({
      startDate: format(start, 'yyyy-MM-dd'),
      endDate: format(end, 'yyyy-MM-dd'),
    })
  }

  // Set date range to current month
  const setCurrentMonth = () => {
    const now = new Date()

    setSelectedDateRange({
      startDate: format(startOfMonth(now), 'yyyy-MM-dd'),
      endDate: format(endOfMonth(now), 'yyyy-MM-dd'),
    })
  }

  // View attendance record details
  const viewRecordDetails = (record: AttendanceRecord) => {
    setSelectedRecord(record)
    setIsDialogOpen(true)
  }

  // Get the selected child's name
  const getSelectedChildName = () => {
    const child = children.find((child) => child.id === selectedChild)
    return child ? `${child.firstname} ${child.lastname}` : 'Select a child'
  }

  // Refresh attendance data
  const refreshData = () => {
    // In a real implementation, we would fetch the data again
    // For now, we'll just show a toast
    toast({
      title: 'Data Refreshed',
      description: 'Attendance data has been refreshed.',
      variant: 'default',
    })
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center">
          Child Attendance Records
        </h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Attendance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          {children.length === 0 ? (
            <div className="text-center py-8">
              <h3 className="text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
              <Button onClick={refreshData} variant="outline" className="mt-4">
                Refresh Data
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center mb-4">
                  <label htmlFor="child-select" className="mr-2 font-medium">
                    Select Child:
                  </label>
                  <Select
                    value={selectedChild}
                    onValueChange={(value) => setSelectedChild(value)}
                    disabled={loading || children.length === 0}
                  >
                    <SelectTrigger className="w-[200px]">
                      <SelectValue
                        placeholder={loading ? 'Loading...' : 'Select a child'}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {children.map((child) => (
                        <SelectItem key={child.id} value={child.id}>
                          {child.firstname} {child.lastname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center mb-4">
                  <label htmlFor="date-range" className="mr-2 font-medium">
                    Date Range:
                  </label>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={setLastWeek}>
                      Last 7 Days
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={setCurrentMonth}
                    >
                      This Month
                    </Button>
                  </div>
                </div>

                <div className="text-sm text-gray-600 mb-4">
                  {format(new Date(selectedDateRange.startDate), 'MMM d, yyyy')}{' '}
                  - {format(new Date(selectedDateRange.endDate), 'MMM d, yyyy')}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-lg mb-2">
                  {getSelectedChildName()} - Attendance Stats
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-3xl font-bold text-blue-600">
                      {attendanceSummary.presentPercentage}%
                    </div>
                    <div className="text-sm text-gray-600">Attendance Rate</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-gray-800">
                      {attendanceSummary.total}
                    </div>
                    <div className="text-sm text-gray-600">Total Sessions</div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-2 mt-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      {attendanceSummary.present}
                    </div>
                    <div className="text-xs text-gray-600">Present</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-red-600">
                      {attendanceSummary.absent}
                    </div>
                    <div className="text-xs text-gray-600">Absent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-600">
                      {attendanceSummary.late + attendanceSummary.leftEarly}
                    </div>
                    <div className="text-xs text-gray-600">Late/Left Early</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Attendance Records</CardTitle>
            {children.length > 0 && (
              <Select
                value={selectedStatus}
                onValueChange={(value) => setSelectedStatus(value)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={AttendanceStatus.PRESENT}>
                    Present
                  </SelectItem>
                  <SelectItem value={AttendanceStatus.ABSENT}>
                    Absent
                  </SelectItem>
                  <SelectItem value={AttendanceStatus.LATE}>Late</SelectItem>
                  <SelectItem value={AttendanceStatus.LEFT_EARLY}>
                    Left Early
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                Loading attendance records...
              </h3>
            </div>
          ) : children.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No attendance records
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No attendance records found for the selected filters.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Day</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        {format(new Date(record.date), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell>{record.day}</TableCell>
                      <TableCell>{record.subjectName}</TableCell>
                      <TableCell>{record.timeSlot}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                            record.status
                          )}`}
                        >
                          {getStatusLabel(record.status)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewRecordDetails(record)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Attendance Record Details</DialogTitle>
          </DialogHeader>

          {selectedRecord && (
            <div className="mt-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Date</h4>
                  <p>{format(new Date(selectedRecord.date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Day</h4>
                  <p>{selectedRecord.day}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Subject</h4>
                  <p>{selectedRecord.subjectName}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Time</h4>
                  <p>{selectedRecord.timeSlot}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Class</h4>
                  <p>{selectedRecord.className}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                      selectedRecord.status
                    )}`}
                  >
                    {getStatusLabel(selectedRecord.status)}
                  </span>
                </div>
              </div>

              {selectedRecord.reason && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Reason</h4>
                  <p>{selectedRecord.reason}</p>
                </div>
              )}

              {selectedRecord.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p>{selectedRecord.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
