import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns'
import { Calendar, RefreshCw } from 'lucide-react'
import { parentService } from '@/lib/api/services/parent-service'
import { api } from '@/lib/api/axios-instance'
import { useToast } from '@/components/ui/use-toast'

// Define attendance status as string literals to handle all possible values
type AttendanceStatus = 'present' | 'absent' | 'late' | 'left_early'

// Define constants for attendance status values
const ATTENDANCE_STATUS = {
  PRESENT: 'present' as AttendanceStatus,
  ABSENT: 'absent' as AttendanceStatus,
  LATE: 'late' as AttendanceStatus,
  LEFT_EARLY: 'left_early' as AttendanceStatus,
}

// Import the Attendance interface from service
import { Attendance } from '@/lib/api/services/attendance-service'

// Define child interface
interface Child {
  id: string
  firstname: string
  lastname: string
  classId?: string
  className?: string
  class?: {
    id: string
    name: string
  }
}

// Function to get attendance status badge color
const getStatusBadgeColor = (status: AttendanceStatus) => {
  switch (status) {
    case ATTENDANCE_STATUS.PRESENT:
      return 'bg-green-100 text-green-800'
    case ATTENDANCE_STATUS.ABSENT:
      return 'bg-red-100 text-red-800'
    case ATTENDANCE_STATUS.LATE:
      return 'bg-yellow-100 text-yellow-800'
    case ATTENDANCE_STATUS.LEFT_EARLY:
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Function to get attendance status label
const getStatusLabel = (status: AttendanceStatus) => {
  switch (status) {
    case ATTENDANCE_STATUS.PRESENT:
      return 'Present'
    case ATTENDANCE_STATUS.ABSENT:
      return 'Absent'
    case ATTENDANCE_STATUS.LATE:
      return 'Late'
    case ATTENDANCE_STATUS.LEFT_EARLY:
      return 'Left Early'
    default:
      return status
  }
}

export default function ChildAttendanceView() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [children, setChildren] = useState<Child[]>([])
  const [selectedChild, setSelectedChild] = useState<string>('')
  const [selectedDateRange, setSelectedDateRange] = useState<{
    startDate: string
    endDate: string
  }>({
    startDate: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    endDate: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
  })
  const [attendanceRecords, setAttendanceRecords] = useState<Attendance[]>([])
  const [filteredRecords, setFilteredRecords] = useState<Attendance[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedRecord, setSelectedRecord] = useState<Attendance | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [attendanceSummary, setAttendanceSummary] = useState<{
    total: number
    present: number
    absent: number
    late: number
    leftEarly: number
    presentPercentage: number
  }>({
    total: 0,
    present: 0,
    absent: 0,
    late: 0,
    leftEarly: 0,
    presentPercentage: 0,
  })
  const [allAttendanceRecords, setAllAttendanceRecords] = useState<{
    [key: string]: Attendance[]
  }>({})
  const [error, setError] = useState<string | null>(null)

  // Fetch logged-in parent's children with attendance records
  const fetchChildrenAttendance = async () => {
    setLoading(true)
    setError(null)

    try {
      // Get parent ID from localStorage
      const parentId = localStorage.getItem('id')
      if (!parentId) {
        throw new Error('Parent ID not found. Please log in again.')
      }

      // Fetch parent profile to get children
      const parentData = await parentService.getById(parentId)

      if (!parentData.students || parentData.students.length === 0) {
        setChildren([])
        toast({
          title: 'No Children Found',
          description: 'No children records found linked to your account.',
          variant: 'default',
        })
        setLoading(false)
        return
      }

      // Set children data
      setChildren(parentData.students)

      // Set the first child as selected
      const firstChildId = parentData.students[0].id
      setSelectedChild(firstChildId)

      // Fetch attendance records for the first child
      const url = `/attendance/student/${firstChildId}?startDate=${selectedDateRange.startDate}&endDate=${selectedDateRange.endDate}`
      const response = await api.get<Attendance[]>(url)
      const attendanceData = response.data

      // Store attendance records
      setAttendanceRecords(attendanceData)
      setAllAttendanceRecords({ [firstChildId]: attendanceData })
    } catch (error: any) {
      console.error('Error fetching children and attendance data:', error)
      setError(error.message || 'Failed to fetch data')
      toast({
        title: 'Error',
        description:
          error.message || 'Could not fetch data. Please try again later.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch data on component mount and when date range changes
  useEffect(() => {
    fetchChildrenAttendance()
  }, [selectedDateRange.startDate, selectedDateRange.endDate])

  // Update attendance records when selected child changes
  useEffect(() => {
    if (!selectedChild) return

    // If we already have attendance records for this child, use them
    if (allAttendanceRecords[selectedChild]) {
      setAttendanceRecords(allAttendanceRecords[selectedChild])
      return
    }

    // Otherwise fetch from API
    const fetchAttendanceRecords = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch attendance records for the selected child with date range
        const url = `/attendance/student/${selectedChild}?startDate=${selectedDateRange.startDate}&endDate=${selectedDateRange.endDate}`
        console.log('Fetching attendance records from:', url)

        // Use the correct endpoint with date range parameters
        const response = await api.get<Attendance[]>(url)
        const attendanceData = response.data

        // Set attendance records
        setAttendanceRecords(attendanceData)

        // Add to our cache of attendance records
        setAllAttendanceRecords((prev) => ({
          ...prev,
          [selectedChild]: attendanceData,
        }))
      } catch (error: any) {
        console.error('Error fetching attendance records:', error)
        setError(error.message || 'Failed to fetch attendance records')
        setAttendanceRecords([])

        toast({
          title: 'Error',
          description: 'Could not fetch attendance records for this child.',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchAttendanceRecords()
  }, [selectedChild, selectedDateRange.startDate, selectedDateRange.endDate])

  // Apply filters to attendance records
  useEffect(() => {
    let filtered = [...attendanceRecords]

    // Filter by date range
    filtered = filtered.filter(
      (record) =>
        record.date >= selectedDateRange.startDate &&
        record.date <= selectedDateRange.endDate
    )

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter((record) => record.status === selectedStatus)
    }

    // Sort by date (newest first)
    filtered.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    )

    setFilteredRecords(filtered)

    // Calculate attendance summary
    const total = filtered.length
    const present = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.PRESENT
    ).length
    const absent = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.ABSENT
    ).length
    const late = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.LATE
    ).length
    const leftEarly = filtered.filter(
      (record) => record.status === ATTENDANCE_STATUS.LEFT_EARLY
    ).length
    const presentPercentage =
      total > 0 ? Math.round((present / total) * 100) : 0

    setAttendanceSummary({
      total,
      present,
      absent,
      late,
      leftEarly,
      presentPercentage,
    })
  }, [attendanceRecords, selectedDateRange, selectedStatus])

  // Set date range to last 7 days
  const setLastWeek = () => {
    const end = new Date()
    const start = subDays(end, 7)

    setSelectedDateRange({
      startDate: format(start, 'yyyy-MM-dd'),
      endDate: format(end, 'yyyy-MM-dd'),
    })
  }

  // Set date range to current month
  const setCurrentMonth = () => {
    const now = new Date()

    setSelectedDateRange({
      startDate: format(startOfMonth(now), 'yyyy-MM-dd'),
      endDate: format(endOfMonth(now), 'yyyy-MM-dd'),
    })
  }

  // View attendance record details
  const viewRecordDetails = (record: Attendance) => {
    setSelectedRecord(record)
    setIsDialogOpen(true)
  }

  // Get the selected child's name
  const getSelectedChildName = () => {
    const child = children.find((child) => child.id === selectedChild)
    return child ? `${child.firstname} ${child.lastname}` : 'Select a child'
  }

  // Get the selected child's class name
  const getSelectedChildClassName = () => {
    const child = children.find((child) => child.id === selectedChild)
    if (!child) return ''

    // Handle different ways class info might be stored
    if (child.className) return child.className
    if (child.class?.name) return child.class.name
    return ''
  }

  // Refresh attendance data
  const refreshData = () => {
    fetchChildrenAttendance()
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center">
          Child Attendance Records
        </h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Attendance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          {children.length === 0 ? (
            <div className="text-center py-8">
              <h3 className="text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
              <Button onClick={refreshData} variant="outline" className="mt-4">
                Refresh Data
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center mb-4">
                  <label htmlFor="child-select" className="mr-2 font-medium">
                    Select Child:
                  </label>
                  <Select
                    value={selectedChild}
                    onValueChange={(value) => setSelectedChild(value)}
                    disabled={loading || children.length === 0}
                  >
                    <SelectTrigger className="w-[200px]">
                      <SelectValue
                        placeholder={loading ? 'Loading...' : 'Select a child'}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {children.map((child) => (
                        <SelectItem key={child.id} value={child.id}>
                          {child.firstname} {child.lastname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center mb-4">
                  <label htmlFor="date-range" className="mr-2 font-medium">
                    Date Range:
                  </label>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={setLastWeek}>
                      Last 7 Days
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={setCurrentMonth}
                    >
                      This Month
                    </Button>
                  </div>
                </div>

                <div className="text-sm text-gray-600 mb-4">
                  {format(new Date(selectedDateRange.startDate), 'MMM d, yyyy')}{' '}
                  - {format(new Date(selectedDateRange.endDate), 'MMM d, yyyy')}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-lg mb-2">
                  {getSelectedChildName()} - Attendance Stats
                </h3>
                {getSelectedChildClassName() && (
                  <p className="text-sm text-gray-600 mb-2">
                    Class: {getSelectedChildClassName()}
                  </p>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-3xl font-bold text-blue-600">
                      {attendanceSummary.presentPercentage}%
                    </div>
                    <div className="text-sm text-gray-600">Attendance Rate</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-gray-800">
                      {attendanceSummary.total}
                    </div>
                    <div className="text-sm text-gray-600">Total Sessions</div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-2 mt-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      {attendanceSummary.present}
                    </div>
                    <div className="text-xs text-gray-600">Present</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-red-600">
                      {attendanceSummary.absent}
                    </div>
                    <div className="text-xs text-gray-600">Absent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-600">
                      {attendanceSummary.late + attendanceSummary.leftEarly}
                    </div>
                    <div className="text-xs text-gray-600">Late/Left Early</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Attendance Records</CardTitle>
            {children.length > 0 && (
              <Select
                value={selectedStatus}
                onValueChange={(value) => setSelectedStatus(value)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.PRESENT}>
                    Present
                  </SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.ABSENT}>
                    Absent
                  </SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.LATE}>Late</SelectItem>
                  <SelectItem value={ATTENDANCE_STATUS.LEFT_EARLY}>
                    Left Early
                  </SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                Loading attendance records...
              </h3>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-4">
                <h3 className="text-sm font-semibold">Error loading data</h3>
                <p className="text-xs">{error}</p>
              </div>
              <Button onClick={refreshData} variant="outline" className="mt-2">
                <RefreshCw className="mr-2 h-4 w-4" /> Try Again
              </Button>
            </div>
          ) : children.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No children found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No children records found linked to your account.
              </p>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">
                No attendance records
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No attendance records found for the selected filters.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Day</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        {format(new Date(record.date), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell>{record.day}</TableCell>
                      <TableCell>{record.subjectName}</TableCell>
                      <TableCell>{record.timeSlot}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                            record.status
                          )}`}
                        >
                          {getStatusLabel(record.status)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewRecordDetails(record)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Attendance Record Details</DialogTitle>
          </DialogHeader>

          {selectedRecord && (
            <div className="mt-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Date</h4>
                  <p>{format(new Date(selectedRecord.date), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Day</h4>
                  <p>{selectedRecord.day}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Subject</h4>
                  <p>{selectedRecord.subjectName}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Time</h4>
                  <p>{selectedRecord.timeSlot}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Class</h4>
                  <p>{selectedRecord.className}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(
                      selectedRecord.status
                    )}`}
                  >
                    {getStatusLabel(selectedRecord.status)}
                  </span>
                </div>
              </div>

              {selectedRecord.reason && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Reason</h4>
                  <p>{selectedRecord.reason}</p>
                </div>
              )}

              {selectedRecord.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p>{selectedRecord.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
