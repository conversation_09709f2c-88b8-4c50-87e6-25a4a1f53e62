import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import { services } from '../../mockData/SuperAdminServiceData'

export default function StudentService() {
  return (
    <div className="flex flex-col max-w-5xl mx-auto p-6 space-y-8">
      <section>
        <h2 className="text-2xl font-bold mb-4">Services</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {services.map((service, index) => (
            <Card key={index} className="hover:bg-muted/50 transition-colors">
              <CardContent className="p-4">
                <a
                  href={service.href}
                  className="flex flex-col items-center text-center space-y-2"
                >
                  {service.icon}
                  <span className="text-sm font-medium">{service.name}</span>
                </a>
              </CardContent>
            </Card>
          ))}
          <Card className="hover:bg-muted/50 transition-colors">
            <CardContent className="p-4">
              <Button
                variant="ghost"
                className="w-full h-full flex flex-col items-center space-y-2"
              ></Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
