import * as React from 'react'
import { format } from 'date-fns'
import { Calendar as CalendarIcon } from 'lucide-react'
import { DayPicker } from 'react-day-picker'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface YearMonthPickerProps {
  date?: Date
  setDate: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function YearMonthPicker({
  date,
  setDate,
  placeholder = 'Pick a date',
  disabled = false,
  className,
}: YearMonthPickerProps) {
  // Ensure we have a valid date object
  const validDate = React.useMemo(() => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return new Date()
    }
    return date
  }, [date])

  // Use state instead of refs for month and year
  const [selectedMonth, setSelectedMonth] = React.useState<number>(validDate.getMonth())
  const [selectedYear, setSelectedYear] = React.useState<number>(validDate.getFullYear())
  const [isOpen, setIsOpen] = React.useState(false)
  // Add state to force re-renders when date changes
  const [, setForceUpdate] = React.useState(0)

  // Update state when date prop changes
  React.useEffect(() => {
    if (validDate) {
      setSelectedMonth(validDate.getMonth())
      setSelectedYear(validDate.getFullYear())
    }
  }, [validDate])

  // Generate years (120 years back from current year for birthdays)
  const currentYear = new Date().getFullYear()
  const years = React.useMemo(() => {
    return Array.from({ length: 120 }, (_, i) => currentYear - i)
  }, [currentYear])

  // Month names
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]

  const handleSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      setDate(selectedDate)
      setSelectedMonth(selectedDate.getMonth())
      setSelectedYear(selectedDate.getFullYear())
      // Force update to ensure the button text is updated
      setForceUpdate(prev => prev + 1)
    } else {
      setDate(undefined)
    }
    setIsOpen(false)
  }

  const handleYearChange = (yearStr: string) => {
    const year = parseInt(yearStr)
    setSelectedYear(year)

    const newDate = new Date(validDate)
    newDate.setFullYear(year)
    newDate.setMonth(selectedMonth)
    setDate(newDate)
    // Force update to ensure the button text is updated
    setForceUpdate(prev => prev + 1)
  }

  const handleMonthChange = (monthName: string) => {
    const month = months.indexOf(monthName)
    setSelectedMonth(month)

    const newDate = new Date(validDate)
    newDate.setMonth(month)
    newDate.setFullYear(selectedYear)
    setDate(newDate)
    // Force update to ensure the button text is updated
    setForceUpdate(prev => prev + 1)
  }

  // Force re-render when date changes
  React.useEffect(() => {
    // This effect ensures the component re-renders when the date prop changes
    // which will update the button text
    setForceUpdate(prev => prev + 1)
  }, [validDate])

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-full justify-start text-left font-normal',
            !validDate && 'text-muted-foreground',
            disabled && 'opacity-50 cursor-not-allowed',
            className
          )}
          disabled={disabled}
          onClick={() => setIsOpen(true)}
          type="button"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {validDate ? format(validDate, 'PPP') : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 space-y-3">
          <div className="flex space-x-2">
            <Select
              value={months[selectedMonth]}
              onValueChange={handleMonthChange}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Month" />
              </SelectTrigger>
              <SelectContent>
                {months.map((monthName) => (
                  <SelectItem key={monthName} value={monthName}>
                    {monthName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedYear.toString()}
              onValueChange={handleYearChange}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent className="max-h-[300px] overflow-y-auto">
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DayPicker
            mode="single"
            selected={validDate}
            onSelect={handleSelect}
            month={new Date(selectedYear, selectedMonth)}
            className="border-t pt-3"
            disabled={disabled}
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}
