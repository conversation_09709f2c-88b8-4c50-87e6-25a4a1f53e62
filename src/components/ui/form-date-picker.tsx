import { Label } from '@/components/ui/label'
import { DatePicker } from '@/components/ui/date-picker'
import { cn } from '@/lib/utils'

interface FormDatePickerProps {
  id: string
  label: string
  value?: Date
  onChange: (date: Date | undefined) => void
  onBlur?: () => void
  error?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

export function FormDatePicker({
  id,
  label,
  value,
  onChange,
  onBlur,
  error,
  placeholder = 'Pick a date',
  disabled = false,
  required = false,
}: FormDatePickerProps) {
  // Create a wrapper function to handle both onChange and onBlur
  const handleDateChange = (date: Date | undefined) => {
    onChange(date)
    if (onBlur) onBlur()
  }

  return (
    <div className="space-y-2">
      <Label
        htmlFor={id}
        className={cn(
          required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
        )}
      >
        {label}
      </Label>
      <div id={id}>
        <DatePicker
          date={value}
          setDate={handleDateChange}
          placeholder={placeholder}
          disabled={disabled}
        />
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  )
}
