import { cn } from '@/lib/utils'

interface NotificationBadgeProps {
  count: number
  maxCount?: number
  className?: string
}

/**
 * A notification badge component that displays a count
 * @param count The number to display in the badge
 * @param maxCount The maximum number to display before showing "+"
 * @param className Additional CSS classes
 */
export function NotificationBadge({
  count,
  maxCount = 99,
  className,
}: NotificationBadgeProps) {
  if (count <= 0) return null

  // Format the count display
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString()

  return (
    <div
      className={cn(
        'absolute -top-2 -right-2 flex items-center justify-center',
        'min-w-[1.25rem] h-5 px-1 rounded-full text-xs font-medium',
        'bg-red-500 text-white',
        className
      )}
    >
      {displayCount}
    </div>
  )
}
