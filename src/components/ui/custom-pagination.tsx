import * as React from 'react'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'

import { cn } from '@/lib/utils'
import { ButtonProps, buttonVariants } from '@/components/ui/button'

const CustomPagination = ({
  className,
  ...props
}: React.ComponentProps<'nav'>) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn('mx-auto flex w-full justify-center', className)}
    {...props}
  />
)
CustomPagination.displayName = 'CustomPagination'

const CustomPaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<'ul'>
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn('flex flex-row items-center gap-1', className)}
    {...props}
  />
))
CustomPaginationContent.displayName = 'CustomPaginationContent'

const CustomPaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<'li'>
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn('', className)} {...props} />
))
CustomPaginationItem.displayName = 'CustomPaginationItem'

type CustomPaginationLinkProps = {
  isActive?: boolean
  disabled?: boolean
} & Pick<ButtonProps, 'size'> &
  React.ComponentProps<'a'>

const CustomPaginationLink = ({
  className,
  isActive,
  disabled,
  size = 'icon',
  ...props
}: CustomPaginationLinkProps) => (
  <a
    aria-current={isActive ? 'page' : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? 'outline' : 'ghost',
        size,
      }),
      disabled && 'pointer-events-none opacity-50',
      className
    )}
    {...props}
  />
)
CustomPaginationLink.displayName = 'CustomPaginationLink'

const CustomPaginationPrevious = ({
  className,
  ...props
}: React.ComponentProps<typeof CustomPaginationLink>) => (
  <CustomPaginationLink
    aria-label="Go to previous page"
    size="default"
    className={cn('gap-1 pl-2.5', className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Previous</span>
  </CustomPaginationLink>
)
CustomPaginationPrevious.displayName = 'CustomPaginationPrevious'

const CustomPaginationNext = ({
  className,
  ...props
}: React.ComponentProps<typeof CustomPaginationLink>) => (
  <CustomPaginationLink
    aria-label="Go to next page"
    size="default"
    className={cn('gap-1 pr-2.5', className)}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </CustomPaginationLink>
)
CustomPaginationNext.displayName = 'CustomPaginationNext'

const CustomPaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<'span'>) => (
  <span
    aria-hidden
    className={cn('flex h-9 w-9 items-center justify-center', className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)
CustomPaginationEllipsis.displayName = 'CustomPaginationEllipsis'

export {
  CustomPagination,
  CustomPaginationContent,
  CustomPaginationEllipsis,
  CustomPaginationItem,
  CustomPaginationLink,
  CustomPaginationNext,
  CustomPaginationPrevious,
}
