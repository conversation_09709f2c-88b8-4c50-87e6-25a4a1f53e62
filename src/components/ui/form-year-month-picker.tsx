import * as React from 'react'
import { cn } from '@/lib/utils'
import { Label } from '@/components/ui/label'
import { YearMonthPicker } from '@/components/ui/year-month-picker'

interface FormYearMonthPickerProps {
  id: string
  label: string
  value?: Date
  onChange: (date: Date | undefined) => void
  onBlur?: () => void
  error?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

export function FormYearMonthPicker({
  id,
  label,
  value,
  onChange,
  onBlur,
  error,
  placeholder = 'Pick a date',
  disabled = false,
  required = false,
}: FormYearMonthPickerProps) {
  // Use state to track the selected date
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(value);

  // Update state when value prop changes
  React.useEffect(() => {
    setSelectedDate(value);
  }, [value]);

  // Create a wrapper function to handle both onChange and onBlur
  const handleDateChange = (date: Date | undefined) => {
    // Update our local state
    setSelectedDate(date);

    // Call the parent's onChange
    onChange(date);

    // Update the input field value
    const inputElement = document.querySelector(
      `input[name="${id}"]`
    ) as HTMLInputElement;

    if (inputElement) {
      if (date) {
        inputElement.value = date.toISOString().split('T')[0];
        // Force a re-render by dispatching an input event
        const event = new Event('input', { bubbles: true });
        inputElement.dispatchEvent(event);
      } else {
        inputElement.value = '';
      }
    }

    if (onBlur) onBlur();
  }

  return (
    <div className="space-y-2">
      <Label
        htmlFor={id}
        className={cn(
          required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
        )}
      >
        {label}
      </Label>
      <div id={id}>
        <YearMonthPicker
          date={selectedDate}
          setDate={handleDateChange}
          placeholder={placeholder}
          disabled={disabled}
        />
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
      <input
        type="text"
        name={id}
        value={selectedDate ? selectedDate.toISOString().split('T')[0] : ''}
        readOnly
        className="hidden"
      />
    </div>
  )
}
