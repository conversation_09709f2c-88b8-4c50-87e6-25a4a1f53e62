import { useState, useRef, useEffect } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  ArrowLeft,
  BookOpen,
  PlusCircle,
  Trash2,
  RefreshCw,
  Bold,
  Italic,
  Underline,
} from 'lucide-react'
import {
  FlashcardDeckType,
  Flashcard,
  CardSide,
  adaptCardForUI,
} from './FlashcardTypes'
import FileUploader from '@/components/shared/FileUploader'
import { useFlashcardImageUpload } from '@/hooks/useFlashcardImageUpload'
import { useFlashcards } from '@/hooks/useFlashcards'
import { useToast } from '@/components/ui/use-toast'
import CdnImage from '@/components/shared/CdnImage'

// DnD item types
const ItemTypes = {
  CARD: 'card',
}

interface FlashcardDeckProps {
  deck: FlashcardDeckType
  onUpdate: (updatedDeck: FlashcardDeckType) => void
  onBack: () => void
  onStudy: () => void
}

interface DraggableCardProps {
  card: Flashcard
  index: number
  moveCard: (dragIndex: number, hoverIndex: number) => void
  onEdit: () => void
  onDelete: () => void
}

// Draggable Card Component
const DraggableCard = ({
  card,
  index,
  moveCard,
  onEdit,
  onDelete,
}: DraggableCardProps) => {
  const ref = useRef<HTMLDivElement>(null)

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CARD,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: ItemTypes.CARD,
    hover(item: { index: number }, monitor) {
      if (!ref.current) {
        return
      }

      const dragIndex = item.index
      const hoverIndex = index

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // Determine mouse position
      const clientOffset = monitor.getClientOffset()

      // Get pixels to the top
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex)

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
  })

  drag(drop(ref))

  return (
    <div
      ref={ref}
      className={`p-3 mb-3 border rounded-md cursor-move ${isDragging ? 'opacity-50' : ''}`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="font-medium">
            {index + 1}. {card.front?.text || card.frontText || 'Front of card'}
          </div>
          <div className="text-sm text-muted-foreground mt-1">
            {card.back?.text || card.backText || 'Back of card'}
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm" onClick={onEdit}>
            Edit
          </Button>
          <Button variant="ghost" size="sm" onClick={onDelete}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="flex mt-2 space-x-2">
        {(card.front?.image || card.frontImage) &&
          (card.front?.image || card.frontImage) !== '' && (
            <div className="w-16 h-16 bg-muted rounded flex items-center justify-center overflow-hidden">
              <CdnImage
                src={card.front?.image || card.frontImage}
                alt="Front"
                className="w-full h-full object-contain"
                key={`front-${card.id}-${Date.now()}`} // Force refresh when image changes
              />
            </div>
          )}
        {(card.back?.image || card.backImage) &&
          (card.back?.image || card.backImage) !== '' && (
            <div className="w-16 h-16 bg-muted rounded flex items-center justify-center overflow-hidden">
              <CdnImage
                src={card.back?.image || card.backImage}
                alt="Back"
                className="w-full h-full object-contain"
                key={`back-${card.id}-${Date.now()}`} // Force refresh when image changes
              />
            </div>
          )}
      </div>
    </div>
  )
}

// Main FlashcardDeck Component
export default function FlashcardDeck({
  deck,
  onUpdate,
  onBack,
  onStudy,
}: FlashcardDeckProps) {
  const [editingCard, setEditingCard] = useState<Flashcard | null>(null)
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [deckName, setDeckName] = useState(deck.name)
  const [deckVisibility, setDeckVisibility] = useState(deck.visibility)

  // Get image upload hook
  const { uploadImage, isUploading } = useFlashcardImageUpload()

  // Get toast hook
  const { toast } = useToast()

  // Temporary image storage for the current editing session
  const [tempImages, setTempImages] = useState<{
    front?: File | null
    back?: File | null
  }>({})

  // Get the flashcard hooks
  const { useUpdateCard, useDeleteCard } = useFlashcards()
  const deleteCardMutation = useDeleteCard(deck.id)

  // We'll update this reference when the editing card changes
  const [currentCardId, setCurrentCardId] = useState<string>('')
  const updateCardMutation = useUpdateCard(currentCardId, deck.id)

  // Update the current card ID when the editing card changes
  useEffect(() => {
    if (editingCard && editingCard.id) {
      console.log('Updating current card ID from effect:', editingCard.id)
      setCurrentCardId(editingCard.id)
    }
  }, [editingCard])

  // Add a new card - only creates a temporary card in the UI, not in the API
  const handleAddCard = () => {
    try {
      // Create a temporary card for the UI only - no API call yet
      const tempId = `temp-${crypto.randomUUID()}`
      const newCard: Flashcard = {
        id: tempId,
        front: { text: '' },
        back: { text: '' },
        deckId: deck.id,
        order: deck.cards?.length || 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        frontText: '', // Empty text
        backText: '', // Empty text
        // Explicitly set image properties to empty strings to avoid inheriting from previous cards
        frontImage: '',
        backImage: '',
      }

      console.log('Creating temporary card in UI only...')

      // Add the temporary card to the deck for immediate UI feedback
      const updatedDeck = {
        ...deck,
        cards: [...(deck.cards || []), newCard],
      }

      // Update the deck and set the new card as the editing card
      onUpdate(updatedDeck)
      setEditingCard(newCard)
      setEditingIndex(updatedDeck.cards.length - 1)
      setCurrentCardId('') // Clear the current card ID since this is a temporary card

      // Reset temp images
      setTempImages({})
    } catch (error) {
      // Log error but don't show toast
      console.error('An unexpected error occurred:', error)
    }
  }

  // Delete a card function

  // Delete a card
  const handleDeleteCard = (index: number) => {
    try {
      if (!deck.cards || deck.cards.length === 0) return

      const cardToDelete = deck.cards[index]

      // No toast for deleting card
      console.log('Deleting card...')

      // Delete the card from the API
      deleteCardMutation.mutate(cardToDelete.id, {
        onSuccess: () => {
          // Card was successfully deleted from the API
          // The deck will be refreshed automatically due to the invalidation in the mutation

          // No success toast for card deletion
          console.log('Card deleted successfully')
        },
        onError: (error) => {
          // Log error but don't show toast
          console.error('Failed to delete card:', error)
        },
      })

      // Update the UI immediately for better user experience
      const updatedCards = [...deck.cards]
      updatedCards.splice(index, 1)

      const updatedDeck = {
        ...deck,
        cards: updatedCards,
      }

      onUpdate(updatedDeck)

      if (editingIndex === index) {
        setEditingCard(null)
        setEditingIndex(null)
      }
    } catch (error) {
      // Log error but don't show toast
      console.error(
        'An unexpected error occurred while deleting the card:',
        error
      )
    }
  }

  // Update a card function

  // Update a card
  const handleUpdateCard = async (forceUpdate = false) => {
    try {
      if (editingCard && editingIndex !== null && deck.cards) {
        // Log the current editing card for debugging
        console.log('Current editing card:', editingCard)

        // Only proceed with API updates when explicitly saving (forceUpdate=true)
        // This prevents auto-saving when clicking outside text fields
        if (!forceUpdate) {
          // Just update the local state without API calls
          console.log(
            'Text field blur detected - updating local state only, no API call'
          )

          // Update the card in the deck for immediate UI feedback
          const updatedCards = [...deck.cards]
          updatedCards[editingIndex] = editingCard

          const updatedDeck = {
            ...deck,
            cards: updatedCards,
          }

          // Update the deck locally only
          onUpdate(updatedDeck)
          return
        }

        // Validate card content when explicitly saving
        const frontText = editingCard.front?.text || editingCard.frontText || ''
        const backText = editingCard.back?.text || editingCard.backText || ''

        // Validate front and back text
        if (!frontText.trim() || !backText.trim()) {
          // Show a single validation error toast with clearer message
          toast({
            title: 'Card Content Required',
            description:
              'Please enter text for both the front and back of the card before saving.',
            variant: 'destructive',
          })

          return
        }

        // No loading toast for saving
        console.log('Saving card and uploading images...')

        // First, upload any pending images to CDN
        let updatedEditingCard = { ...editingCard }

        // Upload front image if exists
        if (tempImages.front) {
          const frontImageUrl = await uploadImage(tempImages.front)
          if (frontImageUrl) {
            updatedEditingCard = {
              ...updatedEditingCard,
              front: {
                text: updatedEditingCard.front?.text || '',
                image: frontImageUrl,
              } as CardSide,
              frontImage: frontImageUrl,
            }
          }
        }

        // Upload back image if exists
        if (tempImages.back) {
          const backImageUrl = await uploadImage(tempImages.back)
          if (backImageUrl) {
            updatedEditingCard = {
              ...updatedEditingCard,
              back: {
                text: updatedEditingCard.back?.text || '',
                image: backImageUrl,
              } as CardSide,
              backImage: backImageUrl,
            }
          }
        }

        // Always send API update when we reach this point (we've already filtered out non-forceUpdate cases)
        // Update the card in the API
        console.log('Updating card with data:', {
          frontText:
            updatedEditingCard.front?.text ||
            updatedEditingCard.frontText ||
            '',
          backText:
            updatedEditingCard.back?.text || updatedEditingCard.backText || '',
          frontImage:
            updatedEditingCard.frontImage || updatedEditingCard.front?.image,
          backImage:
            updatedEditingCard.backImage || updatedEditingCard.back?.image,
        })

        // Check if this is a temporary card (starts with temp-)
        const isTemporaryCard = updatedEditingCard.id?.startsWith('temp-')
        console.log(
          `Card is ${isTemporaryCard ? 'temporary' : 'permanent'} with ID:`,
          updatedEditingCard.id
        )

        // For temporary cards, we'll create a new one in the API
        // For permanent cards, we'll update the existing one
        setCurrentCardId(updatedEditingCard.id || '')

        updateCardMutation.mutate(
          {
            frontText:
              updatedEditingCard.front?.text ||
              updatedEditingCard.frontText ||
              '',
            backText:
              updatedEditingCard.back?.text ||
              updatedEditingCard.backText ||
              '',
            frontImage:
              updatedEditingCard.frontImage || updatedEditingCard.front?.image,
            backImage:
              updatedEditingCard.backImage || updatedEditingCard.back?.image,
            // Only set forceCreate to true if we're explicitly saving AND this is a temporary card
            forceCreate: forceUpdate && isTemporaryCard,
          },
          {
            onSuccess: (updatedCard) => {
              // Adapt the updated card for UI
              const adaptedCard = adaptCardForUI(updatedCard)

              console.log('Card updated successfully:', adaptedCard)

              // Update the card in the deck
              const updatedCards = [...deck.cards]
              updatedCards[editingIndex] = adaptedCard

              const updatedDeck = {
                ...deck,
                cards: updatedCards,
              }

              // Update the deck - this will trigger the parent component to update the deck list
              console.log('Updating deck with updated card:', updatedDeck)
              onUpdate(updatedDeck)

              // Update the editing card with the API response
              setEditingCard(adaptedCard)

              // Only close the editor when explicitly saving
              if (forceUpdate) {
                console.log('Card updated successfully')

                // Close the editor only when explicitly saving
                setEditingCard(null)
                setEditingIndex(null)
              }
            },
            onError: (error) => {
              // Log error but don't show toast
              console.error('Failed to update card:', error)
            },
          }
        )

        // Update the card in the deck for immediate UI feedback
        const updatedCards = [...deck.cards]
        updatedCards[editingIndex] = updatedEditingCard

        const updatedDeck = {
          ...deck,
          cards: updatedCards,
        }

        // Update the deck
        onUpdate(updatedDeck)

        // Update the editing card with the CDN URLs
        setEditingCard(updatedEditingCard)

        // Force a re-render of the card list by updating the state
        setTimeout(() => {
          // This will trigger a re-render of the card list with the updated images
          onUpdate({ ...updatedDeck })
        }, 100)

        // Clear temp images after upload
        setTempImages({})
      }
    } catch (error) {
      // Log error but don't show toast
      console.error(
        'An unexpected error occurred while saving the card:',
        error
      )
    }
  }

  // Move card (for drag and drop)
  const moveCard = (dragIndex: number, hoverIndex: number) => {
    if (!deck.cards || deck.cards.length === 0) return

    const dragCard = deck.cards[dragIndex]
    const updatedCards = [...deck.cards]
    updatedCards.splice(dragIndex, 1)
    updatedCards.splice(hoverIndex, 0, dragCard)

    const updatedDeck = {
      ...deck,
      cards: updatedCards,
    }

    onUpdate(updatedDeck)

    // Update editing index if we're currently editing a card that was moved
    if (editingIndex === dragIndex) {
      setEditingIndex(hoverIndex)
    }
  }

  // Update deck name and visibility
  const handleUpdateDeckInfo = () => {
    const updatedDeck = {
      ...deck,
      name: deckName,
      visibility: deckVisibility,
    }

    onUpdate(updatedDeck)
  }

  return (
    <div className="max-w-5xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Decks
        </Button>
        <Button onClick={onStudy}>
          <BookOpen className="mr-2 h-4 w-4" />
          Study Mode
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Edit Deck</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label
                htmlFor="deck-name"
                className="block text-sm font-medium mb-1"
              >
                Name
              </label>
              <Input
                id="deck-name"
                value={deckName}
                onChange={(e) => setDeckName(e.target.value)}
                onBlur={handleUpdateDeckInfo}
              />
            </div>
            <div>
              <label
                htmlFor="deck-visibility"
                className="block text-sm font-medium mb-1"
              >
                Visibility
              </label>
              <Select
                value={deckVisibility}
                onValueChange={(value: 'private' | 'public') => {
                  setDeckVisibility(value)
                  setTimeout(handleUpdateDeckInfo, 0)
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="private">Private</SelectItem>
                  <SelectItem value="public">Public</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <p className="text-sm text-muted-foreground mb-4">
            Changes are saved when you click "Done". Click and drag to rearrange
            flashcards.
          </p>

          <Button onClick={handleAddCard}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Card
          </Button>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium mb-4">
            Flashcards ({deck.cards?.length || 0})
          </h3>
          {!deck.cards || deck.cards.length === 0 ? (
            <div className="text-center p-8 border rounded-md">
              <p className="text-muted-foreground mb-4">No flashcards yet</p>
              <Button onClick={handleAddCard}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Card
              </Button>
            </div>
          ) : (
            <div>
              {(deck.cards || []).map((card: Flashcard, index: number) => (
                <DraggableCard
                  key={`${card.id || `temp-card-${index}`}-${card.front?.image || ''}-${card.back?.image || ''}`}
                  card={card}
                  index={index}
                  moveCard={moveCard}
                  onEdit={() => {
                    setEditingCard(card)
                    setEditingIndex(index)
                    setCurrentCardId(card.id || '')
                    console.log('Setting current card ID for editing:', card.id)
                  }}
                  onDelete={() => handleDeleteCard(index)}
                />
              ))}
            </div>
          )}
        </div>

        {editingCard && (
          <div>
            <h3 className="text-lg font-medium mb-4">
              Edit Card {editingIndex !== null ? editingIndex + 1 : ''}
            </h3>
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-2">Front</h4>
                    <div className="flex items-center space-x-2 mb-2 border-b pb-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.front?.text || ''
                          setEditingCard({
                            ...editingCard,
                            front: {
                              ...editingCard.front,
                              text: `**${text}**`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Bold className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.front?.text || ''
                          setEditingCard({
                            ...editingCard,
                            front: {
                              ...editingCard.front,
                              text: `*${text}*`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Italic className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.front?.text || ''
                          setEditingCard({
                            ...editingCard,
                            front: {
                              ...editingCard.front,
                              text: `__${text}__`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Underline className="h-4 w-4" />
                      </Button>
                    </div>
                    <Textarea
                      value={
                        editingCard.front?.text || editingCard.frontText || ''
                      }
                      onChange={(e) => {
                        setEditingCard({
                          ...editingCard,
                          front: {
                            ...editingCard.front,
                            text: e.target.value,
                          },
                          frontText: e.target.value, // Update both formats
                        })
                      }}
                      onBlur={() => handleUpdateCard(false)}
                      className="mb-2"
                    />

                    <div className="mb-4">
                      <FileUploader
                        onFileUploaded={(url) => {
                          // This will be called after the image is uploaded to CDN
                          console.log('Front image uploaded successfully:', url)
                          // Store the URL in the card and also in frontImage property
                          console.log('Setting front image URL:', url)
                          setEditingCard({
                            ...editingCard,
                            front: {
                              text:
                                editingCard.front?.text ||
                                editingCard.frontText ||
                                '',
                              image: url,
                            } as CardSide,
                            frontImage: url, // Important: Set this property for API compatibility
                          })
                          // Clear the temp image since we now have the CDN URL
                          setTempImages((prev) => ({ ...prev, front: null }))

                          // Update local state only, don't trigger API save
                          console.log(
                            'Image uploaded - updating local state only, no API call'
                          )

                          // Force a re-render of the card list to show the updated image
                          if (editingIndex !== null && deck.cards) {
                            const updatedCards = [...deck.cards]
                            const updatedCard = {
                              ...editingCard,
                              frontImage: url,
                              frontText:
                                editingCard.front?.text ||
                                editingCard.frontText ||
                                '',
                            }

                            // Also update the front property if it exists
                            if (editingCard.front) {
                              updatedCard.front = {
                                text: editingCard.front.text || '',
                                image: url,
                              } as CardSide
                            }

                            updatedCards[editingIndex] = updatedCard

                            // Update the deck to refresh the UI
                            onUpdate({
                              ...deck,
                              cards: updatedCards,
                            })
                          }
                        }}
                        previewUrl={
                          editingCard.front?.image || editingCard.frontImage
                        }
                        label="Front Image"
                        buttonText="Add Image"
                      />
                    </div>

                    {/* Front image preview is handled by FileUploader */}
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Back</h4>
                    <div className="flex items-center space-x-2 mb-2 border-b pb-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.back?.text || ''
                          setEditingCard({
                            ...editingCard,
                            back: {
                              ...editingCard.back,
                              text: `**${text}**`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Bold className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.back?.text || ''
                          setEditingCard({
                            ...editingCard,
                            back: {
                              ...editingCard.back,
                              text: `*${text}*`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Italic className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const text = editingCard.back?.text || ''
                          setEditingCard({
                            ...editingCard,
                            back: {
                              ...editingCard.back,
                              text: `__${text}__`,
                            },
                          })
                          // Don't trigger API save for formatting changes
                        }}
                      >
                        <Underline className="h-4 w-4" />
                      </Button>
                    </div>
                    <Textarea
                      value={
                        editingCard.back?.text || editingCard.backText || ''
                      }
                      onChange={(e) => {
                        setEditingCard({
                          ...editingCard,
                          back: {
                            ...editingCard.back,
                            text: e.target.value,
                          },
                          backText: e.target.value, // Update both formats
                        })
                      }}
                      onBlur={() => handleUpdateCard(false)}
                      className="mb-2"
                    />

                    <div className="mb-4">
                      <FileUploader
                        onFileUploaded={(url) => {
                          // This will be called after the image is uploaded to CDN
                          console.log('Back image uploaded successfully:', url)
                          // Store the URL in the card and also in backImage property
                          console.log('Setting back image URL:', url)
                          setEditingCard({
                            ...editingCard,
                            back: {
                              text:
                                editingCard.back?.text ||
                                editingCard.backText ||
                                '',
                              image: url,
                            } as CardSide,
                            backImage: url, // Important: Set this property for API compatibility
                          })
                          // Clear the temp image since we now have the CDN URL
                          setTempImages((prev) => ({ ...prev, back: null }))

                          // Update local state only, don't trigger API save
                          console.log(
                            'Image uploaded - updating local state only, no API call'
                          )

                          // Force a re-render of the card list to show the updated image
                          if (editingIndex !== null && deck.cards) {
                            const updatedCards = [...deck.cards]
                            const updatedCard = {
                              ...editingCard,
                              backImage: url,
                              backText:
                                editingCard.back?.text ||
                                editingCard.backText ||
                                '',
                            }

                            // Also update the back property if it exists
                            if (editingCard.back) {
                              updatedCard.back = {
                                text: editingCard.back.text || '',
                                image: url,
                              } as CardSide
                            }

                            updatedCards[editingIndex] = updatedCard

                            // Update the deck to refresh the UI
                            onUpdate({
                              ...deck,
                              cards: updatedCards,
                            })
                          }
                        }}
                        previewUrl={
                          editingCard.back?.image || editingCard.backImage
                        }
                        label="Back Image"
                        buttonText="Add Image"
                      />
                    </div>

                    {/* Back image preview is handled by FileUploader */}
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setEditingCard(null)
                        setEditingIndex(null)
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => handleUpdateCard(true)}
                      disabled={isUploading}
                    >
                      {isUploading ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Card'
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
