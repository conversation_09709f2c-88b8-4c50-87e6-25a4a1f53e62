import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import AIResultsDisplay from './AIResultsDisplay'
import { ProcessAIResponse } from '@/services/aiService'

// Sample data for testing the flashcard functionality
const sampleAIResults: ProcessAIResponse = {
  quiz: [
    {
      question: "What is the capital of France?",
      options: ["London", "Berlin", "Paris", "Madrid"],
      answer: "Paris"
    },
    {
      question: "Which planet is known as the Red Planet?",
      options: ["Venus", "Mars", "Jupiter", "Saturn"],
      answer: "Mars"
    },
    {
      question: "What is 2 + 2?",
      options: ["3", "4", "5", "6"],
      answer: "4"
    }
  ],
  flashcards: [
    {
      front: "What is React?",
      back: "A JavaScript library for building user interfaces, particularly web applications with interactive UIs."
    },
    {
      front: "What is TypeScript?",
      back: "A programming language developed by Microsoft that builds on JavaScript by adding static type definitions."
    },
    {
      front: "What is TanStack Query?",
      back: "A powerful data synchronization library for React that makes fetching, caching, and updating server state simple."
    },
    {
      front: "What is Tailwind CSS?",
      back: "A utility-first CSS framework that provides low-level utility classes to build custom designs quickly."
    },
    {
      front: "What is the purpose of useState?",
      back: "A React Hook that allows you to add state to functional components and manage component re-renders."
    }
  ],
  notes: `# Sample Study Notes

## Key Concepts

### React Fundamentals
- **Components**: Building blocks of React applications
- **Props**: Data passed from parent to child components
- **State**: Internal data that can change over time
- **Hooks**: Functions that let you use state and lifecycle features

[failed to load image data]

### TypeScript Benefits
- **Type Safety**: Catch errors at compile time
- **Better IDE Support**: Enhanced autocomplete and refactoring
- **Self-Documenting Code**: Types serve as documentation
- **Easier Refactoring**: Confident code changes

![Chart showing TypeScript adoption](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==)

### Modern Development
- **TanStack Query**: Simplifies server state management
- **Tailwind CSS**: Rapid UI development with utility classes
- **Component Libraries**: Reusable UI components for consistency

[Image: Architecture diagram not available]

## Best Practices
1. Keep components small and focused
2. Use TypeScript for better code quality
3. Implement proper error handling
4. Write meaningful tests
5. Follow consistent naming conventions

[Chart: Performance metrics]

## Summary
Modern web development combines React's component-based architecture with TypeScript's type safety and powerful tools like TanStack Query for state management. This creates robust, maintainable applications with excellent developer experience.

[failed to load image data]

**Note**: Some images and charts from the original PDF could not be displayed but the text content has been preserved.`,
  status: "success",
  message: "Content processed successfully"
}

interface FlashcardDemoProps {
  className?: string
}

export default function FlashcardDemo({ className }: FlashcardDemoProps) {
  const [showDemo, setShowDemo] = useState(false)

  const handleStartDemo = () => {
    setShowDemo(true)
  }

  const handleBackToStart = () => {
    setShowDemo(false)
  }

  if (showDemo) {
    return (
      <AIResultsDisplay
        results={sampleAIResults}
        onBack={handleBackToStart}
        className={className}
      />
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>Flashcard Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Test the improved flashcard functionality with sample data including:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-1">Quiz Questions</h4>
              <p className="text-blue-700">3 interactive multiple choice questions</p>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <h4 className="font-medium text-green-900 mb-1">Flashcards</h4>
              <p className="text-green-700">5 cards about web development</p>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-1">Study Notes</h4>
              <p className="text-purple-700">Comprehensive notes with formatting</p>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h5 className="font-medium text-yellow-900 mb-2">✨ New Features:</h5>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• <strong>Flashcards</strong>: Smooth transitions, keyboard navigation, progress tracking</li>
              <li>• <strong>Notes</strong>: Smart image error handling, markdown formatting, copy/download</li>
              <li>• <strong>File Upload</strong>: Drag & drop support, file validation, visual feedback</li>
              <li>• <strong>Error Handling</strong>: Graceful handling of "failed to load image data" errors</li>
            </ul>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">🔧 PDF Processing Notes:</h5>
            <p className="text-sm text-blue-800">
              This demo includes sample notes with image loading errors (like "failed to load image data")
              to demonstrate how the improved notes renderer handles problematic content from PDF processing.
            </p>
          </div>

          <Button onClick={handleStartDemo} className="w-full">
            Start Flashcard Demo
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
