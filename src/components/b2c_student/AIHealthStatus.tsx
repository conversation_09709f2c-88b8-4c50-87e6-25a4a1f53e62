import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  AlertTriangle
} from 'lucide-react'
import { useAIHealthCheck } from '@/hooks/useAI'

interface AIHealthStatusProps {
  className?: string
}

export default function AIHealthStatus({ className }: AIHealthStatusProps) {
  const { data: health, isLoading, isError, error, refetch } = useAIHealthCheck()

  const getStatusIcon = () => {
    if (isLoading) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (isError) return <XCircle className="h-4 w-4" />
    if (health?.status === 'running') return <CheckCircle className="h-4 w-4" />
    return <AlertTriangle className="h-4 w-4" />
  }

  const getStatusBadge = () => {
    if (isLoading) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <RefreshCw className="h-3 w-3 animate-spin" />
          Checking...
        </Badge>
      )
    }
    
    if (isError) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <WifiOff className="h-3 w-3" />
          Offline
        </Badge>
      )
    }
    
    if (health?.status === 'running') {
      return (
        <Badge variant="default" className="flex items-center gap-1 bg-green-500">
          <Wifi className="h-3 w-3" />
          Online
        </Badge>
      )
    }
    
    return (
      <Badge variant="secondary" className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Unknown
      </Badge>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <span className="flex items-center gap-2">
            {getStatusIcon()}
            AI Service Status
          </span>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Service Information */}
          {health && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Service:</span>
                <span className="font-medium">{health.message}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Version:</span>
                <span className="font-medium">{health.version}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status:</span>
                <span className="font-medium capitalize">{health.status}</span>
              </div>
            </div>
          )}

          {/* Error Information */}
          {isError && (
            <div className="text-sm">
              <p className="text-red-600 mb-2">
                Unable to connect to AI service
              </p>
              <p className="text-muted-foreground text-xs">
                {error?.message || 'Please check if the AI service is running on localhost:8000'}
              </p>
            </div>
          )}

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
            className="w-full"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>

          {/* Service URL Info */}
          <div className="text-xs text-muted-foreground text-center">
            Service URL: https://ai.jeridschool.tech/
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
