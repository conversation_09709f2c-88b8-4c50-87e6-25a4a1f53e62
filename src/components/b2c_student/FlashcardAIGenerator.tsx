import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Slider } from '@/components/ui/slider'
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { FlashcardDeckType, Flashcard } from './FlashcardTypes'
import axios from 'axios'

interface FlashcardAIGeneratorProps {
  deck: FlashcardDeckType
  onUpdateDeck: (updatedDeck: FlashcardDeckType) => void
  onClose: () => void
}

// Get OpenAI API key from environment variables
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY

export default function FlashcardAIGenerator({
  deck,
  onUpdateDeck,
  onClose,
}: FlashcardAIGeneratorProps) {
  const [topic, setTopic] = useState('')
  const [numCards, setNumCards] = useState(5)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const { toast } = useToast()

  const generateFlashcards = async () => {
    if (!topic.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a topic for your flashcards',
        variant: 'destructive',
      })
      return
    }

    setIsGenerating(true)
    setGeneratedContent('')

    try {
      // Prepare the prompt for the AI
      const prompt = `Create ${numCards} flashcards about "${topic}".
      Format each flashcard as JSON with "front" and "back" properties.
      The front should contain a question or concept, and the back should contain the answer or explanation.
      Return only the JSON array without any additional text.
      Example format:
      [
        {
          "front": "What is photosynthesis?",
          "back": "The process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water."
        },
        {
          "front": "What is the capital of France?",
          "back": "Paris"
        }
      ]`

      // Call OpenAI API
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful assistant that creates educational flashcards.',
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.7,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${OPENAI_API_KEY}`,
          },
        }
      )

      // Extract the generated content
      const aiResponse = response.data.choices[0].message.content
      setGeneratedContent(aiResponse)

      // Parse the JSON response
      try {
        const flashcardsData = JSON.parse(aiResponse)

        // Create new flashcards from the AI-generated content
        const newFlashcards: Flashcard[] = flashcardsData.map(
          (item: any, index: number) => ({
            id: `temp-${Date.now()}-${index}`,
            front: { text: item.front },
            back: { text: item.back },
            deckId: deck.id,
            order: (deck.cards?.length || 0) + index,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            frontText: item.front,
            backText: item.back,
          })
        )

        // Update the deck with the new flashcards
        const updatedDeck = {
          ...deck,
          cards: [...(deck.cards || []), ...newFlashcards],
        }

        onUpdateDeck(updatedDeck)

        toast({
          title: 'Success',
          description: `Generated ${newFlashcards.length} flashcards about "${topic}"`,
        })

        // Close the generator
        onClose()
      } catch (parseError) {
        console.error('Failed to parse AI response:', parseError)
        toast({
          title: 'Error',
          description:
            'Failed to parse the AI-generated content. Please try again.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error generating flashcards:', error)
      toast({
        title: 'Error',
        description: 'Failed to generate flashcards. Please try again later.',
        variant: 'destructive',
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Sparkles className="h-5 w-5 mr-2 text-primary" />
          AI Flashcard Generator
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <label htmlFor="topic" className="block text-sm font-medium mb-1">
              Topic
            </label>
            <Textarea
              id="topic"
              placeholder="Enter a topic or concept for your flashcards (e.g., 'Photosynthesis', 'French Revolution', 'JavaScript Basics')"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div>
            <label
              htmlFor="num-cards"
              className="block text-sm font-medium mb-1"
            >
              Number of Flashcards: {numCards}
            </label>
            <Slider
              id="num-cards"
              min={1}
              max={20}
              step={1}
              value={[numCards]}
              onValueChange={(value) => setNumCards(value[0])}
              className="py-4"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={generateFlashcards}
              disabled={isGenerating || !topic.trim()}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Flashcards
                </>
              )}
            </Button>
          </div>

          {generatedContent && (
            <div className="mt-4 p-4 bg-muted rounded-md">
              <h3 className="text-sm font-medium mb-2">
                Generated Content (Debug):
              </h3>
              <pre className="text-xs overflow-auto max-h-[200px]">
                {generatedContent}
              </pre>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
