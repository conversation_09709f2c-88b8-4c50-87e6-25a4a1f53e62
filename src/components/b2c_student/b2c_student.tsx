import { Card, CardContent } from '../ui/card'
import { services } from '../../mockData/B2C_Student'
import {  Brain } from 'lucide-react'
import FileUploadChat from './FileUploadChat'

export default function B2C_StudentService() {
  const handleServiceClick = (path: string) => {
    if (path.startsWith('#')) {
      // Stay on the same page for hash links
      window.location.hash = path
    } else {
      window.location.href = path
    }
  }

  return (
    <>
      <div className="flex flex-col max-w-5xl mx-auto p-6 space-y-8">
        <section>
          <h2 className="text-2xl font-bold mb-4">Services</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {/* Main Services */}
            {services.map((service, index) => (
              <Card
                key={index}
                className="hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => handleServiceClick(service.href)}
              >
                <CardContent className="p-4">
                  <div className="flex flex-col items-center text-center space-y-2">
                    {service.icon}
                    <span className="text-sm font-medium">{service.name}</span>
                  </div>
                </CardContent>
              </Card>
            ))}


            <Card className="relative hover:bg-muted/50 transition-colors cursor-pointer">
              <CardContent className="p-4">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="relative">
                    <Brain className="h-6 w-6" />
                  </div>
                  <span className="absolute top-0 right-0 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-bl-md rounded-tr-md z-10">
                    SOON
                  </span>
                  <span className="text-sm font-medium">AI Assistant</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
        <FileUploadChat className="w-full" />
      </div>
    </>
  )
}
