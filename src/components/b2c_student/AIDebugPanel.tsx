import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Bug, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  WifiOff
} from 'lucide-react'
import { useAIHealthCheck } from '@/hooks/useAI'
import { aiService } from '@/services/aiService'

interface AIDebugPanelProps {
  className?: string
}

export default function AIDebugPanel({ className }: AIDebugPanelProps) {
  const [testResults, setTestResults] = useState<any>(null)
  const [isTestingFile, setIsTestingFile] = useState(false)
  const { data: health, isLoading, isError, error, refetch } = useAIHealthCheck()

  // Test file processing with a simple text file
  const testFileProcessing = async () => {
    setIsTestingFile(true)
    setTestResults(null)

    try {
      // Create a simple test file
      const testContent = "This is a test document for AI processing. It contains some basic text to verify that file upload and processing works correctly."
      const testFile = new File([testContent], 'test.txt', { type: 'text/plain' })

      console.log('Testing file processing with:', {
        fileName: testFile.name,
        fileSize: testFile.size,
        fileType: testFile.type
      })

      const result = await aiService.processFile(testFile, 'Create a simple summary', 'test-user')
      
      setTestResults({
        success: true,
        result,
        message: 'File processing test successful!'
      })
    } catch (error: any) {
      console.error('File processing test failed:', error)
      setTestResults({
        success: false,
        error: error.message,
        details: {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }
      })
    } finally {
      setIsTestingFile(false)
    }
  }

  const getStatusIcon = () => {
    if (isLoading) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (isError) return <XCircle className="h-4 w-4 text-red-500" />
    if (health?.status === 'running') return <CheckCircle className="h-4 w-4 text-green-500" />
    return <AlertTriangle className="h-4 w-4 text-yellow-500" />
  }

  const getStatusBadge = () => {
    if (isLoading) {
      return <Badge variant="secondary">Checking...</Badge>
    }
    if (isError) {
      return <Badge variant="destructive">Offline</Badge>
    }
    if (health?.status === 'running') {
      return <Badge className="bg-green-500">Online</Badge>
    }
    return <Badge variant="secondary">Unknown</Badge>
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          AI Service Debug Panel
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Service Status */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="font-medium">Service Status</span>
            </div>
            {getStatusBadge()}
          </div>

          {health && (
            <div className="text-sm space-y-1 bg-gray-50 p-3 rounded-md">
              <div><strong>Message:</strong> {health.message}</div>
              <div><strong>Version:</strong> {health.version}</div>
              <div><strong>Status:</strong> {health.status}</div>
            </div>
          )}

          {isError && (
            <Alert variant="destructive">
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                <strong>Connection Error:</strong> {error?.message}
                <br />
                <small>Make sure the AI service is running on http://localhost:8000</small>
              </AlertDescription>
            </Alert>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
            className="w-full"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
        </div>

        {/* File Processing Test */}
        <div className="border-t pt-4 space-y-3">
          <h4 className="font-medium">File Processing Test</h4>
          
          <Button
            onClick={testFileProcessing}
            disabled={isTestingFile || isError}
            className="w-full"
          >
            {isTestingFile ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Testing File Processing...
              </>
            ) : (
              'Test File Processing'
            )}
          </Button>

          {testResults && (
            <Alert variant={testResults.success ? "default" : "destructive"}>
              {testResults.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>{testResults.message || 'Test Failed'}</strong></div>
                  
                  {testResults.success && testResults.result && (
                    <div className="text-xs bg-green-50 p-2 rounded">
                      <div><strong>Quiz Items:</strong> {testResults.result.quiz?.length || 0}</div>
                      <div><strong>Flashcards:</strong> {testResults.result.flashcards?.length || 0}</div>
                      <div><strong>Notes:</strong> {testResults.result.notes ? 'Generated' : 'None'}</div>
                    </div>
                  )}
                  
                  {!testResults.success && (
                    <div className="text-xs bg-red-50 p-2 rounded">
                      <div><strong>Error:</strong> {testResults.error}</div>
                      {testResults.details && (
                        <div className="mt-1">
                          <div><strong>Status:</strong> {testResults.details.status}</div>
                          <div><strong>Status Text:</strong> {testResults.details.statusText}</div>
                          {testResults.details.data && (
                            <div><strong>Details:</strong> {JSON.stringify(testResults.details.data)}</div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Troubleshooting Tips */}
        <div className="border-t pt-4 space-y-2">
          <h4 className="font-medium">Troubleshooting Tips</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>• Check browser console for detailed error logs</div>
            <div>• Ensure AI service is running on localhost:8000</div>
            <div>• Try smaller files (under 5MB) first</div>
            <div>• Check network connectivity</div>
            <div>• Verify file format is supported (PDF, DOC, TXT, etc.)</div>
          </div>
        </div>

        {/* Service Info */}
        <div className="border-t pt-4 text-xs text-gray-500">
          <div><strong>Service URL:</strong> http://localhost:8000</div>
          <div><strong>Timeout:</strong> 3 minutes</div>
          <div><strong>Supported Files:</strong> PDF, DOC, DOCX, TXT, CSV, JPG, PNG</div>
        </div>
      </CardContent>
    </Card>
  )
}
