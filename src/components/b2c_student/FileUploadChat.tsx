import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Send, File, Image, X } from 'lucide-react'
import FileUploader from '@/components/shared/FileUploader'
import { cn } from '@/lib/utils'

interface FileUploadChatProps {
  className?: string
}

type FileType = 'pdf' | 'image' | null

export default function FileUploadChat({ className }: FileUploadChatProps) {
  const [message, setMessage] = useState('')
  const [_, setIsUploading] = useState(false)
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string | null>(null)
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null)
  const [fileType, setFileType] = useState<FileType>(null)
  const [showUploader, setShowUploader] = useState(false)
  // Handle file upload completion
  const handleFileUploaded = (url: string, fileName?: string) => {
    setUploadedFileUrl(url)
    setUploadedFileName(fileName || 'Uploaded file')
    setIsUploading(false)
    setShowUploader(false)
  }

  // Handle file upload error
  const handleUploadError = (error: Error) => {
    console.error('File upload error:', error)
    setIsUploading(false)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!message.trim() && !uploadedFileUrl) return
    
    // Here you would typically send the message and file URL to your backend
    console.log('Sending message:', message)
    console.log('With file:', uploadedFileUrl)
    
    // Reset form
    setMessage('')
    setUploadedFileUrl(null)
    setUploadedFileName(null)
    setFileType(null)
  }

  // Toggle file uploader for specific file type
  const toggleUploader = (type: FileType) => {
    setFileType(type)
    setShowUploader(true)
  }

  // Cancel file upload
  const cancelFileUpload = () => {
    setUploadedFileUrl(null)
    setUploadedFileName(null)
    setFileType(null)
    setShowUploader(false)
  }

  return (
    <Card className={cn("hover:bg-muted/50 transition-colors", className)}>
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Message display area would go here in a real chat application */}
          <div className="min-h-[100px] bg-gray-50 rounded-md p-3 text-sm text-gray-500 flex items-center justify-center">
            Upload files or type a message to get started
          </div>

          {/* File upload area - shown when user clicks upload button */}
          {showUploader && (
            <div className="border rounded-md p-3 relative">
              <Button 
                variant="ghost" 
                size="icon" 
                className="absolute top-2 right-2"
                onClick={cancelFileUpload}
              >
                <X className="h-4 w-4" />
              </Button>
              
              <FileUploader
                onFileUploaded={(url) => handleFileUploaded(url)}
                onError={handleUploadError}
                accept={fileType === 'pdf' ? '.pdf,application/pdf' : 'image/*'}
                label={fileType === 'pdf' ? 'Upload PDF' : 'Upload Image'}
              />
            </div>
          )}

          {/* Uploaded file preview */}
          {uploadedFileUrl && (
            <div className="border rounded-md p-2 bg-blue-50 flex items-center justify-between">
              <div className="flex items-center">
                {fileType === 'pdf' ? (
                  <File className="h-5 w-5 text-blue-500 mr-2" />
                ) : (
                  <Image className="h-5 w-5 text-blue-500 mr-2" />
                )}
                <span className="text-sm truncate max-w-[200px]">
                  {uploadedFileName}
                </span>
              </div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={cancelFileUpload}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Input area with file upload options */}
          <form onSubmit={handleSubmit} className="flex items-end space-x-2">
            <div className="flex-1 space-y-2">
              <Input
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message here..."
                className="w-full"
              />
              
              <div className="flex space-x-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={() => toggleUploader('pdf')}
                  className="text-xs"
                >
                  <File className="h-3 w-3 mr-1" />
                  PDF
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={() => toggleUploader('image')}
                  className="text-xs"
                >
                  <Image className="h-3 w-3 mr-1" />
                  Image
                </Button>
              </div>
            </div>
            
            <Button 
              type="submit" 
              disabled={!message.trim() && !uploadedFileUrl}
              className="h-10"
            >
              <Send className="h-4 w-4 mr-1" />
              Send
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  )
}
