import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  Copy, 
  Download, 
  Eye, 
  EyeOff,
  Image as ImageIcon,
  FileText
} from 'lucide-react'
import { toast } from 'sonner'

interface NotesRendererProps {
  notes: string
  className?: string
}

export default function NotesRenderer({ notes, className }: NotesRendererProps) {
  const [showRawText, setShowRawText] = useState(false)

  // Clean and process the notes content
  const processNotes = (rawNotes: string) => {
    // Remove or replace problematic image references
    let processedNotes = rawNotes
      .replace(/\[failed to load image data\]/gi, '')
      .replace(/!\[.*?\]\(data:image\/.*?\)/g, '[Image content not available]')
      .replace(/!\[.*?\]\(.*?\.(?:png|jpg|jpeg|gif|svg).*?\)/gi, '[Image content not available]')
      .replace(/\[Image:.*?\]/gi, '[Image content not available]')
      .replace(/\[Chart:.*?\]/gi, '[Chart content not available]')
      .replace(/\[Figure:.*?\]/gi, '[Figure content not available]')
    
    // Clean up multiple line breaks
    processedNotes = processedNotes.replace(/\n{3,}/g, '\n\n')
    
    // Clean up empty brackets and parentheses
    processedNotes = processedNotes.replace(/\[\s*\]/g, '')
    processedNotes = processedNotes.replace(/\(\s*\)/g, '')
    
    return processedNotes.trim()
  }

  // Copy notes to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(processNotes(notes))
      toast.success('Notes copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy notes')
    }
  }

  // Download notes as text file
  const downloadNotes = () => {
    const blob = new Blob([processNotes(notes)], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-notes-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Notes downloaded!')
  }

  // Format text with basic markdown-like styling
  const formatText = (text: string) => {
    const lines = text.split('\n')
    return lines.map((line, index) => {
      // Headers
      if (line.startsWith('# ')) {
        return (
          <h1 key={index} className="text-xl font-bold mt-6 mb-3 text-gray-900">
            {line.substring(2)}
          </h1>
        )
      }
      if (line.startsWith('## ')) {
        return (
          <h2 key={index} className="text-lg font-semibold mt-5 mb-2 text-gray-800">
            {line.substring(3)}
          </h2>
        )
      }
      if (line.startsWith('### ')) {
        return (
          <h3 key={index} className="text-base font-medium mt-4 mb-2 text-gray-700">
            {line.substring(4)}
          </h3>
        )
      }
      
      // Bullet points
      if (line.trim().startsWith('- ') || line.trim().startsWith('* ')) {
        return (
          <li key={index} className="ml-4 mb-1 text-gray-700">
            {line.trim().substring(2)}
          </li>
        )
      }
      
      // Numbered lists
      if (/^\d+\.\s/.test(line.trim())) {
        return (
          <li key={index} className="ml-4 mb-1 text-gray-700 list-decimal">
            {line.trim().replace(/^\d+\.\s/, '')}
          </li>
        )
      }
      
      // Bold text
      if (line.includes('**')) {
        const parts = line.split('**')
        return (
          <p key={index} className="mb-2 text-gray-700 leading-relaxed">
            {parts.map((part, i) => 
              i % 2 === 1 ? <strong key={i}>{part}</strong> : part
            )}
          </p>
        )
      }
      
      // Empty lines
      if (line.trim() === '') {
        return <br key={index} />
      }
      
      // Regular paragraphs
      return (
        <p key={index} className="mb-2 text-gray-700 leading-relaxed">
          {line}
        </p>
      )
    })
  }

  const processedNotes = processNotes(notes)
  const hasImageErrors = notes.toLowerCase().includes('failed to load image') || 
                        notes.toLowerCase().includes('[image') ||
                        notes.includes('data:image/')

  return (
    <div className={className}>
      {/* Header with actions */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-gray-600" />
          <span className="font-medium text-gray-900">AI Generated Notes</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowRawText(!showRawText)}
            className="text-xs"
          >
            {showRawText ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
            {showRawText ? 'Formatted' : 'Raw Text'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={copyToClipboard}
            className="text-xs"
          >
            <Copy className="h-3 w-3 mr-1" />
            Copy
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={downloadNotes}
            className="text-xs"
          >
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
        </div>
      </div>

      {/* Image error warning */}
      {hasImageErrors && (
        <Alert className="mb-4 border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>Note:</strong> Some images or charts from the PDF could not be displayed. 
            The text content has been preserved and cleaned up for better readability.
          </AlertDescription>
        </Alert>
      )}

      {/* Notes content */}
      <Card>
        <CardContent className="p-6">
          {processedNotes ? (
            <div className="prose prose-sm max-w-none">
              {showRawText ? (
                <pre className="whitespace-pre-wrap text-sm font-mono bg-gray-50 p-4 rounded-md overflow-x-auto">
                  {processedNotes}
                </pre>
              ) : (
                <div className="space-y-1">
                  {formatText(processedNotes)}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <ImageIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No notes were generated.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Word count and reading time */}
      {processedNotes && (
        <div className="mt-3 text-xs text-gray-500 text-center">
          {processedNotes.split(/\s+/).length} words • 
          {Math.ceil(processedNotes.split(/\s+/).length / 200)} min read
        </div>
      )}
    </div>
  )
}
