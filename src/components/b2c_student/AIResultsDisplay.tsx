import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, <PERSON><PERSON>Content } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  BookOpen,
  Brain,
  StickyNote,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  CheckCircle,
  XCircle,
  ArrowLeft
} from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { ProcessAIResponse } from '@/services/aiService'
import { cn } from '@/lib/utils'
import NotesRenderer from './NotesRenderer'

interface AIResultsDisplayProps {
  results: ProcessAIResponse
  onBack: () => void
  className?: string
}

export default function AIResultsDisplay({
  results,
  onBack,
  className
}: AIResultsDisplayProps) {
  // Quiz state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswers, setSelectedAnswers] = useState<Record<number, string>>({})
  const [showQuizResults, setShowQuizResults] = useState(false)

  // Flashcard state
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [isFlipped, setIsFlipped] = useState(false)

  const { quiz = [], flashcards = [], notes = '' } = results

  // Quiz functions
  const handleAnswerSelect = (questionIndex: number, answer: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }))
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < quiz.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    } else {
      setShowQuizResults(true)
    }
  }

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setSelectedAnswers({})
    setShowQuizResults(false)
  }

  const calculateQuizScore = () => {
    let correct = 0
    quiz.forEach((question, index) => {
      if (selectedAnswers[index] === question.answer) {
        correct++
      }
    })
    return { correct, total: quiz.length, percentage: Math.round((correct / quiz.length) * 100) }
  }

  // Flashcard functions
  const handleNextCard = () => {
    if (currentCardIndex < flashcards.length - 1) {
      setCurrentCardIndex(prev => prev + 1)
      setIsFlipped(false) // Reset flip state when changing cards
    }
  }

  const handlePrevCard = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(prev => prev - 1)
      setIsFlipped(false) // Reset flip state when changing cards
    }
  }

  const handleFlipCard = () => {
    setIsFlipped(prev => !prev)
  }


  // Keyboard navigation for flashcards
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle keyboard events when flashcards tab is active
      if (flashcards.length === 0) return

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          handlePrevCard()
          break
        case 'ArrowRight':
          event.preventDefault()
          handleNextCard()
          break
        case ' ':
        case 'Enter':
          event.preventDefault()
          handleFlipCard()
          break
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [currentCardIndex, flashcards.length])

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Processing
        </Button>
        <Badge variant="secondary" className="text-sm">
          AI Generated Content
        </Badge>
      </div>

      <Tabs defaultValue="quiz" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quiz" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Quiz ({quiz.length})
          </TabsTrigger>
          <TabsTrigger value="flashcards" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Flashcards ({flashcards.length})
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center gap-2">
            <StickyNote className="h-4 w-4" />
            Notes
          </TabsTrigger>
        </TabsList>

        {/* Quiz Tab */}
        <TabsContent value="quiz" className="space-y-4">
          {quiz.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Interactive Quiz</span>
                  <Button variant="outline" size="sm" onClick={resetQuiz}>
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Reset
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!showQuizResults ? (
                  <div className="space-y-6">
                    {/* Question Progress */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Question {currentQuestionIndex + 1} of {quiz.length}</span>
                      <span>{Math.round(((currentQuestionIndex + 1) / quiz.length) * 100)}% Complete</span>
                    </div>

                    {/* Current Question */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">
                        {quiz[currentQuestionIndex]?.question}
                      </h3>

                      {/* Answer Options */}
                      <div className="space-y-2">
                        {quiz[currentQuestionIndex]?.options.map((option, optionIndex) => (
                          <Button
                            key={optionIndex}
                            variant={selectedAnswers[currentQuestionIndex] === option ? "default" : "outline"}
                            className="w-full justify-start text-left h-auto p-3"
                            onClick={() => handleAnswerSelect(currentQuestionIndex, option)}
                          >
                            <span className="mr-2 font-medium">
                              {String.fromCharCode(65 + optionIndex)}.
                            </span>
                            {option}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Navigation */}
                    <div className="flex justify-between">
                      <Button
                        variant="outline"
                        onClick={handlePrevQuestion}
                        disabled={currentQuestionIndex === 0}
                      >
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        Previous
                      </Button>
                      <Button
                        onClick={handleNextQuestion}
                        disabled={!selectedAnswers[currentQuestionIndex]}
                      >
                        {currentQuestionIndex === quiz.length - 1 ? 'Finish Quiz' : 'Next'}
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  /* Quiz Results */
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold mb-2">Quiz Complete!</h3>
                      <div className="text-3xl font-bold text-primary mb-4">
                        {calculateQuizScore().percentage}%
                      </div>
                      <p className="text-muted-foreground">
                        You got {calculateQuizScore().correct} out of {calculateQuizScore().total} questions correct
                      </p>
                    </div>

                    {/* Answer Review */}
                    <div className="space-y-3">
                      <h4 className="font-medium">Review Your Answers:</h4>
                      {quiz.map((question, index) => {
                        const isCorrect = selectedAnswers[index] === question.answer
                        return (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex items-start gap-2">
                              {isCorrect ? (
                                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                              )}
                              <div className="flex-1">
                                <p className="font-medium text-sm">{question.question}</p>
                                <p className="text-sm text-muted-foreground mt-1">
                                  Your answer: {selectedAnswers[index] || 'Not answered'}
                                </p>
                                {!isCorrect && (
                                  <p className="text-sm text-green-600 mt-1">
                                    Correct answer: {question.answer}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6 text-center text-muted-foreground">
                No quiz questions were generated.
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Flashcards Tab */}
        <TabsContent value="flashcards" className="space-y-4">
          {flashcards.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Interactive Flashcards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Card Progress */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Card {currentCardIndex + 1} of {flashcards.length}</span>
                      <div className="text-xs space-x-2">
                        <span>Click to flip</span>
                        <span>•</span>
                        <span>← → to navigate</span>
                        <span>•</span>
                        <span>Space to flip</span>
                      </div>
                    </div>
                    <Progress
                      value={((currentCardIndex + 1) / flashcards.length) * 100}
                      className="h-2"
                    />
                  </div>

                  {/* Flashcard */}
                  <div className="relative h-64 cursor-pointer group" onClick={handleFlipCard}>
                    <div className={cn(
                      "absolute inset-0 transition-all duration-300 ease-in-out transform",
                      !isFlipped ? "opacity-100 scale-100" : "opacity-0 scale-95"
                    )}>
                      {/* Front of card */}
                      <Card className="h-full border-2 border-primary/20 hover:border-primary/40 transition-all duration-200 group-hover:shadow-lg">
                        <CardContent className="h-full flex flex-col items-center justify-center p-6 text-center">
                          <div className="mb-4">
                            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2 group-hover:bg-primary/20 transition-colors">
                              <BookOpen className="h-6 w-6 text-primary" />
                            </div>
                            <p className="text-xs text-muted-foreground uppercase tracking-wide">Question</p>
                          </div>
                          <p className="text-lg font-medium leading-relaxed">
                            {flashcards[currentCardIndex]?.front}
                          </p>
                          <p className="text-xs text-muted-foreground mt-4 opacity-70 group-hover:opacity-100 transition-opacity">
                            Click to reveal answer
                          </p>
                        </CardContent>
                      </Card>
                    </div>

                    <div className={cn(
                      "absolute inset-0 transition-all duration-300 ease-in-out transform",
                      isFlipped ? "opacity-100 scale-100" : "opacity-0 scale-95"
                    )}>
                      {/* Back of card */}
                      <Card className="h-full border-2 border-green-200 bg-green-50/50 group-hover:shadow-lg transition-all duration-200">
                        <CardContent className="h-full flex flex-col items-center justify-center p-6 text-center">
                          <div className="mb-4">
                            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2 group-hover:bg-green-200 transition-colors">
                              <CheckCircle className="h-6 w-6 text-green-600" />
                            </div>
                            <p className="text-xs text-green-600 uppercase tracking-wide">Answer</p>
                          </div>
                          <p className="text-lg font-medium leading-relaxed text-green-800">
                            {flashcards[currentCardIndex]?.back}
                          </p>
                          <p className="text-xs text-green-600 mt-4 opacity-70 group-hover:opacity-100 transition-opacity">
                            Click to see question
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={handlePrevCard}
                      disabled={currentCardIndex === 0}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleNextCard}
                      disabled={currentCardIndex === flashcards.length - 1}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6 text-center text-muted-foreground">
                No flashcards were generated.
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Notes Tab */}
        <TabsContent value="notes" className="space-y-4">
          <NotesRenderer notes={notes} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
