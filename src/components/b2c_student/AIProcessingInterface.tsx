import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import {
  Youtube,
  Upload,
  Loader2,
  CheckCircle,
  AlertCircle,
  FileText,
  BookOpen,
  X
} from 'lucide-react'
import { useAIProcessing } from '@/hooks/useAI'
import { ProcessAIResponse } from '@/services/aiService'
import { cn } from '@/lib/utils'

interface AIProcessingInterfaceProps {
  onResultsGenerated: (results: ProcessAIResponse) => void
  className?: string
}

export default function AIProcessingInterface({
  onResultsGenerated,
  className
}: AIProcessingInterfaceProps) {
  // State
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [customPrompt, setCustomPrompt] = useState('')
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [activeTab, setActiveTab] = useState('youtube')
  const [processingProgress, setProcessingProgress] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)

  // Hooks
  const { processYouTube, processFile, isProcessing } = useAIProcessing()

  // Get user ID from localStorage or auth context
  const userId = localStorage.getItem('user_id') || 'anonymous'

  // Handle YouTube processing
  const handleYouTubeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!youtubeUrl.trim()) return

    setProcessingProgress(10)

    try {
      const result = await processYouTube.mutateAsync({
        youtube_url: youtubeUrl,
        custom_prompt: customPrompt || undefined,
        user_id: userId,
      })

      setProcessingProgress(100)
      onResultsGenerated(result)

      // Reset form
      setYoutubeUrl('')
      setCustomPrompt('')
    } catch (error) {
      setProcessingProgress(0)
    }
  }

  // Handle file processing
  const handleFileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!uploadedFile) return

    setProcessingProgress(10)

    try {
      console.log('Starting file processing...', {
        fileName: uploadedFile.name,
        fileSize: uploadedFile.size,
        fileType: uploadedFile.type,
        userId,
        customPrompt: customPrompt || 'none'
      })

      const result = await processFile.mutateAsync({
        file: uploadedFile,
        customPrompt: customPrompt || undefined,
        userId,
      })

      console.log('File processing completed:', result)
      setProcessingProgress(100)
      onResultsGenerated(result)

      // Reset form
      setUploadedFile(null)
      setCustomPrompt('')
    } catch (error) {
      console.error('File processing failed:', error)
      setProcessingProgress(0)
    }
  }

  // Handle file selection (for AI processing we need the actual file, not URL)
  const handleFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      setUploadedFile(files[0])
    }
  }



  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      const file = files[0]
      // Check file type
      const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.csv', '.jpg', '.jpeg', '.png']
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

      if (allowedTypes.includes(fileExtension)) {
        setUploadedFile(file)
      } else {
        alert('Please upload a valid file type: PDF, DOC, TXT, CSV, JPG, PNG')
      }
    }
  }

  // Simulate progress during processing
  useEffect(() => {
    if (isProcessing && processingProgress < 90) {
      const interval = setInterval(() => {
        setProcessingProgress(prev => Math.min(prev + 5, 90))
      }, 1000)
      return () => clearInterval(interval)
    }
  }, [isProcessing, processingProgress])

  // Reset progress when processing stops
  useEffect(() => {
    if (!isProcessing) {
      const timer = setTimeout(() => {
        setProcessingProgress(0)
      }, 2000) // Reset after 2 seconds
      return () => clearTimeout(timer)
    }
  }, [isProcessing])

  // Add timeout protection
  useEffect(() => {
    if (isProcessing) {
      const timeout = setTimeout(() => {
        console.warn('Processing timeout - this may indicate an issue with the AI service')
        setProcessingProgress(0)
      }, 180000) // 3 minutes timeout
      return () => clearTimeout(timeout)
    }
  }, [isProcessing])

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          AI Content Processing
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="youtube" className="flex items-center gap-2">
              <Youtube className="h-4 w-4" />
              YouTube
            </TabsTrigger>
            <TabsTrigger value="file" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              File Upload
            </TabsTrigger>
          </TabsList>

          {/* YouTube Tab */}
          <TabsContent value="youtube" className="space-y-4">
            <form onSubmit={handleYouTubeSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="youtube-url">YouTube URL</Label>
                <Input
                  id="youtube-url"
                  type="url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                  disabled={isProcessing}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-prompt">Custom Prompt (Optional)</Label>
                <Textarea
                  id="custom-prompt"
                  placeholder="e.g., Create a quiz focusing on the main concepts..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  disabled={isProcessing}
                  rows={3}
                />
              </div>

              <Button
                type="submit"
                disabled={!youtubeUrl.trim() || isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Youtube className="h-4 w-4 mr-2" />
                    Process YouTube Video
                  </>
                )}
              </Button>
            </form>
          </TabsContent>

          {/* File Upload Tab */}
          <TabsContent value="file" className="space-y-4">
            <form onSubmit={handleFileSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="file-input">Upload File</Label>
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                    isDragOver
                      ? "border-primary bg-primary/5"
                      : "border-gray-300 hover:border-gray-400"
                  )}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <input
                    id="file-input"
                    type="file"
                    accept=".pdf,.doc,.docx,.txt,.csv,.jpg,.jpeg,.png"
                    onChange={handleFileSelected}
                    className="hidden"
                    disabled={isProcessing}
                  />
                  <label
                    htmlFor="file-input"
                    className="cursor-pointer flex flex-col items-center space-y-2"
                  >
                    <Upload className={cn(
                      "h-8 w-8 transition-colors",
                      isDragOver ? "text-primary" : "text-gray-400"
                    )} />
                    <span className={cn(
                      "text-sm transition-colors",
                      isDragOver ? "text-primary font-medium" : "text-gray-600"
                    )}>
                      {isDragOver ? "Drop your file here" : "Click to upload or drag and drop"}
                    </span>
                    <span className="text-xs text-gray-500">
                      PDF, DOC, TXT, CSV, JPG, PNG (Max 10MB)
                    </span>
                  </label>
                </div>
                {uploadedFile && (
                  <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded-md">
                    <CheckCircle className="h-4 w-4" />
                    <span className="font-medium">{uploadedFile.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setUploadedFile(null)}
                      className="ml-auto h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="file-custom-prompt">Custom Prompt (Optional)</Label>
                <Textarea
                  id="file-custom-prompt"
                  placeholder="e.g., Create flashcards from this document..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  disabled={isProcessing}
                  rows={3}
                />
              </div>

              <Button
                type="submit"
                disabled={!uploadedFile || isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Process File
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>

        {/* Processing Progress */}
        {isProcessing && (
          <div className="mt-6 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Processing with AI...</span>
              <span>{processingProgress}%</span>
            </div>
            <Progress value={processingProgress} className="w-full" />
            <p className="text-xs text-muted-foreground">
              This may take 30-60 seconds. Please wait...
            </p>
          </div>
        )}

        {/* Error Display */}
        {(processYouTube.isError || processFile.isError) && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Processing Failed</span>
            </div>
            <p className="text-sm text-red-600 mt-1">
              {processYouTube.error?.message || processFile.error?.message}
            </p>
          </div>
        )}

        {/* Success Message */}
        {(processYouTube.isSuccess || processFile.isSuccess) && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Content Processed Successfully!</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              Your quiz, flashcards, and notes are ready.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
