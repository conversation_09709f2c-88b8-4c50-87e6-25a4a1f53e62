import { useState } from 'react'
import { Card, Button, Tabs, Input, Select, Form, Divider, Empty } from 'antd'
import { PlusOutlined, SearchOutlined, StarOutlined } from '@ant-design/icons'
import { Clock as ClockIcon } from 'lucide-react'

// Define the types for the flashcards
interface Flashcard {
  id: string
  question: string
  answer: string
  subject: string
  tags: string[]
  isFavorite: boolean
  lastReviewed?: string
}

// Mock data for the flashcards
const mockFlashcards: Flashcard[] = [
  {
    id: '1',
    question: 'What is the capital of France?',
    answer: 'Paris',
    subject: 'Geography',
    tags: ['Europe', 'Capitals'],
    isFavorite: true,
    lastReviewed: '2023-05-15',
  },
  {
    id: '2',
    question: 'What is the formula for the area of a circle?',
    answer: 'A = πr²',
    subject: 'Mathematics',
    tags: ['Geometry', 'Formulas'],
    isFavorite: false,
    lastReviewed: '2023-05-10',
  },
  {
    id: '3',
    question: 'What is the chemical symbol for gold?',
    answer: 'Au',
    subject: 'Chemistry',
    tags: ['Elements', 'Periodic Table'],
    isFavorite: true,
    lastReviewed: '2023-05-12',
  },
  {
    id: '4',
    question: 'Who wrote "<PERSON> and <PERSON>"?',
    answer: '<PERSON>',
    subject: 'Literature',
    tags: ['Plays', 'Authors'],
    isFavorite: false,
    lastReviewed: '2023-05-08',
  },
  {
    id: '5',
    question: 'What is the law of conservation of energy?',
    answer:
      'Energy cannot be created or destroyed, only transformed from one form to another.',
    subject: 'Physics',
    tags: ['Laws', 'Energy'],
    isFavorite: true,
    lastReviewed: '2023-05-14',
  },
]

// Subject options for the dropdown
const subjectOptions = [
  { value: 'mathematics', label: 'Mathematics' },
  { value: 'physics', label: 'Physics' },
  { value: 'chemistry', label: 'Chemistry' },
  { value: 'biology', label: 'Biology' },
  { value: 'history', label: 'History' },
  { value: 'geography', label: 'Geography' },
  { value: 'literature', label: 'Literature' },
  { value: 'language', label: 'Language' },
]

const FlashcardsService = () => {
  const [activeTab, setActiveTab] = useState('all')
  const [searchText, setSearchText] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [form] = Form.useForm()
  const [flashcards, setFlashcards] = useState<Flashcard[]>(mockFlashcards)
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [showAnswer, setShowAnswer] = useState(false)

  // Filter flashcards based on active tab and search text
  const filteredFlashcards = flashcards.filter((card) => {
    const matchesSearch =
      card.question.toLowerCase().includes(searchText.toLowerCase()) ||
      card.answer.toLowerCase().includes(searchText.toLowerCase()) ||
      card.tags.some((tag) =>
        tag.toLowerCase().includes(searchText.toLowerCase())
      )

    if (activeTab === 'all') return matchesSearch
    if (activeTab === 'favorites') return card.isFavorite && matchesSearch
    if (activeTab === 'recent') {
      // Sort by last reviewed date (most recent first)
      return matchesSearch
    }

    return matchesSearch
  })

  const handleCreateFlashcard = async () => {
    try {
      const values = await form.validateFields()
      const newFlashcard: Flashcard = {
        id: Date.now().toString(),
        question: values.question,
        answer: values.answer,
        subject: values.subject,
        tags: values.tags
          ? values.tags.split(',').map((tag: string) => tag.trim())
          : [],
        isFavorite: false,
        lastReviewed: new Date().toISOString().split('T')[0],
      }

      setFlashcards([...flashcards, newFlashcard])
      form.resetFields()
      setShowForm(false)
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  const toggleFavorite = (id: string) => {
    setFlashcards(
      flashcards.map((card) =>
        card.id === id ? { ...card, isFavorite: !card.isFavorite } : card
      )
    )
  }

  const handleNextCard = () => {
    setShowAnswer(false)
    setCurrentCardIndex((currentCardIndex + 1) % filteredFlashcards.length)
  }

  const handlePrevCard = () => {
    setShowAnswer(false)
    setCurrentCardIndex(
      (currentCardIndex - 1 + filteredFlashcards.length) %
        filteredFlashcards.length
    )
  }

  const renderStudyMode = () => {
    if (filteredFlashcards.length === 0) {
      return (
        <Empty
          description="No flashcards found. Create some flashcards to start studying!"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )
    }

    const currentCard = filteredFlashcards[currentCardIndex]

    return (
      <div className="max-w-2xl mx-auto">
        <div className="flex justify-between items-center mb-4">
          <span>
            Card {currentCardIndex + 1} of {filteredFlashcards.length}
          </span>
          <Button
            icon={currentCard.isFavorite ? <StarOutlined /> : <StarOutlined />}
            onClick={() => toggleFavorite(currentCard.id)}
            type={currentCard.isFavorite ? 'primary' : 'default'}
          >
            {currentCard.isFavorite ? 'Favorited' : 'Add to Favorites'}
          </Button>
        </div>

        <Card
          className="mb-4 min-h-[200px] flex flex-col justify-center"
          title={`Question (${currentCard.subject})`}
        >
          <div className="text-lg">{currentCard.question}</div>
          <div className="mt-2 text-sm text-gray-500">
            Tags: {currentCard.tags.join(', ')}
          </div>
        </Card>

        {showAnswer ? (
          <Card className="mb-4 min-h-[150px] bg-blue-50">
            <div className="text-lg">{currentCard.answer}</div>
          </Card>
        ) : (
          <Button
            type="primary"
            block
            onClick={() => setShowAnswer(true)}
            className="mb-4"
          >
            Show Answer
          </Button>
        )}

        <div className="flex justify-between">
          <Button onClick={handlePrevCard}>Previous</Button>
          <Button type="primary" onClick={handleNextCard}>
            Next
          </Button>
        </div>
      </div>
    )
  }

  const renderFlashcardsList = () => {
    return (
      <div>
        {filteredFlashcards.length === 0 ? (
          <Empty
            description="No flashcards found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredFlashcards.map((card) => (
              <Card
                key={card.id}
                title={
                  <div className="flex justify-between items-center">
                    <span>{card.subject}</span>
                    <Button
                      icon={
                        card.isFavorite ? <StarOutlined /> : <StarOutlined />
                      }
                      type="text"
                      onClick={() => toggleFavorite(card.id)}
                      className={card.isFavorite ? 'text-yellow-500' : ''}
                    />
                  </div>
                }
                className="h-full"
              >
                <div className="font-medium mb-2">{card.question}</div>
                <Divider className="my-2" />
                <div className="text-gray-600">{card.answer}</div>
                <div className="mt-3 text-xs text-gray-500">
                  Tags: {card.tags.join(', ')}
                </div>
                {card.lastReviewed && (
                  <div className="mt-1 text-xs text-gray-400 flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" /> Last reviewed:{' '}
                    {card.lastReviewed}
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Flashcards</h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setShowForm(!showForm)}
        >
          Create Flashcard
        </Button>
      </div>

      {showForm && (
        <Card title="Create New Flashcard" className="mb-6">
          <Form form={form} layout="vertical" requiredMark={false}>
            <Form.Item
              name="subject"
              label="Subject"
              rules={[{ required: true, message: 'Please select a subject' }]}
            >
              <Select
                placeholder="Select the subject"
                options={subjectOptions}
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="question"
              label="Question"
              rules={[{ required: true, message: 'Please enter a question' }]}
            >
              <Input.TextArea placeholder="Enter your question" rows={2} />
            </Form.Item>

            <Form.Item
              name="answer"
              label="Answer"
              rules={[{ required: true, message: 'Please enter an answer' }]}
            >
              <Input.TextArea placeholder="Enter the answer" rows={3} />
            </Form.Item>

            <Form.Item name="tags" label="Tags (comma separated)">
              <Input placeholder="e.g. math, algebra, equations" />
            </Form.Item>

            <div className="flex justify-end space-x-2">
              <Button onClick={() => setShowForm(false)}>Cancel</Button>
              <Button type="primary" onClick={handleCreateFlashcard}>
                Create
              </Button>
            </div>
          </Form>
        </Card>
      )}

      <div className="mb-4 flex justify-between items-center">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'all',
              label: 'All Flashcards',
            },
            {
              key: 'favorites',
              label: 'Favorites',
            },
            {
              key: 'recent',
              label: 'Recently Reviewed',
            },
            {
              key: 'study',
              label: 'Study Mode',
            },
          ]}
        />

        <Input
          placeholder="Search flashcards"
          prefix={<SearchOutlined />}
          style={{ width: 250 }}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </div>

      {activeTab === 'study' ? renderStudyMode() : renderFlashcardsList()}
    </div>
  )
}

export default FlashcardsService
