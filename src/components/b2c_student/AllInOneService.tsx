import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from '@/components/ui/tabs'
import { BookMarked, MessageSquare, Brain, Wifi, WifiOff } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import NotesCollectionList from '@/components/notes/NotesCollectionList'
import NotesCollection from '@/components/notes/NotesCollection'
import { FlashcardDeckType, Flashcard } from './FlashcardTypes'
import { useFlashcards } from '@/hooks/useFlashcards'
import FlashcardDeck from './FlashcardDeck'
import FlashcardStudy from './FlashcardStudy'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './FlashcardAIChat'
import FlashcardAIGenerator from './FlashcardAIGenerator'
import AIProcessingInterface from './AIProcessingInterface'
import AIResultsDisplay from './AIResultsDisplay'
import AI<PERSON><PERSON>bot from './AIChatbot'
import { ProcessAIResponse } from '@/services/aiService'
import { useAIHealthCheck } from '@/hooks/useAI'

export default function AllInOneService() {
  // State for notes
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null)

  // State for flashcards
  const [selectedDeck, setSelectedDeck] = useState<FlashcardDeckType | null>(null)
  const [currentCard, setCurrentCard] = useState<Flashcard | undefined>(undefined)
  const [isStudying, setIsStudying] = useState(false)
  const [showAIChat, setShowAIChat] = useState(false)
  const [showAIGenerator, setShowAIGenerator] = useState(false)

  // State for AI processing
  const [aiResults, setAiResults] = useState<ProcessAIResponse | null>(null)
  const [showAIResults, setShowAIResults] = useState(false)

  // Hooks
  const { useDecks } = useFlashcards()
  const { data: decks = [] } = useDecks()
  const {  isError: aiHealthError } = useAIHealthCheck()

  // Handle back button for notes
  const handleBackToCollections = () => {
    setSelectedCollectionId(null)
  }

  // Handle deck selection for flashcards
  const handleDeckSelect = (deck: FlashcardDeckType) => {
    setSelectedDeck(deck)
    setIsStudying(false)
  }

  // Handle back button for flashcards
  const handleBackToDecks = () => {
    setSelectedDeck(null)
    setCurrentCard(undefined)
    setIsStudying(false)
    setShowAIChat(false)
    setShowAIGenerator(false)
  }

  // Handle study mode for flashcards
  const handleStudyMode = () => {
    setIsStudying(true)
  }

  // These functions are kept for future use when we implement AI features
  // Currently not used but will be needed when we add AI buttons to the UI
  /*
  const handleAIChat = () => {
    setShowAIChat(true)
  }

  const handleAIGenerator = () => {
    setShowAIGenerator(true)
  }
  */

  // Handle deck update for flashcards
  const handleDeckUpdate = (updatedDeck: FlashcardDeckType) => {
    setSelectedDeck(updatedDeck)
  }

  // Handle AI results
  const handleAIResultsGenerated = (results: ProcessAIResponse) => {
    setAiResults(results)
    setShowAIResults(true)
  }

  // Handle back from AI results
  const handleBackFromAIResults = () => {
    setShowAIResults(false)
    setAiResults(null)
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      {/* Header with AI Status */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">All in One AI Learning</h1>
        <div className="flex items-center gap-2">
          {aiHealthError ? (
            <Badge variant="destructive" className="flex items-center gap-1">
              <WifiOff className="h-3 w-3" />
              AI Offline
            </Badge>
          ) : (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Wifi className="h-3 w-3" />
              AI Ready
            </Badge>
          )}
        </div>
      </div>

      <Tabs defaultValue="ai-processing" className="w-full">
        <TabsList className="mb-6 w-full justify-start">
          <TabsTrigger value="ai-processing" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            <span>AI Processing</span>
          </TabsTrigger>
          {/* <TabsTrigger value="flashcards" className="flex items-center gap-2">
            <BookMarked className="h-4 w-4" />
            <span>Flashcards</span>
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center gap-2">
            <StickyNote className="h-4 w-4" />
            <span>Notes</span>
          </TabsTrigger> */}
          <TabsTrigger value="chatbot" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span>Jeridooo AI</span>
          </TabsTrigger>
        </TabsList>

        {/* AI Processing Tab */}
        <TabsContent value="ai-processing" className="space-y-4">
          {showAIResults && aiResults ? (
            <AIResultsDisplay
              results={aiResults}
              onBack={handleBackFromAIResults}
            />
          ) : (
            <AIProcessingInterface
              onResultsGenerated={handleAIResultsGenerated}
            />
          )}
        </TabsContent>

        {/* Flashcards Tab */}
        <TabsContent value="flashcards" className="space-y-4">
          {!selectedDeck ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {decks.map((deck) => (
                <Card
                  key={deck.id}
                  className="hover:bg-muted/50 transition-colors cursor-pointer"
                  onClick={() => handleDeckSelect(deck)}
                >
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center text-center space-y-2 py-4">
                      <BookMarked className="h-8 w-8 text-primary" />
                      <span className="text-lg font-medium">{deck.name}</span>
                      <span className="text-sm text-muted-foreground">{deck.cardCount} cards</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : isStudying ? (
            <FlashcardStudy
              deck={selectedDeck}
              onBack={handleBackToDecks}
            />
          ) : showAIChat ? (
            <FlashcardAIChat
              deck={selectedDeck}
              currentCard={currentCard}
              onUpdateDeck={handleDeckUpdate}
              onClose={handleBackToDecks}
            />
          ) : showAIGenerator ? (
            <FlashcardAIGenerator
              deck={selectedDeck}
              onUpdateDeck={handleDeckUpdate}
              onClose={handleBackToDecks}
            />
          ) : (
            <FlashcardDeck
              deck={selectedDeck}
              onBack={handleBackToDecks}
              onStudy={handleStudyMode}
              onUpdate={handleDeckUpdate}
            />
          )}
        </TabsContent>

        {/* Notes Tab */}
        <TabsContent value="notes" className="space-y-4">
          {selectedCollectionId ? (
            <NotesCollection
              collectionId={selectedCollectionId}
              onBack={handleBackToCollections}
            />
          ) : (
            <NotesCollectionList
              onViewCollection={(id: string) => setSelectedCollectionId(id)}
            />
          )}
        </TabsContent>

        {/* AI Assistant Tab */}
        <TabsContent value="chatbot" className="space-y-4">
          <AIChatbot className="w-full" />
        </TabsContent>
      </Tabs>
    </div>
  )
}
