import { useState, useEffect } from 'react'
import {
  Calendar,
  BookMarked,
  MessageSquare,
  Brain,
  FileText,
} from 'lucide-react'
import { Link } from '@tanstack/react-router'
import StudentService from './b2c_student'
import { ScheduleView, StudentNotes } from '../shared'
import PaymentCheck from '../shared/PaymentCheck'
import { useCurrentStudent } from '@/hooks/useCurrentStudent'
import StudentHeader from './StudentHeader'

// Define the navigation item type for better type safety
interface NavigationItem {
  name?: string;
  icon?: React.ComponentType<any>;
  href?: string;
  component?: React.ComponentType<any> | (() => React.ReactNode);
  soon?: boolean;
}

const navigation: NavigationItem[] = [
  {
    name: 'Chat',
    icon: MessageSquare,
    href: '/chat',
    component: StudentService,
    soon: false,
  },
  // Empty item - keeping it for spacing or future use
  {
    name: '',
  },
  {
    name: 'Schedule View',
    icon: Calendar,
    component: () => <ScheduleView userRole="student" initialSchedule={{}} />,
    soon: false,
  },
  {
    name: 'My Notes',
    icon: FileText,
    component: () => <StudentNotes userRole="student" />,
    soon: true,
  },
  {
    name: 'AI Assistant',
    icon: Brain,
    href: '#ai-assistant',
    component: StudentService,
    soon: true,
  },
]

export default function StudentLayout() {
  const [currentPage, setCurrentPage] = useState('Student Service')
  const { isPaid, firstname, lastname, isLoading, refetch } =
    useCurrentStudent()

  // Add effect to refetch data once on mount
  useEffect(() => {
    // Refetch on mount
    refetch()
  }, [])

  // Check if we're on the flashcards route - handle both hash and pathname
  const isFlashcardsRoute =
    window.location.pathname.includes('/student/flashcards') ||
    window.location.hash.includes('#/student/flashcards')

  // Get the component to render based on the current page
  const Component =
    navigation.find((item) => item.name === currentPage)?.component ||
    StudentService

  // Show loading state while fetching student data
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading student information...</p>
        </div>
      </div>
    )
  }

  // If we're on the flashcards route, don't render the StudentLayout content
  if (isFlashcardsRoute) {
    console.log('Detected flashcards route, returning null from StudentLayout')
    return null
  }

  const handleLogout = () => {
    // Clear all localStorage items
    localStorage.removeItem('access_token')
    localStorage.removeItem('id')
    localStorage.removeItem('role')
    localStorage.removeItem('firstname')
    localStorage.removeItem('lastname')

    // Redirect to login page
    window.location.href = '/login'
  }

  return (
    <div className="flex flex-col h-screen">
      <StudentHeader onLogout={handleLogout} />
      <div className="flex flex-1">
        <nav className="w-64 bg-gray-100 p-4">
          {navigation.map((item, index) => {
            // Skip rendering empty items
            if (!item.name) return null;

            return (
              <button
                key={item.name || `nav-item-${index}`}
                onClick={() => {
                  console.log('Navigating to:', item.name)
                  if (item.href) {
                    if (item.href.startsWith('#')) {
                      // Stay on the same page for hash links
                      window.location.hash = item.href
                    } else {
                      // Navigate to the specified path
                      window.location.href = item.href
                    }
                  } else if (item.name) {
                    // Only set current page if name exists
                    setCurrentPage(item.name)
                  }
                }}
                className={`flex items-center w-full p-2 rounded mb-1 ${
                  currentPage === item.name ? 'bg-blue-500 text-white' : ''
                }`}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    {item.icon && <item.icon className="mr-2 h-5 w-5" />}
                    {item.name}
                  </div>
                  {item.soon && (
                    <span className="text-xs font-medium text-blue-500 bg-blue-50 px-2 py-0.5 rounded-full">
                      Soon
                    </span>
                  )}
                </div>
              </button>
            );
          })}
          <Link
            to="/student/flashcards"
            className="flex items-center w-full p-2 rounded mt-2 bg-primary text-white"
          >
            <BookMarked className="mr-2 h-5 w-5" />
            Flashcards (Direct Link)
          </Link>
        </nav>
        <main className="flex-1 p-4">
          {/* Only show content if student has paid, otherwise show payment required message */}
          <PaymentCheck
            isPaid={isPaid}
            studentName={`${firstname} ${lastname}`}
          >
            <Component />
          </PaymentCheck>
        </main>
      </div>
    </div>
  )
}
