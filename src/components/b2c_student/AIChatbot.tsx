import { useState, useRef, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  Trash2,
  MessageSquare
} from 'lucide-react'
import { useSendChatMessage, useDeleteChatSession } from '@/hooks/useAI'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface AIChatbotProps {
  className?: string
}

export default function AIChatbot({ className }: AIChatbotProps) {
  // State
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm <PERSON><PERSON><PERSON>, your AI learning assistant. I can help you with questions about your study materials, explain complex concepts, create study plans, or assist with your learning journey. How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [sessionId, setSessionId] = useState<string | null>(null)

  // Refs
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Hooks
  const sendMessage = useSendChatMessage()
  const deleteSession = useDeleteChatSession()

  // Get user ID from localStorage or auth context
  const userId = localStorage.getItem('user_id') || 'anonymous'

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // Handle sending message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!inputMessage.trim() || sendMessage.isPending) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage.trim(),
      isUser: true,
      timestamp: new Date(),
    }

    // Add user message to chat
    setMessages(prev => [...prev, userMessage])
    setInputMessage('')

    try {
      // Send message to AI
      const response = await sendMessage.mutateAsync({
        user_id: userId,
        message: userMessage.content,
        session_id: sessionId || undefined,
      })

      // Update session ID if this is the first message
      if (!sessionId && response.session_id) {
        setSessionId(response.session_id)
      }

      // Add AI response to chat
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.reply,
        isUser: false,
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      // Add error message to chat
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "Sorry, I'm having trouble responding right now. Please try again.",
        isUser: false,
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, errorMessage])
    }
  }

  // Handle clearing chat
  const handleClearChat = async () => {
    if (sessionId) {
      try {
        await deleteSession.mutateAsync(sessionId)
      } catch (error) {
        console.error('Failed to delete session:', error)
      }
    }

    // Reset chat state
    setMessages([
      {
        id: '1',
        content: "Hello! I'm Jeridoo, your AI learning assistant. I can help you with questions about your study materials, explain complex concepts, create study plans, or assist with your learning journey. How can I help you today?",
        isUser: false,
        timestamp: new Date(),
      }
    ])
    setSessionId(null)
    inputRef.current?.focus()
  }

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <Card className={cn("flex flex-col h-[600px]", className)}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Jeridoo AI Assistant
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearChat}
            disabled={deleteSession.isPending}
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Clear Chat
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.isUser ? "justify-end" : "justify-start"
                )}
              >
                {!message.isUser && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarImage src="/jeridoooAvatar.png" alt="Jeridoo AI Assistant" />
                    <AvatarFallback className="bg-primary/10">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}

                <div
                  className={cn(
                    "max-w-[80%] rounded-lg px-3 py-2 text-sm",
                    message.isUser
                      ? "bg-primary text-primary-foreground ml-auto"
                      : "bg-muted"
                  )}
                >
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  <p
                    className={cn(
                      "text-xs mt-1 opacity-70",
                      message.isUser ? "text-right" : "text-left"
                    )}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>

                {message.isUser && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-secondary">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {/* Loading indicator */}
            {sendMessage.isPending && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarImage src="/jeridoooAvatar.png" alt="Jeridoo AI Assistant" />
                  <AvatarFallback className="bg-primary/10">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-3 py-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Thinking...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask Jeridoo anything about your studies..."
              disabled={sendMessage.isPending}
              className="flex-1"
            />
            <Button
              type="submit"
              disabled={!inputMessage.trim() || sendMessage.isPending}
              size="icon"
            >
              {sendMessage.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
          
          {/* Status indicator */}
          <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
            <span>
              {sessionId ? `Session: ${sessionId.slice(0, 8)}...` : 'New session'}
            </span>
            <span>
              {messages.length - 1} messages
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
