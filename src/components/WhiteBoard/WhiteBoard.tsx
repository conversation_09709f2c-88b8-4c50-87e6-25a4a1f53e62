import { useEffect, useRef, useState } from 'react'
import { Excalidraw, Footer, MainMenu } from '@excalidraw/excalidraw'
import screenfull from 'screenfull'
import { Maximize2, Minimize2 } from 'lucide-react'
import './WhiteBoard.css'
import { Button } from '../ui/button'

export default function WhiteBoard() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const toggleFullscreen = () => {
    if (screenfull.isEnabled && containerRef.current) {
      screenfull.toggle(containerRef.current)
    }
  }

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(screenfull.isEnabled && screenfull.isFullscreen)
    }

    if (screenfull.isEnabled) {
      screenfull.on('change', handleFullscreenChange)
    }

    return () => {
      if (screenfull.isEnabled) {
        screenfull.off('change', handleFullscreenChange)
      }
    }
  }, [])

  // Enable F11 to fullscreen
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F11') {
        event.preventDefault() // Prevent the default behavior of F11
        toggleFullscreen()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [toggleFullscreen])

  return (
    <div className="h-screen w-full relative" ref={containerRef}>
      <Excalidraw
        initialData={{
          elements: [],
          appState: { viewBackgroundColor: '#ffffff' },
        }}
      >
        <MainMenu>
          <MainMenu.DefaultItems.LoadScene />
          <MainMenu.DefaultItems.Export />
          <MainMenu.DefaultItems.SaveToActiveFile />
          <MainMenu.DefaultItems.SaveAsImage />
          <MainMenu.DefaultItems.Help />
          <MainMenu.DefaultItems.ClearCanvas />
          <MainMenu.DefaultItems.ToggleTheme />
          <MainMenu.DefaultItems.ChangeCanvasBackground />
        </MainMenu>
        <Button
          onClick={toggleFullscreen}
          className="absolute top-4 left-14 z-50 h-9 w-"
        >
          {isFullscreen ? <Minimize2 /> : <Maximize2 />}
        </Button>
        <Footer>
          {/* <button
            onClick={toggleFullscreen}
            style={{
              marginTop: '1px',
              marginLeft: '10px',
              padding: '5px',
              backgroundColor: isFullscreen ? '#FCD34D' : '#525FE1',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
            }}
          >
            {isFullscreen ? <Minimize2 size={29} /> : <Maximize2 size={29} />}
          </button> */}
        </Footer>
      </Excalidraw>
    </div>
  )
}
