import { useEffect, useRef } from 'react'
import UserSay from './UserSay'
import { Link } from '@tanstack/react-router'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import '../styles/about.css'

gsap.registerPlugin(ScrollTrigger)

const About = () => {
  const contentRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const aboutSection = document.getElementById('about')
    if (aboutSection) {
      aboutSection.style.minHeight = '100vh'

      const scrollTrigger = ScrollTrigger.create({
        trigger: aboutSection,
        start: 'top 20%',
        end: 'bottom 20%',
        onEnter: () => {
          aboutSection.classList.add('in-view')
        },
        onLeave: () => {
          aboutSection.classList.remove('in-view')
        },
        onEnterBack: () => {
          aboutSection.classList.add('in-view')
        },
        onLeaveBack: () => {
          aboutSection.classList.remove('in-view')
        }
      })

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            aboutSection.classList.add('in-view')
          } else {
            aboutSection.classList.remove('in-view')
          }
        })
      }, { threshold: 0.1 })

      observer.observe(aboutSection)

      return () => {
        scrollTrigger.kill()
        observer.disconnect()
      }
    }
  }, [])

  useEffect(() => {
    if (contentRef.current) {
      gsap.fromTo(
        contentRef.current,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.6, ease: 'power2.out' }
      )
    }
  }, [])

  return (
    <section
      id="about"
      className="relative overflow-hidden about-section"
      style={{
        scrollSnapAlign: 'start',
        scrollSnapStop: 'always',
        height: '100vh',
        width: '100vw',
        position: 'relative',
        isolation: 'isolate',
      }}
    >
      {/* Background image */}
      <div className="fixed inset-0 w-full h-full z-0">
        <img
          src="/images/landingpage/school.webp"
          alt="School Background"
          className="w-[500px] h-[500px] object-cover absolute left-[800px] top-[200px]"
        />
      </div>

      {/* Foreground content */}
      <div className="relative z-10 h-full w-full">
        <div className="container mx-auto px-4 py-8 h-full flex flex-col justify-center">
          <div className="max-w-2xl" ref={contentRef}>
            <UserSay
              blueWord="Our"
              title="Story"
              description="How a Simple Idea Transformed School Management"
            />
            <p className="text-lg text-gray-800 mt-4 max-w-xl bg-white/80 backdrop-blur-sm p-4 rounded-lg shadow-md">
              Our journey began with a simple question: <strong>why is managing schools still so hard?</strong>
              We saw teachers overwhelmed by schedules, admins drowning in paperwork, and students disconnected from their learning tools.
              <br /><br />
              So we built something better. A smart, affordable school management system that does the heavy lifting—automating timetables, tracking attendance, simplifying communication, and empowering everyone involved.
              <br /><br />
              Whether you're a teacher, student, or parent, our goal is to make your school life smoother, more connected, and future-ready. We believe technology should work for education—not the other way around.
            </p>

            <Link
              to="/join"
              className="inline-flex items-center mt-6 px-6 py-2 bg-blue-600 text-white text-base font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
            >
              Join Us
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
