"use client"

import { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Quote,
  Code,
} from 'lucide-react'

export interface SimpleRichEditorRef {
  getContent: () => string
}

interface SimpleRichEditorProps {
  initialContent: string
  placeholder?: string
}

const SimpleRichEditor = forwardRef<SimpleRichEditorRef, SimpleRichEditorProps>(
  ({ initialContent, placeholder = 'Start writing your note...' }, ref) => {
    const editorRef = useRef<HTMLDivElement>(null)
    // Track if the editor is empty for potential UI feedback
    const [, setIsEmpty] = useState(!initialContent)

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      getContent: () => {
        return editorRef.current?.innerHTML || ''
      },
    }))

    // Handle formatting commands
    const execCommand = (command: string, value: string = '') => {
      document.execCommand(command, false, value)
      editorRef.current?.focus()
    }

    // Check if the editor is empty
    const checkIfEmpty = () => {
      if (editorRef.current) {
        const text = editorRef.current.innerText.trim()
        setIsEmpty(!text)
      }
    }

    return (
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        <div
          className="flex flex-wrap items-center gap-1 p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('bold');
            }}
            className="h-8 px-2"
          >
            <Bold className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('italic');
            }}
            className="h-8 px-2"
          >
            <Italic className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('underline');
            }}
            className="h-8 px-2"
          >
            <Underline className="h-4 w-4" />
          </Button>

          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('formatBlock', '<h1>');
            }}
            className="h-8 px-2"
          >
            <Heading1 className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('formatBlock', '<h2>');
            }}
            className="h-8 px-2"
          >
            <Heading2 className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('formatBlock', '<h3>');
            }}
            className="h-8 px-2"
          >
            <Heading3 className="h-4 w-4" />
          </Button>

          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('insertUnorderedList');
            }}
            className="h-8 px-2"
          >
            <List className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('insertOrderedList');
            }}
            className="h-8 px-2"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('formatBlock', '<blockquote>');
            }}
            className="h-8 px-2"
          >
            <Quote className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              execCommand('formatBlock', '<pre>');
            }}
            className="h-8 px-2"
          >
            <Code className="h-4 w-4" />
          </Button>
        </div>

        <div
          ref={editorRef}
          contentEditable
          dangerouslySetInnerHTML={{ __html: initialContent }}
          className="prose prose-sm sm:prose max-w-none p-4 min-h-[300px] focus:outline-none bg-white dark:bg-gray-800 dark:prose-invert"
          onInput={checkIfEmpty}
          onBlur={checkIfEmpty}
          onClick={(e) => e.stopPropagation()}
          onKeyDown={(e) => e.stopPropagation()}
          data-placeholder={placeholder}
        />
      </div>
    )
  }
)

SimpleRichEditor.displayName = 'SimpleRichEditor'

export default SimpleRichEditor
