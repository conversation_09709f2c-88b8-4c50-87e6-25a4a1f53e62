"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft } from "lucide-react"
import { useNotes } from "@/hooks/useNotes"
import type { Note, CreateNoteDto, UpdateNoteDto } from "@/services/notesService"
import { useToast } from "@/components/ui/use-toast"
import TiptapEditor, { TiptapEditorRef } from "./TiptapEditor"

interface NoteFormProps {
  note?: Note | null
  collectionId: string
  onCancel: () => void
  onSuccess: () => void
}

export default function NoteForm({ note, collectionId, onCancel, onSuccess }: NoteFormProps) {
  // State for form fields
  const [title, setTitle] = useState("")
  const [initialContent, setInitialContent] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Reference to the rich editor
  const editorRef = useRef<TiptapEditorRef>(null)

  // Get hooks from useNotes
  const { useCreateNote, useUpdateNote } = useNotes()

  // Get toast hook
  const { toast } = useToast()

  // Create and update mutations
  const createNote = useCreateNote()
  const updateNote = useUpdateNote()

  // Set initial values if editing
  useEffect(() => {
    if (note) {
      setTitle(note.title)
      setInitialContent(note.content)
    }
  }, [note])

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    const editorContent = editorRef.current?.getContent() || ""

    if (!title.trim()) {
      newErrors.title = "Title is required"
    }

    if (!editorContent.trim()) {
      newErrors.content = "Content is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Get the current content from the editor
    const editorContent = editorRef.current?.getContent() || ""

    if (!validateForm()) {
      return
    }

    if (note) {
      // Update existing note
      const updateData: UpdateNoteDto = {
        title: title.trim(),
        content: editorContent,
      }

      updateNote.mutate(
        {
          id: note.id,
          data: updateData,
          collectionId,
        },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: "Note updated successfully",
            })
            onSuccess()
          },
          onError: (error: any) => {
            toast({
              title: "Error",
              description: error.message || "Failed to update note",
              variant: "destructive",
            })
          },
        },
      )
    } else {
      // Create new note
      const createData: CreateNoteDto = {
        title: title.trim(),
        content: editorContent,
        collectionId,
      }

      createNote.mutate(createData, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Note created successfully",
          })
          onSuccess()
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to create note",
            variant: "destructive",
          })
        },
      })
    }
  }

  return (
    <div className="space-y-6 animate-in fade-in slide-in-from-bottom-4 duration-300 bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-md">
      <div className="flex items-center mb-8">
        <Button
          variant="ghost"
          onClick={onCancel}
          className="mr-2 -ml-3 hover:bg-accent/80 transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          {note ? "Edit Note" : "Create New Note"}
        </h1>
      </div>

      <form
        onSubmit={handleSubmit}
        className="space-y-6 max-w-3xl mx-auto"
        onClick={(e) => {
          // Prevent clicks on the form from submitting it
          if (e.target === e.currentTarget) {
            e.preventDefault();
          }
        }}
      >
        <div className="space-y-3">
          <Label htmlFor="title" className="text-base font-medium">
            Note Title
          </Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="e.g., Cell Structure"
            className="h-11 shadow-sm hover:shadow-md transition-shadow bg-white dark:bg-gray-800"
          />
          {errors.title && (
            <p className="text-sm text-destructive mt-1 animate-in fade-in">
              {errors.title}
            </p>
          )}
        </div>

        <div className="space-y-3">
          <Label htmlFor="content" className="text-base font-medium">
            Content
          </Label>
          <div
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <TiptapEditor
              ref={editorRef}
              content={initialContent}
              placeholder="Start writing your note..."
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Your changes will only be saved when you click the {note ? "Update Note" : "Create Note"} button.
          </p>
          {errors.content && (
            <p className="text-sm text-destructive mt-1 animate-in fade-in">
              {errors.content}
            </p>
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            type="button"
            onClick={onCancel}
            className="px-5 h-11 shadow-sm hover:shadow-md transition-shadow"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createNote.isPending || updateNote.isPending}
            className="px-5 h-11 shadow-sm hover:shadow-md transition-shadow bg-primary text-primary-foreground"
          >
            {createNote.isPending || updateNote.isPending ? (
              <span className="flex items-center">
                <span className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-current rounded-full"></span>
                Saving...
              </span>
            ) : note ? (
              "Update Note"
            ) : (
              "Create Note"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
