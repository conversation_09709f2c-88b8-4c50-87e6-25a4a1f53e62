"use client"

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Placeholder from '@tiptap/extension-placeholder'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import Underline from '@tiptap/extension-underline'
import Highlight from '@tiptap/extension-highlight'
import { useState, useEffect, useImperativeHandle, forwardRef } from 'react'
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Quote,
  Code,
  Undo,
  Redo,
  Highlighter,
  Palette,
} from 'lucide-react'
// import useCallback from react
// import from  lucide-react
  // Link as LinkIcon,
  // Image as ImageIcon,

import { Button } from '@/components/ui/button'
// import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

export interface TiptapEditorRef {
  getContent: () => string;
}

interface TiptapEditorProps {
  content: string
  placeholder?: string
  editable?: boolean
}

const TiptapEditor = forwardRef<TiptapEditorRef, TiptapEditorProps>(({
  content,
  placeholder = 'Start writing your note...',
  editable = true,
}, ref) => {
  // const [linkUrl, setLinkUrl] = useState('')
  // const [imageUrl, setImageUrl] = useState('')
  // const [showLinkInput, setShowLinkInput] = useState(false)
  // const [showImageInput, setShowImageInput] = useState(false)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [editorContent, setEditorContent] = useState(content)

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline cursor-pointer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full rounded-md my-4',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      TextStyle.configure(),
      Color.configure(),
      Underline,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 text-black px-1 rounded',
        },
      }),
    ],
    content: editorContent,
    editable,
  })

  // Expose methods to parent component through ref
  useImperativeHandle(ref, () => ({
    getContent: () => {
      return editor ? editor.getHTML() : editorContent
    }
  }))

  // Update internal state when editor changes
  useEffect(() => {
    if (editor) {
      // This is a one-way binding - we're not updating the parent component
      const handleUpdate = () => {
        // We're only updating our internal state
        setEditorContent(editor.getHTML())
      }

      // Add event listener
      editor.on('update', handleUpdate)

      // Clean up
      return () => {
        editor.off('update', handleUpdate)
      }
    }
  }, [editor])

  // Update editor content when prop changes
  useEffect(() => {
    if (editor && content !== editorContent) {
      setEditorContent(content)
      editor.commands.setContent(content)
    }
  }, [content, editor])

  // // ! remove the link and image because it's not work now
  // const addLink = useCallback(() => {
  //   if (!editor) return

  //   if (linkUrl) {
  //     editor
  //       .chain()
  //       .focus()
  //       .extendMarkRange('link')
  //       .setLink({ href: linkUrl })
  //       .run()

  //     setLinkUrl('')
  //     setShowLinkInput(false)
  //   }
  // }, [editor, linkUrl])

  // const addImage = useCallback(() => {
  //   if (!editor) return

  //   if (imageUrl) {
  //     editor
  //       .chain()
  //       .focus()
  //       .setImage({ src: imageUrl })
  //       .run()

  //     setImageUrl('')
  //     setShowImageInput(false)
  //   }
  // }, [editor, imageUrl])

  if (!editor) {
    return null
  }

  return (
    <div className="tiptap-editor border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
      {editable && (
        <div
          className="flex flex-wrap items-center gap-1 p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleBold().run();
            }}
            className={`h-8 px-2 ${editor.isActive('bold') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Bold className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleItalic().run();
            }}
            className={`h-8 px-2 ${editor.isActive('italic') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Italic className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleUnderline().run();
            }}
            className={`h-8 px-2 ${editor.isActive('underline') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <UnderlineIcon className="h-4 w-4" />
          </Button>

          <Popover open={showColorPicker} onOpenChange={setShowColorPicker}>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowColorPicker(true);
                }}
                className="h-8 px-2"
              >
                <Palette className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-2">
              <div className="space-y-2">
                <div className="grid grid-cols-4 gap-2">
                  {['#000000', '#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'].map(color => (
                    <button
                      key={color}
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        editor.chain().focus().setColor(color).run();
                        setShowColorPicker(false);
                      }}
                      className="w-8 h-8 rounded-md border border-gray-200 dark:border-gray-700"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      editor.chain().focus().unsetColor().run();
                      setShowColorPicker(false);
                    }}
                    className="w-full text-xs"
                  >
                    Remove color
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleHighlight().run();
            }}
            className={`h-8 px-2 ${editor.isActive('highlight') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Highlighter className="h-4 w-4" />
          </Button>

          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleHeading({ level: 1 }).run();
            }}
            className={`h-8 px-2 ${editor.isActive('heading', { level: 1 }) ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Heading1 className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleHeading({ level: 2 }).run();
            }}
            className={`h-8 px-2 ${editor.isActive('heading', { level: 2 }) ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Heading2 className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleHeading({ level: 3 }).run();
            }}
            className={`h-8 px-2 ${editor.isActive('heading', { level: 3 }) ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Heading3 className="h-4 w-4" />
          </Button>

          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleBulletList().run();
            }}
            className={`h-8 px-2 ${editor.isActive('bulletList') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <List className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleOrderedList().run();
            }}
            className={`h-8 px-2 ${editor.isActive('orderedList') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <ListOrdered className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleBlockquote().run();
            }}
            className={`h-8 px-2 ${editor.isActive('blockquote') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Quote className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().toggleCodeBlock().run();
            }}
            className={`h-8 px-2 ${editor.isActive('codeBlock') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          >
            <Code className="h-4 w-4" />
          </Button>

          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />


            {/* remove link  */}
          {/* <Popover open={showLinkInput} onOpenChange={setShowLinkInput}>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                className={`h-8 px-2 ${editor.isActive('link') ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
              >
                <LinkIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-2">
              <div className="flex gap-2">
                <Input
                  type="url"
                  placeholder="https://example.com"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    addLink();
                  }}
                >
                  Add
                </Button>
              </div>
            </PopoverContent>
          </Popover> */}
          {/* remove image  */}
          {/* <Popover open={showImageInput} onOpenChange={setShowImageInput}>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="lg"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                className="h-8 px-2"
              >
                <ImageIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-2">
              <div className="flex gap-2">
                <Input
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    addImage();
                  }}
                >
                  Add
                </Button>
              </div>
            </PopoverContent>
          </Popover> */}

          {/* <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" /> */}

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().undo().run();
            }}
            disabled={!editor.can().undo()}
            className="h-8 px-2"
          >
            <Undo className="h-4 w-4" />
          </Button>

          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              editor.chain().focus().redo().run();
            }}
            disabled={!editor.can().redo()}
            className="h-8 px-2"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      )}

      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <EditorContent
          editor={editor}
          className="prose prose-sm sm:prose max-w-none p-4 min-h-[300px] focus:outline-none bg-white dark:bg-gray-800 dark:prose-invert"
        />
      </div>
    </div>
  )
})

export default TiptapEditor
