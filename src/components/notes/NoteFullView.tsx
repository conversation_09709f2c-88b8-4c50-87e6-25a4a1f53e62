"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, X, Maximize2, Minimize2 } from "lucide-react"
import TiptapContent from "./TiptapContent"
import type { Note } from "@/services/notesService"

interface NoteFullViewProps {
  note: Note
  onClose: () => void
}

export default function NoteFullView({ note, onClose }: NoteFullViewProps) {
  const [isFullScreen, setIsFullScreen] = useState(false)

  // Handle escape key to exit full screen or close the view
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        if (isFullScreen) {
          setIsFullScreen(false)
        } else {
          onClose()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [isFullScreen, onClose])

  // Toggle full screen
  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen)
  }

  return (
    <div
      className={`fixed inset-0 bg-white dark:bg-gray-900 z-50 flex flex-col ${
        isFullScreen ? "p-0" : "p-4 sm:p-6"
      }`}
    >
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullScreen}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            {isFullScreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <div className={`max-w-4xl mx-auto ${isFullScreen ? "p-6" : ""}`}>
          <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
            {note.title}
          </h1>
          <div className="prose prose-lg max-w-none dark:prose-invert tiptap-content">
            <TiptapContent content={note.content} />
          </div>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500 dark:text-gray-400 flex justify-between">
        <span>Created: {new Date(note.createdAt).toLocaleDateString()}</span>
        <span>Updated: {new Date(note.updatedAt).toLocaleDateString()}</span>
      </div>
    </div>
  )
}
