"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft } from "lucide-react"
import { useNotes } from "@/hooks/useNotes"
import type { NotesCollection, CreateCollectionDto } from "@/services/notesService"
import { useToast } from "@/components/ui/use-toast"

interface NotesCollectionFormProps {
  collection?: NotesCollection | null
  onCancel: () => void
  onSuccess: () => void
}

export default function NotesCollectionForm({ collection, onCancel, onSuccess }: NotesCollectionFormProps) {
  // State for form fields
  const [name, setName] = useState("")
  const [visibility, setVisibility] = useState<"private" | "public">("private")
  const [topic, setTopic] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Get hooks from useNotes
  const { useCreateCollection, useUpdateCollection } = useNotes()

  // Get toast hook
  const { toast } = useToast()

  // Create and update mutations
  const createCollection = useCreateCollection()
  const updateCollection = useUpdateCollection()

  // Set initial values if editing
  useEffect(() => {
    if (collection) {
      setName(collection.name)
      setVisibility(collection.visibility)
      setTopic(collection.topic || "")
    }
  }, [collection])

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!name.trim()) {
      newErrors.name = "Name is required"
    }

    if (!topic.trim()) {
      newErrors.topic = "Topic is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const data: CreateCollectionDto = {
      name: name.trim(),
      visibility,
      topic: topic.trim(),
    }

    if (collection) {
      // Update existing collection
      updateCollection.mutate(
        {
          id: collection.id,
          data,
        },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: "Collection updated successfully",
            })
            onSuccess()
          },
          onError: (error: any) => {
            toast({
              title: "Error",
              description: error.message || "Failed to update collection",
              variant: "destructive",
            })
          },
        },
      )
    } else {
      // Create new collection
      createCollection.mutate(data, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Collection created successfully",
          })
          onSuccess()
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to create collection",
            variant: "destructive",
          })
        },
      })
    }
  }

  return (
    <div className="space-y-6 animate-in fade-in slide-in-from-bottom-4 duration-300 bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-md">
      <div className="flex items-center mb-8">
        <Button 
          variant="ghost" 
          onClick={onCancel} 
          className="mr-2 -ml-3 hover:bg-accent/80 transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
          {collection ? "Edit Collection" : "Create New Collection"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6 max-w-md mx-auto">
        <div className="space-y-3 bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <Label htmlFor="name" className="text-base font-medium">
            Collection Name
          </Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g., Biology Notes"
            className="h-11 shadow-sm hover:shadow-md transition-shadow"
          />
          {errors.name && (
            <p className="text-sm text-destructive mt-1 animate-in fade-in">
              {errors.name}
            </p>
          )}
        </div>

        <div className="space-y-3 bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <Label htmlFor="topic" className="text-base font-medium">
            Topic
          </Label>
          <Input
            id="topic"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="e.g., Science, Math, History"
            className="h-11 shadow-sm hover:shadow-md transition-shadow"
          />
          {errors.topic && (
            <p className="text-sm text-destructive mt-1 animate-in fade-in">
              {errors.topic}
            </p>
          )}
        </div>

        <div className="space-y-3 bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <Label htmlFor="visibility" className="text-base font-medium">
            Visibility
          </Label>
          <Select value={visibility} onValueChange={(value: "private" | "public") => setVisibility(value)}>
            <SelectTrigger id="visibility" className="h-11 shadow-sm hover:shadow-md transition-shadow">
              <SelectValue placeholder="Select visibility" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="private">Private</SelectItem>
              <SelectItem value="public">Public</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
            {visibility === "private" ? (
              <span className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-blue-500"></span>
                Private collections are only visible to you.
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-green-500"></span>
                Public collections can be seen by other users.
              </span>
            )}
          </p>
        </div>

        <div className="flex justify-end space-x-3 pt-6 mt-4 border-t border-gray-200 dark:border-gray-700">
          <Button 
            variant="outline" 
            type="button" 
            onClick={onCancel} 
            className="px-5 h-11 shadow-sm hover:shadow-md transition-shadow"
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={createCollection.isPending || updateCollection.isPending} 
            className="px-5 h-11 shadow-sm hover:shadow-md transition-shadow bg-primary text-primary-foreground"
          >
            {createCollection.isPending || updateCollection.isPending ? (
              <span className="flex items-center">
                <span className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-current rounded-full"></span>
                Saving...
              </span>
            ) : collection ? (
              "Update Collection"
            ) : (
              "Create Collection"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
