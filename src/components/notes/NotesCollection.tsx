"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, PlusCircle, Trash2, Edit, Eye } from "lucide-react"
import { useNotes } from "@/hooks/useNotes"
import type { Note } from "@/services/notesService"
import { useToast } from "@/components/ui/use-toast"
import NoteForm from "./NoteForm"
import TiptapContent from "./TiptapContent"
import NoteFullView from "./NoteFullView"

interface NotesCollectionProps {
  collectionId?: string
  onBack?: () => void
}

export default function NotesCollection({ collectionId: propCollectionId, onBack }: NotesCollectionProps = {}) {
  const [collectionId, setCollectionId] = useState<string>("")
  const { toast } = useToast()
  const [isCreatingNote, setIsCreatingNote] = useState(false)
  const [editingNote, setEditingNote] = useState<Note | null>(null)
  const [viewingNote, setViewingNote] = useState<Note | null>(null)

  // Set collection ID from props or URL
  useEffect(() => {
    if (propCollectionId) {
      setCollectionId(propCollectionId)
    } else {
      // Try to get from URL if not provided as prop
      const urlParams = new URLSearchParams(window.location.search)
      const id = urlParams.get("collectionId")
      if (id) {
        setCollectionId(id)
      }
    }
  }, [propCollectionId])

  // Get hooks from useNotes
  const { useCollection, useDeleteNote } = useNotes()

  // Get collection data
  const { data: collection, isLoading, error, refetch } = useCollection(collectionId || "")

  // Delete note mutation
  const deleteNote = useDeleteNote()

  // Handle back to collections
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      // Fallback if no onBack prop is provided
      const url = new URL(window.location.href)
      url.searchParams.delete("collectionId")
      window.history.pushState({}, "", url)
      window.location.reload() // Force reload to handle the URL change
    }
  }

  // Handle edit note
  const handleEditNote = (note: Note) => {
    setEditingNote(note)
  }

  // Handle view note
  const handleViewNote = (note: Note) => {
    setViewingNote(note)
  }

  // Handle delete note
  const handleDeleteNote = (noteId: string) => {
    if (window.confirm("Are you sure you want to delete this note?")) {
      deleteNote.mutate(
        { id: noteId, collectionId: collectionId || "" },
        {
          onSuccess: () => {
            toast({
              title: "Success",
              description: "Note deleted successfully",
            })
            refetch()
          },
          onError: (error: any) => {
            toast({
              title: "Error",
              description: error.message || "Failed to delete note",
              variant: "destructive",
            })
          },
        },
      )
    }
  }

  // Handle cancel editing/creating
  const handleCancel = () => {
    setIsCreatingNote(false)
    setEditingNote(null)
  }

  // Handle successful creation/update
  const handleSuccess = () => {
    setIsCreatingNote(false)
    setEditingNote(null)
    refetch()
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-3 text-muted-foreground">Loading collection...</span>
      </div>
    )
  }

  // Show error state
  if (error || !collection) {
    return (
      <Alert variant="destructive" className="animate-in fade-in">
        <AlertDescription>
          <h3 className="font-bold">Error loading collection</h3>
          <p className="mt-1">{error instanceof Error ? error.message : "Collection not found"}</p>
          <Button onClick={handleBack} className="mt-3">
            Back to Collections
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  // If viewing a note in full screen
  if (viewingNote) {
    return (
      <NoteFullView
        note={viewingNote}
        onClose={() => setViewingNote(null)}
      />
    )
  }

  // If creating or editing, show the form
  if (isCreatingNote || editingNote) {
    return (
      <NoteForm
        note={editingNote}
        collectionId={collectionId || ""}
        onCancel={handleCancel}
        onSuccess={handleSuccess}
      />
    )
  }

  return (
    <div className="space-y-6 animate-in fade-in bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-md">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mr-2 -ml-3 hover:bg-accent/80 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            {collection.name}
          </h1>
        </div>
        <Button
          onClick={() => setIsCreatingNote(true)}
          disabled={(collection.notes?.length || 0) >= 100}
          className="whitespace-nowrap shadow-sm hover:shadow-md transition-shadow bg-primary text-primary-foreground"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Note
        </Button>
      </div>

      <div className="flex flex-wrap items-center gap-3 bg-white dark:bg-gray-800 p-5 rounded-lg mb-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
        <div className="flex flex-wrap items-center gap-2">
          <span
            className={`px-3 py-1.5 rounded-full text-sm font-medium ${
              collection.visibility === "public"
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
            }`}
          >
            {collection.visibility === "public" ? "Public" : "Private"}
          </span>
          <span className="text-sm px-3 py-1.5 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 font-medium">
            Topic: {collection.topic || "General"}
          </span>
          <span className="text-sm px-3 py-1.5 rounded-full bg-primary/10 text-primary font-medium">
            {collection.notes?.length || 0} notes
          </span>
        </div>
        <div className="ml-auto text-sm text-muted-foreground">
          <span className="hidden sm:inline">Created: </span>
          {new Date(collection.createdAt).toLocaleDateString()}
        </div>
      </div>

      {!collection.notes || collection.notes.length === 0 ? (
        <Card className="p-8 text-center border-dashed animate-in fade-in bg-white dark:bg-gray-800">
          <div className="flex flex-col items-center justify-center space-y-4 py-8">
            <div className="rounded-full bg-primary/10 p-5 animate-pulse">
              <PlusCircle className="h-12 w-12 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">No notes in this collection yet</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Create your first note to start organizing your thoughts in this collection.
            </p>
            <Button
              onClick={() => setIsCreatingNote(true)}
              size="lg"
              className="mt-4 shadow-sm hover:shadow-md transition-shadow px-6"
            >
              <PlusCircle className="mr-2 h-5 w-5" />
              Create Your First Note
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 animate-in fade-in max-w-6xl mx-auto">
          {collection.notes.map((note) => (
            <Card
              key={note.id}
              className="overflow-hidden hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700 hover:border-primary/20 bg-white dark:bg-gray-800"
            >
              <CardContent className="p-5">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-medium text-lg line-clamp-1 text-gray-900 dark:text-white">
                    {note.title}
                  </h3>
                  <div className="flex space-x-1 -mr-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewNote(note)}
                      className="h-8 w-8 p-0 hover:bg-accent/80 rounded-full"
                    >
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditNote(note)}
                      className="h-8 w-8 p-0 hover:bg-accent/80 rounded-full"
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteNote(note.id)}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-full"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 my-2 pt-3">
                  <div className="max-h-32 overflow-hidden">
                    <TiptapContent content={note.content} />
                    <div className="h-10 bg-gradient-to-t from-white dark:from-gray-800 to-transparent -mt-10 relative"></div>
                  </div>
                </div>
                <div className="mt-3 text-xs text-muted-foreground flex justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                  <span>Created: {new Date(note.createdAt).toLocaleDateString()}</span>
                  <span>Updated: {new Date(note.updatedAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {collection.notes && collection.notes.length >= 100 && (
        <Alert className="bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800 animate-in fade-in mt-6">
          <AlertDescription className="text-yellow-800 dark:text-yellow-200">
            You have reached the maximum limit of 100 notes in this collection. Delete existing notes to create new
            ones.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
