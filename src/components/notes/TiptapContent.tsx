"use client"

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import Underline from '@tiptap/extension-underline'
import Highlight from '@tiptap/extension-highlight'

interface TiptapContentProps {
  content: string
}

export default function TiptapContent({ content }: TiptapContentProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          class: 'text-blue-500 underline cursor-pointer',
          target: '_blank',
          rel: 'noopener noreferrer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full rounded-md my-4',
        },
      }),
      TextStyle.configure(),
      Color.configure(),
      Underline,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 text-black px-1 rounded',
        },
      }),
    ],
    content,
    editable: false,
  })

  return (
    <div className="tiptap-content">
      <style>{`
        .tiptap-content .ProseMirror [style*="color"] {
          color: inherit;
        }
        .tiptap-content .ProseMirror [style*="color: #000000"] { color: #000000 !important; }
        .tiptap-content .ProseMirror [style*="color: #ef4444"] { color: #ef4444 !important; }
        .tiptap-content .ProseMirror [style*="color: #f97316"] { color: #f97316 !important; }
        .tiptap-content .ProseMirror [style*="color: #eab308"] { color: #eab308 !important; }
        .tiptap-content .ProseMirror [style*="color: #22c55e"] { color: #22c55e !important; }
        .tiptap-content .ProseMirror [style*="color: #3b82f6"] { color: #3b82f6 !important; }
        .tiptap-content .ProseMirror [style*="color: #8b5cf6"] { color: #8b5cf6 !important; }
        .tiptap-content .ProseMirror [style*="color: #ec4899"] { color: #ec4899 !important; }

        .dark .tiptap-content .ProseMirror [style*="color: #000000"] { color: #ffffff !important; }
      `}</style>
      <EditorContent
        editor={editor}
        className="prose prose-sm sm:prose max-w-none dark:prose-invert"
      />
    </div>
  )
}
