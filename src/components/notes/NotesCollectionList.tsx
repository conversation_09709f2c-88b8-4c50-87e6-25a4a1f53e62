"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { PlusCircle, Trash2, Edit, Eye, Search, BookOpen, SlidersHorizontal } from "lucide-react"
import { useNotes } from "@/hooks/useNotes"
import type { NotesCollection } from "@/services/notesService"
import { useToast } from "@/components/ui/use-toast"
import NotesCollectionForm from "./NotesCollectionForm"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface NotesCollectionListProps {
  onViewCollection?: (collectionId: string) => void
}

export default function NotesCollectionList({ onViewCollection }: NotesCollectionListProps = {}) {
  const { toast } = useToast()
  const [isCreatingCollection, setIsCreatingCollection] = useState(false)
  const [editingCollection, setEditingCollection] = useState<NotesCollection | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [visibilityFilter, setVisibilityFilter] = useState<"all" | "public" | "private">("all")
  const [topicFilter, setTopicFilter] = useState<string>("all")
  const [showFilters, setShowFilters] = useState(false)
  const [availableTopics, setAvailableTopics] = useState<string[]>([])

  // Get hooks from useNotes
  const { useCollections, useDeleteCollection } = useNotes()

  // Get collections data
  const { data: allCollections = [], isLoading, error, refetch } = useCollections()

  // Extract unique topics from collections
  useEffect(() => {
    if (allCollections.length > 0) {
      const topics = allCollections
        .map((collection) => collection.topic || "General")
        .filter((value, index, self) => self.indexOf(value) === index)
      setAvailableTopics(topics)
    }
  }, [allCollections])

  // Filter collections based on search query and filters
  const collections = allCollections.filter((collection) => {
    // Search filter
    const matchesSearch =
      searchQuery === "" ||
      collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (collection.topic || "General").toLowerCase().includes(searchQuery.toLowerCase())

    // Visibility filter
    const matchesVisibility = visibilityFilter === "all" || collection.visibility === visibilityFilter

    // Topic filter
    const matchesTopic = topicFilter === "all" || (collection.topic || "General") === topicFilter

    return matchesSearch && matchesVisibility && matchesTopic
  })

  // Delete collection mutation
  const deleteCollection = useDeleteCollection()

  // Handle view collection
  const handleViewCollection = (collectionId: string) => {
    if (onViewCollection) {
      onViewCollection(collectionId)
    } else {
      // Fallback if no onViewCollection prop is provided
      const url = new URL(window.location.href)
      url.searchParams.set("collectionId", collectionId)
      window.history.pushState({}, "", url)
      window.location.reload() // Force reload to handle the URL change
    }
  }

  // Handle edit collection
  const handleEditCollection = (collection: NotesCollection) => {
    setEditingCollection(collection)
  }

  // Handle delete collection
  const handleDeleteCollection = (collectionId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this collection? All notes in this collection will be permanently deleted.",
      )
    ) {
      deleteCollection.mutate(collectionId, {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Collection deleted successfully",
          })
          refetch()
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to delete collection",
            variant: "destructive",
          })
        },
      })
    }
  }

  // Handle cancel editing/creating
  const handleCancel = () => {
    setIsCreatingCollection(false)
    setEditingCollection(null)
  }

  // Handle successful creation/update
  const handleSuccess = () => {
    setIsCreatingCollection(false)
    setEditingCollection(null)
    refetch()
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-3 text-muted-foreground">Loading collections...</span>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-800 rounded-md">
        <h3 className="font-bold">Error loading collections</h3>
        <p>{error instanceof Error ? error.message : "Unknown error occurred"}</p>
        <Button onClick={() => refetch()} className="mt-2">
          Try Again
        </Button>
      </div>
    )
  }

  // If creating or editing, show the form
  if (isCreatingCollection || editingCollection) {
    return <NotesCollectionForm collection={editingCollection} onCancel={handleCancel} onSuccess={handleSuccess} />
  }

  // Render the collection list
  return (
    <div className="space-y-6 bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-md animate-in fade-in">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">My Notes Collections</h1>
          <p className="text-sm text-muted-foreground mt-1">
            {allCollections.length} collection{allCollections.length !== 1 ? 's' : ''} • {collections.length} shown
          </p>
        </div>
        <Button
          onClick={() => setIsCreatingCollection(true)}
          disabled={allCollections.length >= 10}
          className="whitespace-nowrap shadow-sm hover:shadow-md transition-shadow bg-primary text-primary-foreground"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          New Collection
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="relative flex-1">
          <Input
            placeholder="Search collections by name or topic..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-11 shadow-sm hover:shadow-md transition-shadow"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        </div>

        <div className="flex gap-2">
          <Button
            variant={showFilters ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 h-11 shadow-sm hover:shadow-md transition-shadow"
          >
            <SlidersHorizontal className="h-4 w-4" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-5 border rounded-lg bg-white dark:bg-gray-800 mb-6 shadow-sm animate-in fade-in border-gray-200 dark:border-gray-700">
          <div>
            <label className="text-sm font-medium mb-2 block text-gray-700 dark:text-gray-300">Visibility</label>
            <Select
              value={visibilityFilter}
              onValueChange={(value: "all" | "public" | "private") => setVisibilityFilter(value)}
            >
              <SelectTrigger className="w-full h-11 shadow-sm hover:shadow-md transition-shadow">
                <SelectValue placeholder="Filter by visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block text-gray-700 dark:text-gray-300">Topic</label>
            <Select value={topicFilter} onValueChange={(value) => setTopicFilter(value)}>
              <SelectTrigger className="w-full h-11 shadow-sm hover:shadow-md transition-shadow">
                <SelectValue placeholder="Filter by topic" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Topics</SelectItem>
                {availableTopics.map((topic) => (
                  <SelectItem key={topic} value={topic}>
                    {topic}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <Alert variant="destructive" className="animate-in fade-in">
          <AlertDescription>Error loading collections. Please try again.</AlertDescription>
          <div className="mt-4">
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </Alert>
      ) : allCollections.length === 0 ? (
        <Card className="p-8 text-center border-dashed animate-in fade-in bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div className="flex flex-col items-center justify-center space-y-4 py-8">
            <div className="rounded-full bg-primary/10 p-6 animate-pulse">
              <BookOpen className="h-12 w-12 text-primary" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">No notes collections yet</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Create your first notes collection to start organizing your thoughts. You can add text and organize them
              by topics.
            </p>
            <Button 
              onClick={() => setIsCreatingCollection(true)} 
              size="lg" 
              className="mt-4 shadow-sm hover:shadow-md transition-shadow px-6 bg-primary text-primary-foreground"
            >
              <PlusCircle className="mr-2 h-5 w-5" />
              Create Your First Collection
            </Button>
          </div>
        </Card>
      ) : collections.length === 0 ? (
        <div className="text-center p-8 border rounded-md bg-white dark:bg-gray-800 animate-in fade-in border-gray-200 dark:border-gray-700">
          <p className="text-muted-foreground mb-4">No collections match your search criteria</p>
          <Button
            variant="outline"
            onClick={() => {
              setSearchQuery("")
              setVisibilityFilter("all")
              setTopicFilter("all")
            }}
            className="shadow-sm hover:shadow-md transition-shadow"
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 animate-in fade-in">
          {collections.map((collection) => (
            <Card 
              key={collection.id} 
              className="overflow-hidden hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700 hover:border-primary/20 bg-white dark:bg-gray-800"
            >
              <CardHeader className="pb-2 pt-4 bg-gray-50 dark:bg-gray-850 border-b border-gray-100 dark:border-gray-700">
                <CardTitle className="text-xl line-clamp-1 text-gray-900 dark:text-white">
                  {collection.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-5">
                <div className="text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-1 mb-2">
                    <span className="font-medium text-gray-900 dark:text-white">{collection.noteCount}</span> 
                    <span>notes • Updated</span>
                    <span className="font-medium">
                      {new Date(collection.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        collection.visibility === "public"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                          : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
                      }`}
                    >
                      {collection.visibility === "public" ? "Public" : "Private"}
                    </span>
                    <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                      {collection.topic || "General"}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleViewCollection(collection.id)}
                    className="w-1/3 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <div className="space-x-2 flex">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleEditCollection(collection)}
                      className="shadow-sm hover:shadow-md transition-shadow"
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCollection(collection.id)}
                      className="text-destructive hover:text-destructive shadow-sm hover:shadow-md transition-shadow"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
