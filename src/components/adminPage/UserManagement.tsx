import { useForm } from '@tanstack/react-form'
import { useState } from 'react'
import { TeacherForm } from './User/Teacher'
import { StudentForm } from './User/Student'
import { ClassForm } from './User/Class'
import { MatiereForm } from './User/Matiere'
import { defaultValues } from './User/defaultValues'
import { parentForm } from './User/Parent'

export default function UserManagement() {
  const [entity, setEntity] = useState('teacher')
  const [message, setMessage] = useState('')

  const form = useForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      try {
        console.log('Form submitted:', value)
        // Implement your submission logic here
        setMessage(`Successfully added ${entity}`)
      } catch (error) {
        console.error('Submission error:', error)
        setMessage(`Error adding ${entity}`)
      }
    },
  })

  const formComponents: any = {
    subject: MatiereForm,
    teacher: TeacherForm,
    student: StudentForm,
    class: ClassForm,
    parent: parentForm,
    // classroom: ClassroomForm,
    // seance: SeanceForm,
  }

  const FormComponent = formComponents[entity]

  return (
    <div className="flex flex-col items-center p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">Entity Manager</h1>
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-md p-6">
        <select
          onChange={(e) => setEntity(e.target.value)}
          value={entity}
          className="w-full mb-6 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="subject">Subject</option>
          <option value="class">Class</option>
          <option value="teacher">Teacher</option>
          <option value="student">Student</option>
          <option value="parent">Parent</option>
          {/* <option value="seance">Seance</option> */}
          {/* <option value="classroom">Classroom</option> */}
        </select>

        <form onSubmit={form.handleSubmit} className="space-y-4">
          <FormComponent form={form} />
          {/* <button
            type="submit"
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            Add {entity}
          </button> */}
        </form>

        {message && (
          <p className="mt-4 text-center text-sm font-medium px-4 py-2 rounded-md bg-green-100 text-green-800">
            {message}
          </p>
        )}
      </div>
    </div>
  )
}
