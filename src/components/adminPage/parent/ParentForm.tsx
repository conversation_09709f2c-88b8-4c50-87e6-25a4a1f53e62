import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Parent as BaseParent, Student } from '@/interface/types/user'
import { useState } from 'react'
import FileUploader from '@/components/shared/FileUploader'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import { Eye, EyeOff, Search, X } from 'lucide-react'
import { FormYearMonthPicker } from '@/components/ui/form-year-month-picker'

// Add missing properties to Parent interface
interface Parent extends BaseParent {
  avatar?: string
  isActive?: boolean
}

const parentSchema = z.object({
  cin: z.string().min(8, 'CIN must be 8 characters'),
  firstname: z.string().min(2, 'First name must be at least 2 characters'),
  lastname: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password must contain at least one special character'
    )
    .optional(),
  birthday: z.date().optional(),
  gender: z.enum(['male', 'female'] as const),
  address: z.string().optional(),
  phone: z.string().optional(),
  avatar: z.string().url().default(DEFAULT_AVATAR),
  isActive: z.boolean().default(true),
  studentIds: z.array(z.string()).optional(),
})

type ParentFormValues = z.infer<typeof parentSchema>
export type { ParentFormValues }

interface ParentFormProps {
  initialData?: Partial<Parent>
  students: Student[]
  onSubmit: (data: ParentFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing parents
 * @param {ParentFormProps} props - The props for the ParentForm component
 * @returns {JSX.Element} The rendered ParentForm component
 */
export function ParentForm({
  initialData,
  students,
  onSubmit,
  onCancel,
  submitLabel,
}: ParentFormProps) {
  // Get the initial selected student IDs from the parent's students
  const initialStudentIds =
    initialData?.students?.map((student) => student.id) || []
  const [selectedStudentIds, setSelectedStudentIds] =
    useState<string[]>(initialStudentIds)

  // State for student search
  const [studentSearchQuery, setStudentSearchQuery] = useState('')

  // Filter students based on search query
  const filteredStudents = students.filter((student) => {
    const fullName = `${student.firstname} ${student.lastname}`.toLowerCase()
    const cin = student.cin?.toLowerCase() || ''
    const query = studentSearchQuery.toLowerCase()
    return fullName.includes(query) || cin.includes(query)
  })

  // State for password visibility toggle
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isPasswordFocused, setIsPasswordFocused] = useState(false)

  // Function to toggle password visibility
  const togglePasswordVisibility = () => {
    setIsPasswordVisible((prev) => !prev)
  }

  // Normalize the gender value from server
  const normalizeGender = (gender: string | undefined): 'male' | 'female' => {
    if (!gender) return 'male' // Default to male if no gender is provided
    return gender.toLowerCase() as 'male' | 'female'
  }

  // Log initial data for debugging
  console.log('ParentForm initialData:', initialData);
  console.log('ParentForm initialStudentIds:', initialStudentIds);
  console.log('ParentForm students:', students);

  const form = useForm<ParentFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      cin: initialData?.cin || '',
      firstname: initialData?.firstname || '',
      lastname: initialData?.lastname || '',
      email: initialData?.email || '',
      password: '',
      birthday: initialData?.birthday
        ? new Date(initialData.birthday)
        : undefined,
      gender: normalizeGender(initialData?.gender),
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      avatar: initialData?.avatar || DEFAULT_AVATAR,
      isActive:
        initialData?.isActive !== undefined ? initialData.isActive : true,
      studentIds: initialStudentIds,
    },
    onSubmit: async ({ value }) => {
      // Update the studentIds field with the selected students
      const formData = {
        ...value,
        studentIds: selectedStudentIds,
        // Ensure avatar and isActive are always present
        avatar: value.avatar || DEFAULT_AVATAR,
        isActive: value.isActive !== undefined ? value.isActive : true,
      }
      await onSubmit(formData)
    },
  })

  // Function to handle student selection
  const handleStudentSelect = (studentId: string) => {
    setSelectedStudentIds((prev) => {
      if (prev.includes(studentId)) {
        return prev.filter((id) => id !== studentId)
      }
      return [...prev, studentId]
    })
  }

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="cin"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'CIN is required'
              if (value.length !== 8) return 'CIN must be 8 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="cin">CIN</Label>
              <Input
                id="cin"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter CIN"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="firstname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'First name is required'
              if (value.length < 2)
                return 'First name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="firstname">First Name</Label>
              <Input
                id="firstname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter first name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="lastname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Last name is required'
              if (value.length < 2)
                return 'Last name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="lastname">Last Name</Label>
              <Input
                id="lastname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter last name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
                return 'Invalid email format'
              }
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter email"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        {!initialData ? (
          <form.Field
            name="password"
            validators={{
              // Only validate on blur to avoid showing errors while typing
              onChange: ({ value }) => {
                // Don't show errors while typing, just check if empty
                if (!value) return 'Password is required'
                return undefined
              },
              onBlur: ({ value }) => {
                // Full validation on blur
                if (!value) return 'Password is required'

                const errors = []
                if (value.length < 6) {
                  errors.push('Password must be at least 6 characters')
                }
                if (!/[A-Z]/.test(value)) {
                  errors.push(
                    'Password must contain at least one uppercase letter'
                  )
                }
                if (!/[0-9]/.test(value)) {
                  errors.push('Password must contain at least one number')
                }
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
                  errors.push(
                    'Password must contain at least one special character'
                  )
                }

                return errors.length > 0 ? errors.join(', ') : undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={isPasswordVisible ? 'text' : 'password'}
                    value={field.state.value}
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => {
                      field.handleBlur()
                      setIsPasswordFocused(false)
                    }}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter password"
                    className={
                      field.state.meta.isTouched && field.state.meta.errors
                        ? 'border-red-500'
                        : ''
                    }
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={togglePasswordVisibility}
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {/* Password requirements - only show when field is focused or has content */}
                {(isPasswordFocused || field.state.value) && (
                  <div className="mt-2 text-xs text-gray-500">
                    <p>Password must contain:</p>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li
                        className={
                          field.state.value && field.state.value.length >= 6
                            ? 'text-green-500'
                            : ''
                        }
                      >
                        At least 6 characters
                      </li>
                      <li
                        className={
                          field.state.value && /[A-Z]/.test(field.state.value)
                            ? 'text-green-500'
                            : ''
                        }
                      >
                        At least one uppercase letter
                      </li>
                      <li
                        className={
                          field.state.value && /[0-9]/.test(field.state.value)
                            ? 'text-green-500'
                            : ''
                        }
                      >
                        At least one number
                      </li>
                      <li
                        className={
                          field.state.value &&
                          /[!@#$%^&*(),.?":{}|<>]/.test(field.state.value)
                            ? 'text-green-500'
                            : ''
                        }
                      >
                        At least one special character
                      </li>
                    </ul>
                  </div>
                )}

                {/* Password strength indicator */}
                {field.state.value && (
                  <div className="mt-2">
                    <div className="flex justify-between mb-1">
                      <span className="text-xs">Password strength:</span>
                      <span className="text-xs">
                        {(() => {
                          const value = field.state.value
                          const hasLength = value.length >= 6
                          const hasUpper = /[A-Z]/.test(value)
                          const hasNumber = /[0-9]/.test(value)
                          const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(
                            value
                          )
                          const score = [
                            hasLength,
                            hasUpper,
                            hasNumber,
                            hasSpecial,
                          ].filter(Boolean).length

                          if (score === 4) return 'Strong'
                          if (score === 3) return 'Good'
                          if (score === 2) return 'Fair'
                          return 'Weak'
                        })()}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full ${(() => {
                          const value = field.state.value
                          const hasLength = value.length >= 6
                          const hasUpper = /[A-Z]/.test(value)
                          const hasNumber = /[0-9]/.test(value)
                          const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(
                            value
                          )
                          const score = [
                            hasLength,
                            hasUpper,
                            hasNumber,
                            hasSpecial,
                          ].filter(Boolean).length

                          if (score === 4) return 'bg-green-500 w-full'
                          if (score === 3) return 'bg-blue-500 w-3/4'
                          if (score === 2) return 'bg-yellow-500 w-2/4'
                          return 'bg-red-500 w-1/4'
                        })()}`}
                      ></div>
                    </div>
                  </div>
                )}

                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>
        ) : (
          <form.Field
            name="password"
            validators={{
              onBlur: ({ value }) => {
                // Skip validation if empty (keeping current password)
                if (!value || value.trim() === '') return undefined
                
                // Validate password only if provided
                const errors = []
                if (value.length < 6) {
                  errors.push('Password must be at least 6 characters')
                }
                if (!/[A-Z]/.test(value)) {
                  errors.push('Password must contain at least one uppercase letter')
                }
                if (!/[0-9]/.test(value)) {
                  errors.push('Password must contain at least one number')
                }
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
                  errors.push('Password must contain at least one special character')
                }

                return errors.length > 0 ? errors.join(', ') : undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password (leave empty to keep current)</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={isPasswordVisible ? 'text' : 'password'}
                    value={field.state.value}
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => {
                      field.handleBlur()
                      setIsPasswordFocused(false)
                    }}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter new password or leave empty"
                    className={
                      field.state.meta.isTouched && field.state.meta.errors
                        ? 'border-red-500'
                        : ''
                    }
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={togglePasswordVisibility}
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
                {isPasswordFocused && (
                  <p className="text-xs text-gray-500">
                    Leave empty to keep the current password. Enter a new password to change it.
                  </p>
                )}
              </div>
            )}
          </form.Field>
        )}

        <form.Field name="birthday">
          {(field) => (
            <FormYearMonthPicker
              id="birthday"
              label="Birthday"
              value={field.state.value}
              onChange={(date) => field.handleChange(date)}
              onBlur={field.handleBlur}
              placeholder="Select birthday"
            />
          )}
        </form.Field>

        <form.Field
          name="gender"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Gender is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={field.state.value}
                onValueChange={(value: 'male' | 'female') =>
                  field.handleChange(value)
                }
              >
                <SelectTrigger id="gender">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="address">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter address"
              />
            </div>
          )}
        </form.Field>

        <form.Field name="phone">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter phone number"
              />
            </div>
          )}
        </form.Field>

        <form.Field name="avatar">
          {(field) => (
            <FileUploader
              label="Parent Avatar"
              defaultPreview={field.state.value || DEFAULT_AVATAR}
              onFileUploaded={(url) => field.handleChange(url)}
              isAvatar={true}
              previewComponent={(previewUrl) => (
                <CdnImage
                  src={previewUrl}
                  alt="Parent avatar"
                  className="h-28 w-28 rounded-full object-cover border-2 border-gray-200 shadow-sm"
                />
              )}
            />
          )}
        </form.Field>

        <form.Field name="isActive">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <Select
                value={field.state.value.toString()}
                onValueChange={(value) => field.handleChange(value === 'true')}
              >
                <SelectTrigger id="isActive">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </form.Field>
      </div>

      <div className="space-y-2">
        <Label htmlFor="students">Assigned Students</Label>
        <div className="mb-2 relative">
          <Input
            placeholder="Search students by name or CIN..."
            value={studentSearchQuery}
            onChange={(e) => setStudentSearchQuery(e.target.value)}
            className="w-full pl-9"
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          {studentSearchQuery && (
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3"
              onClick={() => setStudentSearchQuery('')}
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          )}
        </div>
        <div className="border rounded-md p-2">
          <ScrollArea className="h-[200px] w-full">
            <div className="space-y-2">
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student) => (
                  <div
                    key={student.id}
                    className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded cursor-pointer"
                    onClick={() => handleStudentSelect(student.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedStudentIds.includes(student.id)}
                      onChange={() => handleStudentSelect(student.id)}
                      className="h-4 w-4"
                    />
                    <span>
                      {student.firstname} {student.lastname} ({student.cin})
                    </span>
                  </div>
                ))
              ) : (
                <div className="p-2 text-center text-gray-500">
                  {studentSearchQuery
                    ? 'No students match your search'
                    : 'No students available'}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
        <div className="mt-2 flex flex-wrap gap-2">
          {selectedStudentIds.map((id) => {
            const student = students.find((s) => s.id === id)
            return (
              <Badge
                key={id}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {student?.firstname} {student?.lastname}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleStudentSelect(id)
                  }}
                  className="ml-1 hover:text-red-500"
                >
                  ×
                </button>
              </Badge>
            )
          })}
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
