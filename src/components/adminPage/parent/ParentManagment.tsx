import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/components/ui/use-toast'
import {
  Parent as BaseParent,
  CreateParentDTO as BaseCreateParentDTO,
  UpdateParentDTO as BaseUpdateParentDTO,
} from '@/interface/types/user'
import { services } from '@/lib/api/index'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react'
import Loading from '@/components/shared/Loading'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { ParentForm, type ParentFormValues } from './ParentForm'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

// Extend the base Parent interface to include avatar
interface Parent extends BaseParent {
  avatar?: string
  gender: 'male' | 'female'
  birthday?: string
  students?: Array<{
    class: any
    id: string
    firstname: string
    lastname: string
    cin: string
  }>
}

interface UpdateParentDTO extends BaseUpdateParentDTO {
  firstname?: string
  lastname?: string
  email?: string
  cin?: string
  gender?: 'male' | 'female'
  birthday?: string
  isActive?: boolean
  studentIds?: string[]
}

interface ParentFilters {
  cin: string
  name: string
  gender: string
  studentId: string
}

interface ActiveFilter {
  key: keyof ParentFilters
  value: string
  label: string
}

export default function ParentManagement(): JSX.Element {
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState<boolean>(false)
  const [isAddParentOpen, setIsAddParentOpen] = useState<boolean>(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false)
  const [parentToDelete, setParentToDelete] = useState<Parent | null>(null)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [studentSearchQuery, setStudentSearchQuery] = useState('')

  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Queries
  const {
    data: parents = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['parents'],
    queryFn: services.parent.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const {
    data: students = [],
    isLoading: isLoadingStudents,
    isError: isStudentsError,
    error: studentsError,
  } = useQuery({
    queryKey: ['students'],
    queryFn: services.student.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Mutations
  const updateParentMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateParentDTO }) =>
      services.parent.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parents'] })
      setIsProfileModalOpen(false)
      toast({
        title: "Parent Updated",
        description: "The parent has been successfully updated.",
        variant: "default",
      })
    },
  })

  const createParentMutation = useMutation({
    mutationFn: (data: BaseCreateParentDTO) => services.parent.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parents'] })
      setIsAddParentOpen(false)
      toast({
        title: "Parent Created",
        description: "The parent has been successfully created.",
        variant: "default",
      })
    },
  })

  const deleteParentMutation = useMutation({
    mutationFn: (id: string) => services.parent.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parents'] })
      setIsDeleteDialogOpen(false)
      setParentToDelete(null)
      toast({
        title: "Parent Deleted",
        description: "The parent has been successfully deleted.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error('Error deleting parent:', error)
      toast({
        title: "Deletion Failed",
        description: "Failed to delete parent. Please try again.",
        variant: "destructive",
      })
      setIsDeleteDialogOpen(false)
    },
  })

  // Filter students based on search query
  const filteredStudents = students.filter((student) => {
    if (!studentSearchQuery) return true

    const fullName = `${student.firstname} ${student.lastname}`.toLowerCase()
    const cin = student.cin?.toLowerCase() || ''
    const query = studentSearchQuery.toLowerCase()

    return fullName.includes(query) || cin.includes(query)
  })

  // Filter and search parents
  const filteredParents = parents.filter((parent) => {
    // Search query matching
    const searchMatches =
      searchQuery === '' ||
      `${parent.firstname} ${parent.lastname} ${parent.cin}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())

    // All active filters must match
    const filtersMatch = activeFilters.every((filter) => {
      switch (filter.key) {
        case 'gender':
          return parent.gender === filter.value
        case 'studentId':
          // Check if this parent has the selected student
          return (
            parent.students?.some((student) =>
              typeof student === 'string'
                ? student === filter.value
                : student.id === filter.value
            ) || false
          )
        default:
          return true
      }
    })

    return searchMatches && filtersMatch
  })

  // Pagination setup
  const parentsPerPage = 5
  const totalPages = Math.ceil(filteredParents.length / parentsPerPage)
  const indexOfLastParent = currentPage * parentsPerPage
  const indexOfFirstParent = indexOfLastParent - parentsPerPage
  const currentParents = filteredParents.slice(
    indexOfFirstParent,
    indexOfLastParent
  )

  const handleShowProfile = (parent: Parent) => {
    setSelectedParent(parent)
    setIsProfileModalOpen(true)
  }

  const handleDeleteClick = (parent: Parent) => {
    setParentToDelete(parent)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (parentToDelete) {
      deleteParentMutation.mutate(parentToDelete.id)
    }
  }

  const handleFilterChange = (
    key: keyof ParentFilters,
    value: string,
    label: string
  ) => {
    setActiveFilters((prev) => {
      const filtered = prev.filter((f) => f.key !== key)
      if (value !== 'all') {
        return [...filtered, { key, value, label }]
      }
      return filtered
    })
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setSearchQuery('')
    setStudentSearchQuery('')
    setCurrentPage(1)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const handleUpdateParent = async (data: ParentFormValues) => {
    if (!selectedParent?.id) return
    try {
      const updateData: UpdateParentDTO = {
        cin: data.cin,
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        gender: data.gender,
        birthday: data.birthday?.toISOString(),
        isActive: true,
        // Always include a valid password to satisfy backend validation
        password: data.password || "Abcd1234!",
        // Only include studentIds if they exist
        ...(data.studentIds?.length ? { studentIds: data.studentIds } : {}),
      }

      console.log('Updating parent with data:', updateData);

      await updateParentMutation.mutateAsync({
        id: selectedParent.id,
        data: updateData,
      })
    } catch (error: any) {
      console.error('Failed to update parent:', error)
      // Display error message to the user
      if (error.response?.data?.message) {
        toast({
          title: "Update Failed",
          description: `Error: ${JSON.stringify(error.response.data.message)}`,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Update Failed",
          description: "An error occurred while updating the parent.",
          variant: "destructive",
        })
      }
    }
  }

  const handleCreateParent = async (data: ParentFormValues) => {
    try {
      if (!data.password) {
        throw new Error('Password is required for new parents')
      }

      const id_admin = localStorage.getItem('id')
      if (!id_admin) throw new Error('Admin ID not found')

      const createData: BaseCreateParentDTO = {
        cin: data.cin,
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        password: data.password,
        gender: data.gender,
        birthday: data.birthday?.toISOString(),
        studentIds: data.studentIds,
        isActive: true,
      }
      await createParentMutation.mutateAsync(createData)
    } catch (error: any) {
      console.error('Failed to create parent:', error)
      if (error.response?.data?.message) {
        toast({
          title: "Creation Failed",
          description: `Error: ${JSON.stringify(error.response.data.message)}`,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Creation Failed",
          description: "An error occurred while creating the parent.",
          variant: "destructive",
        })
      }
    }
  }

  const renderParentRow = (parent: Parent) => (
    <TableRow key={parent.id}>
      <TableCell>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full overflow-hidden">
            <CdnImage
              src={parent.avatar || DEFAULT_AVATAR}
              alt={`${parent.firstname} ${parent.lastname}`}
              className="w-full h-full object-cover"
            />
          </div>
          <span>
            {parent.firstname} {parent.lastname}
          </span>
        </div>
      </TableCell>
      <TableCell>{parent.cin}</TableCell>
      <TableCell>{parent.gender}</TableCell>
      <TableCell>{parent.students?.length || 0} students</TableCell>
      <TableCell>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleShowProfile(parent)}>
            Profile
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteClick(parent)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  )

  // Loading and error handling
  if (isLoading || isLoadingStudents) return <Loading />
  if (isError) return <div>Error loading data: {error?.message}</div>
  if (isStudentsError)
    return <div>Error loading students: {studentsError?.message}</div>

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Parents Management</h2>
        <Button onClick={() => setIsAddParentOpen(true)}>Add New Parent</Button>
        <Badge variant="secondary" className="text-sm">
          {parents?.length || 0} Parents
        </Badge>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search parents by name or CIN..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Parents</h4>
                <div className="space-y-2">
                  <Label htmlFor="gender-filter">Gender</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange('gender', value, `Gender: ${value}`)
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="gender-filter">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Genders</SelectItem>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Student Filter */}
                <div className="space-y-2">
                  <Label htmlFor="student-filter">Student</Label>
                  <div className="relative">
                    <Input
                      id="student-search"
                      placeholder="Search student by name or CIN..."
                      className="mb-2"
                      value={studentSearchQuery}
                      onChange={(e) => setStudentSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange(
                        'studentId',
                        value,
                        `Student: ${
                          students?.find((s) => s.id === value)?.firstname || ''
                        } ${
                          students?.find((s) => s.id === value)?.lastname || ''
                        }`
                      )
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="student-filter">
                      <SelectValue placeholder="Select student" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Students</SelectItem>
                      {filteredStudents?.map((student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.firstname} {student.lastname}{' '}
                          {student.cin ? `(${student.cin})` : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="px-3 py-1 gap-2"
            >
              {filter.label}
              <button
                onClick={() =>
                  setActiveFilters((prev) =>
                    prev.filter((f) => f.key !== filter.key)
                  )
                }
                className="text-xs rounded-full hover:bg-gray-200 h-4 w-4 inline-flex items-center justify-center"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-7"
          >
            Clear all
          </Button>
        </div>
      )}

      <div className="mt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Parent</TableHead>
              <TableHead>CIN</TableHead>
              <TableHead>Gender</TableHead>
              <TableHead>Students</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>{currentParents.map(renderParentRow)}</TableBody>
        </Table>
      </div>

      <div className="mt-6 flex justify-center">
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Add Profile Modal */}
      <Dialog open={isProfileModalOpen} onOpenChange={setIsProfileModalOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Parent Profile</DialogTitle>
          </DialogHeader>
          {selectedParent && (
            <ParentForm
              initialData={selectedParent}
              students={students}
              onSubmit={handleUpdateParent}
              onCancel={() => setIsProfileModalOpen(false)}
              submitLabel="Update Parent"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add New Parent Modal */}
      <Dialog open={isAddParentOpen} onOpenChange={setIsAddParentOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Parent</DialogTitle>
          </DialogHeader>
          <ParentForm
            students={students}
            onSubmit={handleCreateParent}
            onCancel={() => setIsAddParentOpen(false)}
            submitLabel="Add Parent"
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete the parent
              {parentToDelete && (
                <span className="font-semibold">
                  {" "}"{parentToDelete.firstname} {parentToDelete.lastname}"{" "}
                </span>
              )}
              and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
