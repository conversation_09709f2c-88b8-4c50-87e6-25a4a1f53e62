import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Parent as BaseParent, Student, UpdateParentDTO } from '@/interface/types'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'

import { parentService, studentService } from '@/lib/api/services'

// Extended Parent interface with studentIds for API compatibility
interface Parent extends BaseParent {
  studentIds?: string[]
}
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, ChevronLeft, ChevronRight } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useVirtualizer } from '@tanstack/react-virtual'
import { ParentForm } from './ParentForm'
import type { ParentFormValues } from './ParentForm'
import Loading from '@/components/shared/Loading'

interface ParentFilters {
  cin: string
  name: string
  gender: string
  hasStudents: string
}

interface ActiveFilter {
  key: keyof ParentFilters
  value: string
  label: string
}

export default function ParentManagement(): JSX.Element {
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState<boolean>(false)
  const [isAddParentOpen, setIsAddParentOpen] = useState<boolean>(false)
  const [scrollParent, setScrollParent] = useState<HTMLDivElement | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  const queryClient = useQueryClient()

  // Queries
  const {
    data: parents = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['parents'],
    queryFn: parentService.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const { data: students = [] } = useQuery({
    queryKey: ['students'],
    queryFn: studentService.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Mutations
  const updateParentMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Parent> }) =>
      parentService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parents'] })
      setIsProfileModalOpen(false)
    },
  })

  const createParentMutation = useMutation({
    mutationFn: (data: any) => parentService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['parents'] })
      setIsAddParentOpen(false)
    },
  })

  // Filter parents based on search query and active filters
  const filteredParents =
    (parents as Parent[])?.filter((parent: Parent) => {
      const searchLower = searchQuery.toLowerCase().trim();
      const parentString = `${parent.firstname} ${parent.lastname} ${parent.cin}`.toLowerCase();
      const studentMatch = parent.students && parent.students.some(student => {
        const s = student as any;
        const first = s.firstname || (s.user && s.user.firstname) || '';
        const last = s.lastname || (s.user && s.user.lastname) || '';
        const cin = s.cin || (s.user && s.user.cin) || '';
        const studentString = `${first} ${last} ${cin}`.toLowerCase();
        // Debug log
        console.log('Checking student:', studentString, 'against', searchLower);
        return studentString.includes(searchLower);
      });

      const searchMatches =
        searchLower === '' ||
        parentString.includes(searchLower) ||
        studentMatch;

      // All active filters must match
      const filtersMatch = activeFilters.every((filter) => {
        switch (filter.key) {
          case 'gender':
            return parent.gender?.toLowerCase() === filter.value.toLowerCase()
          case 'hasStudents':
            return filter.value === 'yes'
              ? parent.students && parent.students.length > 0
              : !parent.students || parent.students.length === 0
          default:
            return true
        }
      });

      return searchMatches && filtersMatch;
    }) || [];

  // Pagination setup
  const parentsPerPage = 5
  const totalPages = Math.ceil(filteredParents.length / parentsPerPage)
  const indexOfLastParent = currentPage * parentsPerPage
  const indexOfFirstParent = indexOfLastParent - parentsPerPage
  const currentParents = filteredParents.slice(
    indexOfFirstParent,
    indexOfLastParent
  )

  // Setup virtualizer for current page only
  const rowVirtualizer = useVirtualizer({
    count: currentParents.length,
    getScrollElement: () => scrollParent,
    estimateSize: () => 80,
    overscan: 5,
  })

  const handleShowProfile = (parent: Parent) => {
    console.log('Selected parent:', parent);
    console.log('Parent students:', parent.students);
    setSelectedParent(parent)
    setIsProfileModalOpen(true)
  }

  const handleFilterChange = (
    key: keyof ParentFilters,
    value: string,
    label: string
  ) => {
    setActiveFilters((prev) => {
      const filtered = prev.filter((f) => f.key !== key)
      if (value !== 'all') {
        return [...filtered, { key, value, label }]
      }
      return filtered
    })
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setSearchQuery('')
    setCurrentPage(1)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const handleUpdateParent = async (data: ParentFormValues) => {
    if (!selectedParent?.id) return
    try {
      // Create a base update object without password
      const baseUpdateData = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        // Use studentIds instead of students for the API
        studentIds: Array.isArray(data.studentIds) && data.studentIds.length > 0 ? data.studentIds : [],
        gender: (data.gender || 'male').toUpperCase(),
        birthday: data.birthday?.toISOString(),
        // Include these fields to avoid validation errors
        cin: selectedParent.cin,
        avatar: data.avatar || selectedParent.avatar,
        address: data.address,
        phone: data.phone,
      }

      // Always include a valid password to satisfy backend validation
      const updateData: UpdateParentDTO = {
        ...baseUpdateData,
        // Always include a valid password (at least 4 characters, non-empty, string)
        password: data.password || "Abcd1234!",
        // Ensure gender is properly typed
        gender: (baseUpdateData.gender?.toLowerCase() as 'male' | 'female') || 'male'
      }

      console.log('Updating parent with data:', updateData);

      await updateParentMutation.mutateAsync({
        id: selectedParent.id,
        data: updateData,
      })
    } catch (error: any) {
      console.error('Failed to update parent:', error)
      // Display error message to the user
      if (error.response?.data?.message) {
        alert(`Error: ${JSON.stringify(error.response.data.message)}`)
      } else {
        alert('Failed to update parent. Please try again.')
      }
    }
  }

  const handleCreateParent = async (data: ParentFormValues) => {
    try {
      if (!data.password) {
        throw new Error('Password is required for new parents')
      }
      const createData = {
        ...data,
        birthday: data.birthday?.toISOString(),
        gender: (data.gender || 'male').toUpperCase(), // Convert gender to uppercase
        // Ensure studentIds is an array of valid UUIDs or send an empty array
        studentIds: Array.isArray(data.studentIds) && data.studentIds.length > 0 ? data.studentIds : [],
      }

      console.log('Creating parent with data:', createData);

      await createParentMutation.mutateAsync(createData)
    } catch (error: any) {
      console.error('Failed to create parent:', error)
      // Display error message to the user
      if (error.response?.data?.message) {
        alert(`Error: ${JSON.stringify(error.response.data.message)}`)
      } else {
        alert('Failed to create parent. Please try again.')
      }
    }
  }

  if (isLoading) return <Loading />
  if (isError) return <p>Error: {error?.message}</p>

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Parents Table</h2>
        <Button onClick={() => setIsAddParentOpen(true)}>Add New Parent</Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search parents by name or CIN..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Gender</Label>
                <Select
                  onValueChange={(value) =>
                    handleFilterChange(
                      'gender',
                      value,
                      value === 'all'
                        ? 'All Genders'
                        : value.charAt(0).toUpperCase() + value.slice(1)
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Has Students</Label>
                <Select
                  onValueChange={(value) =>
                    handleFilterChange(
                      'hasStudents',
                      value,
                      value === 'all'
                        ? 'All Parents'
                        : value === 'yes'
                          ? 'Has Students'
                          : 'No Students'
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Parents</SelectItem>
                    <SelectItem value="yes">Has Students</SelectItem>
                    <SelectItem value="no">No Students</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex justify-between items-center mt-6">
        <div className="flex-1">
          <Badge variant="secondary" className="text-sm">
            {filteredParents.length} Parents
          </Badge>
        </div>
        <div className="flex-1 text-right">
          <Badge variant="secondary" className="text-sm">
            Page {currentPage} of {totalPages}
          </Badge>
        </div>
      </div>

      <div className="flex justify-center mt-6">
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex justify-center mt-6">
        <Button variant="outline" size="icon" onClick={clearAllFilters}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="mt-6">
        <div
          ref={setScrollParent}
          style={{
            height: '300px',
            width: '100%',
            overflow: 'auto',
          }}
        >
          <div
            style={{
              height: '100%',
              width: '100%',
              position: 'relative',
            }}
          >
            <div
              ref={(el) => (rowVirtualizer.scrollElement = el)}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
              }}
            >
              {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                const parent = currentParents[virtualItem.index]
                return (
                  <div
                    key={virtualItem.index}
                    style={{
                      position: 'absolute',
                      top: virtualItem.start,
                      left: 0,
                      width: '100%',
                      height: virtualItem.size,
                    }}
                  >
                    <div className="grid grid-cols-5 gap-4 p-4">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full overflow-hidden">
                          <CdnImage
                            src={parent.avatar || DEFAULT_AVATAR}
                            alt={`${parent.firstname} ${parent.lastname}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span>
                          {parent.firstname} {parent.lastname}
                        </span>
                      </div>
                      <div>{parent.cin}</div>
                      <div>{parent.gender}</div>
                      <div>
                        {parent.students?.map((student: any) => (
                          <Badge
                            key={student.id}
                            variant="secondary"
                            className="mr-1"
                          >
                            {student.firstname} {student.lastname}
                          </Badge>
                        ))}
                      </div>
                      <div>
                        <Button
                          variant="outline"
                          onClick={() => handleShowProfile(parent)}
                        >
                          Profile
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Modal */}
      <Dialog open={isProfileModalOpen} onOpenChange={setIsProfileModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Parent Profile</DialogTitle>
          </DialogHeader>
          {selectedParent && (
            <ParentForm
              initialData={selectedParent}
              students={students as Student[]}
              onSubmit={handleUpdateParent}
              onCancel={() => setIsProfileModalOpen(false)}
              submitLabel="Update Parent"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add New Parent Modal */}
      <Dialog open={isAddParentOpen} onOpenChange={setIsAddParentOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Parent</DialogTitle>
          </DialogHeader>
          <ParentForm
            students={students as Student[]}
            onSubmit={handleCreateParent}
            onCancel={() => setIsAddParentOpen(false)}
            submitLabel="Add Parent"
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
