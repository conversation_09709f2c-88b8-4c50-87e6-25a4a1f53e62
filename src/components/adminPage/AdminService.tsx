import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import { services } from '../../mockData/AdminServiceData'
import { QuickTimetableView } from './dashboard/QuickTimetableView'
import { useEffect } from 'react'
import { AdminTourButton, TimetableTourManager } from './timetable'

export default function AdminService() {
  // Check if we should trigger the admin timetable tour
  useEffect(() => {
    const userOnboarding = localStorage.getItem('userOnboarding') === 'true'
    const tourCompleted =
      localStorage.getItem('tour_completed_admin_timetable') === 'true'
    const tourSkipped =
      localStorage.getItem('tour_skipped_admin_timetable') === 'true'

    console.log('Admin Service: Checking if tour should be triggered', {
      userOnboarding,
      tourCompleted,
      tourSkipped,
    })

    // If this is a new user who needs onboarding, set the trigger flag
    if (userOnboarding && !tourCompleted && !tourSkipped) {
      console.log('Admin Service: Setting trigger flag for tour')
      localStorage.setItem('trigger_admin_timetable_tour', 'true')

      // Dispatch event to start the tour
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))
      }, 1000)
    }
  }, [])

  return (
    <div className="flex flex-col max-w-5xl mx-auto p-6 space-y-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold admin-dashboard-header">Services</h2>
        <AdminTourButton
          variant="outline"
          className="flex items-center gap-2"
        />
      </div>

      <section>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {services.map((service, index) => (
            <Card
              key={index}
              className={`${
                service.name === 'TimeTable'
                  ? 'timetable-card service-card-1'
                  : service.name === 'Data Storage'
                    ? 'data-storage-card service-card-2'
                    : `service-card-${index}`
              } hover:bg-muted/50 transition-colors relative`}
            >
              <CardContent className="p-4">
                <a
                  href={service.href}
                  className="flex flex-col items-center text-center space-y-2"
                  data-tour={
                    service.name === 'TimeTable' ? 'timetable-link' : ''
                  }
                  onClick={(e) => {
                    if (service.href.startsWith('#')) {
                      e.preventDefault()
                      // Stay on the same page when clicking on services with # links
                    }
                  }}
                >
                  {service.name === 'Elimination' ||
                  service.name === 'Score' ? (
                    <div className="flex flex-col items-center">
                      <span className="absolute top-0 right-0 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-bl-md rounded-tr-md z-10">
                        Soon
                      </span>
                      {service.icon}
                    </div>
                  ) : (
                    service.icon
                  )}
                  <span className="text-sm font-medium">{service.name}</span>
                </a>
              </CardContent>
            </Card>
          ))}
          <Card className="hover:bg-muted/50 transition-colors">
            <CardContent className="p-4">
              <Button
                variant="ghost"
                className="w-full h-full flex flex-col items-center space-y-2"
              ></Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Quick Timetable View */}
      <section className="admin-dashboard-stats">
        <h2 className="text-2xl font-bold mb-4">Quick Stats</h2>
        <QuickTimetableView />
      </section>

      {/* Quick Actions Section */}
      <section className="admin-dashboard-actions">
        <h2 className="text-2xl font-bold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <Button variant="outline" className="p-4 h-auto flex flex-col gap-2">
            <span>Add New Student</span>
          </Button>
          <Button variant="outline" className="p-4 h-auto flex flex-col gap-2">
            <span>Add New Teacher</span>
          </Button>
          <Button variant="outline" className="p-4 h-auto flex flex-col gap-2">
            <span>Generate Report</span>
          </Button>
        </div>
      </section>

      {/* Include the TimetableTourManager component */}
      <TimetableTourManager />
    </div>
  )
}
