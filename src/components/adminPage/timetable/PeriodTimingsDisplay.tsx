import { useState, useEffect } from 'react'

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Clock, Plus, Trash2, Wand2 } from 'lucide-react'
import { v4 as uuidv4 } from 'uuid'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface PeriodTiming {
  id: string
  periodNumber: number
  startTime: string
  endTime: string
  isBreak: boolean
}

interface PeriodTimingsDisplayProps {
  timetableData: any
  handleInputChange: (field: string, value: any) => void
}

export function PeriodTimingsDisplay({
  timetableData,
  handleInputChange,
}: PeriodTimingsDisplayProps) {
  const [periodCount, setPeriodCount] = useState<number>(
    timetableData.periodCount || 8
  )
  const [periodTimings, setPeriodTimings] = useState<PeriodTiming[]>(
    timetableData.periodTimings || []
  )
  const [showAutoGenerateDialog, setShowAutoGenerateDialog] = useState(false)
  const [autoGenSettings, setAutoGenSettings] = useState({
    startTime: timetableData.startTime || '08:00',
    endTime: timetableData.endTime || '17:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    periodLength: timetableData.periodLength || 45,
  })

  // Update parent component when period timings change
  useEffect(() => {
    // Update the periodTimings array for UI display
    handleInputChange('periodTimings', periodTimings)
    handleInputChange('periodCount', periodCount)

    // Format data for the server in the required format
    const schoolDays = timetableData.schoolDays || [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
    ]

    // Create the timeSlots object in the format expected by the server
    const timeSlots: Record<
      string,
      { studyTimes: string[]; restTime: string }
    > = {}

    // Initialize each day with empty study times
    schoolDays.forEach((day: string) => {
      timeSlots[day] = {
        studyTimes: [],
        restTime: '',
      }
    })

    // Format time as "8 => 9" instead of "08:00 => 09:00"
    const formatTimeForServer = (time: string) => {
      if (!time) return ''

      const [hours, minutes] = time.split(':').map(Number)
      // If minutes are 0, just return the hour
      return minutes === 0 ? hours.toString() : time
    }

    // Add regular periods to each day
    const regularPeriods = periodTimings.filter((period) => !period.isBreak)
    const breakPeriods = periodTimings.filter((period) => period.isBreak)

    // Sort periods by number
    regularPeriods.sort((a, b) => a.periodNumber - b.periodNumber)

    // Add regular periods to each day's studyTimes
    schoolDays.forEach((day: string) => {
      timeSlots[day].studyTimes = regularPeriods
        .filter((period) => period.startTime && period.endTime)
        .map((period) => {
          const startFormatted = formatTimeForServer(period.startTime)
          const endFormatted = formatTimeForServer(period.endTime)
          return `${startFormatted} => ${endFormatted}`
        })
        .filter(
          (timeStr) =>
            timeStr !== ' => ' &&
            timeStr.split(' => ')[0] !== timeStr.split(' => ')[1]
        )
    })

    // Add break time if available (use the first break)
    if (breakPeriods.length > 0) {
      const firstBreak = breakPeriods[0]
      if (firstBreak.startTime && firstBreak.endTime) {
        const breakStartFormatted = formatTimeForServer(firstBreak.startTime)
        const breakEndFormatted = formatTimeForServer(firstBreak.endTime)
        const breakTimeStr = `${breakStartFormatted} => ${breakEndFormatted}`

        // Add the break time to each day
        schoolDays.forEach((day: string) => {
          timeSlots[day].restTime = breakTimeStr
        })
      }
    }

    // Update the timeslots in the parent component
    handleInputChange('timeslots', timeSlots)
  }, [periodTimings, periodCount, handleInputChange, timetableData.schoolDays])

  // Generate a new period timing
  const addNewPeriod = () => {
    const newPeriod: PeriodTiming = {
      id: uuidv4(),
      periodNumber: periodTimings.length + 1,
      startTime: '',
      endTime: '',
      isBreak: false,
    }
    setPeriodTimings([...periodTimings, newPeriod])
  }

  // Remove a period timing
  const removePeriod = (id: string) => {
    const updatedPeriods = periodTimings.filter((period) => period.id !== id)
    // Renumber periods
    const renumberedPeriods = updatedPeriods.map((period, index) => ({
      ...period,
      periodNumber: index + 1,
    }))
    setPeriodTimings(renumberedPeriods)
  }

  // Update a period timing
  const updatePeriod = (id: string, field: keyof PeriodTiming, value: any) => {
    const updatedPeriods = periodTimings.map((period) => {
      if (period.id === id) {
        return { ...period, [field]: value }
      }
      return period
    })
    setPeriodTimings(updatedPeriods)
  }

  // Auto-generate period timings
  const autoGeneratePeriods = () => {
    const { startTime, endTime, breakStartTime, breakEndTime, periodLength } =
      autoGenSettings

    // Convert times to minutes
    const convertToMinutes = (timeStr: string) => {
      const [hours, minutes] = timeStr.split(':').map(Number)
      return hours * 60 + (minutes || 0)
    }

    const startMinutes = convertToMinutes(startTime)
    const endMinutes = convertToMinutes(endTime)
    const breakStartMinutes = convertToMinutes(breakStartTime)
    const breakEndMinutes = convertToMinutes(breakEndTime)

    // Generate periods
    const newPeriods: PeriodTiming[] = []
    let currentMinutes = startMinutes
    let periodNumber = 1

    // Format times for UI display (HH:MM format)
    const formatTimeForUI = (minutes: number) => {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
    }

    // Add periods before break
    while (currentMinutes + periodLength <= breakStartMinutes) {
      const periodEndMinutes = currentMinutes + periodLength

      newPeriods.push({
        id: uuidv4(),
        periodNumber: periodNumber++,
        startTime: formatTimeForUI(currentMinutes),
        endTime: formatTimeForUI(periodEndMinutes),
        isBreak: false,
      })

      currentMinutes = periodEndMinutes
    }

    // Add break period
    newPeriods.push({
      id: uuidv4(),
      periodNumber: periodNumber++,
      startTime: breakStartTime,
      endTime: breakEndTime,
      isBreak: true,
    })

    // Add periods after break
    currentMinutes = breakEndMinutes
    while (
      currentMinutes + periodLength <= endMinutes &&
      newPeriods.length < periodCount
    ) {
      const periodEndMinutes = currentMinutes + periodLength

      newPeriods.push({
        id: uuidv4(),
        periodNumber: periodNumber++,
        startTime: formatTimeForUI(currentMinutes),
        endTime: formatTimeForUI(periodEndMinutes),
        isBreak: false,
      })

      currentMinutes = periodEndMinutes
    }

    setPeriodTimings(newPeriods)
    setPeriodCount(newPeriods.length)
    setShowAutoGenerateDialog(false)
  }

  // Update period count
  const handlePeriodCountChange = (value: number) => {
    setPeriodCount(value)

    // If we have fewer periods than the new count, add empty ones
    if (periodTimings.length < value) {
      const newPeriods = [...periodTimings]
      for (let i = periodTimings.length; i < value; i++) {
        newPeriods.push({
          id: uuidv4(),
          periodNumber: i + 1,
          startTime: '',
          endTime: '',
          isBreak: false,
        })
      }
      setPeriodTimings(newPeriods)
    }
    // If we have more periods than the new count, remove excess ones
    else if (periodTimings.length > value) {
      setPeriodTimings(periodTimings.slice(0, value))
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Clock className="mr-2 h-5 w-5" />
          Period Timings Configuration
        </CardTitle>
        <CardDescription>
          Configure the time slots for your school schedule
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-end gap-4">
          <div className="space-y-2 flex-1">
            <Label htmlFor="periodCount">Number of Periods Per Day</Label>
            <Input
              id="periodCount"
              type="number"
              min="1"
              max="20"
              value={periodCount}
              onChange={(e) =>
                handlePeriodCountChange(parseInt(e.target.value) || 1)
              }
            />
          </div>

          <Dialog
            open={showAutoGenerateDialog}
            onOpenChange={setShowAutoGenerateDialog}
          >
            <DialogTrigger asChild>
              <Button variant="outline" className="mb-0.5">
                <Wand2 className="mr-2 h-4 w-4" />
                Auto Generate
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Auto Generate Periods</DialogTitle>
                <DialogDescription>
                  Configure settings to automatically generate period timings
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startTime">School Start Time</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={autoGenSettings.startTime}
                      onChange={(e) =>
                        setAutoGenSettings({
                          ...autoGenSettings,
                          startTime: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endTime">School End Time</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={autoGenSettings.endTime}
                      onChange={(e) =>
                        setAutoGenSettings({
                          ...autoGenSettings,
                          endTime: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="breakStartTime">Break Start Time</Label>
                    <Input
                      id="breakStartTime"
                      type="time"
                      value={autoGenSettings.breakStartTime}
                      onChange={(e) =>
                        setAutoGenSettings({
                          ...autoGenSettings,
                          breakStartTime: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="breakEndTime">Break End Time</Label>
                    <Input
                      id="breakEndTime"
                      type="time"
                      value={autoGenSettings.breakEndTime}
                      onChange={(e) =>
                        setAutoGenSettings({
                          ...autoGenSettings,
                          breakEndTime: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="periodLength">Period Length (minutes)</Label>
                  <Input
                    id="periodLength"
                    type="number"
                    min="0"
                    max="90"
                    value={autoGenSettings.periodLength}
                    onChange={(e) =>
                      setAutoGenSettings({
                        ...autoGenSettings,
                        periodLength: parseInt(e.target.value) || 45,
                      })
                    }
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowAutoGenerateDialog(false)}
                >
                  Cancel
                </Button>
                <Button onClick={autoGeneratePeriods}>Generate Periods</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={addNewPeriod}>
            <Plus className="mr-2 h-4 w-4" />
            Add Period
          </Button>
        </div>

        <Alert className="bg-blue-50 border-blue-200">
          <AlertDescription>
            Configure your school's daily schedule. Add regular periods and
            breaks. The time format should be HH:MM (24-hour format).
          </AlertDescription>
        </Alert>

        <ScrollArea className="h-[400px] border rounded-md p-4">
          {periodTimings.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No period timings defined. Use the buttons above to add periods or
              auto-generate a schedule.
            </div>
          ) : (
            <div className="space-y-4">
              {periodTimings.map((period) => (
                <div
                  key={period.id}
                  className={`p-4 rounded-md border ${period.isBreak ? 'bg-amber-50' : 'bg-slate-50'}`}
                >
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center">
                      <span className="font-medium mr-2">
                        Period {period.periodNumber}
                      </span>
                      {period.isBreak ? (
                        <Badge
                          variant="secondary"
                          className="bg-amber-100 text-amber-800"
                        >
                          Break
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="bg-blue-50 text-blue-800"
                        >
                          Lesson
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removePeriod(period.id)}
                      className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`start-${period.id}`}>Start Time</Label>
                      <Input
                        id={`start-${period.id}`}
                        type="time"
                        value={period.startTime}
                        onChange={(e) =>
                          updatePeriod(period.id, 'startTime', e.target.value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`end-${period.id}`}>End Time</Label>
                      <Input
                        id={`end-${period.id}`}
                        type="time"
                        value={period.endTime}
                        onChange={(e) =>
                          updatePeriod(period.id, 'endTime', e.target.value)
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mt-4">
                    <Switch
                      id={`break-${period.id}`}
                      checked={period.isBreak}
                      onCheckedChange={(checked) =>
                        updatePeriod(period.id, 'isBreak', checked)
                      }
                    />
                    <Label htmlFor={`break-${period.id}`}>
                      This is a break period
                    </Label>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
