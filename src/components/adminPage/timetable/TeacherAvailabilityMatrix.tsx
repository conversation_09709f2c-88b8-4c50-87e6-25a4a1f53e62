import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Check, X } from 'lucide-react'

interface TeacherAvailabilityMatrixProps {
  teacher: {
    id: string
    name: string
    unavailableTimes?: Record<string, string[]>
  }
  isOpen: boolean
  onClose: () => void
  onSave: (
    teacherId: string,
    unavailableTimes: Record<string, string[]>
  ) => void
}

export function TeacherAvailabilityMatrix({
  teacher,
  isOpen,
  onClose,
  onSave,
}: TeacherAvailabilityMatrixProps) {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  const periods = Array.from({ length: 8 }, (_, i) => `Period ${i + 1}`)

  // Initialize availability matrix
  const [availability, setAvailability] = useState<
    Record<string, Record<string, boolean>>
  >({})

  // Initialize the availability matrix when the component mounts or teacher changes
  useEffect(() => {
    if (teacher) {
      const initialAvailability: Record<string, Record<string, boolean>> = {}

      days.forEach((day) => {
        initialAvailability[day] = {}
        periods.forEach((period) => {
          // Default to available (true)
          initialAvailability[day][period] = true
        })
      })

      // Mark unavailable times
      if (teacher.unavailableTimes) {
        Object.entries(teacher.unavailableTimes).forEach(([day, times]) => {
          if (initialAvailability[day]) {
            times.forEach((time) => {
              // Map time to period (simplified for example)
              const periodIndex = Math.floor(
                parseInt(time.split(' => ')[0]) - 8
              )
              if (periodIndex >= 0 && periodIndex < periods.length) {
                const period = `Period ${periodIndex + 1}`
                initialAvailability[day][period] = false
              }
            })
          }
        })
      }

      setAvailability(initialAvailability)
    }
  }, [teacher])

  // Toggle availability for a specific day and period
  const toggleAvailability = (day: string, period: string) => {
    setAvailability((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [period]: !prev[day][period],
      },
    }))
  }

  // Convert availability matrix back to unavailableTimes format
  const saveAvailability = () => {
    const unavailableTimes: Record<string, string[]> = {}

    // Initialize all days with empty arrays (even if they have no unavailable times)
    days.forEach((day) => {
      unavailableTimes[day] = []
    })

    // Add unavailable times for each day
    days.forEach((day) => {
      periods.forEach((period, index) => {
        if (!availability[day][period]) {
          // Convert period to time range (simplified)
          const startHour = 8 + index
          const endHour = startHour + 1
          unavailableTimes[day].push(`${startHour} => ${endHour}`)
        }
      })

      // We no longer remove empty days - we keep them as empty arrays
      // This ensures all days are included in the JSON even if they have no unavailable times
    })

    onSave(teacher.id, unavailableTimes)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Manage Availability for {teacher.name}</DialogTitle>
        </DialogHeader>

        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-100 rounded-sm flex items-center justify-center">
              <Check className="h-3 w-3 text-green-600" />
            </div>
            <span className="text-sm">Available</span>
          </div>

          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-100 rounded-sm flex items-center justify-center">
              <X className="h-3 w-3 text-red-600" />
            </div>
            <span className="text-sm">Time Off</span>
          </div>
        </div>

        <div className="border rounded-md overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/50">
                <th className="p-2 text-left font-medium text-sm">
                  Period/Day
                </th>
                {days.map((day) => (
                  <th key={day} className="p-2 text-center font-medium text-sm">
                    {day}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {periods.map((period, i) => (
                <tr key={period} className={i % 2 === 0 ? 'bg-muted/20' : ''}>
                  <td className="p-2 text-sm font-medium">{period}</td>
                  {days.map((day) => (
                    <td key={`${day}-${period}`} className="p-2 text-center">
                      <button
                        className={`w-8 h-8 rounded-sm flex items-center justify-center ${
                          availability[day]?.[period]
                            ? 'bg-green-100 hover:bg-green-200'
                            : 'bg-red-100 hover:bg-red-200'
                        }`}
                        onClick={() => toggleAvailability(day, period)}
                      >
                        {availability[day]?.[period] ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <X className="h-4 w-4 text-red-600" />
                        )}
                      </button>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={saveAvailability}>Save Changes</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
