import { useState, useEffect } from 'react'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  GraduationCap,
  BookOpen,
  Calendar,
  ArrowRight,
} from 'lucide-react'
// Empty schedule result object
import { classService } from '@/lib/api/services/class-service'
import { teacherService } from '@/lib/api/services/teacher-service'
import { Skeleton } from '@/components/ui/skeleton'
import { api } from '@/lib/api/axios-instance'
import dashboardService from '@/lib/api/services/dashboard-service-new'
import { dashboardTimetableService } from '@/lib/api/services/dashboard-timetable-service'

// Define types for our activity data
interface Activity {
  id: string
  type: 'timetable' | 'class' | 'teacher'
  title: string
  date: string
  status: 'DELETE' | 'UPDATE' | 'CREATE'
}

// Define Timetable interface
interface Timetable {
  id: string
  description?: string
  academicYear?: string
  isActive: boolean
  updatedAt: string
  data: {
    scheduleData?: Array<{
      class: string
      [key: string]: any
    }>
    days?: any[]
    timeSlots?: any[]
    entries?: any[]
  }
}

export function Overview() {
  const [schoolStats, setSchoolStats] = useState({
    students: 0,
    teachers: 0,
    classes: 0,
    subjects: 0,
    rooms: 0,
    timetables: 0,
  })

  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<Activity[]>([])
  const [selectedClass, setSelectedClass] = useState('Ti1')
  const [timetables, setTimetables] = useState<Timetable[]>([])

  // Navigate to timetable generator
  const handleGenerateNewTimetable = () => {
    // Find parent component and tell it to change the active component
    // This assumes the parent component (RouteComponent) has setActiveComponent function
    // We're using window to pass this event up to any parent that might be listening
    window.dispatchEvent(new CustomEvent('navigate-timetable-generator'))
  }

  // Navigate to timetable display
  const handleViewTimetable = () => {
    const activeTimetable = timetables.find((t) => t.isActive)
    if (
      timetables.length > 0 &&
      activeTimetable &&
      activeTimetable.data?.scheduleData &&
      Array.isArray(activeTimetable.data.scheduleData) &&
      activeTimetable.data.scheduleData.length > 0
    ) {
      // Create a custom event to tell the parent to show timetable display
      window.dispatchEvent(new CustomEvent('navigate-timetable-display'))
    } else {
      // If no timetable exists, redirect to generator instead
      window.dispatchEvent(new CustomEvent('navigate-timetable-generator'))
    }
  }

  // Fetch recent activity from API
  const fetchRecentActivity = async () => {
    try {
      // Try to fetch activity data from dashboard API
      try {
        const activities = await dashboardService.getRecentActivities(5)

        // Convert dashboard activities to our Activity format
        const formattedActivities = activities.map((activity) => {
          // Ensure the type is one of the allowed values
          let activityType: 'teacher' | 'class' | 'timetable' = 'timetable'
          if (activity.type.toLowerCase() === 'teacher') {
            activityType = 'teacher'
          } else if (activity.type.toLowerCase() === 'class') {
            activityType = 'class'
          }

          // Ensure the status is one of the allowed values
          let actionStatus: 'DELETE' | 'UPDATE' | 'CREATE' = 'UPDATE'
          if (activity.action === 'DELETE') {
            actionStatus = 'DELETE'
          } else if (activity.action === 'CREATE') {
            actionStatus = 'CREATE'
          }

          return {
            id: activity.id,
            type: activityType,
            title: `${activity.action} ${activity.entityName}`,
            date: new Date(activity.timestamp).toLocaleString(),
            status: actionStatus,
          }
        })

        setRecentActivity(formattedActivities)
      } catch (apiError) {
        console.error(
          'Error fetching recent activity from dashboard:',
          apiError
        )

        // Try fallback endpoint
        try {
          const response = await api.get('/activity/recent')
          if (response.data && Array.isArray(response.data)) {
            setRecentActivity(response.data)
          } else {
            throw new Error('Invalid activity data format')
          }
        } catch (fallbackError) {
          console.error('Error fetching from fallback endpoint:', fallbackError)
          setRecentActivity([])
        }
      }
    } catch (error) {
      console.error('Error in fetchRecentActivity:', error)
      setRecentActivity([])
    }
  }

  useEffect(() => {
    // Fetch school statistics
    const fetchSchoolStats = async () => {
      try {
        setLoading(true)

        // Fetch real data from the dashboard API
        try {
          // Get dashboard stats
          const dashboardStats = await dashboardService.getStats()

          // Get timetables
          const timetableData =
            await dashboardTimetableService.getCurrentEstablishmentTimetables()
          setTimetables(timetableData || [])

          // Set school stats with real data from the dashboard API
          setSchoolStats({
            students: dashboardStats.students?.total || 0,
            teachers: dashboardStats.teachers?.total || 0,
            classes: dashboardStats.classes?.total || 0,
            subjects: dashboardStats.subjects?.total || 0,
            rooms: dashboardStats.classrooms?.total || 0,
            timetables: timetableData?.length || 0,
          })

          console.log('Dashboard stats loaded:', dashboardStats)
          console.log('Timetables loaded:', timetableData?.length || 0)
        } catch (apiError) {
          console.error('Error fetching data from dashboard API:', apiError)

          // Fallback to direct API calls if dashboard API fails
          try {
            const classes = await classService.getAll()
            const teachers = await teacherService.getAll()

            // Get unique subjects from classes
            const subjectsSet = new Set<string>()
            classes.forEach((cls) => {
              if (cls.subjects && Array.isArray(cls.subjects)) {
                cls.subjects.forEach((subject) => subjectsSet.add(subject))
              }
            })

            // Count total students
            const studentCount = classes.reduce(
              (acc, cls) => acc + (cls.students?.length || 0),
              0
            )

            // Try to get timetables
            let timetableCount = 0
            try {
              const timetableData =
                await dashboardTimetableService.getCurrentEstablishmentTimetables()
              setTimetables(timetableData || [])
              timetableCount = timetableData?.length || 0
            } catch (timetableError) {
              console.error('Error fetching timetables:', timetableError)
            }

            // Try to get classrooms
            let roomCount = 0
            try {
              const response = await api.get('/classrooms')
              if (response.data && Array.isArray(response.data)) {
                roomCount = response.data.length
              }
            } catch (roomError) {
              console.error('Error fetching classrooms:', roomError)
              roomCount = 24 // Fallback value
            }

            // Set school stats with data from direct API calls
            setSchoolStats({
              students: studentCount,
              teachers: teachers.length,
              classes: classes.length,
              subjects: subjectsSet.size,
              rooms: roomCount,
              timetables: timetableCount,
            })
          } catch (fallbackError) {
            console.error('Error in fallback data fetching:', fallbackError)

            // If all attempts fail, set zeros for all values
            setSchoolStats({
              students: 0,
              teachers: 0,
              classes: 0,
              subjects: 0,
              rooms: 0,
              timetables: 0,
            })
          }
        }

        // Also fetch recent activity
        await fetchRecentActivity()

        setLoading(false)
      } catch (error) {
        console.error('Error fetching school statistics:', error)
        setLoading(false)
      }
    }

    fetchSchoolStats()
  }, [])

  // Get the schedule for the selected class from the active timetable
  const getClassSchedule = () => {
    if (!timetables.length || !timetables.find((t) => t.isActive)) {
      return {}
    }

    const activeTimetable = timetables.find((t) => t.isActive)
    const classSchedule = activeTimetable?.data?.scheduleData?.find(
      (data: any) => data.class === selectedClass
    )

    if (classSchedule) {
      const { class: _, ...scheduleWithoutClass } = classSchedule
      return scheduleWithoutClass
    }
    return {}
  }

  const classSchedule = getClassSchedule()

  // Get timetable utilization statistics
  const getTimetableStats = () => {
    let totalSlots = 0
    let filledSlots = 0

    // Count slots
    if (classSchedule && Object.keys(classSchedule).length > 0) {
      Object.keys(classSchedule).forEach((day) => {
        if (Array.isArray(classSchedule[day as keyof typeof classSchedule])) {
          const daySlots = classSchedule[
            day as keyof typeof classSchedule
          ] as any[]
          totalSlots += daySlots.length
          filledSlots += daySlots.filter((slot) => slot.subject).length
        }
      })
    } else if (timetables.length > 0 && timetables.find((t) => t.isActive)) {
      // If no class schedule is available but we have an active timetable,
      // calculate utilization across all classes
      const activeTimetable = timetables.find((t) => t.isActive)

      if (activeTimetable?.data?.scheduleData) {
        activeTimetable.data.scheduleData.forEach((classData: any) => {
          const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
          days.forEach((day) => {
            if (Array.isArray(classData[day])) {
              const daySlots = classData[day]
              totalSlots += daySlots.length
              filledSlots += daySlots.filter((slot: any) => slot.subject).length
            }
          })
        })
      }
    }

    return {
      utilizationRate:
        totalSlots > 0 ? Math.round((filledSlots / totalSlots) * 100) : 0,
      totalSlots,
      filledSlots,
    }
  }

  const timetableStats = getTimetableStats()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between timetable-header">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">School Dashboard</h2>
          <p className="text-gray-500">
            Overview of your school's timetable and resources
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="default"
            onClick={handleGenerateNewTimetable}
            className="create-timetable-button"
          >
            Generate New Timetable
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Students
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">{schoolStats.students}</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Teachers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-green-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">{schoolStats.teachers}</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Classes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-purple-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">{schoolStats.classes}</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Subjects
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-amber-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">{schoolStats.subjects}</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Classrooms
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-red-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">{schoolStats.rooms}</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Generated Timetables
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-indigo-500 mr-3" />
              {loading ? (
                <Skeleton className="h-10 w-16" />
              ) : (
                <div className="text-3xl font-bold">
                  {schoolStats.timetables}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timetable Overview and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Timetable Summary */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Current Timetable</CardTitle>
            <CardDescription>
              Overview of the current active timetable
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-32 w-full" />
              </div>
            ) : (
              <div className="space-y-5">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="text-sm font-semibold">
                      {timetables.length > 0 &&
                      timetables.find((t) => t.isActive)
                        ? timetables.find((t) => t.isActive)?.description ||
                          'Current Active Timetable'
                        : 'No Active Timetable'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {timetables.length > 0 &&
                      timetables.find((t) => t.isActive)
                        ? `Academic Year: ${timetables.find((t) => t.isActive)?.academicYear || 'Current'}`
                        : 'No timetable details available'}
                    </p>
                  </div>
                  <Badge
                    variant="outline"
                    className={
                      timetables.length > 0 &&
                      timetables.find((t) => t.isActive)
                        ? 'bg-green-100 text-green-800 hover:bg-green-100'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-100'
                    }
                  >
                    {timetables.length > 0 && timetables.find((t) => t.isActive)
                      ? 'Active'
                      : 'None'}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div>
                    <div className="text-sm font-medium">
                      Timetable Utilization
                    </div>
                    <div className="flex items-center space-x-2">
                      <Progress
                        value={timetableStats.utilizationRate}
                        className="h-2"
                      />
                      <div className="text-sm font-medium">
                        {timetableStats.utilizationRate}%
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {timetableStats.filledSlots} of{' '}
                      {timetableStats.totalSlots} slots used
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-xs text-gray-500">Classes</div>
                      <div className="text-xl font-bold">
                        {schoolStats.classes || 0}
                      </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-xs text-gray-500">Days</div>
                      <div className="text-xl font-bold">
                        {timetables.length > 0 &&
                        timetables.find((t) => t.isActive) &&
                        timetables.find((t) => t.isActive)?.data?.days?.length
                          ? timetables.find((t) => t.isActive)?.data?.days
                              ?.length
                          : 5}
                      </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-xs text-gray-500">Periods/Day</div>
                      <div className="text-xl font-bold">
                        {timetables.length > 0 &&
                        timetables.find((t) => t.isActive) &&
                        timetables.find((t) => t.isActive)?.data?.timeSlots
                          ?.length
                          ? timetables.find((t) => t.isActive)?.data?.timeSlots
                              ?.length
                          : '-'}
                      </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-xs text-gray-500">Last Updated</div>
                      <div className="text-sm font-medium">
                        {timetables.length > 0 &&
                        timetables.find((t) => t.isActive)
                          ? new Date(
                              timetables.find((t) => t.isActive)?.updatedAt ||
                                new Date()
                            ).toLocaleDateString()
                          : '-'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={handleViewTimetable}
            >
              View Full Timetable
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and changes</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-14 w-full" />
                <Skeleton className="h-14 w-full" />
                <Skeleton className="h-14 w-full" />
              </div>
            ) : (
              <div className="space-y-5">
                {recentActivity.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-start pb-4 last:pb-0 border-b last:border-0 border-gray-100"
                  >
                    <div
                      className={`flex-shrink-0 rounded-full p-1 mr-3 ${
                        activity.type === 'timetable'
                          ? 'bg-blue-100 text-blue-600'
                          : activity.type === 'class'
                            ? 'bg-purple-100 text-purple-600'
                            : 'bg-green-100 text-green-600'
                      }`}
                    >
                      {activity.type === 'timetable' ? (
                        <Calendar className="h-4 w-4" />
                      ) : activity.type === 'class' ? (
                        <Users className="h-4 w-4" />
                      ) : (
                        <GraduationCap className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-gray-500">{activity.date}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Conflict Detection Section */}
      <Card className="conflict-detection">
        <CardHeader>
          <CardTitle>Conflict Detection</CardTitle>
          <CardDescription>
            The system automatically detects scheduling conflicts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-800">
              No timetable data available.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Teacher Availability Section */}
      <Card className="teacher-availability">
        <CardHeader>
          <CardTitle>Teacher Availability</CardTitle>
          <CardDescription>
            View and set teacher availability for scheduling
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-sm text-gray-800">
                No teacher availability data available.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Access to Timetable Views */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Timetable View</CardTitle>
          <CardDescription>
            View the current timetable by class, teacher, or room
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              {/* Class Selection Dropdown */}
              <div className="flex items-center space-x-4">
                <div className="w-40">
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="w-full p-2 border rounded"
                    disabled={
                      !timetables.length || !timetables.find((t) => t.isActive)
                    }
                  >
                    {(() => {
                      const activeTimetable = timetables.find((t) => t.isActive)
                      const scheduleData = activeTimetable?.data?.scheduleData

                      if (
                        timetables.length > 0 &&
                        activeTimetable &&
                        scheduleData &&
                        Array.isArray(scheduleData) &&
                        scheduleData.length > 0
                      ) {
                        return scheduleData.map((data: any) => (
                          <option key={data.class} value={data.class}>
                            {data.class}
                          </option>
                        ))
                      } else {
                        return <option value="">No classes</option>
                      }
                    })()}
                  </select>
                </div>

                <Tabs defaultValue="student" className="flex-1">
                  <TabsList className="w-full justify-start">
                    <TabsTrigger value="student">Class View</TabsTrigger>
                    <TabsTrigger value="teacher">Teacher View</TabsTrigger>
                    <TabsTrigger value="room">Room View</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {/* Timetable preview or empty message */}
              {(() => {
                const activeTimetable = timetables.find((t) => t.isActive)
                const scheduleData = activeTimetable?.data?.scheduleData

                if (
                  timetables.length > 0 &&
                  activeTimetable &&
                  scheduleData &&
                  Array.isArray(scheduleData) &&
                  scheduleData.length > 0
                ) {
                  return (
                    <div className="border border-gray-200 rounded-md overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[100px]">Time</TableHead>
                            <TableHead>Monday</TableHead>
                            <TableHead>Tuesday</TableHead>
                            <TableHead>Wednesday</TableHead>
                            <TableHead>Thursday</TableHead>
                            <TableHead>Friday</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {/* Show just first two periods as preview */}
                          {[0, 1].map((periodIndex) => {
                            const activeTimetable = timetables.find(
                              (t) => t.isActive
                            )
                            const classData =
                              activeTimetable?.data?.scheduleData?.find(
                                (d: any) => d.class === selectedClass
                              )

                            return (
                              <TableRow key={periodIndex}>
                                <TableCell className="font-medium">
                                  {activeTimetable?.data?.timeSlots?.[
                                    periodIndex
                                  ]?.label || `Period ${periodIndex + 1}`}
                                </TableCell>
                                {[
                                  'Monday',
                                  'Tuesday',
                                  'Wednesday',
                                  'Thursday',
                                  'Friday',
                                ].map((day) => {
                                  const period = classData?.[day]?.[periodIndex]
                                  return (
                                    <TableCell
                                      key={day}
                                      className={
                                        period?.subject ? 'bg-blue-50' : ''
                                      }
                                    >
                                      {period?.subject ? (
                                        <div>
                                          <div className="font-medium">
                                            {period.subject}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {period.teacher}
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="text-xs text-gray-400">
                                          -
                                        </div>
                                      )}
                                    </TableCell>
                                  )
                                })}
                              </TableRow>
                            )
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  )
                } else {
                  return (
                    <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                      <p className="text-sm text-gray-800 text-center py-8">
                        No timetable data available. Generate a timetable to
                        view it here.
                      </p>
                    </div>
                  )
                }
              })()}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            variant="secondary"
            className="w-full export-options"
            onClick={handleViewTimetable}
          >
            View Complete Schedule
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>

      {/* Back to Dashboard Link */}
      <div className="flex justify-end mt-4">
        <a
          href="/admin"
          className="text-primary hover:underline back-to-dashboard"
        >
          Back to Dashboard
        </a>
      </div>
    </div>
  )
}
