import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import {
  Plus,
  Trash2,
  Loader2,
  Check,
  X,
  BookOpen,
  Calendar,
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { TeacherAvailabilityMatrix } from './TeacherAvailabilityMatrix'

interface Subject {
  id: string
  name: string
}

interface Teacher {
  id: string
  name: string
  subjects: string[]
  unavailableTimes?: Record<string, string[]>
}

interface TeachersConfigurationProps {
  teachers: Teacher[]
  subjects: Subject[]
  isLoading: boolean
  onAddTeacher: (teacher: Teacher) => void
  onDeleteTeacher: (teacherId: string) => void
  onUpdateTeacherAvailability?: (
    teacherId: string,
    unavailableTimes: Record<string, string[]>
  ) => void
}

export function TeachersConfiguration({
  teachers,
  subjects,
  isLoading,
  onAddTeacher,
  onDeleteTeacher,
  onUpdateTeacherAvailability,
}: TeachersConfigurationProps) {
  const { toast } = useToast()
  const [newTeacherName, setNewTeacherName] = useState('')
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([])
  const [editingTeacher, setEditingTeacher] = useState<string | null>(null)
  const [editSubjects, setEditSubjects] = useState<string[]>([])
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [isAvailabilityModalOpen, setIsAvailabilityModalOpen] = useState(false)

  const handleAddTeacher = () => {
    if (!newTeacherName.trim()) {
      toast({
        title: 'Error',
        description: 'Teacher name cannot be empty',
        variant: 'destructive',
      })
      return
    }

    // Check if teacher already exists
    if (
      teachers.some(
        (t) => t.name.toLowerCase() === newTeacherName.trim().toLowerCase()
      )
    ) {
      toast({
        title: 'Error',
        description: 'A teacher with this name already exists',
        variant: 'destructive',
      })
      return
    }

    // Generate a unique ID for the new teacher
    const newTeacherId = `teacher_${Date.now()}`

    // Add the new teacher with empty unavailableTimes
    onAddTeacher({
      id: newTeacherId,
      name: newTeacherName.trim(),
      subjects: selectedSubjects,
      unavailableTimes: {
        Monday: [],
        Tuesday: [],
        Wednesday: [],
        Thursday: [],
        Friday: [],
        Saturday: [],
      },
    })

    // Reset the input fields
    setNewTeacherName('')
    setSelectedSubjects([])

    toast({
      title: 'Teacher Added',
      description: `${newTeacherName.trim()} has been added to the teachers list`,
    })
  }

  const handleEditStart = (teacher: Teacher) => {
    setEditingTeacher(teacher.id)
    setEditSubjects([...teacher.subjects])
  }

  const handleEditSave = (teacherId: string) => {
    const updatedTeachers = teachers.map((teacher) =>
      teacher.id === teacherId
        ? { ...teacher, subjects: editSubjects }
        : teacher
    )

    // Update the teachers list in the parent component
    onDeleteTeacher(teacherId)
    const updatedTeacher = updatedTeachers.find((t) => t.id === teacherId)
    if (updatedTeacher) {
      onAddTeacher(updatedTeacher)
    }

    setEditingTeacher(null)

    toast({
      title: 'Teacher Updated',
      description: "Teacher's subjects have been updated",
    })
  }

  const handleEditCancel = () => {
    setEditingTeacher(null)
    setEditSubjects([])
  }

  const handleEditSubjectToggle = (subjectId: string) => {
    if (editSubjects.includes(subjectId)) {
      setEditSubjects(editSubjects.filter((id) => id !== subjectId))
    } else {
      setEditSubjects([...editSubjects, subjectId])
    }
  }

  const toggleSubjectSelection = (subjectId: string) => {
    if (selectedSubjects.includes(subjectId)) {
      setSelectedSubjects(selectedSubjects.filter((id) => id !== subjectId))
    } else {
      setSelectedSubjects([...selectedSubjects, subjectId])
    }
  }

  const getSubjectName = (subjectId: string) => {
    return subjects.find((s) => s.id === subjectId)?.name || 'Unknown'
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Teachers Configuration</CardTitle>
        <CardDescription>
          Configure the teachers and their subjects for the timetable
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="new-teacher">Teacher Name</Label>
            <Input
              id="new-teacher"
              placeholder="Enter teacher name (e.g., John Smith)"
              value={newTeacherName}
              onChange={(e) => setNewTeacherName(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="teacher-subjects">Subjects</Label>
            <div className="space-y-2">
              <Select>
                <SelectTrigger id="teacher-subjects" className="w-full">
                  <SelectValue placeholder="Select subjects this teacher can teach" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem
                      key={subject.id}
                      value={subject.id}
                      onSelect={() => toggleSubjectSelection(subject.id)}
                    >
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex flex-wrap gap-2 mt-2">
                {selectedSubjects.map((subjectId, index) => (
                  <Badge
                    key={`new-${subjectId}-${index}`}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {getSubjectName(subjectId)}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => toggleSubjectSelection(subjectId)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <Button onClick={handleAddTeacher} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-1" />
            Add Teacher
          </Button>
        </div>

        <Separator />

        {isLoading ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading teachers...</span>
          </div>
        ) : teachers.length === 0 ? (
          <div className="text-center p-4 border rounded-md bg-muted/20">
            No teachers defined yet. Add your first teacher using the form
            above.
          </div>
        ) : (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Teacher Name</TableHead>
                  <TableHead>Subjects</TableHead>
                  <TableHead className="w-[150px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachers.map((teacher) => (
                  <TableRow key={teacher.id}>
                    <TableCell>{teacher.name}</TableCell>
                    <TableCell>
                      {editingTeacher === teacher.id ? (
                        <div className="space-y-2">
                          <Select>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select subjects" />
                            </SelectTrigger>
                            <SelectContent>
                              {subjects.map((subject) => (
                                <SelectItem
                                  key={subject.id}
                                  value={subject.id}
                                  onSelect={() =>
                                    handleEditSubjectToggle(subject.id)
                                  }
                                >
                                  {subject.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <div className="flex flex-wrap gap-2">
                            {editSubjects.map((subjectId, index) => (
                              <Badge
                                key={`edit-${teacher.id}-${subjectId}-${index}`}
                                variant="secondary"
                                className="flex items-center gap-1"
                              >
                                {getSubjectName(subjectId)}
                                <X
                                  className="h-3 w-3 cursor-pointer"
                                  onClick={() =>
                                    handleEditSubjectToggle(subjectId)
                                  }
                                />
                              </Badge>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {teacher.subjects.length > 0 ? (
                            teacher.subjects.map((subjectId, index) => (
                              <Badge
                                key={`${teacher.id}-${subjectId}-${index}`}
                                variant="outline"
                              >
                                {getSubjectName(subjectId)}
                              </Badge>
                            ))
                          ) : (
                            <span className="text-muted-foreground">
                              No subjects assigned
                            </span>
                          )}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        {editingTeacher === teacher.id ? (
                          <>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditSave(teacher.id)}
                              title="Save Changes"
                            >
                              <Check className="h-4 w-4 text-green-500" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={handleEditCancel}
                              title="Cancel"
                            >
                              <X className="h-4 w-4 text-muted-foreground" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditStart(teacher)}
                              title="Edit Subjects"
                            >
                              <BookOpen className="h-4 w-4 text-blue-500" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedTeacher(teacher)
                                setIsAvailabilityModalOpen(true)
                              }}
                              title="Manage Availability"
                            >
                              <Calendar className="h-4 w-4 text-green-500" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => onDeleteTeacher(teacher.id)}
                              title="Delete Teacher"
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Teacher Availability Matrix */}
      {selectedTeacher && (
        <TeacherAvailabilityMatrix
          teacher={selectedTeacher}
          isOpen={isAvailabilityModalOpen}
          onClose={() => setIsAvailabilityModalOpen(false)}
          onSave={(teacherId, unavailableTimes) => {
            if (onUpdateTeacherAvailability) {
              onUpdateTeacherAvailability(teacherId, unavailableTimes)
            }
            setIsAvailabilityModalOpen(false)
            toast({
              title: 'Availability Updated',
              description: `${selectedTeacher.name}'s availability has been updated.`,
            })
          }}
        />
      )}
    </Card>
  )
}
