import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Plus, Trash2, Loader2 } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface Subject {
  id: string
  name: string
  color?: string
}

interface SubjectsConfigurationProps {
  subjects: Subject[]
  isLoading?: boolean
  onAddSubject: (subject: Subject) => void
  onDeleteSubject: (subjectId: string) => void
}

export function SubjectsConfiguration({
  subjects,
  isLoading = false,
  onAddSubject,
  onDeleteSubject,
}: SubjectsConfigurationProps) {
  const { toast } = useToast()
  const [newSubjectName, setNewSubjectName] = useState('')
  const [newSubjectColor, setNewSubjectColor] = useState(getRandomColor())

  // Function to generate a random color
  function getRandomColor() {
    const colors = [
      '#FF5733',
      '#33FF57',
      '#3357FF',
      '#F033FF',
      '#FF33F0',
      '#33FFF0',
      '#F0FF33',
      '#FF3333',
      '#33FF33',
      '#3333FF',
      '#FFAA33',
      '#33FFAA',
      '#AA33FF',
      '#FF33AA',
      '#33AAFF',
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const handleAddSubject = () => {
    if (!newSubjectName.trim()) {
      toast({
        title: 'Error',
        description: 'Subject name cannot be empty',
        variant: 'destructive',
      })
      return
    }

    // Check if subject already exists
    if (
      subjects.some(
        (subject) =>
          subject.name.toLowerCase() === newSubjectName.trim().toLowerCase()
      )
    ) {
      toast({
        title: 'Error',
        description: 'This subject already exists',
        variant: 'destructive',
      })
      return
    }

    // Generate a unique ID using timestamp
    const newSubject: Subject = {
      id: `subject_${Date.now()}`,
      name: newSubjectName.trim(),
      color: newSubjectColor,
    }

    onAddSubject(newSubject)
    setNewSubjectName('')
    // Generate a new random color for the next subject
    setNewSubjectColor(getRandomColor())

    toast({
      title: 'Subject Added',
      description: `${newSubjectName} has been added to the subjects list`,
    })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddSubject()
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Subjects Configuration</CardTitle>
        <CardDescription>
          Define the subjects or courses that will be taught in your timetable
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-end gap-4">
          <div className="flex-1">
            <Label htmlFor="subject-name">Subject Name</Label>
            <Input
              id="subject-name"
              placeholder="e.g., Mathematics"
              value={newSubjectName}
              onChange={(e) => setNewSubjectName(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={isLoading}
            />
          </div>
          <div className="w-24">
            <Label htmlFor="subject-color">Color</Label>
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 rounded-full border border-gray-300 cursor-pointer overflow-hidden relative"
                style={{ backgroundColor: newSubjectColor }}
                onClick={() =>
                  document.getElementById('subject-color')?.click()
                }
              >
                <input
                  type="color"
                  id="subject-color"
                  value={newSubjectColor}
                  onChange={(e) => setNewSubjectColor(e.target.value)}
                  className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
                />
              </div>
              <div className="text-xs text-gray-500">
                {newSubjectColor.toUpperCase()}
              </div>
            </div>
          </div>
          <Button
            onClick={handleAddSubject}
            disabled={isLoading || !newSubjectName.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Subject
          </Button>
        </div>

        <Separator />

        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <p>Loading subjects...</p>
          </div>
        ) : subjects.length === 0 ? (
          <div className="text-center p-4 border rounded-md bg-muted/20">
            No subjects defined yet. Add your first subject using the form
            above.
          </div>
        ) : (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Subject Name</TableHead>
                  <TableHead>Color</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {subjects.map((subject) => (
                  <TableRow key={subject.id}>
                    <TableCell>{subject.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-6 h-6 rounded-full border border-gray-300 cursor-pointer"
                          style={{
                            backgroundColor: subject.color || getRandomColor(),
                          }}
                          onClick={() => {
                            // Create a temporary input element to handle color picking
                            const input = document.createElement('input')
                            input.type = 'color'
                            input.value = subject.color || getRandomColor()
                            input.addEventListener('change', (e) => {
                              // Update the subject color
                              const newColor = (e.target as HTMLInputElement)
                                .value
                              const updatedSubject = {
                                ...subject,
                                color: newColor,
                              }
                              // Remove the old subject and add the updated one
                              onDeleteSubject(subject.id)
                              onAddSubject(updatedSubject)
                            })
                            input.click()
                          }}
                        />
                        <span className="text-xs text-gray-500">
                          {(subject.color || getRandomColor()).toUpperCase()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDeleteSubject(subject.id)}
                        title="Delete Subject"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4 text-sm text-muted-foreground">
              Total subjects: {subjects.length}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
