import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { AlertTriangle, ArrowRightLeft, Copy } from 'lucide-react'
import {
  validateTimetable,
  formatValidationResults,
} from '@/utils/timetableValidator'

interface TimetableComparerProps {
  initialTimetableData?: any
  className?: string
}

export function TimetableComparer({
  initialTimetableData,
  className,
}: TimetableComparerProps) {
  const [open, setOpen] = useState(false)
  const [inputData, setInputData] = useState('')
  const [outputData, setOutputData] = useState('')
  const [comparisonResults, setComparisonResults] = useState<string>('')
  const [activeTab, setActiveTab] = useState('input')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize with data if provided
  React.useEffect(() => {
    if (initialTimetableData) {
      setInputData(JSON.stringify(initialTimetableData, null, 2))
    }
  }, [])

  const handleCompare = () => {
    setIsLoading(true)
    setError(null)

    try {
      // Parse the input and output data
      const inputJson = JSON.parse(inputData)
      const outputJson = JSON.parse(outputData)

      // Log the data for debugging
      console.log('Input data:', inputJson)
      console.log('Output data:', outputJson)

      // Compare the data structures
      const results = compareTimetableData(inputJson, outputJson)

      // Set the results
      setComparisonResults(results)

      // Switch to the results tab
      setActiveTab('results')

      setIsLoading(false)
    } catch (error) {
      console.error('Error comparing timetable data:', error)
      setError(
        `Error comparing timetable data: ${error instanceof Error ? error.message : String(error)}`
      )
      setIsLoading(false)
    }
  }

  const handleSwap = () => {
    const temp = inputData
    setInputData(outputData)
    setOutputData(temp)
  }

  const handleCopyResults = () => {
    navigator.clipboard.writeText(comparisonResults)
  }

  const compareTimetableData = (inputData: any, outputData: any): string => {
    let results = '# Timetable Comparison Results\n\n'

    // Detect the format of the input and output data
    const isInputRawData =
      inputData.timeSlots && inputData.classes && inputData.teachers
    const isOutputSchedule =
      outputData.scheduleData && Array.isArray(outputData.scheduleData)

    results += `Input data format: ${isInputRawData ? 'Raw timetable data' : 'Unknown format'}\n`
    results += `Output data format: ${isOutputSchedule ? 'Schedule data' : 'Unknown format'}\n\n`

    if (!isInputRawData) {
      results +=
        '⚠️ Input data does not appear to be in the expected raw timetable format.\n\n'
    }

    if (!isOutputSchedule) {
      results +=
        '⚠️ Output data does not appear to be in the expected schedule format.\n\n'
    }

    // If we have the expected formats, proceed with the comparison
    if (isInputRawData && isOutputSchedule) {
      results += '## Input Data Summary\n\n'

      // Summarize classes
      const classes = inputData.classes || []
      results += `Classes: ${classes.length} (${classes.map((c: any) => c.className).join(', ')})\n`

      // Summarize teachers
      const teachers = inputData.teachers || []
      results += `Teachers: ${teachers.length}\n`

      // Summarize time slots
      const days = Object.keys(inputData.timeSlots || {})
      results += `Days: ${days.length} (${days.join(', ')})\n\n`

      // Summarize schedule data
      results += '## Output Schedule Summary\n\n'
      const scheduleData = outputData.scheduleData || []
      results += `Classes in schedule: ${scheduleData.length} (${scheduleData.map((s: any) => s.class).join(', ')})\n\n`

      // Compare time slots with schedule days
      results += '## Time Slots vs Schedule Days Comparison\n\n'

      const inputDays = Object.keys(inputData.timeSlots)
      const scheduleDays = new Set()

      // Extract days from schedule data
      outputData.scheduleData.forEach((classSchedule: any) => {
        Object.keys(classSchedule).forEach((key) => {
          if (key !== 'class') {
            scheduleDays.add(key)
          }
        })
      })

      const outputDaysArray = Array.from(scheduleDays) as string[]

      results += `Input has ${inputDays.length} days: ${inputDays.join(', ')}\n`
      results += `Output has ${outputDaysArray.length} days: ${outputDaysArray.join(', ')}\n\n`

      // Check for missing days
      const missingInOutputDays = inputDays.filter(
        (day) => !scheduleDays.has(day)
      )
      const missingInInputDays = outputDaysArray.filter(
        (day) => !inputDays.includes(day)
      )

      if (missingInOutputDays.length > 0) {
        results += `Days missing in output: ${missingInOutputDays.join(', ')}\n`
      }

      if (missingInInputDays.length > 0) {
        results += `Days missing in input: ${missingInInputDays.join(', ')}\n`
      }

      results += '\n'

      // Compare classes
      results += '## Classes Comparison\n\n'

      const inputClasses = inputData.classes.map((c: any) => c.className)
      const outputClasses = outputData.scheduleData.map((s: any) => s.class)

      results += `Input has ${inputClasses.length} classes\n`
      results += `Output has ${outputClasses.length} classes\n\n`

      // Check for missing classes
      const missingInOutputClasses = inputClasses.filter(
        (name: string) => !outputClasses.includes(name)
      )
      const missingInInputClasses = outputClasses.filter(
        (name: string) => !inputClasses.includes(name)
      )

      if (missingInOutputClasses.length > 0) {
        results += `Classes missing in output: ${missingInOutputClasses.join(', ')}\n`
      }

      if (missingInInputClasses.length > 0) {
        results += `Classes missing in input: ${missingInInputClasses.join(', ')}\n`
      }

      results += '\n'

      // Compare subjects for each class
      results += '## Subject Assignment Analysis\n\n'

      // Create a map of expected subject hours from input data
      const expectedSubjectHours = new Map()

      inputData.classes.forEach((classData: any) => {
        const className = classData.className
        classData.subjects.forEach((subject: any) => {
          const key = `${className}-${subject.subject}-${subject.teacherID}`
          expectedSubjectHours.set(key, subject.hours)
        })
      })

      // Count actual subject hours from schedule data
      const actualSubjectHours = new Map()

      outputData.scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class

        Object.entries(classSchedule).forEach(([day, slots]: [string, any]) => {
          if (day === 'class') return

          if (Array.isArray(slots)) {
            slots.forEach((slot: any) => {
              const key = `${className}-${slot.subject}-${slot.teacherID}`
              actualSubjectHours.set(
                key,
                (actualSubjectHours.get(key) || 0) + 1
              )
            })
          }
        })
      })

      // Compare expected vs actual hours
      results += '### Expected vs Actual Subject Hours\n\n'

      let unassignedSubjects = 0
      let overassignedSubjects = 0
      let correctlyAssignedSubjects = 0

      expectedSubjectHours.forEach((expectedHours, key) => {
        const actualHours = actualSubjectHours.get(key) || 0
        const [className, subject, teacherId] = key.split('-')

        if (actualHours < expectedHours) {
          results += `⚠️ ${className}: ${subject} (Teacher ${teacherId}) - Expected ${expectedHours} hours, got ${actualHours} hours\n`
          unassignedSubjects++
        } else if (actualHours > expectedHours) {
          results += `⚠️ ${className}: ${subject} (Teacher ${teacherId}) - Expected ${expectedHours} hours, got ${actualHours} hours (overassigned)\n`
          overassignedSubjects++
        } else {
          correctlyAssignedSubjects++
        }
      })

      // Check for subjects in schedule that weren't in input
      actualSubjectHours.forEach((hours, key) => {
        if (!expectedSubjectHours.has(key)) {
          const [className, subject, teacherId] = key.split('-')
          results += `⚠️ ${className}: ${subject} (Teacher ${teacherId}) - Not in input data but assigned ${hours} hours\n`
          overassignedSubjects++
        }
      })

      results += '\n'
      results += `Summary: ${correctlyAssignedSubjects} subjects correctly assigned, ${unassignedSubjects} subjects underassigned, ${overassignedSubjects} subjects overassigned\n\n`

      // Teacher workload analysis
      results += '## Teacher Workload Analysis\n\n'

      // Create a map of teacher constraints
      const teacherConstraints = new Map()

      inputData.teachers.forEach((teacher: any) => {
        teacherConstraints.set(teacher.teacherId, {
          name: teacher.teacherName,
          minimumHours: teacher.minimumHours || 0,
          maximumHours: teacher.maximumHours || 0,
          unavailableTimes: teacher.unavailableTimes || {},
        })
      })

      // Count actual teacher hours from schedule
      const teacherHours = new Map()

      outputData.scheduleData.forEach((classSchedule: any) => {
        Object.entries(classSchedule).forEach(([day, slots]: [string, any]) => {
          if (day === 'class') return

          if (Array.isArray(slots)) {
            slots.forEach((slot: any) => {
              const teacherId = slot.teacherID
              teacherHours.set(
                teacherId,
                (teacherHours.get(teacherId) || 0) + 1
              )
            })
          }
        })
      })

      // Compare with constraints
      let underworkedTeachers = 0
      let overworkedTeachers = 0
      let correctlyLoadedTeachers = 0

      teacherConstraints.forEach((constraints, teacherId) => {
        const actualHours = teacherHours.get(teacherId) || 0

        if (
          constraints.minimumHours > 0 &&
          actualHours < constraints.minimumHours
        ) {
          results += `⚠️ Teacher ${constraints.name} (ID: ${teacherId}) - Underworked: ${actualHours} hours (minimum: ${constraints.minimumHours})\n`
          underworkedTeachers++
        } else if (
          constraints.maximumHours > 0 &&
          actualHours > constraints.maximumHours
        ) {
          results += `⚠️ Teacher ${constraints.name} (ID: ${teacherId}) - Overworked: ${actualHours} hours (maximum: ${constraints.maximumHours})\n`
          overworkedTeachers++
        } else if (actualHours > 0) {
          correctlyLoadedTeachers++
        }
      })

      results += '\n'
      results += `Summary: ${correctlyLoadedTeachers} teachers with correct workload, ${underworkedTeachers} underworked, ${overworkedTeachers} overworked\n\n`

      // Check for teacher unavailable time conflicts
      results += '## Teacher Unavailable Time Conflicts\n\n'

      let unavailableTimeConflicts = 0

      outputData.scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class

        Object.entries(classSchedule).forEach(([day, slots]: [string, any]) => {
          if (day === 'class') return

          if (Array.isArray(slots)) {
            slots.forEach((slot: any) => {
              const teacherId = slot.teacherID
              const time = slot.time

              const teacherConstraint = teacherConstraints.get(teacherId)
              if (
                teacherConstraint &&
                teacherConstraint.unavailableTimes &&
                teacherConstraint.unavailableTimes[day] &&
                teacherConstraint.unavailableTimes[day].includes(time)
              ) {
                results += `⚠️ Teacher ${teacherConstraint.name} (ID: ${teacherId}) is scheduled during unavailable time: ${day} ${time} in class ${className} for ${slot.subject}\n`
                unavailableTimeConflicts++
              }
            })
          }
        })
      })

      if (unavailableTimeConflicts === 0) {
        results += 'No teacher unavailable time conflicts found.\n'
      } else {
        results += `\nTotal unavailable time conflicts: ${unavailableTimeConflicts}\n`
      }

      results += '\n'

      // Room conflict analysis
      results += '## Room Conflict Analysis\n\n'

      // Check for room conflicts (same room used by multiple classes at the same time)
      const roomSchedule = new Map()

      outputData.scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class

        Object.entries(classSchedule).forEach(([day, slots]: [string, any]) => {
          if (day === 'class') return

          if (Array.isArray(slots)) {
            slots.forEach((slot: any) => {
              if (slot.salle) {
                const key = `${day}-${slot.time}-${slot.salle}`

                if (!roomSchedule.has(key)) {
                  roomSchedule.set(key, [])
                }

                roomSchedule.get(key).push({
                  class: className,
                  subject: slot.subject,
                  teacher: slot.teacher,
                  teacherID: slot.teacherID,
                })
              }
            })
          }
        })
      })

      let roomConflicts = 0

      roomSchedule.forEach((assignments, key) => {
        if (assignments.length > 1) {
          const [day, time, room] = key.split('-')

          results += `⚠️ Room conflict in ${room} on ${day} at ${time}:\n`
          assignments.forEach((assignment: any) => {
            results += `  - Class ${assignment.class}, Subject: ${assignment.subject}, Teacher: ${assignment.teacher}\n`
          })
          results += '\n'

          roomConflicts++
        }
      })

      if (roomConflicts === 0) {
        results += 'No room conflicts found.\n'
      } else {
        results += `\nTotal room conflicts: ${roomConflicts}\n`
      }
    } else {
      // Basic structure comparison if formats are not as expected
      results += '## Data Structure Comparison\n\n'

      // Check top-level keys
      const inputKeys = Object.keys(inputData)
      const outputKeys = Object.keys(outputData)

      results += `Input data has ${inputKeys.length} top-level keys: ${inputKeys.join(', ')}\n`
      results += `Output data has ${outputKeys.length} top-level keys: ${outputKeys.join(', ')}\n\n`

      // Check for missing keys
      const missingInOutput = inputKeys.filter(
        (key) => !outputKeys.includes(key)
      )
      const missingInInput = outputKeys.filter(
        (key) => !inputKeys.includes(key)
      )

      if (missingInOutput.length > 0) {
        results += `Keys missing in output: ${missingInOutput.join(', ')}\n`
      }

      if (missingInInput.length > 0) {
        results += `Keys missing in input: ${missingInInput.join(', ')}\n`
      }

      results += '\n'
    }

    // Compare data field if it exists
    if (inputData.data && outputData.data) {
      results += '### Data Field Comparison\n\n'

      const inputDataKeys = Object.keys(inputData.data)
      const outputDataKeys = Object.keys(outputData.data)

      results += `Input data.data has ${inputDataKeys.length} keys: ${inputDataKeys.join(', ')}\n`
      results += `Output data.data has ${outputDataKeys.length} keys: ${outputDataKeys.join(', ')}\n\n`

      // Check for missing keys
      const missingInOutputData = inputDataKeys.filter(
        (key) => !outputDataKeys.includes(key)
      )
      const missingInInputData = outputDataKeys.filter(
        (key) => !inputDataKeys.includes(key)
      )

      if (missingInOutputData.length > 0) {
        results += `Keys missing in output.data: ${missingInOutputData.join(', ')}\n`
      }

      if (missingInInputData.length > 0) {
        results += `Keys missing in input.data: ${missingInInputData.join(', ')}\n`
      }

      results += '\n'

      // Compare timeSlots
      if (inputData.data.timeSlots && outputData.data.timeSlots) {
        results += '#### Time Slots Comparison\n\n'

        const inputDays = Object.keys(inputData.data.timeSlots)
        const outputDays = Object.keys(outputData.data.timeSlots)

        results += `Input has ${inputDays.length} days: ${inputDays.join(', ')}\n`
        results += `Output has ${outputDays.length} days: ${outputDays.join(', ')}\n\n`

        // Check for missing days
        const missingInOutputDays = inputDays.filter(
          (day) => !outputDays.includes(day)
        )
        const missingInInputDays = outputDays.filter(
          (day) => !inputDays.includes(day)
        )

        if (missingInOutputDays.length > 0) {
          results += `Days missing in output: ${missingInOutputDays.join(', ')}\n`
        }

        if (missingInInputDays.length > 0) {
          results += `Days missing in input: ${missingInInputDays.join(', ')}\n`
        }

        results += '\n'

        // Compare study times for each day
        const commonDays = inputDays.filter((day) => outputDays.includes(day))

        commonDays.forEach((day) => {
          const inputStudyTimes = inputData.data.timeSlots[day].studyTimes || []
          const outputStudyTimes =
            outputData.data.timeSlots[day].studyTimes || []

          results += `${day} study times:\n`
          results += `- Input: ${inputStudyTimes.length} slots (${inputStudyTimes.join(', ')})\n`
          results += `- Output: ${outputStudyTimes.length} slots (${outputStudyTimes.join(', ')})\n\n`

          // Check for missing study times
          const missingInOutputTimes = inputStudyTimes.filter(
            (time: string) => !outputStudyTimes.includes(time)
          )
          const missingInInputTimes = outputStudyTimes.filter(
            (time: string) => !inputStudyTimes.includes(time)
          )

          if (missingInOutputTimes.length > 0) {
            results += `Study times missing in output for ${day}: ${missingInOutputTimes.join(', ')}\n`
          }

          if (missingInInputTimes.length > 0) {
            results += `Study times missing in input for ${day}: ${missingInInputTimes.join(', ')}\n`
          }

          results += '\n'
        })
      }

      // Compare classes
      if (inputData.data.classes && outputData.data.classes) {
        results += '#### Classes Comparison\n\n'

        results += `Input has ${inputData.data.classes.length} classes\n`
        results += `Output has ${outputData.data.classes.length} classes\n\n`

        // Get class names
        const inputClassNames = inputData.data.classes.map(
          (c: any) => c.className
        )
        const outputClassNames = outputData.data.classes.map(
          (c: any) => c.className
        )

        // Check for missing classes
        const missingInOutputClasses = inputClassNames.filter(
          (name: string) => !outputClassNames.includes(name)
        )
        const missingInInputClasses = outputClassNames.filter(
          (name: string) => !inputClassNames.includes(name)
        )

        if (missingInOutputClasses.length > 0) {
          results += `Classes missing in output: ${missingInOutputClasses.join(', ')}\n`
        }

        if (missingInInputClasses.length > 0) {
          results += `Classes missing in input: ${missingInInputClasses.join(', ')}\n`
        }

        results += '\n'

        // Compare subjects for common classes
        const commonClasses = inputClassNames.filter((name: string) =>
          outputClassNames.includes(name)
        )

        if (commonClasses.length > 0) {
          results += '##### Subject Comparison for Common Classes\n\n'

          commonClasses.forEach((className: string) => {
            const inputClass = inputData.data.classes.find(
              (c: any) => c.className === className
            )
            const outputClass = outputData.data.classes.find(
              (c: any) => c.className === className
            )

            if (inputClass && outputClass) {
              results += `${className}:\n`
              results += `- Input: ${inputClass.subjects.length} subjects\n`
              results += `- Output: ${outputClass.subjects.length} subjects\n\n`

              // Compare subjects
              const inputSubjects = inputClass.subjects.map(
                (s: any) => `${s.subject}-${s.teacherID}`
              )
              const outputSubjects = outputClass.subjects.map(
                (s: any) => `${s.subject}-${s.teacherID}`
              )

              // Check for missing subjects
              const missingInOutputSubjects = inputSubjects.filter(
                (s: string) => !outputSubjects.includes(s)
              )
              const missingInInputSubjects = outputSubjects.filter(
                (s: string) => !inputSubjects.includes(s)
              )

              if (missingInOutputSubjects.length > 0) {
                results += `Subjects missing in output for ${className}: ${missingInOutputSubjects.join(', ')}\n`
              }

              if (missingInInputSubjects.length > 0) {
                results += `Subjects missing in input for ${className}: ${missingInInputSubjects.join(', ')}\n`
              }

              results += '\n'
            }
          })
        }
      }

      // Compare teachers
      if (inputData.data.teachers && outputData.data.teachers) {
        results += '#### Teachers Comparison\n\n'

        results += `Input has ${inputData.data.teachers.length} teachers\n`
        results += `Output has ${outputData.data.teachers.length} teachers\n\n`

        // Get teacher IDs
        const inputTeacherIds = inputData.data.teachers.map(
          (t: any) => t.teacherId
        )
        const outputTeacherIds = outputData.data.teachers.map(
          (t: any) => t.teacherId
        )

        // Check for missing teachers
        const missingInOutputTeachers = inputTeacherIds.filter(
          (id: string | number) => !outputTeacherIds.includes(id)
        )
        const missingInInputTeachers = outputTeacherIds.filter(
          (id: string | number) => !inputTeacherIds.includes(id)
        )

        if (missingInOutputTeachers.length > 0) {
          results += `Teachers missing in output: ${missingInOutputTeachers.join(', ')}\n`
        }

        if (missingInInputTeachers.length > 0) {
          results += `Teachers missing in input: ${missingInInputTeachers.join(', ')}\n`
        }

        results += '\n'
      }
    }

    // Compare schedule data
    const inputScheduleData =
      inputData.scheduleData || inputData.result?.scheduleData || []
    const outputScheduleData =
      outputData.scheduleData || outputData.result?.scheduleData || []

    if (inputScheduleData.length > 0 || outputScheduleData.length > 0) {
      results += '## Schedule Data Comparison\n\n'

      results += `Input has ${inputScheduleData.length} schedule items\n`
      results += `Output has ${outputScheduleData.length} schedule items\n\n`

      // Get class names from schedule
      const inputScheduleClasses = inputScheduleData.map((s: any) => s.class)
      const outputScheduleClasses = outputScheduleData.map((s: any) => s.class)

      // Check for missing classes in schedule
      const missingInOutputScheduleClasses = inputScheduleClasses.filter(
        (c: string) => !outputScheduleClasses.includes(c)
      )
      const missingInInputScheduleClasses = outputScheduleClasses.filter(
        (c: string) => !inputScheduleClasses.includes(c)
      )

      if (missingInOutputScheduleClasses.length > 0) {
        results += `Classes missing in output schedule: ${missingInOutputScheduleClasses.join(', ')}\n`
      }

      if (missingInInputScheduleClasses.length > 0) {
        results += `Classes missing in input schedule: ${missingInInputScheduleClasses.join(', ')}\n`
      }

      results += '\n'

      // Compare assignments for common classes
      const commonScheduleClasses = inputScheduleClasses.filter((c: string) =>
        outputScheduleClasses.includes(c)
      )

      if (commonScheduleClasses.length > 0) {
        results += '### Assignment Comparison for Common Classes\n\n'

        commonScheduleClasses.forEach((className: string) => {
          const inputClassSchedule = inputScheduleData.find(
            (s: any) => s.class === className
          )
          const outputClassSchedule = outputScheduleData.find(
            (s: any) => s.class === className
          )

          if (inputClassSchedule && outputClassSchedule) {
            results += `${className}:\n`

            // Get days
            const inputDays = Object.keys(inputClassSchedule).filter(
              (key) => key !== 'class'
            )
            const outputDays = Object.keys(outputClassSchedule).filter(
              (key) => key !== 'class'
            )

            results += `- Input days: ${inputDays.join(', ')}\n`
            results += `- Output days: ${outputDays.join(', ')}\n\n`

            // Check for missing days
            const missingInOutputDays = inputDays.filter(
              (day) => !outputDays.includes(day)
            )
            const missingInInputDays = outputDays.filter(
              (day) => !inputDays.includes(day)
            )

            if (missingInOutputDays.length > 0) {
              results += `Days missing in output for ${className}: ${missingInOutputDays.join(', ')}\n`
            }

            if (missingInInputDays.length > 0) {
              results += `Days missing in input for ${className}: ${missingInInputDays.join(', ')}\n`
            }

            results += '\n'

            // Compare assignments for common days
            const commonDays = inputDays.filter((day) =>
              outputDays.includes(day)
            )

            commonDays.forEach((day) => {
              const inputAssignments = inputClassSchedule[day] || []
              const outputAssignments = outputClassSchedule[day] || []

              results += `${className} - ${day}:\n`
              results += `- Input: ${inputAssignments.length} assignments\n`
              results += `- Output: ${outputAssignments.length} assignments\n\n`

              // Count assignments by subject
              const inputSubjectCounts =
                countAssignmentsBySubject(inputAssignments)
              const outputSubjectCounts =
                countAssignmentsBySubject(outputAssignments)

              results += `Input subjects: ${formatSubjectCounts(inputSubjectCounts)}\n`
              results += `Output subjects: ${formatSubjectCounts(outputSubjectCounts)}\n\n`

              // Check for differences in subject counts
              const allSubjects = new Set([
                ...Object.keys(inputSubjectCounts),
                ...Object.keys(outputSubjectCounts),
              ])

              let hasDifferences = false
              allSubjects.forEach((subject) => {
                const inputCount = inputSubjectCounts[subject] || 0
                const outputCount = outputSubjectCounts[subject] || 0

                if (inputCount !== outputCount) {
                  if (!hasDifferences) {
                    results += `Subject count differences for ${className} on ${day}:\n`
                    hasDifferences = true
                  }

                  results += `- ${subject}: Input=${inputCount}, Output=${outputCount}\n`
                }
              })

              if (hasDifferences) {
                results += '\n'
              }
            })
          }
        })
      }
    }

    // Run validation on both input and output
    try {
      results += '## Validation Results\n\n'

      // Prepare data for validation
      const prepareDataForValidation = (data: any) => {
        const inputData = data.data || data
        const scheduleData =
          data.scheduleData ||
          data.data?.scheduleData ||
          data.result?.scheduleData ||
          []

        // Flatten the schedule data
        const flatScheduleData = scheduleData.flatMap((classSchedule: any) => {
          const className = classSchedule.class
          return Object.entries(classSchedule)
            .filter(([key]) => key !== 'class')
            .flatMap(([day, slots]: [string, any]) => {
              if (!Array.isArray(slots)) return []
              return slots.map((slot: any) => ({
                class: className,
                day,
                ...slot,
              }))
            })
        })

        return { inputData, flatScheduleData }
      }

      // Validate input data if it has the required structure
      if (
        inputData.data?.teachers &&
        inputData.data?.classes &&
        inputData.data?.timeSlots
      ) {
        const { inputData: validationInputData, flatScheduleData } =
          prepareDataForValidation(inputData)

        results += '### Input Data Validation\n\n'

        try {
          const validationResults = validateTimetable(
            validationInputData,
            flatScheduleData
          )
          results += formatValidationResults(validationResults)
        } catch (error) {
          results += `Error validating input data: ${error instanceof Error ? error.message : String(error)}\n\n`
        }
      } else {
        results += '### Input Data Validation\n\n'
        results +=
          'Input data does not have the required structure for validation.\n\n'
      }

      // Validate output data if it has the required structure
      if (
        outputData.data?.teachers &&
        outputData.data?.classes &&
        outputData.data?.timeSlots
      ) {
        const { inputData: validationInputData, flatScheduleData } =
          prepareDataForValidation(outputData)

        results += '### Output Data Validation\n\n'

        try {
          const validationResults = validateTimetable(
            validationInputData,
            flatScheduleData
          )
          results += formatValidationResults(validationResults)
        } catch (error) {
          results += `Error validating output data: ${error instanceof Error ? error.message : String(error)}\n\n`
        }
      } else {
        results += '### Output Data Validation\n\n'
        results +=
          'Output data does not have the required structure for validation.\n\n'
      }
    } catch (error) {
      results += `Error running validation: ${error instanceof Error ? error.message : String(error)}\n\n`
    }

    return results
  }

  const countAssignmentsBySubject = (assignments: any[]) => {
    const counts: Record<string, number> = {}

    assignments.forEach((assignment) => {
      if (assignment.subject) {
        counts[assignment.subject] = (counts[assignment.subject] || 0) + 1
      }
    })

    return counts
  }

  const formatSubjectCounts = (counts: Record<string, number>) => {
    return Object.entries(counts)
      .map(([subject, count]) => `${subject}(${count})`)
      .join(', ')
  }

  return (
    <>
      <Button
        variant="outline"
        className={`gap-2 ${className || ''}`}
        onClick={() => setOpen(true)}
      >
        <ArrowRightLeft className="h-4 w-4" />
        Compare Timetables
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Timetable Comparer</DialogTitle>
          </DialogHeader>

          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="input">Input & Output</TabsTrigger>
              <TabsTrigger value="results">Comparison Results</TabsTrigger>
              <TabsTrigger value="help">Help</TabsTrigger>
            </TabsList>

            <TabsContent value="input" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Enter Timetable Data</h3>
                <Button variant="outline" size="sm" onClick={handleSwap}>
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  Swap Input/Output
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="input-data">Input Timetable (JSON)</Label>
                  <Textarea
                    id="input-data"
                    placeholder="Paste your input timetable JSON here..."
                    className="h-[400px] font-mono text-sm"
                    value={inputData}
                    onChange={(e) => setInputData(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="output-data">Output Timetable (JSON)</Label>
                  <Textarea
                    id="output-data"
                    placeholder="Paste your output timetable JSON here..."
                    className="h-[400px] font-mono text-sm"
                    value={outputData}
                    onChange={(e) => setOutputData(e.target.value)}
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                  <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <div>{error}</div>
                </div>
              )}

              <div className="flex justify-end">
                <Button
                  onClick={handleCompare}
                  disabled={isLoading || !inputData || !outputData}
                >
                  {isLoading ? 'Comparing...' : 'Compare Timetables'}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Comparison Results</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      // Store the current input/output data in localStorage for the debug sidebar
                      try {
                        localStorage.setItem('timetable_debug_input', inputData)
                        localStorage.setItem(
                          'timetable_debug_output',
                          outputData
                        )
                        // Show a message to the user
                        alert(
                          'Timetable data saved for unscheduled lessons analysis. Click the "Unscheduled Lessons" button in the timetable view to see the results.'
                        )
                      } catch (error) {
                        console.error('Error saving debug data:', error)
                        alert(
                          'Error saving debug data. See console for details.'
                        )
                      }
                    }}
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Show Unscheduled
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyResults}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Results
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-[500px] border rounded-md p-4">
                <div className="prose prose-sm dark:prose-invert">
                  {comparisonResults.split('\n').map((line, i) => {
                    if (line.startsWith('# ')) {
                      return <h1 key={i}>{line.substring(2)}</h1>
                    } else if (line.startsWith('## ')) {
                      return <h2 key={i}>{line.substring(3)}</h2>
                    } else if (line.startsWith('### ')) {
                      return <h3 key={i}>{line.substring(4)}</h3>
                    } else if (line.startsWith('#### ')) {
                      return <h4 key={i}>{line.substring(5)}</h4>
                    } else if (line.startsWith('##### ')) {
                      return <h5 key={i}>{line.substring(6)}</h5>
                    } else if (line.startsWith('- ')) {
                      return (
                        <p key={i} className="ml-4">
                          {line}
                        </p>
                      )
                    } else {
                      return <p key={i}>{line}</p>
                    }
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="help" className="space-y-4">
              <h3 className="text-lg font-medium">
                How to Use the Timetable Comparer
              </h3>

              <div className="prose prose-sm dark:prose-invert">
                <h4>Purpose</h4>
                <p>
                  This tool helps you compare two timetable JSON structures to
                  identify differences and potential issues. It's useful for
                  debugging problems with your timetable data.
                </p>

                <h4>Instructions</h4>
                <ol>
                  <li>Paste your input timetable JSON in the left textarea</li>
                  <li>
                    Paste your output timetable JSON in the right textarea
                  </li>
                  <li>Click "Compare Timetables" to analyze the differences</li>
                  <li>View the results in the "Comparison Results" tab</li>
                </ol>

                <h4>Expected Input Format</h4>
                <p>
                  The input should be a JSON object with the following
                  structure:
                </p>
                <pre className="text-xs">
                  {`{
  "timeSlots": {
    "Monday": {
      "studyTimes": ["8 => 9", "9 => 10", ...],
      "restTime": "12 => 14"
    },
    ...
  },
  "salles": ["Salle 1", "Salle 2", ...],
  "classes": [
    {
      "className": "1A",
      "subjects": [
        {"subject": "Math", "hours": 3, "teacherID": 17},
        ...
      ]
    },
    ...
  ],
  "teachers": [
    {
      "teacherId": 1,
      "teacherName": "Teacher 1",
      "subjects": ["Theater Arts", "Psychology"],
      "unavailableTimes": {
        "Monday": ["8 => 9", "9 => 10", ...],
        ...
      },
      "minimumHours": 10,
      "maximumHours": 15
    },
    ...
  ]
}`}
                </pre>

                <h4>Expected Output Format</h4>
                <p>
                  The output should be a JSON object with the following
                  structure:
                </p>
                <pre className="text-xs">
                  {`{
  "scheduleData": [
    {
      "class": "1A",
      "Monday": [
        {
          "time": "8 => 9",
          "subject": "Math",
          "teacherID": 17,
          "teacher": "Teacher 17",
          "salle": "Salle 1"
        },
        ...
      ],
      "Tuesday": [...],
      ...
    },
    ...
  ]
}`}
                </pre>

                <h4>What's Compared</h4>
                <ul>
                  <li>Days in input vs. days in output</li>
                  <li>Classes in input vs. classes in output</li>
                  <li>Expected subject hours vs. actual assigned hours</li>
                  <li>Teacher workload constraints vs. actual hours</li>
                  <li>Teacher unavailable times vs. scheduled times</li>
                  <li>
                    Room conflicts (same room used by multiple classes at once)
                  </li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  )
}
