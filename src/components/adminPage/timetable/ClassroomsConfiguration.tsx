import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Plus, Trash2, Loader2 } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface Classroom {
  id: string
  name: string
  capacity?: number
}

interface ClassroomsConfigurationProps {
  classrooms: Classroom[]
  isLoading: boolean
  onAddClassroom: (classroom: Classroom) => void
  onDeleteClassroom: (classroomId: string) => void
}

export default function ClassroomsConfiguration({
  classrooms,
  isLoading,
  onAddClassroom,
  onDeleteClassroom,
}: ClassroomsConfigurationProps) {
  const { toast } = useToast()
  const [newClassroomName, setNewClassroomName] = useState('')
  const [newClassroomCapacity, setNewClassroomCapacity] = useState<
    number | undefined
  >(30)

  const handleAddClassroom = () => {
    if (!newClassroomName.trim()) {
      toast({
        title: 'Classroom Name Required',
        description: 'Please enter a name for the classroom.',
        variant: 'destructive',
      })
      return
    }

    // Check if classroom with same name already exists
    if (
      classrooms.some(
        (c) => c.name.toLowerCase() === newClassroomName.toLowerCase()
      )
    ) {
      toast({
        title: 'Classroom Already Exists',
        description: 'A classroom with this name already exists.',
        variant: 'destructive',
      })
      return
    }

    // Add the new classroom
    onAddClassroom({
      id: `c${Date.now()}`, // Generate a unique ID
      name: newClassroomName,
      capacity: newClassroomCapacity,
    })

    // Reset form
    setNewClassroomName('')
    setNewClassroomCapacity(30)

    toast({
      title: 'Classroom Added',
      description: `${newClassroomName} has been added to the list of classrooms.`,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Classrooms/Salles Configuration</CardTitle>
        <CardDescription>
          Add classrooms that will be used for scheduling classes.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="classroom-name">Classroom Name</Label>
              <Input
                id="classroom-name"
                placeholder="e.g., Salle 1"
                value={newClassroomName}
                onChange={(e) => setNewClassroomName(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="classroom-capacity">Capacity (Optional)</Label>
              <Input
                id="classroom-capacity"
                type="number"
                placeholder="e.g., 30"
                value={newClassroomCapacity || ''}
                onChange={(e) =>
                  setNewClassroomCapacity(
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
            <div className="flex items-end">
              <Button onClick={handleAddClassroom} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Add Classroom
              </Button>
            </div>
          </div>

          <Separator className="my-4" />

          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : classrooms.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No classrooms added yet. Add your first classroom using the form
              above.
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {classrooms.map((classroom) => (
                    <TableRow key={classroom.id}>
                      <TableCell className="font-medium">
                        {classroom.name}
                      </TableCell>
                      <TableCell>
                        {classroom.capacity || 'Not specified'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteClassroom(classroom.id)}
                          title="Delete Classroom"
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
