import { normalizeTimeString } from '@/utils/timeFormatUtils'

/**
 * Formats timetable data for API requests
 */
export class TimetableDataFormatter {
  /**
   * Formats timetable data for the validate_hard API endpoint
   *
   * @param timetableData The timetable data to format
   * @returns Formatted data for the API
   */
  static formatForValidateHard(timetableData: any) {
    // Extract the actual data and schedule data
    const actualData = timetableData.data || timetableData
    const scheduleData =
      timetableData.scheduleData ||
      timetableData.data?.scheduleData ||
      timetableData.result?.scheduleData

    return {
      data: actualData,
      result: {
        scheduleData: scheduleData,
      },
    }
  }

  /**
   * Formats timetable data for the analyze_soft API endpoint
   *
   * @param timetableData The timetable data to format
   * @returns Formatted data for the API
   */
  static formatForAnalyzeSoft(timetableData: any) {
    return this.formatForValidateHard(timetableData)
  }

  /**
   * Formats timetable data for the warnings API endpoint
   *
   * @param timetableData The timetable data to format
   * @returns Formatted data for the API
   */
  static formatForWarnings(timetableData: any) {
    const actualData = timetableData.data || timetableData

    return {
      data: actualData,
    }
  }

  /**
   * Formats timetable data for the color_schedule API endpoint
   *
   * @param timetableData The timetable data to format
   * @returns Formatted data for the API
   */
  static formatForColorSchedule(timetableData: any) {
    return this.formatForValidateHard(timetableData)
  }

  /**
   * Normalizes time slots in the timetable data
   *
   * @param timetableData The timetable data to normalize
   * @returns Normalized timetable data
   */
  static normalizeTimeSlots(timetableData: any) {
    const data = { ...timetableData }

    // If we have timeSlots in the format from the API
    if (
      data.timeSlots &&
      typeof data.timeSlots === 'object' &&
      !Array.isArray(data.timeSlots)
    ) {
      // Process each day's study times
      Object.entries(data.timeSlots).forEach(
        ([_day, dayData]: [string, any]) => {
          if (dayData.studyTimes && Array.isArray(dayData.studyTimes)) {
            // Normalize each time string
            dayData.studyTimes = dayData.studyTimes.map((timeStr: string) =>
              normalizeTimeString(timeStr)
            )
          }

          // Also normalize rest time if available
          if (dayData.restTime) {
            dayData.restTime = normalizeTimeString(dayData.restTime)
          }
        }
      )
    }

    return data
  }
}
