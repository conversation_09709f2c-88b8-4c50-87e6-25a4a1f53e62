import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  validateTimetable,
  formatValidationResults,
} from '@/utils/timetableValidator'
import { AlertTriangle } from 'lucide-react'

interface TimetableValidatorProps {
  timetableData: any
  className?: string
}

export function TimetableValidator({
  timetableData,
  className,
}: TimetableValidatorProps) {
  const [validationResults, setValidationResults] = useState<string>('')
  const [open, setOpen] = useState(false)

  const runValidation = () => {
    try {
      // Log the timetable data structure for debugging
      console.log(
        'Timetable data structure:',
        JSON.stringify(timetableData, null, 2)
      )

      // Extract the necessary data from the timetable
      const inputData = timetableData.data || timetableData
      const scheduleData =
        timetableData.scheduleData ||
        timetableData.data?.scheduleData ||
        timetableData.result?.scheduleData ||
        []

      // Check if we have the required data
      if (!inputData || !scheduleData || scheduleData.length === 0) {
        setValidationResults(
          `# Error Validating Timetable\n\nThe timetable data is missing required information. Please make sure you have loaded a valid timetable.`
        )
        setOpen(true)
        return
      }

      // Check if the data has the expected structure
      if (!inputData.teachers || !inputData.classes || !inputData.timeSlots) {
        // Try to analyze what we have
        let analysisResults = '# Timetable Structure Analysis\n\n'

        // Check what keys we have
        analysisResults += `## Available Data\n\n`
        analysisResults += `Top-level keys: ${Object.keys(timetableData).join(', ')}\n\n`

        if (timetableData.data) {
          analysisResults += `Data keys: ${Object.keys(timetableData.data).join(', ')}\n\n`
        }

        if (timetableData.result) {
          analysisResults += `Result keys: ${Object.keys(timetableData.result).join(', ')}\n\n`
        }

        // Analyze schedule data
        if (scheduleData.length > 0) {
          analysisResults += `## Schedule Data\n\n`
          analysisResults += `Found ${scheduleData.length} schedule items.\n`
          analysisResults += `First item keys: ${Object.keys(scheduleData[0]).join(', ')}\n\n`

          // Sample a few schedule entries
          analysisResults += `### Sample Schedule Entries\n\n`
          scheduleData.slice(0, 3).forEach((item: any, index: number) => {
            analysisResults += `Item ${index + 1}: ${JSON.stringify(item, null, 2)}\n\n`
          })
        }

        setValidationResults(analysisResults)
        setOpen(true)
        return
      }

      // Flatten the schedule data if needed
      const flatScheduleData = scheduleData.flatMap((classSchedule: any) => {
        const className = classSchedule.class
        return Object.entries(classSchedule)
          .filter(([key]) => key !== 'class')
          .flatMap(([day, slots]: [string, any]) => {
            if (!Array.isArray(slots)) return []
            return slots.map((slot: any) => ({
              class: className,
              day,
              ...slot,
            }))
          })
      })

      // Log the flattened data for debugging
      console.log('Flattened schedule data:', flatScheduleData.slice(0, 5))

      // Run the validation
      const results = validateTimetable(inputData, flatScheduleData)

      // Format the results
      const formattedResults = formatValidationResults(results)

      // Set the results
      setValidationResults(formattedResults)

      // Open the dialog
      setOpen(true)

      // Log the results to the console
      console.log('Timetable validation results:', results)
    } catch (error) {
      console.error('Error validating timetable:', error)
      setValidationResults(
        `# Error Validating Timetable\n\nAn error occurred while validating the timetable: ${error}\n\nPlease check the console for more details.`
      )
      setOpen(true)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={`gap-2 ${className || ''}`}
          onClick={runValidation}
        >
          <AlertTriangle className="h-4 w-4" />
          Validate Timetable
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Timetable Validation Results</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[60vh] mt-4 p-4 border rounded-md">
          <div className="prose prose-sm dark:prose-invert">
            {validationResults.split('\n').map((line, i) => {
              if (line.startsWith('# ')) {
                return <h1 key={i}>{line.substring(2)}</h1>
              } else if (line.startsWith('## ')) {
                return <h2 key={i}>{line.substring(3)}</h2>
              } else if (line.startsWith('- ')) {
                return (
                  <p key={i} className="ml-4">
                    {line}
                  </p>
                )
              } else if (line.startsWith('  - ')) {
                return (
                  <p key={i} className="ml-8">
                    {line}
                  </p>
                )
              } else {
                return <p key={i}>{line}</p>
              }
            })}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
