import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import {
  Plus,
  Trash2,
  Upload,
  BookOpen,
  ChevronDown,
  Copy,
  Info,
  LightbulbIcon,
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { parseDivision, validateDivision } from '@/utils/lessonGroupUtils'

interface Subject {
  id: string
  name: string
}

interface Teacher {
  id: string
  name: string
  subjects: string[]
}

interface Class {
  id: string
  name: string
}

interface Room {
  id: string
  name: string
  type?: string
}

interface Lesson {
  subject: string
  teacher: string
  class: string
  hours: number
  isGrouped?: boolean
  divide?: number[]
  alternateWith?: string
}

interface LessonsConfigurationProps {
  lessons: Lesson[]
  subjects: Subject[]
  teachers: Teacher[]
  classes: Class[]
  rooms?: Room[]
  onAddLesson: (lesson: Lesson) => void
  onDeleteLesson: (index: number) => void
}

export function LessonsConfiguration({
  lessons,
  subjects,
  teachers,
  classes,
  onAddLesson,
  onDeleteLesson,
}: LessonsConfigurationProps) {
  console.log('LessonsConfiguration rendered with props:', {
    lessonsCount: lessons.length,
    subjectsCount: subjects.length,
    teachersCount: teachers.length,
    classesCount: classes.length,
  })
  const { toast } = useToast()
  const [selectedClass, setSelectedClass] = useState<string>('')
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const [selectedTeacher, setSelectedTeacher] = useState<string>('')
  const [hoursPerWeek, setHoursPerWeek] = useState<number>(1)

  // Group settings
  const [isGrouped, setIsGrouped] = useState<boolean>(false)
  const [divideInput, setDivideInput] = useState<string>('')

  // Alternating subjects
  const [alternateWith, setAlternateWith] = useState<string>('')
  const [alternateWithTeacher, setAlternateWithTeacher] = useState<string>('')
  const [alternationPattern, setAlternationPattern] = useState<
    'weekly' | 'biweekly'
  >('weekly')

  // Allow all teachers to be assigned to any subject
  const eligibleTeachers = teachers

  const handleAddLesson = () => {
    if (!selectedClass || !selectedSubject || !selectedTeacher) {
      toast({
        title: 'Error',
        description: 'Please select a class, subject, and teacher',
        variant: 'destructive',
      })
      return
    }

    if (hoursPerWeek < 1) {
      toast({
        title: 'Error',
        description: 'Hours per week must be at least 1',
        variant: 'destructive',
      })
      return
    }

    // Validate group division if enabled
    if (isGrouped) {
      const division = parseDivision(divideInput)
      if (!division || !validateDivision(division, hoursPerWeek)) {
        toast({
          title: 'Invalid Group Division',
          description: `Division must sum to ${hoursPerWeek} hours. Example: "2,2" for 4 hours.`,
          variant: 'destructive',
        })
        return
      }
    }

    // Validate alternating subjects if selected
    if (alternateWith && !alternateWithTeacher) {
      toast({
        title: 'Missing Teacher',
        description: 'Please select a teacher for the alternating subject',
        variant: 'destructive',
      })
      return
    }

    // Check if the lesson already exists
    const lessonExists = lessons.some(
      (lesson) =>
        lesson.class === selectedClass &&
        lesson.subject === selectedSubject &&
        lesson.teacher === selectedTeacher &&
        !lesson.isGrouped &&
        !lesson.alternateWith
    )

    if (lessonExists) {
      toast({
        title: 'Error',
        description: 'This lesson already exists',
        variant: 'destructive',
      })
      return
    }

    // Create the lesson object
    const newLesson: Lesson = {
      class: selectedClass,
      subject: selectedSubject,
      teacher: selectedTeacher,
      hours: hoursPerWeek,
    }

    // Add group information if enabled
    if (isGrouped) {
      newLesson.isGrouped = true
      newLesson.divide = parseDivision(divideInput)
    }

    // Handle alternating subjects
    if (alternateWith && alternateWithTeacher) {
      // First, add the primary subject
      onAddLesson(newLesson)

      // Then, add the alternating subject as a separate lesson
      const alternatingLesson: Lesson = {
        class: selectedClass,
        subject: alternateWith,
        teacher: alternateWithTeacher,
        hours: hoursPerWeek,
        alternateWith: selectedSubject, // Point back to the primary subject
      }

      // Add the alternating lesson
      onAddLesson(alternatingLesson)

      // Reset form
      setSelectedClass('')
      setSelectedSubject('')
      setSelectedTeacher('')
      setHoursPerWeek(1)
      setIsGrouped(false)
      setDivideInput('')
      setAlternateWith('')
      setAlternateWithTeacher('')
      setAlternationPattern('weekly')

      toast({
        title: 'Alternating Lessons Added',
        description:
          'Both alternating lessons have been added to the timetable',
      })

      return // Exit early since we've already added both lessons
    }

    // Add the new lesson (if not alternating)
    onAddLesson(newLesson)

    // Reset form
    setSelectedClass('')
    setSelectedSubject('')
    setSelectedTeacher('')
    setHoursPerWeek(1)
    setIsGrouped(false)
    setDivideInput('')
    setAlternateWith('')
    setAlternateWithTeacher('')
    setAlternationPattern('weekly')

    toast({
      title: 'Lesson Added',
      description: 'The lesson has been added to the timetable',
    })
  }

  const getSubjectName = (subjectId: string) => {
    return subjects.find((s) => s.id === subjectId)?.name || 'Unknown'
  }

  const getTeacherName = (teacherId: string) => {
    return teachers.find((t) => t.id === teacherId)?.name || 'Unknown'
  }

  const getClassName = (classId: string) => {
    return classes.find((c) => c.id === classId)?.name || 'Unknown'
  }

  const handleClassChange = (value: string) => {
    setSelectedClass(value)
  }

  const handleSubjectChange = (value: string) => {
    setSelectedSubject(value)
    // Don't reset teacher when subject changes
  }

  const handleTeacherChange = (value: string) => {
    setSelectedTeacher(value)
  }

  const handleHoursChange = (value: string) => {
    const hours = parseInt(value)
    if (!isNaN(hours) && hours >= 1) {
      setHoursPerWeek(hours)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Lessons
        </CardTitle>
        <CardDescription>
          Add lessons for each class with the subject(s), teacher(s), and number
          of periods per timetable cycle.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Tips & Tricks section */}
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <h3 className="text-lg font-medium flex items-center gap-2 text-yellow-800">
            <LightbulbIcon className="h-5 w-5 text-yellow-600" />
            Tips & Tricks
          </h3>
          <ul className="mt-2 space-y-2">
            <li className="flex items-start gap-2">
              <div className="mt-1 text-yellow-600">•</div>
              <div>
                <p className="font-medium text-yellow-800">
                  Link subjects to appropriate classes
                </p>
                <p className="text-sm text-yellow-700">
                  Correctly assign each subject to the classes that need to take
                  it.
                </p>
              </div>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-1 text-yellow-600">•</div>
              <div>
                <p className="font-medium text-yellow-800">
                  Set the right number of periods
                </p>
                <p className="text-sm text-yellow-700">
                  Specify exactly how many periods per week each subject
                  requires for each class.
                </p>
              </div>
            </li>
          </ul>
        </div>

        {/* Main form */}
        <div className="border rounded-lg p-4 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-4 items-end">
            <div>
              <Label htmlFor="class-select">CLASSES</Label>
              <Select value={selectedClass} onValueChange={handleClassChange}>
                <SelectTrigger id="class-select">
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="teacher-select">TEACHERS</Label>
              <Select
                value={selectedTeacher}
                onValueChange={handleTeacherChange}
              >
                <SelectTrigger id="teacher-select">
                  <SelectValue placeholder="Select a teacher" />
                </SelectTrigger>
                <SelectContent>
                  {eligibleTeachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="subject-select">SUBJECTS</Label>
              <Select
                value={selectedSubject}
                onValueChange={handleSubjectChange}
              >
                <SelectTrigger id="subject-select">
                  <SelectValue placeholder="Select a subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="hours">PERIODS/CYCLE</Label>
              <Input
                id="hours"
                type="number"
                min={1}
                max={10}
                value={hoursPerWeek}
                onChange={(e) => handleHoursChange(e.target.value)}
                className="w-full"
              />
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10"
                title="Expand options"
              >
                <ChevronDown className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 text-destructive"
                title="Remove lesson"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Group splitting option */}
          <div className="mb-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="split-classes"
                checked={isGrouped}
                onCheckedChange={(checked) => setIsGrouped(checked === true)}
              />
              <Label
                htmlFor="split-classes"
                className="font-medium cursor-pointer"
                onClick={() => setIsGrouped(!isGrouped)}
              >
                Split Classes into periods 
              </Label>
            </div>
            <p className="text-sm text-muted-foreground ml-6 mt-1">
              Use when classes are split into multiple groups, each with their
              own teachers and subjects. All scheduled at the same time.
            </p>

            {isGrouped && (
              <div className="mt-4 ml-6 border rounded-md p-4 bg-slate-50">
                <div className="mb-4">
                  <Label htmlFor="divide-input">Division Pattern</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      id="divide-input"
                      placeholder="e.g., 2,2 for 4 hours"
                      value={divideInput}
                      onChange={(e) => setDivideInput(e.target.value)}
                    />
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Add Group
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Enter how to divide the {hoursPerWeek} hours. Example: "2,2"
                    for two groups of 2 hours each.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Alternating subjects option */}
          <div className="mb-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="alternate-subjects"
                checked={!!alternateWith}
                onCheckedChange={(checked) => {
                  if (checked) {
                    // If checked and no subject is selected, select the first one
                    if (subjects.length > 0 && !alternateWith) {
                      setAlternateWith(subjects[0].id)
                    }
                  } else {
                    // If unchecked, clear the alternating subject and teacher
                    setAlternateWith('')
                    setAlternateWithTeacher('')
                  }
                }}
              />
              <Label
                htmlFor="alternate-subjects"
                className="font-medium cursor-pointer"
                onClick={() => {
                  // Toggle the checkbox when the label is clicked
                  if (alternateWith) {
                    setAlternateWith('')
                    setAlternateWithTeacher('')
                  } else if (subjects.length > 0) {
                    setAlternateWith(subjects[0].id)
                  }
                }}
              >
                Alternating Subjects
              </Label>
            </div>
            <p className="text-sm text-muted-foreground ml-6 mt-1">
              For subjects that alternate with each other, like Art/Music or
              different language options.
            </p>

            {alternateWith && (
              <div className="mt-4 ml-6 border rounded-md p-4 bg-slate-50">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="alternate-subject">
                      Alternating Subject
                    </Label>
                    <Select
                      value={alternateWith}
                      onValueChange={setAlternateWith}
                    >
                      <SelectTrigger id="alternate-subject">
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground mt-1">
                      This subject will alternate with{' '}
                      {getSubjectName(selectedSubject)}
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="alternate-teacher">Teacher</Label>
                    <Select
                      value={alternateWithTeacher}
                      onValueChange={setAlternateWithTeacher}
                    >
                      <SelectTrigger id="alternate-teacher">
                        <SelectValue placeholder="Select teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        {eligibleTeachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground mt-1">
                      Who will teach {getSubjectName(alternateWith)}
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <Label htmlFor="alternation-pattern">
                    Alternation Pattern
                  </Label>
                  <Select
                    value={alternationPattern}
                    onValueChange={(val: 'weekly' | 'biweekly') =>
                      setAlternationPattern(val)
                    }
                  >
                    <SelectTrigger id="alternation-pattern">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">
                        Weekly (alternates every week)
                      </SelectItem>
                      <SelectItem value="biweekly">
                        Biweekly (alternates every two weeks)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-700">
                      <Info className="h-4 w-4 inline-block mr-1" />
                      This will create two separate lessons that alternate with
                      each other:
                    </p>
                    <ul className="mt-2 text-sm text-blue-700 list-disc list-inside">
                      <li>
                        {getSubjectName(selectedSubject)} taught by{' '}
                        {getTeacherName(selectedTeacher)}
                      </li>
                      <li>
                        {getSubjectName(alternateWith)} taught by{' '}
                        {getTeacherName(alternateWithTeacher)}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline" className="flex items-center gap-1">
              <Upload className="h-4 w-4" />
              Bulk Import
            </Button>
            <Button variant="outline" className="flex items-center gap-1">
              <Copy className="h-4 w-4" />
              Copy Last Lesson
            </Button>
            <Button
              onClick={handleAddLesson}
              className="bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add New Lesson
            </Button>
          </div>
        </div>

        <Separator />

        {lessons.length === 0 ? (
          <div className="text-center p-4 border rounded-md bg-muted/20">
            No lessons defined yet. Add your first lesson using the form above.
          </div>
        ) : (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Class</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Teacher</TableHead>
                  <TableHead>Periods/Week</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {lessons.map((lesson, index) => (
                  <TableRow key={index}>
                    <TableCell>{getClassName(lesson.class)}</TableCell>
                    <TableCell>
                      {getSubjectName(lesson.subject)}
                      {lesson.alternateWith && (
                        <span className="text-xs ml-1 text-muted-foreground">
                          (alternates with{' '}
                          {getSubjectName(lesson.alternateWith)})
                        </span>
                      )}
                      {lesson.isGrouped && lesson.divide && (
                        <span className="text-xs ml-1 text-muted-foreground">
                          (Periods: {lesson.divide.join(', ')})
                        </span>
                      )}
                    </TableCell>
                    <TableCell>{getTeacherName(lesson.teacher)}</TableCell>
                    <TableCell>{lesson.hours}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDeleteLesson(index)}
                        title="Delete Lesson"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4 flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                Total lessons: {lessons.length}
              </div>
              <Button variant="outline" className="flex items-center gap-1">
                <Upload className="h-4 w-4" />
                Bulk Import
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
