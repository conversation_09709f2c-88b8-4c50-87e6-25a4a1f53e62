import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { useTourStore } from '@/store/tourStore'
import { RefreshCw } from 'lucide-react'

export function TourRestartButton() {
  const {
    adminTimetableTourActive,
    adminTimetableTourCompleted,
    resetAdminTimetableTour,
    startAdminTimetableTour,
    setAdminTimetableTourStep,
  } = useTourStore()

  const [, setVisible] = useState(true)

  useEffect(() => {
    // Show the button when the tour is not active or when it's completed
    setVisible(!adminTimetableTourActive || adminTimetableTourCompleted)

    // Add a listener for tour status changes
    const handleTourStatusChange = () => {
      setVisible(!adminTimetableTourActive || adminTimetableTourCompleted)
    }

    window.addEventListener('tour-status-change', handleTourStatusChange)

    return () => {
      window.removeEventListener('tour-status-change', handleTourStatusChange)
    }
  }, [adminTimetableTourActive, adminTimetableTourCompleted])

  const handleRestartClick = () => {
    console.log('TourRestartButton: Restarting tour')

    // Reset tour state in localStorage
    localStorage.removeItem('tour_completed_admin_timetable')
    localStorage.removeItem('tour_skipped_admin_timetable')
    localStorage.removeItem('admin_timetable_tour_current_step')
    localStorage.setItem('trigger_admin_timetable_tour', 'true')
    localStorage.setItem('userOnboarding', 'true')

    // Reset the tour in Zustand
    resetAdminTimetableTour()

    // Start from the beginning
    setAdminTimetableTourStep(0)

    // Start the tour
    setTimeout(() => {
      startAdminTimetableTour()
      window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))

      // Reload the page to ensure a clean state
      setTimeout(() => {
        window.location.reload()
      }, 300)
    }, 100)
  }

  if (adminTimetableTourActive) {
    return null
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 10001,
      }}
    >
      <Button
        onClick={handleRestartClick}
        variant="outline"
        size="sm"
        className="bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-md px-3 py-2 flex items-center gap-2"
        title="Restart Tour"
      >
        <RefreshCw className="h-4 w-4" />
        <span>Restart Tour</span>
      </Button>
    </div>
  )
}
