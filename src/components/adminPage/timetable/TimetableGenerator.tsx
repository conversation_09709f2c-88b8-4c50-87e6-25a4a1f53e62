import { useState, useEffect } from 'react'
// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { useQueries } from '@tanstack/react-query'
import { api } from '@/lib/axios'

// Icons
import {
  ArrowLeft,
  ArrowRight,
  Loader2,
  CheckCircle2,
  Settings,
  BookOpen,
  User,
  GraduationCap,
  Clock,
  Calendar,
} from 'lucide-react'

// API Services
import { timetableService } from '@/lib/api/services/timetable-service'

// External timetable API URL is defined in the timetable-service.ts file
// import { services } from '@/lib/api' // Not used

// Timetable Components
import { BasicTimetableInfo } from './BasicTimetableInfo'
import { SubjectsConfiguration } from './SubjectsConfiguration'
import { TeachersConfiguration } from './TeachersConfiguration'
import { ClassesConfiguration } from './ClassesConfiguration'
import { LessonsConfiguration } from './LessonsConfiguration'
import { GenerationResults } from './GenerationResults'
import ClassroomsConfiguration from './ClassroomsConfiguration'
import { PeriodTimingsDisplay } from './PeriodTimingsDisplay'

// Types for timetable data
interface Subject {
  id: string
  name: string
  color?: string
}

interface Teacher {
  id: string
  name: string
  subjects: string[]
  unavailableTimes?: Record<string, string[]>
  minimumHours?: number
  maximumHours?: number
}

interface Classroom {
  id: string
  name: string
  capacity?: number
  type?: string
}

interface Class {
  id: string
  name: string
}

interface Lesson {
  subject: string // ID of the subject
  teacher: string // ID of the teacher
  class: string // ID of the class
  hours: number // Number of hours per week
  isGrouped?: boolean // Whether this lesson is split into groups
  divide?: number[] // How to divide the hours, e.g. [2, 2] for 4 hours
  alternateWith?: string // ID of the alternating subject
  room?: string // ID of the room
}

interface Room {
  id: string
  name: string
  capacity?: number
  type?: string
}

interface Timeslot {
  id: string
  day: string
  period: number
  start: string
  end: string
  isBreak?: boolean
}

interface PeriodTiming {
  id: string
  periodNumber: number
  startTime: string
  endTime: string
  isBreak: boolean
}

interface TimetableData {
  subjects: Subject[]
  teachers: Teacher[]
  classes: Class[]
  lessons: Lesson[]
  rooms: Room[]
  timeslots: Timeslot[]
  name?: string
  academicYear?: string
  periodCount?: number
  schoolDays?: string[]
  startTime?: string
  endTime?: string
  periodLength?: number
  periodTimings?: PeriodTiming[]
  constraints?: {
    avoidBackToBack: boolean
    optimizeFreeTime: boolean
    balanceTeacherLoads: boolean
    maxDailyPeriodsPerTeacher?: number
    preferConsecutivePeriods?: boolean
  }
}

interface GeneratedTimetable {
  id: string
  name: string
  schedule?: any[] // For the old format
  scheduleData?: any[] // For the new format from the API
  stats?: {
    classes: number
    teachers: number
    rooms: number
    periods: number
    days: number
    assignments: number
  }
}

interface TimetableGeneratorProps {
  onTimetableGenerated?: (data: any) => void
}

export default function TimetableGenerator({
  onTimetableGenerated,
}: TimetableGeneratorProps = {}) {
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 8
  const { toast } = useToast()

  // Note: The LessonsConfiguration component manages its own state now

  // Form state for timetable generation
  const [timetableData, setTimetableData] = useState<TimetableData>({
    subjects: [],
    teachers: [],
    classes: [],
    lessons: [],
    rooms: [],
    timeslots: [],
    schoolDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    periodCount: 8,
    startTime: '08:00',
    endTime: '17:00',
    periodLength: 45,
    academicYear: new Date().getFullYear().toString(),
  })

  // State for the timetable generation process
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedTimetable, setGeneratedTimetable] =
    useState<GeneratedTimetable | null>(null)

  // Use React Query to fetch data from all endpoints
  const queries = useQueries({
    queries: [
      {
        queryKey: ['subjects'],
        queryFn: async () => {
          try {
            const response = await api.get('/subject')

            // Map subjects to the format expected by the timetable generator
            const formattedSubjects: Subject[] = response.data.map(
              (subject: any) => ({
                id: String(subject.id),
                name: subject.name,
              })
            )

            return formattedSubjects
          } catch (error) {
            // Error fetching subjects
            // Return empty array instead of throwing to prevent UI from breaking
            return []
          }
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
      {
        queryKey: ['teachers'],
        queryFn: async () => {
          try {
            const response = await api.get('/teacher')

            // Log the first teacher to see the structure
            if (response.data.length > 0) {
              console.log('First teacher from API:', response.data[0])
            }

            // Map teachers to the format expected by the timetable generator
            const formattedTeachers: Teacher[] = response.data.map(
              (teacher: any) => {
                // Ensure we have a valid ID - use the numeric ID if available
                const teacherId =
                  teacher.id !== null && teacher.id !== undefined
                    ? String(teacher.id)
                    : `teacher_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

                return {
                  id: teacherId, // Use the ID directly
                  name:
                    teacher.name ||
                    `${teacher.firstname || ''} ${teacher.lastname || ''}`.trim(),
                  subjects: teacher.subjects || [],
                  unavailableTimes: teacher.unavailableTimes || {},
                  minimumHours: teacher.minimumHours,
                  maximumHours: teacher.maximumHours,
                }
              }
            )

            return formattedTeachers
          } catch (error) {
            // Error fetching teachers
            // Return empty array instead of throwing to prevent UI from breaking
            return []
          }
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
      {
        queryKey: ['classes'],
        queryFn: async () => {
          try {
            const response = await api.get('/class')

            // Map classes to the format expected by the timetable generator
            const formattedClasses: Class[] = response.data.map(
              (classItem: any) => ({
                id: String(classItem.id),
                name: classItem.name,
              })
            )

            return formattedClasses
          } catch (error) {
            // Error fetching classes
            // Return empty array instead of throwing to prevent UI from breaking
            return []
          }
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
      {
        queryKey: ['classrooms'],
        queryFn: async () => {
          try {
            const response = await api.get('/classrooms')

            // Map classrooms to the format expected by the timetable generator
            const formattedClassrooms: Classroom[] = response.data.map(
              (classroom: any) => ({
                id: String(classroom.id),
                name: classroom.name,
                capacity: classroom.capacity,
              })
            )

            return formattedClassrooms
          } catch (error) {
            // Error fetching classrooms
            // Return empty array instead of throwing to prevent UI from breaking
            return []
          }
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
    ],
  })

  // Extract data and loading states from queries
  const [subjectsQuery, teachersQuery, classesQuery, classroomsQuery] = queries
  const subjectsData = subjectsQuery.data
  const teachersData = teachersQuery.data

  // Update timetableData when data is loaded from React Query
  useEffect(() => {
    const updatedData: Partial<TimetableData> = {}

    if (teachersData) {
      updatedData.teachers = teachersData
    }

    if (subjectsData) {
      updatedData.subjects = subjectsData
    }

    if (classesQuery.data) {
      updatedData.classes = classesQuery.data
    }

    if (classroomsQuery.data) {
      updatedData.rooms = classroomsQuery.data.map((room) => ({
        id: room.id,
        name: room.name,
        capacity: room.capacity || 30,
        type: room.type || 'room', // Default to 'room' if type is not specified
      }))
    }

    if (Object.keys(updatedData).length > 0) {
      setTimetableData((prev) => ({
        ...prev,
        ...updatedData,
      }))
    }
  }, [teachersData, subjectsData, classesQuery.data, classroomsQuery.data])

  // Test backend connection when the component mounts
  useEffect(() => {
    testBackendConnection()
  }, [])

  // Generate timetable data in the required format for the API when on the Review & Generate step
  useEffect(() => {
    if (
      !subjectsQuery.isLoading &&
      !teachersQuery.isLoading &&
      !classesQuery.isLoading &&
      !classroomsQuery.isLoading &&
      currentStep === 7 // Review & Generate step
    ) {
      generateTimetableApiData()
    }
  }, [
    currentStep,
    timetableData.timeslots,
    timetableData.teachers,
    timetableData.classes,
    timetableData.subjects,
    timetableData.rooms,
    subjectsQuery.isLoading,
    teachersQuery.isLoading,
    classesQuery.isLoading,
    classroomsQuery.isLoading,
  ])

  // Function to generate timetable data in the required format for the API
  const generateTimetableApiData = () => {
    // Generate time slots based on the user's configuration
    const generateTimeSlots = () => {
      // Use the general settings as the primary source of truth, but validate them first
      const startTime =
        timetableData.startTime &&
        typeof timetableData.startTime === 'string' &&
        timetableData.startTime.includes(':')
          ? timetableData.startTime
          : '08:00'
      const endTime =
        timetableData.endTime &&
        typeof timetableData.endTime === 'string' &&
        timetableData.endTime.includes(':')
          ? timetableData.endTime
          : '17:00'
      const periodLength =
        timetableData.periodLength &&
        typeof timetableData.periodLength === 'number' &&
        timetableData.periodLength > 0
          ? timetableData.periodLength
          : 45
      const periodCount =
        timetableData.periodCount &&
        typeof timetableData.periodCount === 'number' &&
        timetableData.periodCount > 0
          ? timetableData.periodCount
          : 8

      // Parse start and end times
      const [startHour, startMinute] = startTime.split(':').map(Number)
      const [endHour, endMinute] = endTime.split(':').map(Number)

      // Calculate total minutes in the day
      const startTimeInMinutes = startHour * 60 + startMinute
      const endTimeInMinutes = endHour * 60 + endMinute

      // Calculate lunch break time (approximately in the middle of the day)
      const totalMinutes = endTimeInMinutes - startTimeInMinutes
      const lunchStartMinutes =
        startTimeInMinutes + Math.floor(totalMinutes / 2) - 30
      const lunchEndMinutes = lunchStartMinutes + 60 // 1 hour lunch break

      // Format lunch break time
      const lunchStartHour = Math.floor(lunchStartMinutes / 60)
      const lunchEndHour = Math.floor(lunchEndMinutes / 60)

      // Format lunch break time as "12 => 14" instead of "12:00 => 14:00" for API compatibility
      const lunchBreak = `${lunchStartHour} => ${lunchEndHour}`

      // Generate time slots
      const studyTimes = []
      let currentMinutes = startTimeInMinutes

      for (let i = 0; i < periodCount; i++) {
        // Skip lunch break
        if (
          currentMinutes >= lunchStartMinutes &&
          currentMinutes < lunchEndMinutes
        ) {
          currentMinutes = lunchEndMinutes
        }

        if (currentMinutes + periodLength <= endTimeInMinutes) {
          const startHour = Math.floor(currentMinutes / 60)
          const endMinutes = currentMinutes + periodLength
          const endHour = Math.floor(endMinutes / 60)

          // Format time as "8 => 9" instead of "08:00 => 09:00" for API compatibility
          // Only add the time slot if start and end hours are different
          if (startHour !== endHour) {
            studyTimes.push(`${startHour} => ${endHour}`)
          } else if (endMinutes % 60 > 0) {
            // If we have minutes, keep the full time format
            const startMinutes = currentMinutes % 60
            const endMinutesRemainder = endMinutes % 60
            const startTime = `${startHour}:${startMinutes === 0 ? '00' : startMinutes}`
            const endTime = `${endHour}:${endMinutesRemainder === 0 ? '00' : endMinutesRemainder}`
            studyTimes.push(`${startTime} => ${endTime}`)
          }

          currentMinutes = endMinutes
        }
      }

      return { studyTimes, lunchBreak }
    }

    // === MANUAL TIME SLOTS/BREAKS ONLY: No auto-generation, only use user input ===

    // These are all possible days that could be selected
    // We'll use the selected days from timetableData.schoolDays

    // Filter to only include the days that are selected
    const schoolDays =
      timetableData.schoolDays && timetableData.schoolDays.length > 0
        ? timetableData.schoolDays.filter((day) => day) // Filter out any null/undefined values
        : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'] // Default to weekdays

    // Log the days being used
    console.log('Using selected school days in this order:', schoolDays)

    // Build timeSlots object from period timings
    const timeSlots: Record<
      string,
      { studyTimes: string[]; restTime: string }
    > = {}

    // Initialize each day with empty study times
    schoolDays.forEach((day) => {
      timeSlots[day] = {
        studyTimes: [],
        restTime: '',
      }
    })

    // Format time as "8 => 9" or keep original format if minutes are present
    const formatTime = (time: string) => {
      if (time.includes(':')) {
        const [hours, minutes] = time.split(':').map(Number)
        // If minutes are 0, just return the hour
        // Otherwise, keep the original format with minutes
        return minutes === 0 ? hours.toString() : time
      }
      return time
    }

    // Use period timings if available
    if (timetableData.periodTimings && timetableData.periodTimings.length > 0) {
      // Group timings by type (regular periods vs breaks)
      const regularPeriods = timetableData.periodTimings.filter(
        (timing) => !timing.isBreak
      )
      const breaks = timetableData.periodTimings.filter(
        (timing) => timing.isBreak
      )

      // Sort periods by number
      regularPeriods.sort((a, b) => a.periodNumber - b.periodNumber)

      // Add regular periods to each day
      schoolDays.forEach((day) => {
        // Add study times - only include periods with valid start and end times
        timeSlots[day].studyTimes = regularPeriods
          .filter(
            (period) =>
              period.startTime &&
              period.endTime &&
              period.startTime.trim() &&
              period.endTime.trim()
          )
          .map((period) => {
            return `${formatTime(period.startTime)} => ${formatTime(period.endTime)}`
          })

        // Add break time if available (use the first break)
        if (breaks.length > 0) {
          const firstBreak = breaks[0]
          if (
            firstBreak.startTime &&
            firstBreak.endTime &&
            firstBreak.startTime.trim() &&
            firstBreak.endTime.trim()
          ) {
            timeSlots[day].restTime =
              `${formatTime(firstBreak.startTime)} => ${formatTime(firstBreak.endTime)}`
          }
        }
      })
    } else {
      // If no period timings, generate default time slots
      const { studyTimes, lunchBreak } = generateTimeSlots()

      // Add default study times and lunch break to each day
      schoolDays.forEach((day) => {
        timeSlots[day].studyTimes = studyTimes
        timeSlots[day].restTime = lunchBreak
      })
    }

    // Convert rooms to the required format
    let salles = timetableData.rooms.map((room) => ({
      name: room.name,
      type: room.type || 'room',
    }))

    // Get the actual teachers used in lessons
    const teachersUsedInLessons = new Set(
      timetableData.lessons.map((lesson) => lesson.teacher)
    )

    // Create a map of teacher IDs to their index + 1
    const teacherIdToIndex = new Map<string, number>()
    timetableData.teachers
      .filter((teacher) => teachersUsedInLessons.has(teacher.id))
      .forEach((teacher, index) => {
        teacherIdToIndex.set(teacher.id, index + 1)
      })

    // Convert classes and lessons to the required format
    // Only include classes that have lessons
    const classesWithLessons = new Set(
      timetableData.lessons.map((lesson) => lesson.class)
    )

    let classes = timetableData.classes
      .filter((cls) => classesWithLessons.has(cls.id))
      .map((cls) => {
        // Find lessons for this class
        const classLessons = timetableData.lessons.filter(
          (lesson) => lesson.class === cls.id
        )

        // Group lessons by subject
        const subjectMap = new Map<
          string,
          {
            hours: number
            teacherId: string
            divide?: number[]
            alternateWith?: string
          }
        >()

        classLessons.forEach((lesson) => {
          const subject = timetableData.subjects.find(
            (s) => s.id === lesson.subject
          )
          const teacher = timetableData.teachers.find(
            (t) => t.id === lesson.teacher
          )

          if (subject && teacher) {
            // We'll use the subject as is, which includes the color property if it exists
            // Check if the subject already exists in the map
            const existingSubject = subjectMap.get(subject.name)
            if (existingSubject) {
              // If it exists, update the hours
              existingSubject.hours += lesson.hours
            } else {
              // If it doesn't exist, add it
              const subjectData: {
                hours: number
                teacherId: string
                divide?: number[]
                alternateWith?: string
              } = {
                hours: lesson.hours,
                teacherId: teacher.id,
                // Use the divide property from the lesson if available
                divide:
                  lesson.divide ||
                  (lesson.hours >= 4
                    ? [
                        Math.ceil(lesson.hours / 2),
                        Math.floor(lesson.hours / 2),
                      ]
                    : undefined),
              }

              // Add alternating subject information if available
              if (lesson.alternateWith) {
                const alternateSubject = timetableData.subjects.find(
                  (s) => s.id === lesson.alternateWith
                )

                if (alternateSubject) {
                  // Store the name of the subject it alternates with
                  subjectData.alternateWith = alternateSubject.name
                }
              }

              subjectMap.set(subject.name, subjectData)
            }
          }
        })

        // Convert to the required format
        const subjects = Array.from(subjectMap.entries()).map(
          ([subjectName, data]) => {
            // Find the teacher by ID to get the name
            const teacher = timetableData.teachers.find(
              (t) => t.id === data.teacherId
            )

            const result: any = {
              subject: subjectName,
              hours: data.hours,
              teacherID: data.teacherId, // Use the actual UUID of the teacher
              teacher: teacher ? teacher.name : 'Unknown Teacher', // Include the teacher's name
            }

            // Add divide property if it exists
            if (data.divide) {
              result.divide = data.divide
            }

            // Add alternating subject information if it exists
            if (data.alternateWith) {
              result.alternateWith = data.alternateWith
            }

            // Find the original lesson to get the room information
            const classLessons = timetableData.lessons.filter(
              (l) => l.class === cls.id
            )
            const lessonWithRoom = classLessons.find((l) => {
              // Find the subject by name
              const subject = timetableData.subjects.find(
                (s) => s.name === subjectName
              )
              return (
                subject &&
                l.subject === subject.id &&
                l.teacher === data.teacherId &&
                l.room
              )
            })

            // Add room information if it exists
            if (lessonWithRoom?.room) {
              const room = timetableData.rooms.find(
                (r) => r.id === lessonWithRoom.room
              )
              if (room) {
                result.room = room.name
              }
            }

            return result
          }
        )

        return {
          className: cls.name,
          subjects,
        }
      })

    // Only include teachers that are actually used in lessons
    let teachers = timetableData.teachers
      .filter((teacher) => teachersUsedInLessons.has(teacher.id))
      .map((teacher) => {
        // Initialize unavailableTimes with empty arrays only for the selected days
        let unavailableTimes: Record<string, string[]> = {}

        // Only include the days that are selected in schoolDays
        schoolDays.forEach((day) => {
          unavailableTimes[day] = []
        })

        // If teacher has specific unavailable times, add them
        if (
          teacher.unavailableTimes &&
          Object.keys(teacher.unavailableTimes).length > 0
        ) {
          Object.entries(teacher.unavailableTimes).forEach(([day, times]) => {
            if (day in unavailableTimes) {
              // Make sure the times are in the format "8 => 9"
              unavailableTimes[day] = times.map((time) => {
                // If the time is already in the correct format, return it
                if (time.includes('=>')) {
                  // Check if it's in the format "08:00 => 09:00" and convert to "8 => 9"
                  if (time.includes(':')) {
                    const [start, end] = time.split('=>').map((t) => t.trim())
                    const startHour = parseInt(start.split(':')[0], 10)
                    const endHour = parseInt(end.split(':')[0], 10)
                    return `${startHour} => ${endHour}`
                  }
                  return time
                }

                // Otherwise, convert it to the correct format
                const [start, end] = time.split('-').map((t) => t.trim())
                // If the time contains a colon, extract just the hour
                if (start.includes(':') || end.includes(':')) {
                  const startHour = start.includes(':')
                    ? parseInt(start.split(':')[0], 10)
                    : start
                  const endHour = end.includes(':')
                    ? parseInt(end.split(':')[0], 10)
                    : end
                  return `${startHour} => ${endHour}`
                }
                return `${start} => ${end}`
              })
            }
          })
        }
        // Note: We always include unavailableTimes even if it's empty

        // Get subject names from IDs
        const subjectNames = teacher.subjects
          .map((subjectId) => {
            const subject = timetableData.subjects.find(
              (s) => s.id === subjectId
            )
            return subject ? subject.name : ''
          })
          .filter(Boolean)

        // If no subjects were found, add a default one
        if (subjectNames.length === 0 && timetableData.subjects.length > 0) {
          subjectNames.push(timetableData.subjects[0].name)
        }

        return {
          teacherId: teacher.id, // Use the actual UUID of the teacher
          teacherName: teacher.name,
          subjects: subjectNames,
          unavailableTimes,
          minimumHours: teacher.minimumHours || 10,
          maximumHours: teacher.maximumHours || 20,
        }
      })

    // Ensure we have at least some data in each section
    if (salles.length === 0) {
      salles = [
        { name: 'Salle 1', type: 'room' },
        { name: 'Salle 2', type: 'room' },
        { name: 'Salle 3', type: 'room' },
      ]
    }

    // Check if we have any lessons defined
    if (timetableData.lessons.length === 0) {
      console.log('No lessons defined, cannot generate timetable')
      // Don't create fake lessons, let the API handle the error
    }

    // Check if we have any teachers
    if (teachers.length === 0) {
      // If no teachers are found, show an error
      console.log('No teachers found for the selected lessons')
    }

    // Final formatted data
    const formattedData = {
      timeSlots,
      salles,
      classes,
      teachers,
    }

    // Don't log the formatted data here, it will be logged in the API service
    return formattedData
  }

  // Helper function to find a teacher's name by ID
  const findTeacherNameById = (teacherId: string, teachers: any[]): string => {
    if (!teacherId) return 'Unknown Teacher'
    const teacher = teachers.find((t) => t.id === teacherId)
    return teacher ? teacher.name : 'Unknown Teacher'
  }

  // Test connection to the backend service
  const testBackendConnection = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'https://timetable.jeridschool.tech'}/health`
      )
      if (response.ok) {
        console.log('Backend connection successful')
        // Connection successful, but no toast notification
      } else {
        console.error('Backend connection failed with status:', response.status)
      }
    } catch (error) {
      console.error('Backend connection failed:', error)
      // Backend connection test failed, but no toast notification
    }
  }

  // Fetch timetable data from the external API
  const fetchTimetableFromExternalAPI = async () => {
    try {
      // Show loading toast
      toast({
        title: 'Generating Timetable',
        description: 'This may take a moment. Please wait...',
      })

      // Generate the timetable data in the required format for the external API
      const externalParams = generateTimetableApiData()

      // Format the data according to the CreateTimetableDto
      const requestBody = {
        data: externalParams,
        description:
          timetableData.name ||
          `Timetable generated on ${new Date().toLocaleDateString()}`,
        academicYear:
          timetableData.academicYear || new Date().getFullYear().toString(),
        isActive: true,
      }

      // Log the exact data being sent to the server
      console.log('Sending data to API:', JSON.stringify(requestBody, null, 2))

      // Make a POST request to the generate-schedule endpoint
      let response
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries) {
        response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || 'https://timetable.jeridschool.tech'}/generate-schedule`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
            },
            body: JSON.stringify(requestBody),
          }
        )

        // If we get a 422 error, wait a moment and try again
        if (response.status === 422) {
          console.log(
            `Received 422 error, retrying (${retryCount + 1}/${maxRetries})...`
          )
          retryCount++
          // Wait for 1 second before retrying
          await new Promise((resolve) => setTimeout(resolve, 1000))
        } else {
          // For any other status, break out of the loop
          break
        }
      }

      // Check if we have a response
      if (!response) {
        throw new Error('Failed to get a response from the server')
      }

      if (!response.ok) {
        // Try to get the error details from the response
        try {
          const errorData = await response.json()
          console.error('Server error details:', errorData)
          throw new Error(
            `Failed to fetch timetable data: ${response.status} - ${JSON.stringify(errorData)}`
          )
        } catch (parseError) {
          // If we can't parse the error response, just throw the status
          console.error('Error parsing server response:', parseError)
          throw new Error(`Failed to fetch timetable data: ${response.status}`)
        }
      }

      const data = await response.json()
      console.log('Fetched timetable data:', data)

      // Process the data to ensure groups are properly handled
      let scheduleData = data.scheduleData || data

      // Make sure scheduleData is properly formatted with groups if present
      if (Array.isArray(scheduleData)) {
        console.log(
          'Processing scheduleData to ensure groups are properly handled'
        )

        // Process each class schedule
        scheduleData = scheduleData.map((classSchedule) => {
          const result = { ...classSchedule }

          // Process each day
          Object.keys(classSchedule).forEach((day) => {
            if (day !== 'class' && Array.isArray(classSchedule[day])) {
              // Process each period in the day
              result[day] = classSchedule[day].map((period) => {
                // If the period has group information, make sure it's properly structured
                if (period && (period.group1 || period.group2)) {
                  return {
                    ...period,
                    // Ensure teacher name is set if only teacherID is available
                    teacher:
                      period.teacher ||
                      findTeacherNameById(
                        period.teacherID,
                        timetableData.teachers
                      ),
                    // Ensure group1 is properly formatted
                    group1: period.group1
                      ? {
                          subject: period.group1.subject || period.subject,
                          teacher:
                            period.group1.teacher ||
                            findTeacherNameById(
                              period.group1.teacherID,
                              timetableData.teachers
                            ) ||
                            period.teacher,
                          salle: period.group1.salle || period.salle,
                        }
                      : null,
                    // Ensure group2 is properly formatted
                    group2: period.group2
                      ? {
                          subject: period.group2.subject || period.subject,
                          teacher:
                            period.group2.teacher ||
                            findTeacherNameById(
                              period.group2.teacherID,
                              timetableData.teachers
                            ) ||
                            period.teacher,
                          salle: period.group2.salle || period.salle,
                        }
                      : null,
                  }
                }

                // For regular periods, ensure teacher name is set if only teacherID is available
                if (period && period.teacherID && !period.teacher) {
                  return {
                    ...period,
                    teacher: findTeacherNameById(
                      period.teacherID,
                      timetableData.teachers
                    ),
                  }
                }
                return period
              })
            }
          })

          return result
        })
      }

      // Set the generated timetable with the processed data
      const formattedResult = {
        id: String(Date.now()),
        name:
          timetableData.name || `Timetable ${new Date().toLocaleDateString()}`,
        scheduleData: scheduleData,
        // Include the original input data that was sent to port 8000
        originalInputData: externalParams,
      }

      setGeneratedTimetable(formattedResult)

      // Save the generated timetable to the backend so it persists after reload
      try {
        console.log('Attempting to save timetable to database...')

        // Import the dashboardTimetableService dynamically to avoid circular deps
        const { dashboardTimetableService } = await import(
          '@/lib/api/services/dashboard-timetable-service'
        )

        // Prepare the save data with inputData included
        const saveData = {
          data: {
            ...formattedResult,
            // Include the original input data inside the data field for compatibility with analyze feature
            data: externalParams,
          },
          description: formattedResult.name,
          academicYear:
            timetableData.academicYear || new Date().getFullYear().toString(),
          isActive: true,
          // Include the original input data at the top level to ensure it's saved in the API
          inputData: externalParams,
        }

        console.log('Saving timetable with data structure:', {
          dataKeys: Object.keys(saveData.data),
          description: saveData.description,
          academicYear: saveData.academicYear,
          hasInputData: !!saveData.inputData,
        })

        // Get the token for logging purposes
        const token = localStorage.getItem('access_token')
        console.log(
          'Using token for saving:',
          token ? 'Token exists' : 'No token'
        )

        // Save the timetable to the dashboard service (api.jeridschool.tech)
        const savedTimetable =
          await dashboardTimetableService.saveTimetable(saveData)

        console.log('Timetable saved successfully with ID:', savedTimetable.id)

        // Store the timetable ID in localStorage for reference
        localStorage.setItem('lastSavedTimetableId', savedTimetable.id)

        toast({
          title: 'Timetable Saved',
          description:
            'Your timetable has been saved and will be available after reload.',
        })

        // Now also save to the server using the same method as the "Save to Server" button
        try {
          // Get the display data in the format expected by the server
          const displayData = {
            ...formattedResult,
            // Make sure we have the original input data
            originalInputData: externalParams,
          }

          // Create a function to save to server similar to the "Save to Server" button
          const saveTimetableToServer = async (data: any): Promise<void> => {
            // Get the authentication token from localStorage
            const token = localStorage.getItem('access_token')
            if (!token) {
              console.error('No authentication token found')
              return
            }

            // Determine the URL and method
            const url = 'http://localhost:3000/timetable'
            const method = 'POST'

            // Format the entries for the server
            const entries: Array<{
              className: string
              day: string
              subject: string
              teacher: string
              teacherID?: string
              salle?: string
              time: string
              group1?: any
              group2?: any
            }> = []
            if (data.scheduleData && Array.isArray(data.scheduleData)) {
              data.scheduleData.forEach((classData: any) => {
                const className = classData.class

                // Process each day
                Object.keys(classData).forEach((day) => {
                  // Skip the class property
                  if (day === 'class') return

                  // Ensure the day's schedule is an array
                  if (Array.isArray(classData[day])) {
                    // Process each period in the day
                    classData[day].forEach((period: any) => {
                      if (period && period.subject) {
                        // Ensure teacher name is set if only teacherID is available
                        const teacherName =
                          period.teacher ||
                          findTeacherNameById(
                            period.teacherID,
                            timetableData.teachers
                          )

                        entries.push({
                          className,
                          day,
                          subject: period.subject,
                          teacher: teacherName,
                          teacherID: period.teacherID,
                          salle: period.salle,
                          time: period.time,
                          // Add group information if present and ensure teacher names are set
                          group1: period.group1
                            ? {
                                ...period.group1,
                                teacher:
                                  period.group1.teacher ||
                                  findTeacherNameById(
                                    period.group1.teacherID,
                                    timetableData.teachers
                                  ) ||
                                  teacherName,
                              }
                            : period.group1,
                          group2: period.group2
                            ? {
                                ...period.group2,
                                teacher:
                                  period.group2.teacher ||
                                  findTeacherNameById(
                                    period.group2.teacherID,
                                    timetableData.teachers
                                  ) ||
                                  teacherName,
                              }
                            : period.group2,
                        })
                      }
                    })
                  }
                })
              })
            }

            // Prepare the server data according to CreateTimetableDto
            const serverData = {
              data: {
                entries,
                // Include the original input data inside the data field for compatibility with analyze feature
                data: externalParams,
              },
              description: data.name || 'Generated Timetable',
              academicYear:
                timetableData.academicYear ||
                new Date().getFullYear().toString(),
              isActive: true,
              // Also include inputData at the top level to ensure it's saved in the API
              inputData: externalParams,
              // Do not include etablissementId as it will be auto-handled by the backend
            }

            // Log the inputData to verify it's being included
            console.log(
              'InputData being sent to server in POST request:',
              JSON.stringify(externalParams).substring(0, 100) + '...'
            )

            // Send the data to the server
            console.log(
              'Saving timetable to server:',
              url,
              'with method:',
              method
            )

            const response = await fetch(url, {
              method: method,
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(serverData),
            })

            if (response.ok) {
              console.log('Timetable data saved successfully to server')
              toast({
                title: 'Timetable Saved to Server',
                description: 'The timetable has been saved to the server.',
              })
            } else {
              console.error(
                'Failed to save timetable data to server:',
                response.status,
                response.statusText
              )
              toast({
                title: 'Server Save Failed',
                description: `Failed to save timetable data to server: ${response.status} (${response.statusText})`,
                variant: 'destructive',
              })
            }
          }

          // Call the function to save to server
          await saveTimetableToServer(displayData)
        } catch (serverError) {
          console.error('Error saving to server:', serverError)
          toast({
            title: 'Server Save Failed',
            description:
              'An error occurred while saving the timetable to the server.',
            variant: 'destructive',
          })
        }
      } catch (error) {
        const saveError = error as any
        console.error('Error saving generated timetable to backend:', saveError)
        console.error('Error details:', {
          message: saveError.message || 'Unknown error',
          response: saveError.response?.data,
          status: saveError.response?.status,
        })

        toast({
          title: 'Error Saving Timetable',
          description:
            'The timetable was generated but could not be saved to the backend. Check console for details.',
          variant: 'destructive',
        })
      }

      // Notify the parent component if a callback was provided
      if (onTimetableGenerated) {
        onTimetableGenerated(formattedResult)
      }

      // Show success notification
      const classCount = data.scheduleData
        ? data.scheduleData.length
        : Array.isArray(data)
          ? data.length
          : 'multiple'
      toast({
        title: 'Timetable Generated Successfully',
        description: `Generated timetable with ${classCount} classes.`,
        variant: 'default',
      })

      // Log success message with retry information
      if (retryCount > 0) {
        console.log(
          `Timetable generated successfully after ${retryCount} retries.`
        )
      } else {
        console.log('Timetable generated successfully on first attempt.')
      }

      // Move to the results step
      setCurrentStep(8)
      setIsGenerating(false)

      return data
    } catch (error: any) {
      console.error('Error fetching timetable data:', error)

      // Determine if this is a 422 error that we should handle specially
      let errorMessage =
        error.message ||
        'An error occurred while generating the timetable data.'

      // Check if the error message contains a 422 status code
      if (errorMessage.includes('422')) {
        errorMessage =
          'The server is processing your request. This may take a moment. Please try again.'
        // Don't show the error toast for 422 errors since we're handling them with retries
        console.log('422 error detected, handled by retry mechanism')
      } else {
        toast({
          title: 'Failed to Generate Timetable',
          description: errorMessage,
          variant: 'destructive',
        })
      }

      setIsGenerating(false)
      return null
    }
  }

  // Track component mounting and step changes
  useEffect(() => {
    console.log(
      'TimetableGenerator component mounted or currentStep changed to:',
      currentStep
    )
    // Test backend connection on mount
    testBackendConnection()
  }, [currentStep])

  // Handle input changes for timetable data
  const handleInputChange = (field: string, value: any) => {
    setTimetableData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      }

      // If periodCount changes, we need to regenerate periodTimings
      if (field === 'periodCount' && value !== prev.periodCount) {
        console.log(`Period count changed from ${prev.periodCount} to ${value}`)
        // Clear periodTimings to force regeneration
        newData.periodTimings = undefined
      }

      return newData
    })
  }

  // Note: The BasicTimetableInfo component now handles constraints directly

  // Navigation functions
  const handleNextStep = () => {
    const nextStep = Math.min(currentStep + 1, totalSteps)

    // If moving to the Review & Generate step, prepare the timetable data
    if (nextStep === 7) {
      // Just prepare the UI, don't call the API yet
      // We'll only call the API when the user clicks "Generate Timetable"
    }

    // If moving to the final step, prepare for generation
    if (nextStep === 8) {
      // Don't log or generate anything here, just move to the next step
      // The user will need to click the "Generate Timetable" button
    }

    // Move to the next step without logging
    setCurrentStep(nextStep)
  }

  const handlePreviousStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  // We no longer need the handleTimeslotsUpdate function since we're using periodTimings directly

  // Generate timetable
  const handleGenerateTimetable = async () => {
    setIsGenerating(true)

    try {
      // Show a toast notification that we're generating the timetable
      toast({
        title: 'Generating Timetable',
        description:
          'Please wait while we generate your timetable. This may take a moment...',
      })

      // First try to fetch from the external API at port 8000
      const externalResult = await fetchTimetableFromExternalAPI()
      if (externalResult) {
        // If we successfully fetched data from the external API, we're done
        return
      }

      // If external API fetch failed, fall back to the original generation method
      // Check if there are any lessons defined
      if (timetableData.lessons.length === 0) {
        toast({
          title: 'No Lessons Defined',
          description:
            'Please define at least one lesson before generating a timetable.',
          variant: 'destructive',
        })
        setIsGenerating(false)
        return
      }

      // Generate the timetable data in the required format for the external API
      const externalParams = generateTimetableApiData()

      // Call the external timetable generator API
      let result
      try {
        // Call the external timetable API (logging is handled in the service)
        result =
          await timetableService.generateExternalTimetable(externalParams)

        // After successful generation, save to internal API
        try {
          console.log(
            'Attempting to save timetable to database using timetableService...'
          )

          // Prepare the save data
          const saveData = {
            name: timetableData.name || 'Generated Timetable',
            data: {
              ...result,
              // Include the original input data inside the data field for compatibility with analyze feature
              data: externalParams,
            },
            // Include the original input data that was sent to timetable.jeridschool.tech
            originalInputData: externalParams,
            constraints: JSON.stringify(timetableData.constraints || {}),
            // Add inputData explicitly at the top level to ensure it's saved in the API
            inputData: externalParams,
          }

          // Log the inputData to verify it's being included
          console.log(
            'InputData being sent to server:',
            JSON.stringify(externalParams).substring(0, 100) + '...'
          )

          console.log('Saving timetable with data structure:', {
            dataKeys: Object.keys(saveData),
            name: saveData.name,
            hasInputData: !!saveData.inputData,
            constraintsLength: saveData.constraints.length,
          })

          // Get the token for logging purposes
          const token = localStorage.getItem('access_token')
          console.log(
            'Using token for saving:',
            token ? 'Token exists' : 'No token'
          )

          // Save the timetable
          const savedTimetable = await timetableService.saveTimetable(saveData)

          console.log(
            'Timetable saved successfully with ID:',
            savedTimetable.id
          )

          // Store the timetable ID in localStorage for reference
          localStorage.setItem('lastSavedTimetableId', savedTimetable.id)

          // Timetable saved to internal API
          toast({
            title: 'Timetable Saved',
            description: 'Your timetable has been saved to the database.',
          })

          // Suggest viewing the timetable dashboard
          toast({
            title: 'View All Timetables',
            description:
              'Go to the Timetable Dashboard to view all your saved timetables.',
            action: (
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  (window.location.href = '/admin/timetable-dashboard')
                }
              >
                Go to Dashboard
              </Button>
            ),
          })
        } catch (error) {
          const saveError = error as any
          console.error('Error saving timetable to internal API:', saveError)
          console.error('Error details:', {
            message: saveError.message || 'Unknown error',
            response: saveError.response?.data,
            status: saveError.response?.status,
          })

          toast({
            title: 'Error Saving Timetable',
            description:
              'The timetable was generated but could not be saved to the database. Check console for details.',
            variant: 'destructive',
          })
          // Continue even if saving fails
        }
      } catch (apiError: any) {
        console.error('API Error:', apiError)
        toast({
          title: 'Timetable Generation Failed',
          description:
            apiError.message ||
            'Failed to connect to the timetable generator API',
          variant: 'destructive',
        })
        setIsGenerating(false)
        return
      }

      // Convert the result to a format that GeneratedTimetable expects
      const scheduleArray: any[] = []

      // Check if we have a valid result
      if (!result) {
        throw new Error('No response from timetable generator API')
      }

      // If the result has scheduleData property, process it
      if (result.scheduleData) {
        console.log('Processing scheduleData format')
        // Transform the data to the expected format
        result.scheduleData.forEach((classSchedule: any) => {
          const className = classSchedule.class

          // Process each day's schedule
          Object.entries(classSchedule).forEach(([day, periods]) => {
            if (day !== 'class' && Array.isArray(periods)) {
              periods.forEach((period: any) => {
                // Format the period data to match what the timetable display expects
                scheduleArray.push({
                  class: className,
                  day,
                  subject: period.subject,
                  teacher: period.teacher,
                  room: period.salle,
                  start: period.time ? period.time.split(' => ')[0] : '',
                  end: period.time ? period.time.split(' => ')[1] : '',
                  period:
                    scheduleArray.filter(
                      (a) => a.class === className && a.day === day
                    ).length + 1,
                })
              })
            }
          })
        })
      } else if (result.schedule) {
        console.log('Processing schedule format')
        // Transform the data to the expected format
        Object.entries(result.schedule).forEach(([className, periods]) => {
          // Check if periods is an array before iterating
          if (Array.isArray(periods)) {
            periods.forEach((period: any) => {
              scheduleArray.push({
                class: className,
                ...period,
              })
            })
          }
        })
      } else {
        // If no recognized format is found, create a simple schedule
        console.log(
          'No recognized schedule format found, creating default schedule'
        )

        // Create a simple schedule for each class
        timetableData.classes.forEach((cls) => {
          const className = cls.name

          // Add periods for each day
          Object.keys(externalParams.timeSlots).forEach((day) => {
            const studyTimes = externalParams.timeSlots[day].studyTimes

            // Add a period for each study time
            studyTimes.forEach((timeSlot, index) => {
              const [start, end] = timeSlot.split(' => ')

              // Find lessons for this class
              const classLessons = timetableData.lessons.filter(
                (lesson) => lesson.class === cls.id
              )

              if (classLessons.length > 0) {
                const lessonIndex = index % classLessons.length
                const lesson = classLessons[lessonIndex]

                // Find subject and teacher
                const subject = timetableData.subjects.find(
                  (s) => s.id === lesson.subject
                )
                const teacher = timetableData.teachers.find(
                  (t) => t.id === lesson.teacher
                )

                scheduleArray.push({
                  class: className,
                  day,
                  start,
                  end,
                  subject: subject ? subject.name : 'Default Subject',
                  teacher: teacher ? teacher.name : 'Default Teacher',
                  room: externalParams.salles[
                    index % externalParams.salles.length
                  ],
                })
              } else {
                // If no lessons, don't add fake ones
                console.log('No lessons defined for class:', className)
                // Skip adding fake lessons
              }
            })
          })
        })
      }

      // Prepare the result to pass to the parent component
      const formattedResult: any = {
        id: String(Date.now()), // Generate an ID
        name:
          timetableData.name || `Timetable ${new Date().toLocaleDateString()}`,
      }

      // If we have scheduleData from the API, use it directly
      if (result.scheduleData) {
        formattedResult.scheduleData = result.scheduleData
      } else {
        // Otherwise use our processed schedule array
        formattedResult.schedule = scheduleArray
      }

      // Add stats
      formattedResult.stats = {
        classes: timetableData.classes.length,
        teachers: timetableData.teachers.length,
        rooms: timetableData.rooms.length,
        periods: Object.values(externalParams.timeSlots).reduce(
          (acc, day) => Math.max(acc, day.studyTimes.length),
          0
        ),
        days: Object.keys(externalParams.timeSlots).length,
        assignments: scheduleArray.length,
      }

      setGeneratedTimetable(formattedResult)

      // Notify the parent component if a callback was provided
      if (onTimetableGenerated) {
        onTimetableGenerated(formattedResult)
      }

      // Show success notification
      toast({
        title: 'Timetable Generated Successfully',
        description:
          'Your timetable has been generated with ' +
          scheduleArray.length +
          ' assignments.',
      })

      // Move to the results step
      setCurrentStep(8)
    } catch (error: any) {
      // Error generating timetable
      console.error('Timetable generation error:', error)
      toast({
        title: 'Timetable Generation Failed',
        description:
          error.message ||
          'An error occurred while generating the timetable. Please check the console for details.',
        variant: 'destructive',
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // Render the step content based on the current step
  const renderStepContent = () => {
    console.log('Rendering step content for step:', currentStep)
    switch (currentStep) {
      case 1:
        return (
          <BasicTimetableInfo
            timetableData={timetableData}
            handleInputChange={handleInputChange}
          />
        )
      case 2:
        return (
          <PeriodTimingsDisplay
            timetableData={timetableData}
            handleInputChange={handleInputChange}
          />
        )
      case 3:
        return (
          <SubjectsConfiguration
            subjects={timetableData.subjects}
            isLoading={subjectsQuery.isLoading}
            onAddSubject={(subject) => {
              setTimetableData((prev) => ({
                ...prev,
                subjects: [...prev.subjects, subject],
              }))
            }}
            onDeleteSubject={(subjectId) => {
              setTimetableData((prev) => ({
                ...prev,
                subjects: prev.subjects.filter((s) => s.id !== subjectId),
              }))
            }}
          />
        )
      case 4:
        return (
          <TeachersConfiguration
            teachers={timetableData.teachers}
            subjects={timetableData.subjects}
            isLoading={teachersQuery.isLoading}
            onAddTeacher={(teacher) => {
              setTimetableData((prev) => ({
                ...prev,
                teachers: [...prev.teachers, teacher],
              }))
            }}
            onDeleteTeacher={(teacherId) => {
              setTimetableData((prev) => ({
                ...prev,
                teachers: prev.teachers.filter((t) => t.id !== teacherId),
              }))
            }}
            onUpdateTeacherAvailability={(teacherId, unavailableTimes) => {
              setTimetableData((prev) => ({
                ...prev,
                teachers: prev.teachers.map((teacher) =>
                  teacher.id === teacherId
                    ? { ...teacher, unavailableTimes }
                    : teacher
                ),
              }))
            }}
          />
        )
      case 5:
        return (
          <ClassesConfiguration
            classes={timetableData.classes}
            isLoading={classesQuery.isLoading}
            onAddClass={(classItem) => {
              setTimetableData((prev) => ({
                ...prev,
                classes: [...prev.classes, classItem],
              }))
            }}
            onDeleteClass={(classId) => {
              setTimetableData((prev) => ({
                ...prev,
                classes: prev.classes.filter((c) => c.id !== classId),
              }))
            }}
          />
        )
      case 6:
        return (
          <ClassroomsConfiguration
            classrooms={timetableData.rooms}
            isLoading={classroomsQuery.isLoading}
            onAddClassroom={(classroom) => {
              setTimetableData((prev) => ({
                ...prev,
                rooms: [...prev.rooms, classroom],
              }))
            }}
            onDeleteClassroom={(classroomId) => {
              setTimetableData((prev) => ({
                ...prev,
                rooms: prev.rooms.filter((r) => r.id !== classroomId),
              }))
            }}
          />
        )
      case 7:
        console.log('Rendering LessonsConfiguration component')
        console.log('Current timetable data:', {
          lessons: timetableData.lessons.length,
          classes: timetableData.classes.length,
          subjects: timetableData.subjects.length,
          teachers: timetableData.teachers.length,
        })
        return (
          <LessonsConfiguration
            lessons={timetableData.lessons}
            classes={timetableData.classes}
            subjects={timetableData.subjects}
            teachers={timetableData.teachers}
            rooms={timetableData.rooms}
            onAddLesson={(lesson) => {
              console.log('Adding lesson:', lesson)
              setTimetableData((prev) => ({
                ...prev,
                lessons: [...prev.lessons, lesson],
              }))
            }}
            onDeleteLesson={(index) => {
              console.log('Deleting lesson at index:', index)
              setTimetableData((prev) => ({
                ...prev,
                lessons: prev.lessons.filter((_, i) => i !== index),
              }))
            }}
          />
        )
      case 8:
        // If we haven't generated a timetable yet, show a message to generate one
        if (!generatedTimetable && !isGenerating) {
          return (
            <div className="space-y-4 p-6 border rounded-lg bg-muted/20">
              <h2 className="text-xl font-semibold">
                Ready to Generate Timetable
              </h2>
              <p className="text-muted-foreground">
                Click the "Generate Timetable" button below to create your
                timetable.
              </p>
              <Button
                onClick={handleGenerateTimetable}
                className="bg-green-600 hover:bg-green-700"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Generate Timetable
                  </>
                )}
              </Button>
            </div>
          )
        }

        // If we're generating or have generated a timetable, show the results
        return (
          <GenerationResults
            isGenerating={isGenerating}
            success={!!generatedTimetable && !isGenerating}
            error={
              isGenerating
                ? undefined
                : generatedTimetable
                  ? undefined
                  : 'Failed to generate timetable'
            }
            timetableData={
              generatedTimetable
                ? {
                    assignments: generatedTimetable.schedule || [],
                    timeslots: timetableData.periodTimings
                      ? [
                          ...new Set(
                            timetableData.schoolDays || [
                              'Monday',
                              'Tuesday',
                              'Wednesday',
                              'Thursday',
                              'Friday',
                            ]
                          ),
                        ].map((day) => ({
                          day,
                          slots: timetableData.periodTimings
                            ? timetableData.periodTimings.map((timing) => ({
                                start: timing.startTime,
                                end: timing.endTime,
                                isBreak: timing.isBreak || false,
                              }))
                            : [],
                        }))
                      : [],
                    name: timetableData.name || 'New Timetable',
                    classes: timetableData.classes,
                    teachers: timetableData.teachers,
                    subjects: timetableData.subjects,
                  }
                : undefined
            }
            onRegenerate={handleGenerateTimetable}
            onSave={() => {
              // Save timetable without notification
            }}
            onExport={() => {
              // Export timetable without notification
            }}
          />
        )
      default:
        return <div>Unknown step</div>
    }
  }

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="hidden md:block">
        <div className="flex justify-between">
          <div
            className={`flex items-center ${
              currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 1 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <Settings size={18} />
            </div>
            <span className="font-medium">General Settings</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 2 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <Clock size={18} />
            </div>
            <span className="font-medium">Period Timings</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 3 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 3 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <BookOpen size={18} />
            </div>
            <span className="font-medium">Subjects/Courses</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 4 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 4 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <User size={18} />
            </div>
            <span className="font-medium">Teachers</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 5 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 5 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <GraduationCap size={18} />
            </div>
            <span className="font-medium">Classes/Grades</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 6 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 6 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <BookOpen size={18} />
            </div>
            <span className="font-medium">Classrooms</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 7 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 7 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <BookOpen size={18} />
            </div>
            <span className="font-medium">Lessons</span>
          </div>

          <div
            className={`flex items-center ${
              currentStep >= 8 ? 'text-primary' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 ${
                currentStep >= 8 ? 'bg-primary text-white' : 'bg-muted'
              }`}
            >
              <Calendar size={18} />
            </div>
            <span className="font-medium">Review & Generate</span>
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mb-4">
        <Button
          variant="outline"
          onClick={() => {
            console.log('Previous button clicked, current step:', currentStep)
            handlePreviousStep()
          }}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <Button
          onClick={handleNextStep}
          className="bg-primary hover:bg-primary/90 text-white font-medium"
          disabled={currentStep >= 8}
        >
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Content for each step */}
      <Card className="shadow-lg border-0">
        <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 pb-4 sticky top-0 z-10">
          <CardTitle>
            {(() => {
              let title = ''
              if (currentStep === 1) title = 'Basic Timetable Settings'
              else if (currentStep === 2) title = 'Period Timings Configuration'
              else if (currentStep === 3) title = 'Add Subjects/Courses'
              else if (currentStep === 4) title = 'Teacher Information'
              else if (currentStep === 5) title = 'Define Classes or Grades'
              else if (currentStep === 6) title = 'Configure Classrooms'
              else if (currentStep === 7) title = 'Configure Lessons'
              else if (currentStep === 8) title = 'Review & Generate'
              console.log(
                'Setting card title for step',
                currentStep,
                ':',
                title
              )
              return title
            })()}
          </CardTitle>
        </CardHeader>

        <CardContent className="p-6 max-h-[calc(100vh-300px)] overflow-y-auto">
          {renderStepContent()}
        </CardContent>
      </Card>
    </div>
  )
}
