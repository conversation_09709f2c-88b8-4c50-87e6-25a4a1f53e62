import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '../../ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '../../ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../ui/select'
import { Input } from '../../ui/input'
import { Label } from '../../ui/label'
import { Textarea } from '../../ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { useToast } from '../../ui/use-toast'
import {
  salles,
  teachers,
  timeSlots,
  days,
} from '../../../mockData/admin/ScheduleViewData'

// Import the JSON data
import scheduleResult from './ScheduleResult.json'

import { Course, Schedule } from '@/interface/types'
import { dashboardTimetableService } from '@/lib/api/services/dashboard-timetable-service'
import { TimetableHistory } from './TimetableHistory'

export default function ViewSchedule() {
  const { toast } = useToast()
  const [schedule, setSchedule] = useState<Schedule>({})
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null)
  const [modalOpen, setModalOpen] = useState(false)
  const [saveModalOpen, setSaveModalOpen] = useState(false)
  const [newSubject, setNewSubject] = useState('')
  const [newTeacher, setNewTeacher] = useState('')
  const [newSalle, setNewSalle] = useState('')
  const [selectedClass, setSelectedClass] = useState('Ti1') // Default selected class
  const [timetableDescription, setTimetableDescription] = useState('')
  const [academicYear, setAcademicYear] = useState(
    `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`
  )
  const [isSaving, setIsSaving] = useState(false)

  // Get the user's etablissementId from localStorage
  const [etablissementId, setEtablissementId] = useState<string | null>(null)

  useEffect(() => {
    // Get the user's etablissementId from localStorage
    const userEtablissementId = localStorage.getItem('etablissementId')
    if (userEtablissementId) {
      setEtablissementId(userEtablissementId)
    } else {
      // If not found in localStorage, try to get it from the JWT token
      const token = localStorage.getItem('access_token')
      if (token) {
        try {
          const base64Url = token.split('.')[1]
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
          const jsonPayload = decodeURIComponent(
            atob(base64)
              .split('')
              .map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
              })
              .join('')
          )
          const payload = JSON.parse(jsonPayload)
          if (payload.etablissementId) {
            setEtablissementId(payload.etablissementId)
          } else if (payload.sub) {
            // Try to get the user ID and use it to fetch the etablissementId
            console.log('User ID from token:', payload.sub)
            // For development, set a default etablissementId
            if (import.meta.env.DEV) {
              setEtablissementId('11111111-1111-1111-1111-111111111111')
            }
          }
        } catch (error) {
          console.error('Error decoding token:', error)
        }
      }
    }

    // For development, set a default etablissementId if none was found
    if (import.meta.env.DEV && !userEtablissementId) {
      setEtablissementId('11111111-1111-1111-1111-111111111111')
    }
  }, [])

  // Load the schedule data for the selected class
  useEffect(() => {
    const classSchedule = scheduleResult.scheduleData.find(
      (data: any) => data.class === selectedClass
    )
    if (classSchedule) {
      // Destructure the `class` property and use the rest of the object
      const { class: _, ...scheduleWithoutClass } = classSchedule
      setSchedule(scheduleWithoutClass)
    }
  }, [selectedClass])

  const getScheduledCourse = (day: string, timeSlot: string): Course | null => {
    if (!schedule[day]) return null
    return (
      schedule[day].find((course: Course) => course.time === timeSlot) || null
    )
  }

  const handleCellClick = (
    day: string,
    timeSlot: string,
    course: Course | null
  ) => {
    setSelectedCourse(
      course || { day, time: timeSlot, subject: '', teacher: '', salle: '' }
    )
    setModalOpen(true)
    if (course) {
      setNewSubject(course.subject || '')
      setNewTeacher(course.teacher || '')
      setNewSalle(course.salle || '')
    } else {
      setNewSubject('')
      setNewTeacher('')
      setNewSalle('')
    }
  }

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewSubject(e.target.value)
  }

  const handleTeacherChange = (value: string) => {
    setNewTeacher(value)
  }

  const handleSalleChange = (value: string) => {
    setNewSalle(value)
  }

  const handleModalClose = () => {
    setModalOpen(false)
  }

  const handleSubmit = () => {
    if (selectedCourse && newSubject && newTeacher && newSalle) {
      setSchedule((prevSchedule) => {
        const newSchedule = { ...prevSchedule }
        const day = selectedCourse.day!
        const time = selectedCourse.time

        if (!newSchedule[day]) {
          newSchedule[day] = []
        }

        const courseIndex = newSchedule[day].findIndex(
          (course: Course) => course.time === time
        )
        const updatedCourse = {
          time,
          subject: newSubject,
          teacher: newTeacher,
          salle: newSalle,
        }

        if (courseIndex !== -1) {
          newSchedule[day][courseIndex] = updatedCourse
        } else {
          newSchedule[day].push(updatedCourse)
        }

        return newSchedule
      })
      setModalOpen(false)
    }
  }

  // Function to save the timetable to the backend
  const handleSaveTimetable = async () => {
    try {
      setIsSaving(true)

      // Check if we have an etablissementId
      if (!etablissementId) {
        toast({
          title: 'Error',
          description:
            'Could not determine your establishment ID. Please log out and log in again.',
          variant: 'destructive',
        })
        setIsSaving(false)
        return
      }

      // Format the data to match the backend API
      const timetableData = {
        data: scheduleResult,
        description: timetableDescription || `Timetable for ${selectedClass}`,
        academicYear: academicYear,
        isActive: true,
        etablissementId: etablissementId,
      }

      // Save the timetable
      await dashboardTimetableService.saveTimetable(timetableData)

      // Show success message
      toast({
        title: 'Timetable Saved',
        description:
          'Your timetable has been saved successfully. You can view it in the Saved Timetables tab.',
      })

      // Close the modal
      setSaveModalOpen(false)
      setTimetableDescription('')
    } catch (error) {
      console.error('Error saving timetable:', error)
      toast({
        title: 'Error Saving Timetable',
        description:
          'There was an error saving your timetable. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="w-full mx-auto">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Institut Supérieur des Études Technologiques de Tozeur
          </CardTitle>
          <CardDescription className="text-center">
            Département Technologies de l'Informatique
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="current" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="current">Current Timetable</TabsTrigger>
          <TabsTrigger value="history">Saved Timetables</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Current Timetable</CardTitle>
                <Button onClick={() => setSaveModalOpen(true)}>
                  Save Timetable
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <form className="space-y-4 w-full">
                {/* Class Selection Dropdown */}
                <div className="mb-4">
                  <Label htmlFor="class" className="font-semibold">
                    Classe
                  </Label>
                  <Select
                    value={selectedClass}
                    onValueChange={(value) => setSelectedClass(value)}
                  >
                    <SelectTrigger id="class" className="w-full">
                      <SelectValue placeholder="Choisir la classe" />
                    </SelectTrigger>
                    <SelectContent>
                      {scheduleResult.scheduleData.map((data: any) => (
                        <SelectItem key={data.class} value={data.class}>
                          {data.class}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Schedule Grid */}
                <div className="grid grid-cols-7 gap-2 mt-6 w-full">
                  <div className="font-bold">Time</div>
                  {days.map((day) => (
                    <div key={day} className="font-bold">
                      {day}
                    </div>
                  ))}

                  {timeSlots.map((timeSlot) => (
                    <React.Fragment key={`time-slot-${timeSlot}`}>
                      <div className="font-semibold">{timeSlot}</div>
                      {days.map((day) => {
                        const course = getScheduledCourse(day, timeSlot)
                        return (
                          <div
                            key={`${day}-${timeSlot}`}
                            onClick={() =>
                              handleCellClick(day, timeSlot, course)
                            }
                            className={`p-2 border rounded ${
                              course
                                ? 'bg-blue-50 hover:bg-blue-100 cursor-pointer'
                                : 'bg-gray-50 hover:bg-gray-100 cursor-pointer'
                            }`}
                          >
                            {course ? (
                              <div className="text-sm">
                                <div className="font-medium truncate">
                                  {course.subject}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {course.teacher}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {course.salle}
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-400">Libre</div>
                            )}
                          </div>
                        )
                      })}
                    </React.Fragment>
                  ))}
                </div>

                {/* Modal for Adding/Editing Courses */}
                {modalOpen && (
                  <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg w-1/3">
                      {selectedCourse && (
                        <h3 className="font-bold text-lg mb-4">
                          {selectedCourse.subject
                            ? 'Modifier le cours'
                            : 'Ajouter un cours'}
                        </h3>
                      )}

                      <div className="mb-4">
                        <Label htmlFor="subject" className="font-semibold">
                          Matière
                        </Label>
                        <Input
                          id="subject"
                          type="text"
                          value={newSubject}
                          onChange={handleSubjectChange}
                          className="w-full"
                          placeholder="Entrez le nom de la matière"
                        />
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="teacher" className="font-semibold">
                          Enseignant
                        </Label>
                        <Select
                          value={newTeacher}
                          onValueChange={handleTeacherChange}
                        >
                          <SelectTrigger id="teacher" className="w-full">
                            <SelectValue placeholder="Choisir l'enseignant" />
                          </SelectTrigger>
                          <SelectContent>
                            {teachers.map((teacher) => (
                              <SelectItem key={teacher} value={teacher}>
                                {teacher}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="salle" className="font-semibold">
                          Salle
                        </Label>
                        <Select
                          value={newSalle}
                          onValueChange={handleSalleChange}
                        >
                          <SelectTrigger id="salle" className="w-full">
                            <SelectValue placeholder="Choisir la salle" />
                          </SelectTrigger>
                          <SelectContent>
                            {salles.map((salle) => (
                              <SelectItem key={salle} value={salle}>
                                {salle}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex justify-between mt-4">
                        <Button onClick={handleModalClose} variant="outline">
                          Fermer
                        </Button>
                        <Button onClick={handleSubmit}>Enregistrer</Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Timetable Modal */}
                {saveModalOpen && (
                  <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg w-1/3">
                      <h3 className="font-bold text-lg mb-4">Save Timetable</h3>

                      <div className="mb-4">
                        <Label htmlFor="description" className="font-semibold">
                          Description
                        </Label>
                        <Textarea
                          id="description"
                          value={timetableDescription}
                          onChange={(e) =>
                            setTimetableDescription(e.target.value)
                          }
                          className="w-full"
                          placeholder="Enter a description for this timetable"
                        />
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="academicYear" className="font-semibold">
                          Academic Year
                        </Label>
                        <Input
                          id="academicYear"
                          type="text"
                          value={academicYear}
                          onChange={(e) => setAcademicYear(e.target.value)}
                          className="w-full"
                          placeholder="e.g. 2023-2024"
                        />
                      </div>

                      <div className="flex justify-between mt-4">
                        <Button
                          onClick={() => setSaveModalOpen(false)}
                          variant="outline"
                          disabled={isSaving}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleSaveTimetable}
                          disabled={isSaving}
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          <TimetableHistory />
        </TabsContent>
      </Tabs>
    </div>
  )
}
