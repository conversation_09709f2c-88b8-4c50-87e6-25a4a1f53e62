import { useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useToast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { Eye, Trash2, Loader2 } from 'lucide-react'
import {
  dashboardTimetableService,
  Timetable,
} from '@/lib/api/services/dashboard-timetable-service'
import { TimetableDisplay } from './TimetableDisplay'

export function TimetableHistory() {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [selectedTimetable, setSelectedTimetable] = useState<Timetable | null>(
    null
  )
  const [viewTimetable, setViewTimetable] = useState<boolean>(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)

  // Fetch timetables
  const {
    data: timetables,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['timetables'],
    queryFn: dashboardTimetableService.getCurrentEstablishmentTimetables,
  })

  // Format date for display
  const formatDate = (dateString: string | Date) => {
    try {
      const date =
        typeof dateString === 'string' ? new Date(dateString) : dateString
      return format(date, 'MMM d, yyyy')
    } catch (error) {
      return 'Invalid date'
    }
  }

  // Handle view timetable
  const handleViewTimetable = (timetable: Timetable) => {
    setSelectedTimetable(timetable)
    setViewTimetable(true)
  }

  // Handle delete timetable
  const handleDeleteClick = (timetable: Timetable) => {
    setSelectedTimetable(timetable)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedTimetable) return

    try {
      setIsDeleting(true)
      await dashboardTimetableService.deleteTimetable(selectedTimetable.id)

      // Invalidate the timetables query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['timetables'] })

      toast({
        title: 'Timetable Deleted',
        description: 'The timetable has been successfully deleted.',
      })

      setDeleteDialogOpen(false)
      setSelectedTimetable(null)

      // If we were viewing the timetable, go back to the list
      if (viewTimetable) {
        setViewTimetable(false)
      }
    } catch (error) {
      console.error('Error deleting timetable:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete the timetable. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      {viewTimetable && selectedTimetable ? (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Button variant="outline" onClick={() => setViewTimetable(false)}>
              Back to List
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDeleteClick(selectedTimetable)}
            >
              Delete Timetable
            </Button>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedTimetable.description || 'Timetable'}
                {selectedTimetable.academicYear && (
                  <span className="ml-2 text-muted-foreground text-sm">
                    ({selectedTimetable.academicYear})
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Created on {formatDate(selectedTimetable.createdAt)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimetableDisplay
                timetableData={selectedTimetable.data}
                onBack={() => setViewTimetable(false)}
                onRegenerate={() => {
                  toast({
                    title: 'Regenerate Not Available',
                    description:
                      'To regenerate, please create a new timetable.',
                  })
                }}
              />
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Saved Timetables</CardTitle>
            <CardDescription>
              View and manage your saved timetables
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <h3 className="text-lg font-medium">
                  Error Loading Timetables
                </h3>
                <p className="text-muted-foreground max-w-md mt-2">
                  There was a problem loading your timetables. Please try again.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => refetch()}
                >
                  Try Again
                </Button>
              </div>
            ) : timetables && timetables.length > 0 ? (
              <ScrollArea className="h-[500px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead>Academic Year</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {timetables.map((timetable) => (
                      <TableRow key={timetable.id}>
                        <TableCell className="font-medium">
                          {timetable.description || 'Untitled Timetable'}
                        </TableCell>
                        <TableCell>
                          {timetable.academicYear || 'Not specified'}
                        </TableCell>
                        <TableCell>
                          {timetable.isActive ? (
                            <Badge variant="default" className="bg-green-500">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline">Inactive</Badge>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(timetable.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewTimetable(timetable)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteClick(timetable)}
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <h3 className="text-lg font-medium">No Timetables Found</h3>
                <p className="text-muted-foreground max-w-md mt-2">
                  You haven't saved any timetables yet. Generate and save a
                  timetable to get started.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Timetable</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this timetable? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
