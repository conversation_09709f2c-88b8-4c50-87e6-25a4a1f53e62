import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

interface ConflictDialogProps {
  open: boolean
  onClose: () => void
  conflictData: {
    subject: string
    teacher: string
    class: string
    period: string
    day: string
    conflicts: {
      type: 'teacher' | 'class' | 'room'
      message: string
    }[]
  }
}

export function ConflictDialog({
  open,
  onClose,
  conflictData,
}: ConflictDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            Conflict Detected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-sm mb-2">
              We can't place{' '}
              <span className="font-semibold">{conflictData.subject}</span> here
              because:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-sm">
              {conflictData.conflicts.map((conflict, index) => (
                <li key={index} className="text-red-700">
                  {conflict.message}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="text-sm font-medium mb-2">Steps to resolve:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li className="text-sm">
                <span className="text-purple-600 font-medium">1.</span> Remove
                the existing conflicting lesson to the Parking area
              </li>
              <li className="text-sm">
                <span className="text-purple-600 font-medium">2.</span> Drop
                this lesson into the desired slot
              </li>
              <li className="text-sm">
                <span className="text-purple-600 font-medium">3.</span> Ensure
                no teacher or class is double-booked in the same period
              </li>
            </ol>
          </div>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Got it</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
