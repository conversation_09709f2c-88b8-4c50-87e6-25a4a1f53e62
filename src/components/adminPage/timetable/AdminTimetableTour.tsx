import { useState, useEffect } from 'react'
import Joyride, { Step, CallBackProps } from 'react-joyride'
import { useTourStore } from '@/store/tourStore'

export function AdminTimetableTour() {
  const [steps, setSteps] = useState<Step[]>([])

  // Use Zustand store for tour state
  const {
    adminTimetableTourActive: run,
    adminTimetableTourStep,
    adminTimetableTourCompleted,
    adminTimetableTourSkipped,
    startAdminTimetableTour,
    completeAdminTimetableTour,
    skipAdminTimetableTour,
    setAdminTimetableTourStep,
  } = useTourStore()

  useEffect(() => {
    // Define a simplified version of the tour with just the essential steps
    // No navigation to other pages, just show information about the current page
    const tourSteps: Step[] = [
      {
        target: 'body',
        content:
          'Welcome! This tour will guide you through the admin dashboard features.',
        placement: 'center',
        disableBeacon: true,
        title: 'Admin Dashboard',
        disableOverlay: false,
        disableScrolling: false,
        isFixed: true, // Keep the tooltip fixed in the center
      },
      {
        target: '.admin-dashboard-header, h2',
        content:
          'This is the main dashboard where you can access various services.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Services Overview',
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15, // Add more padding to the spotlight
      },
      // Static data for service cards to avoid lag
      {
        target: '.data-storage-card',
        content:
          'The Data Storage service allows you to manage and access all your school data including students, teachers, and classes.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Data Storage Service',
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15,
      },
      {
        target: '.timetable-card',
        content:
          'The Timetable service lets you create, view and manage class schedules for your school.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Timetable Service',
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15,
      },
      {
        target: '.admin-dashboard-stats, .quick-stats',
        content:
          'Here you can see quick statistics and overview of your system.',
        placement: 'top',
        disableBeacon: true,
        title: 'Quick Stats',
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15,
      },
      {
        target: 'body',
        content: "That's it! You now know the basics of the admin dashboard.",
        placement: 'center',
        disableBeacon: true,
        title: 'Tour Complete',
        disableOverlay: false,
        disableScrolling: false,
        isFixed: true, // Keep the tooltip fixed in the center
      },
    ]

    setSteps(tourSteps)

    // Listen for the start-admin-timetable-tour event
    const handleStartTour = () => {
      console.log('AdminTimetableTour: Starting tour from event')

      // Reset tour completion status
      localStorage.removeItem('tour_completed_admin_timetable')
      localStorage.removeItem('tour_skipped_admin_timetable')

      // Start the tour with a delay to ensure DOM is ready
      setTimeout(() => {
        console.log('AdminTimetableTour: Starting tour via Zustand')
        startAdminTimetableTour()
      }, 500)
    }

    // Listen for the stop-admin-timetable-tour event
    const handleStopTour = () => {
      console.log('AdminTimetableTour: Stopping tour from event')
      skipAdminTimetableTour() // This will stop the tour
    }

    window.addEventListener('start-admin-timetable-tour', handleStartTour)
    window.addEventListener('stop-admin-timetable-tour', handleStopTour)

    // Check if we should start the tour automatically
    const tourCompleted =
      adminTimetableTourCompleted ||
      localStorage.getItem('tour_completed_admin_timetable') === 'true'
    const tourSkipped =
      adminTimetableTourSkipped ||
      localStorage.getItem('tour_skipped_admin_timetable') === 'true'
    const userOnboarding = localStorage.getItem('userOnboarding') === 'true'

    // Check if this is triggered from localStorage
    const triggerTour =
      localStorage.getItem('trigger_admin_timetable_tour') === 'true'

    console.log('AdminTimetableTour: Checking tour status', {
      tourCompleted,
      tourSkipped,
      triggerTour,
      userOnboarding,
      adminTimetableTourStep,
    })

    if (!tourCompleted && !tourSkipped && (triggerTour || userOnboarding)) {
      console.log('AdminTimetableTour: Auto-starting tour')

      // Clear the trigger flag
      localStorage.removeItem('trigger_admin_timetable_tour')

      // Add a longer delay to ensure the DOM is fully ready
      setTimeout(() => {
        console.log('AdminTimetableTour: Starting tour via Zustand after delay')
        startAdminTimetableTour()
      }, 1000)
    }

    return () => {
      window.removeEventListener('start-admin-timetable-tour', handleStartTour)
      window.removeEventListener('stop-admin-timetable-tour', handleStopTour)
    }
  }, [])

  // Handle tour events
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { action, index, status, type } = data

    // Debug what's happening with the tour
    console.log('AdminTimetableTour callback:', {
      action,
      index,
      status,
      type,
      step: steps[index],
    })

    // Handle actions based on the callback type
    switch (type) {
      case 'step:before':
        // Log when a step is about to be shown
        console.log(
          `AdminTimetableTour: Step ${index + 1}/${steps.length} is about to be shown`
        )
        break

      case 'step:after':
        // Log when a step has been shown
        console.log(
          `AdminTimetableTour: Step ${index + 1}/${steps.length} has been shown with action: ${action}`
        )

        // Save current step to localStorage for persistence
        localStorage.setItem('admin_timetable_tour_current_step', String(index))

        // Mark that we've seen this step
        localStorage.setItem(`admin_tour_step_${index}_seen`, 'true')

        // Don't auto-advance on the last step to prevent getting stuck
        if (index === steps.length - 1) {
          console.log('AdminTimetableTour: On last step, not auto-advancing')

          // Force complete the tour after a delay
          setTimeout(() => {
            console.log(
              'AdminTimetableTour: Forcing tour completion from last step'
            )
            completeAdminTimetableTour()
            localStorage.setItem('tour_completed_admin_timetable', 'true')
            localStorage.setItem('userOnboarding', 'false')

            // Force reload the page to ensure a clean state
            setTimeout(() => {
              window.location.reload()
            }, 500)
          }, 3000)

          break
        }

        // For other steps, don't auto-advance - let user click Next
        // This makes the tour faster and more responsive
        const nextIndex = index + 1
        if (nextIndex < steps.length) {
          console.log(`AdminTimetableTour: Ready for step ${nextIndex}`)
          // No auto-advancing - user will click Next button
        }
        break

      case 'tour:start':
        // Log when the tour starts
        console.log('AdminTimetableTour: Tour started')

        // Make sure the body has focus for keyboard navigation
        document.body.focus()
        break

      case 'tour:end':
        // Log when the tour ends
        console.log('AdminTimetableTour: Tour ended with status:', status)

        // Always mark the tour as completed regardless of status
        console.log('AdminTimetableTour: Marking tour as completed')
        completeAdminTimetableTour() // Use Zustand action
        localStorage.setItem('tour_completed_admin_timetable', 'true')

        // Set onboarding to FALSE (completed) in localStorage
        localStorage.setItem('userOnboarding', 'false')

        // Make API call to update onboarding status in the backend
        try {
          const token = localStorage.getItem('token')

          // First try the new endpoint
          fetch('http://localhost:3000/users/onboarding', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ userOnboarding: false }), // Backend expects false to mark as complete
          })
            .then((response) => {
              if (response.ok) {
                console.log(
                  'AdminTimetableTour: Onboarding status updated in backend',
                  response.status
                )
              } else {
                // If the first endpoint fails, try the alternative endpoint
                return fetch('http://localhost:3000/user/onboarding', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                  },
                  body: JSON.stringify({ userOnboarding: false }),
                })
              }
            })
            .then((response) => {
              if (response && response.ok) {
                console.log(
                  'AdminTimetableTour: Onboarding status updated with alternative endpoint',
                  response.status
                )
              }
            })
            .catch((error) => {
              console.error(
                'AdminTimetableTour: Failed to update onboarding status',
                error
              )
            })
        } catch (error) {
          console.error(
            'AdminTimetableTour: Error updating onboarding status',
            error
          )
        }

        // Dispatch an event to notify other components
        window.dispatchEvent(new CustomEvent('tour-status-change'))

        // Force reload the page to ensure a clean state
        setTimeout(() => {
          console.log(
            'AdminTimetableTour: Reloading page to ensure clean state'
          )
          window.location.reload()
        }, 1000)

        break

      case 'error:target_not_found':
        // Handle case where target element is not found
        console.error(
          `AdminTimetableTour: Target element "${steps[index]?.target}" not found`
        )

        // If we're at the last step, just end the tour
        if (index === steps.length - 1) {
          console.log(
            'AdminTimetableTour: At last step with error, ending tour'
          )
          completeAdminTimetableTour()
          localStorage.setItem('tour_completed_admin_timetable', 'true')
          localStorage.setItem('userOnboarding', 'false')
        } else {
          // For other steps, try to recover by moving to the next step
          console.log(
            'AdminTimetableTour: Attempting to recover by moving to the next step'
          )
          setTimeout(() => {
            console.log(`AdminTimetableTour: Skipping to step ${index + 1}`)
            setAdminTimetableTourStep(index + 1)
          }, 500)
        }
        break
    }
  }

  // Force the tour to run if it's active and add safety timeout
  useEffect(() => {
    if (run) {
      console.log('AdminTimetableTour: Tour is active, ensuring it runs')

      // If we're on step 0, force advance to step 1 immediately
      if (adminTimetableTourStep === 0) {
        console.log('AdminTimetableTour: Auto-advancing from step 0 to step 1')
        setAdminTimetableTourStep(1)
      }

      // Safety timeout - if the tour is running for too long, force complete it
      const safetyTimeout = setTimeout(() => {
        console.log(
          'AdminTimetableTour: Safety timeout reached, forcing tour completion'
        )
        completeAdminTimetableTour()
        localStorage.setItem('tour_completed_admin_timetable', 'true')
        localStorage.setItem('userOnboarding', 'false')

        // Force reload the page to ensure a clean state
        setTimeout(() => {
          window.location.reload()
        }, 500)
      }, 30000) // 30 seconds safety timeout

      return () => {
        clearTimeout(safetyTimeout)
      }
    }
  }, [run, adminTimetableTourStep])

  return (
    <Joyride
      steps={steps}
      run={run}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      disableOverlayClose={false} // Allow overlay closing to prevent getting stuck
      disableCloseOnEsc={false} // Allow ESC to close to prevent getting stuck
      spotlightClicks={true} // Enable spotlight clicks for better UX
      scrollToFirstStep={true}
      scrollOffset={100} // Larger scroll offset for better visibility
      scrollDuration={300} // Slightly slower scrolling for better experience
      hideBackButton={false}
      disableScrolling={false}
      disableScrollParentFix={false}
      disableOverlay={false}
      spotlightPadding={10} // More padding for better visibility
      stepIndex={adminTimetableTourStep}
      callback={handleJoyrideCallback}
      debug={true} // Enable debug mode to help troubleshoot
      locale={{
        back: 'Back',
        close: 'Close',
        last: 'Finish',
        next: 'Next',
        skip: 'Skip',
      }}
      floaterProps={{
        disableAnimation: false, // Enable animations
        hideArrow: false,
        offset: 15, // More offset for better positioning
        styles: {
          arrow: {
            length: 8, // Larger arrow
            spread: 12, // Larger spread
          },
          floater: {
            filter: 'drop-shadow(0 0 10px rgba(0, 0, 0, 0.4))', // Stronger shadow
            transition: 'all 0.3s ease', // Smoother transitions
          },
        },
      }}
      styles={{
        options: {
          zIndex: 10000,
          primaryColor: '#007bff',
          backgroundColor: '#ffffff',
          arrowColor: '#ffffff',
          overlayColor: 'rgba(0, 0, 0, 0.5)', // Darker overlay for better visibility
          textColor: '#333',
        },
        tooltip: {
          borderRadius: '8px',
          fontSize: '16px', // Larger font
          padding: '20px', // More padding
          backgroundColor: '#ffffff',
          color: '#333',
          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)', // Stronger shadow
          maxWidth: '450px', // Wider tooltip
        },
        tooltipContainer: {
          textAlign: 'center',
          padding: '10px',
        },
        tooltipTitle: {
          fontSize: '18px', // Larger title
          fontWeight: 'bold',
          marginBottom: '12px',
          color: '#007bff',
          textAlign: 'center',
        },
        tooltipContent: {
          fontSize: '16px', // Larger content
          lineHeight: '1.5',
          marginBottom: '15px',
          textAlign: 'center',
          color: '#333',
          padding: '5px',
        },
        buttonNext: {
          backgroundColor: '#007bff',
          fontSize: '16px', // Larger button text
          padding: '10px 20px', // Larger button
          borderRadius: '4px',
          color: '#fff',
          fontWeight: 'bold',
          border: 'none',
          cursor: 'pointer',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)', // Add shadow to button
        },
        buttonBack: {
          color: '#555',
          marginRight: '15px',
          fontSize: '16px', // Larger button text
          padding: '10px 20px', // Larger button
          fontWeight: 'bold',
        },
        buttonSkip: {
          color: '#666', // Darker color for better visibility
          fontSize: '16px', // Larger button text
          fontWeight: 'bold',
          padding: '10px',
        },
        spotlight: {
          backgroundColor: 'transparent',
          borderRadius: 8,
          boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.6)', // Darker shadow for better visibility
        },
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.6)', // Darker overlay
          mixBlendMode: 'normal',
        },
        beaconInner: {
          backgroundColor: '#007bff',
        },
        beaconOuter: {
          backgroundColor: 'rgba(0, 123, 255, 0.4)', // More visible beacon
          borderColor: '#007bff',
        },
      }}
    />
  )
}
