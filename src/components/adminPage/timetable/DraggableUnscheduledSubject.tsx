import { useDrag } from 'react-dnd'
import { User, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { UnscheduledSubject } from '@/types/timetable'
import { ItemTypes } from './UnscheduleZone'
import { useState } from 'react'

interface DraggableUnscheduledSubjectProps {
  subject: UnscheduledSubject
  onReschedule: (
    subject: UnscheduledSubject,
    day: string,
    time: string,
    room?: string
  ) => void
  isSelected?: boolean
  onSelect?: () => void
}

export function DraggableUnscheduledSubject({
  subject,
  onReschedule,
  isSelected = false,
  onSelect,
}: DraggableUnscheduledSubjectProps) {
  const [showAllSlots, setShowAllSlots] = useState(false)

  // Use the useDrag hook from react-dnd
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.UNSCHEDULED_SUBJECT,
    item: { subject },
    collect: (monitor: any) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }))

  // Handle click on an available slot
  const handleSlotClick = (slot: {
    day: string
    time: string
    available_rooms: string[]
  }) => {
    console.log('Slot clicked:', slot)
    // Use the first available room if any
    const room =
      slot.available_rooms.length > 0 ? slot.available_rooms[0] : undefined
    onReschedule(subject, slot.day, slot.time, room)
  }

  // Toggle showing all slots
  const toggleShowAllSlots = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent triggering drag
    setShowAllSlots(!showAllSlots)
  }

  // Sort available slots by day and time for better organization
  const sortedSlots = [...subject.available_slots].sort((a, b) => {
    // Define day order
    const dayOrder = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ]
    const dayA = dayOrder.indexOf(a.day)
    const dayB = dayOrder.indexOf(b.day)

    if (dayA !== dayB) return dayA - dayB

    // If same day, sort by time
    const timeA = a.time.split('=>')[0].trim()
    const timeB = b.time.split('=>')[0].trim()
    return parseInt(timeA) - parseInt(timeB)
  })

  // Determine how many slots to show
  const slotsToShow = showAllSlots ? sortedSlots : sortedSlots.slice(0, 3)

  return (
    <div
      ref={drag}
      className={`p-2 border rounded-md cursor-move ${isSelected ? 'border-2 border-blue-500 bg-blue-100 shadow-md' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      onClick={onSelect}
    >
      <div className="font-semibold">{subject.subject}</div>
      <div className="text-xs text-gray-600">{subject.class}</div>
      <div className="text-xs text-gray-600 flex items-center">
        <User className="h-3 w-3 mr-1" />
        {subject.teacherName}
      </div>
      {/* Display each missing hour as a separate badge */}
      <div className="text-xs mt-1 flex flex-wrap gap-1">
        {Array.from({ length: subject.uncompleted_hours }).map((_, i) => (
          <Badge key={i} variant="outline" className="bg-red-50">
            1 hour
          </Badge>
        ))}
      </div>
      {sortedSlots.length > 0 && (
        <div className="mt-2">
          <div className="text-xs font-medium mb-1 flex justify-between items-center">
            <span>Available slots:</span>
            {sortedSlots.length > 3 && (
              <button
                onClick={toggleShowAllSlots}
                className="text-xs text-blue-500 hover:text-blue-700"
              >
                {showAllSlots ? 'Show less' : 'Show all'}
              </button>
            )}
          </div>
          <div className="space-y-1 max-h-48 overflow-y-auto">
            {slotsToShow.map((slot, slotIndex) => (
              <div
                key={slotIndex}
                className="text-xs p-1 bg-green-50 rounded hover:bg-green-100 cursor-pointer flex justify-between items-center"
                onClick={() => handleSlotClick(slot)}
              >
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1 text-green-600" />
                  <span>
                    {slot.day} {slot.time}
                  </span>
                </div>
                {slot.available_rooms.length > 0 && (
                  <span className="text-xs text-gray-500 ml-1">
                    {slot.available_rooms[0]}
                  </span>
                )}
              </div>
            ))}
            {!showAllSlots && sortedSlots.length > 3 && (
              <div
                className="text-xs text-gray-500 italic text-center p-1 hover:bg-gray-50 cursor-pointer rounded"
                onClick={toggleShowAllSlots}
              >
                +{sortedSlots.length - 3} more slots
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
