import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Info } from 'lucide-react'

interface PeriodTiming {
  id: string
  periodNumber: number
  startTime: string
  endTime: string
  isBreak: boolean
}

interface TimetableData {
  name?: string
  periodCount?: number
  schoolDays?: string[]
  startTime?: string
  endTime?: string
  periodLength?: number
  periodTimings?: PeriodTiming[]
  constraints?: {
    avoidBackToBack?: boolean
    optimizeFreeTime?: boolean
    balanceTeacherLoads?: boolean
  }
}

interface BasicTimetableInfoProps {
  timetableData: TimetableData
  handleInputChange: (field: string, value: any) => void
}

export function BasicTimetableInfo({
  timetableData,
  handleInputChange,
}: BasicTimetableInfoProps) {
  const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ]
  const selectedDays = timetableData.schoolDays || [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
  ]

  const handleDayToggle = (day: string) => {
    let updatedDays: string[]

    if (selectedDays.includes(day)) {
      // Remove the day if it's already selected
      updatedDays = selectedDays.filter((d) => d !== day)
    } else {
      // Add the day if it's not selected
      updatedDays = [...selectedDays, day]
    }

    handleInputChange('schoolDays', updatedDays)
  }

  const handleConstraintToggle = (constraint: string) => {
    const currentValue =
      timetableData.constraints?.[
        constraint as keyof typeof timetableData.constraints
      ] || false

    handleInputChange('constraints', {
      ...timetableData.constraints,
      [constraint]: !currentValue,
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Basic Timetable Information</CardTitle>
        <CardDescription>
          Configure the basic settings for your timetable generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Getting Started</AlertTitle>
          <AlertDescription>
            Begin by giving your timetable a name and setting the basic
            parameters like school days and periods per day.
          </AlertDescription>
        </Alert>

        <div className="grid gap-4">
          <div>
            <Label htmlFor="timetable-name">Timetable Name</Label>
            <Input
              id="timetable-name"
              placeholder="e.g., Fall 2023 Timetable"
              value={timetableData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start-time">School Day Start Time</Label>
              <Input
                id="start-time"
                type="time"
                value={timetableData.startTime || '08:00'}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="end-time">School Day End Time</Label>
              <Input
                id="end-time"
                type="time"
                value={timetableData.endTime || '17:00'}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
              />
            </div>
          </div>
        </div>

        <div>
          <Label className="block mb-2">School Days</Label>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {days.map((day) => (
              <div key={day} className="flex items-center space-x-2">
                <Switch
                  id={`day-${day}`}
                  checked={selectedDays.includes(day)}
                  onCheckedChange={() => handleDayToggle(day)}
                />
                <Label htmlFor={`day-${day}`}>{day}</Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div>
          <Label className="block mb-2">Optimization Constraints</Label>
          <div className="grid gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="avoid-back-to-back"
                checked={timetableData.constraints?.avoidBackToBack || false}
                onCheckedChange={() =>
                  handleConstraintToggle('avoidBackToBack')
                }
              />
              <Label htmlFor="avoid-back-to-back">
                Avoid back-to-back lessons of the same subject
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="optimize-free-time"
                checked={timetableData.constraints?.optimizeFreeTime || false}
                onCheckedChange={() =>
                  handleConstraintToggle('optimizeFreeTime')
                }
              />
              <Label htmlFor="optimize-free-time">
                Optimize free time (minimize gaps)
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="balance-teacher-loads"
                checked={
                  timetableData.constraints?.balanceTeacherLoads || false
                }
                onCheckedChange={() =>
                  handleConstraintToggle('balanceTeacherLoads')
                }
              />
              <Label htmlFor="balance-teacher-loads">
                Balance teacher workloads across days
              </Label>
            </div>
          </div>
        </div>

        <div className="text-sm text-muted-foreground mt-4">
          These settings help the timetable generator create an optimal
          schedule. You'll be able to add subjects, teachers, and classes in the
          next steps.
        </div>
      </CardContent>
    </Card>
  )
}
