import { useEffect } from 'react'
import { useTourStore } from '@/store/tourStore'

export function TakeTourButton() {
  const { startAdminTimetableTour, setAdminTimetableTourStep } = useTourStore()

  const handleTakeTourClick = () => {
    console.log('TakeTourButton: Take Tour button clicked')

    // Reset tour state
    localStorage.removeItem('tour_completed_admin_timetable')
    localStorage.removeItem('tour_skipped_admin_timetable')
    localStorage.removeItem('admin_timetable_tour_current_step')
    localStorage.setItem('trigger_admin_timetable_tour', 'true')

    // Start from the beginning
    setAdminTimetableTourStep(0)

    // Start the tour
    setTimeout(() => {
      startAdminTimetableTour()
    }, 100)

    // Dispatch an event for other components
    window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))
  }

  useEffect(() => {
    // Find and attach click handler to any "Take Tour" button
    const attachToTakeTourButtons = () => {
      // Look for buttons with "Take Tour" text
      const buttons = document.querySelectorAll('button')
      buttons.forEach((button) => {
        if (button.textContent?.includes('Take Tour')) {
          console.log(
            'TakeTourButton: Found Take Tour button, attaching click handler'
          )
          button.removeEventListener('click', handleTakeTourClick) // Remove any existing handler
          button.addEventListener('click', handleTakeTourClick)

          // Add a direct click handler as well
          button.onclick = (e) => {
            e.preventDefault()
            e.stopPropagation()
            console.log('TakeTourButton: Direct click handler triggered')
            handleTakeTourClick()
            return false
          }
        }
      })
    }

    // Run immediately
    attachToTakeTourButtons()

    // Also run on DOM changes to catch dynamically added buttons
    const observer = new MutationObserver(attachToTakeTourButtons)
    observer.observe(document.body, { childList: true, subtree: true })

    // Add a global click handler for any button with "Take Tour" text
    const globalClickHandler = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button =
          target.tagName === 'BUTTON' ? target : target.closest('button')
        if (button && button.textContent?.includes('Take Tour')) {
          console.log('TakeTourButton: Global click handler triggered')
          e.preventDefault()
          e.stopPropagation()
          handleTakeTourClick()
        }
      }
    }

    document.addEventListener('click', globalClickHandler, true)

    return () => {
      // Clean up
      observer.disconnect()
      document.removeEventListener('click', globalClickHandler, true)

      const buttons = document.querySelectorAll('button')
      buttons.forEach((button) => {
        if (button.textContent?.includes('Take Tour')) {
          button.removeEventListener('click', handleTakeTourClick)
          button.onclick = null
        }
      })
    }
  }, [])

  // This component doesn't render anything visible
  return null
}
