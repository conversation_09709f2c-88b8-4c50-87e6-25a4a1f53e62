import { useState, useEffect } from 'react'
import { TimetableDataFormatter } from './TimetableDataFormatter'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AlertCircle, CheckCircle } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Pie, Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
} from 'chart.js'

// Register Chart.js components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
)

interface TimetableAnalyticsProps {
  open: boolean
  onClose: () => void
  timetableData: any
}

interface AnalyticsData {
  hardConstraints: {
    errors: string[]
  }
  softConstraints: {
    errors: {
      [key: string]: string[]
    }
    error_counts: {
      [key: string]: number
    }
  }
  warnings: {
    class_name: string
    subject: string
    uncompleted_hour: number
    required_hour: number
    reason: string
  }[]
  colorSchedule: {
    class_name: string
    subject: string
    uncompleted_hours: number
    teacherID: number
    teacherName: string
    green: any[]
    red: any[]
    black: any[]
    blue: any[]
  }[]
}

export function TimetableAnalytics({
  open,
  onClose,
  timetableData,
}: TimetableAnalyticsProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  // Define only the setter functions without the state variables
  const setHardErrors = (errors: string[]) => {
    console.warn('Hard constraint errors:', errors)
    // We're not storing these errors in state anymore, just logging them
  }

  const setSoftErrors = (errors: {
    errors: { [key: string]: string[] }
    error_counts: { [key: string]: number }
  }) => {
    console.warn('Soft constraint errors:', errors)
    // We're not storing these errors in state anymore, just logging them
  }

  const setWarnings = (warnings: string[]) => {
    console.warn('Warnings:', warnings)
    // We're not storing these warnings in state anymore, just logging them
  }

  useEffect(() => {
    if (open && timetableData) {
      setIsLoading(true)

      // Fetch analytics data from the timetable generator API
      const fetchAnalytics = async () => {
        try {
          // Format the data for API requests
          const actualData = timetableData.data || timetableData

          // Normalize the data to ensure time formats are consistent
          const normalizedData =
            TimetableDataFormatter.normalizeTimeSlots(actualData)

          // Prepare request bodies for different endpoints
          const hardRequestBody = TimetableDataFormatter.formatForValidateHard({
            data: normalizedData,
            scheduleData:
              timetableData.scheduleData ||
              timetableData.data?.scheduleData ||
              timetableData.result?.scheduleData,
          })

          const softRequestBody = TimetableDataFormatter.formatForAnalyzeSoft({
            data: normalizedData,
            scheduleData:
              timetableData.scheduleData ||
              timetableData.data?.scheduleData ||
              timetableData.result?.scheduleData,
          })

          const warningsRequestBody = TimetableDataFormatter.formatForWarnings({
            data: normalizedData,
          })

          const colorRequestBody =
            TimetableDataFormatter.formatForColorSchedule({
              data: normalizedData,
              scheduleData:
                timetableData.scheduleData ||
                timetableData.data?.scheduleData ||
                timetableData.result?.scheduleData,
            })

          console.log(
            'Sending to validate_hard API:',
            JSON.stringify(hardRequestBody, null, 2)
          )

          // Fetch hard constraints validation
          let hardResponse
          try {
            hardResponse = await fetch(
              'https://timetable.jeridschool.tech/validate-hard',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
                },
                body: JSON.stringify(hardRequestBody),
              }
            )

            if (!hardResponse.ok) {
              console.error(
                'Hard constraints validation failed:',
                await hardResponse.text()
              )
              setHardErrors([
                'Error validating hard constraints. Check console for details.',
              ])
            }
          } catch (error) {
            console.error('Error fetching hard constraints validation:', error)
            setHardErrors([
              'Error connecting to validation service. Is the server running?',
            ])
          }
          let hardData = { errors: [] }
          if (hardResponse && hardResponse.ok) {
            try {
              hardData = await hardResponse.json()
            } catch (error) {
              console.error('Error parsing hard constraints response:', error)
              setHardErrors(['Error parsing response from validation service.'])
            }
          }

          // Fetch soft constraints analysis
          let softResponse
          try {
            softResponse = await fetch(
              'https://timetable.jeridschool.tech/analyze-soft',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
                },
                body: JSON.stringify(softRequestBody),
              }
            )

            if (!softResponse.ok) {
              console.error(
                'Soft constraints analysis failed:',
                await softResponse.text()
              )
              setSoftErrors({
                errors: {
                  'API Error': [
                    'Error analyzing soft constraints. Check console for details.',
                  ],
                },
                error_counts: {
                  'API Error': 1,
                },
              })
            }
          } catch (error) {
            console.error('Error fetching soft constraints analysis:', error)
            setSoftErrors({
              errors: {
                'API Error': [
                  'Error connecting to analysis service. Is the server running?',
                ],
              },
              error_counts: {
                'API Error': 1,
              },
            })
          }
          let softData = { errors: {}, error_counts: {} }
          if (softResponse && softResponse.ok) {
            try {
              softData = await softResponse.json()
            } catch (error) {
              console.error('Error parsing soft constraints response:', error)
              setSoftErrors({
                errors: {
                  'API Error': [
                    'Error parsing response from analysis service.',
                  ],
                },
                error_counts: {
                  'API Error': 1,
                },
              })
            }
          }

          // Fetch warnings
          let warningsResponse
          try {
            warningsResponse = await fetch(
              'https://timetable.jeridschool.tech/warnings',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
                },
                body: JSON.stringify(warningsRequestBody),
              }
            )

            if (!warningsResponse.ok) {
              console.error(
                'Warnings fetch failed:',
                await warningsResponse.text()
              )
              setWarnings([
                'Error fetching warnings. Check console for details.',
              ])
            }
          } catch (error) {
            console.error('Error fetching warnings:', error)
            setWarnings([
              'Error connecting to warnings service. Is the server running?',
            ])
          }
          let warningsData = { errors: [] }
          if (warningsResponse && warningsResponse.ok) {
            try {
              warningsData = await warningsResponse.json()
            } catch (error) {
              console.error('Error parsing warnings response:', error)
              setWarnings(['Error parsing response from warnings service.'])
            }
          }

          // Fetch color schedule
          let colorResponse
          try {
            colorResponse = await fetch(
              'https://timetable.jeridschool.tech/color-schedule',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
                },
                body: JSON.stringify(colorRequestBody),
              }
            )

            if (!colorResponse.ok) {
              console.error(
                'Color schedule fetch failed:',
                await colorResponse.text()
              )
              // We don't need to set any state here as this is optional functionality
            }
          } catch (error) {
            console.error('Error fetching color schedule:', error)
            // We don't need to set any state here as this is optional functionality
          }

          let colorData = { data: {} }
          if (colorResponse && colorResponse.ok) {
            try {
              colorData = await colorResponse.json()
            } catch (error) {
              console.error('Error parsing color schedule response:', error)
              // We don't need to set any state here as this is optional functionality
            }
          }

          // Combine all data
          setAnalyticsData({
            hardConstraints: hardData,
            softConstraints: softData,
            warnings: Array.isArray(warningsData) ? warningsData : [],
            colorSchedule: Array.isArray(colorData) ? colorData : [],
          })
        } catch (error) {
          console.error('Error fetching analytics data:', error)
          // Set some mock data for demonstration
          setAnalyticsData({
            hardConstraints: { errors: [] },
            softConstraints: {
              errors: {
                teacher_workload: [
                  'Teacher John Doe has 12 hours scheduled, exceeding maximum of 10 hours',
                ],
                teacher_consecutive_hours: [
                  'Teacher Jane Smith has 5 consecutive hours on Wednesday',
                ],
              },
              error_counts: {
                teacher_workload: 1,
                teacher_consecutive_hours: 1,
              },
            },
            warnings: [
              {
                class_name: 'Grade 3 B',
                subject: 'Physics',
                uncompleted_hour: 2,
                required_hour: 4,
                reason: 'Teacher schedule conflict',
              },
            ],
            colorSchedule: [
              {
                class_name: 'Grade 3 B',
                subject: 'Physics',
                uncompleted_hours: 2,
                teacherID: 3,
                teacherName: 'Robert Brown',
                green: [
                  {
                    day: 'Wednesday',
                    time: '10:00 => 11:00',
                    available_rooms: ['Room A', 'Room C'],
                  },
                ],
                red: [
                  {
                    day: 'Thursday',
                    time: '9:00 => 10:00',
                    available_rooms: [],
                  },
                ],
                black: [
                  {
                    day: 'Friday',
                    time: '11:00 => 12:00',
                    available_rooms: [],
                  },
                ],
                blue: [
                  {
                    day: 'Monday',
                    time: '14:00 => 15:00',
                    available_rooms: [],
                  },
                ],
              },
            ],
          })
        } finally {
          setIsLoading(false)
        }
      }

      fetchAnalytics()
    }
  }, [open, timetableData])

  // Prepare chart data for the overview
  const getOverviewChartData = () => {
    if (!analyticsData) return null

    const hardConstraintCount = analyticsData.hardConstraints.errors.length
    const softConstraintCount = Object.values(
      analyticsData.softConstraints.error_counts
    ).reduce((a, b) => a + b, 0)
    const warningCount = analyticsData.warnings.length
    const unscheduledCount = analyticsData.colorSchedule.reduce(
      (sum, item) => sum + item.uncompleted_hours,
      0
    )

    return {
      labels: [
        'Hard Constraints',
        'Soft Constraints',
        'Warnings',
        'Unscheduled Hours',
      ],
      datasets: [
        {
          label: 'Timetable Issues',
          data: [
            hardConstraintCount,
            softConstraintCount,
            warningCount,
            unscheduledCount,
          ],
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)', // Red for hard constraints
            'rgba(255, 159, 64, 0.7)', // Orange for soft constraints
            'rgba(255, 205, 86, 0.7)', // Yellow for warnings
            'rgba(75, 192, 192, 0.7)', // Green for unscheduled
          ],
          borderColor: [
            'rgb(255, 99, 132)',
            'rgb(255, 159, 64)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
          ],
          borderWidth: 1,
        },
      ],
    }
  }

  // Prepare chart data for the soft constraints
  const getSoftConstraintsChartData = () => {
    if (!analyticsData) return null

    const labels = Object.keys(analyticsData.softConstraints.error_counts).map(
      (key) =>
        key
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
    )
    const data = Object.values(analyticsData.softConstraints.error_counts)

    return {
      labels,
      datasets: [
        {
          label: 'Soft Constraint Violations',
          data,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgb(54, 162, 235)',
          borderWidth: 1,
        },
      ],
    }
  }

  // Prepare chart data for the color schedule
  const getColorScheduleChartData = () => {
    if (!analyticsData) return null

    const greenCount = analyticsData.colorSchedule.reduce(
      (sum, item) => sum + item.green.length,
      0
    )
    const redCount = analyticsData.colorSchedule.reduce(
      (sum, item) => sum + item.red.length,
      0
    )
    const blackCount = analyticsData.colorSchedule.reduce(
      (sum, item) => sum + item.black.length,
      0
    )
    const blueCount = analyticsData.colorSchedule.reduce(
      (sum, item) => sum + item.blue.length,
      0
    )

    return {
      labels: [
        'Available (Green)',
        'Teacher Busy (Red)',
        'Teacher Unavailable (Black)',
        'No Rooms (Blue)',
      ],
      datasets: [
        {
          label: 'Color Schedule Distribution',
          data: [greenCount, redCount, blackCount, blueCount],
          backgroundColor: [
            'rgba(75, 192, 192, 0.7)', // Green
            'rgba(255, 99, 132, 0.7)', // Red
            'rgba(0, 0, 0, 0.7)', // Black
            'rgba(54, 162, 235, 0.7)', // Blue
          ],
          borderColor: [
            'rgb(75, 192, 192)',
            'rgb(255, 99, 132)',
            'rgb(0, 0, 0)',
            'rgb(54, 162, 235)',
          ],
          borderWidth: 1,
        },
      ],
    }
  }

  // Chart options for 3D effect
  const pieOptions = {
    plugins: {
      legend: {
        position: 'right' as const,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const label = context.label || ''
            const value = context.raw || 0
            const total = context.dataset.data.reduce(
              (a: number, b: number) => a + b,
              0
            )
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          },
        },
      },
    },
    maintainAspectRatio: false,
    responsive: true,
    cutout: '30%',
    radius: '90%',
    layout: {
      padding: 20,
    },
    animation: {
      animateRotate: true,
      animateScale: true,
    },
  }

  const barOptions = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `Count: ${context.raw}`
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
        },
      },
    },
    maintainAspectRatio: false,
    responsive: true,
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-center justify-center text-xl font-bold">
            <PieChart className="h-6 w-6 mr-2 text-blue-500" />
            Timetable Analytics
          </DialogTitle>
          <DialogClose className="absolute right-4 top-4" />
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="mt-4">
            <Tabs
              defaultValue="overview"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview" className="flex items-center">
                  <PieChart className="h-4 w-4 mr-2" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="constraints" className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Constraints
                </TabsTrigger>
                <TabsTrigger value="warnings" className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Warnings
                </TabsTrigger>
                <TabsTrigger value="schedule" className="flex items-center">
                  <BarChart className="h-4 w-4 mr-2" />
                  Color Schedule
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Timetable Issues Overview</CardTitle>
                    <CardDescription>
                      Summary of all issues detected in the timetable
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      {getOverviewChartData() && (
                        <Pie
                          data={getOverviewChartData()!}
                          options={pieOptions}
                        />
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4 mt-6">
                      <div className="bg-red-50 p-4 rounded-lg">
                        <div className="font-semibold text-red-700">
                          Hard Constraints
                        </div>
                        <div className="text-2xl font-bold text-red-600">
                          {analyticsData?.hardConstraints.errors.length || 0}
                        </div>
                        <div className="text-sm text-red-600">
                          {analyticsData?.hardConstraints.errors.length === 0
                            ? 'No violations'
                            : 'Must be resolved'}
                        </div>
                      </div>

                      <div className="bg-orange-50 p-4 rounded-lg">
                        <div className="font-semibold text-orange-700">
                          Soft Constraints
                        </div>
                        <div className="text-2xl font-bold text-orange-600">
                          {Object.values(
                            analyticsData?.softConstraints.error_counts || {}
                          ).reduce((a, b) => a + b, 0)}
                        </div>
                        <div className="text-sm text-orange-600">
                          {Object.values(
                            analyticsData?.softConstraints.error_counts || {}
                          ).reduce((a, b) => a + b, 0) === 0
                            ? 'No violations'
                            : 'Should be improved'}
                        </div>
                      </div>

                      <div className="bg-yellow-50 p-4 rounded-lg">
                        <div className="font-semibold text-yellow-700">
                          Warnings
                        </div>
                        <div className="text-2xl font-bold text-yellow-600">
                          {analyticsData?.warnings.length || 0}
                        </div>
                        <div className="text-sm text-yellow-600">
                          {analyticsData?.warnings.length === 0
                            ? 'No warnings'
                            : 'Potential issues'}
                        </div>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="font-semibold text-blue-700">
                          Unscheduled Hours
                        </div>
                        <div className="text-2xl font-bold text-blue-600">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.uncompleted_hours,
                            0
                          ) || 0}
                        </div>
                        <div className="text-sm text-blue-600">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.uncompleted_hours,
                            0
                          ) === 0
                            ? 'All hours scheduled'
                            : 'Hours to schedule'}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="constraints" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Constraint Violations</CardTitle>
                    <CardDescription>
                      Details of hard and soft constraint violations
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold mb-2 flex items-center">
                          <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
                          Hard Constraints
                        </h3>

                        {analyticsData?.hardConstraints.errors.length === 0 ? (
                          <div className="flex items-center p-4 bg-green-50 rounded-lg">
                            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                            <span className="text-green-700">
                              No hard constraint violations detected
                            </span>
                          </div>
                        ) : (
                          <ul className="space-y-2">
                            {analyticsData?.hardConstraints.errors.map(
                              (error, index) => (
                                <li
                                  key={index}
                                  className="p-3 bg-red-50 rounded-lg text-red-700"
                                >
                                  {error}
                                </li>
                              )
                            )}
                          </ul>
                        )}
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-2 flex items-center">
                          <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
                          Soft Constraints
                        </h3>

                        {Object.values(
                          analyticsData?.softConstraints.error_counts || {}
                        ).reduce((a, b) => a + b, 0) === 0 ? (
                          <div className="flex items-center p-4 bg-green-50 rounded-lg">
                            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                            <span className="text-green-700">
                              No soft constraint violations detected
                            </span>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <div className="h-60">
                              {getSoftConstraintsChartData() && (
                                <Bar
                                  data={getSoftConstraintsChartData()!}
                                  options={barOptions}
                                />
                              )}
                            </div>

                            <div className="space-y-4">
                              {Object.entries(
                                analyticsData?.softConstraints.errors || {}
                              ).map(([category, errors]) => (
                                <div key={category} className="space-y-2">
                                  <h4 className="font-medium text-gray-700">
                                    {category
                                      .split('_')
                                      .map(
                                        (word) =>
                                          word.charAt(0).toUpperCase() +
                                          word.slice(1)
                                      )
                                      .join(' ')}
                                    <Badge variant="outline" className="ml-2">
                                      {errors.length}
                                    </Badge>
                                  </h4>
                                  <ul className="space-y-1">
                                    {errors.map((error, index) => (
                                      <li
                                        key={index}
                                        className="p-2 bg-orange-50 rounded-lg text-orange-700 text-sm"
                                      >
                                        {error}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="warnings" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Timetable Warnings</CardTitle>
                    <CardDescription>
                      Issues encountered during timetable generation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {analyticsData?.warnings.length === 0 ? (
                      <div className="flex items-center p-4 bg-green-50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-green-700">
                          No warnings detected
                        </span>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {analyticsData?.warnings.map((warning, index) => (
                          <div
                            key={index}
                            className="p-4 bg-yellow-50 rounded-lg"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-semibold text-yellow-800">
                                  {warning.subject} - {warning.class_name}
                                </h4>
                                <p className="text-yellow-700 mt-1">
                                  {warning.reason}
                                </p>
                              </div>
                              <Badge
                                variant="outline"
                                className="bg-yellow-100 text-yellow-800"
                              >
                                {warning.uncompleted_hour}/
                                {warning.required_hour} hours
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schedule" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Color Schedule Analysis</CardTitle>
                    <CardDescription>
                      Analysis of available time slots for unscheduled hours
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80 mb-6">
                      {getColorScheduleChartData() && (
                        <Pie
                          data={getColorScheduleChartData()!}
                          options={pieOptions}
                        />
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div className="font-semibold text-green-700">
                          Available Slots (Green)
                        </div>
                        <div className="text-2xl font-bold text-green-600">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.green.length,
                            0
                          ) || 0}
                        </div>
                        <div className="text-sm text-green-600">
                          Class, teacher, and room available
                        </div>
                      </div>

                      <div className="p-3 bg-red-50 rounded-lg">
                        <div className="font-semibold text-red-700">
                          Teacher Busy (Red)
                        </div>
                        <div className="text-2xl font-bold text-red-600">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.red.length,
                            0
                          ) || 0}
                        </div>
                        <div className="text-sm text-red-600">
                          Teacher is scheduled elsewhere
                        </div>
                      </div>

                      <div className="p-3 bg-gray-800 rounded-lg">
                        <div className="font-semibold text-gray-200">
                          Teacher Unavailable (Black)
                        </div>
                        <div className="text-2xl font-bold text-white">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.black.length,
                            0
                          ) || 0}
                        </div>
                        <div className="text-sm text-gray-300">
                          Teacher marked as unavailable
                        </div>
                      </div>

                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="font-semibold text-blue-700">
                          No Rooms (Blue)
                        </div>
                        <div className="text-2xl font-bold text-blue-600">
                          {analyticsData?.colorSchedule.reduce(
                            (sum, item) => sum + item.blue.length,
                            0
                          ) || 0}
                        </div>
                        <div className="text-sm text-blue-600">
                          No available rooms for this slot
                        </div>
                      </div>
                    </div>

                    {analyticsData?.colorSchedule.length === 0 ? (
                      <div className="flex items-center p-4 bg-green-50 rounded-lg mt-6">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-green-700">
                          All hours have been scheduled successfully
                        </span>
                      </div>
                    ) : (
                      <div className="mt-6">
                        <h3 className="text-lg font-semibold mb-3">
                          Unscheduled Subjects
                        </h3>
                        <div className="space-y-4">
                          {analyticsData?.colorSchedule.map((item, index) => (
                            <div key={index} className="p-4 border rounded-lg">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="font-semibold">
                                    {item.subject} - {item.class_name}
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    Teacher: {item.teacherName}
                                  </p>
                                </div>
                                <Badge variant="outline">
                                  {item.uncompleted_hours} hours unscheduled
                                </Badge>
                              </div>

                              <div className="grid grid-cols-4 gap-2 mt-3">
                                <div className="text-center">
                                  <Badge className="bg-green-100 text-green-800 w-full">
                                    {item.green.length} Green
                                  </Badge>
                                </div>
                                <div className="text-center">
                                  <Badge className="bg-red-100 text-red-800 w-full">
                                    {item.red.length} Red
                                  </Badge>
                                </div>
                                <div className="text-center">
                                  <Badge className="bg-gray-800 text-white w-full">
                                    {item.black.length} Black
                                  </Badge>
                                </div>
                                <div className="text-center">
                                  <Badge className="bg-blue-100 text-blue-800 w-full">
                                    {item.blue.length} Blue
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
