import { AdminTimetableTour } from './AdminTimetableTour'
import { TourRestartButton } from './TourRestartButton'
import { TimetableTour } from './TimetableTour'

export function TimetableTourManager() {
  // Check if we're on the timetable page
  const isTimetablePage = window.location.pathname.includes('/admin/timetable')

  return (
    <>
      {/* Show AdminTimetableTour on admin dashboard, TimetableTour on timetable page */}
      {isTimetablePage ? <TimetableTour /> : <AdminTimetableTour />}
      <TourRestartButton />
    </>
  )
}
