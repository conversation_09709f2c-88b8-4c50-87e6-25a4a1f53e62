import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Plus, Trash2, Loader2 } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { School } from 'lucide-react'

interface Class {
  id: string
  name: string
}

interface ClassesConfigurationProps {
  classes: Class[]
  isLoading?: boolean
  onAddClass: (classItem: Class) => void
  onDeleteClass: (classId: string) => void
}

export function ClassesConfiguration({
  classes,
  isLoading = false,
  onAddClass,
  onDeleteClass,
}: ClassesConfigurationProps) {
  const { toast } = useToast()
  const [newClassName, setNewClassName] = useState('')

  const handleAddClass = () => {
    if (!newClassName.trim()) {
      toast({
        title: 'Error',
        description: 'Class name cannot be empty',
        variant: 'destructive',
      })
      return
    }

    // Check if class already exists
    if (
      classes.some(
        (classItem) =>
          classItem.name.toLowerCase() === newClassName.trim().toLowerCase()
      )
    ) {
      toast({
        title: 'Error',
        description: 'This class already exists',
        variant: 'destructive',
      })
      return
    }

    // Generate a unique ID using timestamp
    const newClass: Class = {
      id: `class_${Date.now()}`,
      name: newClassName.trim(),
    }

    onAddClass(newClass)
    setNewClassName('')

    toast({
      title: 'Class Added',
      description: `${newClassName} has been added to the classes list`,
    })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddClass()
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Classes Configuration</CardTitle>
        <CardDescription>
          Define the classes or grades that will be included in your timetable
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <School className="h-4 w-4" />
          <AlertTitle>Classes/Grades Information</AlertTitle>
          <AlertDescription>
            Add all the classes or grades that need to be scheduled in your
            timetable. Each class will have its own unique schedule.
          </AlertDescription>
        </Alert>

        <div className="flex items-end gap-4">
          <div className="flex-1">
            <Label htmlFor="class-name">Class Name</Label>
            <Input
              id="class-name"
              placeholder="e.g., Class 10A or Grade 5"
              value={newClassName}
              onChange={(e) => setNewClassName(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={isLoading}
            />
          </div>
          <Button
            onClick={handleAddClass}
            disabled={isLoading || !newClassName.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Class
          </Button>
        </div>

        <Separator />

        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <p>Loading classes...</p>
          </div>
        ) : classes.length === 0 ? (
          <div className="text-center p-4 border rounded-md bg-muted/20">
            No classes defined yet. Add your first class using the form above.
          </div>
        ) : (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Class Name</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classes.map((classItem) => (
                  <TableRow key={classItem.id}>
                    <TableCell>{classItem.name}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDeleteClass(classItem.id)}
                        title="Delete Class"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4 text-sm text-muted-foreground">
              Total classes: {classes.length}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
