import { useState, useRef, useEffect } from 'react'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'

// Define a map to store subject colors outside the component to persist across renders
const subjectColors: Record<string, string> = {
  Mathematics: '#FF5733',
  Science: '#33FF57',
  English: '#3357FF',
  History: '#F033FF',
  Geography: '#FF33F0',
  Physics: '#33FFF0',
  Chemistry: '#F0FF33',
  Biology: '#FF3333',
  'Computer Science': '#33FF33',
  Art: '#3333FF',
  Music: '#FFAA33',
  'Physical Education': '#33FFAA',
  French: '#AA33FF',
  Spanish: '#FF33AA',
  German: '#33AAFF',
}

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  ArrowLef<PERSON>,
  RefreshCw,
  Trash2,
  FileUp,
  Users,
  User,
  FileDown,
  AlertCircle,
} from 'lucide-react'
import jsPDF from 'jspdf'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import {
  TimetableAssignment,
  TimetableData,
  UnscheduledSubject,
} from '@/types/timetable'
import { UnscheduleZone, ItemTypes } from './UnscheduleZone'
import { DraggableUnscheduledSubject } from './DraggableUnscheduledSubject'
import {
  removeAssignmentFromTimetableData,
  addAssignmentToTimetableData,
} from '@/services/timetableService'
import { TimetableTour } from './TimetableTour'

interface TimetableDisplayProps {
  timetableData: any
  onBack?: () => void
  onRegenerate?: () => void
  onDelete?: () => void
  autoAnalyzeOnMount?: boolean
}

export function TimetableDisplay({
  timetableData,
  onBack,
  onRegenerate,
  onDelete,
  autoAnalyzeOnMount = false,
}: TimetableDisplayProps) {
  const { toast } = useToast()
  const [selectedClass, setSelectedClass] = useState<string>('')
  const [dataInput, setDataInput] = useState<string>('')
  const [resultInput, setResultInput] = useState<string>('')
  const [injectedData, setInjectedData] = useState<any>(null)
  const [isPdfExporting, setIsPdfExporting] = useState<boolean>(false)
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [unscheduledSubjects, setUnscheduledSubjects] = useState<
    UnscheduledSubject[]
  >([])
  const [selectedUnscheduledSubject, setSelectedUnscheduledSubject] =
    useState<UnscheduledSubject | null>(null)
  const timetableRef = useRef<HTMLDivElement>(null)

  // Function to analyze the schedule and find unscheduled lessons
  const analyzeCurrentSchedule = async () => {
    try {
      setIsAnalyzing(true)
      const data = getDisplayData()

      if (!data) {
        toast({
          title: 'No Data',
          description: 'No timetable data available to analyze.',
          variant: 'destructive',
        })
        return
      }

      console.log('Analyzing schedule with data:', data)

      // Prepare the analyze-schedule request body
      // Try to get the inputData from the server if available
      let analyzeBody

      try {
        // First check if we have a timetable ID
        const token = localStorage.getItem('access_token')
        const timetableResponse = await fetch(
          'http://localhost:3000/timetable/etablissement',
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (timetableResponse.ok) {
          const timetableData = await timetableResponse.json()
          if (timetableData.inputData) {
            console.log('Using inputData from server for analysis')
            analyzeBody = {
              data: timetableData.inputData,
              result: data,
            }
          }
        }
      } catch (error) {
        console.error('Error fetching timetable for inputData:', error)
      }

      // Fallback to using the current data if we couldn't get inputData
      if (!analyzeBody) {
        console.log('Using current data for analysis (no inputData available)')
        analyzeBody = {
          data: data,
          result: data,
        }
      }

      console.log('Sending analyze request with body:', analyzeBody)

      // Try to call the real API first
      try {
        const response = await fetch(
          'https://timetable.jeridschool.tech/analyze-schedule',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(analyzeBody),
          }
        )

        // Log the raw response for debugging
        const responseText = await response.text()
        console.log('Raw API response:', responseText)

        // Parse the response text
        let analysisResult
        try {
          analysisResult = JSON.parse(responseText)
          console.log('Parsed analysis result:', analysisResult)

          // Check if we got a valid array response
          if (Array.isArray(analysisResult)) {
            // Convert the API response to our UnscheduledSubject format
            const apiUnscheduledSubjects: UnscheduledSubject[] =
              analysisResult.map((item: any) => ({
                class: item.class || selectedClass,
                subject: item.subject,
                required_hours: item.required_hours || 1,
                scheduled_hours: item.scheduled_hours || 0,
                uncompleted_hours: item.uncompleted_hours || 1,
                teacherID: item.teacherID,
                teacherName: item.teacherName || item.teacher || 'Unknown',
                available_slots: item.available_slots || [],
              }))

            console.log(
              'Converted unscheduled subjects:',
              apiUnscheduledSubjects
            )
            setUnscheduledSubjects(apiUnscheduledSubjects)

            // If we have unscheduled subjects, select the first one to highlight available slots
            if (apiUnscheduledSubjects.length > 0) {
              setSelectedUnscheduledSubject(apiUnscheduledSubjects[0])
            } else {
              setSelectedUnscheduledSubject(null)
            }

            toast({
              title: 'Analysis Complete',
              description: `Found ${apiUnscheduledSubjects.length} unscheduled subjects.`,
            })
            return
          } else {
            console.warn('API did not return an array:', analysisResult)
            // Continue to fallback
          }
        } catch (parseError) {
          console.error('Error parsing API response:', parseError)
          // Continue to fallback
        }
      } catch (apiError) {
        console.error('Error calling analyze-schedule API:', apiError)
        // Continue to fallback
      }

      // Fallback: Try to extract unscheduled subjects from the input data
      const inputData = data.inputData || analyzeBody?.data
      const unscheduledSubjects: UnscheduledSubject[] = []

      if (inputData && inputData.classes && Array.isArray(inputData.classes)) {
        // Extract classes and their subjects from input data
        inputData.classes.forEach((classData: any) => {
          if (classData.subjects && Array.isArray(classData.subjects)) {
            classData.subjects.forEach((subject: any) => {
              // Track how many hours of this subject are already scheduled
              let scheduledHours = 0

              // Look through the schedule data to see if this subject is scheduled
              if (data.scheduleData && Array.isArray(data.scheduleData)) {
                const classSchedule = data.scheduleData.find(
                  (c: any) => c.class === classData.className
                )

                if (classSchedule) {
                  // Check each day for this subject
                  Object.keys(classSchedule).forEach((day) => {
                    if (day === 'class') return

                    if (Array.isArray(classSchedule[day])) {
                      classSchedule[day].forEach((lesson: any) => {
                        if (
                          lesson.subject === subject.subject &&
                          (lesson.teacherID === subject.teacherID ||
                            lesson.teacherId === subject.teacherID)
                        ) {
                          scheduledHours++
                        }
                      })
                    }
                  })
                }
              }

              // If the subject is not fully scheduled, add it to unscheduled subjects
              const requiredHours = subject.hours || 1
              if (scheduledHours < requiredHours) {
                // Find the teacher name
                let teacherName = 'Unknown Teacher'
                if (inputData.teachers && Array.isArray(inputData.teachers)) {
                  const teacher = inputData.teachers.find(
                    (t: any) => t.teacherId === subject.teacherID
                  )
                  if (teacher) {
                    teacherName = teacher.teacherName
                  }
                }

                // Create available slots based on the time slots in the input data
                const availableSlots: any[] = []
                if (inputData.timeSlots) {
                  Object.keys(inputData.timeSlots).forEach((day) => {
                    if (inputData.timeSlots[day].studyTimes) {
                      inputData.timeSlots[day].studyTimes.forEach(
                        (time: string) => {
                          // Add available rooms
                          const availableRooms: string[] = []
                          if (
                            inputData.salles &&
                            Array.isArray(inputData.salles)
                          ) {
                            inputData.salles.forEach((room: any) => {
                              availableRooms.push(room.name)
                            })
                          }

                          availableSlots.push({
                            day,
                            time,
                            available_rooms: availableRooms,
                          })
                        }
                      )
                    }
                  })
                }

                unscheduledSubjects.push({
                  class: classData.className,
                  subject: subject.subject,
                  required_hours: requiredHours,
                  scheduled_hours: scheduledHours,
                  uncompleted_hours: requiredHours - scheduledHours,
                  teacherID: subject.teacherID,
                  teacherName,
                  available_slots: availableSlots,
                })
              }
            })
          }
        })
      }

      // If we found unscheduled subjects, use them
      if (unscheduledSubjects.length > 0) {
        setUnscheduledSubjects(unscheduledSubjects)

        // Select the first unscheduled subject to highlight available slots
        setSelectedUnscheduledSubject(unscheduledSubjects[0])

        toast({
          title: 'Analysis Complete',
          description: `Found ${unscheduledSubjects.length} unscheduled subjects.`,
        })
      } else {
        // If no unscheduled subjects were found, show a message
        toast({
          title: 'Analysis Complete',
          description: 'No unscheduled subjects found.',
        })
      }
    } catch (error) {
      console.error('Error analyzing schedule:', error)
      toast({
        title: 'Analysis Failed',
        description: 'Failed to analyze the schedule. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  // Function to handle unscheduling a lesson
  const handleUnscheduleLesson = async (assignment: TimetableAssignment) => {
    console.log('Unscheduling lesson:', assignment)

    // Remove from timetable data
    const updatedData = removeAssignmentFromTimetableData(
      getDisplayData() as TimetableData,
      assignment
    )

    // Store the unscheduled assignment in the timetable data
    // This ensures it persists when saved to the server
    if (!updatedData.unscheduledAssignments) {
      updatedData.unscheduledAssignments = []
    }

    // Add the unscheduled assignment to the list
    updatedData.unscheduledAssignments.push({
      subject: assignment.subject,
      teacherID: assignment.teacherID,
      teacher: assignment.teacher,
      class: assignment.class,
      day: assignment.day,
      time: assignment.time,
      salle: assignment.salle,
    })

    console.log('Updated data with unscheduled assignment:', updatedData)

    // Update the injected data without saving to server
    // Changes will only be saved when the user clicks the "Save to Server" button
    setInjectedData(updatedData)

    // Show toast notification that changes need to be saved
    toast({
      title: 'Changes Not Saved',
      description: 'Click "Save to Server" to save your changes.',
      variant: 'default',
    })

    // Prepare the analyze-schedule request body
    // Try to get the inputData from the server if available
    let analyzeBody

    try {
      // First check if we have a timetable ID
      const token = localStorage.getItem('access_token')
      const timetableResponse = await fetch(
        'http://localhost:3000/timetable/etablissement',
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      )

      if (timetableResponse.ok) {
        const timetableData = await timetableResponse.json()
        if (timetableData.inputData) {
          console.log('Using inputData from server for unschedule analysis')
          analyzeBody = {
            data: timetableData.inputData,
            result: updatedData,
          }
        }
      }
    } catch (error) {
      console.error('Error fetching timetable for inputData:', error)
    }

    // Fallback to using the current data if we couldn't get inputData
    if (!analyzeBody) {
      console.log(
        'Using current data for unschedule analysis (no inputData available)'
      )
      analyzeBody = {
        data: getDisplayData(),
        result: updatedData,
      }
    }

    let availableSlots: any[] = []
    let required_hours = 1
    let scheduled_hours = 0
    let uncompleted_hours = 1

    try {
      // Call the analyze-schedule API to get available slots for this subject
      const response = await fetch(
        'https://timetable.jeridschool.tech/analyze-schedule',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(analyzeBody),
        }
      )

      // Get the raw response text
      const responseText = await response.text()
      console.log('Raw API response for unscheduled lesson:', responseText)

      try {
        // Try to parse the response
        const analysis = JSON.parse(responseText)
        console.log('Parsed analysis result for unscheduled lesson:', analysis)

        // Find the matching subject in the analysis result
        if (Array.isArray(analysis)) {
          const found = analysis.find(
            (item: any) =>
              item.class === assignment.class &&
              item.subject === assignment.subject &&
              item.teacherID === assignment.teacherID
          )
          if (found) {
            availableSlots = found.available_slots || []
            required_hours = found.required_hours
            scheduled_hours = found.scheduled_hours
            uncompleted_hours = found.uncompleted_hours
          }
        }
      } catch (parseError) {
        console.error('Error parsing API response:', parseError)
        // Fallback to default slots
      }
    } catch (error) {
      console.error('Error calling analyze-schedule API:', error)
    }

    // If no available slots were found or there was an error, use fallback slots
    if (availableSlots.length === 0) {
      // Create fallback slots for all days and times
      const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
      const times = [
        '8 => 9',
        '9 => 10',
        '10 => 11',
        '11 => 12',
        '13 => 14',
        '14 => 15',
        '15 => 16',
      ]

      // Add the original slot first
      availableSlots.push({
        day: assignment.day,
        time: assignment.time,
        available_rooms: [assignment.salle || 'Room 102'],
      })

      // Add some additional slots
      days.forEach((day) => {
        if (day !== assignment.day) {
          times.forEach((time) => {
            if (Math.random() > 0.7) {
              // Only add some slots randomly
              availableSlots.push({
                day,
                time,
                available_rooms: ['Room 102', 'Room 103', 'Room 104'],
              })
            }
          })
        }
      })
    }

    // Create a new unscheduled subject from the assignment
    const newUnscheduledSubject: UnscheduledSubject = {
      class: assignment.class,
      subject: assignment.subject,
      required_hours,
      scheduled_hours,
      uncompleted_hours,
      teacherID: assignment.teacherID,
      teacherName: assignment.teacher,
      available_slots: availableSlots,
    }

    console.log('Created new unscheduled subject:', newUnscheduledSubject)

    // Add the new unscheduled subject to the list
    setUnscheduledSubjects((prev) => [...prev, newUnscheduledSubject])

    // Automatically select the newly unscheduled subject to highlight available slots
    setSelectedUnscheduledSubject(newUnscheduledSubject)

    toast({
      title: 'Lesson Unscheduled',
      description: `${assignment.subject} has been unscheduled.`,
    })
  }

  // Helper function to format schedule data to TimetableEntryDto format
  interface TimetableEntry {
    id: string
    className: string
    subject: string
    day: string
    timeSlot: string
    time?: string
    teacherId: string
    teacherID?: string
    teacher?: string
    classroomId: string
    salle?: string
    group1?: any
    group2?: any
  }

  const formatScheduleDataToEntries = (
    data: TimetableData
  ): TimetableEntry[] => {
    const entries: TimetableEntry[] = []

    // Convert scheduleData to entries format
    if (data.scheduleData && Array.isArray(data.scheduleData)) {
      data.scheduleData.forEach((classData) => {
        const className = classData.class

        // Process each day
        Object.keys(classData).forEach((day) => {
          // Skip the class property
          if (day === 'class') return

          // Process each lesson in the day
          if (Array.isArray(classData[day])) {
            classData[day].forEach((lesson) => {
              if (lesson && lesson.subject) {
                // Create a complete entry with all required fields
                entries.push({
                  id:
                    lesson.id ||
                    `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                  className: className,
                  subject: lesson.subject,
                  day: day,
                  timeSlot: lesson.timeSlot || lesson.time,
                  time: lesson.timeSlot || lesson.time,
                  teacherId: lesson.teacherId || lesson.teacherID || '',
                  teacherID: lesson.teacherId || lesson.teacherID || '',
                  teacher:
                    lesson.teacher ||
                    `Teacher ${lesson.teacherId || lesson.teacherID || ''}`,
                  classroomId: lesson.classroomId || lesson.salle || '',
                  salle: lesson.classroomId || lesson.salle || '',
                  group1: lesson.group1,
                  group2: lesson.group2,
                })
              }
            })
          }
        })
      })
    }

    return entries
  }

  // Function to save timetable data to the server
  const saveTimetableToServer = async (data: TimetableData): Promise<void> => {
    try {
      console.log('Saving timetable data to server:', data)

      // Get the authentication token from localStorage
      const token =
        localStorage.getItem('access_token') ||
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJZTEhNMGFkNC0wZWItNDFiOC1iM2UtZjRlNDE1MmI5OWM1Iiwicm9sZSI6IkFkbWluIiwiaWF0IjoxNjk3MjA5NjU0LCJleHAiOjE2OTcyMTMyNTR9.Ck_sudo'
      console.log('Using authentication token:', token)

      // Fetch the original input data from the server if it's not in the current data
      if (!data.inputData) {
        try {
          console.log('Fetching original input data from server...')
          const inputDataResponse = await fetch(
            'http://localhost:3000/timetable/etablissement',
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (inputDataResponse.ok) {
            const serverData = await inputDataResponse.json()
            if (serverData.inputData) {
              console.log('Successfully fetched input data from server')
              data.inputData = serverData.inputData
            } else {
              console.warn('No input data found on server')
            }
          } else {
            console.warn('Failed to fetch input data from server:', inputDataResponse.status)
          }
        } catch (error) {
          console.error('Error fetching input data:', error)
        }
      }

      // First, check if there's an existing timetable for the establishment
      let timetableId: string | null = null

      // Check if we have a stored timetable ID from a previous save
      const storedTimetableId = localStorage.getItem('lastSavedTimetableId')
      if (storedTimetableId) {
        console.log('Using stored timetable ID:', storedTimetableId)
        timetableId = storedTimetableId
      } else {
        try {
          // Now check for existing timetable
          const getResponse = await fetch(
            'http://localhost:3000/timetable/etablissement',
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (getResponse.ok) {
            const currentTimetable = await getResponse.json()
            timetableId = currentTimetable.id
            console.log('Found existing timetable with ID:', timetableId)

            // Store the ID for future use
            if (timetableId) {
              localStorage.setItem('lastSavedTimetableId', timetableId)
            }
          } else {
            console.warn(
              'Could not find existing timetable, will create a new one'
            )
          }
        } catch (error) {
          console.error('Error fetching current timetable:', error)
        }
      }

      // We'll use the formatScheduleDataToEntries function to convert the data
      // to the format expected by the server
      const entries = formatScheduleDataToEntries(data)
      console.log('Formatted entries for server:', entries)

      // Make sure we include the original scheduleData as well
      const completeData = {
        entries: entries,
        scheduleData: data.scheduleData,
      }
      console.log('Complete server data:', completeData)

      // Check if there are any unscheduled subjects
      const hasUnscheduledSubjects =
        unscheduledSubjects && unscheduledSubjects.length > 0
      console.log(
        'Has unscheduled subjects:',
        hasUnscheduledSubjects,
        unscheduledSubjects
      )

      // We'll use the TimetableEntry interface defined below for PATCH requests

      // Prepare the server data based on the request type
      // Format it according to what the API expects
      let serverData: any = {
        name: 'Generated Timetable',
        // data should contain the result from timetable.jeridschool.tech
        data: {
          scheduleData: data.scheduleData || []
        },
        // inputData should contain the original data sent from the forms
        inputData: data.inputData || {
          timeSlots: {},
          salles: [],
          classes: [],
          teachers: [],
        },
        description: 'Generated Timetable',
        academicYear:
          new Date().getFullYear() + '-' + (new Date().getFullYear() + 1),
        isActive: true,
      }

      console.log('Formatted server data for analyze feature:', serverData)

      // Do not include etablissementId as it will be auto-handled by the backend

      // Determine the appropriate endpoint and method
      let url = 'http://localhost:3000/timetable'
      let method = 'POST'

      // Always use PATCH if we have a timetable ID
      if (timetableId) {
        url = `http://localhost:3000/timetable/${timetableId}`
        method = 'PATCH'
        console.log(
          'Using PATCH to update existing timetable with ID:',
          timetableId
        )

        // For PATCH requests, only send the data field with entries
        // When handling timetable data, only change the data field in PATCH requests and nothing else
        // The server expects entries in TimetableEntryDto format

        // For PATCH, we should ONLY update the data field and never touch inputData
        // We already have the complete data in serverData, no need to overwrite it
        console.log('PATCH request - Formatted data being sent:', serverData)
      } else {
        // If we don't have a timetable ID, try to get it first
        try {
          console.log(
            'Checking for existing timetable before creating a new one'
          )
          const checkResponse = await fetch(
            'http://localhost:3000/timetable/etablissement',
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (checkResponse.ok) {
            const existingTimetable = await checkResponse.json()
            if (existingTimetable && existingTimetable.id) {
              // We found an existing timetable, use PATCH instead
              url = `http://localhost:3000/timetable/${existingTimetable.id}`
              method = 'PATCH'
              console.log(
                'Found existing timetable, using PATCH instead with ID:',
                existingTimetable.id
              )
            } else {
              // No existing timetable found, use POST
              url = 'http://localhost:3000/timetable'
              method = 'POST'
              console.log(
                'No existing timetable found, using POST to create a new one'
              )
            }
          } else {
            // Error checking for existing timetable, use POST as fallback
            url = 'http://localhost:3000/timetable'
            method = 'POST'
            console.log(
              'Error checking for existing timetable, using POST as fallback'
            )
          }
        } catch (error) {
          // Error checking for existing timetable, use POST as fallback
          url = 'http://localhost:3000/timetable'
          method = 'POST'
          console.log('Error checking for existing timetable:', error)
          console.log('Using POST as fallback')
        }

        // For new timetables, include the original input data
        // Check if we have inputData in the original data
        if (data.inputData) {
          console.log(
            'Using existing inputData from the data object:',
            data.inputData
          )
          serverData.data.data = data.inputData
          // Also ensure it's set at the top level
          serverData.inputData = data.inputData
        } else {
          console.log(
            'No inputData found in the data object, generating a comprehensive version'
          )
          // Create a comprehensive inputData structure based on the schedule data
          serverData.data.data = {
            timeSlots: {},
            salles: [],
            classes: [],
            teachers: [],
          }
          // Also set it at the top level
          serverData.inputData = serverData.data.data

          // Extract basic information from the schedule data
          if (data.scheduleData && Array.isArray(data.scheduleData)) {
            data.scheduleData.forEach((classData) => {
              // Add class
              const className = classData.class
              const classSubjects = new Set()
              const teachers = new Map()
              const rooms = new Set()

              // Process each day
              Object.keys(classData).forEach((day) => {
                if (day === 'class') return

                // Initialize timeSlots for this day if needed
                if (!serverData.data.data.timeSlots[day]) {
                  serverData.data.data.timeSlots[day] = {
                    studyTimes: [],
                    restTime: '12 => 13', // Default rest time
                  }
                }

                // Process periods
                if (Array.isArray(classData[day])) {
                  classData[day].forEach((period) => {
                    if (period && period.time) {
                      // Add time slot
                      if (
                        !serverData.data.data.timeSlots[
                          day
                        ].studyTimes.includes(period.time)
                      ) {
                        serverData.data.data.timeSlots[day].studyTimes.push(
                          period.time
                        )
                      }
                    }

                    if (period && period.subject) {
                      // Add subject
                      classSubjects.add(period.subject)

                      // Add teacher
                      if (period.teacherID && period.teacher) {
                        teachers.set(period.teacherID, period.teacher)
                      }

                      // Add room
                      if (period.salle) {
                        rooms.add(period.salle)
                      }
                    }
                  })
                }
              })

              // Add class with subjects
              serverData.data.data.classes.push({
                className,
                subjects: Array.from(classSubjects).map((subject) => ({
                  subject,
                  hours: 2, // Default hours
                  teacherID: Array.from(teachers.keys())[0] || '',
                  teacher: Array.from(teachers.values())[0] || '',
                })),
              })

              // Add teachers
              teachers.forEach((teacherName, teacherId) => {
                serverData.data.data.teachers.push({
                  teacherId,
                  teacherName,
                  subjects: Array.from(classSubjects),
                  unavailableTimes: {
                    Monday: [],
                    Tuesday: [],
                    Wednesday: [],
                    Thursday: [],
                    Friday: [],
                  },
                  minimumHours: 10,
                  maximumHours: 20,
                })
              })

              // Add rooms
              rooms.forEach((room) => {
                serverData.data.data.salles.push({
                  name: room,
                  type: 'room',
                })
              })
            })
          }

          // Make sure the top-level inputData is updated with the generated data
          serverData.inputData = serverData.data.data
        }

        console.log(
          'POST request with unscheduled subjects - data being sent:',
          serverData
        )

        // We already handled the inputData in the previous section
        console.log(
          'Using the inputData we already created and set at both levels'
        )

        console.log(
          'POST request for new timetable - data being sent:',
          serverData
        )
      }

      // Send the data to the server
      console.log('Sending timetable data to:', url, 'with method:', method)
      console.log('Request headers:', {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token.substring(0, 20)}...`, // Log partial token for security
      })
      console.log('Request payload:', JSON.stringify(serverData, null, 2))

      // Prepare the request body with the correct structure
      let requestBody = {
        ...serverData
      }

      // Remove the 'name' property for PATCH requests as it's not allowed by the API
      if (method === 'PATCH' && requestBody.name) {
        console.log('Removing name property from PATCH request as it is not allowed')
        const { name, ...restOfBody } = requestBody
        requestBody = restOfBody
      }

      // Make sure the inputData is properly structured
      if (!requestBody.inputData ||
          !requestBody.inputData.timeSlots ||
          !requestBody.inputData.classes ||
          !requestBody.inputData.teachers) {
        console.warn('InputData is missing required fields, creating default structure')

        // Create a default structure if missing
        const defaultInputData = {
          timeSlots: requestBody.inputData?.timeSlots || {},
          salles: requestBody.inputData?.salles || [],
          classes: requestBody.inputData?.classes || [],
          teachers: requestBody.inputData?.teachers || [],
        }

        // Extract data from scheduleData if available
        if (data.scheduleData && Array.isArray(data.scheduleData)) {
          // Extract class names
          defaultInputData.classes = data.scheduleData.map(classData => ({
            className: classData.class,
            subjects: []
          }))

          // Extract time slots
          const allTimeSlots = new Set<string>()
          data.scheduleData.forEach(classData => {
            Object.keys(classData).forEach(day => {
              if (day !== 'class' && Array.isArray(classData[day])) {
                classData[day].forEach((lesson: any) => {
                  if (lesson && lesson.time) {
                    allTimeSlots.add(lesson.time)
                  }
                })
              }
            })
          })

          // Create timeSlots structure
          const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
          days.forEach(day => {
            defaultInputData.timeSlots[day] = {
              studyTimes: Array.from(allTimeSlots),
              restTime: '12 => 13'
            }
          })
        }

        // Update the requestBody with the default structure
        requestBody.inputData = defaultInputData
      }

      // Ensure the data field is properly structured
      if (!requestBody.data || !requestBody.data.scheduleData) {
        requestBody.data = {
          scheduleData: data.scheduleData || []
        }
      }

      // Log the inputData to verify it's being included
      console.log(
        'InputData being sent to server:',
        JSON.stringify(requestBody.inputData).substring(0, 100) + '...'
      )
      console.log(
        'Request body prepared with inputData explicitly included as a direct field'
      )

      // Double check that inputData is present
      if (!requestBody.inputData) {
        console.error(
          'WARNING: inputData is still missing from the request body!'
        )
      } else {
        console.log(
          'InputData is present in the request body with keys:',
          Object.keys(requestBody.inputData)
        )
      }

      console.log('Final request body after JSON preparation:', requestBody)

      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      })

      console.log('Response status:', response.status, response.statusText)
      console.log(
        'Response headers:',
        Object.fromEntries([...response.headers.entries()])
      )

      // Log the response body for debugging
      const responseText = await response.text()
      try {
        const responseJson = JSON.parse(responseText)
        console.log('Response body:', responseJson)
      } catch (e) {
        console.log('Response body (text):', responseText)
      }

      // Reset the response since we've consumed it
      const responseClone = new Response(responseText, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      })

      if (responseClone.ok) {
        let responseData
        try {
          responseData = JSON.parse(responseText)
          // Check if inputData was saved in the response
          if (responseData.inputData) {
            console.log(
              'SUCCESS: inputData was saved in the database and returned in the response'
            )
            console.log(
              'InputData keys in response:',
              Object.keys(responseData.inputData)
            )
          } else {
            console.warn(
              'WARNING: inputData was not found in the response from the server'
            )
          }

          // Store the timetable ID for future use
          if (responseData.id) {
            console.log('Storing timetable ID for future use:', responseData.id)
            localStorage.setItem('lastSavedTimetableId', responseData.id)
          }

          // Update the injected data with the saved data
          if (responseData.data && responseData.data.scheduleData) {
            console.log('Updating injected data with saved data')
            const savedData = {
              ...responseData,
              scheduleData: responseData.data.scheduleData,
              inputData: responseData.inputData
            }
            setInjectedData(savedData)
            console.log('Injected data updated:', savedData)
          }
        } catch (e) {
          responseData = { message: 'Success (no JSON response)' }
        }
        console.log('Timetable data saved successfully:', responseData)
        toast({
          title: 'Timetable Saved',
          description: 'The timetable has been saved to the server.',
        })
      } else {
        let errorText = ''
        try {
          const errorData = JSON.parse(responseText)
          errorText = JSON.stringify(errorData)
          console.error('Server error response:', errorData)
        } catch (e) {
          errorText = responseClone.statusText
        }

        // Log more detailed information about the error
        console.error('Failed to save timetable data:', {
          status: responseClone.status,
          statusText: responseClone.statusText,
          url: url,
          method: method,
          error: errorText,
          requestData: JSON.stringify(serverData).substring(0, 200) + '...', // Log partial data for debugging
        })

        toast({
          title: 'Save Failed',
          description: `Failed to save timetable data: ${responseClone.status} (${responseClone.statusText})\nEndpoint: ${method} ${url}`,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error saving timetable data:', error)
      toast({
        title: 'Save Failed',
        description: 'An error occurred while saving the timetable data.',
        variant: 'destructive',
      })
    }
  }

  // Function to handle rescheduling an unscheduled subject
  const handleRescheduleUnscheduledSubject = (
    subject: UnscheduledSubject,
    day: string,
    time: string,
    room?: string
  ) => {
    console.log(
      'Rescheduling subject:',
      subject,
      'to day:',
      day,
      'time:',
      time,
      'room:',
      room
    )

    // Create a new assignment from the unscheduled subject
    const newAssignment: TimetableAssignment = {
      subject: subject.subject,
      teacherID: subject.teacherID,
      teacher: subject.teacherName,
      class: subject.class,
      day,
      time,
      salle: room,
    }

    console.log('Created new assignment:', newAssignment)

    // Get the current data
    const currentData = getDisplayData() as TimetableData

    // Add to timetable data
    const updatedData = addAssignmentToTimetableData(
      currentData,
      newAssignment,
      room
    )

    // Remove from unscheduledAssignments if it exists
    if (
      updatedData.unscheduledAssignments &&
      updatedData.unscheduledAssignments.length > 0
    ) {
      updatedData.unscheduledAssignments =
        updatedData.unscheduledAssignments.filter(
          (item) =>
            !(
              item.subject === subject.subject &&
              item.teacherID === subject.teacherID &&
              item.class === subject.class
            )
        )
      console.log(
        'Removed from unscheduledAssignments:',
        updatedData.unscheduledAssignments
      )
    }

    // Update the injected data without saving to server
    // Changes will only be saved when the user clicks the "Save to Server" button
    setInjectedData(updatedData)

    // Remove the unscheduled subject from the list
    setUnscheduledSubjects((prev) => {
      const filtered = prev.filter(
        (s) =>
          !(
            s.subject === subject.subject &&
            s.teacherID === subject.teacherID &&
            s.class === subject.class
          )
      )
      console.log('Filtered unscheduled subjects:', filtered)
      return filtered
    })

    // Clear the selected unscheduled subject
    setSelectedUnscheduledSubject(null)

    toast({
      title: 'Subject Scheduled',
      description: `${subject.subject} has been scheduled to ${day} at ${time}. Click "Save to Server" to save your changes.`,
    })

    // Don't automatically analyze the schedule after rescheduling
    // This was causing the 500 errors when repeatedly calling the API
    // analyzeCurrentSchedule();
  }

  // Initialize selected class when timetable data is loaded
  // Use a ref to track if we've already set a class
  const hasSetInitialClassRef = useRef(false)

  useEffect(() => {
    const data = getDisplayData ? getDisplayData() : timetableData
    console.log('Auto-selecting class from data:', data)

    // Only auto-select if no class is currently selected or we haven't set one yet
    if (data && (!selectedClass || !hasSetInitialClassRef.current)) {
      // Handle scheduleData format
      if (data.scheduleData && data.scheduleData.length > 0) {
        console.log(
          'Auto-selecting first class from scheduleData:',
          data.scheduleData[0].class
        )
        setSelectedClass(data.scheduleData[0].class)
        hasSetInitialClassRef.current = true
      }
      // Handle schedule format
      else if (data.schedule && Object.keys(data.schedule).length > 0) {
        const firstClass = Object.keys(data.schedule)[0]
        console.log('Auto-selecting first class from schedule:', firstClass)
        setSelectedClass(firstClass)
        hasSetInitialClassRef.current = true
      }
    }

    // Force select the first class if none is selected after a short delay
    const timer = setTimeout(() => {
      if (
        !selectedClass &&
        data &&
        data.scheduleData &&
        data.scheduleData.length > 0
      ) {
        console.log(
          'Forcing selection of first class:',
          data.scheduleData[0].class
        )
        setSelectedClass(data.scheduleData[0].class)
      }
    }, 500)

    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timetableData, injectedData])

  // Load timetable data from backend on component mount
  const hasLoadedDataRef = useRef(false)

  useEffect(() => {
    const loadTimetableFromBackend = async () => {
      try {
        // Only load data if we don't already have valid data from props or injected data
        const hasValidTimetableData =
          timetableData &&
          ((timetableData.scheduleData &&
            timetableData.scheduleData.length > 0) ||
            (timetableData.schedule &&
              Object.keys(timetableData.schedule).length > 0))

        const hasValidInjectedData =
          injectedData &&
          ((injectedData.scheduleData &&
            injectedData.scheduleData.length > 0) ||
            (injectedData.schedule &&
              Object.keys(injectedData.schedule).length > 0))

        if (
          !hasValidTimetableData &&
          !hasValidInjectedData &&
          !hasLoadedDataRef.current
        ) {
          console.log('Loading timetable data from backend...')

          // Get the authentication token from localStorage
          const token = localStorage.getItem('access_token')
          if (!token) {
            console.warn(
              'No authentication token found, cannot load timetable data'
            )
            return
          }

          // Fetch the timetable data from the backend
          const response = await fetch(
            'http://localhost:3000/timetable/etablissement',
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (response.ok) {
            const data = await response.json()
            console.log('Loaded timetable data from backend:', data)

            // Check if we have valid data
            if (data && data.data) {
              console.log('Raw data from backend:', data.data)

              // Check if the data has entries array (from port 3000)
              if (
                data.data.entries &&
                Array.isArray(data.data.entries) &&
                data.data.entries.length > 0
              ) {
                console.log('Converting entries format to scheduleData format')

                // Group entries by className
                const entriesByClass = data.data.entries.reduce(
                  (acc: any, entry: any) => {
                    if (!acc[entry.className]) {
                      acc[entry.className] = {
                        class: entry.className,
                        // Initialize empty arrays for each day
                        Monday: [],
                        Tuesday: [],
                        Wednesday: [],
                        Thursday: [],
                        Friday: [],
                        Saturday: [],
                        Sunday: [],
                      }
                    }

                    // Add the entry to the appropriate day
                    if (entry.day && acc[entry.className][entry.day]) {
                      // Create a complete entry with all required fields
                      const completeEntry = {
                        id: entry.id,
                        time: entry.timeSlot || entry.time,
                        timeSlot: entry.timeSlot || entry.time,
                        subject: entry.subject,
                        teacher: entry.teacher || `Teacher ${entry.teacherId}`,
                        teacherID: entry.teacherId || entry.teacherID,
                        teacherId: entry.teacherId || entry.teacherID,
                        salle: entry.classroomId || entry.salle,
                        classroomId: entry.classroomId || entry.salle,
                        group1: entry.group1,
                        group2: entry.group2,
                        day: entry.day,
                        className: entry.className,
                      }
                      acc[entry.className][entry.day].push(completeEntry)
                    }

                    return acc
                  },
                  {}
                )

                // Convert to array format for scheduleData
                const scheduleData = Object.values(entriesByClass)
                console.log('Converted scheduleData:', scheduleData)

                // Create the properly formatted data
                const formattedData = {
                  scheduleData: scheduleData,
                  name: data.description || 'Timetable',
                  // Keep the original entries for reference
                  originalEntries: data.data.entries,
                }

                // Set the injected data with the converted data
                setInjectedData(formattedData)

                // Set the selected class to the first class
                if (scheduleData.length > 0) {
                  const firstClass = (scheduleData[0] as any).class
                  console.log('Setting selected class to:', firstClass)
                  setSelectedClass(firstClass)
                }

                // Show success toast
                toast({
                  title: 'Timetable Loaded',
                  description: `Successfully loaded timetable with ${scheduleData.length} classes.`,
                })
              } else {
                // If no entries array, just use the data as is
                console.log('Using data as is, no entries array found')
                setInjectedData(data.data)

                // Show success toast
                toast({
                  title: 'Timetable Loaded',
                  description:
                    'Successfully loaded timetable data from the server.',
                })
              }

              // Mark as loaded
              hasLoadedDataRef.current = true
            } else {
              console.warn('Timetable data from backend is empty or invalid')
              toast({
                title: 'Empty Timetable',
                description: 'No timetable data found on the server.',
                variant: 'destructive',
              })
            }
          } else {
            console.error(
              'Failed to load timetable data from backend:',
              response.status,
              response.statusText
            )
            toast({
              title: 'Failed to Load Timetable',
              description: `Error: ${response.status} ${response.statusText}`,
              variant: 'destructive',
            })
          }
        } else {
          console.log('Using existing timetable data, not loading from backend')
        }
      } catch (error) {
        console.error('Error loading timetable data from backend:', error)
        toast({
          title: 'Error Loading Timetable',
          description: 'An error occurred while loading the timetable data.',
          variant: 'destructive',
        })
      }
    }

    // Load timetable data from backend
    loadTimetableFromBackend()

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Auto-analyze on mount if requested (e.g., after first generation)
  // Use a ref to ensure we only run this once
  const hasAnalyzedRef = useRef(false)
  useEffect(() => {
    if (autoAnalyzeOnMount && !hasAnalyzedRef.current) {
      // Only analyze once on initial mount
      analyzeCurrentSchedule()
      hasAnalyzedRef.current = true
    }
    // Only run on mount or when autoAnalyzeOnMount changes to true
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoAnalyzeOnMount])

  // Load timetable data from the server when the component mounts
  useEffect(() => {
    const fetchTimetableData = async () => {
      try {
        // Get the authentication token from localStorage
        const token = localStorage.getItem('access_token')
        if (!token) {
          console.warn('No authentication token found')
          return
        }

        console.log('Fetching timetable data from server...')
        const response = await fetch(
          'http://localhost:3000/timetable/etablissement',
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (response.ok) {
          const serverData = await response.json()
          console.log('Successfully fetched timetable data from server:', serverData)

          // Store the timetable ID for future use
          if (serverData.id) {
            console.log('Storing timetable ID for future use:', serverData.id)
            localStorage.setItem('lastSavedTimetableId', serverData.id)
          }

          // Update the injected data with the server data
          if (serverData.data && serverData.data.scheduleData) {
            console.log('Updating injected data with server data')
            const savedData = {
              ...serverData,
              scheduleData: serverData.data.scheduleData,
              inputData: serverData.inputData
            }
            setInjectedData(savedData)
            console.log('Injected data updated from server:', savedData)
          }
        } else {
          console.warn('Failed to fetch timetable data from server:', response.status)
        }
      } catch (error) {
        console.error('Error fetching timetable data:', error)
      }
    }

    // Only fetch if we don't already have data
    if (!injectedData && (!timetableData || !timetableData.scheduleData)) {
      fetchTimetableData()
    }
  }, [])

  // Load unscheduled assignments when timetable data changes
  useEffect(() => {
    // Check if there are unscheduled assignments in the data
    const data = getDisplayData ? getDisplayData() : timetableData

    if (
      data &&
      data.unscheduledAssignments &&
      data.unscheduledAssignments.length > 0
    ) {
      console.log(
        'Loading unscheduled assignments from data:',
        data.unscheduledAssignments
      )

      // Convert unscheduled assignments to unscheduled subjects
      const loadedUnscheduledSubjects: UnscheduledSubject[] =
        data.unscheduledAssignments.map((assignment: TimetableAssignment) => ({
          class: assignment.class,
          subject: assignment.subject,
          required_hours: 1,
          scheduled_hours: 0,
          uncompleted_hours: 1,
          teacherID: assignment.teacherID,
          teacherName: assignment.teacher,
          available_slots: [], // We'll need to analyze to get available slots
        }))

      // Set the unscheduled subjects
      setUnscheduledSubjects(loadedUnscheduledSubjects)

      // Analyze the schedule to get available slots for these unscheduled subjects
      analyzeCurrentSchedule()
    }
  }, [timetableData, injectedData])

  // Remove the automatic analysis on timetable data changes to prevent recursion
  // We'll only analyze when the user explicitly clicks the Analyze button

  // Handle class selection
  const handleClassChange = (className: string) => {
    setSelectedClass(className)
  }

  // State for data injection dialog is defined above

  // Handle raw data injection
  const handleInjectData = () => {
    try {
      // Try to parse both data and result if provided
      let parsedData
      let parsedResult

      if (dataInput.trim()) {
        parsedData = JSON.parse(dataInput)
      }

      if (resultInput.trim()) {
        parsedResult = JSON.parse(resultInput)
        // Use result as the primary data if it has scheduleData
        if (parsedResult && parsedResult.scheduleData) {
          setInjectedData(parsedResult)
          if (parsedResult.scheduleData.length > 0) {
            setSelectedClass(parsedResult.scheduleData[0].class)
          }
          toast({
            title: 'Result Data Injected Successfully',
            description: `Loaded ${parsedResult.scheduleData.length} classes from the result data.`,
          })
          return
        }
      }

      // Fall back to using data if result doesn't have scheduleData
      if (parsedData && parsedData.scheduleData) {
        setInjectedData(parsedData)
        if (parsedData.scheduleData.length > 0) {
          setSelectedClass(parsedData.scheduleData[0].class)
        }
        toast({
          title: 'Data Injected Successfully',
          description: `Loaded ${parsedData.scheduleData.length} classes from the data.`,
        })
      } else {
        toast({
          title: 'Invalid Data Format',
          description: 'The data must contain a scheduleData array.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error injecting data:', error)
      toast({
        title: 'Failed to Parse Data',
        description: 'Please check that your JSON is valid.',
        variant: 'destructive',
      })
    }

    // Don't automatically analyze after injecting data
    // This will be done manually by the user clicking the Analyze button
  }

  // Get the data to display (either injected or from props)
  const getDisplayData = () => {
    console.log('TimetableDisplay - Data from props:', timetableData)
    console.log('TimetableDisplay - Injected data:', injectedData)

    // Use injected data if available
    if (injectedData) {
      return injectedData;
    }

    // Use timetable data from props
    if (timetableData) {
      // If timetableData has a data property with scheduleData, extract it
      if (timetableData.data && timetableData.data.scheduleData) {
        return {
          ...timetableData,
          scheduleData: timetableData.data.scheduleData,
          inputData: timetableData.inputData
        };
      }

      // If timetableData already has scheduleData, use it directly
      if (timetableData.scheduleData) {
        return timetableData;
      }
    }

    return timetableData;
  }

  // Get all classes from the data
  const getClasses = () => {
    const data = getDisplayData()
    if (!data) return []

    console.log('Getting classes from data:', data)

    // Handle different data formats
    if (data.scheduleData) {
      const classes = data.scheduleData.map((item: any) => item.class)
      console.log('Found classes in scheduleData:', classes)
      return classes
    } else if (data.schedule) {
      const classes = Object.keys(data.schedule)
      console.log('Found classes in schedule:', classes)
      return classes
    }

    console.log('No classes found in data')
    return []
  }

  // Get the schedule for the selected class
  const getSelectedClassSchedule = () => {
    const data = getDisplayData()
    if (!data) return null

    console.log(
      'Getting schedule for class:',
      selectedClass,
      'from data:',
      data
    )

    // Handle different data formats
    if (data.scheduleData) {
      const classSchedule = data.scheduleData.find(
        (item: any) => item.class === selectedClass
      )
      if (classSchedule) {
        // Process each day to ensure time formats are consistent
        const processedSchedule = { ...classSchedule }

        // Process each day's periods
        Object.keys(processedSchedule).forEach((day) => {
          if (day !== 'class' && Array.isArray(processedSchedule[day])) {
            processedSchedule[day] = processedSchedule[day].map(
              (period: any) => {
                // Skip if already processed or not a valid period
                if (!period || typeof period !== 'object') return period

                // Create a copy of the period
                const processedPeriod = { ...period }

                // Ensure time property exists and is properly formatted
                if (!processedPeriod.time) {
                  if (processedPeriod.start && processedPeriod.end) {
                    processedPeriod.time = `${processedPeriod.start} => ${processedPeriod.end}`
                  } else if (processedPeriod.period !== undefined) {
                    processedPeriod.time = formatTimeSlot(
                      processedPeriod.period
                    )
                  }
                }

                return processedPeriod
              }
            )
          }
        })

        return processedSchedule
      }
      return null
    } else if (data.schedule && data.schedule[selectedClass]) {
      // Convert the schedule format to match the scheduleData format
      const dayMap: Record<string, any[]> = {}

      data.schedule[selectedClass].forEach((item: any) => {
        if (!dayMap[item.day]) {
          dayMap[item.day] = []
        }

        // Determine the time format
        let timeValue
        if (item.time) {
          // Use the time directly if it exists
          timeValue = item.time
        } else if (item.start && item.end) {
          // Combine start and end if they exist
          timeValue = `${item.start} => ${item.end}`
        } else if (item.period !== undefined) {
          // Use period number as a fallback
          timeValue = formatTimeSlot(item.period)
        } else {
          // Default fallback
          timeValue = 'No time specified'
        }

        dayMap[item.day].push({
          time: timeValue,
          subject: item.subject,
          teacher: item.teacher,
          salle: item.room || item.salle,
        })
      })

      return {
        class: selectedClass,
        ...dayMap,
      }
    }

    return null
  }

  // Get all days that should be displayed in the timetable
  const getDays = () => {
    const schedule = getSelectedClassSchedule()

    // Define the standard week days that should always be shown
    const standardDays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
    ]

    if (!schedule) return standardDays

    console.log('Schedule for selected class:', schedule)

    // Get days from the schedule
    const scheduleDays = Object.keys(schedule).filter((key) => {
      // Skip the 'class' property
      if (key === 'class') return false

      // Include all day properties, even if they're empty arrays
      return true
    })

    // Combine standard days with any additional days from the schedule
    const allDays = new Set([...standardDays, ...scheduleDays])

    // Define the correct day order - standard week order
    const dayOrder = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ]

    // Sort the days according to the defined order
    const sortedDays = Array.from(allDays).sort((a, b) => {
      const indexA = dayOrder.indexOf(a)
      const indexB = dayOrder.indexOf(b)

      // If both days are in the order array, sort by their position
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB
      }

      // If only one day is in the order array, prioritize it
      if (indexA !== -1) return -1
      if (indexB !== -1) return 1

      // If neither day is in the order array, maintain alphabetical order
      return a.localeCompare(b)
    })

    console.log('Days for timetable (sorted):', sortedDays)
    return sortedDays
  }

  // Get all unique time slots across all days
  const getTimeSlots = () => {
    const schedule = getSelectedClassSchedule()
    const data = getDisplayData()
    
    // First, try to extract time slots directly from scheduleData
    if (data && data.scheduleData && Array.isArray(data.scheduleData)) {
      console.log('Extracting time slots directly from scheduleData')
      const timeSlotSet = new Set<string>()
      
      // Go through each class schedule
      data.scheduleData.forEach((classData: any) => {
        // Go through each day
        Object.keys(classData).forEach((day) => {
          if (day !== 'class' && Array.isArray(classData[day])) {
            // Extract time slots from each lesson
            classData[day].forEach((lesson: any) => {
              if (lesson && lesson.time) {
                timeSlotSet.add(lesson.time)
              }
            })
          }
        })
      })
      
      // If we found time slots, sort and return them
      if (timeSlotSet.size > 0) {
        const sortedTimeSlots = Array.from(timeSlotSet).sort((a, b) => {
          const [aStartStr] = a.split('=>').map((s) => s.trim())
          const [bStartStr] = b.split('=>').map((s) => s.trim())
          
          const aMinutes = convertTimeToMinutes(aStartStr)
          const bMinutes = convertTimeToMinutes(bStartStr)
          
          return aMinutes - bMinutes
        })
        
        console.log('Using time slots extracted from scheduleData:', sortedTimeSlots)
        return sortedTimeSlots
      }
    }
    
    // Second, check if we have time slots in the inputData
    if (data && data.inputData && data.inputData.timeSlots) {
      console.log('Found inputData.timeSlots in API data:', data.inputData.timeSlots)
      
      // Collect all unique time slots from all days in the API data
      const apiTimeSlots = new Set<string>()
      Object.values(data.inputData.timeSlots).forEach((dayData: any) => {
        if (dayData.studyTimes && Array.isArray(dayData.studyTimes)) {
          dayData.studyTimes.forEach((timeSlot: string) => {
            apiTimeSlots.add(timeSlot)
          })
        }
      })
      
      // If we found time slots in the API data, use those
      if (apiTimeSlots.size > 0) {
        const sortedApiTimeSlots = Array.from(apiTimeSlots).sort((a, b) => {
          const [aStartStr] = a.split('=>').map((s) => s.trim())
          const [bStartStr] = b.split('=>').map((s) => s.trim())
          
          const aMinutes = convertTimeToMinutes(aStartStr)
          const bMinutes = convertTimeToMinutes(bStartStr)
          
          return aMinutes - bMinutes
        })
        
        console.log('Using time slots from inputData:', sortedApiTimeSlots)
        return sortedApiTimeSlots
      }
    }

    // Define default time slots as a last resort
    const defaultTimeSlots = [
      '8 => 9',
      '9 => 10',
      '10 => 11',
      '11 => 12',
      '12 => 13',
      '13 => 14',
      '14 => 15',
      '15 => 16',
      '16 => 17',
    ]

    // If no time slots found in data, use the selected class schedule
    if (!schedule) {
      console.log('No schedule found, using default time slots')
      return defaultTimeSlots
    }

    console.log('Getting time slots from selected class schedule:', schedule)

    const timeSlots = new Set<string>(defaultTimeSlots)
    const days = getDays()

    // Try to extract additional time slots from each day
    if (days.length > 0) {
      days.forEach((day) => {
        if (Array.isArray(schedule[day])) {
          schedule[day].forEach((lesson: any) => {
            if (lesson && lesson.time) {
              timeSlots.add(lesson.time)
            }
          })
        }
      })
    }

    // Sort time slots
    const sortedTimeSlots = Array.from(timeSlots).sort((a, b) => {
      // Try to parse the time slots
      const [aStartStr] = a.split('=>').map((s) => s.trim())
      const [bStartStr] = b.split('=>').map((s) => s.trim())

      // Convert to minutes for comparison
      const aMinutes = convertTimeToMinutes(aStartStr)
      const bMinutes = convertTimeToMinutes(bStartStr)

      return aMinutes - bMinutes
    })

    console.log('Using time slots from schedule with defaults:', sortedTimeSlots)
    return sortedTimeSlots
  }

  // Helper function to convert time string to minutes for sorting
  const convertTimeToMinutes = (timeStr: string): number => {
    // Handle different time formats
    if (timeStr.includes(':')) {
      // Format: "08:00"
      const [hours, minutes] = timeStr.split(':').map(Number)
      return hours * 60 + (minutes || 0)
    } else {
      // Format: "8" (just the hour)
      return parseInt(timeStr) * 60
    }
  }

  // Helper function to format period numbers to time format
  const formatTimeSlot = (period: number): string => {
    // Convert period numbers to time format (e.g., 1 => "8:00 => 8:45")
    const startHour = 7 + period
    const endHour = startHour + 1
    return `${startHour}:00 => ${endHour}:00`
  }

  // Helper function to get a color for a subject
  const getSubjectColor = (
    subjectName: string | null | undefined
  ): string | null => {
    if (!subjectName) return null

    // If we already have a color for this subject, return it
    if (subjectColors[subjectName]) {
      return subjectColors[subjectName]
    }

    // Otherwise, generate a random color and store it
    const colors = [
      '#FF5733',
      '#33FF57',
      '#3357FF',
      '#F033FF',
      '#FF33F0',
      '#33FFF0',
      '#F0FF33',
      '#FF3333',
      '#33FF33',
      '#3333FF',
      '#FFAA33',
      '#33FFAA',
      '#AA33FF',
      '#FF33AA',
      '#33AAFF',
    ]
    const randomColor = colors[Math.floor(Math.random() * colors.length)]
    subjectColors[subjectName] = randomColor
    return randomColor
  }

  // Helper function to determine if text should be white or black based on background color
  const getContrastColor = (hexColor: string | null): string => {
    if (!hexColor) return '#000000' // Default to black

    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16)
    const g = parseInt(hexColor.slice(3, 5), 16)
    const b = parseInt(hexColor.slice(5, 7), 16)

    // Calculate luminance - <url id="" type="url" status="" title="" wc="">https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests</url>
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

    // Return white for dark colors, black for light colors
    return luminance > 0.5 ? '#000000' : '#FFFFFF'
  }

  // Get lesson for a specific day and time
  const getLesson = (day: string, time: string) => {
    const schedule = getSelectedClassSchedule()
    if (!schedule || !schedule[day]) return null

    // Make sure schedule[day] is an array
    if (!Array.isArray(schedule[day])) {
      console.log(`Schedule for ${day} is not an array:`, schedule[day])
      return null
    }

    // Find the lesson with the matching time (check both time and timeSlot fields)
    // Also handle missing fields by adding them if needed
    const lesson = schedule[day].find(
      (lesson: any) =>
        lesson && (lesson.time === time || lesson.timeSlot === time)
    )

    // If we found a lesson, make sure it has all the required fields
    if (lesson) {
      // Add missing fields if needed
      if (!lesson.time && lesson.timeSlot) {
        lesson.time = lesson.timeSlot
      } else if (!lesson.timeSlot && lesson.time) {
        lesson.timeSlot = lesson.time
      }

      if (!lesson.salle && lesson.classroomId) {
        lesson.salle = lesson.classroomId
      } else if (!lesson.classroomId && lesson.salle) {
        lesson.classroomId = lesson.salle
      }

      if (!lesson.teacher && lesson.teacherId) {
        lesson.teacher = `Teacher ${lesson.teacherId}`
      }

      if (!lesson.teacherID && lesson.teacherId) {
        lesson.teacherID = lesson.teacherId
      } else if (!lesson.teacherId && lesson.teacherID) {
        lesson.teacherId = lesson.teacherID
      }
    }

    if (lesson) {
      console.log(`Found lesson for ${day} at ${time}:`, lesson)
    }

    return lesson
  }

  // Export timetable as PDF
  const exportToPdf = async () => {
    try {
      setIsPdfExporting(true)
      toast({
        title: 'Exporting Timetable',
        description: 'Please wait while we generate your PDF...',
      })

      // Get all classes
      const classes = getClasses()
      if (classes.length === 0) {
        toast({
          title: 'Export Failed',
          description: 'No classes found to export',
          variant: 'destructive',
        })
        return
      }

      // Create PDF
      const pdf = new jsPDF('l', 'mm', 'a4') // Landscape orientation
      const pageWidth = 297 // A4 landscape width in mm
      const pageHeight = 210 // A4 landscape height in mm
      const margin = 10

      // Get the timetable data
      const timetableData = getDisplayData()
      const schoolName = timetableData?.schoolName || 'School Timetable'
      const address = timetableData?.address || 'School Address'

      // Store the current selected class to restore later
      const currentSelectedClass = selectedClass

      // For each class, create a page in the PDF
      for (let i = 0; i < classes.length; i++) {
        const className = classes[i]

        // Add a new page for each class except the first one
        if (i > 0) {
          pdf.addPage()
        }

        // Add school logo or name at the top
        pdf.setFillColor(255, 255, 255)
        pdf.rect(0, 0, pageWidth, 30, 'F')

        // Add title
        pdf.setFontSize(16)
        pdf.setFont('helvetica', 'bold')
        pdf.text(schoolName, pageWidth / 2, 15, { align: 'center' })

        // Add class name
        pdf.setFontSize(14)
        pdf.text(className, pageWidth / 2, 25, { align: 'center' })

        // Add address line
        pdf.setFontSize(8)
        pdf.setFont('helvetica', 'normal')
        pdf.text(address, pageWidth / 2, 32, { align: 'center' })

        // Get data for this specific class without changing the selected class state
        let classData = null

        // Find the data for this specific class
        if (timetableData.scheduleData) {
          classData = timetableData.scheduleData.find(
            (item: any) => item.class === className
          )
        } else if (
          timetableData.schedule &&
          timetableData.schedule[className]
        ) {
          // Convert schedule format to scheduleData format
          const dayMap: Record<string, any[]> = {}

          timetableData.schedule[className].forEach((item: any) => {
            if (!dayMap[item.day]) {
              dayMap[item.day] = []
            }

            // Determine the time format
            let timeValue
            if (item.time) {
              timeValue = item.time
            } else if (item.start && item.end) {
              timeValue = `${item.start} => ${item.end}`
            } else if (item.period !== undefined) {
              timeValue = formatTimeSlot(item.period)
            } else {
              timeValue = 'No time specified'
            }

            dayMap[item.day].push({
              time: timeValue,
              subject: item.subject,
              teacher: item.teacher,
              salle: item.room || item.salle,
            })
          })

          classData = {
            class: className,
            ...dayMap,
          }
        }

        if (!classData) {
          // If no data for this class, add a message
          pdf.setFontSize(12)
          pdf.text(
            'No timetable data available for this class',
            pageWidth / 2,
            60,
            { align: 'center' }
          )
          continue
        }

        // Get days from the class data
        const days = Object.keys(classData).filter((key) => {
          // Skip the 'class' property
          if (key === 'class') return false

          // Check if the property is an array and has items
          if (Array.isArray(classData[key]) && classData[key].length > 0) {
            return true
          }

          return false
        })

        // Sort days according to the defined order - standard week order
        const dayOrder = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ]
        const sortedDays = days.sort((a, b) => {
          const indexA = dayOrder.indexOf(a)
          const indexB = dayOrder.indexOf(b)

          if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB
          }

          if (indexA !== -1) return -1
          if (indexB !== -1) return 1

          return a.localeCompare(b)
        })

        // Get time slots from the class data
        const timeSlotSet = new Set<string>()
        sortedDays.forEach((day) => {
          if (Array.isArray(classData[day])) {
            classData[day].forEach((lesson: any) => {
              if (lesson && lesson.time) {
                timeSlotSet.add(lesson.time)
              }
            })
          }
        })

        // If no time slots were found, use default ones
        let timeSlots = Array.from(timeSlotSet)
        if (timeSlots.length === 0) {
          timeSlots = [
            '08:00 => 08:45',
            '08:45 => 09:30',
            '09:30 => 10:15',
            '10:15 => 11:00',
            '11:00 => 11:45',
            '11:45 => 12:30',
            '13:00 => 13:45',
            '13:45 => 14:30',
            '14:30 => 15:15',
            '15:15 => 16:00',
            '16:00 => 16:45',
          ]
        } else {
          // Sort time slots
          timeSlots = timeSlots.sort((a, b) => {
            const [aStartStr] = a.split('=>').map((s) => s.trim())
            const [bStartStr] = b.split('=>').map((s) => s.trim())

            const aMinutes = convertTimeToMinutes(aStartStr)
            const bMinutes = convertTimeToMinutes(bStartStr)

            return aMinutes - bMinutes
          })
        }

        if (sortedDays.length === 0 || timeSlots.length === 0) {
          // If no days with lessons, add a message
          pdf.setFontSize(12)
          pdf.text(
            'No timetable data available for this class',
            pageWidth / 2,
            60,
            { align: 'center' }
          )
          continue
        }

        // Calculate table dimensions
        const tableTop = 40
        const tableWidth = pageWidth - margin * 2
        const colWidth = tableWidth / (sortedDays.length + 1) // +1 for time column
        const headerRowHeight = 15 // Fixed smaller height for the header row
        const rowHeight =
          (pageHeight - tableTop - margin - headerRowHeight) / timeSlots.length

        // Draw table header
        pdf.setFillColor(240, 240, 240)
        pdf.rect(margin, tableTop, tableWidth, headerRowHeight, 'F')

        // Draw time column header
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'bold')
        pdf.text(
          'Time',
          margin + colWidth / 2,
          tableTop + headerRowHeight / 2,
          { align: 'center', baseline: 'middle' }
        )

        // Draw day headers
        sortedDays.forEach((day, index) => {
          const x = margin + colWidth + index * colWidth
          pdf.text(day, x + colWidth / 2, tableTop + headerRowHeight / 2, {
            align: 'center',
            baseline: 'middle',
          })
        })

        // Draw time slots and lessons
        timeSlots.forEach((time, rowIndex) => {
          const y = tableTop + headerRowHeight + rowIndex * rowHeight

          // Draw time slot
          pdf.setFontSize(8)
          pdf.setFont('helvetica', 'normal')
          pdf.text(time, margin + colWidth / 2, y + rowHeight / 2, {
            align: 'center',
            baseline: 'middle',
          })

          // Draw lessons for each day
          sortedDays.forEach((day, colIndex) => {
            const x = margin + colWidth + colIndex * colWidth

            // Get lesson for this day and time
            let lesson = null
            if (classData[day] && Array.isArray(classData[day])) {
              lesson = classData[day].find((l: any) => l && l.time === time)
            }

            if (lesson) {
              // Draw cell with lesson info
              pdf.setDrawColor(200, 200, 200)
              pdf.rect(x, y, colWidth, rowHeight, 'S')

              // Draw subject
              pdf.setFontSize(8)
              pdf.setFont('helvetica', 'bold')
              const subject = lesson.subject || ''
              pdf.text(subject, x + colWidth / 2, y + rowHeight * 0.3, {
                align: 'center',
                baseline: 'middle',
              })

              // Draw teacher
              pdf.setFontSize(7)
              pdf.setFont('helvetica', 'normal')
              const teacher = lesson.teacher || ''
              pdf.text(teacher, x + colWidth / 2, y + rowHeight * 0.5, {
                align: 'center',
                baseline: 'middle',
              })

              // Draw room
              const room = lesson.salle || lesson.room || ''
              pdf.text(room, x + colWidth / 2, y + rowHeight * 0.7, {
                align: 'center',
                baseline: 'middle',
              })

              // Handle group lessons
              if (lesson.group1 || lesson.group2) {
                // Draw divider for groups
                pdf.setDrawColor(180, 180, 180)
                pdf.line(x, y + rowHeight / 2, x + colWidth, y + rowHeight / 2)

                // Group 1
                if (lesson.group1) {
                  pdf.setFontSize(6)
                  pdf.setFont('helvetica', 'bold')
                  pdf.text(
                    'G1: ' + (lesson.group1.subject || ''),
                    x + colWidth / 2,
                    y + rowHeight * 0.25,
                    { align: 'center', baseline: 'middle' }
                  )

                  pdf.setFontSize(6)
                  pdf.setFont('helvetica', 'normal')
                  pdf.text(
                    lesson.group1.teacher || '',
                    x + colWidth / 2,
                    y + rowHeight * 0.4,
                    { align: 'center', baseline: 'middle' }
                  )
                }

                // Group 2
                if (lesson.group2) {
                  pdf.setFontSize(6)
                  pdf.setFont('helvetica', 'bold')
                  pdf.text(
                    'G2: ' + (lesson.group2.subject || ''),
                    x + colWidth / 2,
                    y + rowHeight * 0.65,
                    { align: 'center', baseline: 'middle' }
                  )

                  pdf.setFontSize(6)
                  pdf.setFont('helvetica', 'normal')
                  pdf.text(
                    lesson.group2.teacher || '',
                    x + colWidth / 2,
                    y + rowHeight * 0.8,
                    { align: 'center', baseline: 'middle' }
                  )
                }
              }
            } else {
              // Empty cell
              pdf.setDrawColor(220, 220, 220)
              pdf.rect(x, y, colWidth, rowHeight, 'S')
            }
          })

          // Draw time column border
          pdf.setDrawColor(200, 200, 200)
          pdf.rect(margin, y, colWidth, rowHeight, 'S')
        })

        // Draw outer table border
        pdf.setDrawColor(0, 0, 0)
        pdf.rect(
          margin,
          tableTop,
          tableWidth,
          headerRowHeight + rowHeight * timeSlots.length,
          'S'
        )

        // Add footer with date
        pdf.setFontSize(8)
        pdf.text(
          `Generated on: ${new Date().toLocaleDateString()}`,
          pageWidth - margin,
          pageHeight - 5,
          { align: 'right' }
        )
      }

      // Restore the original selected class
      setSelectedClass(currentSelectedClass || classes[0])

      // Save the PDF
      pdf.save(`school_timetable.pdf`)

      toast({
        title: 'Export Successful',
        description: `Timetable for all ${classes.length} classes has been exported as a PDF`,
      })
    } catch (error) {
      console.error('Error exporting to PDF:', error)
      toast({
        title: 'Export Failed',
        description: 'An error occurred while exporting the timetable',
        variant: 'destructive',
      })
    } finally {
      setIsPdfExporting(false)
    }
  }

  // Render the timetable
  const renderTimetable = () => {
    const days = getDays()
    const timeSlots = getTimeSlots()

    console.log(
      'Rendering timetable with days:',
      days,
      'and time slots:',
      timeSlots
    )

    // Always show the timetable, even if there are no days or time slots
    // This will use the default time slots if none are found
    if (days.length === 0) {
      console.log('No days found for rendering timetable')
      return (
        <div className="text-center p-8">
          <p className="text-muted-foreground">
            No schedule data available for this class.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Try generating a timetable first or selecting a different class.
          </p>
        </div>
      )
    }

    return (
      <div className="overflow-x-auto timetable-grid" ref={timetableRef}>
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="border p-2 bg-muted font-medium">Time</th>
              {days.map((day) => (
                <th
                  key={day}
                  className="border p-2 bg-muted font-medium min-w-[150px]"
                >
                  {day}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {timeSlots.map((time) => (
              <tr key={time}>
                <td className="border p-2 bg-muted/20 font-medium text-sm">
                  {time}
                </td>
                {days.map((day) => {
                  const lesson = getLesson(day, time)
                  return (
                    <td key={`${day}-${time}`} className="border p-2 align-top">
                      {lesson && (lesson.group1 || lesson.group2) ? (
                        <div
                          className="rounded-md p-2"
                          style={{
                            backgroundColor: 'rgba(235, 245, 255, 0.5)',
                          }}
                        >
                          {/* Group 1 */}
                          {lesson.group1 && (
                            <div className="mb-2">
                              <div className="font-semibold text-sm">
                                <Badge
                                  variant="outline"
                                  className="mr-1"
                                  style={{
                                    backgroundColor:
                                      getSubjectColor(lesson.group1.subject) ||
                                      '#e2f0ff',
                                    borderColor:
                                      getSubjectColor(lesson.group1.subject) ||
                                      '#e2f0ff',
                                    color: getContrastColor(
                                      getSubjectColor(lesson.group1.subject) ||
                                        '#e2f0ff'
                                    ),
                                  }}
                                >
                                  Group 1
                                </Badge>
                                {lesson.group1.subject}
                              </div>
                              <div className="text-xs text-gray-600 flex items-center mt-1">
                                <User className="h-3 w-3 mr-1" />
                                {lesson.group1.teacher}
                              </div>
                              {lesson.group1.salle && (
                                <div className="text-xs text-gray-600">
                                  {lesson.group1.salle}
                                </div>
                              )}
                            </div>
                          )}

                          {/* Group 2 */}
                          {lesson.group2 && (
                            <div
                              className={
                                lesson.group1
                                  ? 'pt-1 border-t border-dashed border-gray-300'
                                  : ''
                              }
                            >
                              <div className="font-semibold text-sm">
                                <Badge
                                  variant="outline"
                                  className="mr-1"
                                  style={{
                                    backgroundColor:
                                      getSubjectColor(lesson.group2.subject) ||
                                      '#e8f5e9',
                                    borderColor:
                                      getSubjectColor(lesson.group2.subject) ||
                                      '#e8f5e9',
                                    color: getContrastColor(
                                      getSubjectColor(lesson.group2.subject) ||
                                        '#e8f5e9'
                                    ),
                                  }}
                                >
                                  Group 2
                                </Badge>
                                {lesson.group2.subject}
                              </div>
                              <div className="text-xs text-gray-600 flex items-center mt-1">
                                <User className="h-3 w-3 mr-1" />
                                {lesson.group2.teacher}
                              </div>
                              {lesson.group2.salle && (
                                <div className="text-xs text-gray-600">
                                  {lesson.group2.salle}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        <DroppableCell day={day} time={time}>
                          <DraggableLesson
                            lesson={lesson}
                            day={day}
                            time={time}
                          />
                        </DroppableCell>
                      )}
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  // Create a droppable cell component
  const DroppableCell = ({
    children,
    day,
    time,
  }: {
    children: React.ReactNode
    day: string
    time: string
  }) => {
    // Check if this cell is an available slot for the selected unscheduled subject
    const isAvailableSlot = selectedUnscheduledSubject
      ? selectedUnscheduledSubject.available_slots.some(
          (slot) => slot.day === day && slot.time === time
        )
      : false

    // Use the useDrop hook from react-dnd
    const [{ isOver }, drop] = useDrop(() => ({
      accept: [ItemTypes.UNSCHEDULED_SUBJECT, ItemTypes.ASSIGNMENT],
      drop: (item: {
        subject?: UnscheduledSubject
        assignment?: TimetableAssignment
      }) => {
        console.log('Dropped item in cell:', item, 'day:', day, 'time:', time)

        // Handle unscheduled subject drop
        if (item.subject) {
          // Find available rooms for this slot if any
          let room: string | undefined
          const availableSlot = item.subject.available_slots.find(
            (slot) => slot.day === day && slot.time === time
          )

          if (availableSlot && availableSlot.available_rooms.length > 0) {
            room = availableSlot.available_rooms[0]
          }

          // Handle the drop of an unscheduled subject
          handleRescheduleUnscheduledSubject(item.subject, day, time, room)
          // Clear the selected unscheduled subject
          setSelectedUnscheduledSubject(null)
        }
        // Handle assignment drop (moving a lesson)
        else if (item.assignment) {
          // First unschedule from original position
          const updatedData = removeAssignmentFromTimetableData(
            getDisplayData() as TimetableData,
            item.assignment
          )

          // Then create a new assignment at the new position
          const newAssignment: TimetableAssignment = {
            ...item.assignment,
            day,
            time,
          }

          // Add to timetable data
          const finalData = addAssignmentToTimetableData(
            updatedData,
            newAssignment,
            newAssignment.salle
          )

          // Update the injected data without saving to server
          // Changes will only be saved when the user clicks the "Save to Server" button
          setInjectedData(finalData)

          toast({
            title: 'Lesson Moved',
            description: `${newAssignment.subject} has been moved to ${day} at ${time}. Click "Save to Server" to save your changes.`,
          })
        }

        return { day, time }
      },
      collect: (monitor: any) => ({
        isOver: !!monitor.isOver(),
      }),
    }))

    return (
      <div
        ref={drop}
        className={`w-full h-full ${isOver ? 'bg-blue-100' : ''} ${isAvailableSlot ? 'bg-green-200 border-2 border-green-500' : ''}`}
        style={{ minHeight: '60px' }}
      >
        {children}
      </div>
    )
  }

  // Create a draggable lesson component
  const DraggableLesson = ({
    lesson,
    day,
    time,
  }: {
    lesson: any
    day: string
    time: string
  }) => {
    // Skip if no lesson or if it's a group lesson
    if (!lesson || lesson.group1 || lesson.group2) {
      return (
        <div
          className={`rounded-md p-2 ${!lesson ? 'h-12 flex items-center justify-center text-xs text-gray-400' : ''}`}
          style={{
            backgroundColor: lesson?.subject
              ? `${getSubjectColor(lesson.subject) || '#f5f5f5'}40` // 40 is hex for 25% opacity
              : 'rgba(245, 245, 245, 0.5)',
          }}
        >
          {lesson ? (
            <>
              <div className="font-semibold text-sm">
                {lesson.subject || 'Free Period'}
              </div>
              {(lesson.teacher || lesson.teacherId || lesson.teacherID) && (
                <div className="text-xs text-gray-600 flex items-center mt-1">
                  <User className="h-3 w-3 mr-1" />
                  {lesson.teacher ||
                    `Teacher ${lesson.teacherId || lesson.teacherID || ''}`}
                </div>
              )}
              {(lesson.salle || lesson.classroomId) && (
                <div className="text-xs text-gray-600">
                  {lesson.salle || lesson.classroomId}
                </div>
              )}
            </>
          ) : (
            <div className="text-center">
              <span className="text-xs text-gray-400">Free</span>
            </div>
          )}
        </div>
      )
    }

    // Create a TimetableAssignment from the lesson
    const assignment: TimetableAssignment = {
      ...lesson,
      day,
      time,
      class: selectedClass,
    }

    // Use the useDrag hook from react-dnd
    const [{ isDragging }, drag] = useDrag(() => ({
      type: ItemTypes.ASSIGNMENT,
      item: { assignment },
      collect: (monitor: any) => ({
        isDragging: !!monitor.isDragging(),
      }),
    }))

    return (
      <div
        ref={drag}
        className="cursor-move rounded-md p-2"
        style={{
          backgroundColor: lesson.subject
            ? `${getSubjectColor(lesson.subject) || '#f5f5f5'}40` // 40 is hex for 25% opacity
            : 'rgba(245, 245, 245, 0.5)',
          opacity: isDragging ? 0.5 : 1,
        }}
        onClick={() => handleUnscheduleLesson(assignment)}
      >
        <div className="font-semibold text-sm">
          {lesson.subject || 'Free Period'}
        </div>
        {(lesson.teacher || lesson.teacherId || lesson.teacherID) && (
          <div className="text-xs text-gray-600 flex items-center mt-1">
            <User className="h-3 w-3 mr-1" />
            {lesson.teacher ||
              `Teacher ${lesson.teacherId || lesson.teacherID || ''}`}
          </div>
        )}
        {(lesson.salle || lesson.classroomId) && (
          <div className="text-xs text-gray-600">
            {lesson.salle || lesson.classroomId}
          </div>
        )}
      </div>
    )
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex gap-4">
        <div className="w-64">
          <UnscheduleZone onUnschedule={handleUnscheduleLesson} />

          {unscheduledSubjects.length > 0 && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-sm">Unscheduled Subjects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {unscheduledSubjects.map((subject, index) => (
                    <DraggableUnscheduledSubject
                      key={index}
                      subject={subject}
                      onReschedule={handleRescheduleUnscheduledSubject}
                      isSelected={
                        selectedUnscheduledSubject?.subject ===
                          subject.subject &&
                        selectedUnscheduledSubject?.teacherID ===
                          subject.teacherID
                      }
                      onSelect={() => {
                        if (
                          selectedUnscheduledSubject?.subject ===
                            subject.subject &&
                          selectedUnscheduledSubject?.teacherID ===
                            subject.teacherID
                        ) {
                          // If already selected, deselect it
                          setSelectedUnscheduledSubject(null)
                        } else {
                          // Otherwise, select it
                          setSelectedUnscheduledSubject(subject)
                        }
                      }}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <Card className="w-full">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="timetable-header">Timetable View</CardTitle>
              <CardDescription>
                {getDisplayData()?.name || 'School Timetable'}
              </CardDescription>
              <div className="mt-2">
                <TimetableTour />
              </div>
            </div>
            <div className="flex gap-2">
              {onBack && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onBack}
                  className="back-to-dashboard"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              )}
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileUp className="h-4 w-4 mr-1" />
                    Inject Data
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Inject Timetable Data</DialogTitle>
                  </DialogHeader>
                  <div className="py-4 space-y-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">
                        Data (Original timetable data):
                      </h3>
                      <Textarea
                        placeholder="Paste your original JSON data here..."
                        className="min-h-[150px] font-mono text-sm"
                        value={dataInput}
                        onChange={(e) => setDataInput(e.target.value)}
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-2">
                        Result (Modified timetable data):
                      </h3>
                      <Textarea
                        placeholder="Paste your result JSON data here..."
                        className="min-h-[150px] font-mono text-sm"
                        value={resultInput}
                        onChange={(e) => setResultInput(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleInjectData}>Inject Data</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              {onRegenerate && (
                <Button variant="outline" size="sm" onClick={onRegenerate}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Regenerate
                </Button>
              )}
              {onDelete && (
                <Button variant="outline" size="sm" onClick={onDelete}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-muted-foreground" />
                <h3 className="text-lg font-medium">Class Selection</h3>
              </div>
              <ScrollArea className="whitespace-nowrap pb-2">
                <div className="flex gap-2">
                  {getClasses().map((className: string) => (
                    <Button
                      key={className}
                      variant={
                        selectedClass === className ? 'default' : 'outline'
                      }
                      size="sm"
                      onClick={() => handleClassChange(className)}
                    >
                      {className}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>

            <Separator />

            <ScrollArea className="h-[calc(100vh-350px)] min-h-[400px]">
              {renderTimetable()}
            </ScrollArea>

            <div className="flex justify-between">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={analyzeCurrentSchedule}
                  disabled={isAnalyzing}
                  className="conflict-detection teacher-availability"
                >
                  {isAnalyzing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 mr-1" />
                      Analyze Schedule
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsSaving(true)
                    saveTimetableToServer(
                      getDisplayData() as TimetableData
                    ).finally(() => setIsSaving(false))
                  }}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <FileUp className="h-4 w-4 mr-1" />
                      Save to Server
                    </>
                  )}
                </Button>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={exportToPdf}
                disabled={isPdfExporting}
                className="export-options"
              >
                {isPdfExporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <FileDown className="h-4 w-4 mr-1" />
                    Export as PDF
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DndProvider>
  )
}
