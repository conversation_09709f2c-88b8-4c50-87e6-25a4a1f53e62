import React from 'react'
import { useTourStore } from '@/store/tourStore'

interface StartTourButtonProps {
  className?: string
  style?: React.CSSProperties
}

export function StartTourButton({ className, style }: StartTourButtonProps) {
  const { startAdminTimetableTour, setAdminTimetableTourStep } = useTourStore()

  const handleClick = () => {
    console.log('StartTourButton: Button clicked')

    // Reset tour state
    localStorage.removeItem('tour_completed_admin_timetable')
    localStorage.removeItem('tour_skipped_admin_timetable')
    localStorage.removeItem('admin_timetable_tour_current_step')
    localStorage.setItem('trigger_admin_timetable_tour', 'true')

    // Start from the beginning
    setAdminTimetableTourStep(0)

    // Start the tour
    setTimeout(() => {
      startAdminTimetableTour()
    }, 100)

    // Dispatch an event for other components
    window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))
  }

  return (
    <button
      onClick={handleClick}
      className={
        className ||
        'bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded'
      }
      style={style}
    >
      Take Tour
    </button>
  )
}
