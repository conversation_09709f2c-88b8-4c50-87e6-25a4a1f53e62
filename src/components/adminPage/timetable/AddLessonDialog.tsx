import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Plus } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { LessonForm } from './LessonForm'
import {
  createDividedLessons,
  createAlternatingLessons,
} from '@/utils/lessonGroupUtils'

interface Subject {
  id: string | number
  name: string
}

interface Teacher {
  id: string | number
  name: string
  subjects: string[]
}

interface Room {
  id: string | number
  name: string
  type: string
}

interface Class {
  id: string | number
  name: string
}

interface LessonFormValues {
  subject: string
  teacher: string
  class: string
  room?: string
  hours: number
  isGrouped: boolean
  divide?: number[]
  alternateWith?: string
  alternateWithTeacher?: string
  alternationPattern?: 'weekly' | 'biweekly'
}

interface AddLessonDialogProps {
  subjects: Subject[]
  teachers: Teacher[]
  rooms: Room[]
  classes: Class[]
  onLessonAdded: (lessons: any[]) => void
  buttonText?: string
  buttonVariant?: 'default' | 'outline' | 'secondary'
}

export function AddLessonDialog({
  subjects,
  teachers,
  rooms,
  classes,
  onLessonAdded,
  buttonText = 'Add Lesson',
  buttonVariant = 'default',
}: AddLessonDialogProps) {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()

  const handleSubmit = (values: LessonFormValues) => {
    // Get subject and teacher names
    const subject =
      subjects.find((s) => s.id.toString() === values.subject)?.name || ''
    const teacher =
      teachers.find((t) => t.id.toString() === values.teacher)?.name || ''
    const className =
      classes.find((c) => c.id.toString() === values.class)?.name || ''
    const room = values.room
      ? rooms.find((r) => r.id.toString() === values.room)?.name
      : undefined

    let lessons = []

    // Handle grouped lessons
    if (values.isGrouped && values.divide && values.divide.length > 0) {
      lessons = createDividedLessons(
        subject,
        className,
        values.teacher,
        teacher,
        values.room,
        room,
        values.divide
      )
    }
    // Handle alternating lessons
    else if (values.alternateWith && values.alternateWithTeacher) {
      const alternateSubject =
        subjects.find((s) => s.id.toString() === values.alternateWith)?.name ||
        ''
      const alternateTeacher =
        teachers.find((t) => t.id.toString() === values.alternateWithTeacher)
          ?.name || ''

      lessons = createAlternatingLessons(
        subject,
        alternateSubject,
        className,
        values.teacher,
        teacher,
        values.alternateWithTeacher,
        alternateTeacher,
        values.room,
        room,
        values.hours,
        values.alternationPattern
      )
    }
    // Handle regular lessons
    else {
      lessons = [
        {
          subject,
          className,
          teacherId: values.teacher,
          teacherName: teacher,
          roomId: values.room,
          roomName: room,
          hours: values.hours,
        },
      ]
    }

    // Call the onLessonAdded callback with the created lessons
    onLessonAdded(lessons)

    // Show success toast
    toast({
      title: 'Lesson Added',
      description: `Successfully added ${lessons.length > 1 ? `${lessons.length} lessons` : 'a lesson'} to the timetable.`,
    })

    // Close the dialog
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={buttonVariant}>
          <Plus className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Add Lesson</DialogTitle>
          <DialogDescription>
            Add a new lesson to the timetable. You can configure group divisions
            or alternating subjects.
          </DialogDescription>
        </DialogHeader>

        <LessonForm
          subjects={subjects}
          teachers={teachers}
          rooms={rooms}
          classes={classes}
          onSubmit={handleSubmit}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  )
}
