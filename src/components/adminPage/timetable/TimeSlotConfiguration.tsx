import { useState, useEffect, useMemo } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  Plus,
  Save,
  Trash2,
  RefreshCw,
  Info,
  Coffee,
  Clock,
  Copy,
  CheckCheck,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Switch } from '@/components/ui/switch'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface Timeslot {
  id: string
  day: string
  period: number
  start: string
  end: string
  isBreak?: boolean
}

interface PeriodTiming {
  id: string
  periodNumber: number
  startTime: string
  endTime: string
  isBreak: boolean
}

interface TimeSlotConfigurationProps {
  timeslots: Timeslot[]
  onTimeslotsUpdate: (timeslots: Timeslot[]) => void
  schoolDays?: string[] // Optional school days from parent
  periodCount?: number // Optional period count from parent
  startTime?: string // Optional start time from parent
  endTime?: string // Optional end time from parent
  periodLength?: number // Optional period length from parent
  periodTimings?: PeriodTiming[] // Optional period timings from parent
}

export function TimeSlotConfiguration({
  timeslots,
  onTimeslotsUpdate,
  schoolDays,
  periodCount,
  startTime,
  endTime,
  periodLength,
  periodTimings,
}: TimeSlotConfigurationProps) {
  const { toast } = useToast()
  const [localTimeslots, setLocalTimeslots] = useState<Timeslot[]>(timeslots)
  // Get days from parent component's data if available
  const [days, setDays] = useState<string[]>([
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
  ])
  const [selectedDay, setSelectedDay] = useState('Monday')
  const [isEditing, setIsEditing] = useState<string | null>(null)
  const [editValues, setEditValues] = useState<{ start: string; end: string }>({
    start: '',
    end: '',
  })
  const [selectedBreakTemplate, setSelectedBreakTemplate] =
    useState<string>('morning')
  const [applyToAllDays, setApplyToAllDays] = useState<boolean>(false)
  const [showBreakDialog, setShowBreakDialog] = useState<boolean>(false)
  const [newBreak, setNewBreak] = useState<{
    name: string
    start: string
    end: string
  }>({
    name: 'Morning Break',
    start: '10:15',
    end: '10:30',
  })

  // Break templates
  const breakTemplates = [
    { id: 'morning', name: 'Morning Break', start: '10:15', end: '10:30' },
    { id: 'lunch', name: 'Lunch Break', start: '12:15', end: '13:00' },
    { id: 'afternoon', name: 'Afternoon Break', start: '14:45', end: '15:00' },
  ]

  // Initialize with default timeslots if none provided and update days from parent
  useEffect(() => {
    // Update days from parent if provided
    if (schoolDays && schoolDays.length > 0) {
      setDays(schoolDays)
      // If the selected day is not in the new school days, select the first available day
      if (!schoolDays.includes(selectedDay) && schoolDays.length > 0) {
        setSelectedDay(schoolDays[0])
      }
    }

    // If we have period timings from the BasicTimetableInfo component, convert them to timeslots
    if (periodTimings && periodTimings.length > 0) {
      // Convert period timings to timeslots
      const convertedTimeslots: Timeslot[] = []

      // For each day, create timeslots from period timings
      if (schoolDays && schoolDays.length > 0) {
        schoolDays.forEach((day) => {
          let periodCounter = 1

          // Add regular periods first
          const regularPeriods = periodTimings.filter(
            (timing) => !timing.isBreak
          )
          regularPeriods.sort((a, b) => a.periodNumber - b.periodNumber)

          regularPeriods.forEach((timing) => {
            convertedTimeslots.push({
              id: `${day.substring(0, 3)}_${periodCounter}`,
              day,
              period: periodCounter++,
              start: timing.startTime,
              end: timing.endTime,
              isBreak: false,
            })
          })

          // Then add breaks
          const breaks = periodTimings.filter((timing) => timing.isBreak)
          breaks.forEach((timing, index) => {
            convertedTimeslots.push({
              id: `${day.substring(0, 3)}_break_${index + 1}`,
              day,
              period: -1, // Breaks don't have a period number
              start: timing.startTime,
              end: timing.endTime,
              isBreak: true,
            })
          })
        })
      }

      setLocalTimeslots(convertedTimeslots)
      // Update the parent component
      onTimeslotsUpdate(convertedTimeslots)
    } else if (!timeslots || timeslots.length === 0) {
      // If no timeslots or period timings, generate default timeslots
      const defaults = generateDefaultTimeslots(
        schoolDays,
        periodCount,
        startTime,
        endTime,
        periodLength
      )
      setLocalTimeslots(defaults)
    } else {
      setLocalTimeslots(timeslots)
    }
  }, [
    timeslots,
    schoolDays,
    periodCount,
    selectedDay,
    startTime,
    endTime,
    periodLength,
    periodTimings,
  ])

  // Generate default timeslots based on the period count and school days
  const generateDefaultTimeslots = (
    customDays?: string[],
    customPeriodCount?: number,
    customStartTime?: string,
    _customEndTime?: string, // Prefix with underscore to indicate it's not used
    customPeriodLength?: number
  ): Timeslot[] => {
    // Use provided days or default to Monday-Friday
    const daysToUse =
      customDays && customDays.length > 0
        ? customDays
        : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']

    // Use the general settings from props as the primary source of truth, but validate them first
    const periodCount =
      customPeriodCount && customPeriodCount > 0 ? customPeriodCount : 6 // Default to 6 if not specified or invalid
    const periodLength =
      customPeriodLength && customPeriodLength > 0 ? customPeriodLength : 45 // Default to 45 minutes if not specified or invalid

    // Parse start and end times - use the provided values from general settings with validation
    let startHour = 8
    let startMinute = 0
    if (customStartTime && customStartTime.includes(':')) {
      const [hours, minutes] = customStartTime.split(':').map(Number)
      startHour = !isNaN(hours) ? hours : 8
      startMinute = !isNaN(minutes) ? minutes : 0
    }

    // We don't need to parse end time for this function
    // The end time is used elsewhere in the component

    // Create default periods with a lunch break in the middle
    let defaultPeriods = []

    // Morning periods
    const morningCount = Math.ceil(periodCount / 2)
    let currentHour = startHour
    let currentMinute = startMinute

    for (let i = 0; i < morningCount; i++) {
      const startHour = currentHour
      const startMinute = currentMinute

      // Use the configured period length
      let endMinute = startMinute + periodLength
      let endHour = startHour

      if (endMinute >= 60) {
        endHour += 1
        endMinute -= 60
      }

      defaultPeriods.push({
        start: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`,
        end: `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`,
        isBreak: false,
      })

      // 5 minute break between periods
      currentMinute = endMinute + 5
      currentHour = endHour

      if (currentMinute >= 60) {
        currentHour += 1
        currentMinute -= 60
      }
    }

    // Add lunch break if there's enough time
    // Only add lunch break if we have more than 3 periods
    if (periodCount > 3) {
      const lunchStartHour = currentHour
      const lunchStartMinute = currentMinute

      let lunchEndHour = lunchStartHour
      let lunchEndMinute = lunchStartMinute + 45 // 45 minute lunch break

      if (lunchEndMinute >= 60) {
        lunchEndHour += 1
        lunchEndMinute -= 60
      }

      defaultPeriods.push({
        start: `${lunchStartHour.toString().padStart(2, '0')}:${lunchStartMinute.toString().padStart(2, '0')}`,
        end: `${lunchEndHour.toString().padStart(2, '0')}:${lunchEndMinute.toString().padStart(2, '0')}`,
        isBreak: true,
        name: 'Lunch Break',
      })

      // Afternoon periods
      currentHour = lunchEndHour
      currentMinute = lunchEndMinute
    }

    const afternoonCount = periodCount - morningCount

    for (let i = 0; i < afternoonCount; i++) {
      const startHour = currentHour
      const startMinute = currentMinute

      // Use the configured period length
      let endMinute = startMinute + periodLength
      let endHour = startHour

      if (endMinute >= 60) {
        endHour += 1
        endMinute -= 60
      }

      defaultPeriods.push({
        start: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`,
        end: `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`,
        isBreak: false,
      })

      // 5 minute break between periods
      currentMinute = endMinute + 5
      currentHour = endHour

      if (currentMinute >= 60) {
        currentHour += 1
        currentMinute -= 60
      }
    }

    const generated: Timeslot[] = []

    daysToUse.forEach((day) => {
      let periodCounter = 1
      defaultPeriods.forEach((period, index) => {
        const isBreak = period.isBreak || false
        generated.push({
          id: `${day.substring(0, 3)}_${isBreak ? 'break_' : ''}${index + 1}`,
          day: day,
          period: isBreak ? -1 : periodCounter++, // Use -1 for breaks
          start: period.start,
          end: period.end,
          isBreak,
        })
      })
    })

    return generated
  }

  // Convert HH:MM time to minutes for sorting
  const convertTimeToMinutes = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number)
    return hours * 60 + minutes
  }

  // Calculate duration between two times in minutes
  const calculateBreakDuration = (start: string, end: string) => {
    const startMinutes = convertTimeToMinutes(start)
    const endMinutes = convertTimeToMinutes(end)
    return endMinutes - startMinutes
  }

  // Get timeslots for a specific day
  function getTimeslotsForDay(day: string) {
    return localTimeslots
      .filter((slot) => slot.day === day)
      .sort((a, b) => {
        const aMinutes = convertTimeToMinutes(a.start)
        const bMinutes = convertTimeToMinutes(b.start)
        return aMinutes - bMinutes
      })
  }

  // Filter timeslots by the selected day and sort by start time
  const timeslotsForDay = useMemo(() => {
    return getTimeslotsForDay(selectedDay)
  }, [selectedDay, localTimeslots])

  // Add a new period
  const addPeriod = (day: string) => {
    const daySlots = getTimeslotsForDay(day)
    // Use the period length from props or default to 45 minutes
    const usePeriodLength = periodLength || 45

    // Use the start time from props or default to 08:00
    const useStartTime = startTime || '08:00'
    const [defaultStartHour, defaultStartMinute] = useStartTime
      .split(':')
      .map(Number)

    if (daySlots.length === 0) {
      // Add first period of the day using the start time from Basic Settings
      // Calculate end time based on period length
      let endHour = defaultStartHour
      let endMinute = defaultStartMinute + usePeriodLength

      if (endMinute >= 60) {
        endHour += Math.floor(endMinute / 60)
        endMinute = endMinute % 60
      }

      const formattedStartTime = `${defaultStartHour.toString().padStart(2, '0')}:${defaultStartMinute.toString().padStart(2, '0')}`
      const formattedEndTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`

      const newSlot: Timeslot = {
        id: `${day.substring(0, 3)}_1`,
        day,
        period: 1,
        start: formattedStartTime,
        end: formattedEndTime,
        isBreak: false,
      }
      setLocalTimeslots([...localTimeslots, newSlot])
      toast({
        title: 'First Period Added',
        description: `Added first period to ${day} from ${formattedStartTime} to ${formattedEndTime}`,
      })
      return
    }

    // Find the last slot of the day
    const lastSlot = daySlots[daySlots.length - 1]
    const periodNumber = daySlots.filter((slot) => !slot.isBreak).length + 1

    // Calculate new start time (5 minutes after the last slot ends)
    const [lastEndHour, lastEndMinute] = lastSlot.end.split(':').map(Number)
    let newStartHour = lastEndHour
    let newStartMinute = lastEndMinute + 5

    if (newStartMinute >= 60) {
      newStartHour += Math.floor(newStartMinute / 60)
      newStartMinute = newStartMinute % 60
    }

    // Calculate new end time using the period length from Basic Settings
    let newEndHour = newStartHour
    let newEndMinute = newStartMinute + usePeriodLength

    if (newEndMinute >= 60) {
      newEndHour += Math.floor(newEndMinute / 60)
      newEndMinute = newEndMinute % 60
    }

    // Format times
    const newStartTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`
    const newEndTime = `${newEndHour.toString().padStart(2, '0')}:${newEndMinute.toString().padStart(2, '0')}`

    const newSlot: Timeslot = {
      id: `${day.substring(0, 3)}_${periodNumber}`,
      day,
      period: periodNumber,
      start: newStartTime,
      end: newEndTime,
      isBreak: false,
    }

    setLocalTimeslots([...localTimeslots, newSlot])
    toast({
      title: 'Period Added',
      description: `Added period ${periodNumber} to ${day} from ${newStartTime} to ${newEndTime}`,
    })
  }

  // Handle adding breaks from template or custom
  const handleAddBreak = () => {
    if (applyToAllDays) {
      days.forEach((day) => addBreakToDay(day))
    } else {
      addBreakToDay(selectedDay)
    }

    // Make sure to update the parent component
    onTimeslotsUpdate(localTimeslots)

    setShowBreakDialog(false)
  }

  // Add a break to a specific day
  const addBreakToDay = (day: string) => {
    const daySlots = getTimeslotsForDay(day)

    if (daySlots.length === 0) {
      toast({
        title: 'Cannot Add Break',
        description:
          'You need to add at least one period before adding a break',
        variant: 'destructive',
      })
      return
    }

    const breakCount = daySlots.filter((slot) => slot.isBreak).length + 1

    const newBreakSlot: Timeslot = {
      id: `${day.substring(0, 3)}_break_${breakCount}`,
      day,
      period: -1, // Breaks don't have a period number
      start: newBreak.start,
      end: newBreak.end,
      isBreak: true,
    }

    setLocalTimeslots([...localTimeslots, newBreakSlot])

    // Re-order the timeslots to maintain chronological order
    setTimeout(() => {
      updatePeriodNumbers(day)
    }, 0)

    toast({
      title: 'Break Added',
      description: `Added ${newBreak.name} to ${day} from ${newBreak.start} to ${newBreak.end}`,
    })
  }

  // Update the period numbers for a day after adding or removing slots
  const updatePeriodNumbers = (day: string) => {
    // Get all slots for the day and sort them by start time
    const daySlots = getTimeslotsForDay(day).sort((a, b) => {
      // Convert times to minutes for comparison
      const aMinutes = convertTimeToMinutes(a.start)
      const bMinutes = convertTimeToMinutes(b.start)
      return aMinutes - bMinutes
    })

    // Keep all slots from other days unchanged
    const otherDaysSlots = localTimeslots.filter((slot) => slot.day !== day)

    // Create new array for the updated day slots
    const updatedDaySlots: Timeslot[] = []
    let periodCounter = 1

    // First add all slots with updated period numbers
    daySlots.forEach((slot) => {
      if (!slot.isBreak) {
        updatedDaySlots.push({
          ...slot,
          id: `${day.substring(0, 3)}_${periodCounter}`, // Update ID to match new period number
          period: periodCounter++,
        })
      } else {
        // For breaks, keep the same but ensure unique ID
        updatedDaySlots.push({
          ...slot,
          id: `${day.substring(0, 3)}_break_${Math.random().toString(36).substring(2, 7)}`,
        })
      }
    })

    // Combine all slots and update state
    setLocalTimeslots([...otherDaysSlots, ...updatedDaySlots])

    // Notify parent component of the change
    onTimeslotsUpdate([...otherDaysSlots, ...updatedDaySlots])
  }

  // Remove a slot
  const removeSlot = (slotId: string) => {
    const slotToRemove = localTimeslots.find((slot) => slot.id === slotId)
    if (!slotToRemove) return

    // Create a new array without the removed slot
    const updatedSlots = localTimeslots.filter((slot) => slot.id !== slotId)

    // Update local state immediately
    setLocalTimeslots(updatedSlots)

    // Also notify parent component of the change
    onTimeslotsUpdate(updatedSlots)

    // Update period numbers after a short delay to ensure state is updated
    setTimeout(() => {
      updatePeriodNumbers(slotToRemove.day)
    }, 50)

    toast({
      title: slotToRemove.isBreak ? 'Break Removed' : 'Period Removed',
      description: `${slotToRemove.isBreak ? 'Break' : 'Period'} has been removed`,
    })
  }

  // Begin editing a slot
  const startEditing = (slot: Timeslot) => {
    setIsEditing(slot.id)
    setEditValues({
      start: slot.start,
      end: slot.end,
    })
  }

  // Save the edited slot
  const saveEdit = (slotId: string) => {
    // Find the slot being edited
    const slotToEdit = localTimeslots.find((slot) => slot.id === slotId)
    if (!slotToEdit) return

    // Create updated slots array
    const updatedSlots = localTimeslots.map((slot) =>
      slot.id === slotId
        ? { ...slot, start: editValues.start, end: editValues.end }
        : slot
    )

    // Update local state
    setLocalTimeslots(updatedSlots)

    // Notify parent component
    onTimeslotsUpdate(updatedSlots)

    // Sort slots if needed
    setTimeout(() => {
      updatePeriodNumbers(slotToEdit.day)
    }, 50)

    setIsEditing(null)
    toast({
      title: 'Time Updated',
      description: `Time slot has been updated to ${editValues.start} - ${editValues.end}`,
    })
  }

  // Handle break template selection
  const handleSelectBreakTemplate = (templateId: string) => {
    const template = breakTemplates.find((t) => t.id === templateId)
    if (template) {
      setSelectedBreakTemplate(templateId)
      setNewBreak({
        name: template.name,
        start: template.start,
        end: template.end,
      })
    }
  }

  // Update a slot's time
  const updateSlotTime = (field: 'start' | 'end', value: string) => {
    setEditValues({
      ...editValues,
      [field]: value,
    })
  }

  // Reset to default timeslots
  const resetToDefault = () => {
    const defaultSlots = generateDefaultTimeslots(
      schoolDays,
      periodCount,
      startTime,
      endTime,
      periodLength
    )
    setLocalTimeslots(defaultSlots)
    toast({
      title: 'Reset to Default',
      description: 'Timeslots have been reset based on your general settings',
    })
  }

  // Apply the current day's schedule to all days
  const applyCurrentDayToAll = () => {
    const currentDaySlots = getTimeslotsForDay(selectedDay)
    if (currentDaySlots.length === 0) {
      toast({
        title: 'Cannot Apply',
        description: 'The current day has no time slots to apply',
        variant: 'destructive',
      })
      return
    }

    const updatedSlots = [
      ...localTimeslots.filter((slot) => slot.day === selectedDay),
    ]

    days.forEach((day) => {
      if (day !== selectedDay) {
        currentDaySlots.forEach((slot) => {
          const newId = slot.id.replace(
            selectedDay.substring(0, 3),
            day.substring(0, 3)
          )
          updatedSlots.push({
            ...slot,
            id: newId,
            day,
          })
        })
      }
    })

    setLocalTimeslots(updatedSlots)
    toast({
      title: 'Schedule Applied',
      description: `Applied ${selectedDay}'s schedule to all days`,
    })
  }

  // Save the timeslot configuration
  const saveConfiguration = () => {
    // Validate no overlapping periods
    const hasOverlaps = days.some((day) => {
      const daySlots = getTimeslotsForDay(day)
      for (let i = 0; i < daySlots.length - 1; i++) {
        const currentEnd = convertTimeToMinutes(daySlots[i].end)
        const nextStart = convertTimeToMinutes(daySlots[i + 1].start)
        if (currentEnd > nextStart) {
          toast({
            title: 'Time Conflict',
            description: `Overlapping times detected on ${day}`,
            variant: 'destructive',
          })
          return true
        }
      }
      return false
    })

    if (hasOverlaps) return

    // Make sure each day has at least one time slot
    const daysWithoutSlots = days.filter((day) => {
      return (
        getTimeslotsForDay(day).filter((slot) => !slot.isBreak).length === 0
      )
    })

    if (daysWithoutSlots.length > 0) {
      toast({
        title: 'Missing Time Slots',
        description: `The following days have no time slots: ${daysWithoutSlots.join(', ')}`,
        variant: 'destructive',
      })
      return
    }

    // Sort time slots by start time for each day
    const sortedTimeslots = [...localTimeslots]
    days.forEach((day) => {
      updatePeriodNumbers(day)
    })

    onTimeslotsUpdate(sortedTimeslots)
    toast({
      title: 'Configuration Saved',
      description: 'Time slot configuration has been saved successfully',
      variant: 'default',
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-primary" />
            Time Slot Configuration
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={resetToDefault}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset to Default
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Reset to standard school schedule</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
        <CardDescription>
          Configure time slots for each day of the week, including class periods
          and breaks
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Alert className="bg-amber-50">
          <Info className="h-4 w-4" />
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            Set up your school's schedule including all lessons and breaks.
            Breaks will not be considered for timetable assignments.
          </AlertDescription>
        </Alert>

        <Tabs
          defaultValue={selectedDay}
          onValueChange={setSelectedDay}
          className="w-full"
        >
          <TabsList className="w-full grid grid-cols-5 mb-6">
            {days.map((day) => (
              <TabsTrigger key={day} value={day} className="relative">
                {day}
                <Badge
                  variant="secondary"
                  className="absolute -top-2 -right-2 text-xs"
                >
                  {getTimeslotsForDay(day).filter((s) => !s.isBreak).length}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>

          {days.map((day) => (
            <TabsContent
              key={day}
              value={day}
              className="border rounded-lg p-4"
            >
              <div className="flex justify-between mb-4">
                <div className="space-x-2">
                  <Button onClick={() => addPeriod(day)} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Period
                  </Button>

                  <Dialog
                    open={showBreakDialog}
                    onOpenChange={setShowBreakDialog}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Coffee className="h-4 w-4 mr-2" />
                        Add Break
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Break Period</DialogTitle>
                        <DialogDescription>
                          Add a break period to the schedule. Breaks will be
                          excluded from lesson scheduling.
                        </DialogDescription>
                      </DialogHeader>

                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="break-name" className="text-right">
                            Name
                          </Label>
                          <Input
                            id="break-name"
                            className="col-span-3"
                            value={newBreak.name}
                            onChange={(e) =>
                              setNewBreak({ ...newBreak, name: e.target.value })
                            }
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="break-start" className="text-right">
                            Start Time
                          </Label>
                          <Input
                            id="break-start"
                            type="time"
                            className="col-span-3"
                            value={newBreak.start}
                            onChange={(e) =>
                              setNewBreak({
                                ...newBreak,
                                start: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="break-end" className="text-right">
                            End Time
                          </Label>
                          <Input
                            id="break-end"
                            type="time"
                            className="col-span-3"
                            value={newBreak.end}
                            onChange={(e) =>
                              setNewBreak({ ...newBreak, end: e.target.value })
                            }
                          />
                        </div>

                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label className="text-right">Duration</Label>
                          <div className="col-span-3">
                            <p className="text-sm">
                              {calculateBreakDuration(
                                newBreak.start,
                                newBreak.end
                              )}{' '}
                              minutes
                            </p>
                          </div>
                        </div>

                        <Separator />

                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label className="text-right">Templates</Label>
                          <div className="col-span-3 flex flex-wrap gap-2">
                            {breakTemplates.map((template) => (
                              <Button
                                key={template.id}
                                variant={
                                  selectedBreakTemplate === template.id
                                    ? 'default'
                                    : 'outline'
                                }
                                size="sm"
                                onClick={() =>
                                  handleSelectBreakTemplate(template.id)
                                }
                              >
                                {template.name}
                              </Button>
                            ))}
                          </div>
                        </div>

                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="apply-all" className="text-right">
                            Apply to all days
                          </Label>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="apply-all"
                              checked={applyToAllDays}
                              onCheckedChange={setApplyToAllDays}
                            />
                            <Label htmlFor="apply-all">
                              {applyToAllDays ? 'Yes' : 'No'}
                            </Label>
                          </div>
                        </div>
                      </div>

                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setShowBreakDialog(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleAddBreak}>Add Break</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={applyCurrentDayToAll}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Apply to All Days
                </Button>
              </div>

              <div className="border rounded-md">
                <div className="grid grid-cols-12 gap-4 px-4 py-3 font-medium text-sm bg-muted">
                  <div className="col-span-1">Period</div>
                  <div className="col-span-4">Start Time</div>
                  <div className="col-span-4">End Time</div>
                  <div className="col-span-3">Actions</div>
                </div>

                <ScrollArea className="h-[calc(100vh-450px)] min-h-[300px]">
                  {timeslotsForDay.length === 0 ? (
                    <div className="py-12 text-center text-muted-foreground">
                      No time slots added for {day}. Add a period to start.
                    </div>
                  ) : (
                    <div className="divide-y">
                      {timeslotsForDay.map((slot) => (
                        <div
                          key={slot.id}
                          className={`grid grid-cols-12 gap-4 px-4 py-3 items-center ${
                            slot.isBreak ? 'bg-amber-50' : ''
                          }`}
                        >
                          <div className="col-span-1 font-medium">
                            {slot.isBreak ? (
                              <Badge
                                variant="outline"
                                className="bg-amber-100 text-amber-800"
                              >
                                Break
                              </Badge>
                            ) : (
                              `${slot.period}`
                            )}
                          </div>

                          {isEditing === slot.id ? (
                            <>
                              <div className="col-span-4">
                                <Input
                                  type="time"
                                  value={editValues.start}
                                  onChange={(e) =>
                                    updateSlotTime('start', e.target.value)
                                  }
                                />
                              </div>
                              <div className="col-span-4">
                                <Input
                                  type="time"
                                  value={editValues.end}
                                  onChange={(e) =>
                                    updateSlotTime('end', e.target.value)
                                  }
                                />
                              </div>
                              <div className="col-span-3 flex space-x-1">
                                <Button
                                  size="sm"
                                  variant="default"
                                  onClick={() => saveEdit(slot.id)}
                                >
                                  <CheckCheck className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setIsEditing(null)}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </>
                          ) : (
                            <>
                              <div className="col-span-4">{slot.start}</div>
                              <div className="col-span-4">{slot.end}</div>
                              <div className="col-span-3 flex space-x-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => startEditing(slot)}
                                >
                                  Edit
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => removeSlot(slot.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button onClick={saveConfiguration}>
          <Save className="h-4 w-4 mr-2" />
          Save Configuration
        </Button>
      </CardFooter>
    </Card>
  )
}
