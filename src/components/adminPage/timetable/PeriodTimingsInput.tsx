import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, ChevronUp, Plus, Trash2, Wand2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface PeriodTiming {
  id: string
  periodNumber: number
  startTime: string
  endTime: string
  isBreak: boolean
}

// Helper function to format time for display (12-hour format with AM/PM)
const formatTimeDisplay = (time: string): string => {
  if (!time) return ''

  const [hours, minutes] = time.split(':').map(Number)
  const period = hours >= 12 ? 'PM' : 'AM'
  const displayHours = hours % 12 || 12 // Convert 0 to 12 for 12 AM

  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`
}

interface PeriodTimingsInputProps {
  periodCount: number
  onTimingsChange: (timings: PeriodTiming[]) => void
  initialTimings?: PeriodTiming[]
  startTime?: string
  endTime?: string
  periodLength?: number
}

export function PeriodTimingsInput({
  periodCount,
  onTimingsChange,
  initialTimings,
  startTime = '08:00',
  endTime = '17:00',
  periodLength = 45,
}: PeriodTimingsInputProps) {
  const { toast } = useToast()
  const [showTimings, setShowTimings] = useState(false)
  const [timings, setTimings] = useState<PeriodTiming[]>([])
  const [breakTimings, setBreakTimings] = useState<PeriodTiming[]>([])

  // Initialize timings when component mounts or props change
  useEffect(() => {
    if (initialTimings && initialTimings.length > 0) {
      // Use provided timings
      const periods = initialTimings.filter((t) => !t.isBreak)
      const breaks = initialTimings.filter((t) => t.isBreak)

      // Check if we need to regenerate timings
      // Only regenerate if we have no periods at all or if the periodCount has decreased
      if (periods.length === 0 || periods.length > periodCount) {
        console.log(
          `Regenerating timings: periods=${periods.length}, periodCount=${periodCount}`
        )
        generateEmptyTimings()
      } else {
        // If we have some periods but fewer than periodCount, add empty ones
        if (periods.length < periodCount) {
          const newTimings = [...periods]
          // Add empty periods for the missing ones
          for (let i = periods.length; i < periodCount; i++) {
            newTimings.push({
              id: `period-${i + 1}`,
              periodNumber: i + 1,
              startTime: '',
              endTime: '',
              isBreak: false,
            })
          }
          console.log(
            `Added ${periodCount - periods.length} empty periods to existing ${periods.length} periods`
          )
          setTimings(newTimings)
          setBreakTimings(breaks)
        } else {
          // Just use the existing periods
          setTimings(periods)
          setBreakTimings(breaks)
        }
      }
    } else {
      // Generate empty timings based on periodCount
      generateEmptyTimings()
    }
  }, [periodCount, startTime, endTime, periodLength, initialTimings])

  // Generate empty timings based on the period count
  const generateEmptyTimings = () => {
    console.log(`Generating ${periodCount} empty periods`)
    const newTimings: PeriodTiming[] = []

    // Create empty periods with just the period number
    for (let i = 0; i < periodCount; i++) {
      newTimings.push({
        id: `period-${i + 1}`,
        periodNumber: i + 1,
        startTime: '',
        endTime: '',
        isBreak: false,
      })
    }

    setTimings(newTimings)
    setBreakTimings([]) // Reset break timings
    onTimingsChange([...newTimings])
  }

  // Generate default timings based on the inputs
  const generateDefaultTimings = () => {
    console.log(`Generating ${periodCount} periods with times`)

    // Start with current timings if they exist, otherwise create new empty ones
    let newTimings: PeriodTiming[] = []

    // If we already have timings, use them as a base
    if (timings.length > 0) {
      // Copy existing timings
      newTimings = [...timings]

      // If we need more periods than we currently have, add empty ones
      if (timings.length < periodCount) {
        for (let i = timings.length; i < periodCount; i++) {
          newTimings.push({
            id: `period-${i + 1}`,
            periodNumber: i + 1,
            startTime: '',
            endTime: '',
            isBreak: false,
          })
        }
        console.log(
          `Added ${periodCount - timings.length} empty periods to existing ${timings.length} periods`
        )
      }
      // If we have too many periods, trim the array
      else if (timings.length > periodCount) {
        newTimings = newTimings.slice(0, periodCount)
        console.log(
          `Trimmed timings from ${timings.length} to ${periodCount} periods`
        )
      }
    }
    // If we don't have any timings yet, create all new ones
    else {
      for (let i = 0; i < periodCount; i++) {
        newTimings.push({
          id: `period-${i + 1}`,
          periodNumber: i + 1,
          startTime: '',
          endTime: '',
          isBreak: false,
        })
      }
      console.log(`Created ${periodCount} new empty periods`)
    }

    // Use the general settings from props as the primary source of truth, but validate them first
    // Only use autoGenSettings for the break between periods which isn't in the general settings
    const useStartTime =
      startTime && startTime.includes(':') ? startTime : '08:00'
    const useEndTime = endTime && endTime.includes(':') ? endTime : '17:00'
    const usePeriodLength = periodLength && periodLength > 0 ? periodLength : 45
    // Ensure break between periods is within valid range (0-90)
    const useBreakBetween =
      typeof autoGenSettings.breakBetweenPeriods === 'number' &&
      autoGenSettings.breakBetweenPeriods >= 0 &&
      autoGenSettings.breakBetweenPeriods <= 90
        ? autoGenSettings.breakBetweenPeriods
        : 5

    // Parse start and end times
    const [startHour, startMinute] = useStartTime.split(':').map(Number)
    const [endHour, endMinute] = useEndTime.split(':').map(Number)

    // Calculate total minutes in the school day
    const startTimeInMinutes = startHour * 60 + startMinute
    const endTimeInMinutes = endHour * 60 + endMinute

    // Handle case where end time is earlier than start time (next day)
    const totalMinutes =
      endTimeInMinutes > startTimeInMinutes
        ? endTimeInMinutes - startTimeInMinutes
        : endTimeInMinutes + 24 * 60 - startTimeInMinutes

    // Calculate how many periods can fit in the available time
    // Each period needs: period length + break between (except the last period doesn't need a break after)
    const timeNeededPerPeriod = usePeriodLength + useBreakBetween

    // Special case: if break between is 0, we can fit more periods
    let maxPossiblePeriods
    if (useBreakBetween === 0) {
      // If no break between periods, we can fit as many periods as will go into the total time
      maxPossiblePeriods = Math.floor(totalMinutes / usePeriodLength)
      console.log(
        `No break between periods, can fit ${maxPossiblePeriods} periods in the available time`
      )
    } else {
      // Normal case with breaks between periods
      maxPossiblePeriods = Math.floor(
        (totalMinutes + useBreakBetween) / timeNeededPerPeriod
      )
      console.log(
        `With ${useBreakBetween}min breaks, can fit ${maxPossiblePeriods} periods in the available time`
      )
    }

    // Start at the beginning of the day
    let currentTimeInMinutes = startTimeInMinutes

    // Fill in only as many periods as will fit within the time constraints
    const periodsToFill = Math.min(periodCount, maxPossiblePeriods)
    console.log(
      `Will fill ${periodsToFill} periods out of ${periodCount} requested`
    )

    for (let i = 0; i < periodsToFill; i++) {
      // Calculate start time
      const startHour = Math.floor(currentTimeInMinutes / 60) % 24
      const startMinute = currentTimeInMinutes % 60

      // Format start time as HH:MM
      const formattedStartTime = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`

      // Calculate end time
      const endTimeInMinutes = currentTimeInMinutes + usePeriodLength
      const endHour = Math.floor(endTimeInMinutes / 60) % 24
      const endMinute = endTimeInMinutes % 60

      // Format end time as HH:MM
      const formattedEndTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`

      console.log(
        `Setting period ${i + 1} to ${formattedStartTime} - ${formattedEndTime}`
      )
      // Update the period with calculated times
      newTimings[i].startTime = formattedStartTime
      newTimings[i].endTime = formattedEndTime

      // Move to the next period start time (add period length + break)
      // If break is 0, the next period starts immediately after this one
      currentTimeInMinutes = endTimeInMinutes + useBreakBetween
      console.log(
        `Next period starts at ${Math.floor(currentTimeInMinutes / 60)}:${currentTimeInMinutes % 60}`
      )
    }

    // Count how many periods were filled with times
    const filledPeriods = newTimings.filter(
      (p) => p.startTime && p.endTime
    ).length
    console.log(`Filled ${filledPeriods} periods with times`)

    // Update the UI with all periods (including empty ones)
    setTimings(newTimings)
    setBreakTimings([]) // Reset break timings

    // Only send non-empty periods to the parent component
    const nonEmptyTimings = newTimings.filter(
      (timing) => timing.startTime && timing.endTime
    )
    console.log(`Sending ${nonEmptyTimings.length} non-empty periods to parent`)
    onTimingsChange(nonEmptyTimings)

    // Return the filled periods count for the toast notification
    return filledPeriods
  }

  // Handle time input changes
  const handleTimeChange = (
    index: number,
    field: 'startTime' | 'endTime',
    value: string
  ) => {
    const updatedTimings = [...timings]
    updatedTimings[index] = {
      ...updatedTimings[index],
      [field]: value,
    }

    setTimings(updatedTimings)

    // Only send non-empty periods to the parent component
    const nonEmptyTimings = updatedTimings.filter(
      (timing) => timing.startTime && timing.endTime
    )
    onTimingsChange([...nonEmptyTimings, ...breakTimings])
  }

  // Handle break time input changes
  const handleBreakTimeChange = (
    index: number,
    field: 'startTime' | 'endTime',
    value: string
  ) => {
    const updatedBreaks = [...breakTimings]
    updatedBreaks[index] = {
      ...updatedBreaks[index],
      [field]: value,
    }

    setBreakTimings(updatedBreaks)

    // Only send non-empty periods to the parent component
    const nonEmptyTimings = timings.filter(
      (timing) => timing.startTime && timing.endTime
    )
    onTimingsChange([...nonEmptyTimings, ...updatedBreaks])
  }

  // Add a new break
  const addBreak = () => {
    const newBreak: PeriodTiming = {
      id: `break-${breakTimings.length + 1}`,
      periodNumber: -1, // Breaks don't have a period number
      startTime: '10:15',
      endTime: '10:30',
      isBreak: true,
    }

    const updatedBreaks = [...breakTimings, newBreak]
    setBreakTimings(updatedBreaks)

    // Only send non-empty periods to the parent component
    const nonEmptyTimings = timings.filter(
      (timing) => timing.startTime && timing.endTime
    )
    onTimingsChange([...nonEmptyTimings, ...updatedBreaks])

    toast({
      title: 'Break Added',
      description: 'A new break time has been added to the schedule.',
    })
  }

  // Remove a break
  const removeBreak = (index: number) => {
    const updatedBreaks = breakTimings.filter((_, i) => i !== index)
    setBreakTimings(updatedBreaks)

    // Only send non-empty periods to the parent component
    const nonEmptyTimings = timings.filter(
      (timing) => timing.startTime && timing.endTime
    )
    onTimingsChange([...nonEmptyTimings, ...updatedBreaks])

    toast({
      title: 'Break Removed',
      description: 'The break time has been removed from the schedule.',
    })
  }

  // State for auto-generate dialog
  const [showAutoGenerateDialog, setShowAutoGenerateDialog] = useState(false)
  const [autoGenSettings, setAutoGenSettings] = useState({
    startTime: startTime,
    endTime: endTime,
    periodLength: periodLength,
    breakBetweenPeriods: 5, // Default 5 minutes between periods, can be 0-90
  })

  // Update auto-generate settings when props change
  useEffect(() => {
    setAutoGenSettings((prev) => ({
      ...prev,
      startTime: startTime,
      endTime: endTime,
      periodLength: periodLength,
    }))
  }, [startTime, endTime, periodLength])

  // Handle auto-generate settings change
  const handleAutoGenSettingChange = (field: string, value: any) => {
    setAutoGenSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Auto-generate periods based on settings
  const handleAutoGenerate = () => {
    // First generate the timings and get the number of filled periods
    const filledPeriods = generateDefaultTimings()
    setShowAutoGenerateDialog(false)

    // Force the UI to show the timings
    setShowTimings(true)

    // Get the actual values being used (with fallbacks for invalid values)
    const useStartTime =
      startTime && startTime.includes(':') ? startTime : '08:00'
    const useEndTime = endTime && endTime.includes(':') ? endTime : '17:00'
    const usePeriodLength = periodLength && periodLength > 0 ? periodLength : 45
    // Ensure break between periods is within valid range (0-90)
    const useBreakBetween =
      typeof autoGenSettings.breakBetweenPeriods === 'number' &&
      autoGenSettings.breakBetweenPeriods >= 0 &&
      autoGenSettings.breakBetweenPeriods <= 90
        ? autoGenSettings.breakBetweenPeriods
        : 5

    // Parse start and end times
    const [startHour, startMinute] = useStartTime.split(':').map(Number)
    const [endHour, endMinute] = useEndTime.split(':').map(Number)

    // Calculate total minutes in the school day
    const startTimeInMinutes = startHour * 60 + startMinute
    const endTimeInMinutes = endHour * 60 + endMinute
    const totalMinutes =
      endTimeInMinutes > startTimeInMinutes
        ? endTimeInMinutes - startTimeInMinutes
        : endTimeInMinutes + 24 * 60 - startTimeInMinutes

    // Calculate max possible periods
    const timeNeededPerPeriod = usePeriodLength + useBreakBetween

    // Special case: if break between is 0, we can fit more periods
    let maxPossiblePeriods
    if (useBreakBetween === 0) {
      // If no break between periods, we can fit as many periods as will go into the total time
      maxPossiblePeriods = Math.floor(totalMinutes / usePeriodLength)
      console.log(
        `No break between periods, can fit ${maxPossiblePeriods} periods in the available time`
      )
    } else {
      // Normal case with breaks between periods
      maxPossiblePeriods = Math.floor(
        (totalMinutes + useBreakBetween) / timeNeededPerPeriod
      )
      console.log(
        `With ${useBreakBetween}min breaks, can fit ${maxPossiblePeriods} periods in the available time`
      )
    }

    // Use the actual filled periods count from generateDefaultTimings
    const totalPeriods = periodCount

    if (filledPeriods < totalPeriods) {
      const hoursAvailable = Math.floor(totalMinutes / 60)
      const minutesAvailable = totalMinutes % 60

      toast({
        title: 'Periods Partially Generated',
        description: `Generated times for ${filledPeriods} out of ${totalPeriods} periods. With ${hoursAvailable}h ${minutesAvailable}m available (${formatTimeDisplay(useStartTime)} to ${formatTimeDisplay(useEndTime)}) and ${usePeriodLength}min periods, only ${maxPossiblePeriods} periods can fit.`,
      })
    } else {
      toast({
        title: 'Periods Generated',
        description: `Period times have been automatically generated using ${startTime && startTime.includes(':') ? 'the general settings' : 'default values'} (Start: ${formatTimeDisplay(useStartTime)}, End: ${formatTimeDisplay(useEndTime)}, Period Length: ${usePeriodLength} min).`,
      })
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => setShowTimings(!showTimings)}
          className="p-0 h-auto"
        >
          {showTimings ? (
            <ChevronUp className="h-4 w-4 mr-2" />
          ) : (
            <ChevronDown className="h-4 w-4 mr-2" />
          )}
          {showTimings ? 'Hide Period Timings' : 'Show Period Timings'}
        </Button>

        {showTimings && (
          <Dialog
            open={showAutoGenerateDialog}
            onOpenChange={setShowAutoGenerateDialog}
          >
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Wand2 className="h-4 w-4 mr-2" />
                Auto-Generate
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Auto-Generate Period Times</DialogTitle>
                <DialogDescription>
                  Automatically generate {periodCount} period times based on
                  your general settings. The start time, end time, and period
                  length will be taken from the general timetable settings.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 items-center gap-4">
                  <Label htmlFor="auto-start-time">School Start Time</Label>
                  <div>
                    <Input
                      id="auto-start-time"
                      type="time"
                      value={
                        startTime && startTime.includes(':')
                          ? startTime
                          : '08:00'
                      }
                      disabled
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      {startTime && startTime.includes(':')
                        ? formatTimeDisplay(startTime)
                        : formatTimeDisplay('08:00')}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {startTime && startTime.includes(':')
                        ? 'Using value from general settings'
                        : 'Using default value (general settings invalid)'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 items-center gap-4">
                  <Label htmlFor="auto-end-time">School End Time</Label>
                  <div>
                    <Input
                      id="auto-end-time"
                      type="time"
                      value={
                        endTime && endTime.includes(':') ? endTime : '17:00'
                      }
                      disabled
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      {endTime && endTime.includes(':')
                        ? formatTimeDisplay(endTime)
                        : formatTimeDisplay('17:00')}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {endTime && endTime.includes(':')
                        ? 'Using value from general settings'
                        : 'Using default value (general settings invalid)'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 items-center gap-4">
                  <Label htmlFor="auto-period-length">
                    Period Length (minutes)
                  </Label>
                  <div>
                    <Input
                      id="auto-period-length"
                      type="number"
                      min={30}
                      max={120}
                      value={
                        periodLength && periodLength > 0 ? periodLength : 45
                      }
                      disabled
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      {periodLength && periodLength > 0
                        ? 'Using value from general settings'
                        : 'Using default value (general settings invalid)'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 items-center gap-4">
                  <Label htmlFor="auto-break-between">
                    Minutes Between Periods
                  </Label>
                  <div>
                    <Input
                      id="auto-break-between"
                      type="number"
                      min={0}
                      max={90}
                      value={autoGenSettings.breakBetweenPeriods}
                      onChange={(e) => {
                        // Allow 0 as a valid value
                        const value =
                          e.target.value === '' ? 0 : parseInt(e.target.value)
                        handleAutoGenSettingChange(
                          'breakBetweenPeriods',
                          isNaN(value) ? 5 : value
                        )
                      }}
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      Break time between periods (0-90 minutes). Set to 0 for no
                      break.
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowAutoGenerateDialog(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleAutoGenerate}>Generate</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {showTimings && (
        <div className="space-y-4 bg-muted/30 p-4 rounded-lg">
          {/* Period Timings */}
          <div className="space-y-4">
            {timings.map((timing, index) => (
              <div
                key={timing.id}
                className="grid grid-cols-12 gap-2 items-center bg-background p-3 rounded-md"
              >
                <Label className="text-sm col-span-2">
                  Period {timing.periodNumber}
                </Label>
                <div className="col-span-5">
                  <Input
                    type="time"
                    value={timing.startTime}
                    onChange={(e) =>
                      handleTimeChange(index, 'startTime', e.target.value)
                    }
                    placeholder="--:-- --"
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {timing.startTime
                      ? formatTimeDisplay(timing.startTime)
                      : '--:-- --'}
                  </div>
                </div>
                <div className="col-span-5">
                  <Input
                    type="time"
                    value={timing.endTime}
                    onChange={(e) =>
                      handleTimeChange(index, 'endTime', e.target.value)
                    }
                    placeholder="--:-- --"
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    {timing.endTime
                      ? formatTimeDisplay(timing.endTime)
                      : '--:-- --'}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <Separator className="my-4" />

          {/* Break Timings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Break Times</h3>
              <Button variant="outline" size="sm" onClick={addBreak}>
                <Plus className="h-4 w-4 mr-2" />
                Add Break
              </Button>
            </div>

            {breakTimings.length === 0 ? (
              <p className="text-sm text-muted-foreground">
                No breaks added yet. Click "Add Break" to add a break time.
              </p>
            ) : (
              <div className="space-y-2">
                {breakTimings.map((breakTime, index) => (
                  <div
                    key={breakTime.id}
                    className="grid grid-cols-12 gap-2 items-center bg-background p-3 rounded-md"
                  >
                    <Label className="text-sm col-span-2">
                      Break {index + 1}
                    </Label>
                    <div className="col-span-4">
                      <Input
                        type="time"
                        value={breakTime.startTime}
                        onChange={(e) =>
                          handleBreakTimeChange(
                            index,
                            'startTime',
                            e.target.value
                          )
                        }
                        placeholder="--:-- --"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        {breakTime.startTime
                          ? formatTimeDisplay(breakTime.startTime)
                          : ''}
                      </div>
                    </div>
                    <div className="col-span-4">
                      <Input
                        type="time"
                        value={breakTime.endTime}
                        onChange={(e) =>
                          handleBreakTimeChange(
                            index,
                            'endTime',
                            e.target.value
                          )
                        }
                        placeholder="--:-- --"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        {breakTime.endTime
                          ? formatTimeDisplay(breakTime.endTime)
                          : ''}
                      </div>
                    </div>
                    <div className="col-span-2 flex justify-end">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeBreak(index)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
