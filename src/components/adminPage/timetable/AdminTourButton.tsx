import { But<PERSON> } from '@/components/ui/button'
import { HelpCircle } from 'lucide-react'
import { ButtonProps } from '@/components/ui/button'

interface AdminTourButtonProps extends ButtonProps {}

export function AdminTourButton(props: AdminTourButtonProps) {
  const handleClick = () => {
    console.log('AdminTourButton: Take Tour button clicked')

    // Reset tour completion status
    localStorage.removeItem('tour_completed_admin_timetable')
    localStorage.removeItem('tour_skipped_admin_timetable')

    // Set userOnboarding to true
    localStorage.setItem('userOnboarding', 'true')

    // Set trigger flag
    localStorage.setItem('trigger_admin_timetable_tour', 'true')

    // Dispatch event to start the tour
    window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))

    // Reload the page to ensure a clean state
    setTimeout(() => {
      window.location.reload()
    }, 300)
  }

  return (
    <Button onClick={handleClick} {...props}>
      <HelpCircle className="h-4 w-4 mr-1" />
      <span>Take Tour</span>
    </Button>
  )
}
