import { useDrop } from 'react-dnd'
import { X } from 'lucide-react'
import { TimetableAssignment } from '@/types/timetable'

// Define the item types for drag and drop
export const ItemTypes = {
  ASSIGNMENT: 'assignment',
  UNSCHEDULED_SUBJECT: 'unscheduled_subject',
}

interface UnscheduleZoneProps {
  onUnschedule: (assignment: TimetableAssignment) => void
}

export function UnscheduleZone({ onUnschedule }: UnscheduleZoneProps) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.ASSIGNMENT,
    drop: (item: { assignment: TimetableAssignment }) => {
      onUnschedule(item.assignment)
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }))

  return (
    <div
      ref={drop}
      className={`flex items-center justify-center p-3 border-2 border-dashed rounded-md mb-4 ${
        isOver ? 'bg-red-100 border-red-400' : 'bg-gray-50 border-gray-300'
      }`}
      style={{ minHeight: '80px' }}
    >
      <div className="flex flex-col items-center text-center">
        <X className="h-6 w-6 mb-1 text-gray-400" />
        <p className="text-sm font-medium text-gray-500">
          Drag lessons here to unschedule them
        </p>
        {isOver && (
          <div className="absolute inset-0 flex items-center justify-center bg-red-100 bg-opacity-30 pointer-events-none rounded-md">
            <div className="bg-red-500 text-white p-1 rounded text-xs font-bold">
              Drop to Unschedule
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
