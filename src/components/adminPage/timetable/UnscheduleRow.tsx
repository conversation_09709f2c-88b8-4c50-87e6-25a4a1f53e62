import { useDrop } from 'react-dnd'
import { X } from 'lucide-react'
import { TimetableAssignment } from '@/types/timetable'

// Define the item type for drag and drop
export const ItemTypes = {
  ASSIGNMENT: 'assignment',
}

interface UnscheduleRowProps {
  onUnschedule: (assignment: TimetableAssignment) => void
  days: string[]
}

export function UnscheduleRow({ onUnschedule, days }: UnscheduleRowProps) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.ASSIGNMENT,
    drop: (item: { assignment: TimetableAssignment }) => {
      onUnschedule(item.assignment)
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }))

  return (
    <tr className="border-b">
      <td className="p-2 align-top bg-gray-50 font-medium">Unschedule</td>
      <td
        ref={drop}
        colSpan={days.length}
        className={`p-3 text-center ${isOver ? 'bg-red-100' : 'bg-gray-50'} border-dashed border-2 border-gray-300`}
      >
        <div className="flex flex-col items-center justify-center h-16">
          <X className="h-5 w-5 mb-1 text-gray-400" />
          <p className="text-sm font-medium text-gray-500">
            Drag lessons here to unschedule them
          </p>
          {isOver && (
            <div className="absolute inset-0 flex items-center justify-center bg-red-100 bg-opacity-30 pointer-events-none">
              <div className="bg-red-500 text-white p-1 rounded text-xs font-bold">
                Drop to Unschedule
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )
}
