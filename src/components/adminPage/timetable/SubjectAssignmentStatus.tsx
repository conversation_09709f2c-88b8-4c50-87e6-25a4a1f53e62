import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ClipboardList,
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

interface SubjectAssignmentStatusProps {
  timetableData: any
}

export function SubjectAssignmentStatus({
  timetableData,
}: SubjectAssignmentStatusProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (open && !analysisResults) {
      analyzeSubjectAssignments()
    }
  }, [open])

  const analyzeSubjectAssignments = () => {
    setIsLoading(true)

    try {
      // Extract the necessary data
      const inputData = timetableData.data || timetableData
      const scheduleData =
        timetableData.scheduleData ||
        timetableData.data?.scheduleData ||
        timetableData.result?.scheduleData ||
        []

      // Check if we have the required data
      if (
        !inputData ||
        !inputData.classes ||
        !inputData.teachers ||
        !scheduleData ||
        scheduleData.length === 0
      ) {
        setIsLoading(false)
        return
      }

      // Create a map of expected subject hours from input data
      const expectedSubjectHours = new Map()

      // Store all subject details for reference
      const subjectDetails = new Map()

      inputData.classes.forEach((classData: any) => {
        const className = classData.className
        classData.subjects.forEach((subject: any) => {
          const key = `${className}-${subject.subject}-${subject.teacherID}`
          expectedSubjectHours.set(key, subject.hours)
          subjectDetails.set(key, subject)
        })
      })

      // Count actual subject hours from schedule data
      const actualSubjectHours = new Map()

      scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class

        Object.entries(classSchedule).forEach(([day, slots]: [string, any]) => {
          if (day === 'class') return

          if (Array.isArray(slots)) {
            slots.forEach((slot: any) => {
              const key = `${className}-${slot.subject}-${slot.teacherID}`
              actualSubjectHours.set(
                key,
                (actualSubjectHours.get(key) || 0) + 1
              )
            })
          }
        })
      })

      // Create a map of teacher names
      const teacherNames = new Map()
      inputData.teachers.forEach((teacher: any) => {
        teacherNames.set(teacher.teacherId, teacher.teacherName)
      })

      // Group by class
      const classesList = inputData.classes.map((c: any) => c.className)

      const classSummaries: any = {}

      classesList.forEach((className: string) => {
        // Get all subjects for this class
        const classSubjects = Array.from(expectedSubjectHours.entries())
          .filter(([key]) => key.startsWith(`${className}-`))
          .map(([key, hours]) => {
            const [, subject, teacherId] = key.split('-')
            const actualHours = actualSubjectHours.get(key) || 0
            const teacherName =
              teacherNames.get(parseInt(teacherId)) || `Teacher ${teacherId}`
            const details = subjectDetails.get(key) || {}

            return {
              subject,
              teacherId,
              teacherName,
              expectedHours: hours,
              actualHours,
              status:
                actualHours === hours
                  ? 'COMPLETE'
                  : actualHours < hours
                    ? 'INCOMPLETE'
                    : 'OVERASSIGNED',
              details,
            }
          })

        // Sort by status (INCOMPLETE first, then OVERASSIGNED, then COMPLETE)
        classSubjects.sort((a, b) => {
          const statusOrder = { INCOMPLETE: 0, OVERASSIGNED: 1, COMPLETE: 2 }
          return (
            statusOrder[a.status as keyof typeof statusOrder] -
              statusOrder[b.status as keyof typeof statusOrder] ||
            a.subject.localeCompare(b.subject)
          )
        })

        // Summary for this class
        const complete = classSubjects.filter(
          (s) => s.status === 'COMPLETE'
        ).length
        const incomplete = classSubjects.filter(
          (s) => s.status === 'INCOMPLETE'
        ).length
        const overassigned = classSubjects.filter(
          (s) => s.status === 'OVERASSIGNED'
        ).length

        classSummaries[className] = {
          subjects: classSubjects,
          summary: {
            complete,
            incomplete,
            overassigned,
            total: classSubjects.length,
            completionRate:
              classSubjects.length > 0
                ? Math.round((complete / classSubjects.length) * 100)
                : 0,
          },
        }
      })

      // Check for subjects in schedule that weren't in input
      const unexpectedAssignments: any[] = []

      actualSubjectHours.forEach((hours, key) => {
        if (!expectedSubjectHours.has(key)) {
          const [className, subject, teacherId] = key.split('-')
          const teacherName =
            teacherNames.get(parseInt(teacherId)) || `Teacher ${teacherId}`

          unexpectedAssignments.push({
            className,
            subject,
            teacherId,
            teacherName,
            hours,
          })
        }
      })

      // Overall summary
      const totalSubjects = expectedSubjectHours.size
      const completeSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) === hours
      ).length
      const incompleteSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) < hours
      ).length
      const overassignedSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) > hours
      ).length

      const results = {
        classSummaries,
        unexpectedAssignments,
        overallSummary: {
          totalSubjects,
          completeSubjects,
          incompleteSubjects,
          overassignedSubjects,
          unexpectedCount: unexpectedAssignments.length,
          completionRate:
            totalSubjects > 0
              ? Math.round((completeSubjects / totalSubjects) * 100)
              : 0,
        },
      }

      setAnalysisResults(results)
    } catch (error) {
      console.error('Error analyzing subject assignments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETE':
        return <Badge className="bg-green-500">Complete</Badge>
      case 'INCOMPLETE':
        return <Badge variant="destructive">Incomplete</Badge>
      case 'OVERASSIGNED':
        return (
          <Badge
            variant="outline"
            className="border-yellow-500 text-yellow-500"
          >
            Overassigned
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <>
      <Button variant="outline" className="gap-2" onClick={() => setOpen(true)}>
        <ClipboardList className="h-4 w-4" />
        Subject Status
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Subject Assignment Status</DialogTitle>
          </DialogHeader>

          {isLoading ? (
            <div className="flex items-center justify-center h-[500px]">
              <div className="text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>Analyzing subject assignments...</p>
              </div>
            </div>
          ) : analysisResults ? (
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="classes">Classes</TabsTrigger>
                <TabsTrigger value="unexpected">Unexpected</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Overall Completion</h3>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subject Assignment Completion</span>
                      <span>
                        {analysisResults.overallSummary.completionRate}%
                      </span>
                    </div>
                    <Progress
                      value={analysisResults.overallSummary.completionRate}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="border rounded-md p-4 text-center">
                      <div className="flex items-center justify-center mb-2">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-lg font-medium">
                          {analysisResults.overallSummary.completeSubjects}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">Complete</p>
                    </div>

                    <div className="border rounded-md p-4 text-center">
                      <div className="flex items-center justify-center mb-2">
                        <XCircle className="h-5 w-5 text-red-500 mr-2" />
                        <span className="text-lg font-medium">
                          {analysisResults.overallSummary.incompleteSubjects}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Incomplete
                      </p>
                    </div>

                    <div className="border rounded-md p-4 text-center">
                      <div className="flex items-center justify-center mb-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                        <span className="text-lg font-medium">
                          {analysisResults.overallSummary.overassignedSubjects}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Overassigned
                      </p>
                    </div>
                  </div>

                  <h3 className="text-lg font-medium mt-6">
                    Class Completion Rates
                  </h3>

                  <div className="space-y-4">
                    {Object.entries(analysisResults.classSummaries).map(
                      ([className, data]: [string, any]) => (
                        <div key={className} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Class {className}</span>
                            <span>
                              {data.summary.completionRate}% (
                              {data.summary.complete}/{data.summary.total})
                            </span>
                          </div>
                          <Progress value={data.summary.completionRate} />
                        </div>
                      )
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="classes" className="space-y-4">
                <Tabs
                  defaultValue={Object.keys(analysisResults.classSummaries)[0]}
                  className="w-full"
                >
                  <TabsList className="flex flex-wrap h-auto">
                    {Object.keys(analysisResults.classSummaries).map(
                      (className) => (
                        <TabsTrigger
                          key={className}
                          value={className}
                          className="mb-1"
                        >
                          {className}
                        </TabsTrigger>
                      )
                    )}
                  </TabsList>

                  {Object.entries(analysisResults.classSummaries).map(
                    ([className, data]: [string, any]) => (
                      <TabsContent
                        key={className}
                        value={className}
                        className="space-y-4"
                      >
                        <div className="flex justify-between items-center">
                          <h3 className="text-lg font-medium">
                            Class {className}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">
                              {data.summary.complete}/{data.summary.total}{' '}
                              complete ({data.summary.completionRate}%)
                            </span>
                            <Progress
                              value={data.summary.completionRate}
                              className="w-24"
                            />
                          </div>
                        </div>

                        <ScrollArea className="h-[400px]">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Subject</TableHead>
                                <TableHead>Teacher</TableHead>
                                <TableHead className="text-center">
                                  Expected
                                </TableHead>
                                <TableHead className="text-center">
                                  Actual
                                </TableHead>
                                <TableHead className="text-center">
                                  Status
                                </TableHead>
                                <TableHead>Details</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {data.subjects.map(
                                (subject: any, index: number) => (
                                  <TableRow key={index}>
                                    <TableCell className="font-medium">
                                      {subject.subject}
                                    </TableCell>
                                    <TableCell>
                                      {subject.teacherName} ({subject.teacherId}
                                      )
                                    </TableCell>
                                    <TableCell className="text-center">
                                      {subject.expectedHours}
                                    </TableCell>
                                    <TableCell className="text-center">
                                      {subject.actualHours}
                                    </TableCell>
                                    <TableCell className="text-center">
                                      {getStatusBadge(subject.status)}
                                    </TableCell>
                                    <TableCell className="max-w-[200px] truncate">
                                      <code className="text-xs">
                                        {JSON.stringify(subject.details)}
                                      </code>
                                    </TableCell>
                                  </TableRow>
                                )
                              )}
                            </TableBody>
                          </Table>
                        </ScrollArea>
                      </TabsContent>
                    )
                  )}
                </Tabs>
              </TabsContent>

              <TabsContent value="unexpected" className="space-y-4">
                <h3 className="text-lg font-medium">Unexpected Assignments</h3>

                {analysisResults.unexpectedAssignments.length === 0 ? (
                  <p className="text-muted-foreground">
                    No unexpected assignments found.
                  </p>
                ) : (
                  <ScrollArea className="h-[400px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Class</TableHead>
                          <TableHead>Subject</TableHead>
                          <TableHead>Teacher</TableHead>
                          <TableHead className="text-center">Hours</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {analysisResults.unexpectedAssignments.map(
                          (assignment: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {assignment.className}
                              </TableCell>
                              <TableCell>{assignment.subject}</TableCell>
                              <TableCell>
                                {assignment.teacherName} ({assignment.teacherId}
                                )
                              </TableCell>
                              <TableCell className="text-center">
                                {assignment.hours}
                              </TableCell>
                            </TableRow>
                          )
                        )}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="flex items-center justify-center h-[500px]">
              <p className="text-muted-foreground">
                No timetable data available for analysis.
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
