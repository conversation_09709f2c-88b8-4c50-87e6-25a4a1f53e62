import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ClipboardList,
  Download,
  Search,
  Clock,
  User,
  BookOpen,
  School,
} from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'

interface EnhancedSubjectStatusProps {
  timetableData: any
  className?: string
}

export function EnhancedSubjectStatus({
  timetableData,
  className,
}: EnhancedSubjectStatusProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('unscheduled')
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredResults, setFilteredResults] = useState<any>(null)

  // Log timetable data when component mounts
  useEffect(() => {
    console.log(
      'EnhancedSubjectStatus - Component mounted with data:',
      JSON.stringify(timetableData, null, 2)
    )
  }, [])

  useEffect(() => {
    if (open && !analysisResults) {
      analyzeSubjectAssignments()
    }
  }, [open])

  useEffect(() => {
    if (analysisResults) {
      filterResults()
    }
  }, [searchTerm, analysisResults])

  const filterResults = () => {
    if (!analysisResults) return

    if (!searchTerm) {
      setFilteredResults(analysisResults)
      return
    }

    const term = searchTerm.toLowerCase()

    // Filter unscheduled subjects
    const filteredUnscheduled = analysisResults.unscheduledSubjects.filter(
      (subject: any) =>
        subject.className.toLowerCase().includes(term) ||
        subject.subject.toLowerCase().includes(term) ||
        subject.teacherName.toLowerCase().includes(term)
    )

    // Filter class summaries
    const filteredClassSummaries: Record<string, any> = {}
    Object.entries(analysisResults.classSummaries).forEach(
      ([className, data]: [string, any]) => {
        if (className.toLowerCase().includes(term)) {
          filteredClassSummaries[className] = data
        } else {
          const filteredSubjects = data.subjects.filter(
            (subject: any) =>
              subject.subject.toLowerCase().includes(term) ||
              subject.teacherName.toLowerCase().includes(term)
          )

          if (filteredSubjects.length > 0) {
            filteredClassSummaries[className] = {
              ...data,
              subjects: filteredSubjects,
            }
          }
        }
      }
    )

    setFilteredResults({
      ...analysisResults,
      unscheduledSubjects: filteredUnscheduled,
      classSummaries: filteredClassSummaries,
    })
  }

  const analyzeSubjectAssignments = () => {
    setIsLoading(true)

    try {
      // Log the timetable data for debugging
      console.log(
        'EnhancedSubjectStatus - Analyzing timetable data:',
        JSON.stringify(timetableData, null, 2)
      )

      // Extract the necessary data - handle different data formats
      let inputData = timetableData.data || timetableData
      let scheduleData =
        timetableData.scheduleData ||
        timetableData.data?.scheduleData ||
        timetableData.result?.scheduleData ||
        []

      // Special handling for the comparison result format we see in the screenshot
      if (timetableData.data && typeof timetableData.data === 'object') {
        // Check if this is the format from the comparison screen
        if (
          timetableData.data.classes &&
          Array.isArray(timetableData.data.classes) &&
          timetableData.data.teachers &&
          Array.isArray(timetableData.data.teachers) &&
          timetableData.data.timeSlots &&
          typeof timetableData.data.timeSlots === 'object'
        ) {
          console.log('EnhancedSubjectStatus - Detected comparison format')
          inputData = timetableData.data
        }
      }

      // If we have a result object with scheduleData
      if (timetableData.result && timetableData.result.scheduleData) {
        scheduleData = timetableData.result.scheduleData
      }

      console.log(
        'EnhancedSubjectStatus - Input data:',
        JSON.stringify(inputData, null, 2)
      )
      console.log(
        'EnhancedSubjectStatus - Schedule data:',
        JSON.stringify(scheduleData, null, 2)
      )

      // If we have a result object with scheduleData in a different format
      if (timetableData.result && timetableData.result.scheduleData) {
        scheduleData = timetableData.result.scheduleData
      }

      // If we have a scheduleData array directly in the timetable data
      if (Array.isArray(timetableData.scheduleData)) {
        scheduleData = timetableData.scheduleData
      }

      // Check if we have the required data
      if (!inputData || !inputData.classes || !inputData.teachers) {
        console.error('EnhancedSubjectStatus - Missing required data:', {
          hasInputData: !!inputData,
          hasClasses: !!(inputData && inputData.classes),
          hasTeachers: !!(inputData && inputData.teachers),
          scheduleDataAvailable: !!scheduleData && scheduleData.length > 0,
        })
        setIsLoading(false)
        return
      }

      // Create a map of expected subject hours from input data
      const expectedSubjectHours = new Map()

      // Store all subject details for reference
      const subjectDetails = new Map()

      inputData.classes.forEach((classData: any) => {
        const className = classData.className
        classData.subjects.forEach((subject: any) => {
          const key = `${className}-${subject.subject}-${subject.teacherID}`
          expectedSubjectHours.set(key, subject.hours)
          subjectDetails.set(key, subject)
        })
      })

      // Count actual subject hours from schedule data
      const actualSubjectHours = new Map()

      // Make sure we have schedule data to process
      if (scheduleData && scheduleData.length > 0) {
        scheduleData.forEach((classSchedule: any) => {
          const className = classSchedule.class

          Object.entries(classSchedule).forEach(
            ([day, slots]: [string, any]) => {
              if (day === 'class') return

              if (Array.isArray(slots)) {
                slots.forEach((slot: any) => {
                  const key = `${className}-${slot.subject}-${slot.teacherID}`
                  actualSubjectHours.set(
                    key,
                    (actualSubjectHours.get(key) || 0) + 1
                  )
                })
              }
            }
          )
        })
      } else {
        console.log(
          'EnhancedSubjectStatus - No schedule data available, showing only expected hours'
        )
      }

      // Create a map of teacher names
      const teacherNames = new Map()
      inputData.teachers.forEach((teacher: any) => {
        teacherNames.set(teacher.teacherId, teacher.teacherName)
      })

      // Group by class
      const classesList = inputData.classes.map((c: any) => c.className)

      const classSummaries: any = {}

      classesList.forEach((className: string) => {
        // Get all subjects for this class
        const classSubjects = Array.from(expectedSubjectHours.entries())
          .filter(([key]) => key.startsWith(`${className}-`))
          .map(([key, hours]) => {
            const [, subject, teacherId] = key.split('-')
            const actualHours = actualSubjectHours.get(key) || 0
            const teacherName =
              teacherNames.get(parseInt(teacherId)) || `Teacher ${teacherId}`
            const details = subjectDetails.get(key) || {}

            return {
              className,
              subject,
              teacherId,
              teacherName,
              expectedHours: hours,
              actualHours,
              remainingHours: hours - actualHours,
              status:
                actualHours === hours
                  ? 'COMPLETE'
                  : actualHours < hours
                    ? 'INCOMPLETE'
                    : 'OVERASSIGNED',
              details,
            }
          })

        // Sort by status (INCOMPLETE first, then OVERASSIGNED, then COMPLETE)
        classSubjects.sort((a, b) => {
          const statusOrder = { INCOMPLETE: 0, OVERASSIGNED: 1, COMPLETE: 2 }
          return (
            statusOrder[a.status as keyof typeof statusOrder] -
              statusOrder[b.status as keyof typeof statusOrder] ||
            a.subject.localeCompare(b.subject)
          )
        })

        // Summary for this class
        const complete = classSubjects.filter(
          (s) => s.status === 'COMPLETE'
        ).length
        const incomplete = classSubjects.filter(
          (s) => s.status === 'INCOMPLETE'
        ).length
        const overassigned = classSubjects.filter(
          (s) => s.status === 'OVERASSIGNED'
        ).length

        classSummaries[className] = {
          subjects: classSubjects,
          summary: {
            complete,
            incomplete,
            overassigned,
            total: classSubjects.length,
            completionRate:
              classSubjects.length > 0
                ? Math.round((complete / classSubjects.length) * 100)
                : 0,
          },
        }
      })

      // Get all unscheduled subjects
      const unscheduledSubjects = Array.from(expectedSubjectHours.entries())
        .filter(([key, hours]) => {
          const actualHours = actualSubjectHours.get(key) || 0
          return actualHours < hours
        })
        .map(([key, hours]) => {
          const [className, subject, teacherId] = key.split('-')
          const actualHours = actualSubjectHours.get(key) || 0
          const teacherName =
            teacherNames.get(parseInt(teacherId)) || `Teacher ${teacherId}`
          const details = subjectDetails.get(key) || {}

          return {
            className,
            subject,
            teacherId,
            teacherName,
            expectedHours: hours,
            actualHours,
            remainingHours: hours - actualHours,
            details,
          }
        })
        .sort(
          (a, b) =>
            a.className.localeCompare(b.className) ||
            a.subject.localeCompare(b.subject)
        )

      // Check for subjects in schedule that weren't in input
      const unexpectedAssignments: any[] = []

      actualSubjectHours.forEach((hours, key) => {
        if (!expectedSubjectHours.has(key)) {
          const [className, subject, teacherId] = key.split('-')
          const teacherName =
            teacherNames.get(parseInt(teacherId)) || `Teacher ${teacherId}`

          unexpectedAssignments.push({
            className,
            subject,
            teacherId,
            teacherName,
            hours,
          })
        }
      })

      // Overall summary
      const totalSubjects = expectedSubjectHours.size
      const completeSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) === hours
      ).length
      const incompleteSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) < hours
      ).length
      const overassignedSubjects = Array.from(
        expectedSubjectHours.entries()
      ).filter(
        ([key, hours]) => (actualSubjectHours.get(key) || 0) > hours
      ).length

      const results = {
        classSummaries,
        unscheduledSubjects,
        unexpectedAssignments,
        overallSummary: {
          totalSubjects,
          completeSubjects,
          incompleteSubjects,
          overassignedSubjects,
          unexpectedCount: unexpectedAssignments.length,
          completionRate:
            totalSubjects > 0
              ? Math.round((completeSubjects / totalSubjects) * 100)
              : 0,
        },
      }

      setAnalysisResults(results)
      setFilteredResults(results)
    } catch (error) {
      console.error('Error analyzing subject assignments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETE':
        return <Badge className="bg-green-500">Complete</Badge>
      case 'INCOMPLETE':
        return <Badge variant="destructive">Incomplete</Badge>
      case 'OVERASSIGNED':
        return (
          <Badge
            variant="outline"
            className="border-yellow-500 text-yellow-500"
          >
            Overassigned
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const exportToCSV = () => {
    if (!analysisResults) return

    // Create CSV content
    let csvContent =
      'Class,Subject,Teacher,TeacherID,Expected Hours,Actual Hours,Remaining Hours,Status,Details\n'

    // Add unscheduled subjects
    analysisResults.unscheduledSubjects.forEach((subject: any) => {
      csvContent += `${subject.className},${subject.subject},${subject.teacherName},${subject.teacherId},${subject.expectedHours},${subject.actualHours},${subject.remainingHours},"Incomplete",${JSON.stringify(subject.details).replace(/"/g, '""')}\n`
    })

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', 'unscheduled_subjects.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <>
      <Button
        variant="destructive"
        className={`gap-2 ${className || ''}`}
        onClick={() => setOpen(true)}
      >
        <ClipboardList className="h-4 w-4 mr-1" />
        Subject Status
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Subject Assignment Status</DialogTitle>
          </DialogHeader>

          {isLoading ? (
            <div className="flex items-center justify-center h-[500px]">
              <div className="text-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>Analyzing subject assignments...</p>
              </div>
            </div>
          ) : filteredResults ? (
            <>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by class, subject, or teacher..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  className="ml-2"
                  onClick={exportToCSV}
                  title="Export unscheduled subjects to CSV"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-4 gap-4 mb-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Completion
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {filteredResults.overallSummary.completionRate}%
                    </div>
                    <Progress
                      value={filteredResults.overallSummary.completionRate}
                      className="h-2 mt-2"
                    />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Complete
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <div className="text-2xl font-bold">
                      {filteredResults.overallSummary.completeSubjects}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Incomplete
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex items-center">
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                    <div className="text-2xl font-bold">
                      {filteredResults.overallSummary.incompleteSubjects}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Overassigned
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
                    <div className="text-2xl font-bold">
                      {filteredResults.overallSummary.overassignedSubjects}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid grid-cols-3">
                  <TabsTrigger value="unscheduled">
                    Unscheduled Lessons (
                    {filteredResults.unscheduledSubjects.length})
                  </TabsTrigger>
                  <TabsTrigger value="classes">
                    Classes (
                    {Object.keys(filteredResults.classSummaries).length})
                  </TabsTrigger>
                  <TabsTrigger value="unexpected">
                    Unexpected ({filteredResults.unexpectedAssignments.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="unscheduled" className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Unscheduled Lessons</h3>
                    <p className="text-sm text-muted-foreground">
                      {filteredResults.unscheduledSubjects.length} lessons need
                      to be scheduled
                    </p>
                  </div>

                  {filteredResults.unscheduledSubjects.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[300px] border rounded-md p-8">
                      <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                      <h3 className="text-xl font-medium mb-2">
                        All lessons scheduled!
                      </h3>
                      <p className="text-center text-muted-foreground">
                        All required lessons have been scheduled according to
                        the input data.
                      </p>
                    </div>
                  ) : (
                    <ScrollArea className="h-[400px] border rounded-md">
                      <div className="p-4 grid grid-cols-1 gap-4">
                        {filteredResults.unscheduledSubjects.map(
                          (subject: any, index: number) => (
                            <Card key={index} className="overflow-hidden">
                              <div
                                className={`h-2 ${subject.remainingHours > 2 ? 'bg-red-500' : 'bg-yellow-500'}`}
                              ></div>
                              <CardHeader className="pb-2">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <CardTitle className="text-base">
                                      {subject.subject}
                                    </CardTitle>
                                    <CardDescription>
                                      Class {subject.className}
                                    </CardDescription>
                                  </div>
                                  <Badge variant="outline" className="ml-2">
                                    {subject.actualHours}/
                                    {subject.expectedHours} hours
                                  </Badge>
                                </div>
                              </CardHeader>
                              <CardContent className="pb-2">
                                <div className="grid grid-cols-2 gap-4">
                                  <div className="flex items-center">
                                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                                    <span>
                                      {subject.teacherName} (ID:{' '}
                                      {subject.teacherId})
                                    </span>
                                  </div>
                                  <div className="flex items-center">
                                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                                    <span>
                                      {subject.remainingHours} hours remaining
                                    </span>
                                  </div>
                                </div>
                              </CardContent>
                              <CardFooter className="pt-0">
                                <div className="text-xs text-muted-foreground w-full overflow-hidden">
                                  <code className="block truncate">
                                    {JSON.stringify(subject.details)}
                                  </code>
                                </div>
                              </CardFooter>
                            </Card>
                          )
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </TabsContent>

                <TabsContent value="classes" className="space-y-4">
                  <Tabs
                    defaultValue={
                      Object.keys(filteredResults.classSummaries)[0] || ''
                    }
                    className="w-full"
                  >
                    <TabsList className="flex flex-wrap h-auto">
                      {Object.keys(filteredResults.classSummaries).map(
                        (className) => (
                          <TabsTrigger
                            key={className}
                            value={className}
                            className="mb-1"
                          >
                            {className}
                          </TabsTrigger>
                        )
                      )}
                    </TabsList>

                    {Object.entries(filteredResults.classSummaries).map(
                      ([className, data]: [string, any]) => (
                        <TabsContent
                          key={className}
                          value={className}
                          className="space-y-4"
                        >
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <School className="h-5 w-5 mr-2" />
                              <h3 className="text-lg font-medium">
                                Class {className}
                              </h3>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {data.summary.complete}/{data.summary.total}{' '}
                                complete ({data.summary.completionRate}%)
                              </span>
                              <Progress
                                value={data.summary.completionRate}
                                className="w-24"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-3 gap-4 mb-4">
                            <Card>
                              <CardHeader className="py-2">
                                <CardTitle className="text-sm font-medium">
                                  Complete
                                </CardTitle>
                              </CardHeader>
                              <CardContent className="py-2">
                                <div className="text-xl font-bold text-green-500">
                                  {data.summary.complete}
                                </div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="py-2">
                                <CardTitle className="text-sm font-medium">
                                  Incomplete
                                </CardTitle>
                              </CardHeader>
                              <CardContent className="py-2">
                                <div className="text-xl font-bold text-red-500">
                                  {data.summary.incomplete}
                                </div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="py-2">
                                <CardTitle className="text-sm font-medium">
                                  Overassigned
                                </CardTitle>
                              </CardHeader>
                              <CardContent className="py-2">
                                <div className="text-xl font-bold text-yellow-500">
                                  {data.summary.overassigned}
                                </div>
                              </CardContent>
                            </Card>
                          </div>

                          <ScrollArea className="h-[300px] border rounded-md">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Subject</TableHead>
                                  <TableHead>Teacher</TableHead>
                                  <TableHead className="text-center">
                                    Expected
                                  </TableHead>
                                  <TableHead className="text-center">
                                    Actual
                                  </TableHead>
                                  <TableHead className="text-center">
                                    Remaining
                                  </TableHead>
                                  <TableHead className="text-center">
                                    Status
                                  </TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {data.subjects.map(
                                  (subject: any, index: number) => (
                                    <TableRow key={index}>
                                      <TableCell className="font-medium">
                                        <div className="flex items-center">
                                          <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                                          {subject.subject}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center">
                                          <User className="h-4 w-4 mr-2 text-muted-foreground" />
                                          {subject.teacherName} (
                                          {subject.teacherId})
                                        </div>
                                      </TableCell>
                                      <TableCell className="text-center">
                                        {subject.expectedHours}
                                      </TableCell>
                                      <TableCell className="text-center">
                                        {subject.actualHours}
                                      </TableCell>
                                      <TableCell className="text-center">
                                        {subject.remainingHours > 0 ? (
                                          <span className="text-red-500">
                                            {subject.remainingHours}
                                          </span>
                                        ) : subject.remainingHours < 0 ? (
                                          <span className="text-yellow-500">
                                            {subject.remainingHours}
                                          </span>
                                        ) : (
                                          <span className="text-green-500">
                                            0
                                          </span>
                                        )}
                                      </TableCell>
                                      <TableCell className="text-center">
                                        {getStatusBadge(subject.status)}
                                      </TableCell>
                                    </TableRow>
                                  )
                                )}
                              </TableBody>
                            </Table>
                          </ScrollArea>
                        </TabsContent>
                      )
                    )}
                  </Tabs>
                </TabsContent>

                <TabsContent value="unexpected" className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">
                      Unexpected Assignments
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {filteredResults.unexpectedAssignments.length} unexpected
                      assignments found
                    </p>
                  </div>

                  {filteredResults.unexpectedAssignments.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[300px] border rounded-md p-8">
                      <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                      <h3 className="text-xl font-medium mb-2">
                        No unexpected assignments!
                      </h3>
                      <p className="text-center text-muted-foreground">
                        All scheduled lessons match the expected input data.
                      </p>
                    </div>
                  ) : (
                    <ScrollArea className="h-[400px] border rounded-md">
                      <div className="p-4 grid grid-cols-1 gap-4">
                        {filteredResults.unexpectedAssignments.map(
                          (assignment: any, index: number) => (
                            <Card key={index} className="overflow-hidden">
                              <div className="h-2 bg-yellow-500"></div>
                              <CardHeader className="pb-2">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <CardTitle className="text-base">
                                      {assignment.subject}
                                    </CardTitle>
                                    <CardDescription>
                                      Class {assignment.className}
                                    </CardDescription>
                                  </div>
                                  <Badge
                                    variant="outline"
                                    className="ml-2 bg-yellow-50"
                                  >
                                    {assignment.hours} hours
                                  </Badge>
                                </div>
                              </CardHeader>
                              <CardContent>
                                <div className="flex items-center">
                                  <User className="h-4 w-4 mr-2 text-muted-foreground" />
                                  <span>
                                    {assignment.teacherName} (ID:{' '}
                                    {assignment.teacherId})
                                  </span>
                                </div>
                                <p className="text-sm text-muted-foreground mt-2">
                                  This subject was not found in the input data
                                  but appears in the schedule.
                                </p>
                              </CardContent>
                            </Card>
                          )
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </TabsContent>
              </Tabs>
            </>
          ) : (
            <div className="flex items-center justify-center h-[500px]">
              <p className="text-muted-foreground">
                No timetable data available for analysis.
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
