import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'

// Define interfaces for the form
interface Subject {
  id: string | number
  name: string
}

interface Teacher {
  id: string | number
  name: string
  subjects: string[]
}

interface Room {
  id: string | number
  name: string
  type: string
}

interface Class {
  id: string | number
  name: string
}

interface LessonFormValues {
  subject: string
  teacher: string
  class: string
  room?: string
  hours: number
  // New fields for group division
  isGrouped: boolean
  divide?: number[]
  // New fields for alternating subjects
  alternateWith?: string
  alternateWithTeacher?: string
  alternationPattern?: 'weekly' | 'biweekly'
}

interface LessonFormProps {
  subjects: Subject[]
  teachers: Teacher[]
  rooms: Room[]
  classes: Class[]
  onSubmit: (values: LessonFormValues) => void
  onCancel: () => void
  initialValues?: Partial<LessonFormValues>
}

export function LessonForm({
  subjects,
  teachers,
  rooms,
  classes,
  onSubmit,
  onCancel,
  initialValues,
}: LessonFormProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('basic')

  // Form state
  const [formValues, setFormValues] = useState<LessonFormValues>({
    subject: initialValues?.subject || '',
    teacher: initialValues?.teacher || '',
    class: initialValues?.class || '',
    room: initialValues?.room || '',
    hours: initialValues?.hours || 1,
    isGrouped: initialValues?.isGrouped || false,
    divide: initialValues?.divide || [],
    alternateWith: initialValues?.alternateWith || '',
    alternateWithTeacher: initialValues?.alternateWithTeacher || '',
    alternationPattern: initialValues?.alternationPattern || 'weekly',
  })

  // Filtered lists based on selections
  const [eligibleTeachers, setEligibleTeachers] = useState<Teacher[]>([])
  const [eligibleAlternateTeachers, setEligibleAlternateTeachers] = useState<
    Teacher[]
  >([])

  // Update eligible teachers when subject changes
  useEffect(() => {
    if (formValues.subject) {
      const filtered = teachers.filter((teacher) =>
        teacher.subjects.includes(formValues.subject)
      )
      setEligibleTeachers(filtered)
    } else {
      setEligibleTeachers([])
    }
  }, [formValues.subject, teachers])

  // Update eligible alternate teachers when alternateWith changes
  useEffect(() => {
    if (formValues.alternateWith) {
      const filtered = teachers.filter((teacher) =>
        teacher.subjects.includes(formValues.alternateWith || '')
      )
      setEligibleAlternateTeachers(filtered)
    } else {
      setEligibleAlternateTeachers([])
    }
  }, [formValues.alternateWith, teachers])

  // Handle form field changes
  const handleChange = (field: keyof LessonFormValues, value: any) => {
    setFormValues((prev) => ({ ...prev, [field]: value }))

    // Reset dependent fields when primary fields change
    if (field === 'subject') {
      setFormValues((prev) => ({ ...prev, teacher: '' }))
    }

    if (field === 'alternateWith') {
      setFormValues((prev) => ({ ...prev, alternateWithTeacher: '' }))
    }

    if (field === 'isGrouped' && value === false) {
      setFormValues((prev) => ({ ...prev, divide: [] }))
    }
  }

  // Handle division input
  const handleDivisionChange = (index: number, value: string) => {
    const numValue = parseInt(value, 10) || 0
    const newDivide = [...(formValues.divide || [])]
    newDivide[index] = numValue

    setFormValues((prev) => ({ ...prev, divide: newDivide }))
  }

  // Add a new division
  const addDivision = () => {
    const newDivide = [...(formValues.divide || []), 1]
    setFormValues((prev) => ({ ...prev, divide: newDivide }))
  }

  // Remove a division
  const removeDivision = (index: number) => {
    const newDivide = [...(formValues.divide || [])]
    newDivide.splice(index, 1)
    setFormValues((prev) => ({ ...prev, divide: newDivide }))
  }

  // Calculate total hours from divisions
  const totalDivisionHours = (formValues.divide || []).reduce(
    (sum, hours) => sum + hours,
    0
  )

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formValues.subject || !formValues.teacher || !formValues.class) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      })
      return
    }

    if (formValues.hours < 1) {
      toast({
        title: 'Error',
        description: 'Hours must be at least 1',
        variant: 'destructive',
      })
      return
    }

    // Validate divisions if grouped
    if (
      formValues.isGrouped &&
      (!formValues.divide || formValues.divide.length === 0)
    ) {
      toast({
        title: 'Error',
        description: 'Please add at least one division for grouped lessons',
        variant: 'destructive',
      })
      return
    }

    // Validate that divisions sum up to total hours
    if (formValues.isGrouped && totalDivisionHours !== formValues.hours) {
      toast({
        title: 'Error',
        description: `Division hours (${totalDivisionHours}) must equal total hours (${formValues.hours})`,
        variant: 'destructive',
      })
      return
    }

    // Validate alternating subjects
    if (formValues.alternateWith && !formValues.alternateWithTeacher) {
      toast({
        title: 'Error',
        description: 'Please select a teacher for the alternating subject',
        variant: 'destructive',
      })
      return
    }

    // Submit the form
    onSubmit(formValues)
  }

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>Add Lesson</CardTitle>
        <CardDescription>
          Configure a new lesson for the timetable
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="basic">Basic Information</TabsTrigger>
              <TabsTrigger value="groups">Group Division</TabsTrigger>
              <TabsTrigger value="alternating">
                Alternating Subjects
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="class">Class</Label>
                  <Select
                    value={formValues.class}
                    onValueChange={(value) => handleChange('class', value)}
                  >
                    <SelectTrigger id="class">
                      <SelectValue placeholder="Select a class" />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map((cls) => (
                        <SelectItem key={cls.id} value={cls.id.toString()}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Select
                    value={formValues.subject}
                    onValueChange={(value) => handleChange('subject', value)}
                  >
                    <SelectTrigger id="subject">
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map((subject) => (
                        <SelectItem
                          key={subject.id}
                          value={subject.id.toString()}
                        >
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="teacher">Teacher</Label>
                  <Select
                    value={formValues.teacher}
                    onValueChange={(value) => handleChange('teacher', value)}
                    disabled={!formValues.subject}
                  >
                    <SelectTrigger id="teacher">
                      <SelectValue
                        placeholder={
                          formValues.subject
                            ? 'Select a teacher'
                            : 'Select a subject first'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {eligibleTeachers.map((teacher) => (
                        <SelectItem
                          key={teacher.id}
                          value={teacher.id.toString()}
                        >
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="room">Room (Optional)</Label>
                  <Select
                    value={formValues.room}
                    onValueChange={(value) => handleChange('room', value)}
                  >
                    <SelectTrigger id="room">
                      <SelectValue placeholder="Select a room" />
                    </SelectTrigger>
                    <SelectContent>
                      {rooms.map((room) => (
                        <SelectItem key={room.id} value={room.id.toString()}>
                          {room.name} ({room.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hours">Hours Per Week</Label>
                  <Input
                    id="hours"
                    type="number"
                    min={1}
                    max={20}
                    value={formValues.hours}
                    onChange={(e) =>
                      handleChange('hours', parseInt(e.target.value, 10) || 1)
                    }
                  />
                </div>

                <div className="flex items-center space-x-2 pt-6">
                  <Checkbox
                    id="isGrouped"
                    checked={formValues.isGrouped}
                    onCheckedChange={(checked) =>
                      handleChange('isGrouped', checked)
                    }
                  />
                  <Label htmlFor="isGrouped">Divide into groups</Label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="groups" className="space-y-4">
              <div className="p-4 border rounded-md bg-muted/20">
                <h3 className="text-lg font-medium mb-2">Group Division</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Divide the lesson into multiple groups. The sum of hours
                  should equal the total hours ({formValues.hours}).
                </p>

                <div className="flex items-center mb-2">
                  <Checkbox
                    id="isGrouped-tab"
                    checked={formValues.isGrouped}
                    onCheckedChange={(checked) =>
                      handleChange('isGrouped', checked)
                    }
                  />
                  <Label htmlFor="isGrouped-tab" className="ml-2">
                    Enable group division
                  </Label>
                </div>

                {formValues.isGrouped && (
                  <div className="space-y-4 mt-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Group Hours</h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addDivision}
                      >
                        Add Group
                      </Button>
                    </div>

                    {(formValues.divide || []).length === 0 ? (
                      <p className="text-sm text-muted-foreground">
                        No groups defined. Click "Add Group" to create a group.
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {(formValues.divide || []).map((hours, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-2"
                          >
                            <Label className="w-24">Group {index + 1}</Label>
                            <Input
                              type="number"
                              min={1}
                              value={hours}
                              onChange={(e) =>
                                handleDivisionChange(index, e.target.value)
                              }
                              className="w-20"
                            />
                            <span className="text-sm">hours</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDivision(index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}

                        <div className="flex justify-between text-sm pt-2">
                          <span>Total: {totalDivisionHours} hours</span>
                          {totalDivisionHours !== formValues.hours && (
                            <span className="text-destructive">
                              Should equal {formValues.hours} hours
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="alternating" className="space-y-4">
              <div className="p-4 border rounded-md bg-muted/20">
                <h3 className="text-lg font-medium mb-2">
                  Alternating Subjects
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure this lesson to alternate with another subject (e.g.,
                  Art alternating with Music).
                </p>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="alternateWith">
                      Alternate With Subject
                    </Label>
                    <Select
                      value={formValues.alternateWith}
                      onValueChange={(value) =>
                        handleChange('alternateWith', value)
                      }
                    >
                      <SelectTrigger id="alternateWith">
                        <SelectValue placeholder="Select a subject to alternate with (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None</SelectItem>
                        {subjects
                          .filter((s) => s.id.toString() !== formValues.subject)
                          .map((subject) => (
                            <SelectItem
                              key={subject.id}
                              value={subject.id.toString()}
                            >
                              {subject.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {formValues.alternateWith && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="alternateWithTeacher">
                          Teacher for Alternating Subject
                        </Label>
                        <Select
                          value={formValues.alternateWithTeacher}
                          onValueChange={(value) =>
                            handleChange('alternateWithTeacher', value)
                          }
                        >
                          <SelectTrigger id="alternateWithTeacher">
                            <SelectValue placeholder="Select a teacher" />
                          </SelectTrigger>
                          <SelectContent>
                            {eligibleAlternateTeachers.map((teacher) => (
                              <SelectItem
                                key={teacher.id}
                                value={teacher.id.toString()}
                              >
                                {teacher.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="alternationPattern">
                          Alternation Pattern
                        </Label>
                        <Select
                          value={formValues.alternationPattern}
                          onValueChange={(value) =>
                            handleChange(
                              'alternationPattern',
                              value as 'weekly' | 'biweekly'
                            )
                          }
                        >
                          <SelectTrigger id="alternationPattern">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekly">
                              Weekly (alternate every week)
                            </SelectItem>
                            <SelectItem value="biweekly">
                              Biweekly (alternate every two weeks)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save Lesson</Button>
        </CardFooter>
      </form>
    </Card>
  )
}
