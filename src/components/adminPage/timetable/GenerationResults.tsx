import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Loader, Check, Download, AlertTriangle, RefreshCw } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'

interface TimeSlot {
  start: string
  end: string
  isBreak?: boolean
}

interface TimetableDay {
  day: string
  slots: TimeSlot[]
}

interface TimetableAssignment {
  class: string
  teacher: string
  subject: string
  day: string
  period: number
  room?: string
}

interface GenerationResultsProps {
  isGenerating: boolean
  success?: boolean
  error?: string
  timetableData?: {
    assignments: TimetableAssignment[]
    timeslots: TimetableDay[]
    name: string
    classes: { id: string; name: string }[]
    teachers: { id: string; name: string }[]
    subjects: { id: string; name: string }[]
  }
  onRegenerate: () => void
  onSave: () => void
  onExport: () => void
}

export function GenerationResults({
  isGenerating,
  success,
  error,
  timetableData,
  onRegenerate,
  onSave,
  onExport,
}: GenerationResultsProps) {
  const [selectedView, setSelectedView] = useState<'class' | 'teacher'>('class')
  const [selectedEntity, setSelectedEntity] = useState<string>('')
  const [progress, setProgress] = useState(0)

  // Don't log component props

  // Simulate progress when generating
  useEffect(() => {
    if (isGenerating) {
      const interval = setInterval(() => {
        setProgress((oldProgress) => {
          const newProgress = Math.min(oldProgress + Math.random() * 10, 95)
          return newProgress
        })
      }, 1000)

      return () => {
        clearInterval(interval)
        if (!isGenerating && success) {
          setProgress(100)
        } else if (!isGenerating && !success) {
          setProgress(0)
        }
      }
    } else {
      if (success) {
        setProgress(100)
      } else {
        setProgress(0)
      }
    }
  }, [isGenerating, success])

  // Set default selected entity when timetable data is loaded
  useEffect(() => {
    if (timetableData && selectedEntity === '') {
      console.log('Setting default selected entity from timetable data')
      if (selectedView === 'class' && timetableData.classes.length > 0) {
        console.log('Setting default class:', timetableData.classes[0].id)
        setSelectedEntity(timetableData.classes[0].id)
      } else if (
        selectedView === 'teacher' &&
        timetableData.teachers.length > 0
      ) {
        console.log('Setting default teacher:', timetableData.teachers[0].id)
        setSelectedEntity(timetableData.teachers[0].id)
      }
    }
  }, [timetableData, selectedView, selectedEntity])

  // Handle tab change
  const handleViewChange = (value: string) => {
    setSelectedView(value as 'class' | 'teacher')
    setSelectedEntity('')
  }

  // Render the timetable grid
  const renderTimetable = () => {
    if (
      !timetableData ||
      !timetableData.assignments ||
      !timetableData.name ||
      !timetableData.classes ||
      timetableData.classes.length === 0
    ) {
      return (
        <div className="text-center p-4 border rounded-md bg-muted/20">
          No timetable data available
        </div>
      )
    }

    // Find the selected class object
    const selectedClassObj =
      timetableData.classes.find((c) => c.id === selectedEntity) ||
      timetableData.classes[0]
    const selectedClassName = selectedClassObj?.name

    // Try to find scheduleData for the selected class (from the server)
    let scheduleDataClass: any = null
    if ((window as any).serverScheduleData) {
      // If injected for debugging
      scheduleDataClass = (window as any).serverScheduleData.find(
        (c: any) => c.class === selectedClassName
      )
    } else if ((timetableData as any).scheduleData) {
      scheduleDataClass = (timetableData as any).scheduleData.find(
        (c: any) => c.class === selectedClassName
      )
    }

    // If not found, fallback to mapping assignments by day and time
    if (!scheduleDataClass) {
      // fallback to previous logic
      return (
        <div className="text-center p-4 border rounded-md bg-muted/20">
          No scheduleData found for class {selectedClassName}
        </div>
      )
    }

    // Build a list of all unique time strings across all days for this class, fully dynamic
    const allDays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ]
    const allTimeStringsSet = new Set<string>()
    allDays.forEach((day) => {
      ;(scheduleDataClass[day] || []).forEach((lesson: any) => {
        if (lesson.time) allTimeStringsSet.add(lesson.time)
      })
    })
    let allTimeStrings = Array.from(allTimeStringsSet)

    // Robust sort: parse hour and minute, fallback to string compare
    function parseTimeString(timeStr: string) {
      // Handles "8 => 9", "08:00 => 09:00", "14 => 15", "14:30 => 15:15"
      const [start] = timeStr.split('=>').map((s) => s.trim())
      const [h, m] = start.split(':')
      return {
        hour: parseInt(h, 10),
        minute: m ? parseInt(m, 10) : 0,
      }
    }
    allTimeStrings.sort((a, b) => {
      const ta = parseTimeString(a)
      const tb = parseTimeString(b)
      if (ta.hour !== tb.hour) return ta.hour - tb.hour
      return ta.minute - tb.minute
    })

    // Filter out days with no lessons
    const activeDays = allDays.filter(
      (day) => scheduleDataClass[day] && scheduleDataClass[day].length > 0
    )

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse">
          <thead>
            <tr>
              <th className="border p-2 bg-muted">Time</th>
              {activeDays.map((day, index) => (
                <th key={index} className="border p-2 bg-muted min-w-32">
                  {day}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {allTimeStrings.map((timeString) => (
              <tr key={timeString}>
                <td className="border p-2 bg-muted/50 text-sm">{timeString}</td>
                {activeDays.map((day, dayIdx) => {
                  const lesson = (scheduleDataClass[day] || []).find(
                    (l: any) => l.time === timeString
                  )
                  return (
                    <td key={dayIdx} className="border p-2">
                      {lesson ? (
                        <div>
                          {/* Check if this is a group lesson */}
                          {'group1' in lesson || 'group2' in lesson ? (
                            <div className="bg-blue-50 p-2 rounded-md">
                              {/* Group 1 */}
                              {lesson.group1 && (
                                <div className="mb-2">
                                  <div className="font-semibold text-sm">
                                    <span className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-xs mr-1">
                                      Group 1
                                    </span>
                                    {lesson.group1.subject}
                                  </div>
                                  <div className="text-xs text-gray-600 flex items-center mt-1">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-3 w-3 mr-1"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                      />
                                    </svg>
                                    {lesson.group1.teacher}
                                  </div>
                                  {lesson.group1.salle && (
                                    <div className="text-xs text-gray-600">
                                      {lesson.group1.salle}
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Group 2 */}
                              {lesson.group2 && (
                                <div
                                  className={
                                    lesson.group1
                                      ? 'pt-1 border-t border-dashed border-gray-300'
                                      : ''
                                  }
                                >
                                  <div className="font-semibold text-sm">
                                    <span className="bg-green-100 text-green-800 px-1 py-0.5 rounded text-xs mr-1">
                                      Group 2
                                    </span>
                                    {lesson.group2.subject}
                                  </div>
                                  <div className="text-xs text-gray-600 flex items-center mt-1">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-3 w-3 mr-1"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                      />
                                    </svg>
                                    {lesson.group2.teacher}
                                  </div>
                                  {lesson.group2.salle && (
                                    <div className="text-xs text-gray-600">
                                      {lesson.group2.salle}
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="bg-primary/5 p-2 rounded-md">
                              <div className="font-semibold text-sm">
                                {lesson.subject || 'Free Period'}
                              </div>
                              {lesson.teacher && (
                                <div className="text-xs text-gray-600 flex items-center mt-1">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-3 w-3 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                    />
                                  </svg>
                                  {lesson.teacher}
                                </div>
                              )}
                              {lesson.salle && (
                                <div className="text-xs text-gray-600">
                                  {lesson.salle}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="h-12 flex items-center justify-center text-xs text-gray-400">
                          Free
                        </div>
                      )}
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  // Render the component based on the current state

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Generation Results</CardTitle>
        <CardDescription>
          View and manage your generated timetable
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isGenerating ? (
          <div className="space-y-4">
            <div className="text-center">
              <Loader className="h-8 w-8 animate-spin mx-auto mb-2" />
              <h3 className="text-lg font-medium">Generating Timetable</h3>
              <p className="text-sm text-muted-foreground">
                This might take a minute. We're finding the optimal schedule.
              </p>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Generation Failed</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={onRegenerate}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Try Again
            </Button>
          </Alert>
        ) : success && timetableData ? (
          <div className="space-y-6">
            <Alert>
              <Check className="h-4 w-4" />
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>
                Your timetable "{timetableData.name}" has been successfully
                generated.
              </AlertDescription>
            </Alert>

            <div className="flex justify-between items-center">
              <Tabs
                defaultValue="class"
                value={selectedView}
                onValueChange={handleViewChange}
                className="w-full"
              >
                <TabsList>
                  <TabsTrigger value="class">View by Class</TabsTrigger>
                  <TabsTrigger value="teacher">View by Teacher</TabsTrigger>
                </TabsList>
                <TabsContent value="class">
                  <div className="flex flex-wrap gap-2 my-2">
                    {timetableData.classes.map((classItem) => (
                      <Button
                        key={classItem.id}
                        variant={
                          selectedEntity === classItem.id
                            ? 'default'
                            : 'outline'
                        }
                        size="sm"
                        onClick={() => setSelectedEntity(classItem.id)}
                      >
                        {classItem.name}
                      </Button>
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="teacher">
                  <div className="flex flex-wrap gap-2 my-2">
                    {timetableData.teachers.map((teacher) => (
                      <Button
                        key={teacher.id}
                        variant={
                          selectedEntity === teacher.id ? 'default' : 'outline'
                        }
                        size="sm"
                        onClick={() => setSelectedEntity(teacher.id)}
                      >
                        {teacher.name}
                      </Button>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <Separator />

            {renderTimetable()}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={onRegenerate}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Regenerate
              </Button>
              <Button variant="outline" onClick={onExport}>
                <Download className="h-4 w-4 mr-1" />
                Export to Excel
              </Button>
              <Button onClick={onSave}>
                <Check className="h-4 w-4 mr-1" />
                Save Timetable
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center p-8 border rounded-md bg-muted/20">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <h3 className="text-lg font-medium">No Results</h3>
            <p className="text-sm text-muted-foreground">
              Generate a timetable to see the results here.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
