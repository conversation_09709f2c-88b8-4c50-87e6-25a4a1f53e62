import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'

import { Loader2, Plus, Pencil, Trash2, School } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  CustomPagination as Pagination,
  CustomPaginationContent as PaginationContent,
  CustomPaginationEllipsis as PaginationEllipsis,
  CustomPaginationItem as PaginationItem,
  CustomPaginationLink as PaginationLink,
  CustomPaginationNext as PaginationNext,
  CustomPaginationPrevious as PaginationPrevious,
} from '@/components/ui/custom-pagination'
import { ClassroomForm, ClassroomFormValues } from './ClassroomForm'
import { Classroom } from '@/interface/types/classroom'

export function ClassroomManagement() {
  const { toast } = useToast()
  const [isAddClassroomOpen, setIsAddClassroomOpen] = useState(false)
  const [isEditClassroomOpen, setIsEditClassroomOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedClassroom, setSelectedClassroom] = useState<Classroom | null>(
    null
  )
  const [classroomToDelete, setClassroomToDelete] = useState<Classroom | null>(
    null
  )
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  // State for classroom data
  const [classroomsData, setClassroomsData] = useState<{
    data: Classroom[]
    meta: { totalItems: number; totalPages: number; currentPage: number }
  }>({ data: [], meta: { totalItems: 0, totalPages: 1, currentPage: 1 } })
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)

  // Function to fetch classrooms
  const fetchClassrooms = async () => {
    setIsLoading(true)
    setIsError(false)

    try {
      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      // Try getting all classrooms without pagination first
      try {
        const response = await fetch(
          `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classrooms`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (!response.ok) {
          // If this fails, try the singular endpoint
          throw new Error(
            `API returned ${response.status}: ${response.statusText}`
          )
        }

        const result = await response.json()
        console.log('All classrooms fetched successfully:', result)

        // Handle the response data
        const allClassrooms = Array.isArray(result) ? result : []

        // Apply pagination manually
        const startIndex = (currentPage - 1) * itemsPerPage
        const endIndex = startIndex + itemsPerPage
        const paginatedClassrooms = allClassrooms.slice(startIndex, endIndex)

        setClassroomsData({
          data: paginatedClassrooms,
          meta: {
            totalItems: allClassrooms.length,
            totalPages: Math.ceil(allClassrooms.length / itemsPerPage),
            currentPage: currentPage,
          },
        })
      } catch (firstError) {
        console.error(
          'First API attempt failed, trying singular endpoint:',
          firstError
        )

        // Try the singular endpoint without pagination
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classroom`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (!response.ok) {
            // If both fail, we'll create an empty data structure
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const result = await response.json()
          console.log(
            'All classrooms fetched successfully (singular endpoint):',
            result
          )

          // Handle the response data
          const allClassrooms = Array.isArray(result) ? result : []

          // Apply pagination manually
          const startIndex = (currentPage - 1) * itemsPerPage
          const endIndex = startIndex + itemsPerPage
          const paginatedClassrooms = allClassrooms.slice(startIndex, endIndex)

          setClassroomsData({
            data: paginatedClassrooms,
            meta: {
              totalItems: allClassrooms.length,
              totalPages: Math.ceil(allClassrooms.length / itemsPerPage),
              currentPage: currentPage,
            },
          })
        } catch (secondError) {
          console.error('Both API attempts failed:', secondError)
          console.log('Setting empty classroom data')

          // If both endpoints fail, set empty data
          setClassroomsData({
            data: [],
            meta: {
              totalItems: 0,
              totalPages: 1,
              currentPage: 1,
            },
          })
        }
      }
    } catch (error) {
      console.error('Error fetching classrooms:', error)
      setIsError(true)
      toast({
        title: 'Error',
        description: 'Failed to load classrooms. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Effect to fetch classrooms when component mounts or pagination changes
  useEffect(() => {
    fetchClassrooms()
  }, [currentPage, itemsPerPage])

  // Function to refresh the classroom list
  const refreshClassrooms = () => {
    fetchClassrooms()
  }

  // Handle creating a new classroom
  const handleCreateClassroom = async (data: ClassroomFormValues) => {
    try {
      // Get the user's etablissementId from localStorage
      const etablissementId =
        localStorage.getItem('etablissementId') ||
        '11111111-1111-1111-1111-111111111111'

      // Convert features from string to array
      const featuresArray = data.features
        ? data.features.split(',').map((feature) => feature.trim())
        : []

      // Ensure capacity is a number
      const capacity =
        typeof data.capacity === 'string'
          ? parseInt(data.capacity, 10)
          : data.capacity

      // Create a clean object with only the required properties
      const createData = {
        name: data.name,
        type: data.type,
        capacity: capacity,
        floor: data.floor,
        building: data.building,
        features: featuresArray,
        notes: data.notes,
        isActive: data.isActive,
        etablissementId: data.etablissementId || etablissementId,
      }

      console.log('Creating classroom with data:', createData)

      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      // Make a direct API call instead of using the mutation
      try {
        // Try the singular endpoint first
        const response = await fetch(
          `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classroom`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(createData),
          }
        )

        if (!response.ok) {
          throw new Error(
            `API returned ${response.status}: ${response.statusText}`
          )
        }

        const result = await response.json()
        console.log('Classroom created successfully:', result)

        // Refresh the classroom list
        fetchClassrooms()
        setIsAddClassroomOpen(false)
        toast({
          title: 'Success',
          description: 'Classroom created successfully',
        })
      } catch (apiError) {
        console.error(
          'First API attempt failed, trying plural endpoint:',
          apiError
        )

        // Try the plural endpoint as fallback
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classrooms`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(createData),
            }
          )

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const result = await response.json()
          console.log(
            'Classroom created successfully (plural endpoint):',
            result
          )

          // Refresh the classroom list
          fetchClassrooms()
          setIsAddClassroomOpen(false)
          toast({
            title: 'Success',
            description: 'Classroom created successfully',
          })
        } catch (secondApiError) {
          console.error('Both API attempts failed:', secondApiError)
          throw new Error(
            'Failed to create classroom after trying both endpoints'
          )
        }
      }
    } catch (error) {
      console.error('Failed to create classroom:', error)
      toast({
        title: 'Error',
        description: 'Failed to create classroom. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Handle updating a classroom
  const handleUpdateClassroom = async (data: ClassroomFormValues) => {
    if (!selectedClassroom?.id) return

    try {
      // Convert features from string to array
      const featuresArray = data.features
        ? data.features.split(',').map((feature) => feature.trim())
        : []

      // Ensure capacity is a number
      const capacity =
        typeof data.capacity === 'string'
          ? parseInt(data.capacity, 10)
          : data.capacity

      // Create a clean object with only the required properties
      const updateData = {
        name: data.name,
        type: data.type,
        capacity: capacity,
        floor: data.floor,
        building: data.building,
        features: featuresArray,
        notes: data.notes,
        isActive: data.isActive,
        etablissementId: selectedClassroom.etablissementId,
      }

      console.log('Updating classroom with data:', {
        id: selectedClassroom.id,
        data: updateData,
      })

      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      // Make a direct API call instead of using the mutation
      try {
        // Try the singular endpoint first
        const response = await fetch(
          `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classroom/${selectedClassroom.id}`,
          {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(updateData),
          }
        )

        if (!response.ok) {
          throw new Error(
            `API returned ${response.status}: ${response.statusText}`
          )
        }

        const result = await response.json()
        console.log('Classroom updated successfully:', result)

        // Refresh the classroom list
        fetchClassrooms()
        setIsEditClassroomOpen(false)
        setSelectedClassroom(null)
        toast({
          title: 'Success',
          description: 'Classroom updated successfully',
        })
      } catch (apiError) {
        console.error(
          'First API attempt failed, trying plural endpoint:',
          apiError
        )

        // Try the plural endpoint as fallback
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classroom/${selectedClassroom.id}`,
            {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(updateData),
            }
          )

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const result = await response.json()
          console.log(
            'Classroom updated successfully (plural endpoint):',
            result
          )

          // Refresh the classroom list
          fetchClassrooms()
          setIsEditClassroomOpen(false)
          setSelectedClassroom(null)
          toast({
            title: 'Success',
            description: 'Classroom updated successfully',
          })
        } catch (secondApiError) {
          console.error('Both API attempts failed:', secondApiError)
          throw new Error(
            'Failed to update classroom after trying both endpoints'
          )
        }
      }
    } catch (error) {
      console.error('Failed to update classroom:', error)
      toast({
        title: 'Error',
        description: 'Failed to update classroom. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Open dialog for editing a classroom
  const handleShowClassroom = (classroom: Classroom) => {
    setSelectedClassroom(classroom)
    setIsEditClassroomOpen(true)
  }

  // Open dialog for deleting a classroom
  const handleDeleteClassroom = (classroom: Classroom) => {
    setClassroomToDelete(classroom)
    setIsDeleteDialogOpen(true)
  }

  // Confirm deletion
  const confirmDelete = async () => {
    if (classroomToDelete) {
      try {
        setIsDeleting(true)
        console.log('Deleting classroom with ID:', classroomToDelete.id)

        // Get the token from localStorage
        const token = localStorage.getItem('access_token')

        // Make a direct API call instead of using the mutation
        try {
          // Try the singular endpoint first
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classroom/${classroomToDelete.id}`,
            {
              method: 'DELETE',
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          console.log('Classroom deleted successfully')

          // Refresh the classroom list
          fetchClassrooms()
          setIsDeleteDialogOpen(false)
          setClassroomToDelete(null)
          toast({
            title: 'Success',
            description: 'Classroom deleted successfully',
          })
        } catch (apiError) {
          console.error(
            'First API attempt failed, trying plural endpoint:',
            apiError
          )

          // Try the plural endpoint as fallback
          try {
            const response = await fetch(
              `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/classrooms/${classroomToDelete.id}`,
              {
                method: 'DELETE',
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            )

            if (!response.ok) {
              throw new Error(
                `API returned ${response.status}: ${response.statusText}`
              )
            }

            console.log('Classroom deleted successfully (plural endpoint)')

            // Refresh the classroom list
            fetchClassrooms()
            setIsDeleteDialogOpen(false)
            setClassroomToDelete(null)
            toast({
              title: 'Success',
              description: 'Classroom deleted successfully',
            })
          } catch (secondApiError) {
            console.error('Both API attempts failed:', secondApiError)
            throw new Error(
              'Failed to delete classroom after trying both endpoints'
            )
          }
        }
      } catch (error) {
        console.error('Failed to delete classroom:', error)
        toast({
          title: 'Error',
          description: 'Failed to delete classroom. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsDeleting(false)
      }
    }
  }

  // Filter classrooms based on search query
  const filteredClassrooms =
    classroomsData?.data?.filter(
      (classroom) =>
        searchQuery === '' ||
        classroom.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        classroom.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        classroom.building.toLowerCase().includes(searchQuery.toLowerCase())
    ) || []

  // Calculate total pages
  const totalPages = classroomsData?.meta?.totalPages || 1

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      // Calculate start and end of middle pages
      let start = Math.max(2, currentPage - 1)
      let end = Math.min(totalPages - 1, currentPage + 1)

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = 4
      } else if (currentPage >= totalPages - 1) {
        start = totalPages - 3
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push('ellipsis1')
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push('ellipsis2')
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (isError) return <div>Error loading classrooms. Please try again.</div>

  return (
    <div className="container mx-auto py-6 px-4">
      <Card className="border shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Classroom Management</CardTitle>
            <CardDescription>
              Manage classrooms, labs, and other spaces in your establishment
            </CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              {classroomsData?.meta?.totalItems || 0} Classrooms
            </Badge>
            <Button onClick={() => setIsAddClassroomOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Classroom
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="flex gap-2">
                <Input
                  placeholder="Search classrooms by name, type, or building..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
                <Button
                  variant="outline"
                  onClick={refreshClassrooms}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-refresh-cw"
                    >
                      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                      <path d="M21 3v5h-5" />
                      <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                      <path d="M3 21v-5h5" />
                    </svg>
                  )}
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <Table>
                <TableCaption>
                  List of classrooms in your establishment
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Features</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredClassrooms.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No classrooms found. Click "Add Classroom" to create
                        one.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredClassrooms.map((classroom) => (
                      <TableRow key={classroom.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <School className="h-4 w-4 text-muted-foreground" />
                            {classroom.name}
                          </div>
                        </TableCell>
                        <TableCell>{classroom.type}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{classroom.capacity}</Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{`${classroom.floor}, ${classroom.building}`}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {classroom.features
                              ?.slice(0, 2)
                              .map((feature, index) => (
                                <Badge key={index} variant="outline">
                                  {feature}
                                </Badge>
                              ))}
                            {classroom.features?.length > 2 && (
                              <Badge variant="outline">
                                +{classroom.features.length - 2} more
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {classroom.isActive ? (
                            <Badge variant="success">Active</Badge>
                          ) : (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleShowClassroom(classroom)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteClassroom(classroom)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }
                          disabled={currentPage === 1}
                        />
                      </PaginationItem>

                      {getPageNumbers().map((page, index) =>
                        page === 'ellipsis1' || page === 'ellipsis2' ? (
                          <PaginationItem key={`ellipsis-${index}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => setCurrentPage(Number(page))}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(totalPages, prev + 1)
                            )
                          }
                          disabled={currentPage === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Classroom Dialog */}
      <Dialog open={isAddClassroomOpen} onOpenChange={setIsAddClassroomOpen}>
        <DialogContent className="sm:max-w-[600px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Add New Classroom</DialogTitle>
          </DialogHeader>
          <ClassroomForm
            onSubmit={handleCreateClassroom}
            onCancel={() => setIsAddClassroomOpen(false)}
            submitLabel="Add Classroom"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Classroom Dialog */}
      <Dialog open={isEditClassroomOpen} onOpenChange={setIsEditClassroomOpen}>
        <DialogContent className="sm:max-w-[600px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Edit Classroom</DialogTitle>
          </DialogHeader>
          <ClassroomForm
            initialData={selectedClassroom || undefined}
            onSubmit={handleUpdateClassroom}
            onCancel={() => setIsEditClassroomOpen(false)}
            submitLabel="Update Classroom"
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete the classroom "
            {classroomToDelete?.name}"? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
