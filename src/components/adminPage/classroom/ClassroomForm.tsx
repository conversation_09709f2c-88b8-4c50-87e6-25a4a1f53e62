import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Classroom } from '@/interface/types/classroom'

const classroomSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.string().min(1, 'Type is required'),
  capacity: z.coerce.number().min(1, 'Capacity must be at least 1'),
  floor: z.string().min(1, 'Floor is required'),
  building: z.string().min(1, 'Building is required'),
  features: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
  etablissementId: z.string().min(1, 'Establishment ID is required'),
})

export type ClassroomFormValues = z.infer<typeof classroomSchema>

interface ClassroomFormProps {
  initialData?: Partial<Classroom>
  onSubmit: (data: ClassroomFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing classrooms
 * @param {ClassroomFormProps} props - The props for the ClassroomForm component
 * @returns {JSX.Element} The rendered ClassroomForm component
 */
export function ClassroomForm({
  initialData,
  onSubmit,
  onCancel,
  submitLabel,
}: ClassroomFormProps) {
  // Get the user's etablissementId from localStorage
  const etablissementId =
    localStorage.getItem('etablissementId') ||
    '11111111-1111-1111-1111-111111111111'

  const form = useForm<ClassroomFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: initialData?.name || '',
      type: initialData?.type || '',
      capacity: initialData?.capacity || 0,
      floor: initialData?.floor || '',
      building: initialData?.building || '',
      features: initialData?.features ? initialData.features.join(', ') : '',
      notes: initialData?.notes || '',
      isActive:
        initialData?.isActive !== undefined ? initialData.isActive : true,
      etablissementId: initialData?.etablissementId || etablissementId,
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="name"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Name is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Room 101"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="type"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Type is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="type">Type</Label>
              <Input
                id="type"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Regular Classroom"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="capacity"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Capacity is required'
              if (value < 1) return 'Capacity must be at least 1'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                value={field.state.value.toString()}
                onBlur={field.handleBlur}
                onChange={(e) =>
                  field.handleChange(parseInt(e.target.value) || 0)
                }
                placeholder="30"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="floor"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Floor is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="floor">Floor</Label>
              <Input
                id="floor"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="1st Floor"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <form.Field
        name="building"
        validators={{
          onChange: ({ value }) => {
            if (!value) return 'Building is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="space-y-2">
            <Label htmlFor="building">Building</Label>
            <Input
              id="building"
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Main Building"
            />
            {field.state.meta.errors && (
              <p className="text-sm text-red-500">
                {field.state.meta.errors.join(', ')}
              </p>
            )}
          </div>
        )}
      </form.Field>

      <form.Field name="features">
        {(field) => (
          <div className="space-y-2">
            <Label htmlFor="features">Features</Label>
            <Input
              id="features"
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Projector, Whiteboard, Air Conditioning"
            />
            <p className="text-xs text-muted-foreground">
              Enter features separated by commas
            </p>
          </div>
        )}
      </form.Field>

      <form.Field name="notes">
        {(field) => (
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Additional information about this classroom"
            />
          </div>
        )}
      </form.Field>

      <form.Field name="isActive">
        {(field) => (
          <div className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <Label className="text-base">Active Status</Label>
              <p className="text-sm text-muted-foreground">
                Inactive classrooms won't appear in scheduling options
              </p>
            </div>
            <Switch
              checked={field.state.value}
              onCheckedChange={field.handleChange}
            />
          </div>
        )}
      </form.Field>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
