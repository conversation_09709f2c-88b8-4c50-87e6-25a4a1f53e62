// /User/FormField.jsx

export const FormField = ({
  form,
  name,
  placeholder,
  type = 'text',
  validation = {},
}: {
  form: any
  name: string
  placeholder: any
  type?: string
  validation?: any
}) => {
  return (
    <form.Field
      name={name}
      validate={validation}
      children={(field: any) => (
        <div className="space-y-1">
          <input
            name={field.name}
            value={field.state.value}
            onBlur={field.handleBlur}
            onChange={(e) => field.handleChange(e.target.value)}
            type={type}
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {field.state.meta.touchedErrors?.[0] && (
            <span className="text-sm text-red-500">
              {field.state.meta.touchedErrors[0]}
            </span>
          )}
        </div>
      )}
    />
  )
}
