import { useForm } from '@tanstack/react-form'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'

// Base API URL
const api_url = 'process.env.VITE_API_URL'

// Helper function to get auth header
const getAuthHeader = () => {
  const token = localStorage.getItem('access_token')
  if (!token) throw new Error('Access token not found')
  return { Authorization: `Bearer ${token}` }
}

// API functions
const getAllSubjects = async () => {
  try {
    const response = await axios.get(`${api_url}/subject`, {
      headers: getAuthHeader(),
    })
    return response.data
  } catch (error) {
    console.error('Error fetching subjects:', error)
    throw new Error('Failed to fetch subjects')
  }
}

const addTeacher = async (teacherData: any) => {
  try {
    const response = await axios.post(`${api_url}/teacher`, teacherData, {
      headers: getAuth<PERSON>eader(),
    })
    return response.data
  } catch (error) {
    console.error('Error adding teacher:', error)
    throw new Error('Failed to add teacher')
  }
}

// Query and Mutation Hooks
const useSubjects = () => {
  return useQuery({
    queryKey: ['subjects'],
    queryFn: getAllSubjects,
  })
}

const useAddTeacher = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: addTeacher,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
    },
  })
}
type FormValues = {
  cin: string
  firstname: string
  lastname: string
  email: string
  password: string
  title: string
  subjectIds: string[] // Explicitly define as string[]
  updatedBy: string
}

// TeacherForm Component
export const TeacherForm = () => {
  const { data: subjects, isLoading, error } = useSubjects()
  const addTeacherMutation = useAddTeacher()

  const form = useForm<FormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      cin: '',
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      title: '',
      subjectIds: [], // Now subjectIds is inferred as string[]
      updatedBy: localStorage.getItem('id') || '',
    },
    onSubmit: async (values) => {
      console.log('Form submitted:', values)

      try {
        const teacherData = {
          ...values,
          role: 'Teacher',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
        await addTeacherMutation.mutateAsync(teacherData)
        alert('Teacher added successfully!')
        form.reset() // Reset the form after successful submission
      } catch (error) {
        console.error('Failed to add teacher:', error)
        alert('Failed to add teacher. Please try again.')
      }
    },
  })

  if (isLoading) return <div className="p-4">Loading subjects...</div>
  if (error) return <div className="p-4 text-red-500">{error.message}</div>

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
    >
      <form.Field
        name="cin"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'CIN is required'
            if (value.value.length < 8)
              return 'CIN must be at least 8 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="CIN"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="firstname"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'First name is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="First Name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="lastname"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Last name is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Last Name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="email"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Email is required'
            if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value.value)) {
              return 'Invalid email address'
            }
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="email"
              placeholder="Email"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="password"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Password is required'
            if (value.value.length < 6)
              return 'Password must be at least 6 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="password"
              placeholder="Password"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="title"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Title is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Title"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="subjectIds"
        validators={{
          onChange: (value) => {
            if (!value.value || value.value.length === 0)
              return 'Please select at least one subject'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Subjects
            </label>
            <div className="space-y-2">
              {subjects.map((subject: { id: string; name: string }) => (
                <div key={subject.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={subject.id}
                    value={subject.id}
                    checked={(field.state.value as string[]).includes(
                      subject.id
                    )} // Cast to string[]
                    onChange={(e) => {
                      const updatedValues = e.target.checked
                        ? [...(field.state.value || []), subject.id]
                        : (field.state.value || []).filter(
                            (value: string) => value !== subject.id
                          )
                      field.handleChange(updatedValues as string[]) // Cast to string[]
                    }}
                    className="h-4 w-4 text-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor={subject.id}
                    className="ml-2 text-sm text-gray-700"
                  >
                    {subject.name}
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}
      </form.Field>

      <button
        type="submit"
        className="w-full p-2 mt-4 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Submit
      </button>
    </form>
  )
}
