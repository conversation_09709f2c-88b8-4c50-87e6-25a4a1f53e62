import { useForm } from '@tanstack/react-form'
import { createSubject } from '@/lib/clases/subject'
import { toast } from 'sonner'
import { Subject } from '@/interface/types'

interface SubjectFormData {
  name: string
  description: string
}

export const MatiereForm = () => {
  const form = useForm<SubjectFormData, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: '',
      description: '',
    },
    onSubmit: async ({ value }) => {
      try {
        const id_admin = localStorage.getItem('id')
        if (!id_admin) throw new Error('Admin ID not found')

        const subjectData: Omit<Subject, 'id'> = {
          name: value.name,
          description: value.description,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: '',
          updatedBy: '',
          etablissementId: '',
        }

        const response = await createSubject(subjectData)
        toast.success('Subject created successfully')
        form.reset()
        return response
      } catch (error) {
        console.error('Error submitting form:', error)
        toast.error(
          error instanceof Error ? error.message : 'Failed to create subject'
        )
        throw error
      }
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        void form.handleSubmit()
      }}
      className="p-4 space-y-4"
    >
      <form.Field
        name="name"
        validators={{
          onChange: ({ value }) => {
            if (!value) return 'Subject Name is required'
            if (value.length < 2)
              return 'Subject Name must be at least 2 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="space-y-1">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Subject Name
            </label>
            <input
              id="name"
              type="text"
              placeholder="Enter subject name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {field.state.meta.errors && (
              <span className="text-red-500 text-sm">
                {field.state.meta.errors.join(', ')}
              </span>
            )}
          </div>
        )}
      </form.Field>

      <form.Field
        name="description"
        validators={{
          onChange: ({ value }) => {
            if (!value) return 'Description is required'
            if (value.length < 10)
              return 'Description must be at least 10 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="space-y-1">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700"
            >
              Description
            </label>
            <textarea
              id="description"
              placeholder="Enter subject description"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              rows={3}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {field.state.meta.errors && (
              <span className="text-red-500 text-sm">
                {field.state.meta.errors.join(', ')}
              </span>
            )}
          </div>
        )}
      </form.Field>

      <button
        type="submit"
        disabled={form.state.isSubmitting}
        className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300 transition-colors"
      >
        {form.state.isSubmitting ? 'Creating Subject...' : 'Create Subject'}
      </button>
    </form>
  )
}
