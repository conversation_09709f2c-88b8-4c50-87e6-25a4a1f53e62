import { useForm } from '@tanstack/react-form'

export const StudentForm = () => {
  const form = useForm({
    defaultValues: {
      cin: '',
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      gradeLevel: '',
      enrolledDate: '',
      parentId: '',
      classId: '',
    },
    onSubmit: async (values) => {
      console.log('Form submitted:', values)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        void form.handleSubmit()
      }}
      className="p-4"
    >
      <form.Field
        name="cin"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'CIN is required'
            if (value.value.length < 8)
              return 'CIN must be at least 8 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="CIN"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="firstname"
        validators={{
          onChange: (value) => {
            if (!value) return 'First name is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="First Name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="lastname"
        validators={{
          onChange: (value) => {
            if (!value) return 'Last name is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Last Name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="email"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Email is required' // Access value.value
            if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value.value)) {
              // Access value.value
              return 'Invalid email address'
            }
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="email"
              placeholder="Email"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="password"
        validators={{
          onChange: (value) => {
            if (!value.value) return 'Password is required'
            if (value.value.length < 6)
              return 'Password must be at least 6 characters'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="password"
              placeholder="Password"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="gradeLevel"
        validators={{
          onChange: (value) => {
            if (!value) return 'Grade Level is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Grade Level"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="enrolledDate"
        validators={{
          onChange: (value) => {
            if (!value) return 'Enrolled Date is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="date"
              placeholder="Enrolled Date"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
              title="Enrolled Date:"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="parentId"
        validators={{
          onChange: (value) => {
            if (!value) return 'Parent ID is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Parent ID"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="classId"
        validators={{
          onChange: (value) => {
            if (!value) return 'Class ID is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Class ID"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <button
        type="submit"
        className="w-full p-2 mt-4 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Submit
      </button>
    </form>
  )
}
