import { useForm } from '@tanstack/react-form'

export const ClassForm = () => {
  const form = useForm({
    defaultValues: {
      nameClass: '',
      gradeId: '',
      supervisorId: '',
    },
    onSubmit: async (values) => {
      console.log('Form submitted:', values)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        void form.handleSubmit()
      }}
      className=""
    >
      <form.Field
        name="nameClass"
        validators={{
          onChange: (value) => {
            if (!value) return 'Class Name is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Class Name"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="gradeId"
        validators={{
          onChange: (value) => {
            if (!value) return 'Grade ID is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Grade ID"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <form.Field
        name="supervisorId"
        validators={{
          onChange: (value) => {
            if (!value) return 'Supervisor ID is required'
            return undefined
          },
        }}
      >
        {(field) => (
          <div className="mb-4">
            <input
              type="text"
              placeholder="Supervisor ID"
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
        )}
      </form.Field>

      <button
        type="submit"
        className="w-full p-2 mt-4 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Submit
      </button>
    </form>
  )
}
