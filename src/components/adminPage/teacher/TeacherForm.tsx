import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Teacher } from '@/interface/types/user'
import { useState } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { FormYearMonthPicker } from '@/components/ui/form-year-month-picker'
import FileUploader from '@/components/shared/FileUploader'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'

const teacherSchema = z.object({
  cin: z.string().min(8, 'CIN must be at least 8 characters'),
  firstname: z.string().min(2, 'First name must be at least 2 characters'),
  lastname: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password must contain at least one special character'
    )
    .optional(),
  title: z.string().min(1, 'Title is required'),
  subjectIds: z.array(z.string()).min(1, 'At least one subject is required'),
  birthday: z.date().optional(),
  gender: z.enum(['male', 'female']).optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  avatar: z.string().optional(),
  isActive: z.boolean().default(true),
})

export type TeacherFormValues = z.infer<typeof teacherSchema>

interface TeacherFormProps {
  initialData?: Partial<Teacher>
  subjects: Array<{ id: string; name: string }>
  onSubmit: (data: TeacherFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

export function TeacherForm({
  initialData,
  subjects,
  onSubmit,
  onCancel,
  submitLabel,
  onRefreshSubjects,
}: TeacherFormProps & { onRefreshSubjects?: () => void }) {
  // State for password visibility toggle
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isPasswordFocused, setIsPasswordFocused] = useState(false)

  // Function to toggle password visibility
  const togglePasswordVisibility = () => {
    setIsPasswordVisible((prev) => !prev)
  }

  // Convert string date to Date object for the form
  const getBirthdayDate = () => {
    if (initialData?.birthday) {
      return new Date(initialData.birthday)
    }
    return undefined
  }

  // Log initial data for debugging
  console.log('TeacherForm initialData:', initialData);
  console.log('TeacherForm subjects:', subjects);

  const form = useForm<TeacherFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      cin: initialData?.cin || '',
      firstname: initialData?.firstname || '',
      lastname: initialData?.lastname || '',
      email: initialData?.email || '',
      password: '',
      title: initialData?.title || '',
      // Extract subject IDs from the subjects array if available
      subjectIds: initialData?.subjects?.map(subject => {
        // Handle different types of subject data
        if (typeof subject === 'string') return subject;
        if (typeof subject === 'object' && subject !== null) {
          // Handle case where subject is an object with id property
          return (subject as any).id || subject;
        }
        return subject;
      }) || [],
      birthday: getBirthdayDate(),
      gender:
        (initialData?.gender?.toLowerCase() as 'male' | 'female') || 'male',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      isActive: true,
    },
    onSubmit: async (data) => {
      await onSubmit(data.value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="cin"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'CIN is required'
              if (value.length < 8) return 'CIN must be at least 8 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="cin">CIN</Label>
              <Input
                id="cin"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter CIN"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="firstname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'First name is required'
              if (value.length < 2)
                return 'First name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="firstname">First Name</Label>
              <Input
                id="firstname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter first name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="lastname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Last name is required'
              if (value.length < 2)
                return 'Last name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="lastname">Last Name</Label>
              <Input
                id="lastname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter last name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
                return 'Invalid email format'
              }
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter email"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        {!initialData ? (
          <form.Field
            name="password"
            validators={{
              onChange: ({ value }) => {
                if (!value) return 'Password is required'
                if (value.length < 6) return 'Password must be at least 6 characters'
                if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter'
                if (!/[0-9]/.test(value)) return 'Password must contain at least one number'
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) return 'Password must contain at least one special character'
                return undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={isPasswordVisible ? 'text' : 'password'}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={togglePasswordVisibility}
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>
        ) : (
          <form.Field
            name="password"
            validators={{
              onBlur: ({ value }) => {
                // Skip validation if empty (keeping current password)
                if (!value || value.trim() === '') return undefined
                
                // Validate password only if provided
                const errors = []
                if (value.length < 6) {
                  errors.push('Password must be at least 6 characters')
                }
                if (!/[A-Z]/.test(value)) {
                  errors.push('Password must contain at least one uppercase letter')
                }
                if (!/[0-9]/.test(value)) {
                  errors.push('Password must contain at least one number')
                }
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
                  errors.push('Password must contain at least one special character')
                }

                return errors.length > 0 ? errors.join(', ') : undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password (leave empty to keep current)</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={isPasswordVisible ? 'text' : 'password'}
                    value={field.state.value}
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => {
                      field.handleBlur()
                      setIsPasswordFocused(false)
                    }}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter new password or leave empty"
                    className={
                      field.state.meta.isTouched && field.state.meta.errors
                        ? 'border-red-500'
                        : ''
                    }
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={togglePasswordVisibility}
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
                {isPasswordFocused && (
                  <p className="text-xs text-gray-500">
                    Leave empty to keep the current password. Enter a new password to change it.
                  </p>
                )}
              </div>
            )}
          </form.Field>
        )}

        <form.Field
          name="title"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Title is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Select
                value={field.state.value}
                onValueChange={field.handleChange}
              >
                <SelectTrigger id="title">
                  <SelectValue placeholder="Select title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Professor">Professor</SelectItem>
                  <SelectItem value="Dr.">Dr.</SelectItem>
                  <SelectItem value="Assistant Professor">
                    Assistant Professor
                  </SelectItem>
                  {/* Add the current title if it's not in the list */}
                  {initialData?.title &&
                    !["Professor", "Dr.", "Assistant Professor"].includes(initialData.title) && (
                    <SelectItem value={initialData.title}>
                      {initialData.title}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="subjectIds"
          validators={{
            onChange: () => {
              // Allow empty arrays for subjects
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="subjects">Subjects</Label>
                {onRefreshSubjects && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      onRefreshSubjects();
                    }}
                    className="h-8 px-2 text-xs"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                      <path d="M21 3v5h-5" />
                      <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                      <path d="M3 21v-5h5" />
                    </svg>
                    Refresh Subjects
                  </Button>
                )}
              </div>
              <Select
                value={field.state.value[field.state.value.length - 1] || ''}
                onValueChange={(value) => {
                  if (!field.state.value.includes(value)) {
                    field.handleChange([...field.state.value, value])
                  }
                }}
              >
                <SelectTrigger id="subjects">
                  <SelectValue placeholder="Select subjects" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.length === 0 ? (
                    <div className="p-2 text-center text-sm text-gray-500">
                      No subjects available.
                      {onRefreshSubjects && (
                        <Button
                          type="button"
                          variant="link"
                          size="sm"
                          onClick={(e) => {
                            e.preventDefault();
                            onRefreshSubjects();
                          }}
                          className="px-1"
                        >
                          Refresh
                        </Button>
                      )}
                    </div>
                  ) : (
                    subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              <div className="mt-2 flex flex-wrap gap-2">
                {field.state.value.map((subjectId) => {
                  const subject = subjects.find((s) => s.id === subjectId)
                  if (!subject) return null
                  return (
                    <Button
                      key={subjectId}
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault()
                        field.handleChange(
                          field.state.value.filter((id) => id !== subjectId)
                        )
                      }}
                    >
                      {subject.name} ×
                    </Button>
                  )
                })}
              </div>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="birthday">
          {(field) => (
            <FormYearMonthPicker
              id="birthday"
              label="Birthday"
              value={field.state.value}
              onChange={(date) => field.handleChange(date)}
              onBlur={field.handleBlur}
              placeholder="Select birthday"
            />
          )}
        </form.Field>

        <form.Field
          name="gender"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Gender is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={field.state.value}
                onValueChange={(value: 'male' | 'female') =>
                  field.handleChange(value)
                }
              >
                <SelectTrigger id="gender">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="address">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter address"
              />
            </div>
          )}
        </form.Field>

        <form.Field name="phone">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter phone number"
              />
            </div>
          )}
        </form.Field>

        <form.Field name="avatar">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="avatar">Avatar</Label>
              <FileUploader
                label="Teacher Avatar"
                defaultPreview={field.state.value || DEFAULT_AVATAR}
                onFileUploaded={(url) => field.handleChange(url)}
                isAvatar={true}
                previewComponent={(previewUrl) => (
                  <CdnImage
                    src={previewUrl}
                    alt="Teacher avatar"
                    className="h-28 w-28 rounded-full object-cover border-2 border-gray-200 shadow-sm"
                  />
                )}
              />
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
