import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/components/ui/use-toast'
import {
  Teacher,
  CreateTeacherDTO,
  UpdateTeacherDTO,
} from '@/interface/types/user'
import { services } from '@/lib/api/index'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { TeacherForm } from './TeacherForm'
import type { TeacherFormValues } from './TeacherForm'
import Loading from '@/components/shared/Loading'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface TeacherFilters {
  cin: string
  name: string
  title: string
}

interface ActiveFilter {
  key: keyof TeacherFilters
  value: string
  label: string
}

export default function TeacherManagement(): JSX.Element {
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState<boolean>(false)
  const [isAddTeacherOpen, setIsAddTeacherOpen] = useState<boolean>(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false)
  const [teacherToDelete, setTeacherToDelete] = useState<Teacher | null>(null)
  const [currentPage, setCurrentPage] = useState(1)

  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Queries
  const {
    data: teachers = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['teachers'],
    queryFn: services.teacher.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Use a shorter stale time for subjects to ensure they're refreshed more frequently
  const {
    data: subjects = [],
    refetch: refetchSubjects
  } = useQuery({
    queryKey: ['subjects'],
    queryFn: services.subject.getAll,
    staleTime: 1000 * 30, // 30 seconds - much shorter to ensure fresh data
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gets focus
  })

  // Mutations
  const updateTeacherMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTeacherDTO }) =>
      services.teacher.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      setIsProfileModalOpen(false)
      toast({
        title: "Teacher Updated",
        description: "The teacher has been successfully updated.",
        variant: "default",
      })
    },
  })

  const createTeacherMutation = useMutation({
    mutationFn: (data: CreateTeacherDTO) => services.teacher.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      setIsAddTeacherOpen(false)
      toast({
        title: "Teacher Created",
        description: "The teacher has been successfully created.",
        variant: "default",
      })
    },
  })

  const deleteTeacherMutation = useMutation({
    mutationFn: (id: string) => services.teacher.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] })
      setIsDeleteDialogOpen(false)
      setTeacherToDelete(null)
      toast({
        title: "Teacher Deleted",
        description: "The teacher has been successfully deleted.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error('Error deleting teacher:', error)
      toast({
        title: "Deletion Failed",
        description: "Failed to delete teacher. Please try again.",
        variant: "destructive",
      })
      setIsDeleteDialogOpen(false)
    },
  })

  // Get unique values for select filters
  const uniqueTitles = [...new Set(teachers?.map((t) => t.title) || [])]

  // Filter and search teachers
  const filteredTeachers =
    teachers?.filter((teacher) => {
      // Search query matching
      const searchMatches =
        searchQuery === '' ||
        `${teacher.firstname} ${teacher.lastname} ${teacher.cin}`
          .toLowerCase()
          .includes(searchQuery.toLowerCase())

      // All active filters must match
      const filtersMatch = activeFilters.every((filter) => {
        switch (filter.key) {
          case 'title':
            return teacher.title === filter.value
          default:
            return true
        }
      })

      return searchMatches && filtersMatch
    }) || []

  // Pagination setup
  const teachersPerPage = 5
  const totalPages = Math.ceil(filteredTeachers.length / teachersPerPage)
  const indexOfLastTeacher = currentPage * teachersPerPage
  const indexOfFirstTeacher = indexOfLastTeacher - teachersPerPage
  const currentTeachers = filteredTeachers.slice(
    indexOfFirstTeacher,
    indexOfLastTeacher
  )

  const handleShowProfile = (teacher: Teacher) => {
    console.log('Selected teacher:', teacher);
    console.log('Teacher subjects:', teacher.subjects);
    setSelectedTeacher(teacher)
    setIsProfileModalOpen(true)
  }

  const handleDeleteClick = (teacher: Teacher) => {
    setTeacherToDelete(teacher)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (teacherToDelete) {
      deleteTeacherMutation.mutate(teacherToDelete.id)
    }
  }

  const handleFilterChange = (
    key: keyof TeacherFilters,
    value: string,
    label: string
  ) => {
    setActiveFilters((prev) => {
      const filtered = prev.filter((f) => f.key !== key)
      if (value !== 'all') {
        return [...filtered, { key, value, label }]
      }
      return filtered
    })
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setSearchQuery('')
    setCurrentPage(1)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const handleUpdateTeacher = async (data: TeacherFormValues) => {
    if (!selectedTeacher?.id) return
    try {
      // Create update data object
      const updateData: UpdateTeacherDTO = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        title: data.title,
        avatar: data.avatar || selectedTeacher.avatar,
        isActive: data.isActive,
        // Ensure subjectIds is an array of valid UUIDs or send an empty array
        subjectIds: Array.isArray(data.subjectIds) && data.subjectIds.length > 0 ? data.subjectIds : [],
        // Include CIN from the original teacher data
        cin: selectedTeacher.cin,
        // IMPORTANT: Only include password if it's provided and not empty
        ...(data.password && data.password.trim() !== '' ? { password: data.password } : {}),
        gender: (data.gender || 'male').toUpperCase() as 'male' | 'female',
        // Add address and phone fields
        address: data.address,
        phone: data.phone,
        // Include birthday if available
        ...(data.birthday ? { birthday: data.birthday.toISOString() } : {}),
      }
      
      // Log the update data for debugging (hide password)
      console.log('Updating teacher with data:', {
        ...updateData,
        password: updateData.password ? '***HIDDEN***' : undefined
      })
      
      await updateTeacherMutation.mutateAsync({
        id: selectedTeacher.id,
        data: updateData,
      })
    } catch (error) {
      console.error('Failed to update teacher:', error)
    }
  }

  const handleCreateTeacher = async (data: TeacherFormValues) => {
    try {
      if (!data.password) {
        throw new Error('Password is required for new teachers')
      }
      const createData: CreateTeacherDTO = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        password: data.password,
        cin: data.cin,
        title: data.title,
        birthday: data.birthday?.toISOString() || new Date().toISOString(),
        gender: (data.gender || 'MALE').toUpperCase(), // Default to male if undefined, convert to uppercase
        avatar: data.avatar || DEFAULT_AVATAR,
        isActive: data.isActive,
        // Ensure subjectIds is an array of valid UUIDs or send an empty array
        subjectIds: Array.isArray(data.subjectIds) && data.subjectIds.length > 0 ? data.subjectIds : [],
      } as any // Cast to any to avoid TypeScript errors
      await createTeacherMutation.mutateAsync(createData)
    } catch (error: any) {
      console.error('Failed to create teacher:', error)
      // Display the error message from the server
      if (error.response?.data?.message) {
        toast({
          title: "Creation Failed",
          description: `Error: ${JSON.stringify(error.response.data.message)}`,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Creation Failed",
          description: "An error occurred while creating the teacher.",
          variant: "destructive",
        })
      }
    }
  }

  // Function to render a teacher row without using hooks inside
  const renderTeacherRow = (teacher: Teacher) => {
    // We don't use the unread count feature for now to avoid hook issues

    return (
      <TableRow key={teacher.id}>
        <TableCell>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full overflow-hidden">
              <CdnImage
                src={teacher.avatar || DEFAULT_AVATAR}
                alt={`${teacher.firstname} ${teacher.lastname}`}
                className="w-full h-full object-cover"
              />
            </div>
            <span>
              {teacher.firstname} {teacher.lastname}
            </span>
          </div>
        </TableCell>
        <TableCell>{teacher.cin}</TableCell>
        <TableCell>{teacher.title || 'No Title'}</TableCell>
        <TableCell>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleShowProfile(teacher)}
            >
              Profile
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteClick(teacher)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  if (isLoading) return <Loading />
  if (isError) return <p>Error: {error?.message}</p>

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Teachers Table</h2>
        <Button onClick={() => setIsAddTeacherOpen(true)}>
          Add New Teacher
        </Button>
        <Badge variant="secondary" className="text-sm">
          {teachers?.length || 0} Teachers
        </Badge>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search teachers by name or CIN..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Teachers</h4>
                <div className="space-y-2">
                  <Label htmlFor="title-filter">Title</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange('title', value, `Title: ${value}`)
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="title-filter">
                      <SelectValue placeholder="Select title" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Titles</SelectItem>
                      {uniqueTitles.map((title) => (
                        <SelectItem key={title} value={title || ''}>
                          {title || 'No Title'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="px-3 py-1 gap-2"
            >
              {filter.label}
              <button
                onClick={() =>
                  setActiveFilters((prev) =>
                    prev.filter((f) => f.key !== filter.key)
                  )
                }
                className="text-xs rounded-full hover:bg-gray-200 h-4 w-4 inline-flex items-center justify-center"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-7"
          >
            Clear all
          </Button>
        </div>
      )}

      <div className="mt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Teacher</TableHead>
              <TableHead>CIN</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>{currentTeachers.map(renderTeacherRow)}</TableBody>
        </Table>
      </div>

      <div className="mt-6 flex justify-center">
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Add Profile Modal */}
      <Dialog open={isProfileModalOpen} onOpenChange={setIsProfileModalOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Teacher Profile</DialogTitle>
          </DialogHeader>
          {selectedTeacher && (
            <TeacherForm
              initialData={selectedTeacher}
              subjects={subjects.map((s) => ({
                id: String(s.id),
                name: s.name,
              }))}
              onSubmit={handleUpdateTeacher}
              onCancel={() => setIsProfileModalOpen(false)}
              submitLabel="Update Teacher"
              onRefreshSubjects={refetchSubjects}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add New Teacher Modal */}
      <Dialog open={isAddTeacherOpen} onOpenChange={setIsAddTeacherOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Teacher</DialogTitle>
          </DialogHeader>
          <TeacherForm
            subjects={subjects.map((s) => ({ id: String(s.id), name: s.name }))}
            onSubmit={handleCreateTeacher}
            onCancel={() => setIsAddTeacherOpen(false)}
            submitLabel="Add Teacher"
            onRefreshSubjects={refetchSubjects}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete the teacher
              {teacherToDelete && (
                <span className="font-semibold">
                  {" "}"{teacherToDelete.firstname} {teacherToDelete.lastname}"{" "}
                </span>
              )}
              and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
