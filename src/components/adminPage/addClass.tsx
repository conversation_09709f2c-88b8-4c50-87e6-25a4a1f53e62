import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import { createClass } from '../../lib/clases/class' // Assuming you have a function to create a class

export const ClassForm = () => {
  const navigate = useNavigate()

  // Use the mutation hook for creating a class
  const { mutate, isPending } = useMutation({
    mutationFn: createClass,
    onSuccess: (data) => {
      console.log('Class created successfully:', data)
      alert('Class created successfully!')
      form.reset() // Reset the form
      navigate({ to: '/classes' }) // Navigate to the classes page
    },
    onError: (error) => {
      console.error('Error creating class:', error)
      alert('Error creating class: ' + error.message)
    },
  })

  const form = useForm({
    defaultValues: {
      nameClass: '',
      gradeId: '',
      supervisorId: '',
    },
    onSubmit: async ({ value }) => {
      console.log('Form submitted with values:', value) // Log form values on submission

      try {
        // According to the API documentation, we should only send name, gradeId, and supervisorId
        // The createdBy, updatedBy, createdAt, updatedAt fields are handled by the backend
        const classData = {
          name: value.nameClass,
          gradeId: value.gradeId,
          supervisorId: value.supervisorId,
          // Do NOT send createdBy, updatedBy, createdAt, updatedAt as they're handled by the backend
        }

        // Use mutate from useMutation to trigger the createClass request
        mutate(classData)
      } catch (error) {
        console.error('Form submission error:', error)
      }
    },
  })

  return (
    <div className="flex h-screen mt-[1px]">
      {/* Left side form */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-250px]">
          <h1 className="text-2xl font-bold text-center">Add Class</h1>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Class Name Field */}
              <div>
                <form.Field
                  name="nameClass"
                  validators={{
                    onChange: (value) => {
                      if (!value) return 'Class Name is required'
                      return undefined
                    },
                  }}
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="Class Name"
                    />
                  )}
                />
              </div>

              {/* Grade ID Field */}
              <div>
                <form.Field
                  name="gradeId"
                  validators={{
                    onChange: (value) => {
                      if (!value) return 'Grade ID is required'
                      return undefined
                    },
                  }}
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="Grade ID"
                    />
                  )}
                />
              </div>

              {/* Supervisor ID Field */}
              <div>
                <form.Field
                  name="supervisorId"
                  validators={{
                    onChange: (value) => {
                      if (!value) return 'Supervisor ID is required'
                      return undefined
                    },
                  }}
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="text"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="Supervisor ID"
                    />
                  )}
                />
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  disabled={isPending} // Disable the button while loading
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary"
                >
                  {isPending ? 'Creating...' : 'Create Class'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
