import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import { AlertCircle, HelpCircle } from 'lucide-react'
import { services } from '../../mockData/SuperAdminServiceData'
import { hasEstablishment } from '@/utils/establishmentUtils'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

export default function SuperAdminService() {
  // State to track if the superadmin has created an establishment
  const [hasCreatedEstablishment, setHasCreatedEstablishment] = useState<
    boolean | null
  >(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check if the superadmin has created an establishment
  useEffect(() => {
    const checkEstablishment = async () => {
      try {
        const result = await hasEstablishment()
        setHasCreatedEstablishment(result)

        // If an establishment was created, clear the needs_establishment flag
        if (result) {
          localStorage.removeItem('needs_establishment')
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error checking establishment:', error)
        setHasCreatedEstablishment(false)
        setIsLoading(false)
      }
    }

    checkEstablishment()

    // Set up an interval to periodically check for establishments
    const intervalId = setInterval(checkEstablishment, 30000) // Check every 30 seconds

    return () => {
      clearInterval(intervalId)
    }
  }, [])

  // Check if the user has completed onboarding but still needs to create an establishment
  useEffect(() => {
    const needsEstablishment =
      localStorage.getItem('needs_establishment') === 'true'
    const userOnboarding = localStorage.getItem('userOnboarding') === 'false' // User completed onboarding

    // If the user has completed onboarding but still needs to create an establishment,
    // redirect them to the establishments page
    if (
      needsEstablishment &&
      userOnboarding &&
      !hasCreatedEstablishment &&
      !isLoading
    ) {
      console.log('SuperAdminService: Redirecting to establishments page')
      window.location.href = '/super_admin/etablisments'
    }
  }, [hasCreatedEstablishment, isLoading])

  // Auto-start tour for new users
  useEffect(() => {
    // Check if this is a new user who needs onboarding
    const token = localStorage.getItem('token')

    if (token) {
      console.log('SuperAdminService: Checking if user needs onboarding')

      // Fetch user data to check onboarding status
      fetch('http://localhost:3000/user-controller/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((data) => {
          console.log('SuperAdminService: User data from API:', data)

          // Check if userOnboarding is TRUE in the database
          if (data.userOnboarding === true) {
            console.log(
              'SuperAdminService: User needs onboarding, starting tour'
            )

            // Set localStorage to match database
            localStorage.setItem('userOnboarding', 'true')
            localStorage.removeItem('tour_completed_super_admin')

            // Use URL parameter approach for more reliable tour start
            console.log('SuperAdminService: Redirecting to start tour')

            // First try the event approach
            window.dispatchEvent(new CustomEvent('start-super-admin-tour'))

            // Then use the URL parameter approach as a backup
            const currentUrl = window.location.href
            const separator = currentUrl.includes('?') ? '&' : '?'

            // Add a small delay before redirecting
            setTimeout(() => {
              // Redirect to the same page with startTour=true parameter
              window.location.href = `${currentUrl}${separator}startTour=true`
            }, 500)
          }
        })
        .catch((error) => {
          console.error('SuperAdminService: Error fetching user data:', error)
        })
    }
  }, [])

  // Function to determine if a service should be disabled
  const isServiceDisabled = (serviceName: string) => {
    if (isLoading) return true // Disable all services while loading
    if (hasCreatedEstablishment) return false // Enable all services if establishment exists

    // If no establishment exists, only enable the Establishments service
    return (
      serviceName.toLowerCase() !== 'etablisments' &&
      serviceName.toLowerCase() !== 'establishments'
    )
  }

  return (
    <div className="flex flex-col items-center justify-center max-w-5xl mx-auto p-6 space-y-8">
      {/* Show warning if no establishment exists */}
      {!isLoading && hasCreatedEstablishment === false && (
        <Alert variant="destructive" className="mb-4 establishments-card">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Important!</AlertTitle>
          <AlertDescription>
            You must create an establishment before accessing other services.
            Please click on the Establishments card below to create your first
            establishment.
          </AlertDescription>
        </Alert>
      )}

      <section>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold super-admin-dashboard-header">
            Services
          </h2>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => {
              console.log('Take Tour button clicked')

              // Force reset any tour completion status
              localStorage.removeItem('tour_completed_super_admin')

              // Set userOnboarding to true
              localStorage.setItem('userOnboarding', 'true')

              // Dispatch the event to start the tour
              window.dispatchEvent(new CustomEvent('start-super-admin-tour'))

              // Use URL parameter approach as a backup
              const currentUrl = window.location.href
              const separator = currentUrl.includes('?') ? '&' : '?'

              // Add a small delay before redirecting
              setTimeout(() => {
                // Redirect to the same page with startTour=true parameter
                window.location.href = `${currentUrl}${separator}startTour=true`
              }, 300)
            }}
          >
            <HelpCircle className="h-4 w-4 mr-1" />
            <span>Take Tour</span>
          </Button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {services.map((service, index) => {
            const isDisabled = isServiceDisabled(service.name) || service.soon

            return (
              <Card
                key={index}
                className={`${
                  service.name === 'Data Storage'
                    ? 'data-storage-card'
                    : service.name.toLowerCase().includes('etablis')
                      ? 'establishments-card'
                      : ''
                } ${isDisabled ? 'opacity-60' : 'hover:bg-muted/50'} transition-colors relative`}
                data-service={service.name.toLowerCase()}
              >
                {service.soon && (
                  <span className="absolute top-0 right-0 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-bl-md rounded-tr-md z-10">
                    SOON
                  </span>
                )}
                {isDisabled && !service.soon && (
                  <span className="absolute top-0 right-0 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-bl-md rounded-tr-md z-10">
                    LOCKED
                  </span>
                )}
                <CardContent className="p-4">
                  {isDisabled ? (
                    <div className="flex flex-col items-center text-center space-y-2 cursor-not-allowed">
                      <div className="relative">
                        {service.icon}
                        {/* Add texture overlay for disabled services */}
                        <div
                          className="absolute inset-0 bg-gradient-to-br from-gray-200/50 to-gray-400/50"
                          style={{
                            backgroundImage:
                              "url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E\")",
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">
                        {service.name}
                      </span>
                    </div>
                  ) : (
                    <a
                      href={service.href}
                      className="flex flex-col items-center text-center space-y-2"
                    >
                      {service.icon}
                      <span className="text-sm font-medium">
                        {service.name}
                      </span>
                    </a>
                  )}
                </CardContent>
              </Card>
            )
          })}
          <Card
            className={`hover:bg-muted/50 transition-colors border-dashed border-2 border-gray-300 ${!hasCreatedEstablishment ? 'opacity-60 cursor-not-allowed' : ''}`}
          >
            <CardContent className="p-4">
              <Button
                variant="ghost"
                className="w-full h-full flex flex-col items-center space-y-2"
                disabled={!hasCreatedEstablishment}
              >
                <span className="text-2xl font-light text-gray-400">+</span>
                <span className="text-sm font-medium text-gray-500">
                  Add Service
                </span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Admin Management Section Preview */}
      <section className="admin-management-section w-full">
        <h2 className="text-2xl font-bold mb-4">Admin Management</h2>
        <Card className={!hasCreatedEstablishment ? 'opacity-60' : ''}>
          <CardContent className="p-4">
            <p className="text-center text-gray-500">
              Manage all admin users from the Data Storage section.
            </p>
            <div className="flex justify-center mt-4">
              <Button
                variant="outline"
                onClick={() => (window.location.href = '/super_admin/database')}
                disabled={!hasCreatedEstablishment}
                className={!hasCreatedEstablishment ? 'cursor-not-allowed' : ''}
              >
                Go to Admin Management
              </Button>
            </div>
            {!hasCreatedEstablishment && (
              <p className="text-center text-red-500 mt-2 text-sm">
                You must create an establishment first
              </p>
            )}
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
