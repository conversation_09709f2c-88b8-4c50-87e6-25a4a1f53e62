import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, RotateCcw } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { adminLookupService, Class, Teacher } from '@/lib/api/services/admin-lookup-service';
import { SessionStatus, SessionFilters as SessionFilterParams } from '@/lib/api/services/admin-session-service';
import { CancellationCategory, CANCELLATION_CATEGORY_LABELS } from '@/lib/constants/cancellation-types';

interface SessionFiltersProps {
  onFiltersChanged: (filters: SessionFilterParams) => void;
}

// Use React.memo to prevent unnecessary re-renders
const SessionFilters: React.FC<SessionFiltersProps> = React.memo(({ onFiltersChanged }) => {
  const [filters, setFilters] = useState<SessionFilterParams>({
    dateRange: {
      from: new Date(),
      to: new Date()
    },
    classId: 'all',
    teacherId: 'all',
    status: 'all',
    searchTerm: '',
    cancelCategory: 'all'
  });

  const [classes, setClasses] = useState<Class[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState({
    classes: false,
    teachers: false
  });

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: SessionStatus.ONGOING, label: 'Ongoing' },
    { value: SessionStatus.REPORTED, label: 'Reported' },
    { value: SessionStatus.NOT_REPORTED, label: 'Not Reported' },
    { value: SessionStatus.CANCELED, label: 'Canceled' },
    { value: SessionStatus.PENDING, label: 'Pending' }
  ];

  // We'll use this directly in the JSX instead of storing it in a variable

  useEffect(() => {
    loadClasses();
    loadTeachers();
  }, []);

  // Use a ref to store the previous filters for comparison
  const prevFiltersRef = useRef<string>(JSON.stringify(filters));

  useEffect(() => {
    // Convert current filters to string for comparison
    const filtersString = JSON.stringify(filters);

    // Only proceed if filters have actually changed
    if (prevFiltersRef.current === filtersString) {
      return;
    }

    // Log filter changes for debugging
    console.log('Filter state changed:', filters);

    // Debounce filter changes
    const timer = setTimeout(() => {
      // Ensure we're passing a complete filter object
      const completeFilters = {
        dateRange: filters.dateRange || {
          from: new Date(),
          to: new Date()
        },
        classId: filters.classId || 'all',
        teacherId: filters.teacherId || 'all',
        status: filters.status || 'all',
        searchTerm: filters.searchTerm || '',
        cancelCategory: filters.cancelCategory || 'all'
      };

      // Update the previous filters ref
      prevFiltersRef.current = JSON.stringify(completeFilters);

      console.log('Sending filters to parent:', completeFilters);
      onFiltersChanged(completeFilters);
    }, 300);

    return () => clearTimeout(timer);
  }, [filters, onFiltersChanged]);

  // Load classes from API
  const loadClasses = async (): Promise<void> => {
    setLoading(prev => ({ ...prev, classes: true }));

    try {
      const data = await adminLookupService.fetchClasses();
      setClasses(data || []);
    } catch (err) {
      console.error('Error loading classes:', err);
      // Set empty array to prevent errors
      setClasses([]);
    } finally {
      setLoading(prev => ({ ...prev, classes: false }));
    }
  };

  // Load teachers from API
  const loadTeachers = async (): Promise<void> => {
    setLoading(prev => ({ ...prev, teachers: true }));

    try {
      const data = await adminLookupService.fetchTeachers();
      setTeachers(data || []);
    } catch (err) {
      console.error('Error loading teachers:', err);
      // Set empty array to prevent errors
      setTeachers([]);
    } finally {
      setLoading(prev => ({ ...prev, teachers: false }));
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof SessionFilterParams, value: any): void => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle date range change
  const handleDateRangeChange = (field: 'from' | 'to', value: Date | undefined): void => {
    if (!value) return;

    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange!,
        [field]: value
      }
    }));
  };

  // Reset filters to default values
  const resetFilters = (): void => {
    setFilters({
      dateRange: {
        from: new Date(),
        to: new Date()
      },
      classId: 'all',
      teacherId: 'all',
      status: 'all',
      searchTerm: '',
      cancelCategory: 'all'
    });
  };

  // Quick date preset functions
  const setDatePreset = (preset: 'today' | 'week' | 'month'): void => {
    const today = new Date();
    let from: Date;
    let to: Date = new Date(today);

    switch (preset) {
      case 'today':
        from = new Date(today);
        break;
      case 'week':
        from = new Date(today);
        from.setDate(today.getDate() - 7);
        break;
      case 'month':
        from = new Date(today);
        from.setDate(today.getDate() - 30);
        break;
      default:
        from = new Date(today);
    }

    setFilters(prev => ({
      ...prev,
      dateRange: { from, to }
    }));
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          <Button
            variant="ghost"
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            onClick={resetFilters}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset Filters
          </Button>
        </div>

        <div className="space-y-4">
          {/* Date Range - Full Width Row */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium text-gray-700">Date Range</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setDatePreset('today')}
                  className="text-xs"
                >
                  Today
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setDatePreset('week')}
                  className="text-xs"
                >
                  Last 7 Days
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setDatePreset('month')}
                  className="text-xs"
                >
                  Last 30 Days
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="block text-sm font-medium text-gray-600 mb-2">From Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal h-10",
                        !filters.dateRange?.from && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.dateRange?.from ? (
                        format(filters.dateRange.from, "MMM dd, yyyy")
                      ) : (
                        <span>Select start date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filters.dateRange?.from}
                      onSelect={(date) => handleDateRangeChange('from', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label className="block text-sm font-medium text-gray-600 mb-2">To Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal h-10",
                        !filters.dateRange?.to && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.dateRange?.to ? (
                        format(filters.dateRange.to, "MMM dd, yyyy")
                      ) : (
                        <span>Select end date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filters.dateRange?.to}
                      onSelect={(date) => handleDateRangeChange('to', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Other Filters - Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

            {/* Class Filter */}
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">Class</Label>
              <Select
                value={filters.classId}
                onValueChange={(value) => handleFilterChange('classId', value)}
                disabled={loading.classes}
              >
                <SelectTrigger className="w-full h-10">
                  <SelectValue placeholder="All Classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Teacher Filter */}
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">Teacher</Label>
              <Select
                value={filters.teacherId}
                onValueChange={(value) => handleFilterChange('teacherId', value)}
                disabled={loading.teachers}
              >
                <SelectTrigger className="w-full h-10">
                  <SelectValue placeholder="All Teachers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Teachers</SelectItem>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.firstname} {teacher.lastname}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger className="w-full h-10">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            {/* <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">Search</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search sessions..."
                  className="pl-8 h-10"
                  value={filters.searchTerm}
                  onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                />
              </div>
            </div>*/}
          </div>

          {/* Cancellation Category Filter - Only visible when status is CANCELED */}
          {filters.status === SessionStatus.CANCELED && (
            <div className="mt-4">
              <Label className="block text-sm font-medium text-gray-700 mb-2">Cancellation Type</Label>
              <div className="max-w-xs">
                <Select
                  value={filters.cancelCategory}
                  onValueChange={(value) => handleFilterChange('cancelCategory', value)}
                >
                  <SelectTrigger className="w-full h-10">
                    <SelectValue placeholder="All Cancellation Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cancellation Types</SelectItem>
                    {Object.values(CancellationCategory).map((category) => (
                      <SelectItem key={category} value={category}>
                        {CANCELLATION_CATEGORY_LABELS[category]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

export default SessionFilters;
