import React, { useRef, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Clock, CheckCircle, AlertCircle, XCircle } from "lucide-react";
import { Chart, registerables, ChartConfiguration, TooltipItem } from 'chart.js';
import { useAttendance } from '@/hooks/useAttendance';

// Register Chart.js components
Chart.register(...registerables);

// Define interfaces for props and state
interface StatusOverviewProps {
  dateRange?: {
    from: Date;
    to: Date
  };
  metrics?: StatusMetrics;
  loading?: boolean;
}

interface StatusMetrics {
  ongoing: number;
  reported: number;
  notReported: number;
  canceled: number;
  total: number;
}

const StatusOverview: React.FC<StatusOverviewProps> = ({ dateRange }) => {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);

  // Get the session statistics query from useAttendance
  const { useSessionStatistics } = useAttendance();

  // Fetch session statistics with React Query
  const {
    data: metrics,
    isLoading: loading,
    isError,
    error
  } = useSessionStatistics(dateRange);

  // Default metrics if data is not available
  const statusMetrics: StatusMetrics = metrics || {
    ongoing: 0,
    reported: 0,
    notReported: 0,
    canceled: 0,
    total: 0
  };

  // Log error if query fails
  if (isError && error) {
    console.error('Error loading status metrics:', error);
  }

  // Update chart when metrics change
  useEffect(() => {
    if (metrics) {
      updateChart(metrics);
    }
  }, [metrics]);

  // Update or create the chart with new data
  const updateChart = (metrics: StatusMetrics): void => {
    const ctx = chartRef.current;
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Create new chart
    const chartConfig: ChartConfiguration = {
      type: 'doughnut',
      data: {
        labels: ['Ongoing', 'Reported', 'Not Reported', 'Canceled'],
        datasets: [{
          data: [
            metrics.ongoing,
            metrics.reported,
            metrics.notReported,
            metrics.canceled
          ],
          backgroundColor: [
            '#3b82f6', // blue-500 for Ongoing
            '#10b981', // green-500 for Reported
            '#ef4444', // red-500 for Not Reported
            '#6b7280'  // gray-500 for Canceled
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              boxWidth: 12,
              padding: 15
            }
          },
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'doughnut'>) => {
                const label = context.label || '';
                const value = context.raw as number;
                const percentage = metrics.total > 0
                  ? Math.round((value / metrics.total) * 100)
                  : 0;
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        // @ts-ignore - cutout is a valid option for doughnut charts
        cutout: '70%'
      }
    };

    chartInstance.current = new Chart(ctx, chartConfig);
  };

  // Calculate percentage for a metric
  const getPercentage = (value: number): number => {
    if (statusMetrics.total === 0) return 0;
    return Math.round((value / statusMetrics.total) * 100);
  };

  return (
    <div className="mb-6">
      {/* Status Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Ongoing Sessions Card */}
        <Card className="border-l-4 border-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Ongoing</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-blue-600">{statusMetrics.ongoing}</p>
                  <p className="ml-2 text-sm text-gray-500">{getPercentage(statusMetrics.ongoing)}%</p>
                </div>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reported Sessions Card */}
        <Card className="border-l-4 border-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Reported</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-green-600">{statusMetrics.reported}</p>
                  <p className="ml-2 text-sm text-gray-500">{getPercentage(statusMetrics.reported)}%</p>
                </div>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Not Reported Sessions Card */}
        <Card className="border-l-4 border-red-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Not Reported</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-red-600">{statusMetrics.notReported}</p>
                  <p className="ml-2 text-sm text-gray-500">{getPercentage(statusMetrics.notReported)}%</p>
                </div>
              </div>
              <div className="p-3 rounded-full bg-red-100">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Canceled Sessions Card */}
        <Card className="border-l-4 border-gray-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Canceled</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-600">{statusMetrics.canceled}</p>
                  <p className="ml-2 text-sm text-gray-500">{getPercentage(statusMetrics.canceled)}%</p>
                </div>
              </div>
              <div className="p-3 rounded-full bg-gray-100">
                <XCircle className="h-6 w-6 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Distribution Chart */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Status Distribution</h3>
            <div className="text-sm text-gray-500">
              Total: {statusMetrics.total} sessions
            </div>
          </div>

          <div className="relative h-64">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <canvas ref={chartRef}></canvas>
            )}
          </div>

          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
              <span className="text-sm text-gray-600">Ongoing</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
              <span className="text-sm text-gray-600">Reported</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
              <span className="text-sm text-gray-600">Not Reported</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-gray-500 mr-2"></div>
              <span className="text-sm text-gray-600">Canceled</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StatusOverview;
