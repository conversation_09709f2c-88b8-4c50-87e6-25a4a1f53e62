import React, { useState } from 'react';
import { format } from 'date-fns';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAttendance } from '@/hooks/useAttendance';

interface SessionGeneratorProps {
  onSessionsGenerated?: () => void;
}

interface FormData {
  startDate: Date;
  endDate: Date;
  teacherId: string;
  timetableId?: string;
}

const SessionGenerator: React.FC<SessionGeneratorProps> = ({ onSessionsGenerated }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 14)), // Default to 2 weeks
    teacherId: 'all',
    timetableId: undefined // Will be fetched or set to undefined for current active timetable
  });

  // Get hooks from useAttendance
  const { useTeachers, useGenerateSessions } = useAttendance();

  // Fetch teachers with React Query
  const {
    data: teachers = [],
    isLoading: isLoadingTeachers,
    isError: isTeachersError,
    error: teachersError
  } = useTeachers();

  // Show error toast if teachers fetch fails
  if (isTeachersError && teachersError) {
    console.error('Error loading teachers:', teachersError);
    toast({
      title: "Error",
      description: "Failed to load teachers. Please try again.",
      variant: "destructive",
    });
  }

  // Session generation mutation
  const generateSessionsMutation = useGenerateSessions();
  const generateSessions = generateSessionsMutation.mutate;
  const isGenerating = generateSessionsMutation.isPending;

  // Handle date changes
  const handleDateChange = (field: 'startDate' | 'endDate', value: Date | undefined): void => {
    if (!value) return;

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle teacher selection
  const handleTeacherChange = (value: string): void => {
    setFormData(prev => ({
      ...prev,
      teacherId: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent): void => {
    e.preventDefault();

    // Validate dates
    if (!formData.startDate || !formData.endDate) {
      toast({
        title: "Validation Error",
        description: "Please select both start and end dates.",
        variant: "destructive",
      });
      return;
    }

    // Validate date range
    if (formData.endDate < formData.startDate) {
      toast({
        title: "Validation Error",
        description: "End date cannot be before start date.",
        variant: "destructive",
      });
      return;
    }

    // Format dates for API
    const startDate = format(formData.startDate, 'yyyy-MM-dd');
    const endDate = format(formData.endDate, 'yyyy-MM-dd');

    // Prepare data for the mutation
    const data = {
      startDate,
      endDate,
      timetableId: formData.timetableId,
      teacherId: formData.teacherId === 'all' ? undefined : formData.teacherId
    };

    // Call the mutation
    generateSessions(data, {
      onSuccess: (result) => {
        // Show success message
        toast({
          title: "Sessions Generated",
          description: `Successfully generated ${result.length || 0} sessions.`,
        });

        // Call the callback if provided
        if (typeof onSessionsGenerated === 'function') {
          onSessionsGenerated();
        }
      },
      onError: (error: any) => {
        console.error('Error generating sessions:', error);
        // Log more detailed error information
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }

        toast({
          title: "Generation Failed",
          description: error.message || "Failed to generate sessions. Please try again.",
          variant: "destructive",
        });
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Generate Sessions</CardTitle>
        <CardDescription>
          Create attendance sessions from the timetable for a specific date range
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Date Range Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="startDate"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.startDate ? (
                      format(formData.startDate, 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.startDate}
                    onSelect={(date) => handleDateChange('startDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="endDate"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.endDate ? (
                      format(formData.endDate, 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.endDate}
                    onSelect={(date) => handleDateChange('endDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Teacher Selection */}
          <div className="space-y-2">
            <Label htmlFor="teacher">Teacher</Label>
            <Select
              value={formData.teacherId}
              onValueChange={handleTeacherChange}
              disabled={isLoadingTeachers}
            >
              <SelectTrigger id="teacher">
                <SelectValue placeholder="Select a teacher" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Teachers</SelectItem>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    {teacher.firstname} {teacher.lastname}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating Sessions...
              </>
            ) : (
              "Generate Sessions"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default SessionGenerator;
