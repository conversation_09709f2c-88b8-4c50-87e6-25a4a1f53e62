import React, { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from 'date-fns';
import { CalendarIcon, Download, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { adminLookupService } from '@/lib/api/services/admin-lookup-service';
import { adminSessionService } from '@/lib/api/services/admin-session-service';

// Define interfaces for the component
interface Class {
  id: string;
  name: string;
}

interface Teacher {
  id: string;
  name: string;
}

interface DateRange {
  from: Date;
  to: Date;
}

interface ReportParams {
  startDate: string;
  endDate: string;
  format: string;
  type: string;
  classId?: string;
  teacherId?: string;
}

type ReportType = 'class' | 'teacher' | 'summary';
type ReportFormat = 'pdf' | 'excel' | 'csv';

const Reports: React.FC = () => {
  const { toast } = useToast();
  const [, setLoading] = useState<boolean>(false);
  const [generating, setGenerating] = useState<boolean>(false);
  const [classes, setClasses] = useState<Class[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [activeTab, setActiveTab] = useState<ReportType>("class");
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(),
    to: new Date(new Date().setDate(new Date().getDate() + 7))
  });
  const [selectedClass, setSelectedClass] = useState<string>("");
  const [selectedTeacher, setSelectedTeacher] = useState<string>("");
  const [reportFormat, setReportFormat] = useState<ReportFormat>("pdf");

  // Load classes and teachers on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Load data from API
  const loadData = async (): Promise<void> => {
    setLoading(true);
    try {
      const [classesData, teachersData] = await Promise.all([
        adminLookupService.fetchClasses(),
        adminLookupService.fetchTeachers()
      ]);
      setClasses(classesData);
      setTeachers(
        teachersData.map((teacher: any) => ({
          id: teacher.id,
          name: teacher.name ?? teacher.fullName ?? ""
        }))
      );
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to load data. Please try again.",
        variant: "destructive",
      });
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle report generation
  const generateReport = async (): Promise<void> => {
    // Validate inputs
    if (activeTab === "class" && !selectedClass) {
      toast({
        title: "Missing information",
        description: "Please select a class.",
        variant: "destructive",
      });
      return;
    }

    if (activeTab === "teacher" && !selectedTeacher) {
      toast({
        title: "Missing information",
        description: "Please select a teacher.",
        variant: "destructive",
      });
      return;
    }

    setGenerating(true);
    try {
      // Call the API to generate the report
      const reportParams: ReportParams = {
        startDate: format(dateRange.from, 'yyyy-MM-dd'),
        endDate: format(dateRange.to, 'yyyy-MM-dd'),
        format: reportFormat,
        type: activeTab,
        ...(activeTab === 'class' && { classId: selectedClass }),
        ...(activeTab === 'teacher' && { teacherId: selectedTeacher })
      };

      const reportUrl = await (adminSessionService as any).generateAttendanceReport(reportParams);

      // Open the report in a new window or download it
      if (reportUrl) {
        window.open(reportUrl, '_blank');
      }

      toast({
        title: "Success",
        description: `Report generated successfully in ${reportFormat.toUpperCase()} format.`,
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to generate report. Please try again.",
        variant: "destructive",
      });
      console.error('Error generating report:', err);
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Attendance Reports</h1>

      <Card>
        <CardHeader>
          <CardTitle>Generate Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ReportType)}>
            <TabsList className="mb-4">
              <TabsTrigger value="class">By Class</TabsTrigger>
              <TabsTrigger value="teacher">By Teacher</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            <TabsContent value="class">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="class">Select Class</Label>
                  <Select value={selectedClass} onValueChange={setSelectedClass}>
                    <SelectTrigger id="class">
                      <SelectValue placeholder="Select a class" />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map((cls) => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="teacher">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="teacher">Select Teacher</Label>
                  <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
                    <SelectTrigger id="teacher">
                      <SelectValue placeholder="Select a teacher" />
                    </SelectTrigger>
                    <SelectContent>
                      {teachers.map((teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="summary">
              <div className="text-center py-4">
                <p>Generate a summary report for all classes and teachers.</p>
              </div>
            </TabsContent>

            <div className="space-y-4 mt-6">
              <div>
                <Label>Date Range</Label>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full sm:w-[200px] justify-start text-left font-normal"
                        type="button"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.from ? format(dateRange.from, 'PPP') : <span>Start date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateRange.from}
                        onSelect={(date) => date && setDateRange({ ...dateRange, from: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full sm:w-[200px] justify-start text-left font-normal"
                        type="button"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.to ? format(dateRange.to, 'PPP') : <span>End date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateRange.to}
                        onSelect={(date) => date && setDateRange({ ...dateRange, to: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div>
                <Label htmlFor="format">Report Format</Label>
                <Select 
                  value={reportFormat} 
                  onValueChange={(value) => setReportFormat(value as ReportFormat)}
                >
                  <SelectTrigger id="format">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                onClick={generateReport}
                className="w-full"
                disabled={generating}
                type="button"
              >
                {generating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Report...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;
