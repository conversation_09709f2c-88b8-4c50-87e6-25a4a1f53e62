import React, { useState, FormEvent } from 'react';
import { format } from 'date-fns';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2, CheckCircle, Info, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAttendance } from '@/hooks/useAttendance';

// Define interfaces for the component

interface Timetable {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface FormData {
  startDate: Date;
  endDate: Date;
  timetableId: string | null;
}

interface LoadingState {
  timetables: boolean;
  generation: boolean;
}

interface GenerationResult {
  count: number;
  startDate: Date;
  endDate: Date;
  timetableId: string | null;
  timetableName: string;
}

const GenerateSessionsView: React.FC = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 14)), // Default to 2 weeks
    timetableId: null // Will be set to the active timetable by default
  });
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [activeTimetable, setActiveTimetable] = useState<Timetable | null>(null);

  // Get hooks from useAttendance
  const { useTimetables, useGenerateSessions } = useAttendance();

  // Fetch timetables with React Query
  const {
    data: timetables = [],
    isLoading: isLoadingTimetables,
    isError: isTimetablesError,
    error: timetablesError
  } = useTimetables({
    onSuccess: (data: any) => {
      // Find active timetable
      const activeOne = data.find((t: Timetable) => t.isActive) || data[0];
      if (activeOne && !activeTimetable) {
        setActiveTimetable(activeOne);
        setFormData(prev => ({
          ...prev,
          timetableId: activeOne.id
        }));
      }
    }
  });

  // Show error toast if timetables fetch fails
  if (isTimetablesError && timetablesError) {
    console.error('Error loading timetables:', timetablesError);
    toast({
      title: "Error",
      description: "Failed to load timetables. Please try again.",
      variant: "destructive",
    });
  }

  // Create a loading state object for UI
  const loading: LoadingState = {
    timetables: isLoadingTimetables,
    generation: false // Will be set by the mutation
  };

  // Handle date changes
  const handleDateChange = (field: 'startDate' | 'endDate', value: Date | undefined): void => {
    if (value) {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle timetable selection
  const handleTimetableChange = (value: string): void => {
    setFormData(prev => ({
      ...prev,
      timetableId: value
    }));

    // Find and set the selected timetable
    const selected = timetables.find(t => t.id === value);
    if (selected) {
      setActiveTimetable(selected);
    }
  };



  // Create a mutation for generating sessions
  const generateSessionsMutation = useGenerateSessions();

  // Update loading state based on mutation status
  loading.generation = generateSessionsMutation.isPending;

  // Handle form submission
  const handleSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();

    // Reset previous generation result
    setGenerationResult(null);

    // Validate timetable
    if (!formData.timetableId) {
      toast({
        title: "Validation Error",
        description: "Please select a timetable.",
        variant: "destructive",
      });
      return;
    }

    // Validate dates
    if (!formData.startDate || !formData.endDate) {
      toast({
        title: "Validation Error",
        description: "Please select both start and end dates.",
        variant: "destructive",
      });
      return;
    }

    // Validate date range
    if (formData.endDate < formData.startDate) {
      toast({
        title: "Validation Error",
        description: "End date cannot be before start date.",
        variant: "destructive",
      });
      return;
    }

    // Format dates for API
    const startDate = format(formData.startDate, 'yyyy-MM-dd');
    const endDate = format(formData.endDate, 'yyyy-MM-dd');

    // Create the payload
    const payload = {
      startDate,
      endDate,
      timetableId: formData.timetableId,
      useTeacherNames: false
    };

    // Execute the mutation
    generateSessionsMutation.mutate(payload, {
      onSuccess: (result) => {
        // Store the generation result
        setGenerationResult({
          count: result.length || 0,
          startDate: formData.startDate,
          endDate: formData.endDate,
          timetableId: formData.timetableId,
          timetableName: activeTimetable?.name || 'Unknown Timetable'
        });
      }
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generate Sessions</CardTitle>
          <CardDescription>
            Create attendance sessions from the timetable for a specific date range
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Timetable Selection */}
            <div className="space-y-2">
              <Label htmlFor="timetable" className="text-base font-medium">Timetable Selection</Label>
              <Select
                value={formData.timetableId || ''}
                onValueChange={handleTimetableChange}
                disabled={loading.timetables}
              >
                <SelectTrigger id="timetable" className="h-12">
                  <SelectValue placeholder="Select a timetable">
                    {activeTimetable ? (
                      <div className="flex items-center">
                        <span className="font-medium">{activeTimetable.name}</span>
                        {activeTimetable.isActive && (
                          <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
                        )}
                      </div>
                    ) : (
                      "Select a timetable"
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {timetables.map((timetable) => (
                    <SelectItem key={timetable.id} value={timetable.id} className="py-3">
                      <div className="flex items-center">
                        <span className="font-medium">{timetable.name}</span>
                        {timetable.isActive && (
                          <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Timetable Info */}
            {activeTimetable && (
              <Alert className="bg-blue-50 border-blue-200 border-2">
                <Info className="h-5 w-5 text-blue-600" />
                <AlertTitle className="text-blue-800 font-bold text-base">Selected Timetable: {activeTimetable.name}</AlertTitle>
                <AlertDescription className="text-blue-700">
                  <div className="mt-2">
                    <div className="flex items-center mb-2">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300 font-medium text-sm">
                        {activeTimetable.name}
                      </Badge>
                      {activeTimetable.isActive && (
                        <Badge className="ml-2 bg-green-100 text-green-800 border-green-300">
                          Currently Active
                        </Badge>
                      )}
                    </div>
                    {activeTimetable.description && (
                      <p className="text-sm">
                        {activeTimetable.description}
                      </p>
                    )}
                    <p className="text-sm mt-2 font-medium text-blue-900">
                      Sessions will be generated using this timetable.
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Date Range Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="startDate"
                      variant="outline"
                      type="button"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.startDate ? (
                        format(formData.startDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.startDate}
                      onSelect={(date) => handleDateChange('startDate', date)}
                      initialFocus
                      fromDate={new Date()} // Optional: restrict to future dates
                      toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))} // Optional: restrict to next year
                      captionLayout="dropdown-buttons" // Improved month/year navigation
                      className="rounded-md border"
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="endDate"
                      variant="outline"
                      type="button"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.endDate ? (
                        format(formData.endDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={formData.endDate}
                      onSelect={(date) => handleDateChange('endDate', date)}
                      initialFocus
                      fromDate={formData.startDate || new Date()} // Don't allow end date before start date
                      toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
                      captionLayout="dropdown-buttons"
                      className="rounded-md border"
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>


            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={loading.generation || !formData.timetableId}
            >
              {loading.generation ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Sessions...
                </>
              ) : (
                "Generate Sessions"
              )}
            </Button>
          </form>
        </CardContent>
        {!activeTimetable && (
          <CardFooter className="bg-amber-50 border-t border-amber-200 p-5">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-6 w-6 text-amber-600 mt-0.5" />
              <div className="text-amber-800">
                <p className="font-bold text-base">No timetable selected</p>
                <p className="mt-1">Please select a timetable from the dropdown above to generate sessions.</p>
                <p className="mt-2 text-sm">The timetable contains the class schedule information needed to create attendance sessions.</p>
              </div>
            </div>
          </CardFooter>
        )}

        {/* Generation Result */}
        {generationResult && (
          <CardFooter className="bg-green-50 border-t border-green-200 flex-col items-start p-6">
            <div className="flex items-start space-x-3 w-full">
              <CheckCircle className="h-6 w-6 text-green-600 mt-0.5" />
              <div className="text-green-800 flex-1">
                <p className="font-bold text-lg mb-2">Sessions Generated Successfully</p>
                <p className="text-base">Generated {generationResult.count} sessions.</p>

                <div className="mt-4 p-3 bg-green-100 rounded-md border border-green-300">
                  <p className="font-semibold text-green-900 mb-2">Generation Details:</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="flex flex-col">
                      <span className="font-medium text-green-800">Timetable Used:</span>
                      <span className="mt-1">
                        <Badge className="bg-green-200 text-green-900 hover:bg-green-200 text-sm px-3 py-1">
                          {generationResult.timetableName}
                        </Badge>
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium text-green-800">Date Range:</span>
                      <span className="mt-1">
                        {format(generationResult.startDate, 'PP')} - {format(generationResult.endDate, 'PP')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
};

export default GenerateSessionsView;
