import React, { useState, useEffect, FormEvent } from 'react';
import { format } from 'date-fns';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useMutation } from '@tanstack/react-query';
import { useAttendance } from '@/hooks/useAttendance';

// Define interfaces for the component
interface Teacher {
  id: string;
  firstname: string;
  lastname: string;
  name?: string;
}

interface LoadingState {
  teachers: boolean;
  generation: boolean;
}

interface FormData {
  startDate: Date;
  endDate: Date;
  teacherId: string;
  timetableId: string | null;
}

interface GenerationResult {
  count: number;
  startDate: Date;
  endDate: Date;
  teacherId: string;
  teacherName: string;
}

const SessionGeneratorPage: React.FC = () => {
  const { toast } = useToast();
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    teachers: false,
    generation: false
  });
  const [formData, setFormData] = useState<FormData>({
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 14)), // Default to 2 weeks
    teacherId: 'all',
    timetableId: null // Will be fetched or set to null for current active timetable
  });
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);

  // Get hooks from useAttendance
  const { useTeachers } = useAttendance();

  // Fetch teachers with React Query
  const {
    data: teachersData = [],
    isLoading: isLoadingTeachers,
    isError: isTeachersError,
    error: teachersError
  } = useTeachers();

  // Update teachers state from query data
  useEffect(() => {
    if (teachersData.length > 0) {
      setTeachers(teachersData);
    }
  }, [teachersData]);

  // Show error toast if teachers fetch fails
  useEffect(() => {
    if (isTeachersError && teachersError) {
      console.error('Error fetching teachers data:', teachersError);
      toast({
        title: 'Error',
        description: 'Failed to load teachers. Please try again.',
        variant: 'destructive',
      });
    }
  }, [isTeachersError, teachersError, toast]);

  // Update loading state based on query
  useEffect(() => {
    setLoading(prev => ({ ...prev, teachers: isLoadingTeachers }));
  }, [isLoadingTeachers]);

  // Handle date changes
  const handleDateChange = (field: 'startDate' | 'endDate', value: Date | undefined): void => {
    if (value) {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle teacher selection
  const handleTeacherChange = (value: string): void => {
    setFormData(prev => ({
      ...prev,
      teacherId: value
    }));
  };

  // Session generation mutation
  const generateSessionsMutation = useMutation({
    mutationFn: async (data: {
      startDate: string;
      endDate: string;
      teacherId?: string;
      timetableId?: string | null;
    }) => {
      // Import the service directly for this mutation
      const { sessionGeneratorService } = await import('@/lib/api/services/session-generator-service');

      // Call the appropriate method based on teacherId
      if (data.teacherId) {
        return sessionGeneratorService.generateForTeacher({
          teacherId: data.teacherId,
          startDate: data.startDate,
          endDate: data.endDate,
          timetableId: data.timetableId || undefined
        });
      } else {
        return sessionGeneratorService.generateForAllTeachers({
          startDate: data.startDate,
          endDate: data.endDate,
          timetableId: data.timetableId || undefined
        });
      }
    },
    onSuccess: (result) => {
      // Find teacher for the result
      const selectedTeacher = teachers.find(t => t.id === formData.teacherId);
      const teacherName = formData.teacherId === 'all'
        ? 'All Teachers'
        : selectedTeacher
          ? `${selectedTeacher.firstname} ${selectedTeacher.lastname}`
          : 'Unknown Teacher';

      // Store the generation result
      setGenerationResult({
        count: result.length || 0,
        startDate: formData.startDate,
        endDate: formData.endDate,
        teacherId: formData.teacherId,
        teacherName
      });

      // Show success message
      toast({
        title: "Sessions Generated",
        description: `Successfully generated ${result.length || 0} sessions.`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error('Error generating sessions:', error);
      toast({
        title: "Generation Failed",
        description: error.response?.data?.message || error.message || "Failed to generate sessions. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Update loading state based on mutation status
  useEffect(() => {
    setLoading(prev => ({ ...prev, generation: generateSessionsMutation.isPending }));
  }, [generateSessionsMutation.isPending]);

  // Handle form submission
  const handleSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();

    // Reset previous generation result
    setGenerationResult(null);

    // Validate dates
    if (!formData.startDate || !formData.endDate) {
      toast({
        title: "Validation Error",
        description: "Please select both start and end dates.",
        variant: "destructive",
      });
      return;
    }

    // Validate date range
    if (formData.endDate < formData.startDate) {
      toast({
        title: "Validation Error",
        description: "End date cannot be before start date.",
        variant: "destructive",
      });
      return;
    }

    // Format dates for API
    const startDate = format(formData.startDate, 'yyyy-MM-dd');
    const endDate = format(formData.endDate, 'yyyy-MM-dd');

    // Prepare data for the mutation
    const data = {
      startDate,
      endDate,
      timetableId: formData.timetableId,
      teacherId: formData.teacherId === 'all' ? undefined : formData.teacherId
    };

    // Call the mutation
    generateSessionsMutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generate Sessions</CardTitle>
          <CardDescription>
            Create attendance sessions from the timetable for a specific date range
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Date Range Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="startDate"
                      variant="outline"
                      type="button"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.startDate ? (
                        format(formData.startDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.startDate}
                      onSelect={(date) => handleDateChange('startDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="endDate"
                      variant="outline"
                      type="button"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.endDate ? (
                        format(formData.endDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.endDate}
                      onSelect={(date) => handleDateChange('endDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Teacher Selection */}
            <div className="space-y-2">
              <Label htmlFor="teacher">Teacher</Label>
              <Select
                value={formData.teacherId}
                onValueChange={handleTeacherChange}
                disabled={loading.teachers}
              >
                <SelectTrigger id="teacher">
                  <SelectValue placeholder="Select a teacher" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Teachers</SelectItem>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.firstname} {teacher.lastname}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={loading.generation}
            >
              {loading.generation ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Sessions...
                </>
              ) : (
                "Generate Sessions"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Generation Result */}
      {generationResult && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center text-green-800">
              <CheckCircle className="mr-2 h-5 w-5" />
              Generation Successful
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-green-800">
              <p><strong>Sessions Generated:</strong> {generationResult.count}</p>
              <p><strong>Date Range:</strong> {format(generationResult.startDate, 'PPP')} to {format(generationResult.endDate, 'PPP')}</p>
              <p><strong>Teacher:</strong> {generationResult.teacherName}</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SessionGeneratorPage;
