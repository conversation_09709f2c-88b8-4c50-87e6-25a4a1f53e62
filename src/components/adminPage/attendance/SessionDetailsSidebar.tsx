import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetClose
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { parseTimeSlot, formatTime } from '@/lib/utils/time-utils';
import {
  adminSessionService,
  Session,
  SessionStatus,
  AttendanceSummary,
  TeacherAbsenceRequest
} from '@/lib/api/services/admin-session-service';
import {
  CancellationCategory,
  getCancellationCategoryLabel,
  getCancellationCategoryColor,
  isTeacherAbsence,
  isSchoolHoliday
} from '@/lib/constants/cancellation-types';
import CancellationDialog from './CancellationDialog';
import { AlertTriangle, Calendar, Cloud, ClipboardList, HelpCircle, UserX } from 'lucide-react';

// No need for an extended interface, we'll use type casting instead

interface SessionDetailsSidebarProps {
  session: Session | null;
  open: boolean;
  onClose: () => void;
  onSessionUpdated: () => void;
}

interface LoadingState {
  updateStatus: boolean;
  markAbsent: boolean;
  cancelSession: boolean;
  saveNotes: boolean;
  attendanceSummary: boolean;
}

const SessionDetailsSidebar: React.FC<SessionDetailsSidebarProps> = ({
  session,
  open,
  onClose,
  onSessionUpdated
}) => {
  const { toast } = useToast();
  const [notes, setNotes] = useState<string>('');
  const [loading, setLoading] = useState<LoadingState>({
    updateStatus: false,
    markAbsent: false,
    cancelSession: false,
    saveNotes: false,
    attendanceSummary: false
  });
  const [showCancellationDialog, setShowCancellationDialog] = useState<boolean>(false);
  const [attendanceSummary, setAttendanceSummary] = useState<AttendanceSummary>({
    present: 0,
    absent: 0,
    late: 0,
    leftEarly: 0,
    total: 0
  });

  const statusOptions = [
    { value: SessionStatus.ONGOING, label: 'Ongoing' },
    { value: SessionStatus.REPORTED, label: 'Reported' },
    { value: SessionStatus.NOT_REPORTED, label: 'Not Reported' },
    { value: SessionStatus.CANCELED, label: 'Canceled' },
    { value: SessionStatus.PENDING, label: 'Pending' }
  ];

  // Update notes when session changes
  useEffect(() => {
    if (session) {
      setNotes(session.notes || '');
      loadAttendanceSummary();
    }
  }, [session]);

  // Load attendance summary for the session
  const loadAttendanceSummary = async (): Promise<void> => {
    if (!session) return;

    setLoading(prev => ({ ...prev, attendanceSummary: true }));

    try {
      const summary = await adminSessionService.fetchSessionAttendanceSummary(session.id);
      setAttendanceSummary(summary);
    } catch (err) {
      console.error('Error loading attendance summary:', err);
      // Use session data if available as fallback
      if (session.studentCount !== undefined) {
        setAttendanceSummary({
          present: session.presentCount || 0,
          absent: session.absentCount || 0,
          late: session.lateCount || 0,
          leftEarly: session.leftEarlyCount || 0,
          total: session.studentCount || 0
        });
      }
    } finally {
      setLoading(prev => ({ ...prev, attendanceSummary: false }));
    }
  };

  // Update session status
  const handleUpdateStatus = async (status: SessionStatus): Promise<void> => {
    if (!session) return;

    setLoading(prev => ({ ...prev, updateStatus: true }));

    try {
      await adminSessionService.updateSessionStatus(session.id, status);
      toast({
        title: "Status updated",
        description: `Session status updated to ${status}`,
      });
      onSessionUpdated();
    } catch (err) {
      toast({
        title: "Update failed",
        description: "Failed to update session status",
        variant: "destructive",
      });
      console.error('Error updating session status:', err);
    } finally {
      setLoading(prev => ({ ...prev, updateStatus: false }));
    }
  };

  // Mark teacher as absent
  const handleMarkTeacherAbsent = async (): Promise<void> => {
    if (!session) return;

    // In a real implementation, we would use a modal dialog
    const reason = prompt('Please enter a reason for marking the teacher as absent:');
    if (!reason) return;

    setLoading(prev => ({ ...prev, markAbsent: true }));

    const request: TeacherAbsenceRequest = {
      reason: reason,
      // Optional fields
      substituteTeacherId: undefined,
      substituteTeacherName: undefined
    };

    try {
      await adminSessionService.markTeacherAbsent(session.id, request);
      toast({
        title: "Teacher marked absent",
        description: "The teacher has been marked as absent for this session",
      });
      onSessionUpdated();
    } catch (err) {
      toast({
        title: "Operation failed",
        description: "Failed to mark teacher as absent",
        variant: "destructive",
      });
      console.error('Error marking teacher as absent:', err);
    } finally {
      setLoading(prev => ({ ...prev, markAbsent: false }));
    }
  };

  // Open cancellation dialog
  const handleCancelSession = (e: React.MouseEvent): void => {
    e.preventDefault();
    e.stopPropagation();
    if (!session) return;
    setShowCancellationDialog(true);
  };

  // Process session cancellation
  const processCancellation = async (reason: string, category: CancellationCategory): Promise<void> => {
    if (!session) return;

    setLoading(prev => ({ ...prev, cancelSession: true }));

    try {
      // Log the session ID for debugging
      console.log(`Attempting to cancel session with ID: ${session.id}, reason: ${reason}, category: ${category}`);

      await adminSessionService.cancelSession(session.id, reason, category);

      toast({
        title: "Session canceled",
        description: "The session has been canceled successfully",
      });

      // Update the session data
      onSessionUpdated();
    } catch (err: any) {
      // Extract the most useful error message
      let errorMessage = "Failed to cancel the session";

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 404) {
        errorMessage = "The cancel endpoint was not found. This may be a backend configuration issue.";
      }

      toast({
        title: "Cancellation failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('Error canceling session:', err);
      console.error('Session ID:', session.id);

      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);
      }
    } finally {
      setLoading(prev => ({ ...prev, cancelSession: false }));
      setShowCancellationDialog(false);
    }
  };

  // Save session notes
  const handleSaveNotes = async (e: React.MouseEvent): Promise<void> => {
    e.preventDefault();
    e.stopPropagation();

    if (!session) {
      toast({
        title: "Error",
        description: "No session selected",
        variant: "destructive",
      });
      return;
    }

    // Validate notes (optional)
    if (notes === session.notes) {
      toast({
        title: "No changes",
        description: "No changes were made to the notes",
        variant: "default",
      });
      return;
    }

    setLoading(prev => ({ ...prev, saveNotes: true }));

    try {
      // Log the request for debugging
      console.log(`Saving notes for session ${session.id}:`, notes);
      console.log('Current session data:', session);

      // Call the API to update notes
      const updatedSession = await adminSessionService.updateSessionNotes(session.id, notes);

      // Log the response for debugging
      console.log('API response:', updatedSession);

      // Verify the notes were actually updated
      if (updatedSession && updatedSession.notes === notes) {
        console.log('Notes successfully updated in the response');

        // Notify parent component that session was updated
        onSessionUpdated();

        toast({
          title: "Success",
          description: "Session notes have been saved successfully",
        });
      } else {
        console.warn('Notes may not have been updated correctly:', {
          originalNotes: session.notes,
          submittedNotes: notes,
          returnedNotes: updatedSession?.notes
        });

        // Still notify parent to refresh data
        onSessionUpdated();

        toast({
          title: "Notes saved",
          description: "Session notes have been saved, but please verify they appear correctly",
          variant: "default",
        });
      }
    } catch (err: any) {
      // Extract the most useful error message
      let errorMessage = "Failed to save session notes";

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      }

      toast({
        title: "Save failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('Error saving session notes:', err);

      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);
      }
    } finally {
      setLoading(prev => ({ ...prev, saveNotes: false }));
    }
  };

  // View attendance details
  // const handleViewAttendanceDetails = (): void => {
  //   if (!session) return;

  //   // In a real implementation, this would navigate to a detailed attendance view
  //   alert('This would navigate to a detailed attendance view for this session');
  // };

  // Get status badge variant
  const getStatusBadgeVariant = (status: SessionStatus | null): string => {
    if (status === null) return "secondary";

    switch (status) {
      case SessionStatus.ONGOING:
        return "info";
      case SessionStatus.REPORTED:
        return "success";
      case SessionStatus.NOT_REPORTED:
        return "destructive";
      case SessionStatus.CANCELED:
        return "outline";
      default:
        return "secondary";
    }
  };

  // Get formatted status text
  const getStatusText = (status: SessionStatus | null): string => {
    if (status === null) return 'Not Started';
    return status.replace('_', ' ');
  };

  // Get the appropriate icon for a cancellation category
  const getCancellationCategoryIcon = (category: CancellationCategory | string | null | undefined) => {
    if (!category) return <HelpCircle className="h-5 w-5" />;

    const categoryStr = String(category);

    switch (categoryStr) {
      case CancellationCategory.TEACHER_ABSENCE:
        return <UserX className="h-5 w-5" />;
      case CancellationCategory.SCHOOL_HOLIDAY:
        return <Calendar className="h-5 w-5" />;
      case CancellationCategory.EMERGENCY:
        return <AlertTriangle className="h-5 w-5" />;
      case CancellationCategory.WEATHER:
        return <Cloud className="h-5 w-5" />;
      case CancellationCategory.ADMINISTRATIVE:
        return <ClipboardList className="h-5 w-5" />;
      case CancellationCategory.OTHER:
      default:
        return <HelpCircle className="h-5 w-5" />;
    }
  };

  if (!session) return null;

  return (
    <>
      <CancellationDialog
        open={showCancellationDialog}
        onClose={() => setShowCancellationDialog(false)}
        onConfirm={processCancellation}
        title="Cancel Session"
        description={`Please provide a reason for canceling the ${session.className} ${session.subjectName} session on ${format(new Date(session.date), 'PPP')}.`}
      />

      <Sheet open={open} onOpenChange={onClose}>
        <SheetContent className="sm:max-w-md">
          <SheetHeader>
            <SheetTitle>Session Details</SheetTitle>
            <SheetClose />
          </SheetHeader>

          <div className="py-6 overflow-y-auto h-full pb-32">
            {/* Session Info */}
            <div className="mb-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Date</Label>
                  <p className="mt-1 text-sm text-gray-900">
                    {session.date ? format(new Date(session.date), 'PPP') : ''}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Time</Label>
                  <p className="mt-1 text-sm text-gray-900">
                    {(() => {
                      // Parse timeSlot into startTime and endTime
                      const { startTime, endTime } = parseTimeSlot(session.timeSlot);

                      // Format the time display
                      if (startTime && endTime) {
                        return `${formatTime(startTime)} - ${formatTime(endTime)}`;
                      } else {
                        return session.timeSlot || '-';
                      }
                    })()}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Class</Label>
                  <p className="mt-1 text-sm text-gray-900">{session.className}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Subject</Label>
                  <p className="mt-1 text-sm text-gray-900">{session.subjectName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Teacher</Label>
                  <p className="mt-1 text-sm text-gray-900">{session.teacherName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <p className="mt-1">
                    {String(session.status) === 'CANCELED' ? (
                      <div className="flex flex-col">
                        <Badge variant="outline" className={`flex items-center gap-1 ${
                          isTeacherAbsence(session.cancelCategory)
                            ? 'bg-orange-100 text-orange-800 border-orange-400 border-2 font-medium'
                            : isSchoolHoliday(session.cancelCategory)
                              ? 'bg-blue-100 text-blue-800 border-blue-400 border-2 font-medium'
                              : session.cancelCategory
                                ? getCancellationCategoryColor(session.cancelCategory)
                                : 'bg-gray-200 text-gray-800 border border-gray-400'
                        }`}>
                          {getCancellationCategoryIcon(session.cancelCategory)}
                          <span>{getStatusText(session.status)}</span>
                        </Badge>
                        {session.cancelCategory && (
                          <Badge className={`mt-1 ${
                            isTeacherAbsence(session.cancelCategory)
                              ? 'bg-orange-100 text-orange-800 border-orange-400'
                              : isSchoolHoliday(session.cancelCategory)
                                ? 'bg-blue-100 text-blue-800 border-blue-400'
                                : session.cancelCategory
                                  ? getCancellationCategoryColor(session.cancelCategory)
                                  : 'bg-gray-200 text-gray-800 border border-gray-400'
                          }`}>
                            {getCancellationCategoryLabel(session.cancelCategory)}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <Badge variant={getStatusBadgeVariant(session.status) as any}>
                        {getStatusText(session.status)}
                      </Badge>
                    )}
                  </p>
                </div>
              </div>
            </div>

            <Separator className="my-4" />

            {/* Canceled Session Warning */}
            {String(session.status) === 'CANCELED' && (
              <div className={`mb-6 p-4 rounded-md ${
                isTeacherAbsence(session.cancelCategory)
                  ? 'bg-orange-100 text-orange-800 border-orange-400 border-2'
                  : isSchoolHoliday(session.cancelCategory)
                    ? 'bg-blue-100 text-blue-800 border-blue-400 border-2'
                    : session.cancelCategory
                      ? getCancellationCategoryColor(session.cancelCategory)
                      : 'bg-gray-100 border border-gray-300'
              }`}>
                <div className="flex items-start">
                  <div className={`flex-shrink-0 ${
                    isTeacherAbsence(session.cancelCategory)
                      ? 'text-orange-700'
                      : isSchoolHoliday(session.cancelCategory)
                        ? 'text-blue-700'
                        : 'text-gray-500'
                  }`}>
                    {session.cancelCategory ?
                      getCancellationCategoryIcon(session.cancelCategory) :
                      <HelpCircle className="h-5 w-5" />
                    }
                  </div>
                  <div className="ml-3 w-full">
                    {/* Cancellation Category Badge */}
                    <div className="flex justify-between items-center mb-2">
                      <h3 className={`text-sm font-medium ${
                        isTeacherAbsence(session.cancelCategory)
                          ? 'text-orange-800 font-semibold'
                          : isSchoolHoliday(session.cancelCategory)
                            ? 'text-blue-800 font-semibold'
                            : 'text-gray-800'
                      }`}>
                        Session Canceled
                      </h3>
                      {session.cancelCategory && (
                        <Badge className={`${
                          isTeacherAbsence(session.cancelCategory)
                            ? 'bg-orange-100 text-orange-800 border-orange-400 border'
                            : isSchoolHoliday(session.cancelCategory)
                              ? 'bg-blue-100 text-blue-800 border-blue-400 border'
                              : session.cancelCategory
                                ? getCancellationCategoryColor(session.cancelCategory)
                                : 'bg-gray-200 text-gray-800 border border-gray-400'
                        }`}>
                          {getCancellationCategoryLabel(session.cancelCategory)}
                        </Badge>
                      )}
                    </div>

                    {/* Cancellation Message */}
                    <div className="mt-2 text-sm">
                      <div className={`p-3 rounded-md ${
                        isTeacherAbsence(session.cancelCategory)
                          ? 'bg-orange-50 text-orange-800 border border-orange-200'
                          : isSchoolHoliday(session.cancelCategory)
                            ? 'bg-blue-50 text-blue-800 border border-blue-200'
                            : 'bg-gray-50 text-gray-700 border border-gray-200'
                      }`}>
                        <p className="font-medium">
                          {isTeacherAbsence(session.cancelCategory)
                            ? 'This session was canceled due to teacher absence'
                            : isSchoolHoliday(session.cancelCategory)
                              ? 'This session was canceled due to school holiday'
                              : session.cancelCategory
                                ? `This session was canceled due to ${getCancellationCategoryLabel(session.cancelCategory).toLowerCase()}`
                                : 'This session has been canceled'}
                        </p>
                        {session.cancelReason && (
                          <p className="mt-2">
                            <span className="font-medium">Reason:</span> {session.cancelReason}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

          {/* Actions */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-3">Actions</h3>

            {String(session.status) === 'CANCELED' ? (
              <div className="text-sm text-gray-500 italic mb-4">
                No actions available for canceled sessions.
              </div>
            ) : (
              <>
                {/* Update Status */}
                <div className="mb-3">
                  <div className="flex items-center">
                    <Select
                      onValueChange={(value) => handleUpdateStatus(value as SessionStatus)}
                      disabled={loading.updateStatus || String(session.status) === 'CANCELED'}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Update Status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Other Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="destructive"
                    onClick={handleMarkTeacherAbsent}
                    disabled={loading.markAbsent || String(session.status) === 'CANCELED'}
                  >
                    {loading.markAbsent ? (
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
                    ) : (
                      "Mark Teacher Absent"
                    )}
                  </Button>

                  <Button
                    variant="secondary"
                    onClick={handleCancelSession}
                    disabled={loading.cancelSession || String(session.status) === 'CANCELED'}
                    type="button"
                  >
                    {loading.cancelSession ? (
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
                    ) : (
                      "Cancel Session"
                    )}
                  </Button>

                  {/* <Button
                    variant="outline"
                    onClick={handleViewAttendanceDetails}
                    disabled={session.status === null || String(session.status) === 'CANCELED'}
                    className="col-span-2"
                  >
                    View Attendance
                  </Button> */}
                </div>
              </>
            )}
          </div>

          <Separator className="my-4" />

          {/* Attendance Summary */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-3">Attendance Summary</h3>

            {loading.attendanceSummary ? (
              <div className="flex justify-center py-4">
                <div className="animate-spin h-6 w-6 border-2 border-blue-500 rounded-full border-t-transparent"></div>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Present</p>
                    <p className="mt-1 text-lg font-semibold text-green-600">{attendanceSummary.present}</p>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-gray-500">Absent</p>
                    <p className="mt-1 text-lg font-semibold text-red-600">{attendanceSummary.absent}</p>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-gray-500">Late</p>
                    <p className="mt-1 text-lg font-semibold text-yellow-600">{attendanceSummary.late}</p>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-gray-500">Left Early</p>
                    <p className="mt-1 text-lg font-semibold text-blue-600">{attendanceSummary.leftEarly}</p>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex justify-between">
                    <p className="text-xs font-medium text-gray-500">Total Students</p>
                    <p className="text-sm font-semibold text-gray-900">{attendanceSummary.total}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator className="my-4" />

          {/* Notes */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">Notes</h3>

            <div className="mb-3">
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
                placeholder="Add notes about this session..."
                className="resize-none"
              />
            </div>

            <div className="flex justify-end">
              <Button
                onClick={handleSaveNotes}
                disabled={loading.saveNotes || notes === session.notes}
                type="button"
                variant={notes !== session.notes ? "default" : "outline"}
                className={notes !== session.notes ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {loading.saveNotes ? (
                  <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></div>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                )}
                {notes !== session.notes ? "Save Changes" : "No Changes"}
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
    </>
  );
};

export default SessionDetailsSidebar;
