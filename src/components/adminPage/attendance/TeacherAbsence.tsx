import React, { useState, ChangeEvent, FormEvent } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAttendance } from '@/hooks/useAttendance';
import { TeacherAbsenceRecordRequest } from '@/lib/api/services/admin-session-service';

const TeacherAbsence: React.FC = () => {
  const { toast } = useToast();
  const { useTeachers, useRecordTeacherAbsence } = useAttendance();

  // State
  const [selectedTeacher, setSelectedTeacher] = useState<string>('');
  const [date, setDate] = useState<Date>(new Date());
  const [reason, setReason] = useState<string>('');
  const [cancelAllDaySessions, setCancelAllDaySessions] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Queries and mutations
  const {
    data: teachers = [],
    isLoading: loading,
    isError,
    error
  } = useTeachers();

  const recordAbsenceMutation = useRecordTeacherAbsence();

  // Show error toast if teacher data fetch fails
  if (isError && error) {
    toast({
      title: "Error",
      description: "Failed to load teachers. Please try again.",
      variant: "destructive",
    });
    console.error('Error loading teachers:', error);
  }

  // Filter teachers based on search term
  const filteredTeachers = teachers.filter(teacher => {
    // Create a full name from firstname and lastname
    const fullName = teacher.fullName || `${teacher.firstname} ${teacher.lastname}`;
    return fullName.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Handle search input change
  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setSearchTerm(e.target.value);
  };

  // Handle reason input change
  const handleReasonChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setReason(e.target.value);
  };

  // Handle form submission
  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    if (!selectedTeacher) {
      toast({
        title: "Missing information",
        description: "Please select a teacher.",
        variant: "destructive",
      });
      return;
    }

    if (!reason) {
      toast({
        title: "Missing information",
        description: "Please provide a reason for the absence.",
        variant: "destructive",
      });
      return;
    }

    // Create the request object
    const request: TeacherAbsenceRecordRequest = {
      teacherId: selectedTeacher,
      date: format(date, 'yyyy-MM-dd'),
      reason: reason,
      cancelAllSessions: cancelAllDaySessions
    };

    // Use the mutation to record the absence
    recordAbsenceMutation.mutate(request, {
      onSuccess: () => {
        // Reset form on success
        setSelectedTeacher('');
        setReason('');
        setCancelAllDaySessions(true);
      }
    });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Teacher Absence Management</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Teacher Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Teacher</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="search">Search Teachers</Label>
                <Input
                  id="search"
                  placeholder="Search by name..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>

              <div className="h-[300px] overflow-y-auto border rounded-md">
                {loading ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredTeachers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No teachers found.
                  </div>
                ) : (
                  <div className="p-2 space-y-2">
                    {filteredTeachers.map((teacher) => (
                      <div
                        key={teacher.id}
                        className={cn(
                          "p-2 rounded-md cursor-pointer hover:bg-gray-100",
                          selectedTeacher === teacher.id && "bg-primary/10"
                        )}
                        onClick={() => setSelectedTeacher(teacher.id)}
                      >
                        <div className="font-medium">{`${teacher.firstname} ${teacher.lastname}`}</div>
                        <div className="text-sm text-muted-foreground">
                          {teacher.email}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Absence Details */}
        <Card>
          <CardHeader>
            <CardTitle>Absence Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="date">Absence Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      type="button"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={(newDate) => newDate && setDate(newDate)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label htmlFor="reason">Reason for Absence</Label>
                <Input
                  id="reason"
                  placeholder="Enter reason..."
                  value={reason}
                  onChange={handleReasonChange}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="cancelAll"
                  checked={cancelAllDaySessions}
                  onCheckedChange={(checked) => setCancelAllDaySessions(!!checked)}
                />
                <Label htmlFor="cancelAll">Cancel all sessions for this day</Label>
              </div>

              <Button type="submit" className="w-full" disabled={recordAbsenceMutation.isPending}>
                {recordAbsenceMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Recording Absence...
                  </>
                ) : (
                  "Record Absence"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TeacherAbsence;
