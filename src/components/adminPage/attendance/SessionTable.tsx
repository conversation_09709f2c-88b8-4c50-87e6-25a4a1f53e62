import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ChevronDown,
  ChevronUp,
  AlertCircle,
  UserX,
  Calendar,
  Cloud,
  ClipboardList,
  HelpCircle
} from "lucide-react";
import { format } from "date-fns";
import {
  CancellationCategory,
  getCancellationCategoryLabel,
  getCancellationCategoryColor,
  isTeacherAbsence,
  isSchoolHoliday
} from '@/lib/constants/cancellation-types';
import { compareTime } from '@/lib/utils/time-utils';

// Define interfaces for the component
export interface Session {
  id: string;
  date: string;
  startTime?: string;
  endTime?: string;
  timeSlot?: string;
  className: string;
  subjectName: string;
  teacherName: string;
  status: SessionStatus | null;
  cancelReason?: string;
  cancelCategory?: CancellationCategory | string;
  [key: string]: any; // For other properties
}

export type SessionStatus = 'ONGOING' | 'REPORTED' | 'NOT_REPORTED' | 'CANCELED';

interface SessionTableProps {
  sessions: Session[];
  selectedSessions: Set<string> | string[];
  loading: boolean;
  onSessionSelected?: (sessionId: string) => void;
  onAllSessionsSelected?: (checked: boolean) => void;
  onSessionDetailsRequested?: (session: Session) => void;
}

type SortDirection = 'asc' | 'desc';
type SortColumn = keyof Session;

const SessionTable: React.FC<SessionTableProps> = ({
  sessions,
  selectedSessions: selectedSessionsProp,
  loading,
  onSessionSelected = () => {},
  onAllSessionsSelected = () => {},
  onSessionDetailsRequested = () => {}
}) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortColumn, setSortColumn] = useState<SortColumn>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [displayedSessions, setDisplayedSessions] = useState<Session[]>([]);
  const [allSelected, setAllSelected] = useState<boolean>(false);

  // Convert selectedSessions to Set if it's an array
  const selectedSessions = selectedSessionsProp instanceof Set
    ? selectedSessionsProp
    : new Set(selectedSessionsProp);

  // Update displayed sessions when sessions, pagination, or sorting changes
  useEffect(() => {
    updateDisplayedSessions();
  }, [sessions, currentPage, pageSize, sortColumn, sortDirection]);

  // Update select all checkbox state when selected sessions change
  useEffect(() => {
    updateSelectAllState();
  }, [selectedSessions, displayedSessions]);

  // Custom sorting function for better date/time handling
  const customSort = (a: Session, b: Session, column: SortColumn, direction: SortDirection): number => {
    let aValue = a[column];
    let bValue = b[column];

    // Handle null values
    if (aValue === null) aValue = '';
    if (bValue === null) bValue = '';

    // Special handling for date sorting
    if (column === 'date') {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      if (dateA.getTime() !== dateB.getTime()) {
        const result = dateA.getTime() - dateB.getTime();
        return direction === 'asc' ? result : -result;
      }

      // If dates are the same, sort by start time as secondary sort
      if (a.startTime && b.startTime) {
        const timeResult = compareTime(a.startTime, b.startTime);
        return direction === 'asc' ? timeResult : -timeResult;
      }
    }

    // Special handling for time sorting
    if (column === 'startTime' && a.startTime && b.startTime) {
      const result = compareTime(a.startTime, b.startTime);
      return direction === 'asc' ? result : -result;
    }

    // Default string comparison for other columns
    const result = String(aValue).localeCompare(String(bValue));
    return direction === 'asc' ? result : -result;
  };

  // Update the displayed sessions based on pagination and sorting
  const updateDisplayedSessions = (): void => {
    // Apply custom sorting
    const sortedSessions = [...sessions].sort((a, b) =>
      customSort(a, b, sortColumn, sortDirection)
    );

    // Apply pagination
    const startIndex = (currentPage - 1) * pageSize;
    setDisplayedSessions(sortedSessions.slice(startIndex, startIndex + pageSize));
  };

  // Update the select all checkbox state
  const updateSelectAllState = (): void => {
    if (displayedSessions.length === 0) {
      setAllSelected(false);
      return;
    }

    setAllSelected(displayedSessions.every(session =>
      selectedSessions.has(session.id)
    ));
  };

  // Toggle selection of a session
  const toggleSelection = (sessionId: string, event: { stopPropagation: () => void }): void => {
    event.stopPropagation();
    onSessionSelected(sessionId);
  };

  // Toggle selection of all sessions
  const toggleSelectAll = (checked: boolean): void => {
    onAllSessionsSelected(checked);
  };

  // Open session details
  const openSessionDetails = (session: Session): void => {
    onSessionDetailsRequested(session);
  };

  // Change page
  const changePage = (page: number): void => {
    setCurrentPage(page);
  };

  // Change page size
  const changePageSize = (size: string): void => {
    setPageSize(parseInt(size));
    setCurrentPage(1); // Reset to first page
  };

  // Sort by column
  const sortBy = (column: SortColumn): void => {
    if (sortColumn === column) {
      // Toggle direction if same column
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to ascending for new column
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: SessionStatus | null): string => {
    if (status === null) return "secondary";

    switch (status) {
      case 'ONGOING':
        return "info";
      case 'REPORTED':
        return "success";
      case 'NOT_REPORTED':
        return "destructive";
      case 'CANCELED':
        // Changed from "outline" to make it more visually distinct
        return "secondary";
      default:
        return "secondary";
    }
  };

  // Get formatted status text
  const getStatusText = (status: SessionStatus | null): string => {
    if (status === null) return 'Not Started';
    return status.replace('_', ' ');
  };

  // Check if a session is today
  const isToday = (dateStr: string): boolean => {
    const sessionDate = new Date(dateStr);
    const today = new Date();
    return sessionDate.toDateString() === today.toDateString();
  };

  // Check if a session is upcoming (today or future)
  const isUpcoming = (dateStr: string): boolean => {
    const sessionDate = new Date(dateStr);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    return sessionDate >= today;
  };

  // Get relative date display
  const getRelativeDateDisplay = (dateStr: string): string => {
    const sessionDate = new Date(dateStr);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (sessionDate.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (sessionDate.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return format(sessionDate, 'PPP');
    }
  };

  // Get the appropriate icon for a cancellation category
  const getCancellationCategoryIcon = (category: CancellationCategory | string | null | undefined) => {
    if (!category) return <HelpCircle className="h-4 w-4" />;

    const categoryStr = String(category);

    switch (categoryStr) {
      case CancellationCategory.TEACHER_ABSENCE:
        return <UserX className="h-4 w-4" />;
      case CancellationCategory.SCHOOL_HOLIDAY:
        return <Calendar className="h-4 w-4" />;
      case CancellationCategory.EMERGENCY:
        return <AlertCircle className="h-4 w-4" />;
      case CancellationCategory.WEATHER:
        return <Cloud className="h-4 w-4" />;
      case CancellationCategory.ADMINISTRATIVE:
        return <ClipboardList className="h-4 w-4" />;
      case CancellationCategory.OTHER:
      default:
        return <HelpCircle className="h-4 w-4" />;
    }
  };

  // Get page numbers for pagination
  const getPageNumbers = (): (number)[] => {
    const totalPages = Math.ceil(sessions.length / pageSize);

    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Complex pagination with ellipsis
    const pages: number[] = [];

    if (currentPage <= 4) {
      // Current page is near the start
      pages.push(1, 2, 3, 4, 5, -1, totalPages);
    } else if (currentPage >= totalPages - 3) {
      // Current page is near the end
      pages.push(1, -1, totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
    } else {
      // Current page is in the middle
      pages.push(1, -1, currentPage - 1, currentPage, currentPage + 1, -1, totalPages);
    }

    return pages;
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-10">
                <Checkbox
                  checked={allSelected}
                  onCheckedChange={toggleSelectAll}
                  disabled={loading || displayedSessions.length === 0}
                />
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('date')}>
                <div className="flex items-center">
                  <span>Date</span>
                  {sortColumn === 'date' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('startTime')}>
                <div className="flex items-center">
                  <span>Time</span>
                  {sortColumn === 'startTime' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('className')}>
                <div className="flex items-center">
                  <span>Class</span>
                  {sortColumn === 'className' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('subjectName')}>
                <div className="flex items-center">
                  <span>Subject</span>
                  {sortColumn === 'subjectName' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('teacherName')}>
                <div className="flex items-center">
                  <span>Teacher</span>
                  {sortColumn === 'teacherName' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => sortBy('status')}>
                <div className="flex items-center">
                  <span>Status</span>
                  {sortColumn === 'status' && (
                    sortDirection === 'asc' ?
                      <ChevronUp className="ml-1 h-4 w-4" /> :
                      <ChevronDown className="ml-1 h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* Loading state */}
            {loading && (
              <TableRow>
                <TableCell colSpan={8} className="h-64 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-gray-500">Loading sessions...</p>
                  </div>
                </TableCell>
              </TableRow>
            )}

            {/* Empty state */}
            {!loading && displayedSessions.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="h-64 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-500">No sessions found</p>
                    <p className="text-gray-400 text-sm mt-1">Try adjusting your filters</p>
                  </div>
                </TableCell>
              </TableRow>
            )}

            {/* Session rows */}
            {!loading && displayedSessions.map((session) => (
              <TableRow
                key={session.id}
                className={`hover:bg-gray-50 cursor-pointer ${
                  session.status === 'CANCELED'
                    ? `${
                        isTeacherAbsence(session.cancelCategory)
                          ? 'opacity-90 bg-orange-50 border-l-4 border-orange-400'
                          : isSchoolHoliday(session.cancelCategory)
                            ? 'opacity-90 bg-blue-50 border-l-4 border-blue-400'
                            : `opacity-80 bg-gray-50 border-l-4 ${
                                session.cancelCategory
                                  ? getCancellationCategoryColor(session.cancelCategory).split(' ')[2] || 'border-gray-400'
                                  : 'border-gray-400'
                              }`
                      }`
                    : isToday(session.date)
                      ? 'bg-blue-50 border-l-4 border-blue-500 font-medium'
                      : isUpcoming(session.date)
                        ? 'bg-green-50'
                        : 'opacity-75'
                }`}
                onClick={() => openSessionDetails(session)}
              >
                <TableCell>
                  <Checkbox
                    checked={selectedSessions.has(session.id)}
                    onCheckedChange={() => {
                      const event = { stopPropagation: () => {} };
                      toggleSelection(session.id, event);
                    }}
                    onClick={(e) => e.stopPropagation()}
                    disabled={session.status === 'CANCELED'}
                  />
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className={isToday(session.date) ? 'font-semibold text-blue-700' : ''}>
                      {session.date ? getRelativeDateDisplay(session.date) : ''}
                    </span>
                    {isToday(session.date) && (
                      <span className="text-xs text-blue-600">
                        {format(new Date(session.date), 'EEEE')}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {session.startTime && session.endTime
                    ? `${session.startTime} - ${session.endTime}`
                    : session.timeSlot || '-'}
                </TableCell>
                <TableCell>{session.className}</TableCell>
                <TableCell>{session.subjectName}</TableCell>
                <TableCell>{session.teacherName}</TableCell>
                <TableCell>
                  {session.status === 'CANCELED' ? (
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <Badge
                          variant="secondary"
                          className={`flex items-center gap-1 ${
                            isTeacherAbsence(session.cancelCategory)
                              ? 'bg-orange-100 text-orange-800 border-orange-400 border-2 font-medium'
                              : isSchoolHoliday(session.cancelCategory)
                                ? 'bg-blue-100 text-blue-800 border-blue-400 border-2 font-medium'
                                : session.cancelCategory
                                  ? getCancellationCategoryColor(session.cancelCategory)
                                  : 'bg-gray-200 text-gray-800 border border-gray-400'
                          }`}
                        >
                          {getCancellationCategoryIcon(session.cancelCategory)}
                          <span>{getStatusText(session.status)}</span>
                        </Badge>
                      </div>
                      {session.cancelCategory && (
                        <Badge
                          className={`mt-1 ${
                            isTeacherAbsence(session.cancelCategory)
                              ? 'bg-orange-100 text-orange-800 border-orange-400 border'
                              : isSchoolHoliday(session.cancelCategory)
                                ? 'bg-blue-100 text-blue-800 border-blue-400 border'
                                : session.cancelCategory
                                  ? getCancellationCategoryColor(session.cancelCategory)
                                  : 'bg-gray-200 text-gray-800 border border-gray-400'
                          }`}
                        >
                          {getCancellationCategoryLabel(session.cancelCategory)}
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <Badge variant={getStatusBadgeVariant(session.status) as any}>
                      {getStatusText(session.status)}
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant={session.status === 'CANCELED' ? "outline" : "ghost"}
                    size="sm"
                    className={session.status === 'CANCELED'
                      ? "text-gray-600 hover:text-gray-800 border-gray-300"
                      : "text-blue-600 hover:text-blue-900"
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                      openSessionDetails(session);
                    }}
                  >
                    {session.status === 'CANCELED' ? "View (Canceled)" : "Details"}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {!loading && sessions.length > 0 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => changePage(currentPage - 1)}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === Math.ceil(sessions.length / pageSize)}
              onClick={() => changePage(currentPage + 1)}
            >
              Next
            </Button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing
                <span className="font-medium mx-1">
                  {(currentPage - 1) * pageSize + 1}
                </span>
                to
                <span className="font-medium mx-1">
                  {Math.min(currentPage * pageSize, sessions.length)}
                </span>
                of
                <span className="font-medium mx-1">
                  {sessions.length}
                </span>
                results
              </p>
            </div>
            <div className="flex items-center space-x-6">
              <Select
                value={pageSize.toString()}
                onValueChange={changePageSize}
              >
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Items per page" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                </SelectContent>
              </Select>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => changePage(currentPage - 1)}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {getPageNumbers().map((page, index) => (
                    page === -1 ? (
                      <PaginationItem key={`ellipsis-${index}`}>
                        <PaginationEllipsis />
                      </PaginationItem>
                    ) : (
                      <PaginationItem key={page}>
                        <PaginationLink
                          isActive={page === currentPage}
                          onClick={() => changePage(page)}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => changePage(currentPage + 1)}
                      className={currentPage === Math.ceil(sessions.length / pageSize) ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionTable;
