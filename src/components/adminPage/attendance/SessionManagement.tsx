import React, { useState, useCallback } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, Calendar, Users, UserCheck} from "lucide-react";
// import { RefreshCw, Calendar, Users, UserCheck, Zap } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import SessionFilters from './SessionFilters';
import SessionTable, { Session as SessionTableSession } from './SessionTable';
import SessionDetailsSidebar from './SessionDetailsSidebar';
import CancellationDialog from './CancellationDialog';
import {
  adminSessionService,
  type Session,
  type SessionFilters as SessionFilterParams
} from '@/lib/api/services/admin-session-service';
import { parseTimeSlot, compareTime } from '@/lib/utils/time-utils';
import { useAttendance } from '@/hooks/useAttendance';
import { CancellationCategory } from '@/lib/constants/cancellation-types';
// import { useSessionStatusUpdates } from '@/hooks/useWebSocket';

const SessionManagement: React.FC = () => {
  const { toast } = useToast();
  const [selectedSessions, setSelectedSessions] = useState<Set<string>>(new Set());
  const [currentFilters, setCurrentFilters] = useState<SessionFilterParams>({
    dateRange: {
      from: new Date(),
      to: new Date()
    },
    classId: 'all',
    teacherId: 'all',
    status: 'all',
    searchTerm: '',
    cancelCategory: 'all'
  });
  const [selectedSession, setSelectedSession] = useState<SessionTableSession | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false);
  const [processingBatch, setProcessingBatch] = useState<boolean>(false);
  const [showCancellationDialog, setShowCancellationDialog] = useState<boolean>(false);
  // const [, setIsUpdatingStatuses] = useState<boolean>(false);

  // Get hooks from useAttendance
  const { useAdminSessions } = useAttendance();

  // Fetch sessions with React Query
  const {
    data: sessions = [],
    isLoading: loading,
    isError,
    error: queryError,
    refetch: refetchSessions
  } = useAdminSessions(currentFilters);

  // Format error message
  const error = isError ? `Failed to load sessions: ${String(queryError)}` : '';

  // WebSocket integration for real-time session status updates
  // const handleSessionStatusUpdate = useCallback((update: any) => {
  //   console.log('📊 Received session status update:', update);

  //   // Refresh sessions to get the latest data
  //   refetchSessions();

  //   // Show toast notification for status changes
  //   toast({
  //     title: "Session Status Updated",
  //     description: `${update.sessionData?.className || 'Session'} - ${update.sessionData?.subjectName || ''} status changed to ${update.newStatus}`,
  //     variant: "default",
  //   });
  // }, [refetchSessions, toast]);

  // const handleBatchStatusUpdate = useCallback((updates: any[]) => {
  //   console.log('📊 Received batch session status updates:', updates.length, 'sessions');

  //   // Refresh sessions to get the latest data
  //   refetchSessions();

  //   // Show toast notification for batch updates
  //   if (updates.length > 0) {
  //     toast({
  //       title: "Session Statuses Updated",
  //       description: `${updates.length} session(s) status updated automatically`,
  //       variant: "default",
  //     });
  //   }
  // }, [refetchSessions, toast]);

  // Use WebSocket hook for real-time updates (disabled to prevent infinite loops)
  // TODO: Re-enable when WebSocket server is properly configured
  // useSessionStatusUpdates(handleSessionStatusUpdate, handleBatchStatusUpdate);

  // Transform API session data to the format expected by SessionTable
  const transformSessionsForTable = (sessions: Session[]): SessionTableSession[] => {
    return sessions.map(session => {
      // Parse timeSlot into startTime and endTime
      const { startTime, endTime } = parseTimeSlot(session.timeSlot);

      // Convert status to uppercase for UI components
      const transformedStatus = session.status ?
        session.status.toUpperCase() as SessionTableSession['status'] :
        null;

      return {
        ...session,
        startTime,
        endTime,
        status: transformedStatus
      };
    });
  };

  // Sort sessions by date and time (closest first)
  const sortSessionsByDateTime = (sessions: SessionTableSession[]): SessionTableSession[] => {
    return [...sessions].sort((a, b) => {
      // First, sort by date
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      if (dateA.getTime() !== dateB.getTime()) {
        return dateA.getTime() - dateB.getTime();
      }

      // If dates are the same, sort by start time
      if (a.startTime && b.startTime) {
        return compareTime(a.startTime, b.startTime);
      }

      // If one doesn't have start time, use timeSlot for comparison
      if (a.timeSlot && b.timeSlot) {
        return a.timeSlot.localeCompare(b.timeSlot);
      }

      return 0;
    });
  };

  // Filtered sessions with time information added and sorted by date/time
  const filteredSessions = sortSessionsByDateTime(transformSessionsForTable(sessions));

  // Refresh sessions data
  const loadSessions = () => {
    setSelectedSessions(new Set());
    refetchSessions();
  };

  // Manual status update for all sessions
 
  // Handle filter changes - memoized to prevent infinite re-renders
  const handleFiltersChanged = useCallback((filters: SessionFilterParams): void => {
    // Use functional update to ensure we're not depending on the previous state
    setCurrentFilters(prevFilters => {
      // Only update if filters have actually changed
      if (JSON.stringify(prevFilters) === JSON.stringify(filters)) {
        return prevFilters; // Return the same reference if no changes
      }
      return filters;
    });
  }, []);

  // Toggle selection of a session
  const toggleSessionSelection = (sessionId: string): void => {
    const newSelectedSessions = new Set(selectedSessions);
    if (newSelectedSessions.has(sessionId)) {
      newSelectedSessions.delete(sessionId);
    } else {
      newSelectedSessions.add(sessionId);
    }
    setSelectedSessions(newSelectedSessions);
  };

  // Select all sessions
  const selectAllSessions = (checked: boolean): void => {
    if (checked) {
      const allIds = new Set(filteredSessions.map(session => session.id));
      setSelectedSessions(allIds);
    } else {
      setSelectedSessions(new Set());
    }
  };

  // Open session details sidebar
  const openSessionDetails = (session: SessionTableSession): void => {
    setSelectedSession(session);
    setSidebarOpen(true);
  };

  // Close session details sidebar
  const closeSidebar = (): void => {
    setSidebarOpen(false);
    setSelectedSession(null);
  };

  // Update statuses for selected sessions
  const updateSelectedSessionStatuses = async (): Promise<void> => {
    if (selectedSessions.size === 0) {
      toast({
        title: "No sessions selected",
        description: "Please select at least one session to update.",
        variant: "warning",
      });
      return;
    }

    setProcessingBatch(true);
    const sessionIds = Array.from(selectedSessions);

    try {
      // Log the session IDs for debugging
      console.log(`Attempting to update statuses for ${sessionIds.length} sessions in batch`);
      console.log('Session IDs:', sessionIds);

      const result = await adminSessionService.updateSessionStatuses(sessionIds);

      // Check if there were any failures
      if (result.failed && result.failed > 0) {
        toast({
          title: "Partial success",
          description: `Successfully updated ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions. ${result.failed} sessions could not be updated.`,
          variant: "warning",
        });

        // Log the errors for debugging
        if (result.errors && result.errors.length > 0) {
          console.error('Errors during batch status update:', result.errors);
        }
      } else {
        toast({
          title: "Sessions updated",
          description: `Successfully updated ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions.`,
          variant: "success",
        });
      }

      // Refresh the sessions data
      loadSessions();
    } catch (err: any) {
      // Extract the most useful error message
      let errorMessage = "Failed to update session statuses. Please try again.";

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 404) {
        errorMessage = "The update endpoint was not found. This may be a backend configuration issue.";
      }

      toast({
        title: "Update failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('Error updating session statuses:', err);

      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);
      }
    } finally {
      setProcessingBatch(false);
    }
  };

  // Open cancellation dialog for batch operations
  const cancelSelectedSessions = (e: React.MouseEvent): void => {
    e.preventDefault();
    e.stopPropagation();

    if (selectedSessions.size === 0) {
      toast({
        title: "No sessions selected",
        description: "Please select at least one session to cancel.",
        variant: "warning",
      });
      return;
    }

    setShowCancellationDialog(true);
  };

  // Process batch cancellation
  const processBatchCancellation = async (reason: string, category: CancellationCategory): Promise<void> => {
    setProcessingBatch(true);
    const sessionIds = Array.from(selectedSessions);

    try {
      // Log the session IDs for debugging
      console.log(`Attempting to cancel ${sessionIds.length} sessions in batch`);
      console.log('Session IDs:', sessionIds);
      console.log('Reason:', reason);
      console.log('Category:', category);

      const result = await adminSessionService.cancelSessions(sessionIds, reason, category);

      // Check if there were any failures
      if (result.failed && result.failed > 0) {
        toast({
          title: "Partial success",
          description: `Successfully canceled ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions. ${result.failed} sessions could not be canceled.`,
          variant: "warning",
        });

        // Log the errors for debugging
        if (result.errors && result.errors.length > 0) {
          console.error('Errors during batch cancellation:', result.errors);
        }
      } else {
        toast({
          title: "Sessions canceled",
          description: `Successfully canceled ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions.`,
          variant: "success",
        });
      }

      // Refresh the sessions data
      loadSessions();
    } catch (err: any) {
      // Extract the most useful error message
      let errorMessage = "Failed to cancel sessions. Please try again.";

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 404) {
        errorMessage = "The cancel endpoint was not found. This may be a backend configuration issue.";
      }

      toast({
        title: "Cancellation failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('Error canceling sessions:', err);

      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);
      }
    } finally {
      setProcessingBatch(false);
      setShowCancellationDialog(false);
    }
  };

  // Mark teachers absent for selected sessions
  const markTeachersAbsentForSelectedSessions = async (): Promise<void> => {
    if (selectedSessions.size === 0) {
      toast({
        title: "No sessions selected",
        description: "Please select at least one session to mark teacher as absent.",
        variant: "warning",
      });
      return;
    }

    // In a real implementation, we would use a modal dialog
    const reason = prompt('Please enter a reason for marking these teachers as absent:');
    if (!reason) return;

    setProcessingBatch(true);
    const sessionIds = Array.from(selectedSessions);

    try {
      // Log the session IDs for debugging
      console.log(`Attempting to mark teachers absent for ${sessionIds.length} sessions in batch`);
      console.log('Session IDs:', sessionIds);
      console.log('Reason:', reason);

      const result = await adminSessionService.markTeachersAbsent(sessionIds, reason);

      // Check if there were any failures
      if (result.failed && result.failed > 0) {
        toast({
          title: "Partial success",
          description: `Successfully marked teachers absent for ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions. ${result.failed} sessions could not be processed.`,
          variant: "warning",
        });

        // Log the errors for debugging
        if (result.errors && result.errors.length > 0) {
          console.error('Errors during batch teacher absence marking:', result.errors);
        }
      } else {
        toast({
          title: "Teachers marked absent",
          description: `Successfully marked teachers absent for ${result.updated || 0} out of ${result.processed || sessionIds.length} sessions.`,
          variant: "success",
        });
      }

      // Refresh the sessions data
      loadSessions();
    } catch (err: any) {
      // Extract the most useful error message
      let errorMessage = "Failed to mark teachers as absent. Please try again.";

      if (err.message) {
        errorMessage = err.message;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 404) {
        errorMessage = "The teacher absence endpoint was not found. This may be a backend configuration issue.";
      }

      toast({
        title: "Operation failed",
        description: errorMessage,
        variant: "destructive",
      });

      console.error('Error marking teachers absent:', err);

      if (err.response) {
        console.error('Error response status:', err.response.status);
        console.error('Error response data:', err.response.data);
      }
    } finally {
      setProcessingBatch(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Cancellation Dialog */}
      <CancellationDialog
        open={showCancellationDialog}
        onClose={() => setShowCancellationDialog(false)}
        onConfirm={processBatchCancellation}
        title="Cancel Sessions"
        description={`Please provide a reason for canceling the ${selectedSessions.size} selected session(s).`}
        isBatch={true}
      />

      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Session Management</h1>
        <div className="flex gap-2">
          {/* <Button
            variant="secondary"
            onClick={triggerManualStatusUpdate}
            disabled={loading || isUpdatingStatuses}
          >
            <Zap className="h-4 w-4 mr-2" />
            {isUpdatingStatuses ? 'Updating...' : 'Update Statuses'}
          </Button> */}
          <Button variant="outline" onClick={loadSessions} disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <SessionFilters onFiltersChanged={handleFiltersChanged} />

      {/* Batch Actions Toolbar */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">
                {selectedSessions.size} session(s) selected
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="default"
                onClick={updateSelectedSessionStatuses}
                disabled={selectedSessions.size === 0 || processingBatch}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                Update Statuses
              </Button>
              <Button
                variant="destructive"
                onClick={cancelSelectedSessions}
                disabled={selectedSessions.size === 0 || processingBatch}
                type="button"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Cancel Sessions
              </Button>
              <Button
                variant="outline"
                onClick={markTeachersAbsentForSelectedSessions}
                disabled={selectedSessions.size === 0 || processingBatch}
              >
                <Users className="h-4 w-4 mr-2" />
                Mark Teachers Absent
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-start">
          <span>{error}</span>
        </div>
      )}

      {/* Sessions Table */}
      <SessionTable
        sessions={filteredSessions}
        selectedSessions={selectedSessions}
        loading={loading}
        onSessionSelected={toggleSessionSelection}
        onAllSessionsSelected={selectAllSessions}
        onSessionDetailsRequested={openSessionDetails}
      />

      {/* Session Details Sidebar */}
      <SessionDetailsSidebar
        session={selectedSession as Session | null}
        open={sidebarOpen}
        onClose={closeSidebar}
        onSessionUpdated={loadSessions}
      />
    </div>
  );
};

export default SessionManagement;
