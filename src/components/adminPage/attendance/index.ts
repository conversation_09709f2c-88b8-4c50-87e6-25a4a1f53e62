/**
 * Attendance Module Exports
 *
 * This file serves as the main entry point for the attendance module.
 * It exports all components that should be accessible from outside the module.
 */

// Import React for type definitions

// Import components directly with explicit .tsx extensions
import AttendanceDashboard from './AttendanceDashboard.tsx';
import SessionDetailsSidebar from './SessionDetailsSidebar.tsx';
import SessionFilters from './SessionFilters.tsx';
import StatusOverview from './StatusOverview.tsx';
import SessionTable from './SessionTable.tsx';
import TeacherAbsence from './TeacherAbsence.tsx';
import Reports from './Reports.tsx';
import GenerateSessionsView from './GenerateSessionsView.tsx';
import SessionManagement from './SessionManagement.tsx';

/**
 * Type definitions for components
 * These help TypeScript understand the component interfaces
 */

// No need for type assertions anymore since all components are properly typed TypeScript components

/**
 * Export all components with their proper types
 * This provides a clean public API for the module
 */
export {
  AttendanceDashboard,
  StatusOverview,
  SessionTable,
  SessionDetailsSidebar,
  SessionFilters,
  TeacherAbsence,
  Reports,
  GenerateSessionsView as SessionGenerator,
  SessionManagement
};

// Export the main component as default
export default AttendanceDashboard;
