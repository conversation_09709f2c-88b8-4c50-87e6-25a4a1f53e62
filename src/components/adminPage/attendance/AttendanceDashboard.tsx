import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, ArrowRight } from "lucide-react";
import StatusOverview from './StatusOverview';
import { useNavigate } from '@tanstack/react-router';
import { useAttendance } from '@/hooks/useAttendance';

const AttendanceDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [dateRange] = useState<{from: Date, to: Date}>({
    from: new Date(),
    to: new Date()
  });

  // Get hooks from useAttendance
  const { useSessionStatistics, useAdminSessions } = useAttendance();

  // Fetch session statistics with React Query
  const {
    data: sessionStats = {
      total: 0,
      pending: 0,
      ongoing: 0,
      reported: 0,
      notReported: 0,
      canceled: 0
    },
    isLoading: isLoadingStats,
    isError: isStatsError,
    error: statsError,
    refetch: refetchStats
  } = useSessionStatistics(dateRange);

  // Fetch recent sessions with React Query
  const {
    data: sessionsData = [],
    isLoading: isLoadingSessions,
    isError: isSessionsError,
    error: sessionsError,
    refetch: refetchSessions
  } = useAdminSessions({ dateRange });

  // Get only the first 5 sessions for display
  const recentSessions = sessionsData.slice(0, 5);

  // Determine if there's any error
  const error = isStatsError || isSessionsError
    ? `Failed to load dashboard data: ${String(statsError || sessionsError)}`
    : '';

  // Loading state
  const loading = isLoadingStats || isLoadingSessions;

  // Refresh all data
  const refreshData = () => {
    refetchStats();
    refetchSessions();
  };

  // Navigate to session management
  const goToSessionManagement = (): void => {
    navigate({ to: '/admin/attendance', search: { component: 'SessionManagement' } });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Smart Attendance System - Dashboard</h1>
        <Button variant="outline" onClick={refreshData} disabled={loading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Overview */}
      <StatusOverview dateRange={dateRange} />

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-start">
          <span>{error}</span>
        </div>
      )}

      {/* Session Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Today's Sessions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{sessionStats?.total}</div>
            <p className="text-sm text-muted-foreground">Total sessions for today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Ongoing Sessions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{sessionStats?.ongoing}</div>
            <p className="text-sm text-muted-foreground">Currently in progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Completed Sessions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{sessionStats?.reported}</div>
            <p className="text-sm text-muted-foreground">Attendance reported</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sessions */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Sessions</CardTitle>
            <Button variant="outline" size="sm" onClick={goToSessionManagement}>
              <ArrowRight className="h-4 w-4 mr-2" />
              View All Sessions
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center p-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : recentSessions.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No recent sessions found
            </div>
          ) : (
            <div className="space-y-4">
              {recentSessions.map(session => (
                <div key={session.id} className="flex items-center justify-between border-b pb-2">
                  <div>
                    <div className="font-medium">{session.className} - {session.subjectName}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(session.date).toLocaleDateString()} • {session.timeSlot} • {session.teacherName}
                    </div>
                  </div>
                  <div>
                    <Badge variant={
                      session.status === 'reported' ? 'success' :
                      session.status === 'ongoing' ? 'default' :
                      session.status === 'not_reported' ? 'destructive' :
                      session.status === 'canceled' ? 'outline' : 'secondary'
                    }>
                      {session.status?.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceDashboard;
