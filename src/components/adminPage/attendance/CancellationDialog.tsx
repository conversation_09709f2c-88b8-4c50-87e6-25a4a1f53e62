import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { AlertTriangle, Calendar, Cloud, ClipboardList, HelpCircle, UserX } from 'lucide-react';
import { CancellationCategory, CANCELLATION_CATEGORY_LABELS } from '@/lib/constants/cancellation-types';

interface CancellationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (reason: string, category: CancellationCategory) => void;
  title?: string;
  description?: string;
  isBatch?: boolean;
}

const CancellationDialog: React.FC<CancellationDialogProps> = ({
  open,
  onClose,
  onConfirm,
  title = 'Cancel Session',
  description = 'Please provide a reason for canceling this session.',
  isBatch = false,
}) => {
  const [reason, setReason] = useState('');
  const [category, setCategory] = useState<CancellationCategory>(CancellationCategory.OTHER);

  const handleConfirm = () => {
    if (!reason.trim()) {
      alert('Please provide a reason for cancellation');
      return;
    }
    onConfirm(reason, category);
    resetForm();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setReason('');
    setCategory(CancellationCategory.OTHER);
  };

  const getCategoryIcon = (cat: CancellationCategory) => {
    switch (cat) {
      case CancellationCategory.TEACHER_ABSENCE:
        return <UserX className="h-4 w-4 mr-2" />;
      case CancellationCategory.SCHOOL_HOLIDAY:
        return <Calendar className="h-4 w-4 mr-2" />;
      case CancellationCategory.EMERGENCY:
        return <AlertTriangle className="h-4 w-4 mr-2" />;
      case CancellationCategory.WEATHER:
        return <Cloud className="h-4 w-4 mr-2" />;
      case CancellationCategory.ADMINISTRATIVE:
        return <ClipboardList className="h-4 w-4 mr-2" />;
      case CancellationCategory.OTHER:
      default:
        return <HelpCircle className="h-4 w-4 mr-2" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="cancellation-category">Cancellation Category</Label>
            <RadioGroup
              value={category}
              onValueChange={(value) => setCategory(value as CancellationCategory)}
              className="grid grid-cols-2 gap-2"
            >
              {Object.values(CancellationCategory).map((cat) => (
                <div key={cat} className="flex items-center space-x-2">
                  <RadioGroupItem value={cat} id={`category-${cat}`} />
                  <Label htmlFor={`category-${cat}`} className="flex items-center cursor-pointer">
                    {getCategoryIcon(cat)}
                    {CANCELLATION_CATEGORY_LABELS[cat]}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cancellation-reason">Reason</Label>
            <Textarea
              id="cancellation-reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Please provide a detailed reason for cancellation..."
              rows={4}
              className="resize-none"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={!reason.trim()}>
            {isBatch ? 'Cancel Sessions' : 'Cancel Session'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CancellationDialog;
