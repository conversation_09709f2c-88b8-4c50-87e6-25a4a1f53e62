import { useState } from 'react'
import AdminService from './AdminService'
import ClassManagement from './etablissment/ClassManagment'
import UserManagement from './UserManagement'
import { Button } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Link } from '@tanstack/react-router'
import TeacherManagement from './teacher/TeacherManagement'
import StudentManagement from './student/StudentManagement'
import ListEliminate from './student/ListEliminate'

export default function Header() {
  const [currentPage, setCurrentPage] = useState('home')

  const renderContent = () => {
    switch (currentPage) {
      case 'AdminService':
        return <AdminService />
      case 'ScheduleView':
      // return <ScheduleView />
      case 'UserMangment':
        return <UserManagement />
      case 'ClassManagement':
        return <ClassManagement />
      case 'TeacherManagement':
        return <TeacherManagement />
      case 'StudentManagement':
        return <StudentManagement />
      case 'ListEliminate':
        return <ListEliminate />
      case 'StudentManagement':
        return <StudentManagement />
      default:
        return (
          <div>
            <h2 className="text-2xl font-bold mb-4">
              Welcome to School Management System
            </h2>
          </div>
        )
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="bg-primary text-primary-foreground p-4">
        <div className="flex justify-between items-center">
          {/* "Jerid School" logo on the left */}
          <div className="text-[22px] font-bold font-poppins text-gray-800">
            <Link to="/" className="text-white">
              <span className="text-secondary">Jerid</span>School
            </Link>
          </div>

          {/* Centered navigation elements */}
          <div className="flex-grow flex justify-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('AdminService')}
            >
              AdminService
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('ScheduleView')}
            >
              TableView
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('ClassManagement')}
            >
              ClassManagement
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('TeacherManagement')}
            >
              TeacherManagement
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('StudentManagement')}
            >
              StudentManagement
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentPage('UserMangment')}
            >
              UserMangment
            </Button>
            <Button variant="ghost" onClick={() => setCurrentPage('addParent')}>
              addParent
            </Button>
          </div>

          {/* Logout button on the right */}
          <div>
            <Button variant="ghost" onClick={() => setCurrentPage('logout')}>
              Logout
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-grow p-4">
        <Card>
          <CardHeader>
            <CardTitle>{currentPage}</CardTitle>
          </CardHeader>
          <CardContent>{renderContent()}</CardContent>
        </Card>
      </main>
    </div>
  )
}
