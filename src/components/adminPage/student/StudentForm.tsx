import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Student, Class } from '@/interface/types'
import { ExtendedParent as Parent } from '@/interface/types/parent-extended'
import FileUploader from '@/components/shared/FileUploader'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import { Eye, EyeOff, Search, X } from 'lucide-react'
import { useState, useEffect, useRef } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { FormYearMonthPicker } from '@/components/ui/form-year-month-picker'

const studentSchema = z.object({
  cin: z.string().min(8, 'CIN must be 8 characters'),
  firstname: z.string().min(2, 'First name must be at least 2 characters'),
  lastname: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password must contain at least one special character'
    )
    .optional(),
  gradeLevel: z.string().optional(),
  enrolledDate: z.date().optional(),
  parentId: z.string().optional(),
  classId: z.string().optional(),
  birthday: z.date().optional(),
  gender: z.enum(['male', 'female'] as const),
  address: z.string().optional(),
  phone: z.string().optional(),
  avatar: z.string().url().optional(),
  isActive: z.boolean().default(true),
})

type StudentFormValues = z.infer<typeof studentSchema>
export type { StudentFormValues }

interface StudentFormProps {
  initialData?: Partial<Student>
  parents: Parent[]
  classes: Class[]
  onSubmit: (data: StudentFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
  fieldErrors?: Record<string, string>
}

/**
 * A reusable form component for creating and editing students
 * @param {StudentFormProps} props - The props for the StudentForm component
 * @returns {JSX.Element} The rendered StudentForm component
 */
export function StudentForm({
  initialData,
  parents,
  classes,
  onSubmit,
  onCancel,
  submitLabel,
  fieldErrors = {},
}: StudentFormProps) {
  // Normalize the gender value from server
  const normalizeGender = (gender: string | undefined): 'male' | 'female' => {
    if (!gender) return 'male' // Default to male if no gender is provided

    // Handle both 'Male'/'Female' and 'male'/'female' formats
    const normalizedGender = gender.toLowerCase()
    if (normalizedGender === 'male' || normalizedGender === 'female') {
      return normalizedGender as 'male' | 'female'
    }

    // Default to male for any other values
    return 'male'
  }

  const [showPassword, setShowPassword] = useState(false)
  const [isPasswordFocused, setIsPasswordFocused] = useState(false)

  // State for parent search
  const [parentSearchQuery, setParentSearchQuery] = useState('')
  const [showParentSelector, setShowParentSelector] = useState(false)
  const parentSelectorRef = useRef<HTMLDivElement>(null)

  // Handle click outside to close the parent selector
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        parentSelectorRef.current &&
        !parentSelectorRef.current.contains(event.target as Node)
      ) {
        setShowParentSelector(false)
        // Reset search query when closing the selector
        setParentSearchQuery('')
      }
    }

    // Only add the event listener when the selector is shown
    if (showParentSelector) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [parentSelectorRef, showParentSelector])

  // Filter parents based on search query
  const filteredParents = parents.filter((parent) => {
    const fullName = `${parent.firstname} ${parent.lastname}`.toLowerCase()
    const cin = parent.cin?.toLowerCase() || ''
    const query = parentSearchQuery.toLowerCase()
    return fullName.includes(query) || cin.includes(query)
  })

  const form = useForm<StudentFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      firstname: initialData?.firstname || '',
      lastname: initialData?.lastname || '',
      email: initialData?.email || '',
      cin: initialData?.cin || '',
      gradeLevel: initialData?.gradeLevel || '',
      gender: normalizeGender(initialData?.gender),
      birthday: initialData?.birthday
        ? new Date(initialData.birthday)
        : undefined,
      enrolledDate: initialData?.enrolledDate
        ? new Date(initialData.enrolledDate)
        : new Date(),
      // Initialize parentId as empty string instead of undefined to avoid controlled/uncontrolled warning
      parentId: initialData?.parent?.id ? String(initialData.parent.id) : '',
      classId: initialData?.class?.id ? String(initialData.class.id) : '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      isActive: initialData?.isActive ?? true,
      // Initialize password as empty string to avoid controlled/uncontrolled warning
      password: '',
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-2 gap-4">
        <form.Field
          name="cin"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'CIN is required'
              if (value.length !== 8) return 'CIN must be 8 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="cin">CIN</Label>
              <Input
                id="cin"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter CIN"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
              {fieldErrors.cin && (
                <p className="text-sm text-red-500">{fieldErrors.cin}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="firstname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'First name is required'
              if (value.length < 2)
                return 'First name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="firstname">First Name</Label>
              <Input
                id="firstname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter first name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
              {fieldErrors.firstname && (
                <p className="text-sm text-red-500">{fieldErrors.firstname}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="lastname"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Last name is required'
              if (value.length < 2)
                return 'Last name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="lastname">Last Name</Label>
              <Input
                id="lastname"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter last name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
              {fieldErrors.lastname && (
                <p className="text-sm text-red-500">{fieldErrors.lastname}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)) {
                return 'Invalid email format'
              }
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter email"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
              {fieldErrors.email && (
                <p className="text-sm text-red-500">{fieldErrors.email}</p>
              )}
            </div>
          )}
        </form.Field>

        {!initialData ? (
          <form.Field
            name="password"
            validators={{
              onChange: ({ value }) => {
                if (!value) return 'Password is required'
                if (value.length < 6) return 'Password must be at least 6 characters'
                if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter'
                if (!/[0-9]/.test(value)) return 'Password must contain at least one number'
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) return 'Password must contain at least one special character'
                return undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
                {fieldErrors.password && (
                  <p className="text-sm text-red-500">{fieldErrors.password}</p>
                )}
              </div>
            )}
          </form.Field>
        ) : (
          <form.Field
            name="password"
            validators={{
              onBlur: ({ value }) => {
                // Skip validation if empty (keeping current password)
                if (!value || value.trim() === '') return undefined
                
                // Validate password only if provided
                const errors = []
                if (value.length < 6) {
                  errors.push('Password must be at least 6 characters')
                }
                if (!/[A-Z]/.test(value)) {
                  errors.push('Password must contain at least one uppercase letter')
                }
                if (!/[0-9]/.test(value)) {
                  errors.push('Password must contain at least one number')
                }
                if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
                  errors.push('Password must contain at least one special character')
                }

                return errors.length > 0 ? errors.join(', ') : undefined
              },
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="password">Password (leave empty to keep current)</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={field.state.value}
                    onFocus={() => setIsPasswordFocused(true)}
                    onBlur={() => {
                      field.handleBlur()
                      setIsPasswordFocused(false)
                    }}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter new password or leave empty"
                    className={
                      field.state.meta.isTouched && field.state.meta.errors
                        ? 'border-red-500'
                        : ''
                    }
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-2 flex items-center"
                    tabIndex={-1}
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
                {isPasswordFocused && (
                  <p className="text-xs text-gray-500">
                    Leave empty to keep the current password. Enter a new password to change it.
                  </p>
                )}
              </div>
            )}
          </form.Field>
        )}

        <form.Field name="gradeLevel">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gradeLevel">Grade Level</Label>
              <Input
                id="gradeLevel"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter grade level"
              />
              {fieldErrors.gradeLevel && (
                <p className="text-sm text-red-500">{fieldErrors.gradeLevel}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="enrolledDate">
          {(field) => (
            <FormYearMonthPicker
              id="enrolledDate"
              label="Enrollment Date"
              value={field.state.value}
              onChange={(date) => field.handleChange(date)}
              onBlur={field.handleBlur}
              error={fieldErrors.enrolledDate}
              placeholder="Select enrollment date"
            />
          )}
        </form.Field>

        <form.Field name="birthday">
          {(field) => (
            <FormYearMonthPicker
              id="birthday"
              label="Birthday"
              value={field.state.value}
              onChange={(date) => field.handleChange(date)}
              onBlur={field.handleBlur}
              error={fieldErrors.birthday}
              placeholder="Select birthday"
            />
          )}
        </form.Field>

        <form.Field
          name="gender"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Gender is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={field.state.value}
                onValueChange={(value: 'male' | 'female') =>
                  field.handleChange(value)
                }
              >
                <SelectTrigger id="gender">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
              {fieldErrors.gender && (
                <p className="text-sm text-red-500">{fieldErrors.gender}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="parentId">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="parentId">Parent</Label>
              <div className="relative" ref={parentSelectorRef}>
                <div
                  className="flex items-center border rounded-md p-2 cursor-pointer"
                  onClick={() => setShowParentSelector((prev) => !prev)}
                >
                  {field.state.value ? (
                    <div className="flex-1">
                      {(() => {
                        const selectedParent = parents.find(
                          (p) => p.id === field.state.value
                        )
                        return selectedParent
                          ? `${selectedParent.firstname} ${selectedParent.lastname}`
                          : 'Select parent'
                      })()}
                    </div>
                  ) : (
                    <div className="flex-1 text-gray-500">Select parent</div>
                  )}
                  <Button
                    type="button"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.stopPropagation()
                      if (field.state.value) {
                        field.handleChange('')
                      }
                    }}
                  >
                    {field.state.value ? 'Clear' : 'Select'}
                  </Button>
                </div>

                {showParentSelector && (
                  <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg">
                    <div className="p-2 relative">
                      <Input
                        placeholder="Search parents by name or CIN..."
                        value={parentSearchQuery}
                        onChange={(e) => setParentSearchQuery(e.target.value)}
                        className="w-full pl-9"
                      />
                      <div className="absolute inset-y-0 left-0 flex items-center pl-5 pointer-events-none">
                        <Search className="h-4 w-4 text-gray-400" />
                      </div>
                      {parentSearchQuery && (
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 flex items-center pr-5"
                          onClick={() => setParentSearchQuery('')}
                        >
                          <X className="h-4 w-4 text-gray-400" />
                        </button>
                      )}
                    </div>
                    <ScrollArea className="h-[200px] w-full">
                      <div className="space-y-2 p-2">
                        {filteredParents.length > 0 ? (
                          filteredParents.map((parent) => (
                            <div
                              key={parent.id}
                              className={`flex items-center space-x-2 p-2 hover:bg-gray-100 rounded cursor-pointer ${field.state.value === parent.id ? 'bg-blue-50' : ''}`}
                              onClick={() => {
                                field.handleChange(parent.id)
                                setShowParentSelector(false)
                              }}
                            >
                              <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
                                <CdnImage
                                  src={parent.avatar || DEFAULT_AVATAR}
                                  alt={`${parent.firstname} ${parent.lastname}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <span>
                                {parent.firstname} {parent.lastname}{' '}
                                {parent.cin && `(${parent.cin})`}
                              </span>
                            </div>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">
                            {parentSearchQuery
                              ? 'No parents match your search'
                              : 'No parents available'}
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                    <div className="p-2 border-t">
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          field.handleChange('')
                          setShowParentSelector(false)
                        }}
                      >
                        Clear Selection
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              {fieldErrors.parentId && (
                <p className="text-sm text-red-500">{fieldErrors.parentId}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="classId">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="classId">Class</Label>
              <Select
                value={field.state.value || 'none'}
                onValueChange={(value) => {
                  // Handle 'none' separately to prevent it from being treated as a valid ID
                  field.handleChange(value === 'none' ? undefined : value)
                }}
              >
                <SelectTrigger id="classId">
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  <SelectItem value="none">No Class</SelectItem>
                  {/* Limit the number of items and simplify the display to avoid recursion */}
                  {classes.slice(0, 100).map((cls) => (
                    <SelectItem key={cls.id} value={String(cls.id)}>
                      {cls.name || 'Unnamed Class'}
                    </SelectItem>
                  ))}
                  {classes.length > 100 && (
                    <div className="py-2 px-2 text-xs text-gray-500">
                      + {classes.length - 100} more classes...
                    </div>
                  )}
                </SelectContent>
              </Select>
              {fieldErrors.classId && (
                <p className="text-sm text-red-500">{fieldErrors.classId}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="address">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter address"
              />
              {fieldErrors.address && (
                <p className="text-sm text-red-500">{fieldErrors.address}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="phone">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={field.state.value || ''}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter phone number"
              />
              {fieldErrors.phone && (
                <p className="text-sm text-red-500">{fieldErrors.phone}</p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field name="avatar">
          {(field) => (
            <FileUploader
              label="Student Avatar"
              defaultPreview={field.state.value || DEFAULT_AVATAR}
              onFileUploaded={(url: string) => field.handleChange(url)}
              isAvatar={true}
              previewComponent={(previewUrl: string) => (
                <CdnImage
                  src={previewUrl}
                  alt="Student avatar"
                  className="h-28 w-28 rounded-full object-cover border-2 border-gray-200 shadow-sm"
                />
              )}
            />
          )}
        </form.Field>

        <form.Field name="isActive">
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="isActive">Status</Label>
              <Select
                value={field.state.value.toString()}
                onValueChange={(value) => field.handleChange(value === 'true')}
              >
                <SelectTrigger id="isActive">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
              {fieldErrors.isActive && (
                <p className="text-sm text-red-500">{fieldErrors.isActive}</p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
