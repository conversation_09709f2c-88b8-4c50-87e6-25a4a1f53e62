import useStudent from '@/hooks/useStudent'
import useSubject from '@/hooks/useSubject'
import { StudentTable } from '@/components/shared/StudentTable'
import { Link } from '@tanstack/react-router'

interface StudentScoreDetailsProps {
  studentId: string
}

/**
 * Component for displaying detailed student scores
 * @param {StudentScoreDetailsProps} props - The component props
 * @returns {JSX.Element} The rendered StudentScoreDetails component
 */
export default function StudentScoreDetails({
  studentId,
}: StudentScoreDetailsProps): JSX.Element {
  const {
    students,
    isLoading: isStudentsLoading,
    isError: isStudentsError,
    error: studentsError,
  } = useStudent()

  const {
    subjects,
    isLoading: isSubjectsLoading,
    isError: isSubjectsError,
    error: subjectsError,
  } = useSubject()

  if (isStudentsLoading || isSubjectsLoading) {
    return <div>Loading...</div>
  }

  if (isStudentsError || isSubjectsError) {
    return <div>Error: {(studentsError || subjectsError)?.toString()}</div>
  }

  const student = students?.find((s) => s.id === studentId)
  if (!student) {
    return <div>Student not found</div>
  }

  // Initialize missing subjects with empty grades
  const studentWithAllSubjects = {
    ...student,
    subjects: subjects?.map((subject) => {
      const existingSubject = student.subjects?.find(
        (s) => 'subjectId' in s && s.subjectId === subject.id
      )
      return (
        existingSubject || {
          subjectId: subject.id,
          grades: [],
        }
      )
    }),
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">
          Score Details - {student.firstname} {student.lastname}
        </h2>
        <Link
          to="/admin/database"
          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          Back to Grades
        </Link>
      </div>

      <StudentTable
        students={[studentWithAllSubjects]}
        subjects={subjects || []}
        showScore
        showActions={false}
      />
    </div>
  )
}
