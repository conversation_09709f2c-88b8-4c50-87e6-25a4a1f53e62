import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Student } from '@/interface/types/user'
import { studentService, parentService, classService } from '@/lib/api/services'
import { jsPDF } from 'jspdf'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, ChevronLeft, ChevronRight, Download, RefreshCw, Trash2 } from 'lucide-react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { StudentForm, type StudentFormValues } from './StudentForm'
import Loading from '@/components/shared/Loading'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ExtendedParent as Parent } from '@/interface/types/parent-extended'
import { Class } from '@/interface/types'
import { useToast } from '@/components/ui/use-toast'

// Define interfaces for filters
interface StudentFilters {
  gradeLevel: string
  gender: string
  isPaid: string
  class: string
  parentId: string
}

interface ActiveFilter {
  key: keyof StudentFilters
  value: string
  label: string
}

// Utility function to generate PDF content for students
const generateStudentPDF = (students: Student[], month: string, year: string, onlyPaid?: boolean, onlyUnpaid?: boolean) => {
  // Create PDF document
  const doc = new jsPDF()
  
  // Add title
  let title = 'All Students'
  if (onlyPaid) title = 'Paid Students'
  if (onlyUnpaid) title = 'Unpaid Students'
  
  doc.setFontSize(18)
  doc.text(`${title} - ${month} ${year}`, 14, 22)
  
  // Add generation info
  doc.setFontSize(10)
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 30)
  
  // Filter students if needed
  let filteredStudents = students
  if (onlyPaid) {
    filteredStudents = students.filter(student => student.isPaid === true)
  } else if (onlyUnpaid) {
    filteredStudents = students.filter(student => student.isPaid === false)
  }
  
  // Set up table parameters
  const margin = 14
  const pageWidth = doc.internal.pageSize.width
  const pageHeight = doc.internal.pageSize.height
  const tableWidth = pageWidth - (margin * 2)
  const rowHeight = 10
  const colWidths = [50, 25, 30, 40, 30]
  const headers = ["Name", "CIN", "Grade Level", "Class", "Payment Status"]
  
  // Draw table header
  doc.setFillColor(240, 240, 240)
  doc.rect(margin, 35, tableWidth, rowHeight, 'F')
  doc.setFont('helvetica', 'bold')
  doc.setFontSize(10)
  
  let xPos = margin
  headers.forEach((header, i) => {
    doc.text(header, xPos + 2, 35 + rowHeight/2 + 2)
    xPos += colWidths[i]
  })
  
  // Draw table rows
  let yPos = 35 + rowHeight
  doc.setFont('helvetica', 'normal')
  
  filteredStudents.forEach((student, index) => {
    // Check if we need a new page
    if (yPos > pageHeight - 20) {
      doc.addPage()
      yPos = 20
      
      // Redraw header on new page
      doc.setFillColor(240, 240, 240)
      doc.rect(margin, yPos, tableWidth, rowHeight, 'F')
      doc.setFont('helvetica', 'bold')
      
      xPos = margin
      headers.forEach((header, i) => {
        doc.text(header, xPos + 2, yPos + rowHeight/2 + 2)
        xPos += colWidths[i]
      })
      
      yPos += rowHeight
      doc.setFont('helvetica', 'normal')
    }
    
    // Set row background color (alternating)
    if (index % 2 === 0) {
      doc.setFillColor(250, 250, 250)
      doc.rect(margin, yPos, tableWidth, rowHeight, 'F')
    }
    
    // Set payment status cell background
    const statusX = margin + colWidths[0] + colWidths[1] + colWidths[2] + colWidths[3]
    const statusWidth = colWidths[4]
    
    if (student.isPaid) {
      doc.setFillColor(204, 229, 255) // Light blue for paid
    } else {
      doc.setFillColor(255, 243, 205) // Light yellow for unpaid
    }
    doc.rect(statusX, yPos, statusWidth, rowHeight, 'F')
    
    // Add row data
    const className = typeof student.class === 'string'
      ? student.class
      : (student.class as any)?.name || 'No Class'
    
    xPos = margin
    
    // Name
    doc.text(`${student.firstname} ${student.lastname}`.substring(0, 24), xPos + 2, yPos + rowHeight/2 + 2)
    xPos += colWidths[0]
    
    // CIN
    doc.text((student.cin || '').substring(0, 10), xPos + 2, yPos + rowHeight/2 + 2)
    xPos += colWidths[1]
    
    // Grade Level
    doc.text((student.gradeLevel || '').substring(0, 12), xPos + 2, yPos + rowHeight/2 + 2)
    xPos += colWidths[2]
    
    // Class
    doc.text(className.substring(0, 18), xPos + 2, yPos + rowHeight/2 + 2)
    xPos += colWidths[3]
    
    // Payment Status
    doc.text(student.isPaid ? 'Paid' : 'Unpaid', xPos + 2, yPos + rowHeight/2 + 2)
    
    yPos += rowHeight
  })
  
  return doc
}

// Utility function to download PDF file
const downloadPDF = (doc: any, filename: string) => {
  doc.save(`${filename}.pdf`)
}

export default function StudentManagement(): JSX.Element {
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = useState<boolean>(false)
  const [isAddStudentOpen, setIsAddStudentOpen] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [studentToTogglePaid, setStudentToTogglePaid] =
    useState<Student | null>(null)
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false)
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false)
  const [resetMonth, setResetMonth] = useState('')
  const [resetYear, setResetYear] = useState('')
  const [isResetting, setIsResetting] = useState(false)

  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [parentSearchQuery, setParentSearchQuery] = useState('')

  const { toast } = useToast()
  const queryClient = useQueryClient()

  // State for download dialogs
  const [isAllStudentsDialogOpen, setIsAllStudentsDialogOpen] = useState(false)
  const [isPaidStudentsDialogOpen, setIsPaidStudentsDialogOpen] = useState(false)
  const [isUnpaidStudentsDialogOpen, setIsUnpaidStudentsDialogOpen] = useState(false)
  const [downloadMonth, setDownloadMonth] = useState('')
  const [downloadYear, setDownloadYear] = useState('')
  const [downloadFilename, setDownloadFilename] = useState('')
  const [isDownloading, setIsDownloading] = useState(false)

  // State for delete student
  const [studentToDelete, setStudentToDelete] = useState<Student | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Queries
  const {
    data: students = [],
    isLoading: isLoadingStudents,
    isError: isStudentsError,
    error: studentsError,
  } = useQuery({
    queryKey: ['students'],
    queryFn: studentService.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const {
    data: parents = [],
    isLoading: isLoadingParents,
    isError: isParentsError,
    error: parentsError,
  } = useQuery({
    queryKey: ['parents'],
    queryFn: parentService.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    isError: isClassesError,
    error: classesError,
  } = useQuery({
    queryKey: ['classes'],
    queryFn: classService.getAll,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Mutations
  const updateStudentMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Student> }) =>
      studentService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsProfileModalOpen(false)
    },
  })

  const createStudentMutation = useMutation({
    mutationFn: (data: Partial<Student>) => studentService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsAddStudentOpen(false)
    },
  })

  const togglePaymentStatusMutation = useMutation({
    mutationFn: ({ id, isPaid }: { id: string; isPaid: boolean }) =>
      studentService.update(id, { isPaid }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsPaymentDialogOpen(false)
      setStudentToTogglePaid(null)
    },
  })

  // Mutation for resetting all students to unpaid
  const resetAllStudentsMutation = useMutation({
    mutationFn: async () => {
      // Create an array of promises for each student update
      const updatePromises = students.map(student =>
        studentService.update(student.id, { isPaid: false })
      )

      // Wait for all updates to complete
      return Promise.all(updatePromises)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsResetting(false)
      setIsResetDialogOpen(false)

      toast({
        title: "Reset Successful",
        description: `All students have been marked as unpaid for ${resetMonth} ${resetYear}`,
      })

      // Generate and download the report after reset
      const pdfDoc = generateStudentPDF(students, resetMonth, resetYear)
      downloadPDF(pdfDoc, `student_payment_status_${resetMonth}_${resetYear}`)
    },
    onError: (error) => {
      console.error('Failed to reset students:', error)
      setIsResetting(false)

      toast({
        title: "Reset Failed",
        description: "There was an error resetting student payment status.",
        variant: "destructive"
      })
    }
  })
  // Handler for opening the delete confirmation dialog
  const handleDeleteClick = (student: Student) => {
    setStudentToDelete(student)
    setIsDeleteDialogOpen(true)
  }

  // Handler for confirming student deletion
  const confirmDeleteStudent = () => {
    if (studentToDelete) {
      setIsDeleting(true)
      deleteStudentMutation.mutate(studentToDelete.id)
    }
  }

  // Delete student mutation
  const deleteStudentMutation = useMutation({
    mutationFn: (id: string) => studentService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
      setIsDeleteDialogOpen(false)
      setStudentToDelete(null)
      setIsDeleting(false)
      
      toast({
        title: "Student Deleted",
        description: "The student has been successfully deleted.",
        variant: "default",
      })
    },
    onError: (error) => {
      console.error('Error deleting student:', error)
      setIsDeleting(false)
      
      toast({
        title: "Deletion Failed",
        description: "Failed to delete student. Please try again.",
        variant: "destructive",
      })
    }
  })

  // Get unique values for select filters
  const uniqueGradeLevels = [
    ...new Set(students?.map((s) => s.gradeLevel) || []),
  ]

  // Get unique class names
  const uniqueClasses = classes ? [...new Set(classes.map((c) => c.name))] : []

  // Filter parents based on search query
  const filteredParents = parents.filter((parent) => {
    if (!parentSearchQuery) return true

    const fullName = `${parent.firstname} ${parent.lastname}`.toLowerCase()
    const cin = parent.cin?.toLowerCase() || ''
    const query = parentSearchQuery.toLowerCase()

    return fullName.includes(query) || cin.includes(query)
  })

  // Filter and search students
  const filteredStudents =
    students?.filter((student) => {
      // Search query matching
      const searchMatches =
        searchQuery === '' ||
        `${student.firstname} ${student.lastname} ${student.cin}`
          .toLowerCase()
          .includes(searchQuery.toLowerCase())

      // All active filters must match
      const filtersMatch = activeFilters.every((filter) => {
        switch (filter.key) {
          case 'gradeLevel':
            return student.gradeLevel === filter.value
          case 'gender':
            return student.gender?.toLowerCase() === filter.value.toLowerCase()
          case 'isPaid':
            return student.isPaid === (filter.value === 'true')
          case 'class':
            // Handle class filtering - check if class name matches
            const className =
              typeof student.class === 'string'
                ? student.class
                : (student.class as any)?.name || ''
            return className === filter.value
          case 'parentId':
            // Handle parent filtering - check if parent ID matches
            const parentId =
              typeof student.parent === 'string'
                ? student.parent
                : (student.parent as any)?.id || ''
            return parentId === filter.value
          default:
            return true
        }
      })

      return searchMatches && filtersMatch
    }) || []

  // Pagination setup
  const studentsPerPage = 5
  const totalPages = Math.ceil(filteredStudents.length / studentsPerPage)
  const indexOfLastStudent = currentPage * studentsPerPage
  const indexOfFirstStudent = indexOfLastStudent - studentsPerPage
  const currentStudents = filteredStudents.slice(
    indexOfFirstStudent,
    indexOfLastStudent
  )

  const handleShowProfile = (student: Student) => {
    setSelectedStudent(student)
    setIsProfileModalOpen(true)
  }

  const handleFilterChange = (
    key: keyof StudentFilters,
    value: string,
    label: string
  ) => {
    setActiveFilters((prev) => {
      const filtered = prev.filter((f) => f.key !== key)
      if (value !== 'all') {
        return [...filtered, { key, value, label }]
      }
      return filtered
    })
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setSearchQuery('')
    setParentSearchQuery('')
    setCurrentPage(1)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const handleTogglePaymentStatus = (student: Student) => {
    setStudentToTogglePaid(student)
    setIsPaymentDialogOpen(true)
  }

  const confirmTogglePayment = () => {
    if (studentToTogglePaid) {
      togglePaymentStatusMutation.mutate({
        id: studentToTogglePaid.id,
        isPaid: !studentToTogglePaid.isPaid,
      })
    }
  }

  // Handler for opening the all students download dialog
  const handleOpenAllStudentsDialog = () => {
    const currentDate = new Date()
    setDownloadMonth(currentDate.toLocaleString('default', { month: 'long' }))
    setDownloadYear(currentDate.getFullYear().toString())
    setDownloadFilename(`all_students_${currentDate.toLocaleString('default', { month: 'long' })}_${currentDate.getFullYear()}`)
    setIsAllStudentsDialogOpen(true)
  }

  // Handler for opening the paid students download dialog
  const handleOpenPaidStudentsDialog = () => {
    const currentDate = new Date()
    setDownloadMonth(currentDate.toLocaleString('default', { month: 'long' }))
    setDownloadYear(currentDate.getFullYear().toString())
    setDownloadFilename(`paid_students_${currentDate.toLocaleString('default', { month: 'long' })}_${currentDate.getFullYear()}`)
    setIsPaidStudentsDialogOpen(true)
  }

  // Handler for opening the unpaid students download dialog
  const handleOpenUnpaidStudentsDialog = () => {
    const currentDate = new Date()
    setDownloadMonth(currentDate.toLocaleString('default', { month: 'long' }))
    setDownloadYear(currentDate.getFullYear().toString())
    setDownloadFilename(`unpaid_students_${currentDate.toLocaleString('default', { month: 'long' })}_${currentDate.getFullYear()}`)
    setIsUnpaidStudentsDialogOpen(true)
  }

  // Handler for confirming the download all students operation
  const confirmDownloadAllStudents = () => {
    // Validate inputs
    if (!downloadMonth.trim() || !downloadYear.trim() || !downloadFilename.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter month, year, and filename.",
        variant: "destructive"
      })
      return
    }

    // Validate year format (4 digits)
    if (!/^\d{4}$/.test(downloadYear)) {
      toast({
        title: "Validation Error",
        description: "Year must be a 4-digit number.",
        variant: "destructive"
      })
      return
    }

    setIsDownloading(true)
    
    try {
      const pdfDoc = generateStudentPDF(students, downloadMonth, downloadYear)
      downloadPDF(pdfDoc, downloadFilename)
      
      toast({
        title: "Download Started",
        description: "The student list PDF is being downloaded.",
      })
    } catch (error) {
      console.error('Download error:', error)
      toast({
        title: "Download Failed",
        description: "There was an error generating the PDF file.",
        variant: "destructive"
      })
    } finally {
      setIsDownloading(false)
      setIsAllStudentsDialogOpen(false)
    }
  }

  // Handler for confirming the download paid students operation
  const confirmDownloadPaidStudents = () => {
    // Validate inputs
    if (!downloadMonth.trim() || !downloadYear.trim() || !downloadFilename.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter month, year, and filename.",
        variant: "destructive"
      })
      return
    }

    // Validate year format (4 digits)
    if (!/^\d{4}$/.test(downloadYear)) {
      toast({
        title: "Validation Error",
        description: "Year must be a 4-digit number.",
        variant: "destructive"
      })
      return
    }

    setIsDownloading(true)
    
    try {
      const pdfDoc = generateStudentPDF(students, downloadMonth, downloadYear, true)
      downloadPDF(pdfDoc, downloadFilename)
      
      toast({
        title: "Download Started",
        description: "The paid students PDF is being downloaded.",
      })
    } catch (error) {
      console.error('Download error:', error)
      toast({
        title: "Download Failed",
        description: "There was an error generating the PDF file.",
        variant: "destructive"
      })
    } finally {
      setIsDownloading(false)
      setIsPaidStudentsDialogOpen(false)
    }
  }

  // Handler for confirming the download unpaid students operation
  const confirmDownloadUnpaidStudents = () => {
    // Validate inputs
    if (!downloadMonth.trim() || !downloadYear.trim() || !downloadFilename.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter month, year, and filename.",
        variant: "destructive"
      })
      return
    }

    // Validate year format (4 digits)
    if (!/^\d{4}$/.test(downloadYear)) {
      toast({
        title: "Validation Error",
        description: "Year must be a 4-digit number.",
        variant: "destructive"
      })
      return
    }

    setIsDownloading(true)
    
    try {
      const pdfDoc = generateStudentPDF(students, downloadMonth, downloadYear, false, true)
      downloadPDF(pdfDoc, downloadFilename)
      
      toast({
        title: "Download Started",
        description: "The unpaid students PDF is being downloaded.",
      })
    } catch (error) {
      console.error('Download error:', error)
      toast({
        title: "Download Failed",
        description: "There was an error generating the PDF file.",
        variant: "destructive"
      })
    } finally {
      setIsDownloading(false)
      setIsUnpaidStudentsDialogOpen(false)
    }
  }

  // Handler for opening the reset dialog
  const handleOpenResetDialog = () => {
    const currentDate = new Date()
    setResetMonth(currentDate.toLocaleString('default', { month: 'long' }))
    setResetYear(currentDate.getFullYear().toString())
    setIsResetDialogOpen(true)
  }

  // Handler for confirming the reset operation
  const confirmResetAllStudents = () => {
    // Validate month and year
    if (!resetMonth.trim() || !resetYear.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter both month and year.",
        variant: "destructive"
      })
      return
    }

    // Validate year format (4 digits)
    if (!/^\d{4}$/.test(resetYear)) {
      toast({
        title: "Validation Error",
        description: "Year must be a 4-digit number.",
        variant: "destructive"
      })
      return
    }

    setIsResetting(true)
    resetAllStudentsMutation.mutate()
  }

  const handleUpdateStudent = async (data: StudentFormValues) => {
    if (!selectedStudent?.id) return
    try {
      // Use any type to allow for class as string
      const updateData: any = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        gradeLevel: data.gradeLevel,
        // Use the parentId directly as a string
        parent: data.parentId || undefined,
        // Use the classId directly as a string for the class name
        class: data.classId || undefined,
        gender: data.gender,
        birthday: data.birthday?.toISOString(),
        address: data.address,
        phone: data.phone,
        avatar: data.avatar,
        isActive: data.isActive,
        isPaid: true, // Default to true, can be modified if needed
      }

      // Add password to update data if provided
      if (data.password && data.password.trim() !== '') {
        updateData.password = data.password;
      }

      console.log('Updating student with data:', updateData);

      await updateStudentMutation.mutateAsync({
        id: selectedStudent.id,
        data: updateData,
      })
    } catch (error) {
      console.error('Failed to update student:', error)
    }
  }

  const handleCreateStudent = async (data: StudentFormValues) => {
    try {
      if (!data.password) {
        throw new Error('Password is required for new students')
      }

      // Format the data according to the API requirements
      const createData: any = {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        password: data.password,
        cin: data.cin,
        gradeLevel: data.gradeLevel || '',
        enrolledDate:
          data.enrolledDate?.toISOString() || new Date().toISOString(),
        // Use the parentId directly as a string
        parent: data.parentId || undefined,
        // Use the classId directly as a string for the class ID
        class: data.classId || undefined,
        gender:
          data.gender.charAt(0).toUpperCase() +
          data.gender.slice(1).toLowerCase(), // Capitalize first letter
        birthday: data.birthday?.toISOString() || null,
        address: data.address || '',
        phone: data.phone || '',
        avatar: data.avatar || DEFAULT_AVATAR,
        isActive: data.isActive,
        isPaid: true, // Default to true, can be modified if needed
        role: 'Student', // Set the role with correct capitalization
      }

      // If the class field is empty or undefined, remove it from the request
      if (!createData.class) {
        delete createData.class
      }

      // If the parent field is empty or undefined, remove it from the request
      if (!createData.parent) {
        delete createData.parent
      }

      // Log the data being sent to the API
      console.log(
        'Creating student with data:',
        JSON.stringify(createData, null, 2)
      )
      await createStudentMutation.mutateAsync(createData)
    } catch (error: any) {
      console.error('Failed to create student:', error)
      // Display the error message from the server
      if (error.response?.data?.message) {
        alert(`Error: ${JSON.stringify(error.response.data.message)}`)
      }
    }
  }

  // Function to render a student row without using hooks inside
  const renderStudentRow = (student: Student) => {
    // We don't use the unread count feature for now to avoid hook issues

    return (
      <TableRow key={student.id}>
        <TableCell>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full overflow-hidden">
              <CdnImage
                src={student.avatar || DEFAULT_AVATAR}
                alt={`${student.firstname} ${student.lastname}`}
                className="w-full h-full object-cover"
              />
            </div>
            <span>
              {student.firstname} {student.lastname}
            </span>
          </div>
        </TableCell>
        <TableCell>{student.cin}</TableCell>
        <TableCell>{student.gradeLevel || 'No Grade Level'}</TableCell>
        <TableCell>
          {typeof student.class === 'string'
            ? student.class
            : (student.class as any)?.name || 'No Class'}
        </TableCell>
        <TableCell>
          <Badge variant={student.isPaid ? 'success' : 'destructive'}>
            {student.isPaid ? 'Paid' : 'Unpaid'}
          </Badge>
        </TableCell>
        <TableCell>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleShowProfile(student)}
            >
              Profile
            </Button>

            <Button
              variant={student.isPaid ? 'destructive' : 'default'}
              onClick={() => handleTogglePaymentStatus(student)}
            >
              {student.isPaid ? 'Mark as Unpaid' : 'Mark as Paid'}
            </Button>

            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteClick(student)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  if (isLoadingStudents || isLoadingParents || isLoadingClasses)
    return <Loading />
  if (isStudentsError)
    return <p>Error loading students: {studentsError?.message}</p>
  if (isParentsError)
    return <p>Error loading parents: {parentsError?.message}</p>
  if (isClassesError)
    return <p>Error loading classes: {classesError?.message}</p>

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Students Table</h2>
        <div className="flex items-center gap-2">
          <Button onClick={() => setIsAddStudentOpen(true)}>
            Add New Student
          </Button>
          <Badge variant="secondary" className="text-sm">
            {students?.length || 0} Students
          </Badge>
        </div>
      </div>

      {/* Export and Reset Buttons */}
      <div className="flex flex-wrap gap-2 mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="w-full mb-2">
          <h3 className="text-lg font-semibold">Payment Management</h3>
        </div>

        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={handleOpenAllStudentsDialog}
        >
          <Download className="h-4 w-4" />
          Download All Students
        </Button>

        <Button
          variant="outline"
          className="flex items-center gap-2 bg-blue-50"
          onClick={handleOpenPaidStudentsDialog}
        >
          <Download className="h-4 w-4" />
          Download Paid Students
        </Button>

        <Button
          variant="outline"
          className="flex items-center gap-2 bg-yellow-50"
          onClick={handleOpenUnpaidStudentsDialog}
        >
          <Download className="h-4 w-4" />
          Download Unpaid Students
        </Button>

        <Button
          variant="outline"
          className="flex items-center gap-2 ml-auto bg-red-50 hover:bg-red-100"
          onClick={handleOpenResetDialog}
        >
          <RefreshCw className="h-4 w-4" />
          Reset All to Unpaid
        </Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search students by name or CIN..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Filter Students</h4>
                <div className="space-y-2">
                  <Label htmlFor="gradeLevel-filter">Grade Level</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange('gradeLevel', value, `Grade: ${value}`)
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="gradeLevel-filter">
                      <SelectValue placeholder="Select grade level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Grades</SelectItem>
                      {uniqueGradeLevels.map((grade) => (
                        <SelectItem
                          key={grade || 'no-grade'}
                          value={grade || 'no-grade'}
                        >
                          {grade || 'No Grade'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gender-filter">Gender</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange('gender', value, `Gender: ${value}`)
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="gender-filter">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Genders</SelectItem>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="isPaid-filter">Payment Status</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange(
                        'isPaid',
                        value,
                        `Payment: ${value === 'true' ? 'Paid' : 'Unpaid'}`
                      )
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="isPaid-filter">
                      <SelectValue placeholder="Select payment status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="true">Paid</SelectItem>
                      <SelectItem value="false">Unpaid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Class Filter */}
                <div className="space-y-2">
                  <Label htmlFor="class-filter">Class</Label>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange('class', value, `Class: ${value}`)
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="class-filter">
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      {uniqueClasses.map((className) => (
                        <SelectItem
                          key={className || 'no-class'}
                          value={className || 'no-class'}
                        >
                          {className || 'No Class'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Parent Filter */}
                <div className="space-y-2">
                  <Label htmlFor="parent-filter">Parent</Label>
                  <div className="relative">
                    <Input
                      id="parent-search"
                      placeholder="Search parent by name or CIN..."
                      className="mb-2"
                      value={parentSearchQuery}
                      onChange={(e) => setParentSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select
                    onValueChange={(value) =>
                      handleFilterChange(
                        'parentId',
                        value,
                        `Parent: ${
                          parents?.find((p) => p.id === value)?.firstname || ''
                        } ${
                          parents?.find((p) => p.id === value)?.lastname || ''
                        }`
                      )
                    }
                    defaultValue="all"
                  >
                    <SelectTrigger id="parent-filter">
                      <SelectValue placeholder="Select parent" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Parents</SelectItem>
                      {filteredParents?.map((parent) => (
                        <SelectItem key={parent.id} value={parent.id}>
                          {parent.firstname} {parent.lastname}{' '}
                          {parent.cin ? `(${parent.cin})` : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="px-3 py-1 gap-2"
            >
              {filter.label}
              <button
                onClick={() =>
                  setActiveFilters((prev) =>
                    prev.filter((f) => f.key !== filter.key)
                  )
                }
                className="text-xs rounded-full hover:bg-gray-200 h-4 w-4 inline-flex items-center justify-center"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-7"
          >
            Clear all
          </Button>
        </div>
      )}

      <div className="mt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>CIN</TableHead>
              <TableHead>Grade Level</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>{currentStudents.map(renderStudentRow)}</TableBody>
        </Table>
      </div>

      <div className="mt-6 flex justify-center">
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      {/* Student Profile Modal */}
      <Dialog open={isProfileModalOpen} onOpenChange={setIsProfileModalOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Student Profile</DialogTitle>
          </DialogHeader>
          {selectedStudent && (
            <StudentForm
              initialData={selectedStudent}
              parents={parents as Parent[]}
              classes={classes as Class[]}
              onSubmit={handleUpdateStudent}
              onCancel={() => setIsProfileModalOpen(false)}
              submitLabel="Update Student"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add New Student Modal */}
      <Dialog open={isAddStudentOpen} onOpenChange={setIsAddStudentOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Student</DialogTitle>
          </DialogHeader>
          <StudentForm
            parents={parents as Parent[]}
            classes={classes as Class[]}
            onSubmit={handleCreateStudent}
            onCancel={() => setIsAddStudentOpen(false)}
            submitLabel="Add Student"
          />
        </DialogContent>
      </Dialog>

      {/* Payment Status Confirmation Dialog */}
      <AlertDialog
        open={isPaymentDialogOpen}
        onOpenChange={setIsPaymentDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Payment Status Change</AlertDialogTitle>
            <AlertDialogDescription>
              {studentToTogglePaid && (
                <>
                  Are you sure you want to mark{' '}
                  <span className="font-semibold">
                    {studentToTogglePaid.firstname}{' '}
                    {studentToTogglePaid.lastname}
                  </span>{' '}
                  as {studentToTogglePaid.isPaid ? 'unpaid' : 'paid'}?
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmTogglePayment}
              className={
                studentToTogglePaid?.isPaid
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-green-600 hover:bg-green-700'
              }
            >
              {studentToTogglePaid?.isPaid ? 'Mark as Unpaid' : 'Mark as Paid'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reset All Students Confirmation Dialog */}
      <AlertDialog
        open={isResetDialogOpen}
        onOpenChange={(open) => !isResetting && setIsResetDialogOpen(open)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset All Students to Unpaid</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark ALL students as unpaid. This action cannot be undone.
              Please enter the month and year for this reset operation:
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="grid grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reset-month">Month</Label>
              <Select
                value={resetMonth}
                onValueChange={setResetMonth}
                disabled={isResetting}
              >
                <SelectTrigger id="reset-month">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'].map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reset-year">Year</Label>
              <Select
                value={resetYear}
                onValueChange={setResetYear}
                disabled={isResetting}
              >
                <SelectTrigger id="reset-year">
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {Array.from({ length: 101 }, (_, i) => (2000 + i).toString()).map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isResetting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmResetAllStudents}
              className="bg-red-600 hover:bg-red-700"
              disabled={isResetting}
            >
              {isResetting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Resetting...
                </>
              ) : (
                'Reset All Students'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Download All Students Dialog */}
      <AlertDialog
        open={isAllStudentsDialogOpen}
        onOpenChange={(open) => !isDownloading && setIsAllStudentsDialogOpen(open)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Download All Students</AlertDialogTitle>
            <AlertDialogDescription>
              This will generate a PDF report of all students.
              Please enter the month, year, and filename for this report:
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="grid grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="download-month">Month</Label>
              <Select
                value={downloadMonth}
                onValueChange={setDownloadMonth}
                disabled={isDownloading}
              >
                <SelectTrigger id="download-month">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'].map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="download-year">Year</Label>
              <Input
                id="download-year"
                value={downloadYear}
                onChange={(e) => setDownloadYear(e.target.value)}
                disabled={isDownloading}
                placeholder="YYYY"
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="download-filename">Filename</Label>
              <Input
                id="download-filename"
                value={downloadFilename}
                onChange={(e) => setDownloadFilename(e.target.value)}
                disabled={isDownloading}
                placeholder="Enter filename (without extension)"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDownloading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDownloadAllStudents}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Downloading...
                </>
              ) : (
                'Download PDF'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Download Paid Students Dialog */}
      <AlertDialog
        open={isPaidStudentsDialogOpen}
        onOpenChange={(open) => !isDownloading && setIsPaidStudentsDialogOpen(open)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Download Paid Students</AlertDialogTitle>
            <AlertDialogDescription>
              This will generate a PDF report of all paid students.
              Please enter the month, year, and filename for this report:
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="grid grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="download-month-paid">Month</Label>
              <Select
                value={downloadMonth}
                onValueChange={setDownloadMonth}
                disabled={isDownloading}
              >
                <SelectTrigger id="download-month-paid">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'].map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="download-year-paid">Year</Label>
              <Input
                id="download-year-paid"
                value={downloadYear}
                onChange={(e) => setDownloadYear(e.target.value)}
                disabled={isDownloading}
                placeholder="YYYY"
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="download-filename-paid">Filename</Label>
              <Input
                id="download-filename-paid"
                value={downloadFilename}
                onChange={(e) => setDownloadFilename(e.target.value)}
                disabled={isDownloading}
                placeholder="Enter filename (without extension)"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDownloading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDownloadPaidStudents}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Downloading...
                </>
              ) : (
                'Download PDF'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Download Unpaid Students Dialog */}
      <AlertDialog
        open={isUnpaidStudentsDialogOpen}
        onOpenChange={(open) => !isDownloading && setIsUnpaidStudentsDialogOpen(open)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Download Unpaid Students</AlertDialogTitle>
            <AlertDialogDescription>
              This will generate a PDF report of all unpaid students.
              Please enter the month, year, and filename for this report:
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="grid grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="download-month-unpaid">Month</Label>
              <Select
                value={downloadMonth}
                onValueChange={setDownloadMonth}
                disabled={isDownloading}
              >
                <SelectTrigger id="download-month-unpaid">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'].map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="download-year-unpaid">Year</Label>
              <Input
                id="download-year-unpaid"
                value={downloadYear}
                onChange={(e) => setDownloadYear(e.target.value)}
                disabled={isDownloading}
                placeholder="YYYY"
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="download-filename-unpaid">Filename</Label>
              <Input
                id="download-filename-unpaid"
                value={downloadFilename}
                onChange={(e) => setDownloadFilename(e.target.value)}
                disabled={isDownloading}
                placeholder="Enter filename (without extension)"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDownloading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDownloadUnpaidStudents}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Downloading...
                </>
              ) : (
                'Download PDF'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Student Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={(open) => !isDeleting && setIsDeleteDialogOpen(open)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Student</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the student
              {studentToDelete && (
                <span className="font-semibold">
                  {" "}"{studentToDelete.firstname} {studentToDelete.lastname}"{" "}
                </span>
              )}
              ? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteStudent}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

