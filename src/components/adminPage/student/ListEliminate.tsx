import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

import {
  EliminateStudent,
  EliminateSubject,
  EliminationThresholds,
  subjects,
} from '@/interface/ListEliminateData'
import { toStringId } from '@/interface/types/SharedTypes'

// Get correct import for initialStudents
import { initialStudents } from '@/interface/ListEliminateData'

const ListEliminate = () => {
  const [students, setStudents] = useState<EliminateStudent[]>(
    initialStudents.map((student) => ({
      id: student.id,
      firstname: student.fullname.split(' ')[0] || '',
      lastname: student.fullname.split(' ').slice(1).join(' ') || '',
      fullname: student.fullname,
      subjects: student.subjects.map((subject) => ({
        subjectId: subject.subjectId,
        absences: subject.absences,
        isEliminated: subject.isEliminated,
        grades: [],
      })),
    }))
  )

  const [selectedStudent, setSelectedStudent] =
    useState<EliminateStudent | null>(null)
  const [isEliminateAllOpen, setIsEliminateAllOpen] = useState(false)
  const [isEliminateOpen, setIsEliminateOpen] = useState(false)
  const [eliminationThresholds, setEliminationThresholds] =
    useState<EliminationThresholds>(
      subjects.reduce(
        (acc, subject) => ({
          ...acc,
          [toStringId(subject.id)]: subject.eliminationThreshold || 0,
        }),
        {} as EliminationThresholds
      )
    )

  const handleEliminate = (
    studentId: string | number,
    subjectId: string | number
  ) => {
    setStudents((prevStudents) =>
      prevStudents.map((student) => {
        if (toStringId(student.id) === toStringId(studentId)) {
          return {
            ...student,
            subjects: (student.subjects || []).map((sub) => {
              if (sub && toStringId(sub.subjectId) === toStringId(subjectId)) {
                return { ...sub, isEliminated: !sub.isEliminated }
              }
              return sub
            }),
          }
        }
        return student
      })
    )
    setIsEliminateOpen(false)
  }

  const handleEliminateAll = () => {
    setStudents((prevStudents) =>
      prevStudents.map((student) => ({
        ...student,
        subjects: (student.subjects || []).map((subject) => {
          if (
            subject &&
            subject.absences &&
            eliminationThresholds[toStringId(subject.subjectId)]
          ) {
            const threshold =
              eliminationThresholds[toStringId(subject.subjectId)]
            return {
              ...subject,
              isEliminated: subject.absences >= threshold,
            }
          }
          return subject
        }),
      }))
    )
    setIsEliminateAllOpen(false)
  }

  const handleThresholdChange = (subjectId: string | number, value: string) => {
    setEliminationThresholds((prev) => ({
      ...prev,
      [toStringId(subjectId)]: parseInt(value) || 0,
    }))
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Student Management</h2>
        <Dialog open={isEliminateAllOpen} onOpenChange={setIsEliminateAllOpen}>
          <DialogTrigger asChild>
            <Button>Eliminate All</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Set Elimination Thresholds</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {subjects.map((subject) => (
                <div
                  key={subject.id}
                  className="flex justify-between items-center space-x-4"
                >
                  <span className="flex-grow">{subject.name}</span>
                  <Input
                    type="number"
                    value={eliminationThresholds[toStringId(subject.id)]}
                    onChange={(e) =>
                      handleThresholdChange(subject.id, e.target.value)
                    }
                    className="w-24"
                    min="0"
                  />
                </div>
              ))}
              <Button onClick={handleEliminateAll} className="w-full">
                Confirm Elimination
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Full Name</TableHead>
            {subjects.map((subject) => (
              <TableHead key={subject.id}>{subject.name}</TableHead>
            ))}
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {students.map((student) => (
            <TableRow key={student.id}>
              <TableCell>{student.fullname}</TableCell>
              {subjects.map((subject) => (
                <TableCell key={subject.id}>
                  {(student.subjects || []).find(
                    (s) => 'subjectId' in s && s.subjectId === subject.id
                  )
                    ? (
                        student.subjects?.find(
                          (s) => 'subjectId' in s && s.subjectId === subject.id
                        ) as EliminateSubject
                      )?.absences || 0
                    : 0}
                </TableCell>
              ))}
              <TableCell>
                <Dialog
                  open={isEliminateOpen && selectedStudent?.id === student.id}
                  onOpenChange={(open) => {
                    setIsEliminateOpen(open)
                    if (!open) setSelectedStudent(null)
                  }}
                >
                  <DialogTrigger asChild>
                    <Button onClick={() => setSelectedStudent(student)}>
                      Eliminate
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Eliminate {student.fullname}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {subjects.map((subject) => {
                        const studentSubject = (student.subjects || []).find(
                          (s) => 'subjectId' in s && s.subjectId === subject.id
                        ) as EliminateSubject | undefined
                        return (
                          <div
                            key={subject.id}
                            className="flex justify-between items-center"
                          >
                            <span>
                              {subject.name} ({studentSubject?.absences || 0}{' '}
                              absences)
                            </span>
                            <Button
                              onClick={() => {
                                if (student.id !== undefined) {
                                  handleEliminate(student.id, subject.id)
                                }
                              }}
                              variant={
                                studentSubject?.isEliminated
                                  ? 'destructive'
                                  : 'default'
                              }
                            >
                              {studentSubject?.isEliminated
                                ? 'Remove Elimination'
                                : 'Eliminate'}
                            </Button>
                          </div>
                        )
                      })}
                    </div>
                  </DialogContent>
                </Dialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

export default ListEliminate
