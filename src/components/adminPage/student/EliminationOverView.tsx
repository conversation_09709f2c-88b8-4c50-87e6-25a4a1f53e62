import { DatabaseOverview } from '@/components/shared/DatabaseOverview'
import { useNavigate } from '@tanstack/react-router'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function EliminationOverview() {
  const navigate = useNavigate({ from: '/admin/database' })

  const handleCardClick = (section: string) => {
    // This function will be called when a card is clicked in the DatabaseOverview
    // We'll update the URL parameters to show the selected section
    navigate({
      search: (prev) => ({
        ...prev,
        section: section,
      }),
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center data-storage-header">
        <h1 className="text-2xl font-bold">Data Storage Overview</h1>
      </div>

      <div className="student-records-section">
        <DatabaseOverview role="admin" onCardClick={handleCardClick} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <Card className="academic-history-section">
          <CardHeader>
            <CardTitle>Academic History</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Access complete academic records including grades, transcripts,
              and performance history.
            </p>
          </CardContent>
        </Card>

        <Card className="attendance-section">
          <CardHeader>
            <CardTitle>Attendance Records</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Track student and teacher attendance with detailed reports and
              analytics.
            </p>
          </CardContent>
        </Card>

        <Card className="export-tools">
          <CardHeader>
            <CardTitle>Export & Reporting Tools</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Generate custom reports and export data in various formats
              including PDF, Excel, and CSV.
            </p>
          </CardContent>
        </Card>

        <Card className="backup-section">
          <CardHeader>
            <CardTitle>Data Backup & Recovery</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Create and manage data backups to ensure your school information
              is always safe.
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end mt-4">
        <a
          href="/admin"
          className="text-primary hover:underline back-to-dashboard"
        >
          Back to Dashboard
        </a>
      </div>
    </div>
  )
}
