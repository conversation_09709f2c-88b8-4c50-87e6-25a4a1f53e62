import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import useStudent from '@/hooks/useStudent'
import useSubject from '@/hooks/useSubject'
import { ScoreCalculator } from '@/components/shared/ScoreCalculator'
import { Student, StudentGrade, StudentSubject } from '@/interface/types'
import { Link } from '@tanstack/react-router'
import { useVirtualizer } from '@tanstack/react-virtual'
import { cn } from '@/lib/utils'

// Define FlexibleId type for this component
type FlexibleId = string | number

// Helper function for ID comparison
function isSameId(
  id1: FlexibleId | undefined,
  id2: FlexibleId | undefined
): boolean {
  if (id1 === undefined || id2 === undefined) return false
  return String(id1) === String(id2)
}

const defaultStudent: Student = {
  id: '',
  cin: '',
  firstname: '',
  lastname: '',
  email: '',
  gradeLevel: '',
  enrolledDate: '',
  isPaid: true,
  parent: {
    id: '',
    cin: '',
    firstname: '',
    lastname: '',
    email: '',
    gender: 'male',
    role: 'parent',
    createdAt: '',
    updatedAt: '',
  },
  class: {
    id: '',
    name: '',
    gradeId: '',
    supervisorId: '',
    createdAt: '',
    updatedAt: '',
    createdBy: '',
    updatedBy: '',
  },
  subjects: [],
  role: 'student',
  gender: '',
  birthday: '',
  isActive: false,
  createdAt: '',
  updatedAt: '',
  createdBy: '',
  updatedBy: '',
  avatar: null,
}

interface VirtualRow {
  index: number
  start: number
  end: number
  size: number
  lane: number
}

/**
 * Component for calculating and managing student scores with virtualized list for better performance
 * @returns {JSX.Element} The rendered CalculateStudentScore component
 */
export default function CalculateStudentScore(): JSX.Element {
  const [isFormulaDialogOpen, setIsFormulaDialogOpen] = useState(false)
  const [selectedStudentId] = useState<string | null>(null)
  const [scrollParent, setScrollParent] = useState<HTMLDivElement | null>(null)

  const {
    students,
    isLoading: isStudentsLoading,
    isError: isStudentsError,
    error: studentsError,
    updateStudent,
  } = useStudent()

  const {
    subjects,
    isLoading: isSubjectsLoading,
    isError: isSubjectsError,
    error: subjectsError,
  } = useSubject()

  // Initialize students with empty scores for all subjects if they don't exist
  const studentsWithScores =
    students?.map((student) => {
      if (!student.subjects || student.subjects.length === 0) {
        return {
          ...student,
          subjects: subjects?.map((subject) => ({
            subjectId: subject.id,
            grades: [],
          })) as StudentSubject[],
        }
      }
      // Add missing subjects with empty grades
      const existingSubjectIds = student.subjects.map((s) =>
        'subjectId' in s ? s.subjectId : ''
      )
      const missingSubjects =
        (subjects
          ?.filter((subject) => !existingSubjectIds.includes(subject.id))
          .map((subject) => ({
            subjectId: subject.id,
            grades: [],
          })) as StudentSubject[]) || []

      return {
        ...student,
        subjects: [...student.subjects, ...missingSubjects],
      }
    }) || []

  // Setup virtualizer
  const rowVirtualizer = useVirtualizer({
    count: studentsWithScores.length,
    getScrollElement: () => scrollParent,
    estimateSize: () => 60, // Estimated row height
    overscan: 5, // Number of items to render outside of the visible area
  })

  const selectedStudent = studentsWithScores?.find(
    (s) => s.id === selectedStudentId
  )

  const handleScoreUpdate = (
    studentId: FlexibleId,
    subjectId: FlexibleId,
    score: number
  ) => {
    // Use the isSameId function for ID comparison in the function body
    console.log(
      `Updating score for student ${studentId}, subject ${subjectId}: ${score}`
    )

    const student = studentsWithScores?.find((s) => isSameId(s.id, studentId))
    if (!student) return

    const newGrade: StudentGrade = {
      id: Date.now().toString(),
      examType: 'final',
      value: score,
      date: new Date().toISOString(),
    }

    const updatedStudent: Student = {
      ...student,
      subjects: student.subjects?.map((subject) =>
        isSameId(subject.subjectId, subjectId)
          ? {
              ...subject,
              grades: [newGrade],
            }
          : subject
      ) as StudentSubject[],
    }

    updateStudent(updatedStudent)
  }

  const calculateGlobalScore = (student: Student) => {
    if (!student.subjects || student.subjects.length === 0) return 0

    let totalScore = 0
    let totalSubjects = 0

    student.subjects.forEach((subject) => {
      if ('grades' in subject && subject.grades.length > 0) {
        const subjectScore =
          subject.grades.reduce((acc, grade) => acc + grade.value, 0) /
          subject.grades.length
        totalScore += subjectScore
        totalSubjects++
      }
    })

    return totalSubjects > 0 ? totalScore / totalSubjects : 0
  }

  if (isStudentsLoading || isSubjectsLoading) {
    return <div>Loading...</div>
  }

  if (isStudentsError || isSubjectsError) {
    return <div>Error: {(studentsError || subjectsError)?.toString()}</div>
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Student Grades</h2>
        <Dialog
          open={isFormulaDialogOpen}
          onOpenChange={setIsFormulaDialogOpen}
        >
          <DialogTrigger asChild>
            <Button>Edit Formulas and Coefficients</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>
                Edit Formulas and Coefficients for Subjects
              </DialogTitle>
            </DialogHeader>
            {subjects && (
              <ScoreCalculator
                student={
                  selectedStudent || studentsWithScores?.[0] || defaultStudent
                }
                subjects={subjects}
                onScoreUpdate={handleScoreUpdate}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div
        ref={setScrollParent}
        className="border rounded-md overflow-auto"
        style={{
          height: '600px', // Fixed height for virtualization
          width: '100%',
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          <div className="sticky top-0 z-10 bg-background border-b">
            <div className="grid grid-cols-4 gap-4 p-4 font-medium">
              <div>Full Name</div>
              <div>CIN</div>
              <div>Global Score</div>
              <div>Actions</div>
            </div>
          </div>

          {rowVirtualizer.getVirtualItems().map((virtualRow: VirtualRow) => {
            const student = studentsWithScores[virtualRow.index]
            return (
              <div
                key={student.id}
                className={cn(
                  'absolute top-0 left-0 w-full grid grid-cols-4 gap-4 p-4 border-b hover:bg-muted/50',
                  virtualRow.index % 2 === 0 ? 'bg-background' : 'bg-muted/30'
                )}
                style={{
                  transform: `translateY(${virtualRow.start}px)`,
                }}
              >
                <div>
                  {student.firstname} {student.lastname}
                </div>
                <div>{student.cin}</div>
                <div>{calculateGlobalScore(student).toFixed(2)}</div>
                <div>
                  <Link
                    to="/admin/database/student-score-details/$studentId"
                    params={{ studentId: student.id }}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                  >
                    Details
                  </Link>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
