import { Card, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

import { ScoreStudentProps } from '@/interface/types'

export function ScoreStudent({ subjects = [] }: ScoreStudentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Average Scores by Subject</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              <TableHead>Average Score</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subjects.map((subject) => (
              <TableRow key={subject.subject}>
                <TableCell>{subject.subject}</TableCell>
                <TableCell>{subject.average}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
