import { Card, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

import { MoyeneStudentProps } from '@/interface/types'

export function MoyeneStudent({ semesters = [] }: MoyeneStudentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Semester Averages</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Semester</TableHead>
              <TableHead>Average Score</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {semesters.map((semester) => (
              <TableRow key={semester.semester}>
                <TableCell>{semester.semester}</TableCell>
                <TableCell>{semester.average}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
