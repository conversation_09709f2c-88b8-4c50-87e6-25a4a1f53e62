import { Outlet } from '@tanstack/react-router'
import { useEffect } from 'react'

export default function DatabaseLayout() {
  // Add CSS classes for tour targeting
  useEffect(() => {
    // Add data-storage-header class to the header
    const header = document.querySelector('h1')
    if (header) {
      header.classList.add('data-storage-header')
    }

    // Find database cards and buttons by their content
    document
      .querySelectorAll('.database-card, .card, [data-entity]')
      .forEach((card) => {
        const cardText = card.textContent?.trim().toLowerCase() || ''

        if (cardText.includes('student')) {
          card.classList.add('student-records-section')
        }

        if (cardText.includes('grade') || cardText.includes('academic')) {
          card.classList.add('academic-history-section')
        }

        if (cardText.includes('attendance')) {
          card.classList.add('attendance-section')
        }
      })
  }, [])

  return (
    <div className="container mx-auto p-4">
      <Outlet />
    </div>
  )
}
