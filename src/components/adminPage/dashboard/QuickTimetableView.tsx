import { useState, useEffect } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Skeleton } from '@/components/ui/skeleton'
import { ArrowRight, Trash2, AlertCircle } from 'lucide-react'
import {
  dashboardTimetableService,
  Timetable,
} from '@/lib/api/services/dashboard-timetable-service'
import { useNavigate } from '@tanstack/react-router'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

export function QuickTimetableView() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const [viewType, setViewType] = useState<'class' | 'teacher' | 'room'>(
    'class'
  )
  const [selectedEntity, setSelectedEntity] = useState<string>('')
  const [previewData, setPreviewData] = useState<any>(null)
  const [availableClasses, setAvailableClasses] = useState<string[]>([])
  const [availableTeachers, setAvailableTeachers] = useState<string[]>([])
  const [availableRooms, setAvailableRooms] = useState<string[]>([])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedTimetable, setSelectedTimetable] = useState<Timetable | null>(
    null
  )

  // Fetch active timetable
  const {
    data: timetables,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['active-timetable'],
    queryFn: async () => {
      const allTimetables =
        await dashboardTimetableService.getCurrentEstablishmentTimetables()
      // Find the active timetable
      return allTimetables.filter((t) => t.isActive)
    },
  })

  // Process timetable data when it changes
  useEffect(() => {
    if (!timetables || timetables.length === 0) {
      console.log('No timetables available')
      return
    }

    const activeTimetable = timetables[0]
    if (!activeTimetable || !activeTimetable.data) {
      console.log('No active timetable data available')
      return
    }

    console.log('Processing active timetable:', activeTimetable.id)
    setSelectedTimetable(activeTimetable)
    const timetableData = activeTimetable.data

    // Extract entities from the timetable data
    if (timetableData.scheduleData) {
      const classes = new Set<string>()
      const teachers = new Set<string>()
      const rooms = new Set<string>()

      timetableData.scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class
        classes.add(className)

        Object.entries(classSchedule).forEach(([day, periods]) => {
          if (day !== 'class' && Array.isArray(periods)) {
            periods.forEach((period: any) => {
              if (period.subject) {
                const teacher = period.teacher || 'Unknown Teacher'
                const room = period.salle || 'Default Room'
                teachers.add(teacher)
                rooms.add(room)
              }
            })
          }
        })
      })

      console.log(
        `Found ${classes.size} classes, ${teachers.size} teachers, ${rooms.size} rooms`
      )
      setAvailableClasses(Array.from(classes))
      setAvailableTeachers(Array.from(teachers))
      setAvailableRooms(Array.from(rooms))

      // Set default entity if not already set
      if (classes.size > 0 && selectedEntity === '') {
        setSelectedEntity(Array.from(classes)[0])
      }

      // Create preview data
      createPreviewData(activeTimetable)
    } else {
      console.log('No scheduleData found in timetable')
    }
  }, [timetables, selectedEntity])

  // Create preview data for the timetable
  const createPreviewData = (timetable: Timetable) => {
    if (!timetable || !timetable.data || !timetable.data.scheduleData) return

    const timetableData = timetable.data
    const entity =
      selectedEntity || (availableClasses.length > 0 ? availableClasses[0] : '')

    if (!entity) return

    // Create a simplified preview with just 2 time slots
    const previewData: any = {
      description: timetable.description,
      academicYear: timetable.academicYear,
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      timeSlots: [
        { period: 1, label: '8h30 - 10h00' },
        { period: 2, label: '10h15 - 11h45' },
      ],
      schedule: {},
    }

    // Initialize schedule structure
    previewData.days.forEach((day: string) => {
      previewData.schedule[day] = {}
      previewData.timeSlots.forEach((slot: any) => {
        previewData.schedule[day][slot.period] = null
      })
    })

    // Fill in the schedule based on view type
    if (viewType === 'class') {
      const classSchedule = timetableData.scheduleData.find(
        (data: any) => data.class === entity
      )
      if (classSchedule) {
        previewData.days.forEach((day: string) => {
          if (classSchedule[day] && Array.isArray(classSchedule[day])) {
            previewData.timeSlots.forEach((slot: any, index: number) => {
              if (
                classSchedule[day][index] &&
                classSchedule[day][index].subject
              ) {
                previewData.schedule[day][slot.period] = {
                  subject: classSchedule[day][index].subject,
                  teacher: classSchedule[day][index].teacher,
                  room: classSchedule[day][index].salle,
                }
              }
            })
          }
        })
      }
    } else {
      // For teacher and room views, we need to scan all classes
      timetableData.scheduleData.forEach((classSchedule: any) => {
        const className = classSchedule.class

        previewData.days.forEach((day: string) => {
          if (classSchedule[day] && Array.isArray(classSchedule[day])) {
            previewData.timeSlots.forEach((slot: any, index: number) => {
              const period = classSchedule[day][index]
              if (period && period.subject) {
                const teacher = period.teacher || 'Unknown Teacher'
                const room = period.salle || 'Default Room'

                if (
                  (viewType === 'teacher' && teacher === entity) ||
                  (viewType === 'room' && room === entity)
                ) {
                  previewData.schedule[day][slot.period] = {
                    subject: period.subject,
                    teacher: teacher,
                    room: room,
                    class: className,
                  }
                }
              }
            })
          }
        })
      })
    }

    setPreviewData(previewData)
  }

  // Handle view type change
  const handleViewTypeChange = (type: 'class' | 'teacher' | 'room') => {
    setViewType(type)
    // Reset selected entity when changing view type
    if (type === 'class' && availableClasses.length > 0) {
      setSelectedEntity(availableClasses[0])
    } else if (type === 'teacher' && availableTeachers.length > 0) {
      setSelectedEntity(availableTeachers[0])
    } else if (type === 'room' && availableRooms.length > 0) {
      setSelectedEntity(availableRooms[0])
    }
  }

  // Handle delete timetable
  const handleDeleteTimetable = async () => {
    if (!selectedTimetable) {
      toast({
        title: 'Error',
        description: 'No timetable selected for deletion',
        variant: 'destructive',
      })
      return
    }

    try {
      console.log(
        'Attempting to delete timetable with ID:',
        selectedTimetable.id
      )

      // Make sure we have the ID
      if (!selectedTimetable.id) {
        throw new Error('Timetable ID is missing')
      }

      // Call the delete endpoint directly to the NestJS server
      await dashboardTimetableService.deleteTimetable(selectedTimetable.id)
      console.log('Delete request sent successfully')

      toast({
        title: 'Timetable Deleted',
        description: 'The timetable has been successfully deleted.',
      })

      // Refresh the data
      queryClient.invalidateQueries({ queryKey: ['active-timetable'] })
      setDeleteDialogOpen(false)
      setSelectedTimetable(null)
      setPreviewData(null)
      setAvailableClasses([])
      setAvailableTeachers([])
      setAvailableRooms([])
      refetch()
    } catch (error) {
      console.error('Error deleting timetable:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete the timetable. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Navigate to full timetable view
  const handleViewFullTimetable = () => {
    navigate({ to: '/admin/timetable' })
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Quick Timetable View</CardTitle>
          <CardDescription>
            View the current timetable by class, teacher, or room
          </CardDescription>
        </div>
        {selectedTimetable && (
          <Button
            variant="outline"
            size="sm"
            className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
            onClick={() => setDeleteDialogOpen(true)}
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Delete
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : !timetables || timetables.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No Active Timetable</h3>
            <p className="text-muted-foreground max-w-md mt-2">
              You don't have an active timetable. Create a timetable and set it
              as active.
            </p>
            <Button className="mt-4" onClick={handleViewFullTimetable}>
              Create Timetable
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* View Type Selection */}
            <div className="flex space-x-2 mb-4">
              <Button
                variant={viewType === 'class' ? 'default' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
                onClick={() => handleViewTypeChange('class')}
              >
                Class
              </Button>
              <Button
                variant={viewType === 'teacher' ? 'default' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
                onClick={() => handleViewTypeChange('teacher')}
              >
                Teacher
              </Button>
              <Button
                variant={viewType === 'room' ? 'default' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
                onClick={() => handleViewTypeChange('room')}
              >
                Room
              </Button>
            </div>

            {/* Entity Selection */}
            <div className="mb-4">
              <select
                value={selectedEntity}
                onChange={(e) => setSelectedEntity(e.target.value)}
                className="w-full p-2 border rounded"
              >
                {viewType === 'class' &&
                  availableClasses.map((cls) => (
                    <option key={cls} value={cls}>
                      {cls}
                    </option>
                  ))}
                {viewType === 'teacher' &&
                  availableTeachers.map((teacher) => (
                    <option key={teacher} value={teacher}>
                      {teacher}
                    </option>
                  ))}
                {viewType === 'room' &&
                  availableRooms.map((room) => (
                    <option key={room} value={room}>
                      {room}
                    </option>
                  ))}
              </select>
            </div>

            {/* Timetable Preview */}
            {previewData ? (
              <div className="overflow-x-auto border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Time</TableHead>
                      {previewData.days.map((day: string) => (
                        <TableHead key={day}>{day}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {previewData.timeSlots.map((slot: any) => (
                      <TableRow key={slot.period}>
                        <TableCell className="font-medium">
                          {slot.label}
                        </TableCell>
                        {previewData.days.map((day: string) => {
                          const cell = previewData.schedule[day][slot.period]
                          return (
                            <TableCell
                              key={`${day}-${slot.period}`}
                              className={cell ? 'bg-blue-50 p-2' : 'p-2'}
                            >
                              {cell ? (
                                <div>
                                  <div className="font-medium">
                                    {cell.subject}
                                  </div>
                                  {viewType === 'class' ? (
                                    <div className="text-xs text-gray-500">
                                      {cell.teacher}
                                    </div>
                                  ) : viewType === 'teacher' ? (
                                    <div className="text-xs text-gray-500">
                                      {cell.class}
                                    </div>
                                  ) : (
                                    <div className="text-xs text-gray-500">
                                      {cell.class}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="text-xs text-gray-400">-</div>
                              )}
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="flex justify-center items-center h-64">
                <p className="text-muted-foreground">
                  No data available for the selected view
                </p>
              </div>
            )}

            {/* View Full Timetable Button */}
            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewFullTimetable}
                className="flex items-center"
              >
                View Complete Schedule
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Timetable</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this timetable? This action
                cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteTimetable}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
