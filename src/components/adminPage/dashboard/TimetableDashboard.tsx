import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Edit,
  Eye,
  MoreVertical,
  Plus,
  RefreshCw,
  Trash2,
  FileText,
  XCircle,
  Loader2,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  dashboardTimetableService,
  Timetable,
} from '@/lib/api/services/dashboard-timetable-service'
import { TimetableDisplay } from '../timetable/TimetableDisplay'

export function TimetableDashboard() {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [selectedTimetable, setSelectedTimetable] = useState<Timetable | null>(
    null
  )
  const [viewTimetable, setViewTimetable] = useState<boolean>(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false)
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false)
  const [editForm, setEditForm] = useState({
    description: '',
    academicYear: '',
    isActive: false,
  })

  // Fetch timetables
  const {
    data: timetables,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['timetables'],
    queryFn: dashboardTimetableService.getCurrentEstablishmentTimetables,
  })

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: dashboardTimetableService.deleteTimetable,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timetables'] })
      toast({
        title: 'Timetable Deleted',
        description: 'The timetable has been successfully deleted.',
      })
      setDeleteDialogOpen(false)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete timetable: ${error}`,
        variant: 'destructive',
      })
    },
  })

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Timetable> }) =>
      dashboardTimetableService.updateTimetable(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timetables'] })
      toast({
        title: 'Timetable Updated',
        description: 'The timetable has been successfully updated.',
      })
      setEditDialogOpen(false)
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update timetable: ${error}`,
        variant: 'destructive',
      })
    },
  })

  // Handle delete
  const handleDelete = (timetable: Timetable) => {
    setSelectedTimetable(timetable)
    setDeleteDialogOpen(true)
  }

  // Handle edit
  const handleEdit = (timetable: Timetable) => {
    setSelectedTimetable(timetable)
    setEditForm({
      description: timetable.description || '',
      academicYear: timetable.academicYear || '',
      isActive: timetable.isActive,
    })
    setEditDialogOpen(true)
  }

  // Handle view
  const handleView = (timetable: Timetable) => {
    setSelectedTimetable(timetable)
    setViewTimetable(true)
  }

  // Handle save edit
  const handleSaveEdit = () => {
    if (!selectedTimetable) return

    updateMutation.mutate({
      id: selectedTimetable.id,
      data: editForm,
    })
  }

  // Handle confirm delete
  const handleConfirmDelete = () => {
    if (!selectedTimetable) return
    deleteMutation.mutate(selectedTimetable.id)
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a')
    } catch (e) {
      return dateString
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Timetables</h2>
          <p className="text-muted-foreground">
            View and manage your school timetables
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => (window.location.href = '/admin/timetable')}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New
          </Button>
        </div>
      </div>

      <Separator />

      {viewTimetable && selectedTimetable ? (
        <div className="space-y-4">
          <Button variant="outline" onClick={() => setViewTimetable(false)}>
            Back to List
          </Button>
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedTimetable.description || 'Timetable'}
                {selectedTimetable.academicYear && (
                  <span className="ml-2 text-muted-foreground text-sm">
                    ({selectedTimetable.academicYear})
                  </span>
                )}
              </CardTitle>
              <CardDescription>
                Created on {formatDate(selectedTimetable.createdAt)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimetableDisplay
                timetableData={selectedTimetable.data}
                onBack={() => setViewTimetable(false)}
                onRegenerate={() => {
                  toast({
                    title: 'Regenerate Not Available',
                    description:
                      'To regenerate, please create a new timetable.',
                  })
                }}
              />
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Your Timetables</CardTitle>
            <CardDescription>
              Manage your school's timetables for different academic years
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <XCircle className="h-12 w-12 text-destructive mb-4" />
                <h3 className="text-lg font-medium">
                  Error Loading Timetables
                </h3>
                <p className="text-muted-foreground max-w-md mt-2">
                  There was a problem loading your timetables. Please try again.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => refetch()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            ) : timetables && timetables.length > 0 ? (
              <ScrollArea className="h-[500px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead>Academic Year</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Updated</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {timetables.map((timetable) => (
                      <TableRow key={timetable.id}>
                        <TableCell className="font-medium">
                          {timetable.description || 'Untitled Timetable'}
                        </TableCell>
                        <TableCell>
                          {timetable.academicYear || 'Not specified'}
                        </TableCell>
                        <TableCell>
                          {timetable.isActive ? (
                            <Badge variant="default" className="bg-green-500">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline">Inactive</Badge>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(timetable.createdAt)}</TableCell>
                        <TableCell>{formatDate(timetable.updatedAt)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleView(timetable)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleEdit(timetable)}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDelete(timetable)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Timetables Found</h3>
                <p className="text-muted-foreground max-w-md mt-2">
                  You haven't created any timetables yet. Create your first
                  timetable to get started.
                </p>
                <Button
                  className="mt-4"
                  onClick={() => (window.location.href = '/admin/timetable')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Timetable
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Timetable</DialogTitle>
            <DialogDescription>
              Update the details of your timetable
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                value={editForm.description}
                onChange={(e) =>
                  setEditForm({ ...editForm, description: e.target.value })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="academicYear" className="text-right">
                Academic Year
              </Label>
              <Input
                id="academicYear"
                value={editForm.academicYear}
                onChange={(e) =>
                  setEditForm({ ...editForm, academicYear: e.target.value })
                }
                className="col-span-3"
                placeholder="e.g. 2023-2024"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="isActive" className="text-right">
                Active
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="isActive"
                  checked={editForm.isActive}
                  onCheckedChange={(checked) =>
                    setEditForm({ ...editForm, isActive: checked })
                  }
                />
                <Label htmlFor="isActive">
                  {editForm.isActive ? 'Active' : 'Inactive'}
                </Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveEdit}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this timetable? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
