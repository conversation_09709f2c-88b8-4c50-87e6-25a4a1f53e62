import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Class, GradeLevel, Teacher } from '@/interface/types'

const classSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  gradeId: z.string().min(1, 'Grade is required'),
  supervisorId: z.string().min(1, 'Supervisor is required'),
})

type ClassFormValues = z.infer<typeof classSchema>
export type { ClassFormValues }

interface ClassFormProps {
  initialData?: Partial<Class>
  grades: GradeLevel[]
  teachers: Teacher[]
  onSubmit: (data: ClassFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing classes
 * @param {ClassFormProps} props - The props for the ClassForm component
 * @returns {JSX.Element} The rendered ClassForm component
 */
export function ClassForm({
  initialData,
  grades,
  teachers,
  onSubmit,
  onCancel,
  submitLabel,
}: ClassFormProps) {
  const form = useForm<ClassFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: initialData?.name || '',
      gradeId: initialData?.gradeId || '',
      supervisorId: initialData?.supervisorId || '',
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 gap-4">
        <form.Field
          name="name"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Name is required'
              if (value.length < 2) return 'Name must be at least 2 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="name">Class Name</Label>
              <Input
                id="name"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter class name"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="gradeId"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Grade is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="gradeId">Grade</Label>
              <Select
                value={field.state.value}
                onValueChange={field.handleChange}
              >
                <SelectTrigger id="gradeId">
                  <SelectValue placeholder="Select grade" />
                </SelectTrigger>
                <SelectContent>
                  {grades.map((grade) => (
                    <SelectItem key={grade.id} value={String(grade.id)}>
                      {grade.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>

        <form.Field
          name="supervisorId"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Supervisor is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="supervisorId">Supervisor</Label>
              <Select
                value={field.state.value}
                onValueChange={field.handleChange}
              >
                <SelectTrigger id="supervisorId">
                  <SelectValue placeholder="Select supervisor" />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={String(teacher.id)}>
                      {teacher.firstname} {teacher.lastname}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
