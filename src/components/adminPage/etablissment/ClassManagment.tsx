import { useState } from 'react'
import { useQ<PERSON>y, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'
import { Class, GradeLevel, Teacher } from '@/interface/types'
import { services } from '@/lib/api'
import { classService, gradeService } from '@/lib/api/services'
import { Loader2, Plus, Pencil, Trash2, GraduationCap } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  CustomPagination as Pagination,
  CustomPaginationContent as PaginationContent,
  CustomPaginationEllipsis as Pagination<PERSON>llipsis,
  CustomPaginationItem as PaginationItem,
  CustomPaginationLink as PaginationLink,
  CustomPaginationNext as PaginationNext,
  CustomPaginationPrevious as PaginationPrevious,
} from '@/components/ui/custom-pagination'
import { ClassForm, ClassFormValues } from './ClassForm'

/**
 * Class Management component for administrators
 * @returns {JSX.Element} The rendered ClassManagement component
 */
export default function ClassManagement(): JSX.Element {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [isAddClassOpen, setIsAddClassOpen] = useState(false)
  const [isEditClassOpen, setIsEditClassOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [classToDelete, setClassToDelete] = useState<Class | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')

  // Queries
  const {
    data: classes = [],
    isLoading: isLoadingClasses,
    isError: isClassesError,
  } = useQuery<Class[]>({
    queryKey: ['classes'],
    queryFn: async () => {
      try {
        // Add error handling to prevent 'class is undefined' error
        const data = await classService.getAll()
        return data || []
      } catch (error) {
        console.error('Error fetching classes:', error)
        return []
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const { data: grades = [], isLoading: isLoadingGrades, refetch: refetchGrades } = useQuery<
    GradeLevel[]
  >({
    queryKey: ['grades'],
    queryFn: async () => {
      try {
        console.log('ClassManagement: Fetching grades with React Query')
        // Try using the grade service first
        try {
          const data = await gradeService.getAll()
          console.log('Grades fetched successfully:', data)
          return Array.isArray(data) ? data : data ? [data] : []
        } catch (gradeError) {
          console.log(
            'Error fetching from grade service, trying direct API call:',
            gradeError
          )
          // Fall back to direct API call
          const token = localStorage.getItem('access_token')
          const apiUrl =
            import.meta.env.VITE_API_URL || 'http://localhost:3000'
          const response = await fetch(`${apiUrl}/grade`, {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const data = await response.json()
          return Array.isArray(data) ? data : data ? [data] : []
        }
      } catch (error) {
        console.error('Error fetching grade levels:', error)
        return []
      }
    },
    staleTime: 0, // Set to 0 to always refetch when component mounts
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  })

  const { data: teachers = [], isLoading: isLoadingTeachers } = useQuery<
    Teacher[]
  >({
    queryKey: ['teachers'],
    queryFn: async () => {
      try {
        const data = await services.teacher.getAll()
        return data || []
      } catch (error) {
        console.error('Error fetching teachers:', error)
        return []
      }
    },
    staleTime: 1000 * 60 * 5,
  })

  // Mutations
  const updateClassMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      classService.update(String(id), data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      setIsEditClassOpen(false)
      setSelectedClass(null)
      toast({
        title: 'Success',
        description: 'Class updated successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update class: ${error}`,
        variant: 'destructive',
      })
    },
  })

  const createClassMutation = useMutation({
    mutationFn: (data: any) => classService.create(data),
    onSuccess: () => {
      // Invalidate both classes and grades queries
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      queryClient.invalidateQueries({ queryKey: ['grades'] })

      // Also manually refetch grades to ensure they're up to date
      refetchGrades()

      setIsAddClassOpen(false)
      toast({
        title: 'Success',
        description: 'Class created successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to create class: ${error}`,
        variant: 'destructive',
      })
    },
  })

  const deleteClassMutation = useMutation({
    mutationFn: (id: string) => classService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] })
      setIsDeleteDialogOpen(false)
      setClassToDelete(null)
      toast({
        title: 'Success',
        description: 'Class deleted successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete class: ${error}`,
        variant: 'destructive',
      })
    },
  })

  // Handle creating a new class
  const handleCreateClass = async (data: ClassFormValues) => {
    try {
      // According to the API documentation, we should only send name, gradeId, and supervisorId
      // The etablissementId is automatically set from the JWT token
      const createData = {
        name: data.name,
        gradeId: data.gradeId,
        supervisorId: data.supervisorId,
        // Do NOT send etablissementId as it's handled by the backend
      }

      console.log('Creating class with data:', createData)

      await createClassMutation.mutateAsync(createData)
    } catch (error) {
      console.error('Failed to create class:', error)
      toast({
        title: 'Error',
        description: `Failed to create class: ${error}`,
        variant: 'destructive',
      })
    }
  }

  // Handle updating a class
  const handleUpdateClass = async (data: ClassFormValues) => {
    if (!selectedClass?.id) return

    try {
      const updateData = {
        name: data.name,
        gradeId: data.gradeId,
        supervisorId: data.supervisorId,
      }

      console.log('Updating class with data:', {
        id: selectedClass.id,
        data: updateData,
      })

      await updateClassMutation.mutateAsync({
        id: String(selectedClass.id),
        data: updateData,
      })
    } catch (error) {
      console.error('Failed to update class:', error)
      toast({
        title: 'Error',
        description: `Failed to update class: ${error}`,
        variant: 'destructive',
      })
    }
  }

  // Open dialog for editing a class
  const handleShowClass = (cls: Class) => {
    setSelectedClass(cls)
    setIsEditClassOpen(true)
  }

  // Open dialog for deleting a class
  const handleDeleteClass = (cls: Class) => {
    setClassToDelete(cls)
    setIsDeleteDialogOpen(true)
  }

  // Confirm deletion
  const confirmDelete = () => {
    if (classToDelete) {
      console.log('Deleting class with ID:', classToDelete.id)
      deleteClassMutation.mutate(classToDelete.id.toString())
    }
  }

  // Filter classes based on search query
  const filteredClasses = classes.filter(
    (cls) =>
      searchQuery === '' ||
      cls.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Pagination
  const totalItems = filteredClasses.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems)
  const currentClasses = filteredClasses.slice(startIndex, endIndex)

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      // Calculate start and end of middle pages
      let start = Math.max(2, currentPage - 1)
      let end = Math.min(totalPages - 1, currentPage + 1)

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = 4
      } else if (currentPage >= totalPages - 1) {
        start = totalPages - 3
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push('ellipsis1')
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push('ellipsis2')
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (isLoadingClasses || isLoadingGrades || isLoadingTeachers)
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  if (isClassesError) return <div>Error loading classes. Please try again.</div>

  return (
    <div className="container mx-auto py-6 px-4">
      <Card className="border shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Classes Management</CardTitle>
            <CardDescription>
              Manage classes and assign teachers in your establishment
            </CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              {classes.length} Classes
            </Badge>
            <Button onClick={() => setIsAddClassOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Class
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search classes by name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {isLoadingClasses ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <Table>
                <TableCaption>
                  List of classes in your establishment
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Grade</TableHead>
                    <TableHead>Supervisor</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentClasses.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8">
                        No classes found. Click "Add Class" to create one.
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentClasses.map((cls) => {
                      const grade = grades.find((g) => g.id === cls.gradeId)
                      const supervisor = teachers.find(
                        (t) => t.id === cls.supervisorId
                      )

                      return (
                        <TableRow key={cls.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <GraduationCap className="h-4 w-4 text-muted-foreground" />
                              {cls.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {grade?.name || 'No Grade'}
                            </Badge>
                          </TableCell>
                          <TableCell className="max-w-xs truncate">
                            {supervisor
                              ? `${supervisor.firstname} ${supervisor.lastname}`
                              : 'No Supervisor'}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleShowClass(cls)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteClass(cls)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }
                          disabled={currentPage === 1}
                        />
                      </PaginationItem>

                      {getPageNumbers().map((page, index) =>
                        page === 'ellipsis1' || page === 'ellipsis2' ? (
                          <PaginationItem key={`ellipsis-${index}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => setCurrentPage(Number(page))}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(totalPages, prev + 1)
                            )
                          }
                          disabled={currentPage === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Class Dialog */}
      <Dialog open={isAddClassOpen} onOpenChange={setIsAddClassOpen}>
        <DialogContent className="sm:max-w-[600px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Add New Class</DialogTitle>
          </DialogHeader>
          <ClassForm
            grades={grades}
            teachers={teachers}
            onSubmit={handleCreateClass}
            onCancel={() => setIsAddClassOpen(false)}
            submitLabel="Add Class"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Class Dialog */}
      <Dialog open={isEditClassOpen} onOpenChange={setIsEditClassOpen}>
        <DialogContent className="sm:max-w-[600px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Edit Class</DialogTitle>
          </DialogHeader>
          {selectedClass && (
            <ClassForm
              initialData={selectedClass}
              grades={grades}
              teachers={teachers}
              onSubmit={handleUpdateClass}
              onCancel={() => setIsEditClassOpen(false)}
              submitLabel="Update Class"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border shadow-sm">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete the class "{classToDelete?.name}"?
            This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteClassMutation.isPending}
            >
              {deleteClassMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
