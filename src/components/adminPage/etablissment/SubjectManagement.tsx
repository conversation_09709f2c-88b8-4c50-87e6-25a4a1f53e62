import { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'
import { Subject, UpdateSubjectDTO } from '@/interface/types'
// Direct API calls are used instead of services
// Use direct API calls instead of service
import { Loader2, Plus, Pencil, Trash2, BookOpen } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  CustomPagination as Pagination,
  CustomPaginationContent as PaginationContent,
  CustomPaginationEllipsis as PaginationEllipsis,
  CustomPaginationItem as PaginationItem,
  CustomPaginationLink as PaginationLink,
  CustomPaginationNext as PaginationNext,
  CustomPaginationPrevious as PaginationPrevious,
} from '@/components/ui/custom-pagination'
import { SubjectForm, SubjectFormValues } from './SubjectForm'

export function SubjectManagement() {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [isAddSubjectOpen, setIsAddSubjectOpen] = useState(false)
  const [isEditSubjectOpen, setIsEditSubjectOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null)
  const [subjectToDelete, setSubjectToDelete] = useState<Subject | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')

  // State for subjects data
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)

  // Function to fetch subjects
  const fetchSubjects = async () => {
    setIsLoading(true)
    setIsError(false)

    try {
      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      // Try the plural endpoint first
      try {
        // Skip the plural endpoint attempt and go directly to the singular endpoint
        throw new Error('Skipping plural endpoint')

        // This code is unreachable due to the throw above, but we'll fix it for TypeScript
        // Using a dummy response variable to satisfy TypeScript
        const dummyResponse = new Response()
        if (!dummyResponse.ok) {
          throw new Error(
            `API returned ${dummyResponse.status}: ${dummyResponse.statusText}`
          )
        }

        const result = await dummyResponse.json()
        console.log('Subjects fetched successfully (plural endpoint):', result)

        setSubjects(Array.isArray(result) ? result : [])
      } catch (firstError) {
        console.error(
          'First API attempt failed, trying singular endpoint:',
          firstError
        )

        // Try the singular endpoint as fallback
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/subject`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          )

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const result = await response.json()
          console.log(
            'Subjects fetched successfully (singular endpoint):',
            result
          )

          setSubjects(Array.isArray(result) ? result : [])
        } catch (secondError) {
          console.error('Both API attempts failed:', secondError)
          console.log('Setting empty subjects array')

          // If both endpoints fail, set empty array
          setSubjects([])
        }
      }
    } catch (error) {
      console.error('Error fetching subjects:', error)
      setIsError(true)
      toast({
        title: 'Error',
        description: 'Failed to load subjects. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Effect to fetch subjects when component mounts
  useEffect(() => {
    fetchSubjects()
  }, [])

  // Function to refresh the subjects list
  const refreshSubjects = () => {
    fetchSubjects()
  }

  // Mutation to update a subject
  const updateMutation = useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string
      data: UpdateSubjectDTO
    }) => {
      const token = localStorage.getItem('access_token')
      const apiUrl =
        import.meta.env.VITE_API_URL || 'http://localhost:3000'

      const response = await fetch(`${apiUrl}/subject/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(
          `API returned ${response.status}: ${response.statusText}`
        )
      }

      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
      // Refresh the subjects list immediately
      fetchSubjects()
      setIsEditSubjectOpen(false)
      setSelectedSubject(null)
      toast({
        title: 'Success',
        description: 'Subject updated successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update subject: ${error}`,
        variant: 'destructive',
      })
    },
  })

  // Mutation to delete a subject
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const token = localStorage.getItem('access_token')
      const apiUrl =
        import.meta.env.VITE_API_URL || 'http://localhost:3000'

      const response = await fetch(`${apiUrl}/subject/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error(
          `API returned ${response.status}: ${response.statusText}`
        )
      }

      return true
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
      // Refresh the subjects list immediately
      fetchSubjects()
      setIsDeleteDialogOpen(false)
      setSubjectToDelete(null)
      toast({
        title: 'Success',
        description: 'Subject deleted successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete subject: ${error}`,
        variant: 'destructive',
      })
    },
  })

  // Handle creating a new subject
  const handleCreateSubject = async (data: SubjectFormValues) => {
    try {
      // No need to get etablissementId as it's not used in the API call

      // Create a simplified object with only the required properties
      const createData = {
        name: data.name,
        teacherIds: [], // Empty array as requested
      }

      console.log('Creating subject with data:', createData)

      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      // Make a direct API call instead of using the mutation
      try {
        // Skip the plural endpoint attempt and go directly to the singular endpoint
        throw new Error('Skipping plural endpoint')

        // This code is unreachable due to the throw above, but we'll fix it for TypeScript
        // Using a dummy response variable to satisfy TypeScript
        const dummyResponse = new Response()
        if (!dummyResponse.ok) {
          throw new Error(
            `API returned ${dummyResponse.status}: ${dummyResponse.statusText}`
          )
        }

        const result = await dummyResponse.json()
        console.log('Subject created successfully (plural endpoint):', result)

        // Refresh the subjects list
        fetchSubjects()
        setIsAddSubjectOpen(false)
        toast({
          title: 'Success',
          description: 'Subject created successfully',
        })
      } catch (firstApiError) {
        console.error(
          'First API attempt failed, trying singular endpoint:',
          firstApiError
        )

        // Try the singular endpoint as fallback
        try {
          const response = await fetch(
            `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/subject`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(createData),
            }
          )

          if (!response.ok) {
            throw new Error(
              `API returned ${response.status}: ${response.statusText}`
            )
          }

          const result = await response.json()
          console.log(
            'Subject created successfully (singular endpoint):',
            result
          )

          // Refresh the subjects list
          fetchSubjects()
          // Also invalidate the subjects query to ensure other components get updated
          queryClient.invalidateQueries({ queryKey: ['subjects'] })
          setIsAddSubjectOpen(false)
          toast({
            title: 'Success',
            description: 'Subject created successfully',
          })
        } catch (secondApiError) {
          console.error('Both API attempts failed:', secondApiError)
          throw new Error(
            'Failed to create subject after trying both endpoints'
          )
        }
      }
    } catch (error) {
      console.error('Failed to create subject:', error)
      toast({
        title: 'Error',
        description: 'Failed to create subject. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Handle updating a subject
  const handleUpdateSubject = async (data: SubjectFormValues) => {
    if (!selectedSubject?.id) return

    try {
      // Create a simplified object with only the required properties
      // Only include the fields that are actually needed by the API
      const updateData: UpdateSubjectDTO = {
        name: data.name,
        teacherIds: data.teacherIds || [],
        // Don't include etablissementId as it's handled by the backend
      }

      console.log('Updating subject with data:', {
        id: selectedSubject.id,
        data: updateData,
      })

      await updateMutation.mutateAsync({
        id: String(selectedSubject.id),
        data: updateData,
      })
    } catch (error) {
      console.error('Failed to update subject:', error)
      toast({
        title: 'Error',
        description: `Failed to update subject: ${error}`,
        variant: 'destructive',
      })
    }
  }

  // Open dialog for editing a subject
  const handleShowSubject = (subject: Subject) => {
    setSelectedSubject(subject)
    setIsEditSubjectOpen(true)
  }

  // Open dialog for deleting a subject
  const handleDeleteSubject = (subject: Subject) => {
    setSubjectToDelete(subject)
    setIsDeleteDialogOpen(true)
  }

  // Confirm deletion
  const confirmDelete = () => {
    if (subjectToDelete) {
      console.log('Deleting subject with ID:', subjectToDelete.id)
      deleteMutation.mutate(String(subjectToDelete.id))
    }
  }

  // Filter subjects based on search query
  const filteredSubjects = subjects.filter(
    (subject) =>
      searchQuery === '' ||
      (subject.name &&
        subject.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (subject.description &&
        subject.description.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Pagination
  const totalItems = filteredSubjects.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems)
  const currentSubjects = filteredSubjects.slice(startIndex, endIndex)

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      // Calculate start and end of middle pages
      let start = Math.max(2, currentPage - 1)
      let end = Math.min(totalPages - 1, currentPage + 1)

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = 4
      } else if (currentPage >= totalPages - 1) {
        start = totalPages - 3
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push('ellipsis1')
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push('ellipsis2')
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (isError) return <div>Error loading subjects. Please try again.</div>

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Subject Management</CardTitle>
            <CardDescription>
              Manage subjects and their properties in your establishment
            </CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              {filteredSubjects.length} Subjects
            </Badge>
            <Button onClick={() => setIsAddSubjectOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Subject
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="flex gap-2">
                <Input
                  placeholder="Search subjects by name or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
                <Button
                  variant="outline"
                  onClick={refreshSubjects}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-refresh-cw"
                    >
                      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                      <path d="M21 3v5h-5" />
                      <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                      <path d="M3 21v-5h5" />
                    </svg>
                  )}
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <Table>
                <TableCaption>
                  List of subjects in your establishment
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Passing Grade</TableHead>
                    <TableHead>Formula</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentSubjects.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No subjects found. Click "Add Subject" to create one.
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentSubjects.map((subject) => (
                      <TableRow key={subject.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            {subject.name}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {subject.description}
                        </TableCell>
                        <TableCell>
                          {subject.passingGrade ? (
                            <Badge variant="outline">
                              {subject.passingGrade}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">
                              Not set
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {subject.formula ? (
                            <code className="bg-muted px-1 py-0.5 rounded text-xs">
                              {subject.formula}
                            </code>
                          ) : (
                            <span className="text-muted-foreground">
                              No formula
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleShowSubject(subject)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteSubject(subject)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }
                          disabled={currentPage === 1}
                        />
                      </PaginationItem>

                      {getPageNumbers().map((page, index) =>
                        page === 'ellipsis1' || page === 'ellipsis2' ? (
                          <PaginationItem key={`ellipsis-${index}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => setCurrentPage(Number(page))}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(totalPages, prev + 1)
                            )
                          }
                          disabled={currentPage === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Subject Dialog */}
      <Dialog open={isAddSubjectOpen} onOpenChange={setIsAddSubjectOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Subject</DialogTitle>
          </DialogHeader>
          <SubjectForm
            onSubmit={handleCreateSubject}
            onCancel={() => setIsAddSubjectOpen(false)}
            submitLabel="Add Subject"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Subject Dialog */}
      <Dialog open={isEditSubjectOpen} onOpenChange={setIsEditSubjectOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Subject</DialogTitle>
          </DialogHeader>
          <SubjectForm
            initialData={selectedSubject || undefined}
            onSubmit={handleUpdateSubject}
            onCancel={() => setIsEditSubjectOpen(false)}
            submitLabel="Update Subject"
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete the subject "{subjectToDelete?.name}
            "? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
