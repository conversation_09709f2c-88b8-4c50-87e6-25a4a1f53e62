import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'
import { Grade } from '@/interface/types'
import { gradeService } from '@/lib/api/services'
import {
  Loader2,
  Plus,
  Pencil,
  Trash2,
  School,
  RefreshCw,
  Search,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import {
  CustomPagination as Pagination,
  CustomPaginationContent as PaginationContent,
  CustomPaginationEllipsis as PaginationEllipsis,
  CustomPaginationItem as PaginationItem,
  CustomPaginationLink as PaginationLink,
  CustomPaginationNext as PaginationNext,
  CustomPaginationPrevious as PaginationPrevious,
} from '@/components/ui/custom-pagination'
import { GradeForm, GradeFormValues } from './GradeForm'

export function GradeManagement() {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [isAddGradeOpen, setIsAddGradeOpen] = useState(false)
  const [isEditGradeOpen, setIsEditGradeOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedGrade, setSelectedGrade] = useState<Grade | null>(null)
  const [gradeToDelete, setGradeToDelete] = useState<Grade | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)

  // Use React Query for grades data
  const {
    data: grades = [],
    isLoading,
    isError,
    refetch: refetchGrades
  } = useQuery<Grade[]>({
    queryKey: ['grades'],
    queryFn: async () => {
      try {
        console.log('GradeManagement: Fetching grades with React Query')
        const result = await gradeService.getAll()
        console.log('Grades fetched successfully:', result)
        return Array.isArray(result) ? result : result ? [result] : []
      } catch (error) {
        console.error('Error fetching grades:', error)
        throw error
      }
    },
    staleTime: 0, // Set to 0 to always refetch when component mounts
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  })

  // Function to fetch grades - now just uses React Query's refetch
  const fetchGrades = async () => {
    console.log('Manually triggering grades refetch')
    try {
      await refetchGrades()
      console.log('Grades refetched successfully')
    } catch (error) {
      console.error('Error refetching grades:', error)
      toast({
        title: 'Error',
        description: 'Failed to load grades. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Function to refresh the grades list
  const refreshGrades = () => {
    fetchGrades()
  }

  // Create grade mutation
  const createGradeMutation = useMutation({
    mutationFn: async (data: GradeFormValues) => {
      // Create a simplified object with only the name property
      const createData = {
        name: data.name,
        // Do not include etablissementId as per user request
      }
      return await gradeService.create(createData)
    },
    onSuccess: () => {
      // Invalidate grades query to update all components using it
      queryClient.invalidateQueries({ queryKey: ['grades'] })

      // Also refetch grades immediately to update this component
      refetchGrades()

      setIsAddGradeOpen(false)
      toast({
        title: 'Success',
        description: 'Grade created successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to create grade:', error)
      toast({
        title: 'Error',
        description: 'Failed to create grade. Please try again.',
        variant: 'destructive',
      })
    }
  })

  // Handle creating a new grade
  const handleCreateGrade = async (data: GradeFormValues) => {
    try {
      console.log('Creating grade with data:', data)
      await createGradeMutation.mutateAsync(data)
    } catch (error) {
      // Error is handled in the mutation
      console.error('Error in handleCreateGrade:', error)
    }
  }

  // Update grade mutation
  const updateGradeMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string, data: GradeFormValues }) => {
      // Create a clean object with only the name property
      const updateData = {
        name: data.name,
        // Do not include etablissementId as per user request
      }
      return await gradeService.update(id, updateData)
    },
    onSuccess: () => {
      // Invalidate grades query to update all components using it
      queryClient.invalidateQueries({ queryKey: ['grades'] })

      // Also refetch grades immediately to update this component
      refetchGrades()

      setIsEditGradeOpen(false)
      setSelectedGrade(null)
      toast({
        title: 'Success',
        description: 'Grade updated successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to update grade:', error)
      toast({
        title: 'Error',
        description: 'Failed to update grade. Please try again.',
        variant: 'destructive',
      })
    }
  })

  // Handle updating a grade
  const handleUpdateGrade = async (data: GradeFormValues) => {
    if (!selectedGrade?.id) return

    try {
      console.log('Updating grade with data:', {
        id: selectedGrade.id,
        data: data,
      })

      await updateGradeMutation.mutateAsync({
        id: selectedGrade.id,
        data: data
      })
    } catch (error) {
      // Error is handled in the mutation
      console.error('Error in handleUpdateGrade:', error)
    }
  }

  // Open dialog for editing a grade
  const handleShowGrade = (grade: Grade) => {
    setSelectedGrade(grade)
    setIsEditGradeOpen(true)
  }

  // Open dialog for deleting a grade
  const handleDeleteGrade = (grade: Grade) => {
    setGradeToDelete(grade)
    setIsDeleteDialogOpen(true)
  }

  // Delete grade mutation
  const deleteGradeMutation = useMutation({
    mutationFn: async (id: string) => {
      return await gradeService.delete(id)
    },
    onSuccess: () => {
      // Invalidate grades query to update all components using it
      queryClient.invalidateQueries({ queryKey: ['grades'] })

      // Also refetch grades immediately to update this component
      refetchGrades()

      setIsDeleteDialogOpen(false)
      setGradeToDelete(null)
      toast({
        title: 'Success',
        description: 'Grade deleted successfully',
      })
    },
    onError: (error) => {
      console.error('Failed to delete grade:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete grade. Please try again.',
        variant: 'destructive',
      })
    },
    onSettled: () => {
      setIsDeleting(false)
    }
  })

  // Confirm deletion
  const confirmDelete = async () => {
    if (gradeToDelete) {
      try {
        setIsDeleting(true)
        console.log('Deleting grade with ID:', gradeToDelete.id)

        await deleteGradeMutation.mutateAsync(gradeToDelete.id)
      } catch (error) {
        // Error is handled in the mutation
        console.error('Error in confirmDelete:', error)
      }
    }
  }

  // Filter grades based on search query
  const filteredGrades = grades.filter(
    (grade) =>
      searchQuery === '' ||
      grade.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Pagination
  const totalItems = filteredGrades.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems)
  const currentGrades = filteredGrades.slice(startIndex, endIndex)

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      // Calculate start and end of middle pages
      let start = Math.max(2, currentPage - 1)
      let end = Math.min(totalPages - 1, currentPage + 1)

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = 4
      } else if (currentPage >= totalPages - 1) {
        start = totalPages - 3
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push('ellipsis1')
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push('ellipsis2')
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (isError) return <div>Error loading grades. Please try again.</div>

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Grade Management</CardTitle>
            <CardDescription>
              Manage grade levels in your establishment
            </CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="text-sm">
              {filteredGrades.length} Grades
            </Badge>
            <Button onClick={() => setIsAddGradeOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Grade
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="flex gap-2">
                <div className="relative w-full">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search grades by name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-8"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={refreshGrades}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <Table>
                <TableCaption>
                  List of grade levels in your establishment
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Classes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentGrades.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8">
                        No grades found. Click "Add Grade" to create one.
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentGrades.map((grade) => (
                      <TableRow key={grade.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <School className="h-4 w-4 text-primary mr-2" />
                            {grade.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          {grade.classes && grade.classes.length > 0 ? (
                            <Badge variant="outline">
                              {grade.classes.length} Classes
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">
                              No classes
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleShowGrade(grade)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteGrade(grade)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(1, prev - 1))
                          }
                          disabled={currentPage === 1}
                        />
                      </PaginationItem>

                      {getPageNumbers().map((page, index) =>
                        page === 'ellipsis1' || page === 'ellipsis2' ? (
                          <PaginationItem key={`ellipsis-${index}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => setCurrentPage(Number(page))}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(totalPages, prev + 1)
                            )
                          }
                          disabled={currentPage === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Grade Dialog */}
      <Dialog open={isAddGradeOpen} onOpenChange={setIsAddGradeOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Grade</DialogTitle>
          </DialogHeader>
          <GradeForm
            onSubmit={handleCreateGrade}
            onCancel={() => setIsAddGradeOpen(false)}
            submitLabel="Add Grade"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Grade Dialog */}
      <Dialog open={isEditGradeOpen} onOpenChange={setIsEditGradeOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Grade</DialogTitle>
          </DialogHeader>
          <GradeForm
            initialData={selectedGrade || undefined}
            onSubmit={handleUpdateGrade}
            onCancel={() => setIsEditGradeOpen(false)}
            submitLabel="Update Grade"
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete the grade "{gradeToDelete?.name}
            "? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
