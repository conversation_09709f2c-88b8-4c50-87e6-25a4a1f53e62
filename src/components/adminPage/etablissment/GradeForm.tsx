import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Grade } from '@/interface/types'

const gradeSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  establishmentId: z.string().optional(),
  etablissementId: z.string().optional(),
})

export type GradeFormValues = z.infer<typeof gradeSchema>

interface GradeFormProps {
  initialData?: Partial<Grade>
  onSubmit: (data: GradeFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing grades
 * @param {GradeFormProps} props - The props for the GradeForm component
 * @returns {JSX.Element} The rendered GradeForm component
 */
export function GradeForm({
  initialData,
  onSubmit,
  onCancel,
  submitLabel,
}: GradeFormProps) {
  // Get the user's etablissementId from localStorage
  const etablissementId =
    localStorage.getItem('etablissementId') ||
    '11111111-1111-1111-1111-111111111111'

  const form = useForm<GradeFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: initialData?.name || '',
      establishmentId: initialData?.establishmentId || etablissementId,
      etablissementId: initialData?.etablissementId || etablissementId,
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 gap-4">
        <form.Field
          name="name"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Name is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="name">Grade Name</Label>
              <Input
                id="name"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Grade 1"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
