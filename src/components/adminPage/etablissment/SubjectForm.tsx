import { useForm } from '@tanstack/react-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Subject } from '@/interface/types'

const subjectSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  teacherIds: z.array(z.string()).optional(),
  description: z.string().optional(),
  formula: z.string().optional(),
  eliminationThreshold: z.coerce.number().optional(),
  passingGrade: z.coerce.number().optional(),
  etablissementId: z.string().optional(),
})

export type SubjectFormValues = z.infer<typeof subjectSchema>

interface SubjectFormProps {
  initialData?: Partial<Subject>
  onSubmit: (data: SubjectFormValues) => Promise<void>
  onCancel: () => void
  submitLabel: string
}

/**
 * A reusable form component for creating and editing subjects
 * @param {SubjectFormProps} props - The props for the SubjectForm component
 * @returns {JSX.Element} The rendered SubjectForm component
 */
export function SubjectForm({
  initialData,
  onSubmit,
  onCancel,
  submitLabel,
}: SubjectFormProps) {
  // Get the user's etablissementId from localStorage
  const etablissementId =
    localStorage.getItem('etablissementId') ||
    '11111111-1111-1111-1111-111111111111'

  const form = useForm<SubjectFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: initialData?.name || '',
      teacherIds: initialData?.teachers || [],
      description: initialData?.description || '',
      formula: initialData?.formula || '',
      eliminationThreshold: initialData?.eliminationThreshold || 0,
      passingGrade: initialData?.passingGrade || 0,
      etablissementId: initialData?.etablissementId || etablissementId,
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value)
    },
  })

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        void form.handleSubmit()
      }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 gap-4">
        <form.Field
          name="name"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Name is required'
              return undefined
            },
          }}
        >
          {(field) => (
            <div className="space-y-2">
              <Label htmlFor="name">Subject Name</Label>
              <Input
                id="name"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Mathematics"
              />
              {field.state.meta.errors && (
                <p className="text-sm text-red-500">
                  {field.state.meta.errors.join(', ')}
                </p>
              )}
            </div>
          )}
        </form.Field>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  )
}
