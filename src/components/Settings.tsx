import React, { useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  fetchUserProfile,
  updateUserPassword,
  updateProfileWithAvatar,
  UserProfile,
} from '@/lib/user/profile'

interface SettingsPageProps {
  jwt: string
}

const SettingsPage: React.FC<SettingsPageProps> = ({ jwt }) => {
  const [activeTab, setActiveTab] = useState<
    'profile' | 'security' | 'preferences'
  >('profile')
  const [formData, setFormData] = useState<Partial<UserProfile>>({})
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
  const [updateStatus, setUpdateStatus] = useState<{
    message: string
    type: 'success' | 'error' | 'none'
  }>({ message: '', type: 'none' })

  // Fetch user data
  const {
    data: userData,
    isLoading,
    isError,
    error: queryError,
    refetch,
  } = useQuery<UserProfile>({
    queryKey: ['userProfile', jwt],
    queryFn: () => fetchUserProfile(jwt),
  })

  // Update profile mutation
  const { mutate: updateProfile, isPending: isUpdatingProfile } = useMutation({
    mutationFn: (data: {
      profileData: Partial<UserProfile>
      avatarFile: File | null
    }) =>
      updateProfileWithAvatar(
        jwt,
        data.profileData,
        data.avatarFile || undefined
      ),
    onSuccess: () => {
      setUpdateStatus({
        message: 'Profile updated successfully!',
        type: 'success',
      })
      refetch() // Refresh the user data
      setFormData({}) // Reset the form data
      setAvatarFile(null) // Reset avatar file
      setAvatarPreview(null) // Reset avatar preview

      // Clear success message after 3 seconds
      setTimeout(() => {
        setUpdateStatus({ message: '', type: 'none' })
      }, 3000)
    },
    onError: (error: any) => {
      setUpdateStatus({
        message: 'Error updating profile: ' + error.message,
        type: 'error',
      })

      // Clear error message after 5 seconds
      setTimeout(() => {
        setUpdateStatus({ message: '', type: 'none' })
      }, 5000)
    },
  })

  // Update password mutation
  const passwordMutation = useMutation({
    mutationFn: () => {
      // The JWT should be accessed from localStorage or another source inside the function
      return updateUserPassword()
    },
    onSuccess: () => {
      alert('Password updated successfully')
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      })
    },
    onError: (error: any) => alert('Error updating password: ' + error.message),
  })

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (Object.keys(formData).length > 0 || avatarFile) {
      // Include the user ID in the form data if it's not already there
      const updatedFormData = { ...formData }
      if (!updatedFormData.id && userData) {
        updatedFormData.id = userData.id
      }

      // Call the mutation with both profile data and avatar file
      updateProfile({
        profileData: updatedFormData,
        avatarFile: avatarFile,
      })
    } else {
      setUpdateStatus({
        message: 'No changes to save',
        type: 'error',
      })

      // Clear message after 3 seconds
      setTimeout(() => {
        setUpdateStatus({ message: '', type: 'none' })
      }, 3000)
    }
  }

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match!')
      return
    }
    passwordMutation.mutate()
  }

  if (isLoading) return <div className="text-center py-8">Loading...</div>
  if (isError)
    return (
      <div className="text-center py-8 text-red-500">
        Error: {(queryError as Error).message}
      </div>
    )
  if (!userData)
    return <div className="text-center py-8">No user data found</div>

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Settings</h1>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6 overflow-x-auto">
        <button
          className={`py-2 px-4 font-medium whitespace-nowrap ${activeTab === 'profile' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
          onClick={() => setActiveTab('profile')}
        >
          Profile
        </button>
        <button
          className={`py-2 px-4 font-medium whitespace-nowrap ${activeTab === 'security' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
          onClick={() => setActiveTab('security')}
        >
          Security
        </button>
        <button
          className={`py-2 px-4 font-medium whitespace-nowrap ${activeTab === 'preferences' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-600'}`}
          onClick={() => setActiveTab('preferences')}
        >
          Preferences
        </button>
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
          <div className="flex flex-col md:flex-row items-center gap-6 mb-6">
            <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 flex-shrink-0 relative group">
              <img
                src={
                  avatarPreview ||
                  userData.avatar ||
                  `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.fullname || 'User')}&size=128&background=random`
                }
                alt={userData.fullname || 'User'}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
                <label
                  htmlFor="avatar-upload"
                  className="text-white text-sm font-medium text-center p-2 cursor-pointer"
                >
                  <span>Change Photo</span>
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        // Store the file for upload
                        setAvatarFile(file)

                        // Create a preview URL
                        const reader = new FileReader()
                        reader.onload = (event) => {
                          if (event.target?.result) {
                            setAvatarPreview(event.target.result as string)
                          }
                        }
                        reader.readAsDataURL(file)
                      }
                    }}
                  />
                </label>
              </div>
            </div>
            <div>
              <h2 className="text-2xl font-semibold">
                {userData.fullname ||
                  `${userData.firstname || ''} ${userData.lastname || ''}`.trim() ||
                  userData.email?.split('@')[0] ||
                  'User'}
              </h2>
              <p className="text-gray-600">{userData.role}</p>
              <p className="text-gray-600">{userData.email}</p>
              <div className="mt-2 text-sm text-gray-500">
                {userData.lastLogin && (
                  <p>
                    Last login: {new Date(userData.lastLogin).toLocaleString()}
                  </p>
                )}
                {userData.createdAt && (
                  <p>
                    Member since:{' '}
                    {new Date(userData.createdAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
          </div>

          {updateStatus.type !== 'none' && (
            <div
              className={`mb-4 p-3 rounded ${updateStatus.type === 'success' ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-red-100 text-red-800 border border-red-300'}`}
            >
              {updateStatus.message}
            </div>
          )}

          <form onSubmit={handleProfileSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  First Name
                </label>
                <input
                  type="text"
                  name="firstname"
                  value={
                    formData.firstname === undefined
                      ? userData.firstname || ''
                      : formData.firstname
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Last Name
                </label>
                <input
                  type="text"
                  name="lastname"
                  value={
                    formData.lastname === undefined
                      ? userData.lastname || ''
                      : formData.lastname
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={
                    formData.email === undefined
                      ? userData.email || ''
                      : formData.email
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled // Email is usually not changeable
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Phone
                </label>
                <input
                  type="text"
                  name="phone"
                  value={
                    formData.phone === undefined
                      ? userData.phone || ''
                      : formData.phone
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your phone number"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  CIN
                </label>
                <input
                  type="text"
                  name="cin"
                  value={
                    formData.cin === undefined
                      ? userData.cin || ''
                      : formData.cin
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your CIN"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Birthday
                </label>
                <input
                  type="date"
                  name="birthday"
                  value={
                    formData.birthday === undefined
                      ? userData.birthday || ''
                      : formData.birthday
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Gender
                </label>
                <select
                  name="gender"
                  value={
                    formData.gender === undefined
                      ? userData.gender || ''
                      : formData.gender
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div className="col-span-2">
                <label className="block text-gray-700 font-medium mb-1">
                  Address
                </label>
                <input
                  type="text"
                  name="address"
                  value={
                    formData.address === undefined
                      ? userData.address || ''
                      : formData.address
                  }
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your address"
                />
              </div>
            </div>

            <div className="mt-6 text-right">
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:bg-blue-300 disabled:cursor-not-allowed"
                disabled={
                  isUpdatingProfile ||
                  (Object.keys(formData).length === 0 && !avatarFile)
                }
              >
                {isUpdatingProfile
                  ? 'Saving...'
                  : avatarFile
                    ? 'Save Changes & Upload Avatar'
                    : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Change Password</h2>
          <form onSubmit={handlePasswordSubmit}>
            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Current Password
                </label>
                <input
                  type="password"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-1">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="mt-6">
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                disabled={
                  passwordMutation.isPending ||
                  !passwordData.newPassword ||
                  !passwordData.confirmPassword
                }
              >
                {passwordMutation.isPending ? 'Updating...' : 'Update Password'}
              </button>
            </div>
          </form>

          <hr className="my-8" />

          <h2 className="text-xl font-semibold mb-4">
            Two-Factor Authentication
          </h2>
          <p className="text-gray-600 mb-4">
            Enhance your account security by enabling two-factor authentication.
          </p>
          <button className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
            Enable 2FA
          </button>
        </div>
      )}

      {/* Preferences Tab */}
      {activeTab === 'preferences' && (
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Notification Settings</h2>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Email Notifications</h3>
                <p className="text-gray-600 text-sm">
                  Receive emails about your account activity
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  value=""
                  className="sr-only peer"
                  defaultChecked
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Assignment Notifications</h3>
                <p className="text-gray-600 text-sm">
                  Get notified when new assignments are posted
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  value=""
                  className="sr-only peer"
                  defaultChecked
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Grade Updates</h3>
                <p className="text-gray-600 text-sm">
                  Get notified when your grades are updated
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  value=""
                  className="sr-only peer"
                  defaultChecked
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          <hr className="my-8" />

          <h2 className="text-xl font-semibold mb-4">Theme Preference</h2>
          <div className="flex flex-wrap gap-4">
            <button className="px-4 py-2 bg-white border border-gray-300 rounded shadow-sm hover:bg-gray-50">
              Light Mode
            </button>
            <button className="px-4 py-2 bg-gray-800 text-white border border-gray-700 rounded shadow-sm hover:bg-gray-700">
              Dark Mode
            </button>
            <button className="px-4 py-2 bg-gray-100 border border-gray-300 rounded shadow-sm hover:bg-gray-200">
              System Default
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default SettingsPage
