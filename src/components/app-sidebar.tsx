import { Home, <PERSON><PERSON><PERSON>, <PERSON>, Settings } from 'lucide-react'
import { Link } from '@tanstack/react-router'

const navigation = [
  { name: 'Dashboard', icon: Home, href: '/', current: true },
  { name: 'Analytics', icon: <PERSON><PERSON><PERSON>, href: '/analytics' },
  { name: 'Users', icon: Users, href: '/users' },
  { name: 'Settings', icon: Settings, href: '/settings' },
]

export function AppSidebar() {
  return (
    <nav className="w-[240px] border-r p-4">
      <div className="space-y-1">
        {navigation.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={`flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium transition-colors ${
              item.current
                ? 'bg-primary text-primary-foreground'
                : 'hover:bg-muted'
            }`}
          >
            <item.icon className="h-4 w-4" />
            {item.name}
          </Link>
        ))}
      </div>
    </nav>
  )
}
