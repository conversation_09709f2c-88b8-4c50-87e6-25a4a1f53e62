import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { YearMonthPicker } from '@/components/ui/year-month-picker'
import { useToast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'
import { userService } from '@/lib/api/services/user-service'
import FileUploader from '@/components/shared/FileUploader'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'

// Define the type for the form data
interface ProfileFormData {
  firstname: string
  lastname: string
  email: string
  cin: string
  birthday: string
  gender: 'MALE' | 'FEMALE'
  address: string
  phone: string
  password: string
  avatar?: string
}

interface ProfileFormProps {
  onCancel?: () => void
}

export function ProfileForm({ onCancel }: ProfileFormProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<ProfileFormData>({
    firstname: '',
    lastname: '',
    email: '',
    cin: '',
    birthday: '',
    gender: 'MALE',
    address: '',
    phone: '',
    password: '',
    avatar: '',
  })
  const [birthdayDate, setBirthdayDate] = useState<Date | undefined>(undefined)
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true)
        const token = localStorage.getItem('access_token')

        if (!token) {
          throw new Error('No authentication token found')
        }

        const response = await fetch(`${API_URL}/user-controller/getme`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch profile data: ${response.statusText}`)
        }

        const userData = await response.json()
        console.log('User data fetched:', userData)

        // Format birthday as YYYY-MM-DD for the date input
        let formattedBirthday = userData.birthday || ''
        if (userData.birthday) {
          const date = new Date(userData.birthday)
          if (!isNaN(date.getTime())) {
            setBirthdayDate(date)
          }
        }

        // If the avatar URL is from our CDN, ensure it's properly proxied
        let avatarUrl = userData.avatar || ''
        if (avatarUrl && (avatarUrl.includes('cdn.jeridschool.tech') || avatarUrl.includes('**************'))) {
          if (!avatarUrl.includes('/health/proxy/image')) {
            avatarUrl = `${API_URL}/health/proxy/image?url=${encodeURIComponent(avatarUrl)}`
          }
        }

        setFormData({
          firstname: userData.firstname || '',
          lastname: userData.lastname || '',
          email: userData.email || '',
          cin: userData.cin || '',
          birthday: formattedBirthday,
          gender: (userData.gender as 'MALE' | 'FEMALE') || 'MALE',
          address: userData.address || '',
          phone: userData.phone || '',
          password: '',
          avatar: avatarUrl,
        })
      } catch (error) {
        console.error('Error fetching user data:', error)
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to load profile data',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [toast, API_URL])

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle date picker changes
  const handleDateChange = (date: Date | undefined) => {
    setBirthdayDate(date)
    if (date) {
      // Format as YYYY-MM-DD
      const formattedDate = date.toISOString().split('T')[0]
      setFormData(prev => ({ ...prev, birthday: formattedDate }))
    } else {
      setFormData(prev => ({ ...prev, birthday: '' }))
    }
  }

  // Handle avatar upload
  const handleAvatarUpload = (url: string) => {
    setFormData(prev => ({ ...prev, avatar: url }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setIsSubmitting(true)

      // Create a copy of the form data with optional password
      const dataToSubmit: {
        firstname: string;
        lastname: string;
        email: string;
        cin: string;
        birthday: string;
        gender: 'MALE' | 'FEMALE';
        address: string;
        phone: string;
        password?: string;
        avatar?: string;
      } = {
        ...formData
      }

      // Remove password if it's empty
      if (!dataToSubmit.password) {
        delete dataToSubmit.password;
      }

      // Submit the form data
      await userService.updateUserProfile(dataToSubmit)

      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      })

      if (onCancel) {
        onCancel()
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
     

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* First Name */}
        <div className="space-y-2">
          <label htmlFor="firstname" className="text-sm font-medium">
            First Name
          </label>
          <Input
            id="firstname"
            name="firstname"
            value={formData.firstname}
            onChange={handleChange}
            placeholder="Enter first name"
            required
          />
        </div>

        {/* Last Name */}
        <div className="space-y-2">
          <label htmlFor="lastname" className="text-sm font-medium">
            Last Name
          </label>
          <Input
            id="lastname"
            name="lastname"
            value={formData.lastname}
            onChange={handleChange}
            placeholder="Enter last name"
            required
          />
        </div>

        {/* Email */}
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Enter email"
            required
          />
        </div>

        {/* CIN */}
        <div className="space-y-2">
          <label htmlFor="cin" className="text-sm font-medium">
            CIN
          </label>
          <Input
            id="cin"
            name="cin"
            value={formData.cin}
            onChange={handleChange}
            placeholder="Enter CIN"
            required
          />
        </div>

        {/* Birthday */}
        <div className="space-y-2">
          <label htmlFor="birthday" className="text-sm font-medium">
            Birthday
          </label>
          <YearMonthPicker
            date={birthdayDate}
            setDate={handleDateChange}
            placeholder="Select birthday"
          />
        </div>

        {/* Gender */}
        <div className="space-y-2">
          <label htmlFor="gender" className="text-sm font-medium">
            Gender
          </label>
          <select
            id="gender"
            name="gender"
            value={formData.gender}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            required
          >
            <option value="MALE">Male</option>
            <option value="FEMALE">Female</option>
          </select>
        </div>

        {/* Address */}
        <div className="space-y-2">
          <label htmlFor="address" className="text-sm font-medium">
            Address
          </label>
          <Input
            id="address"
            name="address"
            value={formData.address}
            onChange={handleChange}
            placeholder="Enter your address"
          />
        </div>

        {/* Phone */}
        <div className="space-y-2">
          <label htmlFor="phone" className="text-sm font-medium">
            Phone Number
          </label>
          <Input
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            placeholder="Enter your phone number"
          />
        </div>

        {/* Password */}
        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            New Password (Optional)
          </label>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleChange}
            placeholder="Enter new password"
          />
        </div>
         {/* Avatar Upload */}
        <div className="flex justify-center mb-6">
          <FileUploader
            onFileUploaded={handleAvatarUpload}
            onError={(error) => {
              console.error('Upload error:', error)
              toast({
                title: 'Error',
                description: 'Failed to upload profile picture',
                variant: 'destructive',
              })
            }}
            previewUrl={formData.avatar}
            isAvatar={true}
            label="Profile Picture"
            defaultPreview={DEFAULT_AVATAR}
            previewComponent={(url) => (
              <CdnImage
                src={url}
                alt="Profile Picture"
                className="h-full w-full object-cover"
                fallbackSrc={DEFAULT_AVATAR}
              />
            )}
          />
        </div>
      </div>

      <div className="flex justify-end gap-4 pt-4">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Updating...
            </>
          ) : (
            'Update Profile'
          )}
        </Button>
      </div>
    </form>
  )
}
