import { Glitch404 } from './glitch-404'
import { GlitchySearch } from './glitchy-seach'
import { <PERSON> } from '@tanstack/react-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

export default function NotFound() {
  const { t } = useTranslation('common')

  // Log the 404 error for debugging
  useEffect(() => {
    console.error(
      '404 Page Not Found - Current path:',
      window.location.pathname
    )
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-xl overflow-hidden">
        <div className="relative h-64 w-full flex items-center justify-center bg-gray-200">
          <div className="text-9xl font-bold text-gray-300">
            {t('notfound-folder.index.title')}
          </div>
        </div>
        <div className="p-6 text-center">
          <Glitch404 />
          <p className="mt-2 text-gray-600">
            {t('notfound-folder.index.description')}
          </p>
          <div className="mt-4 p-3 bg-gray-100 rounded text-sm font-mono text-left">
            <p>{t('notfound-folder.index.debugInfo.title')}</p>
            <p>{t('notfound-folder.index.debugInfo.requestedPath')} {window.location.pathname}</p>
            <p>{t('notfound-folder.index.debugInfo.hostname')} {window.location.hostname}</p>
          </div>
          <GlitchySearch />
          <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
            >
              {t('notfound-folder.index.buttons.goBack')}
            </button>
            <Link
              to="/"
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-bold rounded transition duration-300"
            >
              {t('notfound-folder.index.buttons.home')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
