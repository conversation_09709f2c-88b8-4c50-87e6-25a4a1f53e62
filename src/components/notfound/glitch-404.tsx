import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

export function Glitch404() {
  const { t } = useTranslation('common')
  const [glitch, setGlitch] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setGlitch(true)
      setTimeout(() => setGlitch(false), 200)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  return (
    <h1 className={`text-4xl font-bold mb-4 ${glitch ? 'animate-glitch' : ''}`}>
      {t('notfound-folder.glitch404.title')}
    </h1>
  )
}
