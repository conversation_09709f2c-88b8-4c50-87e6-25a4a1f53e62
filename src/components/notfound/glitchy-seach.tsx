import type React from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export function GlitchySearch() {
  const { t } = useTranslation('common')
  const [search, setSearch] = useState('')
  const [glitch, setGlitch] = useState(false)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setGlitch(true)
    setTimeout(() => {
      setGlitch(false)
      setSearch('')
    }, 1000)
  }

  return (
    <form onSubmit={handleSearch} className="mt-4">
      <input
        type="text"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        placeholder={t('notfound-folder.notfound.glitchySearch.placeholder')}
        className={`w-full px-3 py-2 border rounded ${glitch ? 'animate-glitch-mild' : ''}`}
      />
    </form>
  )
}
