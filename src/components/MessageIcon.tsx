import React from 'react'
import { MessageSquare } from 'lucide-react'
import { Link } from '@tanstack/react-router'
import { useUnreadMessages } from '@/hooks/useUnreadMessages'

interface MessageIconProps {
  className?: string
}

const MessageIcon: React.FC<MessageIconProps> = ({ className = '' }) => {
  const { unreadCount } = useUnreadMessages()

  return (
    <Link to="/chat" className={`relative ${className}`}>
      <MessageSquare className="h-6 w-6 text-gray-600 hover:text-primary transition-colors" />
      {unreadCount > 0 && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-medium rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
          {unreadCount > 99 ? '99+' : unreadCount}
        </div>
      )}
    </Link>
  )
}

export default MessageIcon
