import { useState, useEffect } from 'react'
import { format, subDays, startOfWeek, endOfWeek } from 'date-fns'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Loader2,
  Search,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Eye,
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/components/ui/use-toast'
import {
  sessionService,
  SessionStatus,
} from '@/lib/api/services/session-service'
import { attendanceService } from '@/lib/api/services/attendance-service'

interface Session {
  id: string
  teacherId: string
  teacherName: string
  className: string
  classId?: string
  subjectName: string
  subjectId?: string
  day: string
  timeSlot: string
  date: string
  status: SessionStatus
  notes?: string
}

interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  gender?: string
}

interface AttendanceRecord {
  id: string
  studentId: string
  student: Student
  status: 'present' | 'absent' | 'left_early'
  notes?: string
}

export default function SessionHistoryView() {
  const [teacherId] = useState(localStorage.getItem('id') || '')
  const { toast } = useToast()
  const [sessions, setSessions] = useState<Session[]>([])
  const [filteredSessions, setFilteredSessions] = useState<Session[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(
    startOfWeek(new Date(), { weekStartsOn: 1 })
  )
  const [selectedSession, setSelectedSession] = useState<Session | null>(null)
  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([])
  const [isLoadingAttendance, setIsLoadingAttendance] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Fetch sessions for the current week
  useEffect(() => {
    const fetchSessions = async () => {
      if (!teacherId) return

      setIsLoadingSessions(true)
      try {
        const weekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 })
        const startDate = format(currentWeekStart, 'yyyy-MM-dd')
        const endDate = format(weekEnd, 'yyyy-MM-dd')

        // Get sessions for the date range
        const data = await sessionService.getByDateRange(startDate, endDate)
        setSessions(data)
        setFilteredSessions(data)
      } catch (error) {
        console.error('Error fetching sessions:', error)
        toast({
          title: 'Error',
          description: 'Failed to load sessions. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingSessions(false)
      }
    }

    fetchSessions()
  }, [teacherId, currentWeekStart, toast])

  // Filter sessions when search term or status filter changes
  useEffect(() => {
    let filtered = [...sessions]

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter((session) => session.status === statusFilter)
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (session) =>
          session.className.toLowerCase().includes(term) ||
          session.subjectName.toLowerCase().includes(term)
      )
    }

    setFilteredSessions(filtered)
  }, [sessions, searchTerm, statusFilter])

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentWeekStart((prevDate) => subDays(prevDate, 7))
  }

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentWeekStart((prevDate) => {
      const nextWeek = new Date(prevDate)
      nextWeek.setDate(nextWeek.getDate() + 7)
      return nextWeek
    })
  }

  // View attendance details for a session
  const viewAttendanceDetails = async (session: Session) => {
    setSelectedSession(session)
    setIsLoadingAttendance(true)

    try {
      // Get attendance records for the session
      const data = await attendanceService.getBySession(session.id)
      // Convert the data to the expected format
      const formattedData = data.map((record) => ({
        id: record.id,
        studentId: record.studentId,
        student: record.student || {
          id: record.studentId,
          firstname: 'Unknown',
          lastname: 'Student',
          email: '',
        },
        status: record.status as 'present' | 'absent' | 'left_early',
        notes: record.notes,
      }))
      setAttendanceRecords(formattedData)
      setIsDialogOpen(true)
    } catch (error) {
      console.error('Error fetching attendance records:', error)
      toast({
        title: 'Error',
        description: 'Failed to load attendance records. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingAttendance(false)
    }
  }

  // Get status badge for a session
  const getSessionStatusBadge = (status: SessionStatus) => {
    switch (status) {
      case SessionStatus.ONGOING:
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            Ongoing
          </Badge>
        )
      case SessionStatus.REPORTED:
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Reported
          </Badge>
        )
      case SessionStatus.CANCELED:
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Canceled
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Get status badge for attendance
  const getAttendanceStatusBadge = (status: string) => {
    switch (status) {
      case 'present':
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Present
          </Badge>
        )
      case 'absent':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Absent
          </Badge>
        )
      case 'left_early':
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800">
            Left Early
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Session History</CardTitle>
              <CardDescription>
                View your past sessions and attendance records
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="icon" onClick={goToPreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                {format(currentWeekStart, 'MMM d')} -{' '}
                {format(
                  endOfWeek(currentWeekStart, { weekStartsOn: 1 }),
                  'MMM d, yyyy'
                )}
              </span>
              <Button variant="outline" size="icon" onClick={goToNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by class or subject..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="h-10 w-full md:w-[180px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value={SessionStatus.ONGOING}>Ongoing</option>
              <option value={SessionStatus.REPORTED}>Reported</option>
              <option value={SessionStatus.CANCELED}>Canceled</option>
            </select>
          </div>

          {isLoadingSessions ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No Sessions Found</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your filters to see more results.'
                  : "You don't have any sessions for this week."}
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[500px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Class</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>
                        {format(new Date(session.date), 'EEE, MMM d')}
                      </TableCell>
                      <TableCell>{session.timeSlot}</TableCell>
                      <TableCell className="font-medium">
                        {session.className}
                      </TableCell>
                      <TableCell>{session.subjectName}</TableCell>
                      <TableCell>
                        {getSessionStatusBadge(session.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewAttendanceDetails(session)}
                          disabled={session.status !== SessionStatus.REPORTED}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Attendance Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Attendance Details</DialogTitle>
            <DialogDescription>
              {selectedSession && (
                <div className="text-sm text-muted-foreground">
                  {format(new Date(selectedSession.date), 'EEEE, MMMM d, yyyy')}{' '}
                  | {selectedSession.timeSlot} | {selectedSession.className} |{' '}
                  {selectedSession.subjectName}
                </div>
              )}
            </DialogDescription>
          </DialogHeader>

          {isLoadingAttendance ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : attendanceRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No attendance records found for this session.
            </div>
          ) : (
            <ScrollArea className="h-[400px] mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {attendanceRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">
                        {record.student.firstname} {record.student.lastname}
                      </TableCell>
                      <TableCell>
                        {getAttendanceStatusBadge(record.status)}
                      </TableCell>
                      <TableCell>{record.notes || '-'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
