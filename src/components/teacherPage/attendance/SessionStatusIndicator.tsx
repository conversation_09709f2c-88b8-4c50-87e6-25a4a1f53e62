import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle, AlertCircle, XCircle, Calendar, Wifi, WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';

export type SessionStatus = 'PENDING' | 'ONGOING' | 'NOT_REPORTED' | 'REPORTED' | 'CANCELED';

interface SessionStatusIndicatorProps {
  status: SessionStatus;
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}

interface StatusConfig {
  label: string;
  icon: React.ComponentType<any>;
  variant: 'default' | 'secondary' | 'destructive' | 'outline';
  color: string;
  bgColor: string;
  textColor: string;
  description: string;
}

const statusConfigs: Record<SessionStatus, StatusConfig> = {
  PENDING: {
    label: 'Scheduled',
    icon: Calendar,
    variant: 'outline',
    color: 'border-blue-500 text-blue-700',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    description: 'Session is scheduled and waiting to start'
  },
  ONGOING: {
    label: 'In Progress',
    icon: Clock,
    variant: 'default',
    color: 'border-green-500 text-green-700',
    bgColor: 'bg-green-50',
    textColor: 'text-green-700',
    description: 'Session is currently in progress'
  },
  NOT_REPORTED: {
    label: 'Needs Attendance',
    icon: AlertCircle,
    variant: 'destructive',
    color: 'border-orange-500 text-orange-700',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-700',
    description: 'Session ended but attendance not submitted'
  },
  REPORTED: {
    label: 'Completed',
    icon: CheckCircle,
    variant: 'secondary',
    color: 'border-emerald-500 text-emerald-700',
    bgColor: 'bg-emerald-50',
    textColor: 'text-emerald-700',
    description: 'Session completed with attendance submitted'
  },
  CANCELED: {
    label: 'Canceled',
    icon: XCircle,
    variant: 'destructive',
    color: 'border-red-500 text-red-700',
    bgColor: 'bg-red-50',
    textColor: 'text-red-700',
    description: 'Session has been canceled'
  }
};

export const SessionStatusIndicator: React.FC<SessionStatusIndicatorProps> = ({
  status,
  className,
  showIcon = true,
  showText = true,
  size = 'md',
  animated = false
}) => {
  // Handle undefined or invalid status by defaulting to PENDING
  const safeStatus = status && statusConfigs[status] ? status : 'PENDING';
  const config = statusConfigs[safeStatus];
  const Icon = config.icon;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <Badge
      variant={config.variant}
      className={cn(
        'inline-flex items-center gap-1.5 font-medium transition-all duration-200',
        config.color,
        sizeClasses[size],
        animated && 'animate-pulse',
        className
      )}
      title={config.description}
    >
      {showIcon && (
        <Icon
          className={cn(
            iconSizes[size],
            animated && safeStatus === 'ONGOING' && 'animate-spin'
          )}
        />
      )}
      {showText && config.label}
    </Badge>
  );
};

interface SessionStatusCardProps {
  status: SessionStatus;
  className?: string;
  title?: string;
  subtitle?: string;
  isRealTimeConnected?: boolean;
  lastUpdated?: string;
}

export const SessionStatusCard: React.FC<SessionStatusCardProps> = ({
  status,
  className,
  title,
  subtitle,
  isRealTimeConnected = false,
  lastUpdated
}) => {
  // Handle undefined or invalid status by defaulting to PENDING
  const safeStatus = status && statusConfigs[status] ? status : 'PENDING';
  const config = statusConfigs[safeStatus];

  return (
    <div
      className={cn(
        'rounded-lg border-2 p-4 transition-all duration-300 hover:shadow-md',
        config.color,
        config.bgColor,
        className
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={cn(
            'rounded-full p-2',
            status === 'ONGOING' ? 'bg-green-100' : 'bg-white'
          )}>
            <config.icon
              className={cn(
                'h-6 w-6',
                config.textColor,
                safeStatus === 'ONGOING' && 'animate-pulse'
              )}
            />
          </div>
          <div>
            {title && (
              <h3 className={cn('font-semibold', config.textColor)}>
                {title}
              </h3>
            )}
            {subtitle && (
              <p className={cn('text-sm opacity-80', config.textColor)}>
                {subtitle}
              </p>
            )}
            <SessionStatusIndicator
              status={safeStatus}
              size="sm"
              animated={safeStatus === 'ONGOING'}
            />
          </div>
        </div>

        <div className="flex flex-col items-end gap-1">
          {/* Real-time connection indicator */}
          <div className="flex items-center gap-1">
            {isRealTimeConnected ? (
              <Wifi className="h-3 w-3 text-green-600" aria-label="Real-time updates active" />
            ) : (
              <WifiOff className="h-3 w-3 text-gray-400" aria-label="Real-time updates inactive" />
            )}
            <span className="text-xs text-gray-500">
              {isRealTimeConnected ? 'Live' : 'Offline'}
            </span>
          </div>

          {lastUpdated && (
            <span className="text-xs text-gray-500">
              Updated: {new Date(lastUpdated).toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      <div className="mt-3">
        <p className={cn('text-sm', config.textColor)}>
          {config.description}
        </p>
      </div>
    </div>
  );
};

interface SessionStatusTimelineProps {
  currentStatus: SessionStatus;
  className?: string;
}

export const SessionStatusTimeline: React.FC<SessionStatusTimelineProps> = ({
  currentStatus,
  className
}) => {
  const timelineSteps: { status: SessionStatus; label: string }[] = [
    { status: 'PENDING', label: 'Scheduled' },
    { status: 'ONGOING', label: 'In Progress' },
    { status: 'REPORTED', label: 'Completed' }
  ];

  const getCurrentStepIndex = () => {
    if (currentStatus === 'CANCELED') return -1;
    if (currentStatus === 'NOT_REPORTED') return 2; // Show as if it should be completed
    return timelineSteps.findIndex(step => step.status === currentStatus);
  };

  const currentStepIndex = getCurrentStepIndex();

  const getStepIcon = (status: SessionStatus) => {
    switch (status) {
      case 'PENDING':
        return <Calendar className="h-4 w-4" />;
      case 'ONGOING':
        return <Clock className="h-4 w-4" />;
      case 'REPORTED':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {timelineSteps.map((step, index) => {
        const isActive = index === currentStepIndex;
        const isCompleted = index < currentStepIndex;
        const isCanceled = currentStatus === 'CANCELED';
        const isNotReported = currentStatus === 'NOT_REPORTED' && index === 2;

        return (
          <React.Fragment key={step.status}>
            <div className="flex flex-col items-center gap-1">
              <div
                className={cn(
                  'rounded-full p-2 transition-all duration-200',
                  isActive && !isCanceled
                    ? 'bg-blue-500 text-white'
                    : isCompleted && !isCanceled
                    ? 'bg-green-500 text-white'
                    : isNotReported
                    ? 'bg-orange-500 text-white'
                    : isCanceled
                    ? 'bg-red-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                )}
              >
                {getStepIcon(step.status)}
              </div>
              <span
                className={cn(
                  'text-xs font-medium',
                  isActive || isCompleted ? 'text-gray-900' : 'text-gray-500'
                )}
              >
                {step.label}
              </span>
            </div>
            {index < timelineSteps.length - 1 && (
              <div
                className={cn(
                  'h-0.5 w-8 transition-all duration-200',
                  index < currentStepIndex ? 'bg-green-500' : 'bg-gray-200'
                )}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default SessionStatusIndicator;
