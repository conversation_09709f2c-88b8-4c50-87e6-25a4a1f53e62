// Shared types for the attendance system

export interface LoadingState {
  isLoading: boolean
  error: string | null
  isRetrying: boolean
}

export interface Session {
  id: string
  className: string
  subjectName: string
  date: string
  timeSlot: string
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  studentCount: number
  attendanceCount?: number
}

export interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  avatar?: string
}

export interface AttendanceRecord {
  id: string
  sessionId: string
  studentId: string
  status: 'present' | 'absent' | 'late' | 'left_early'
  notes?: string
  timestamp: string
}

export interface AttendanceStats {
  totalSessionsToday: number
  completedSessions: number
  totalStudents: number
  presentStudents: number
  attendanceRate: number
  ongoingSessions: number
}

export interface AttendanceHistoryRecord {
  id: string
  sessionId: string
  className: string
  subjectName: string
  date: string
  timeSlot: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  lateStudents: number
  leftEarlyStudents: number
  attendanceRate: number
}

export interface ValidationError {
  field: string
  message: string
}

export interface FormValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'left_early'

export interface StudentAttendance {
  studentId: string
  status: AttendanceStatus
  notes: string
}

export interface AttendanceFormData {
  sessionId: string
  attendance: Record<string, StudentAttendance>
  sessionNotes: string
}

// API response types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// Error types
export interface ApiError {
  message: string
  code?: string
  details?: any
}

// Navigation types
export interface NavigationItem {
  id: string
  label: string
  icon: any
  component: React.ComponentType
  ariaLabel?: string
}

// Export types
export type ExportFormat = 'pdf' | 'excel' | 'csv'

export interface ExportOptions {
  format: ExportFormat
  dateRange?: {
    start: Date
    end: Date
  }
  includeNotes?: boolean
  includeStats?: boolean
}
