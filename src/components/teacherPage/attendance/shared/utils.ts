import { AttendanceFormData, ValidationError, FormValidationResult, AttendanceStatus } from './types'

// Validation utilities
export const validateAttendanceForm = (formData: AttendanceFormData): FormValidationResult => {
  const errors: ValidationError[] = []

  // Check if any attendance is marked
  if (Object.keys(formData.attendance).length === 0) {
    errors.push({
      field: 'attendance',
      message: 'Please mark attendance for at least one student'
    })
  }

  // Check for required fields in attendance records
  Object.entries(formData.attendance).forEach(([studentId, attendance]) => {
    if (!attendance.status) {
      errors.push({
        field: `attendance.${studentId}.status`,
        message: 'Attendance status is required for all students'
      })
    }
  })

  // Validate session notes length
  if (formData.sessionNotes && formData.sessionNotes.length > 500) {
    errors.push({
      field: 'sessionNotes',
      message: 'Session notes must be less than 500 characters'
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Attendance statistics calculations
export const calculateAttendanceStats = (attendance: Record<string, { status: AttendanceStatus }>) => {
  const total = Object.keys(attendance).length
  const present = Object.values(attendance).filter(a => a.status === 'present').length
  const absent = Object.values(attendance).filter(a => a.status === 'absent').length
  const late = Object.values(attendance).filter(a => a.status === 'late').length
  const leftEarly = Object.values(attendance).filter(a => a.status === 'left_early').length

  return {
    total,
    present,
    absent,
    late,
    leftEarly,
    attendanceRate: total > 0 ? Math.round((present / total) * 100) : 0
  }
}

// Format date utilities
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export const formatDateTime = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Status utilities
export const getStatusColor = (status: AttendanceStatus): string => {
  switch (status) {
    case 'present':
      return 'text-green-600'
    case 'absent':
      return 'text-red-600'
    case 'late':
      return 'text-yellow-600'
    case 'left_early':
      return 'text-orange-600'
    default:
      return 'text-gray-600'
  }
}

export const getStatusBgColor = (status: AttendanceStatus): string => {
  switch (status) {
    case 'present':
      return 'bg-green-100 text-green-800'
    case 'absent':
      return 'bg-red-100 text-red-800'
    case 'late':
      return 'bg-yellow-100 text-yellow-800'
    case 'left_early':
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Error handling utilities
export const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error
  if (error?.message) return error.message
  if (error?.response?.data?.message) return error.response.data.message
  return 'An unexpected error occurred'
}

export const isNetworkError = (error: any): boolean => {
  return error?.code === 'NETWORK_ERROR' || 
         error?.message?.includes('Network Error') ||
         error?.message?.includes('fetch')
}

// Debounce utility for search
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.warn('Failed to save to localStorage:', error)
  }
}

export const loadFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.warn('Failed to load from localStorage:', error)
    return defaultValue
  }
}

export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to remove from localStorage:', error)
  }
}

// Accessibility utilities
export const generateId = (prefix: string): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

export const announceToScreenReader = (message: string): void => {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Export utilities
export const downloadFile = (data: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(data)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
