import { Card, CardContent, CardHeader } from '@/components/ui/card'

// Loading skeleton for statistics cards
export const StatCardSkeleton = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
      <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
    </CardHeader>
    <CardContent>
      <div className="h-8 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
      <div className="h-3 bg-gray-200 rounded w-32 animate-pulse"></div>
    </CardContent>
  </Card>
)

// Loading skeleton for session cards
export const SessionCardSkeleton = () => (
  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
    <div className="flex items-center space-x-4">
      <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
      <div>
        <div className="h-5 bg-gray-200 rounded w-48 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
      </div>
    </div>
    <div className="flex items-center space-x-4">
      <div className="text-right">
        <div className="h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
      </div>
      <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
    </div>
  </div>
)

// Loading skeleton for student rows
export const StudentRowSkeleton = () => (
  <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
    <div className="flex items-center space-x-3">
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
      <div>
        <div className="h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"></div>
        <div className="h-3 bg-gray-200 rounded w-40 animate-pulse"></div>
      </div>
    </div>
    <div className="flex items-center space-x-2">
      <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
      <div className="h-8 bg-gray-200 rounded w-32 animate-pulse"></div>
      <div className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
    </div>
  </div>
)

// Loading skeleton for table rows
export const TableRowSkeleton = () => (
  <tr>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
    </td>
    <td className="px-6 py-4">
      <div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div>
    </td>
  </tr>
)

// Loading skeleton for charts
export const ChartSkeleton = () => (
  <div className="h-[300px] bg-gray-100 rounded-lg flex items-center justify-center animate-pulse">
    <div className="text-center">
      <div className="h-6 bg-gray-200 rounded w-32 mx-auto mb-2 animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded w-24 mx-auto animate-pulse"></div>
    </div>
  </div>
)

// Generic loading spinner
export const LoadingSpinner = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`} />
  )
}

// Page loading overlay
export const PageLoadingOverlay = () => (
  <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-2 text-sm text-gray-600">Loading...</p>
    </div>
  </div>
)
