import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react'

interface ErrorStateProps {
  error: string
  onRetry: () => void
  isRetrying?: boolean
}

interface NetworkErrorProps {
  onRetry: () => void
  isRetrying?: boolean
}

// Generic error state component
export const ErrorState = ({ error, onRetry, isRetrying = false }: ErrorStateProps) => (
  <div className="text-center py-8">
    <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
    <h3 className="mt-2 text-sm font-medium text-gray-900">Error Loading Data</h3>
    <p className="mt-1 text-sm text-gray-500">{error}</p>
    <Button 
      onClick={onRetry} 
      className="mt-4"
      disabled={isRetrying}
      aria-label="Retry loading data"
    >
      {isRetrying ? (
        <>
          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          Retrying...
        </>
      ) : (
        <>
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </>
      )}
    </Button>
  </div>
)

// Network error state
export const NetworkErrorState = ({ onRetry, isRetrying = false }: NetworkErrorProps) => (
  <div className="text-center py-8">
    <WifiOff className="mx-auto h-12 w-12 text-red-400" />
    <h3 className="mt-2 text-sm font-medium text-gray-900">Connection Error</h3>
    <p className="mt-1 text-sm text-gray-500">
      Unable to connect to the server. Please check your internet connection.
    </p>
    <Button 
      onClick={onRetry} 
      className="mt-4"
      disabled={isRetrying}
      aria-label="Retry connection"
    >
      {isRetrying ? (
        <>
          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          Reconnecting...
        </>
      ) : (
        <>
          <Wifi className="mr-2 h-4 w-4" />
          Reconnect
        </>
      )}
    </Button>
  </div>
)

// Card-based error state for smaller components
export const CardErrorState = ({ error, onRetry, isRetrying = false }: ErrorStateProps) => (
  <Card>
    <CardContent className="p-6">
      <div className="text-center">
        <AlertCircle className="mx-auto h-8 w-8 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
        <p className="mt-1 text-xs text-gray-500">{error}</p>
        <Button 
          onClick={onRetry} 
          size="sm"
          variant="outline"
          className="mt-3"
          disabled={isRetrying}
          aria-label="Retry loading"
        >
          {isRetrying ? (
            <>
              <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
              Retrying...
            </>
          ) : (
            <>
              <RefreshCw className="mr-1 h-3 w-3" />
              Retry
            </>
          )}
        </Button>
      </div>
    </CardContent>
  </Card>
)

// Inline error state for form fields
export const InlineErrorState = ({ error }: { error: string }) => (
  <div className="flex items-center mt-1 text-sm text-red-600" role="alert">
    <AlertCircle className="mr-1 h-4 w-4" />
    {error}
  </div>
)

// Error boundary fallback component
export const ErrorBoundaryFallback = ({ 
  error, 
  resetError 
}: { 
  error: Error
  resetError: () => void 
}) => (
  <div className="min-h-[400px] flex items-center justify-center">
    <div className="text-center max-w-md mx-auto p-6">
      <AlertCircle className="mx-auto h-16 w-16 text-red-400" />
      <h2 className="mt-4 text-lg font-medium text-gray-900">Something went wrong</h2>
      <p className="mt-2 text-sm text-gray-500">
        An unexpected error occurred while loading this component.
      </p>
      <details className="mt-4 text-left">
        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
          Technical details
        </summary>
        <pre className="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded overflow-auto">
          {error.message}
        </pre>
      </details>
      <div className="mt-6 space-x-3">
        <Button onClick={resetError} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
        <Button onClick={() => window.location.reload()} variant="ghost">
          Reload Page
        </Button>
      </div>
    </div>
  </div>
)

// Validation error list component
export const ValidationErrors = ({ errors }: { errors: string[] }) => {
  if (errors.length === 0) return null

  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4" role="alert">
      <div className="flex">
        <AlertCircle className="h-5 w-5 text-red-400" />
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Please correct the following errors:
          </h3>
          <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}
