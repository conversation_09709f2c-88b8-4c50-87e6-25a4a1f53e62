import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  Search,
  RefreshCw,
  AlertCircle,
  UserCheck,
  Eye,
  Play,
  Wifi,
  WifiOff,
} from 'lucide-react'
import { format } from 'date-fns'
import { useTeacherAttendance } from '@/hooks/useTeacherAttendance'
import { TeacherSession } from '@/lib/api/services/teacher-attendance-service'
import TakeAttendanceModal from './TakeAttendanceModal'
import SessionDetailsModal from './SessionDetailsModal'
// import { SessionStatusIndicator, SessionStatusCard, SessionStatusTimeline } from '../../SessionStatusIndicator'

export default function SessionsAndAttendance() {
  console.log('🚀 SessionsAndAttendance component is mounting!')

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('today')
  const [selectedSession, setSelectedSession] = useState<TeacherSession | null>(null)
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)

  // Use the enhanced teacher attendance hook with real-time capabilities
  const {
    useTeacherSessions,
    isWebSocketConnected,
    webSocketError
  } = useTeacherAttendance()

  // Fetch sessions from API
  const {
    data: sessions = [],
    isLoading,
    error,
    refetch
  } = useTeacherSessions()

  // Track last update time for real-time indicator
  const [lastUpdateTime, setLastUpdateTime] = useState<string>(new Date().toISOString())

  // Update last update time when sessions data changes
  useEffect(() => {
    if (sessions.length > 0) {
      setLastUpdateTime(new Date().toISOString())
    }
  }, [sessions])

  // Debug logging
  console.log('🎯 SessionsAndAttendance - Sessions data:', sessions)
  console.log('🎯 SessionsAndAttendance - Loading:', isLoading)
  console.log('🎯 SessionsAndAttendance - Error:', error)

  const handleRefresh = () => {
    refetch()
  }

  const handleTakeAttendance = (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId)
    if (session) {
      setSelectedSession(session)
      setIsAttendanceModalOpen(true)
    }
  }

  const handleViewDetails = (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId)
    if (session) {
      setSelectedSession(session)
      setIsDetailsModalOpen(true)
    }
  }

  const handleCloseAttendanceModal = () => {
    setIsAttendanceModalOpen(false)
    setSelectedSession(null)
  }

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false)
    setSelectedSession(null)
  }

  // Filter sessions based on search and filters
  const filteredSessions = sessions.filter(session => {
    // First check if session exists and has required properties
    if (!session || !session.className || !session.subjectName) {
      console.warn('Invalid session object:', session)
      return false
    }

    const matchesSearch = session.className.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.subjectName.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || session.status === statusFilter

    // Filter by date if needed
    if (dateFilter === 'today') {
      const today = format(new Date(), 'yyyy-MM-dd')
      return matchesSearch && matchesStatus && session.date === today
    }

    return matchesSearch && matchesStatus
  })

  const getAttendanceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600'
    if (rate >= 70) return 'text-blue-600'
    if (rate >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded w-full"></div>
                  <div className="h-10 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="font-medium text-red-800">Error Loading Sessions</h3>
                <p className="text-red-600 text-sm mt-1">
                  {error instanceof Error ? error.message : 'Failed to load sessions'}
                </p>
              </div>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-medium text-gray-900">My Sessions</h3>
            {/* Real-time connection indicator */}
            <div className="flex items-center gap-1">
              {isWebSocketConnected ? (
                <div className="flex items-center gap-1 text-green-600">
                  <Wifi className="h-4 w-4" />
                  <span className="text-xs font-medium">Live</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-gray-400">
                  <WifiOff className="h-4 w-4" />
                  <span className="text-xs font-medium">Offline</span>
                </div>
              )}
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Manage your teaching sessions and take attendance
            {isWebSocketConnected && (
              <span className="ml-2 text-green-600">• Real-time updates active</span>
            )}
            {webSocketError && (
              <span className="ml-2 text-red-600">• Connection error: {webSocketError}</span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            Last updated: {new Date(lastUpdateTime).toLocaleTimeString()}
          </span>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search sessions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Scheduled</SelectItem>
                <SelectItem value="ONGOING">In Progress</SelectItem>
                <SelectItem value="NOT_REPORTED">Needs Attendance</SelectItem>
                <SelectItem value="REPORTED">Completed</SelectItem>
                <SelectItem value="CANCELED">Canceled</SelectItem>
                {/* Legacy status support */}
                <SelectItem value="upcoming">Upcoming (Legacy)</SelectItem>
                <SelectItem value="ongoing">Ongoing (Legacy)</SelectItem>
                <SelectItem value="completed">Completed (Legacy)</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Session Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredSessions.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No sessions found</h3>
                <p className="text-gray-500">
                  Try adjusting your search or filter criteria.
                </p>
              </CardContent>
            </Card>
          </div>
        ) : (
          filteredSessions.map((session) => {
            // Additional safety check before rendering
            if (!session || !session.id) {
              console.warn('Skipping invalid session in render:', session)
              return null
            }

            return (
              <Card key={session.id} className="hover:shadow-lg transition-all duration-200 border-gray-200">
                <CardContent className="p-6">
                  {/* Header with Status */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-lg mb-1">
                        {session.className || 'Unknown Class'}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2">{session.subjectName || 'Unknown Subject'}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {session.status || 'PENDING'}
                    </Badge>
                  </div>

                {/* Session Details */}
                <div className="space-y-3 mb-4">
                  {session.timeSlot && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>{session.timeSlot}</span>
                    </div>
                  )}
                  {session.date && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{format(new Date(session.date), 'MMM d, yyyy')}</span>
                    </div>
                  )}
                  {session.location && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{session.location}</span>
                    </div>
                  )}
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    <span>{session.totalStudents || 0} students</span>
                  </div>
                </div>

                {/* Attendance Info (for completed sessions) */}
                {session.status === 'completed' && session.attendanceRate !== undefined && (
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Attendance</span>
                      <span className={`text-lg font-bold ${getAttendanceColor(session.attendanceRate)}`}>
                        {session.attendanceRate}%
                      </span>
                    </div>
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>Present: {session.presentStudents}</span>
                      <span>Absent: {session.absentStudents}</span>
                    </div>
                  </div>
                )}

                {/* Instructor Info */}
                <div className="flex items-center mb-4 pb-4 border-b border-gray-100">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs font-medium text-gray-600">
                      {session.teacherName ? session.teacherName.split(' ').map(n => n[0]).join('') : 'T'}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">{session.teacherName || 'Teacher'}</span>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  {(session.status === 'upcoming') && (
                    <Button
                      onClick={() => handleTakeAttendance(session.id)}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      size="sm"
                    >
                      <UserCheck className="h-4 w-4 mr-2" />
                      Take Attendance
                    </Button>
                  )}

                  {(session.status === 'ongoing') && (
                    <Button
                      onClick={() => handleTakeAttendance(session.id)}
                      className="w-full bg-green-600 hover:bg-green-700 animate-pulse"
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Continue Session
                    </Button>
                  )}

                  {session.status === 'not_reported' && (
                    <Button
                      onClick={() => handleTakeAttendance(session.id)}
                      className="w-full bg-orange-600 hover:bg-orange-700"
                      size="sm"
                    >
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Submit Attendance
                    </Button>
                  )}

                  <Button
                    onClick={() => handleViewDetails(session.id)}
                    variant="outline"
                    className="w-full"
                    size="sm"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
            )
          })
        )}
      </div>

      {/* Take Attendance Modal */}
      <TakeAttendanceModal
        isOpen={isAttendanceModalOpen}
        onClose={handleCloseAttendanceModal}
        session={selectedSession}
      />

      {/* Session Details Modal */}
      <SessionDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        session={selectedSession}
      />
    </div>
  )
}
