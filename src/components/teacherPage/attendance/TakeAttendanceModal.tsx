import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  Save,
  X,
  AlertCircle,
} from 'lucide-react'
import { format } from 'date-fns'
import { useTeacherAttendance } from '@/hooks/useTeacherAttendance'
import { TeacherSession, AttendanceRecord } from '@/lib/api/services/teacher-attendance-service'

interface TakeAttendanceModalProps {
  isOpen: boolean
  onClose: () => void
  session: TeacherSession | null
}

export default function TakeAttendanceModal({ isOpen, onClose, session }: TakeAttendanceModalProps) {
  const [attendanceRecords, setAttendanceRecords] = useState<Record<string, AttendanceRecord>>({})
  const [sessionNotes, setSessionNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { useSessionStudents, useSubmitAttendance } = useTeacherAttendance()

  // Fetch students for the session
  const {
    data: students = [],
    isLoading: isLoadingStudents,
    error: studentsError
  } = useSessionStudents(session?.id || '')

  // Submit attendance mutation
  const submitAttendanceMutation = useSubmitAttendance()

  // Initialize attendance records when students are loaded
  useEffect(() => {
    if (students.length > 0) {
      const initialRecords: Record<string, AttendanceRecord> = {}
      students.forEach(student => {
        initialRecords[student.id] = {
          studentId: student.id,
          status: 'present', // Default to present
          notes: ''
        }
      })
      setAttendanceRecords(initialRecords)
    }
  }, [students])

  const handleStatusChange = (studentId: string, status: 'present' | 'absent' | 'late' | 'excused') => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status
      }
    }))
  }

  const handleNotesChange = (studentId: string, notes: string) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes
      }
    }))
  }

  const handleSubmit = async () => {
    if (!session) return

    setIsSubmitting(true)
    try {
      const attendanceData = {
        sessionId: session.id,
        className: session.className,
        subject: session.subjectName,
        day: session.day,
        timeSlot: session.timeSlot,
        date: session.date,
        records: Object.values(attendanceRecords)
      }

      await submitAttendanceMutation.mutateAsync(attendanceData)
      onClose()
    } catch (error) {
      console.error('Error submitting attendance:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'excused':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <UserCheck className="h-4 w-4" />
      case 'absent':
        return <UserX className="h-4 w-4" />
      case 'late':
        return <Clock className="h-4 w-4" />
      case 'excused':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const attendanceStats = Object.values(attendanceRecords).reduce(
    (acc, record) => {
      acc[record.status] = (acc[record.status] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  if (!session) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Take Attendance</span>
          </DialogTitle>
          <DialogDescription>
            Mark attendance for {session.className} - {session.subjectName}
          </DialogDescription>
        </DialogHeader>

        {/* Session Info */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600">Class:</span>
                <p className="text-gray-900">{session.className}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Subject:</span>
                <p className="text-gray-900">{session.subjectName}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Time:</span>
                <p className="text-gray-900">{session.timeSlot}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Date:</span>
                <p className="text-gray-900">{format(new Date(session.date), 'MMM d, yyyy')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{attendanceStats.present || 0}</div>
              <div className="text-sm text-gray-600">Present</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{attendanceStats.absent || 0}</div>
              <div className="text-sm text-gray-600">Absent</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">{attendanceStats.late || 0}</div>
              <div className="text-sm text-gray-600">Late</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{attendanceStats.excused || 0}</div>
              <div className="text-sm text-gray-600">Excused</div>
            </CardContent>
          </Card>
        </div>

        {/* Student List */}
        {isLoadingStudents ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : studentsError ? (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <span className="text-red-800">Error loading students</span>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3 mb-6">
            <h3 className="font-medium text-gray-900">Students ({students.length})</h3>
            {students.map((student) => (
              <Card key={student.id} className="border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div>
                        <p className="font-medium text-gray-900">
                          {student.firstName} {student.lastName}
                        </p>
                        {student.rollNumber && (
                          <p className="text-sm text-gray-600">Roll: {student.rollNumber}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Select
                        value={attendanceRecords[student.id]?.status || 'present'}
                        onValueChange={(value: 'present' | 'absent' | 'late' | 'excused') =>
                          handleStatusChange(student.id, value)
                        }
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="present">
                            <div className="flex items-center space-x-2">
                              <UserCheck className="h-4 w-4 text-green-600" />
                              <span>Present</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="absent">
                            <div className="flex items-center space-x-2">
                              <UserX className="h-4 w-4 text-red-600" />
                              <span>Absent</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="late">
                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4 text-yellow-600" />
                              <span>Late</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="excused">
                            <div className="flex items-center space-x-2">
                              <AlertCircle className="h-4 w-4 text-blue-600" />
                              <span>Excused</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>

                      <Badge className={`border ${getStatusColor(attendanceRecords[student.id]?.status || 'present')}`}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(attendanceRecords[student.id]?.status || 'present')}
                          <span className="capitalize">{attendanceRecords[student.id]?.status || 'present'}</span>
                        </div>
                      </Badge>
                    </div>
                  </div>

                  {/* Notes for absent/late students */}
                  {(attendanceRecords[student.id]?.status === 'absent' ||
                    attendanceRecords[student.id]?.status === 'late' ||
                    attendanceRecords[student.id]?.status === 'excused') && (
                    <div className="mt-3">
                      <Textarea
                        placeholder="Add notes (optional)..."
                        value={attendanceRecords[student.id]?.notes || ''}
                        onChange={(e) => handleNotesChange(student.id, e.target.value)}
                        className="text-sm"
                        rows={2}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Session Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Session Notes (Optional)
          </label>
          <Textarea
            placeholder="Add any notes about this session..."
            value={sessionNotes}
            onChange={(e) => setSessionNotes(e.target.value)}
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || students.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Submitting...' : 'Submit Attendance'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
