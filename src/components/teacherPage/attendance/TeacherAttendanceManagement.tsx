import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  BarChart3,
  Calendar,
  FileText,
  Users,
  Clock,
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Import the individual components
import DashboardOverview from './DashboardOverview'
import SessionsAndAttendance from './SessionsAndAttendance'
import AttendanceHistory from './AttendanceHistory'
import Reports from './Reports'

type AttendanceTab = 'dashboard' | 'sessions' | 'history' | 'reports'

interface SidebarItem {
  id: AttendanceTab
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'Overview of attendance statistics'
  },
  {
    id: 'sessions',
    label: 'Sessions & Attendance',
    icon: Calendar,
    description: 'Manage sessions attendance'
  },
  {
    id: 'history',
    label: 'History',
    icon: Clock,
    description: 'View attendance history'
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: FileText,
    description: 'Generate attendance reports'
  }
]

export default function TeacherAttendanceManagement() {
  const [activeTab, setActiveTab] = useState<AttendanceTab>('sessions')

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardOverview />
      case 'sessions':
        return <SessionsAndAttendance />
      case 'history':
        return <AttendanceHistory />
      case 'reports':
        return <Reports />
      default:
        return <DashboardOverview />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Attendance Management
                </h1>
                <p className="text-sm text-gray-500">Teacher Portal</p>
              </div>
            </div>
          </div>

          <nav className="p-4 space-y-2">
            {sidebarItems.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id

              return (
                <Button
                  key={item.id}
                  variant={isActive ? 'default' : 'ghost'}
                  className={cn(
                    'w-full justify-start h-auto p-3 text-left',
                    isActive
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  )}
                  onClick={() => setActiveTab(item.id)}
                >
                  <div className="flex items-start space-x-3">
                    <Icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium">{item.label}</div>
                      <div className={cn(
                        'text-xs mt-1',
                        isActive ? 'text-blue-100' : 'text-gray-500'
                      )}>
                        {item.description}
                      </div>
                    </div>
                  </div>
                </Button>
              )
            })}
          </nav>

          {/* Quick Stats in Sidebar */}
          <div className="p-4 mt-6 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Today's Sessions</span>
                <span className="text-sm font-medium text-gray-900">4</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Attendance Rate</span>
                <span className="text-sm font-medium text-green-600">92%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Students</span>
                <span className="text-sm font-medium text-gray-900">156</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1">
          <div className="p-6">
            {/* Header */}
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {sidebarItems.find(item => item.id === activeTab)?.label}
                  </h2>
                  <p className="text-gray-600 mt-1">
                    {sidebarItems.find(item => item.id === activeTab)?.description}
                  </p>
                </div>
                <div className="text-sm text-gray-500">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
              </div>
            </div>

            {/* Dynamic Content */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[600px]">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
