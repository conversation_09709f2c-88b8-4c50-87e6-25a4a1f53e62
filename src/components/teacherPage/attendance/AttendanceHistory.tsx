import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Calendar,
  Clock,
  Users,
  Search,
  Download,
  RefreshCw,
  AlertCircle,
  Eye,
} from 'lucide-react'
import { format } from 'date-fns'
import { useTeacherAttendance } from '@/hooks/useTeacherAttendance'
import { AttendanceHistoryRecord } from '@/lib/api/services/teacher-attendance-service'

export default function AttendanceHistory() {
  const [searchTerm, setSearchTerm] = useState('')
  const [dateFilter, setDateFilter] = useState<string>('week')
  const [classFilter, setClassFilter] = useState<string>('all')

  // Use the teacher attendance hook
  const { useAttendanceHistory } = useTeacherAttendance()

  // Fetch attendance history from API
  const {
    data: records = [],
    isLoading,
    error,
    refetch
  } = useAttendanceHistory()

  const handleRefresh = () => {
    refetch()
  }

  const handleExport = () => {
    // Simulate export functionality
    console.log('Exporting attendance history...')
    alert('Export functionality would be implemented here')
  }

  // Filter records based on search and filters
  const filteredRecords = records.filter(record => {
    if (!record || !record.className || !record.subjectName) {
      return false;
    }
    const status = (record.status || '').toLowerCase();
    const allowedStatuses = ['not_reported', 'reported', 'canceled'];
    if (!allowedStatuses.includes(status)) {
      if (status) {
        console.warn('Unexpected session status in attendance history:', record.status, record);
      }
      return false;
    }
    const matchesSearch = record.className.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.subjectName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = classFilter === 'all' || record.className === classFilter;
    return matchesSearch && matchesClass;
  });

  const getStatusBadge = (status: AttendanceHistoryRecord['status']) => {
    const variants = {
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    }

    return (
      <Badge className={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getAttendanceRateBadge = (rate: number) => {
    if (rate >= 95) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (rate >= 85) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>
    if (rate >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                    <div className="h-3 bg-gray-200 rounded w-24"></div>
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="font-medium text-red-800">Error Loading History</h3>
                <p className="text-red-600 text-sm mt-1">
                  {error instanceof Error ? error.message : 'Failed to load attendance history'}
                </p>
              </div>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Attendance History</h3>
          <p className="text-sm text-gray-500">
            View and manage your past attendance records
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by class or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={classFilter} onValueChange={setClassFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Class" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                <SelectItem value="Grade 8A">Grade 8A</SelectItem>
                <SelectItem value="Grade 9B">Grade 9B</SelectItem>
                <SelectItem value="Grade 10A">Grade 10A</SelectItem>
                <SelectItem value="Grade 11C">Grade 11C</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* History Records */}
      <div className="space-y-4">
        {filteredRecords.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No records found</h3>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredRecords.map((record) => (
            <Card key={record.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900">
                        {record.className} - {record.subjectName}
                      </h4>
                      {getStatusBadge(record.status)}
                      {record.status === 'completed' && getAttendanceRateBadge(record.attendanceRate)}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(record.date), 'MMM d, yyyy')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{record.timeSlot}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>{record.totalStudents} students</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{record.attendanceRate}% attendance</span>
                      </div>
                    </div>

                    {record.status === 'completed' && (
                      <div className="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg text-sm">
                        <div className="text-center">
                          <p className="font-medium text-green-600">{record.presentStudents}</p>
                          <p className="text-gray-600">Present</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium text-red-600">{record.absentStudents}</p>
                          <p className="text-gray-600">Absent</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium text-yellow-600">{record.lateStudents}</p>
                          <p className="text-gray-600">Late</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="ml-4">
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
