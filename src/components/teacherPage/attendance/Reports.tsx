import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  BarChart3,
  TrendingUp,
  Download,
  RefreshCw,
  AlertCircle,
  Calendar,
  Users,
  FileText,
} from 'lucide-react'
import { useTeacherAttendance } from '@/hooks/useTeacherAttendance'
import {  ClassReport } from '@/lib/api/services/teacher-attendance-service'

export default function Reports() {
  const [selectedPeriod, setSelectedPeriod] = useState<string>('month')
  const [selectedClass, setSelectedClass] = useState<string>('all')

  // Use the teacher attendance hook
  const { useReportsData } = useTeacherAttendance()

  // Fetch reports data from API
  const {
    data: reportData,
    isLoading,
    error,
    refetch
  } = useReportsData(selectedPeriod, 'attendance', selectedClass)

  const handleRefresh = () => {
    refetch()
  }

  const handleExportReport = () => {
    // Simulate export functionality
    console.log('Exporting report...')
    alert('Report export functionality would be implemented here')
  }

  const getTrendIcon = (trend: ClassReport['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />
      case 'stable':
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />
      default:
        return null
    }
  }

  const getAttendanceColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600'
    if (rate >= 85) return 'text-blue-600'
    if (rate >= 75) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <h3 className="font-medium text-red-800">Error Loading Reports</h3>
                <p className="text-red-600 text-sm mt-1">
                  {error instanceof Error ? error.message : 'Failed to load report data'}
                </p>
              </div>
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Attendance Reports</h3>
          <p className="text-sm text-gray-500">
            Analyze attendance patterns and generate reports
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExportReport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Class" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                <SelectItem value="Grade 8A">Grade 8A</SelectItem>
                <SelectItem value="Grade 9B">Grade 9B</SelectItem>
                <SelectItem value="Grade 10A">Grade 10A</SelectItem>
                <SelectItem value="Grade 11C">Grade 11C</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      {reportData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalSessions}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Attendance</p>
                  <p className={`text-2xl font-bold ${getAttendanceColor(reportData.averageAttendance)}`}>
                    {reportData.averageAttendance}%
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold text-gray-900">{reportData.totalStudents}</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Best Class</p>
                  <p className="text-lg font-bold text-green-600">{reportData.bestPerformingClass}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Class Performance */}
      {reportData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Class Performance Report
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.classReports.map((classReport) => (
                <div key={classReport.className} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-lg font-medium text-gray-900">{classReport.className}</h4>
                      {getTrendIcon(classReport.trend)}
                      <Badge className={`${getAttendanceColor(classReport.averageAttendance)} bg-opacity-10`}>
                        {classReport.averageAttendance}% avg
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      {classReport.totalSessions} sessions
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 text-sm">
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <p className="font-medium text-gray-900">{classReport.totalStudents}</p>
                      <p className="text-gray-600">Total Students</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded">
                      <p className="font-medium text-green-600">{classReport.presentStudents}</p>
                      <p className="text-gray-600">Present</p>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded">
                      <p className="font-medium text-red-600">{classReport.absentStudents}</p>
                      <p className="text-gray-600">Absent</p>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded">
                      <p className={`font-medium ${getAttendanceColor(classReport.averageAttendance)}`}>
                        {classReport.averageAttendance}%
                      </p>
                      <p className="text-gray-600">Attendance Rate</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
