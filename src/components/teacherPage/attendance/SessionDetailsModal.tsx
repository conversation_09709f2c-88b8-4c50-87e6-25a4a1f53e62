import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Users,
  UserCheck,
  Clock,
  Calendar,
  BookOpen,
  AlertCircle,
  CheckCircle,
  XCircle,
  Timer,
  Eye,
  Loader2,
} from 'lucide-react'
import { format } from 'date-fns'
import { useTeacherAttendance } from '@/hooks/useTeacherAttendance'
import { TeacherSession } from '@/lib/api/services/teacher-attendance-service'

interface SessionDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  session: TeacherSession | null
}

interface AttendanceRecord {
  id: string
  studentId: string
  status: 'present' | 'absent' | 'late' | 'left_early'
  notes?: string
  student?: {
    id: string
    firstname: string
    lastname: string
    email: string
  }
}

export default function SessionDetailsModal({ isOpen, onClose, session }: SessionDetailsModalProps) {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [attendanceError] = useState<string | null>(null)

  const { useSessionAttendance } = useTeacherAttendance()

  // Fetch attendance records when session changes
  const {
    data: attendance = [],
    isLoading: isLoadingAttendanceData,
  } = useSessionAttendance(session?.id || '')

  useEffect(() => {
    if (session && isOpen) {
      // Map API attendance records to local AttendanceRecord type
      setAttendanceRecords(
        (attendance || []).map((record: any) => ({
          id: record.id ?? record._id ?? '', // fallback if API uses _id
          studentId: record.studentId ?? record.student?.id ?? '',
          status: record.status,
          notes: record.notes,
          student: record.student
            ? {
                id: record.student.id ?? '',
                firstname: record.student.firstname ?? '',
                lastname: record.student.lastname ?? '',
                email: record.student.email ?? '',
              }
            : undefined,
        }))
      )
    }
  }, [session, isOpen, attendance])

  if (!session) return null

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'late':
        return <Timer className="h-4 w-4 text-yellow-600" />
      case 'left_early':
        return <AlertCircle className="h-4 w-4 text-orange-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'present':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Present</Badge>
      case 'absent':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Absent</Badge>
      case 'late':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Late</Badge>
      case 'left_early':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Left Early</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const getSessionStatusBadge = (status: string) => {
    switch (status) {
      case 'REPORTED':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>
      case 'NOT_REPORTED':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Pending</Badge>
      case 'ONGOING':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Ongoing</Badge>
      case 'CANCELED':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Canceled</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Calculate attendance statistics
  const totalStudents = attendanceRecords.length
  const presentCount = attendanceRecords.filter(r => r.status === 'present').length
  const absentCount = attendanceRecords.filter(r => r.status === 'absent').length
  const lateCount = attendanceRecords.filter(r => r.status === 'late').length
  const leftEarlyCount = attendanceRecords.filter(r => r.status === 'left_early').length

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Session Details
          </DialogTitle>
          <DialogDescription>
            View detailed information and attendance records for this session
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
          {/* Session Information */}
          <div className="lg:col-span-1 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Session Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    {format(new Date(session.date), 'EEEE, MMMM d, yyyy')}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{session.timeSlot}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{session.className}</span>
                </div>

                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{session.subjectName}</span>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Status:</span>
                  {getSessionStatusBadge(session.status)}
                </div>
              </CardContent>
            </Card>

            {/* Attendance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Attendance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{presentCount}</div>
                    <div className="text-sm text-gray-500">Present</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{absentCount}</div>
                    <div className="text-sm text-gray-500">Absent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{lateCount}</div>
                    <div className="text-sm text-gray-500">Late</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{leftEarlyCount}</div>
                    <div className="text-sm text-gray-500">Left Early</div>
                  </div>
                </div>
                <Separator className="my-3" />
                <div className="text-center">
                  <div className="text-lg font-semibold">Total: {totalStudents}</div>
                  {totalStudents > 0 && (
                    <div className="text-sm text-gray-500">
                      {Math.round((presentCount / totalStudents) * 100)}% attendance rate
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Attendance Records */}
          <div className="lg:col-span-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <UserCheck className="h-5 w-5" />
                  Student Attendance ({attendanceRecords.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingAttendanceData ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Loading attendance records...</span>
                  </div>
                ) : attendanceError ? (
                  <div className="text-center py-8 text-red-600">
                    <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                    <p>Error loading attendance records</p>
                    <p className="text-sm text-gray-500">{attendanceError}</p>
                  </div>
                ) : attendanceRecords.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2" />
                    <p>No attendance records found</p>
                    <p className="text-sm">Attendance has not been taken for this session yet.</p>
                  </div>
                ) : (
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-2">
                      {attendanceRecords.map((record) => (
                        <div
                          key={record.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                        >
                          <div className="flex items-center gap-3">
                            {getStatusIcon(record.status)}
                            <div>
                              <div className="font-medium">
                                {record.student?.firstname} {record.student?.lastname}
                              </div>
                              {record.student?.email && (
                                <div className="text-sm text-gray-500">{record.student.email}</div>
                              )}
                              {record.notes && (
                                <div className="text-sm text-gray-600 mt-1">
                                  <span className="font-medium">Note:</span> {record.notes}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(record.status)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
