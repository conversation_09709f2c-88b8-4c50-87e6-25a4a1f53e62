import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

// Static data representing <PERSON><PERSON><PERSON>'s past and future absences
import { Absence } from '@/interface/Teacherinterface'

const teacherAbsences = [
  {
    date: '2024-12-01',
    reason: 'Sick leave',
  },
  {
    date: '2024-12-05',
    reason: 'Medical appointment',
  },
  {
    date: '2024-12-10',
    reason: 'Family emergency',
  },
]

export default function TeacherAbsenceManagement() {
  const [absences, setAbsences] = useState<Absence[]>(teacherAbsences)
  const [absenceDate, setAbsenceDate] = useState('')
  const [absenceReason, setAbsenceReason] = useState('')

  const handleAddAbsence = () => {
    if (!absenceDate || !absenceReason) {
      alert('Please fill all fields.')
      return
    }

    const newAbsence: Absence = {
      date: absenceDate,
      reason: absenceReason,
    }

    setAbsences((prevAbsences) => [...prevAbsences, newAbsence])

    // Show an alert with the new absence details
    alert(`New absence recorded:
      Date: ${absenceDate}
      Reason: ${absenceReason}
    `)

    // Clear the input fields
    setAbsenceDate('')
    setAbsenceReason('')
  }

  // Function to categorize absences into past and future
  const categorizeAbsences = (absences: Absence[]) => {
    const today = new Date()
    const pastAbsences = absences.filter(
      (absence) => new Date(absence.date) < today
    )
    const futureAbsences = absences.filter(
      (absence) => new Date(absence.date) >= today
    )
    return { pastAbsences, futureAbsences }
  }

  const { pastAbsences, futureAbsences } = categorizeAbsences(absences)

  return (
    <div className="p-4">
      <h1 className="text-3xl font-bold text-center mb-6">
        Teacher Absence Management
      </h1>

      <p className="text-xl font-medium mb-4">Teacher: Youssef Degachi</p>

      {/* View Past Absences */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold">Past Absences:</h3>
        <div className="space-y-2">
          {pastAbsences.length > 0 ? (
            pastAbsences.map((absence, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded shadow">
                <p className="font-medium">Date: {absence.date}</p>
                <p className="text-sm text-gray-600">
                  Reason: {absence.reason}
                </p>
              </div>
            ))
          ) : (
            <p>No past absences.</p>
          )}
        </div>
      </div>

      {/* View Future Absences */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold">Future Absences:</h3>
        <div className="space-y-2">
          {futureAbsences.length > 0 ? (
            futureAbsences.map((absence, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded shadow">
                <p className="font-medium">Date: {absence.date}</p>
                <p className="text-sm text-gray-600">
                  Reason: {absence.reason}
                </p>
              </div>
            ))
          ) : (
            <p>No future absences.</p>
          )}
        </div>
      </div>

      {/* Create New Absence Request */}
      <div className="mt-6">
        <h3 className="text-xl font-semibold">Create New Absence Request:</h3>
        <div className="space-y-4">
          <div>
            <label className="font-medium">Absence Date</label>
            <Input
              type="date"
              value={absenceDate}
              onChange={(e) => setAbsenceDate(e.target.value)}
            />
          </div>

          <div>
            <label className="font-medium">Reason for Absence</label>
            <Textarea
              value={absenceReason}
              onChange={(e) => setAbsenceReason(e.target.value)}
              placeholder="Enter reason for absence"
            />
          </div>

          <div className="flex justify-center mt-4">
            <Button onClick={handleAddAbsence}>Add Absence</Button>
          </div>
        </div>
      </div>
    </div>
  )
}
