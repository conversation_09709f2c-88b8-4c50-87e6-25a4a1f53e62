import React, { useEffect, useState } from 'react'
import { timetableService } from '../../lib/api/services/timetable-service'

const TimetableTest: React.FC = () => {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTeacherTimetable = async () => {
      setLoading(true)
      try {
        const response = await timetableService.getTeacherTimetable()
        console.log('Teacher timetable data:', response)
        setData(response)
        setLoading(false)
        setError(null)
      } catch (err) {
        console.error('Error fetching teacher timetable:', err)
        setError('Failed to load timetable. Please try again later.')
        setLoading(false)
      }
    }
    
    fetchTeacherTimetable()
  }, [])

  if (loading) return <div className="p-4">Loading timetable...</div>
  if (error) return <div className="p-4 text-red-500">{error}</div>
  if (!data) return <div className="p-4">No timetable data available.</div>

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Teacher Timetable Test</h2>
      
      <div className="bg-white rounded-lg shadow-md p-4 mb-4">
        <h3 className="text-lg font-semibold mb-2">Raw Data</h3>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
      
      {data.scheduleData && Array.isArray(data.scheduleData) && data.scheduleData.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md p-4">
          <h3 className="text-lg font-semibold mb-2">Schedule Data</h3>
          
          {data.scheduleData.map((classSchedule: any, index: number) => (
            <div key={index} className="mb-4 border-b pb-4">
              <h4 className="font-medium text-blue-700">{classSchedule.class}</h4>
              
              {Object.entries(classSchedule)
                .filter(([key]) => key !== 'class')
                .map(([day, lessons]: [string, any]) => (
                  <div key={day} className="mt-2">
                    <h5 className="font-medium">{day}</h5>
                    
                    {Array.isArray(lessons) && lessons.length > 0 ? (
                      <ul className="list-disc pl-5">
                        {lessons.map((lesson: any, lessonIndex: number) => (
                          <li key={lessonIndex} className="mt-1">
                            <span className="font-medium">{lesson.subject}</span> - 
                            <span className="text-gray-600"> {lesson.time || lesson.timeSlot}</span> - 
                            <span className="text-gray-600"> Room: {lesson.salle || lesson.classroomId || 'N/A'}</span>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-gray-500 pl-5">No lessons</p>
                    )}
                  </div>
                ))}
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-4">
          <p className="text-gray-500">No schedule data available.</p>
        </div>
      )}
    </div>
  )
}

export default TimetableTest
