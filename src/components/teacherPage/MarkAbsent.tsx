import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { classes, classDetails } from '../../mockData/MarkAbsent'

export default function MarkAbsent() {
  const [selectedClassId, setSelectedClassId] = useState<number | null>(null)
  const [absentStudents, setAbsentStudents] = useState<Set<number>>(new Set())

  const handleCardClick = (classId: number) => {
    setSelectedClassId(classId)
  }

  const handleCheckboxChange = (studentId: number) => {
    setAbsentStudents((prev) => {
      const updatedSet = new Set(prev)
      if (updatedSet.has(studentId)) {
        updatedSet.delete(studentId)
      } else {
        updatedSet.add(studentId)
      }
      return updatedSet
    })
  }

  const handleSendData = () => {
    if (selectedClassId === null) return

    const classData = classDetails[selectedClassId]
    if (!classData) return

    const absentStudentNames = classData.students
      .filter(
        (student) => student.id !== undefined && absentStudents.has(student.id)
      ) // Ensure `student.id` is defined
      .map((student) => student.fullname)

    if (absentStudentNames.length > 0) {
      alert(`Absent students: ${absentStudentNames.join(', ')}`)
    } else {
      alert('No students are marked absent.')
    }
  }

  const renderClassDetails = () => {
    if (!selectedClassId) return null

    const classData = classDetails[selectedClassId]
    if (!classData) return <p>No data found for this class.</p>

    return (
      <div>
        <h2 className="text-xl font-semibold text-center mb-4">
          Class Details:{' '}
          {classes.find((cls) => cls.id === selectedClassId)?.name} -{' '}
          {classes.find((cls) => cls.id === selectedClassId)?.subject}
        </h2>

        <div className="students-list mt-6">
          <h3 className="font-semibold text-lg mb-3">Students:</h3>
          <div className="space-y-2">
            {classData.students.map((student) => (
              <div
                key={student.id}
                className="flex justify-between items-center p-3 bg-gray-50 rounded"
              >
                <p className="font-medium">{student.fullname}</p>
                <input
                  type="checkbox"
                  checked={absentStudents.has(student.id!)} // Use non-null assertion
                  onChange={() => {
                    if (student.id !== undefined) {
                      handleCheckboxChange(student.id)
                    }
                  }}
                  className="ml-4"
                />
              </div>
            ))}
          </div>
        </div>

        <div className="mt-6 flex justify-center">
          <Button onClick={handleSendData}>Send Absence Data</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <h1 className="text-3xl font-bold text-center mb-6">Class Management</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
        {classes.map((cls) => (
          <div
            key={cls.id}
            className={`bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer ${
              selectedClassId === cls.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => handleCardClick(cls.id)}
          >
            <h2 className="text-xl font-semibold text-gray-800">
              {cls.name} - {cls.subject}
            </h2>
          </div>
        ))}
      </div>

      <div className="mt-6">{renderClassDetails()}</div>
    </div>
  )
}
