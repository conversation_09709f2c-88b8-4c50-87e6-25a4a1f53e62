import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Loader2,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import {
  sessionService,
  SessionStatus,
} from '@/lib/api/services/session-service'
import { classService } from '@/lib/api/services/class-service'
import {
  attendanceService,
  AttendanceStatus,
} from '@/lib/api/services/attendance-service'

interface Session {
  id: string
  teacherId: string
  teacherName: string
  className: string
  classId?: string
  subjectName: string
  subjectId?: string
  day: string
  timeSlot: string
  date: string
  status: SessionStatus
  notes?: string
}

interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  gender?: string
}

interface AttendanceRecord {
  studentId: string
  status: 'present' | 'absent' | 'left_early'
  notes?: string
}

export default function AttendanceView() {
  const [teacherId] = useState(localStorage.getItem('id') || '')
  const { toast } = useToast()
  const [todaySessions, setTodaySessions] = useState<Session[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(true)
  const [selectedSession, setSelectedSession] = useState<Session | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [isLoadingStudents, setIsLoadingStudents] = useState(false)
  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(
    null
  )
  const [selectedStatus, setSelectedStatus] = useState<
    'present' | 'absent' | 'left_early'
  >('present')
  const [notes, setNotes] = useState('')

  // Fetch today's sessions
  useEffect(() => {
    const fetchTodaySessions = async () => {
      if (!teacherId) return

      setIsLoadingSessions(true)
      try {
        const today = format(new Date(), 'yyyy-MM-dd')

        // Use the session service to get today's sessions
        const data = await sessionService.getByDate(today)
        setTodaySessions(data)

        // Auto-select the first session if available
        if (data.length > 0) {
          setSelectedSession(data[0])
        }
      } catch (error) {
        console.error('Error fetching sessions:', error)
        toast({
          title: 'Error',
          description: "Failed to load today's sessions. Please try again.",
          variant: 'destructive',
        })
      } finally {
        setIsLoadingSessions(false)
      }
    }

    fetchTodaySessions()
  }, [teacherId, toast])

  // Fetch students when a session is selected
  useEffect(() => {
    const fetchStudents = async () => {
      if (!selectedSession) return

      setIsLoadingStudents(true)
      try {
        // Get students for the selected class
        const data = await classService.getStudents(
          selectedSession.classId || ''
        )
        setStudents(data)

        // Initialize attendance records for all students (default to present)
        const initialRecords = data.map((student: Student) => ({
          studentId: student.id,
          status: 'present' as const,
          notes: '',
        }))
        setAttendanceRecords(initialRecords)
      } catch (error) {
        console.error('Error fetching students:', error)
        toast({
          title: 'Error',
          description: 'Failed to load students. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingStudents(false)
      }
    }

    if (selectedSession) {
      fetchStudents()
    }
  }, [selectedSession, toast])

  // Update attendance record for a student
  const updateAttendanceRecord = (
    studentId: string,
    status: 'present' | 'absent' | 'left_early',
    notes: string = ''
  ) => {
    setAttendanceRecords((prev) =>
      prev.map((record) =>
        record.studentId === studentId ? { ...record, status, notes } : record
      )
    )
  }

  // Open dialog to add notes for a student
  const openNotesDialog = (
    studentId: string,
    currentStatus: 'present' | 'absent' | 'left_early',
    currentNotes: string = ''
  ) => {
    setSelectedStudentId(studentId)
    setSelectedStatus(currentStatus)
    setNotes(currentNotes)
    setIsDialogOpen(true)
  }

  // Save notes from dialog
  const saveNotes = () => {
    if (selectedStudentId) {
      updateAttendanceRecord(selectedStudentId, selectedStatus, notes)
    }
    setIsDialogOpen(false)
  }

  // Save attendance for the session
  const saveAttendance = async () => {
    if (!selectedSession) return

    setIsSaving(true)
    try {
      // Create the payload
      const payload = {
        date: selectedSession.date,
        className: selectedSession.className,
        subject: selectedSession.subjectName,
        day: selectedSession.day,
        timeSlot: selectedSession.timeSlot,
        sessionId: selectedSession.id,
        records: attendanceRecords.map((record) => ({
          studentId: record.studentId,
          status:
            record.status === 'present'
              ? AttendanceStatus.PRESENT
              : record.status === 'absent'
                ? AttendanceStatus.ABSENT
                : AttendanceStatus.LEFT_EARLY,
          notes: record.notes,
        })),
      }

      // Log the payload for debugging
      console.log('Attendance payload:', JSON.stringify(payload, null, 2))

      // 1. Save attendance records
      await attendanceService.createSessionAttendanceWithSession(payload)

      // 2. Update session status
      await sessionService.updateStatus(
        selectedSession.id,
        SessionStatus.REPORTED
      )

      // Update the session status in the UI
      setTodaySessions((prev) =>
        prev.map((session) =>
          session.id === selectedSession.id
            ? { ...session, status: SessionStatus.REPORTED }
            : session
        )
      )

      if (selectedSession) {
        setSelectedSession({
          ...selectedSession,
          status: SessionStatus.REPORTED,
        })
      }

      toast({
        title: 'Success',
        description: 'Attendance has been saved successfully.',
        variant: 'default',
      })
    } catch (error) {
      console.error('Error saving attendance:', error)
      toast({
        title: 'Error',
        description: 'Failed to save attendance. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Get status badge for a session
  const getSessionStatusBadge = (status: SessionStatus) => {
    switch (status) {
      case SessionStatus.ONGOING:
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            Ongoing
          </Badge>
        )
      case SessionStatus.REPORTED:
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Reported
          </Badge>
        )
      case SessionStatus.CANCELED:
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Canceled
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {isLoadingSessions ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : todaySessions.length === 0 ? (
        <div className="text-center py-8">
          <Calendar className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No Sessions Today</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            You don't have any sessions scheduled for today.
          </p>
        </div>
      ) : (
        <>
          <Tabs
            defaultValue={selectedSession?.id}
            onValueChange={(value) => {
              const selected = todaySessions.find((s) => s.id === value)
              if (selected) setSelectedSession(selected)
            }}
          >
            <TabsList className="mb-4">
              {todaySessions.map((session) => (
                <TabsTrigger key={session.id} value={session.id}>
                  {session.className} - {session.timeSlot}
                </TabsTrigger>
              ))}
            </TabsList>

            {todaySessions.map((session) => (
              <TabsContent key={session.id} value={session.id}>
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="flex items-center">
                          <Clock className="mr-2 h-5 w-5" />
                          {session.className} - {session.subjectName}
                        </CardTitle>
                        <CardDescription>
                          {format(new Date(session.date), 'EEEE, MMMM d, yyyy')}{' '}
                          | {session.timeSlot}
                        </CardDescription>
                      </div>
                      {getSessionStatusBadge(session.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLoadingStudents ? (
                      <div className="flex justify-center items-center h-64">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : students.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No students in this class
                      </div>
                    ) : (
                      <ScrollArea className="h-[400px]">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Notes</TableHead>
                              <TableHead className="text-right">
                                Actions
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {students.map((student) => {
                              const record = attendanceRecords.find(
                                (r) => r.studentId === student.id
                              )
                              const status = record?.status || 'present'

                              return (
                                <TableRow key={student.id}>
                                  <TableCell className="font-medium">
                                    {student.firstname} {student.lastname}
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex items-center">
                                      {status === 'present' && (
                                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                      )}
                                      {status === 'absent' && (
                                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                                      )}
                                      {status === 'left_early' && (
                                        <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
                                      )}
                                      {status === 'present'
                                        ? 'Present'
                                        : status === 'absent'
                                          ? 'Absent'
                                          : 'Left Early'}
                                    </div>
                                  </TableCell>
                                  <TableCell className="max-w-[200px] truncate">
                                    {record?.notes || '-'}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Select
                                      value={status}
                                      onValueChange={(
                                        value:
                                          | 'present'
                                          | 'absent'
                                          | 'left_early'
                                      ) => {
                                        if (value === 'left_early') {
                                          openNotesDialog(
                                            student.id,
                                            value,
                                            record?.notes || ''
                                          )
                                        } else {
                                          updateAttendanceRecord(
                                            student.id,
                                            value
                                          )
                                        }
                                      }}
                                      disabled={
                                        session.status ===
                                        SessionStatus.REPORTED
                                      }
                                    >
                                      <SelectTrigger className="w-[130px]">
                                        <SelectValue placeholder="Select status" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="present">
                                          Present
                                        </SelectItem>
                                        <SelectItem value="absent">
                                          Absent
                                        </SelectItem>
                                        <SelectItem value="left_early">
                                          Left Early
                                        </SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </TableCell>
                                </TableRow>
                              )
                            })}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={saveAttendance}
                      disabled={
                        isSaving ||
                        session.status === SessionStatus.REPORTED ||
                        isLoadingStudents
                      }
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : session.status === SessionStatus.REPORTED ? (
                        'Already Reported'
                      ) : (
                        'Save Attendance'
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </>
      )}

      {/* Notes Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Notes</DialogTitle>
            <DialogDescription>
              Add notes for student absence or early departure.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Select
                value={selectedStatus}
                onValueChange={(value: 'present' | 'absent' | 'left_early') =>
                  setSelectedStatus(value)
                }
              >
                <SelectTrigger className="col-span-4">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="present">Present</SelectItem>
                  <SelectItem value="absent">Absent</SelectItem>
                  <SelectItem value="left_early">Left Early</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Textarea
                className="col-span-4"
                placeholder="Add notes here..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveNotes}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
