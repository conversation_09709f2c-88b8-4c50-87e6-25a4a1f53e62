import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Plus, Download } from 'lucide-react'

// Sample data - replace with your actual data
const subjects = [
  { id: 1, name: 'Mathematics' },
  { id: 2, name: 'Physics' },
  { id: 3, name: 'Chemistry' },
]

const homeworkData = [
  {
    id: 1,
    subjectId: 1,
    title: 'Algebra Exercise Set 3',
    dueDate: '2024-12-28',
    description: 'Complete exercises 15-20 from Chapter 4',
    allowedFileTypes: ['pdf', 'jpg', 'png'],
    submissions: [
      {
        studentId: 1,
        studentName: '<PERSON>',
        submissionDate: '2024-12-25',
        fileName: 'algebra_hw3.pdf',
        status: 'submitted',
      },
      {
        studentId: 2,
        studentName: '<PERSON>',
        submissionDate: '2024-12-26',
        fileName: 'homework3.pdf',
        status: 'submitted',
      },
    ],
  },
]

export default function TeacherHomework() {
  const [selectedSubject, setSelectedSubject] = useState<number | null>(null)
  const [isNewHomeworkModalOpen, setIsNewHomeworkModalOpen] = useState(false)
  const [newHomework, setNewHomework] = useState({
    title: '',
    description: '',
    dueDate: '',
    allowedFileTypes: ['pdf'],
  })

  const handleNewHomeworkSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault()
    // Here you would typically make an API call to save the new homework
    console.log('New homework:', { ...newHomework, subjectId: selectedSubject })
    setIsNewHomeworkModalOpen(false)
    setNewHomework({
      title: '',
      description: '',
      dueDate: '',
      allowedFileTypes: ['pdf'],
    })
  }

  const handleDownload = (studentId: number | string, fileName: string) => {
    // Implement file download logic
    console.log(`Downloading ${fileName} for student ${studentId}`)
  }

  const NewHomeworkDialog = () => (
    <Dialog
      open={isNewHomeworkModalOpen}
      onOpenChange={setIsNewHomeworkModalOpen}
    >
      <DialogTrigger asChild>
        <Button className="mb-4">
          <Plus className="w-4 h-4 mr-2" />
          Create New Homework
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Homework Assignment</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleNewHomeworkSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={newHomework.title}
              onChange={(e) =>
                setNewHomework({ ...newHomework, title: e.target.value })
              }
              required
            />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={newHomework.description}
              onChange={(e) =>
                setNewHomework({ ...newHomework, description: e.target.value })
              }
              required
            />
          </div>
          <div>
            <Label htmlFor="dueDate">Due Date</Label>
            <Input
              id="dueDate"
              type="date"
              value={newHomework.dueDate}
              onChange={(e) =>
                setNewHomework({ ...newHomework, dueDate: e.target.value })
              }
              required
            />
          </div>
          <div>
            <Label>Allowed File Types</Label>
            <div className="flex gap-2">
              {['pdf', 'jpg', 'png'].map((type) => (
                <Button
                  key={type}
                  type="button"
                  variant={
                    newHomework.allowedFileTypes.includes(type)
                      ? 'default'
                      : 'outline'
                  }
                  onClick={() => {
                    const types = newHomework.allowedFileTypes.includes(type)
                      ? newHomework.allowedFileTypes.filter((t) => t !== type)
                      : [...newHomework.allowedFileTypes, type]
                    setNewHomework({ ...newHomework, allowedFileTypes: types })
                  }}
                  className="text-sm"
                >
                  .{type}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsNewHomeworkModalOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Create Homework</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )

  const renderHomeworkAssignments = () => {
    const filteredHomework = selectedSubject
      ? homeworkData.filter((hw) => hw.subjectId === selectedSubject)
      : homeworkData

    return (
      <div className="space-y-4">
        {filteredHomework.map((homework) => (
          <Card key={homework.id} className="p-4">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold">{homework.title}</h3>
                <p className="text-sm text-gray-600">
                  Due Date: {homework.dueDate}
                </p>
                <p className="mt-2">{homework.description}</p>
                <p className="text-sm text-gray-600 mt-1">
                  Accepted formats: {homework.allowedFileTypes.join(', ')}
                </p>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-semibold mb-2">
                Submissions ({homework.submissions.length})
              </h4>
              <div className="divide-y">
                {homework.submissions.map((submission) => (
                  <div
                    key={submission.studentId}
                    className="py-2 flex items-center justify-between"
                  >
                    <div>
                      <p className="font-medium">{submission.studentName}</p>
                      <p className="text-sm text-gray-600">
                        Submitted: {submission.submissionDate}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleDownload(
                            submission.studentId,
                            submission.fileName
                          )
                        }
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <Card className="p-4">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Homework Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <Card
            className={`p-4 cursor-pointer transition-all ${
              !selectedSubject ? 'border-primary bg-primary/5' : ''
            }`}
            onClick={() => setSelectedSubject(null)}
          >
            <CardTitle className="text-center">All Subjects</CardTitle>
          </Card>
          {subjects.map((subject) => (
            <Card
              key={subject.id}
              className={`p-4 cursor-pointer transition-all ${
                selectedSubject === subject.id
                  ? 'border-primary bg-primary/5'
                  : ''
              }`}
              onClick={() => setSelectedSubject(subject.id)}
            >
              <CardTitle className="text-center">{subject.name}</CardTitle>
            </Card>
          ))}
        </div>

        <NewHomeworkDialog />
        {renderHomeworkAssignments()}
      </CardContent>
    </Card>
  )
}
