import { useState } from 'react'
import {
  Upload,
  But<PERSON>,
  Card,
  Divider,
  Progress,
  Spin,
  Alert,
  Select,
  Form,
  Steps,
} from 'antd'
import {
  UploadOutlined,
  ArrowRightOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons'
import type { UploadFile, UploadProps } from 'antd/es/upload/interface'

// Define the types for the correction data
interface CorrectionItem {
  id: string
  title: string
  description?: string
  points: string
  details?: CorrectionDetail[]
}

interface CorrectionDetail {
  id: string
  title: string
  description?: string
}

interface ExerciseResult {
  id: string
  exerciseNumber: number
  title: string
  score: string
  maxScore: string
  corrections: CorrectionItem[]
}

// Mock data for the correction results (will be replaced with API data)
const mockCorrectionResults: ExerciseResult[] = [
  {
    id: '1',
    exerciseNumber: 1,
    title: 'Exercise 1',
    score: '1',
    maxScore: '4',
    corrections: [
      {
        id: '1-1',
        title: 'Erreur de notation mathématique',
        description:
          "L'élève cite les points sans dégager les virgules (non pertinent)",
        points: '0/1',
      },
      {
        id: '1-2',
        title: 'Erreur de calcul',
        description:
          "L'élève se trompe dans le calcul des rapports à calculer (il commet une erreur de configuration plaçant avec elle des triangles emboités).",
        points: '0/2',
      },
      {
        id: '1-3',
        title: 'Erreur de calcul - conclusion',
        description:
          'L\'élève utilise le mot "pour" et non "donc" dans sa conclusion (non pertinent)',
        points: '1/1',
      },
    ],
  },
  {
    id: '2',
    exerciseNumber: 2,
    title: 'Exercise 2',
    score: '0.75',
    maxScore: '3',
    corrections: [
      {
        id: '2-1',
        title: 'Erreur de rédaction',
        description:
          "L'élève effectue les bons calculs et la conclusion est correcte mais la rédaction n'est pas claire.",
        points: '0.75/3',
      },
    ],
  },
]

// Subject options for the dropdown
const subjectOptions = [
  { value: 'mathematics', label: 'Mathematics' },
  { value: 'physics', label: 'Physics' },
  { value: 'chemistry', label: 'Chemistry' },
  { value: 'biology', label: 'Biology' },
  { value: 'history', label: 'History' },
  { value: 'geography', label: 'Geography' },
  { value: 'literature', label: 'Literature' },
  { value: 'language', label: 'Language' },
]

// Grade level options
const gradeLevelOptions = [
  { value: 'elementary', label: 'Elementary School' },
  { value: 'middle', label: 'Middle School' },
  { value: 'high', label: 'High School' },
  { value: 'university', label: 'University' },
]

const AutoCorrectorService = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [uploading, setUploading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [correctionResults, setCorrectionResults] = useState<ExerciseResult[]>(
    []
  )
  const [error, setError] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [form] = Form.useForm()

  const handleUpload = async () => {
    try {
      // Validate the form first
      await form.validateFields()

      const formData = new FormData()
      fileList.forEach((file) => {
        formData.append('files[]', file as any)
      })

      // Add form data to the request
      const formValues = form.getFieldsValue()
      Object.keys(formValues).forEach((key) => {
        formData.append(key, formValues[key])
      })

      setUploading(true)
      setError(null)

      try {
        // This will be replaced with the actual API call
        // const response = await services.teacher.uploadForCorrection(formData)

        // For now, simulate API call with a timeout
        await new Promise((resolve) => setTimeout(resolve, 2000))
        setUploading(false)
        setProcessing(true)

        // Simulate processing time
        await new Promise((resolve) => setTimeout(resolve, 3000))
        setProcessing(false)

        // Set mock data (will be replaced with actual API response)
        setCorrectionResults(mockCorrectionResults)

        // Move to the results step
        setCurrentStep(1)
      } catch (error) {
        console.error('Error uploading files:', error)
        setError('Failed to upload files. Please try again.')
        setUploading(false)
        setProcessing(false)
      }
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  const props: UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file)
      const newFileList = fileList.slice()
      newFileList.splice(index, 1)
      setFileList(newFileList)
    },
    beforeUpload: (file) => {
      // Check if file is an image
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        setError('You can only upload image files!')
        return false
      }

      // Check file size (limit to 10MB)
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        setError('Image must be smaller than 10MB!')
        return false
      }

      setFileList([...fileList, file])
      return false
    },
    fileList,
  }

  const resetCorrection = () => {
    setFileList([])
    setCorrectionResults([])
    setError(null)
    setCurrentStep(0)
    form.resetFields()
  }

  const steps = [
    {
      title: 'Upload',
      content: (
        <div className="max-w-4xl mx-auto">
          <Card title="Upload Student Work" className="mb-6">
            <Form form={form} layout="vertical" requiredMark={false}>
              <Form.Item
                name="subject"
                label="Subject"
                rules={[{ required: true, message: 'Please select a subject' }]}
              >
                <Select
                  placeholder="Select the subject to correct"
                  options={subjectOptions}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="gradeLevel"
                label="Grade Level"
                rules={[
                  { required: true, message: 'Please select a grade level' },
                ]}
              >
                <Select
                  placeholder="Select the grade level"
                  options={gradeLevelOptions}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="assignmentTitle"
                label="Assignment Title (Optional)"
              >
                <input
                  type="text"
                  placeholder="Enter assignment title"
                  className="w-full p-2 border rounded"
                />
              </Form.Item>

              <Divider />

              <Form.Item
                label="Student Work"
                name="studentWork"
                rules={[
                  {
                    required: true,
                    message: 'Please upload an image of student work',
                    validator: () => {
                      if (fileList.length === 0) {
                        return Promise.reject(
                          'Please upload an image of student work'
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
              >
                <Upload {...props} listType="picture">
                  <Button icon={<UploadOutlined />}>Select Image</Button>
                </Upload>
              </Form.Item>
            </Form>

            <div className="mt-4">
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={fileList.length === 0}
                loading={uploading}
                style={{ marginTop: 16 }}
              >
                {uploading ? 'Uploading' : 'Start Correction'}
              </Button>
            </div>

            {processing && (
              <div className="mt-6">
                <Spin tip="Processing image and analyzing content..." />
                <Progress percent={75} status="active" className="mt-4" />
                <p className="text-gray-500 mt-2">
                  This may take a few moments as we analyze the handwriting and
                  mathematical notation.
                </p>
              </div>
            )}
          </Card>
        </div>
      ),
    },
    {
      title: 'Results',
      content: (
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Correction Results</h2>
            <Button onClick={resetCorrection}>New Correction</Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-100 p-3 font-medium">Student Work</div>
              <div className="p-4">
                {fileList.length > 0 && (
                  <img
                    src={URL.createObjectURL(fileList[0] as any)}
                    alt="Student work"
                    className="w-full h-auto border"
                  />
                )}
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-100 p-3 font-medium">AI Analysis</div>
              <div className="p-4 max-h-[600px] overflow-y-auto">
                {correctionResults.map((exercise) => (
                  <Card
                    key={exercise.id}
                    title={`Exercise ${exercise.exerciseNumber}`}
                    className="mb-4"
                    extra={
                      <span className="text-blue-600">
                        {exercise.score}/{exercise.maxScore}
                      </span>
                    }
                  >
                    {exercise.corrections.map((correction) => (
                      <div
                        key={correction.id}
                        className="mb-3 pb-3 border-b last:border-b-0"
                      >
                        <div className="flex justify-between">
                          <div className="font-medium">{correction.title}</div>
                          <div className="text-blue-600">
                            {correction.points}
                          </div>
                        </div>
                        {correction.description && (
                          <p className="text-gray-600 text-sm mt-1">
                            {correction.description}
                          </p>
                        )}
                      </div>
                    ))}
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ]

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Auto Corrector</h1>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          className="mb-4"
          closable
          onClose={() => setError(null)}
        />
      )}

      <Steps
        current={currentStep}
        items={[
          {
            title: 'Upload & Configure',
          },
          {
            title: 'Correction Results',
          },
        ]}
        className="mb-8"
      />

      <div className="steps-content">{steps[currentStep].content}</div>

      <div className="steps-action mt-6 flex justify-between">
        {currentStep > 0 && (
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => setCurrentStep(currentStep - 1)}
          >
            Previous
          </Button>
        )}

        {currentStep === 0 && (
          <Button
            type="primary"
            onClick={handleUpload}
            disabled={fileList.length === 0}
            loading={uploading || processing}
          >
            {uploading
              ? 'Uploading'
              : processing
                ? 'Processing'
                : 'Start Correction'}
            {!uploading && !processing && <ArrowRightOutlined />}
          </Button>
        )}
      </div>
    </div>
  )
}

export default AutoCorrectorService
