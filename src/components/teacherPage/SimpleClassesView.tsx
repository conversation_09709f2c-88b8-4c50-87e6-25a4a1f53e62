import { useState, useEffect } from 'react'
import { api } from '@/lib/api/axios-instance'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, Search, Users, GraduationCap } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface Session {
  id: string
  teacherid: string
  teachername: string
  classname: string
  classid: string
  subjectname: string
  subjectid: string
  day: string
  timeslot: string
  date: string
  status: string
}

interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  gender?: string
  avatar?: string
}

interface Class {
  id: string
  name: string
  students: Student[]
}

export default function SimpleClassesView() {
  const [teacherId] = useState(localStorage.getItem('id') || '')
  const { toast } = useToast()
  const [classes, setClasses] = useState<Class[]>([])
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch teacher's sessions and class data
  useEffect(() => {
    const fetchData = async () => {
      if (!teacherId) return

      setIsLoading(true)
      try {
        // Get all sessions for the teacher
        const sessionsResponse = await api.get('/sessions')
        console.log('Sessions:', sessionsResponse.data)

        // Extract unique class IDs from sessions
        const uniqueClassIds = [
          ...new Set(
            sessionsResponse.data
              .filter((session: Session) => session.classid) // Filter out sessions with no classid
              .map((session: Session) => session.classid)
          ),
        ]
        console.log('Unique class IDs:', uniqueClassIds)

        // Fetch class data for each unique class ID
        const classesData: Class[] = []

        for (const classId of uniqueClassIds) {
          try {
            // Get class details (which includes students)
            const classResponse = await api.get(`/class/${classId}`)
            console.log(`Class ${classId} details:`, classResponse.data)

            classesData.push({
              id: String(classId), // Explicitly convert to string
              name: classResponse.data.name,
              students: classResponse.data.students || [],
            })
          } catch (error) {
            console.error(`Error fetching details for class ${classId}:`, error)
          }
        }

        setClasses(classesData)

        // Auto-select the first class if available
        if (classesData.length > 0) {
          setSelectedClass(classesData[0])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load class data. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [teacherId, toast])

  // Filter students based on search term
  const filteredStudents =
    selectedClass?.students.filter((student) => {
      if (!searchTerm) return true

      const fullName = `${student.firstname} ${student.lastname}`.toLowerCase()
      const email = student.email.toLowerCase()
      const term = searchTerm.toLowerCase()

      return fullName.includes(term) || email.includes(term)
    }) || []

  // Function to get initials from name
  const getInitials = (firstname: string, lastname: string) => {
    return `${firstname.charAt(0)}${lastname.charAt(0)}`.toUpperCase()
  }

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : classes.length === 0 ? (
        <div className="text-center py-8">
          <GraduationCap className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No Classes Assigned</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            You don't have any classes assigned to you yet.
          </p>
        </div>
      ) : (
        <>
          <Tabs
            defaultValue={selectedClass?.id}
            onValueChange={(value) => {
              const selected = classes.find((c) => c.id === value)
              if (selected) setSelectedClass(selected)
            }}
          >
            <TabsList className="mb-4">
              {classes.map((cls) => (
                <TabsTrigger key={cls.id} value={cls.id}>
                  {cls.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {classes.map((cls) => (
              <TabsContent key={cls.id} value={cls.id}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="mr-2 h-5 w-5" />
                      {cls.name} Students
                    </CardTitle>
                    <CardDescription>
                      View all students in {cls.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between mb-4">
                      <div className="relative w-full max-w-sm">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search students..."
                          className="pl-8"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                      <Badge variant="outline" className="ml-2 h-9 px-3 py-2">
                        {cls.id === selectedClass?.id
                          ? filteredStudents.length
                          : cls.students.length}{' '}
                        Students
                      </Badge>
                    </div>

                    {cls.id === selectedClass?.id &&
                    filteredStudents.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchTerm
                          ? 'No students match your search'
                          : 'No students in this class'}
                      </div>
                    ) : (
                      <ScrollArea className="h-[400px]">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>Email</TableHead>
                              <TableHead>Gender</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {(cls.id === selectedClass?.id
                              ? filteredStudents
                              : cls.students
                            ).map((student) => (
                              <TableRow key={student.id}>
                                <TableCell className="font-medium">
                                  <div className="flex items-center">
                                    <Avatar className="h-8 w-8 mr-2">
                                      <AvatarImage
                                        src={student.avatar}
                                        alt={`${student.firstname} ${student.lastname}`}
                                      />
                                      <AvatarFallback>
                                        {getInitials(
                                          student.firstname,
                                          student.lastname
                                        )}
                                      </AvatarFallback>
                                    </Avatar>
                                    {student.firstname} {student.lastname}
                                  </div>
                                </TableCell>
                                <TableCell>{student.email}</TableCell>
                                <TableCell>
                                  {student.gender || 'Not specified'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </>
      )}
    </div>
  )
}
