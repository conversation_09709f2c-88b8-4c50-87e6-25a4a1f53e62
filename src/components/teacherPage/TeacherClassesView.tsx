import { useState, useEffect } from 'react'
import { teacherService } from '@/lib/api/services/teacher-service'
import { api } from '@/lib/api/axios-instance'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Loader2,
  Search,
  Users,
  GraduationCap,
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  User,
} from 'lucide-react'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface Class {
  id: string
  name: string
  grade_id?: string
  grade_name?: string
  supervisor_id?: string
  created_at?: string
  updated_at?: string
}

interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  cin?: string
  gender?: string
  parentId?: string
  classId?: string
  birthday?: string
  phone?: string
  avatar?: string
}

export default function TeacherClassesView() {
  const [teacherId] = useState(localStorage.getItem('id') || '')
  const { toast } = useToast()
  const [classes, setClasses] = useState<Class[]>([])
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)
  const [isLoadingStudents, setIsLoadingStudents] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list')

  // Fetch teacher's classes
  useEffect(() => {
    const fetchClasses = async () => {
      if (!teacherId) return

      setIsLoadingClasses(true)
      try {
        // Get real data from API
        const teacherClasses = await teacherService.getClasses(teacherId)
        console.log('Received teacher classes:', teacherClasses)

        // Convert to the expected format if needed
        const formattedClasses = teacherClasses.map((cls: any) => {
          return {
            id: String(cls.id || ''),
            name: cls.name || 'Unknown Class',
            grade_id: cls.grade_id || '',
            grade_name: cls.grade_name || '',
            supervisor_id: cls.supervisor_id || '',
            created_at: cls.created_at || '',
            updated_at: cls.updated_at || '',
          }
        })

        setClasses(formattedClasses)

        // Auto-select the first class if available
        if (formattedClasses.length > 0) {
          setSelectedClass(formattedClasses[0])
        }
      } catch (error) {
        console.error('Error fetching classes:', error)
        toast({
          title: 'Error',
          description: 'Failed to load classes. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingClasses(false)
      }
    }

    fetchClasses()
  }, [teacherId, toast])

  // Fetch students when a class is selected
  useEffect(() => {
    const fetchStudents = async () => {
      if (!selectedClass) return

      setIsLoadingStudents(true)
      try {
        // Get the real class ID from the sessions
        const sessionsResponse = await teacherService.getSessions()
        console.log('Fetched all sessions:', sessionsResponse)

        // Find the first session for this class to get the real class ID
        const classSession = sessionsResponse.find(
          (session: any) => session.className === selectedClass.name
        )

        if (classSession && classSession.classId) {
          console.log('Found class ID:', classSession.classId)

          // Directly fetch the class with students included using the API instance
          const response = await api.get(`/class/${classSession.classId}`)
          const classData = response.data
          console.log('Fetched class data:', classData)

          if (
            classData &&
            classData.students &&
            Array.isArray(classData.students)
          ) {
            // Format the students
            const formattedStudents = classData.students.map(
              (student: any) => ({
                id: String(student.id || ''),
                firstname: student.firstname || '',
                lastname: student.lastname || '',
                email: student.email || '',
                cin: student.cin || '',
                gender: student.gender || '',
                parentId: student.parentId || '',
                classId: classSession.classId,
                birthday: student.birthday || '',
                phone: student.phone || '',
                avatar: student.avatar || undefined,
              })
            )

            console.log('Formatted students:', formattedStudents)
            setStudents(formattedStudents)
          } else {
            console.log('No students found in class data')
            setStudents([])
          }
        } else {
          console.log('No session found with class ID')
          setStudents([])
        }
      } catch (error) {
        console.error('Error fetching students:', error)
        toast({
          title: 'Error',
          description: 'Failed to load students. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingStudents(false)
      }
    }

    if (selectedClass) {
      fetchStudents()
    }
  }, [selectedClass, toast])

  // Filter students based on search term
  const filteredStudents = students.filter((student) => {
    const fullName = `${student.firstname} ${student.lastname}`.toLowerCase()
    return (
      fullName.includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  // Function to get initials from name
  const getInitials = (firstname: string, lastname: string) => {
    return `${firstname.charAt(0)}${lastname.charAt(0)}`.toUpperCase()
  }

  // Function to view student details
  const viewStudentDetails = (student: Student) => {
    setSelectedStudent(student)
    setViewMode('detail')
  }

  // Function to go back to list view
  const backToList = () => {
    setViewMode('list')
    setSelectedStudent(null)
  }

  // Render student details view
  const renderStudentDetails = () => {
    if (!selectedStudent) return null

    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={backToList}
            className="mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to List
          </Button>
          <h2 className="text-2xl font-bold">Student Details</h2>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Student Profile Card */}
          <Card className="w-full md:w-1/3">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <Avatar className="h-24 w-24">
                  <AvatarImage
                    src={selectedStudent.avatar}
                    alt={`${selectedStudent.firstname} ${selectedStudent.lastname}`}
                  />
                  <AvatarFallback className="text-lg">
                    {getInitials(
                      selectedStudent.firstname,
                      selectedStudent.lastname
                    )}
                  </AvatarFallback>
                </Avatar>
                <div className="text-center">
                  <h3 className="text-xl font-semibold">
                    {selectedStudent.firstname} {selectedStudent.lastname}
                  </h3>
                  <p className="text-muted-foreground">
                    {selectedClass?.name || 'Unknown Class'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Student Information Card */}
          <Card className="w-full md:w-2/3">
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p>{selectedStudent.email}</p>
                  </div>
                </div>

                {selectedStudent.phone && (
                  <div className="flex items-center">
                    <Phone className="h-5 w-5 mr-2 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p>{selectedStudent.phone}</p>
                    </div>
                  </div>
                )}

                {selectedStudent.birthday && (
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Birthday</p>
                      <p>
                        {new Date(
                          selectedStudent.birthday
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {selectedStudent.gender && (
                  <div className="flex items-center">
                    <User className="h-5 w-5 mr-2 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Gender</p>
                      <p>{selectedStudent.gender}</p>
                    </div>
                  </div>
                )}

                {selectedStudent.cin && (
                  <div className="flex items-center">
                    <div className="h-5 w-5 mr-2 flex items-center justify-center text-muted-foreground">
                      ID
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">CIN</p>
                      <p>{selectedStudent.cin}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Render the main content based on view mode
  const renderContent = () => {
    if (viewMode === 'detail') {
      return renderStudentDetails()
    }

    return (
      <div className="space-y-6">
        {isLoadingClasses ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : classes.length === 0 ? (
          <div className="text-center py-8">
            <GraduationCap className="h-12 w-12 mx-auto text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No Classes Assigned</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              You don't have any classes assigned to you yet.
            </p>
          </div>
        ) : (
          <>
            <Tabs
              defaultValue={selectedClass?.id}
              onValueChange={(value) => {
                const selected = classes.find((c) => c.id === value)
                if (selected) setSelectedClass(selected)
              }}
            >
              <TabsList className="mb-4">
                {classes.map((cls) => (
                  <TabsTrigger key={cls.id} value={cls.id}>
                    {cls.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              {classes.map((cls) => (
                <TabsContent key={cls.id} value={cls.id}>
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Users className="mr-2 h-5 w-5" />
                        {cls.name} Students
                      </CardTitle>
                      <CardDescription>
                        View all students in {cls.name}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between mb-4">
                        <div className="relative w-full max-w-sm">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search students..."
                            className="pl-8"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                        <Badge variant="outline" className="ml-2 h-9 px-3 py-2">
                          {filteredStudents.length} Students
                        </Badge>
                      </div>

                      {isLoadingStudents ? (
                        <div className="flex justify-center items-center h-64">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      ) : filteredStudents.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          {searchTerm ? (
                            'No students match your search'
                          ) : (
                            <>
                              <p>No students in this class</p>
                              <p className="mt-2 text-sm">
                                Students need to be added to this class by an
                                administrator.
                              </p>
                              <p className="mt-1 text-sm">
                                Please contact your administrator to add
                                students to this class.
                              </p>
                            </>
                          )}
                        </div>
                      ) : (
                        <ScrollArea className="h-[400px]">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Gender</TableHead>
                                <TableHead className="text-right">
                                  Actions
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {filteredStudents.map((student) => (
                                <TableRow key={student.id}>
                                  <TableCell className="font-medium">
                                    <div className="flex items-center">
                                      <Avatar className="h-8 w-8 mr-2">
                                        <AvatarImage
                                          src={student.avatar}
                                          alt={`${student.firstname} ${student.lastname}`}
                                        />
                                        <AvatarFallback>
                                          {getInitials(
                                            student.firstname,
                                            student.lastname
                                          )}
                                        </AvatarFallback>
                                      </Avatar>
                                      {student.firstname} {student.lastname}
                                    </div>
                                  </TableCell>
                                  <TableCell>{student.email}</TableCell>
                                  <TableCell>
                                    {student.gender || 'Not specified'}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        viewStudentDetails(student)
                                      }
                                    >
                                      View Details
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </ScrollArea>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          </>
        )}
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">My Classes</h1>
      {renderContent()}
    </div>
  )
}
