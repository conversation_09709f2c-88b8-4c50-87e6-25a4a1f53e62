import React from 'react'

interface TeacherHeaderProps {
  onLogout: () => void
}

const TeacherHeader: React.FC<TeacherHeaderProps> = ({}) => {
  return (
    <header className="bg-white shadow-sm mb-6 p-4">
      <div className="container mx-auto flex justify-between items-center">
        <div className="text-xl font-bold">
          <span className="text-[#525FE1]">Teacher</span> Dashboard
        </div>
      </div>
    </header>
  )
}

export default TeacherHeader
