import { useState } from 'react'
import {
  LayoutDashboard,
  Calendar,
  BookOpen,
  GraduationCap,
  Calculator,
  Users,
  ClipboardCheck,
  History,
  Brain,
} from 'lucide-react'
import TeacherService from './TeacherService'
import TeacherHomework from './TeacherHomeWork'
import TeacherAbsenceManagement from './TeacherAbsenceManagement'
import { ScheduleView, StudentScore, StudentNotes } from '../shared'
import TeacherClassesView from './TeacherClassesView'
import AttendanceView from './AttendanceView'
import SessionHistoryView from './SessionHistoryView'
import AutoCorrector from './AutoCorrector'

const navigation = [
  { name: 'Teacher Service', icon: LayoutDashboard, component: TeacherService },
  { name: 'Classes', icon: Users, component: TeacherClassesView },
  {
    name: 'Schedule View',
    icon: Calendar,
    component: () => <ScheduleView userRole="teacher" initialSchedule={{}} />,
  },
  { name: 'Attendance', icon: ClipboardCheck, component: AttendanceView },
  { name: 'Session History', icon: History, component: SessionHistoryView },
  { name: 'Student Homework', icon: Calculator, component: TeacherHomework },
  {
    name: 'Absence Management',
    icon: BookOpen,
    component: TeacherAbsenceManagement,
  },
  {
    name: 'Auto Corrector',
    icon: Brain,
    component: AutoCorrector,
  },
  {
    name: 'Student Notes',
    icon: GraduationCap,
    component: () => <StudentNotes userRole="teacher" />,
  },
  {
    name: 'Student Score',
    icon: Calculator,
    component: () => <StudentScore userRole="teacher" />,
  },
]

export default function TeacherLayout() {
  const [currentPage, setCurrentPage] = useState('Teacher Service')

  const Component =
    navigation.find((item) => item.name === currentPage)?.component ||
    TeacherService

  return (
    <div className="flex h-screen">
      <nav className="w-64 bg-gray-100 p-4">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => setCurrentPage(item.name)}
            className={`flex items-center w-full p-2 rounded ${
              currentPage === item.name ? 'bg-blue-500 text-white' : ''
            }`}
          >
            <item.icon className="mr-2 h-5 w-5" />
            {item.name}
          </button>
        ))}
      </nav>
      <main className="flex-1 p-4">
        <Component />
      </main>
    </div>
  )
}
