import { useState, useEffect } from 'react'
import { teacherService } from '@/lib/api/services/teacher-service'
import { classService } from '@/lib/api/services/class-service'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, Search, Users, GraduationCap } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'

interface Class {
  id: string
  name: string
  grade_id?: string
  grade_name?: string
  supervisor_id?: string
  created_at?: string
  updated_at?: string
}

interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  cin?: string
  gender?: string
  parentId?: string
  classId?: string
}

export default function ClassesView() {
  const [teacherId] = useState(localStorage.getItem('id') || '')
  const { toast } = useToast()
  const [classes, setClasses] = useState<Class[]>([])
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)
  const [isLoadingStudents, setIsLoadingStudents] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Fetch teacher's classes
  useEffect(() => {
    const fetchClasses = async () => {
      if (!teacherId) return

      setIsLoadingClasses(true)
      try {
        const teacherClasses = await teacherService.getClasses(teacherId)
        setClasses(teacherClasses)

        // Auto-select the first class if available
        if (teacherClasses.length > 0) {
          setSelectedClass(teacherClasses[0])
        }
      } catch (error) {
        console.error('Error fetching classes:', error)
        toast({
          title: 'Error',
          description: 'Failed to load classes. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingClasses(false)
      }
    }

    fetchClasses()
  }, [teacherId, toast])

  // Fetch students when a class is selected
  useEffect(() => {
    const fetchStudents = async () => {
      if (!selectedClass) return

      setIsLoadingStudents(true)
      try {
        // Use the class service to get students for the selected class
        const data = await classService.getStudents(selectedClass.id)
        setStudents(data)
      } catch (error) {
        console.error('Error fetching students:', error)
        toast({
          title: 'Error',
          description: 'Failed to load students. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setIsLoadingStudents(false)
      }
    }

    if (selectedClass) {
      fetchStudents()
    }
  }, [selectedClass, toast])

  // Filter students based on search term
  const filteredStudents = students.filter((student) => {
    if (!searchTerm) return true

    const fullName = `${student.firstname} ${student.lastname}`.toLowerCase()
    const email = student.email.toLowerCase()
    const term = searchTerm.toLowerCase()

    return fullName.includes(term) || email.includes(term)
  })

  return (
    <div className="space-y-6">
      {isLoadingClasses ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : classes.length === 0 ? (
        <div className="text-center py-8">
          <GraduationCap className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No Classes Assigned</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            You don't have any classes assigned to you yet.
          </p>
        </div>
      ) : (
        <>
          <Tabs
            defaultValue={selectedClass?.id}
            onValueChange={(value) => {
              const selected = classes.find((c) => c.id === value)
              if (selected) setSelectedClass(selected)
            }}
          >
            <TabsList className="mb-4">
              {classes.map((cls) => (
                <TabsTrigger key={cls.id} value={cls.id}>
                  {cls.name}
                </TabsTrigger>
              ))}
            </TabsList>

            {classes.map((cls) => (
              <TabsContent key={cls.id} value={cls.id}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="mr-2 h-5 w-5" />
                      {cls.name} Students
                    </CardTitle>
                    <CardDescription>
                      View all students in {cls.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between mb-4">
                      <div className="relative w-full max-w-sm">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search students..."
                          className="pl-8"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                      <Badge variant="outline" className="ml-2 h-9 px-3 py-2">
                        {filteredStudents.length} Students
                      </Badge>
                    </div>

                    {isLoadingStudents ? (
                      <div className="flex justify-center items-center h-64">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : filteredStudents.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchTerm
                          ? 'No students match your search'
                          : 'No students in this class'}
                      </div>
                    ) : (
                      <ScrollArea className="h-[400px]">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>Email</TableHead>
                              <TableHead>Gender</TableHead>
                              <TableHead className="text-right">
                                Actions
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredStudents.map((student) => (
                              <TableRow key={student.id}>
                                <TableCell className="font-medium">
                                  {student.firstname} {student.lastname}
                                </TableCell>
                                <TableCell>{student.email}</TableCell>
                                <TableCell>
                                  {student.gender || 'Not specified'}
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button variant="ghost" size="sm">
                                    View Details
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </ScrollArea>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </>
      )}
    </div>
  )
}
