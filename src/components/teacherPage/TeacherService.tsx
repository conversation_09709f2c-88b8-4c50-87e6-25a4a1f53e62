import { services } from '@/mockData/teacherServiceData'
import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import TeacherHeader from './TeacherHeader'

export default function TeacherService() {
  const handleServiceClick = (path: string) => {
    window.location.href = path
  }

  const handleLogout = () => {
    // Clear all localStorage items
    localStorage.removeItem('access_token')
    localStorage.removeItem('id')
    localStorage.removeItem('role')
    localStorage.removeItem('firstname')
    localStorage.removeItem('lastname')

    // Redirect to login page
    window.location.href = '/login'
  }

  return (
    <>
      <TeacherHeader onLogout={handleLogout} />
      <div className="flex flex-col max-w-5xl mx-auto p-6 space-y-8">
        <section>
          <h2 className="text-2xl font-bold mb-4">Services</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {services.map((service, index) => (
              <Card
                key={index}
                className="hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => handleServiceClick(service.href)}
              >
                <CardContent className="p-4">
                  <div className="flex flex-col items-center text-center space-y-2">
                    {service.icon}
                    <span className="text-sm font-medium flex items-center justify-center gap-2">
                      {service.name}
                      {service.soon && (
                        <span className="ml-1 px-2 py-0.5 rounded-full bg-yellow-400 text-xs text-white font-semibold animate-pulse">
                          Soon
                        </span>
                      )}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
            <Card className="hover:bg-muted/50 transition-colors">
              <CardContent className="p-4">
                <Button
                  variant="ghost"
                  className="w-full h-full flex flex-col items-center space-y-2"
                ></Button>
              </CardContent>
            </Card>
          </div>
        </section>
        <Card className="hover:bg-muted/50 transition-colors">
          <CardContent className="p-4">
            <Button
              variant="ghost"
              className="w-full h-full flex flex-col items-center space-y-2"
            >
              Success is the sum of small efforts—repeated daily. Show up, even
              when it’s hard."
            </Button>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
