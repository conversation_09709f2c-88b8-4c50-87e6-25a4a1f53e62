import React, { useEffect, useState } from 'react'
import { TimetableAssignment } from '../../types/timetable'
import { timetableService } from '../../lib/api/services/timetable-service'

// Define the response type for the teacher timetable API
interface TeacherTimetableResponse {
  scheduleData: Array<{
    class: string
    [day: string]: any[] | string // Can be either an array of assignments or the class name
  }>
}

const DAYS_ORDER = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
]

const TeacherTimetable: React.FC = () => {
  const [data, setData] = useState<TeacherTimetableResponse | null>(null)
  const [processedData, setProcessedData] = useState<Record<string, TimetableAssignment[]>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTeacherTimetable = async () => {
      setLoading(true)
      try {
        // Get the teacher timetable using the timetable service
        const response = await timetableService.getTeacherTimetable()
        console.log('Teacher timetable data:', response)
        setData(response)

        // Process the data into the format needed for display
        if (response && response.scheduleData && Array.isArray(response.scheduleData)) {
          const processed: Record<string, TimetableAssignment[]> = {}

          // Initialize empty arrays for each day
          DAYS_ORDER.forEach(day => {
            processed[day] = []
          })

          // Process each class schedule
          response.scheduleData.forEach((classSchedule: {
            class: string;
            [day: string]: any[] | string;
          }) => {
            // Process each day's assignments
            DAYS_ORDER.forEach(day => {
              if (classSchedule[day] && Array.isArray(classSchedule[day])) {
                // Add each assignment to the corresponding day
                classSchedule[day].forEach((assignment: any) => {
                  processed[day].push({
                    subject: assignment.subject,
                    teacher: assignment.teacher,
                    teacherID: assignment.teacherID || assignment.teacherId,
                    salle: assignment.salle || assignment.classroomId,
                    time: assignment.time || assignment.timeSlot,
                    day: day,
                    class: classSchedule.class || assignment.className,
                    group1: assignment.group1,
                    group2: assignment.group2
                  })
                })
              }
            })
          })

          setProcessedData(processed)
        }

        setLoading(false)
        setError(null)
      } catch (err) {
        console.error('Error fetching teacher timetable:', err)
        setError('Failed to load timetable. Please try again later.')
        setLoading(false)
      }
    }

    fetchTeacherTimetable()
  }, [])

  if (loading) return <div className="p-4">Loading timetable...</div>
  if (error) return <div className="p-4 text-red-500">{error}</div>
  if (!data) return <div className="p-4">No timetable data available. Please contact your administrator.</div>
  if (!data.scheduleData || !Array.isArray(data.scheduleData) || data.scheduleData.length === 0) {
    return <div className="p-4">No schedule data found in your timetable. Please contact your administrator.</div>
  }

  // Get current day name
  const today = new Date()
  const todayName = DAYS_ORDER[today.getDay()]

  // Check if we have any lessons

  return (
    <div className="p-4">
      <button
        onClick={() => window.history.back()}
        className="mb-4 flex items-center gap-2 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded shadow transition"
        aria-label="Back"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            d="M15 19l-7-7 7-7"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        Back
      </button>
      <h2 className="text-2xl font-extrabold mb-6 text-blue-700 flex items-center gap-2">
        <svg
          className="w-7 h-7 text-blue-500"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <rect
            x="3"
            y="4"
            width="18"
            height="18"
            rx="2"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
          />
          <path
            d="M16 2v4M8 2v4M3 10h18"
            stroke="currentColor"
            strokeWidth="2"
          />
        </svg>
        Teacher Timetable
      </h2>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300 shadow-lg rounded-lg overflow-hidden bg-white">
          <thead>
            <tr>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Day
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Time
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Subject
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Class
              </th>
              <th className="border px-3 py-2 bg-blue-100 text-blue-800">
                Room
              </th>
            </tr>
          </thead>
          <tbody>
            {DAYS_ORDER.map((day) => {
              const lessons = processedData[day] || []
              const isToday = day === todayName
              if (!lessons.length) {
                return (
                  <tr
                    key={day}
                    className={isToday ? 'bg-yellow-50' : 'bg-gray-50'}
                  >
                    <td
                      className={`border px-3 py-2 font-semibold ${isToday ? 'text-yellow-700' : 'text-gray-700'}`}
                    >
                      {day}
                    </td>
                    <td className="border px-3 py-2 text-gray-400" colSpan={4}>
                      No lessons
                    </td>
                  </tr>
                )
              }
              return lessons.map((lesson, idx) => (
                <tr
                  key={day + idx}
                  className={
                    isToday
                      ? 'bg-yellow-50'
                      : idx % 2 === 0
                        ? 'bg-white'
                        : 'bg-gray-50'
                  }
                >
                  {idx === 0 && (
                    <td
                      className={`border px-3 py-2 font-semibold ${isToday ? 'text-yellow-700' : 'text-gray-700'}`}
                      rowSpan={lessons.length}
                    >
                      {day}
                    </td>
                  )}
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-blue-50 text-blue-700 rounded px-2 py-0.5 font-mono text-sm">
                      {lesson.time}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-green-100 text-green-800 rounded px-2 py-0.5 font-semibold">
                      {lesson.subject}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-purple-100 text-purple-800 rounded px-2 py-0.5">
                      {lesson.class || lesson.class || '-'}
                    </span>
                  </td>
                  <td className="border px-3 py-2">
                    <span className="inline-block bg-gray-200 text-gray-700 rounded px-2 py-0.5">
                      {lesson.salle}
                    </span>
                  </td>
                </tr>
              ))
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default TeacherTimetable
