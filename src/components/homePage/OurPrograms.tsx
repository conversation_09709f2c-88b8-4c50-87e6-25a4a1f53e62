import UserSay from '../UserSay'
import { useState } from 'react'
import { ArrowRight, Check } from 'lucide-react'
import { useTranslation } from 'react-i18next'
// import { Link } from '@tanstack/react-router' // Removed unused import

function OurPrograms() {
  const { t } = useTranslation('home');
  
  const [formData, setFormData] = useState({
    schoolName: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    enrollmentCount: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Format data to match the SheetDB API requirements
      const dataToSubmit = {
        data: [
          {
            fullname: `${formData.firstName} ${formData.lastName}`,
            email: formData.email,
            schoolname: formData.schoolName,
            phonenumber: formData.phone,
            enrollmentcount: formData.enrollmentCount,
            message: formData.message,
            type: 'join', // Add a type field to distinguish join requests
          },
        ],
      }

      // Send data to SheetDB API
      const response = await fetch(
        'https://sheetdb.io/api/v1/b81ea9nhoaxhx',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(dataToSubmit),
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit form')
      }

      // Success
      setIsSubmitting(false)
      setIsSubmitted(true)

      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setFormData({
          schoolName: '',
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          enrollmentCount: '',
          message: '',
        })
      }, 3000)
    } catch (error) {
      console.error('Submission error:', error)
      alert('Failed to submit form. Please try again.')
      setIsSubmitting(false)
    }
  }

  // Keep the original programs array to avoid TypeScript issues
  const programs = [
    {
      title: t('ourPrograms.programs.0.title'),
      image: '/images/landingpage/school.jpg',
      description: t('ourPrograms.programs.0.description'),
    },
    {
      title: t('ourPrograms.programs.1.title'),
      image: '/images/landingpage/trainingCenter.jpg',
      description: t('ourPrograms.programs.1.description'),
    },
    {
      title: t('ourPrograms.programs.2.title'),
      image: '/images/landingpage/uni.jpg',
      description: t('ourPrograms.programs.2.description'),
    },
  ]

  // Keep the original enrollment options
  const enrollmentOptions = [
    t('ourPrograms.contactForm.fields.enrollmentCount.options.0'),
    t('ourPrograms.contactForm.fields.enrollmentCount.options.1'),
    t('ourPrograms.contactForm.fields.enrollmentCount.options.2'),
    t('ourPrograms.contactForm.fields.enrollmentCount.options.3'),
  ]

  // Keep the original benefits items
  const benefitItems = [
    t('ourPrograms.benefits.items.0'),
    t('ourPrograms.benefits.items.1'),
    t('ourPrograms.benefits.items.2'),
    t('ourPrograms.benefits.items.3'),
    t('ourPrograms.benefits.items.4'),
    t('ourPrograms.benefits.items.5'),
    t('ourPrograms.benefits.items.6'),
  ]

  return (
    <div className="container mx-auto px-4 py-16">
      <UserSay
        blueWord={t('ourPrograms.title.blueWord')}
        title={t('ourPrograms.title.rest')}
        description={t('ourPrograms.description')}
      />
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-20">
        {programs.map((program, index) => (
          <div
            key={index}
            className="overflow-hidden rounded-lg bg-white shadow-lg transition-transform duration-300 hover:-translate-y-1"
          >
            <img
              alt={program.title}
              className="h-64 w-full object-cover"
              src={program.image}
              width={600}
              height={400}
            />
            <div className="p-6">
              <h3 className="mb-4 text-2xl font-bold">{program.title}</h3>
              <p className="mb-6 text-gray-600">{program.description}</p>
              <button className="rounded-full bg-gradient-to-r from-[#FFC107] to-[#FFD54F] px-4 py-1.5 font-semibold text-xs text-gray-900 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 transform -rotate-1">
                {t('ourPrograms.readMore')}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Contact Form Section */}
      <div className="bg-[#f8f9fa] rounded-2xl overflow-hidden shadow-xl my-16">
        <div className="grid md:grid-cols-2">
          {/* Left side - Form */}
          <div className="p-8 md:p-12">
            <h2 className="text-3xl font-bold mb-2">{t('ourPrograms.contactForm.title')}</h2>
            <p className="text-gray-600 mb-8">
              {t('ourPrograms.contactForm.subtitle')}
            </p>

            {isSubmitted ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <Check className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-green-800 mb-2">{t('ourPrograms.contactForm.success.title')}</h3>
                <p className="text-green-700">
                  {t('ourPrograms.contactForm.success.message')}
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="schoolName" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('ourPrograms.contactForm.fields.schoolName.label')}
                  </label>
                  <input
                    type="text"
                    id="schoolName"
                    name="schoolName"
                    value={formData.schoolName}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('ourPrograms.contactForm.fields.firstName.label')}
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('ourPrograms.contactForm.fields.lastName.label')}
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('ourPrograms.contactForm.fields.email.label')}
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('ourPrograms.contactForm.fields.phone.label')}
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                  />
                </div>

                <div>
                  <label htmlFor="enrollmentCount" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('ourPrograms.contactForm.fields.enrollmentCount.label')}
                  </label>
                  <select
                    id="enrollmentCount"
                    name="enrollmentCount"
                    value={formData.enrollmentCount}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                    required
                  >
                    <option value="">{t('ourPrograms.contactForm.fields.enrollmentCount.placeholder')}</option>
                    {enrollmentOptions.map((option, index) => (
                      <option key={index} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('ourPrograms.contactForm.fields.message.label')}
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                  ></textarea>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full bg-[#525FE1] hover:bg-[#4549c0] text-white px-6 py-3 rounded-md font-medium transition-colors flex items-center justify-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('ourPrograms.contactForm.submitButton.processing')}
                      </>
                    ) : (
                      <>
                        {t('ourPrograms.contactForm.submitButton.text')} <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </button>
                </div>

                <p className="text-xs text-gray-500 mt-4">
                  {t('ourPrograms.contactForm.termsText')} <a href="#" className="text-[#525FE1] hover:underline">{t('ourPrograms.contactForm.termsLink')}</a> {t('and')} <a href="#" className="text-[#525FE1] hover:underline">{t('ourPrograms.contactForm.privacyLink')}</a>.
                </p>
              </form>
            )}
          </div>

          {/* Right side - Image and benefits */}
          <div className="bg-[#525FE1] text-white p-8 md:p-12 flex flex-col justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-6">{t('ourPrograms.benefits.title')}</h3>

              <ul className="space-y-4">
                {benefitItems.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <div className="flex-shrink-0 h-6 w-6 rounded-full bg-white/20 flex items-center justify-center mr-3 mt-0.5">
                      <Check className="h-3.5 w-3.5 text-white" />
                    </div>
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-8">
              <a href="/demo" className="inline-block bg-white text-[#525FE1] px-6 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">
                {t('ourPrograms.benefits.demoButton')}
              </a>
              <p className="mt-4 text-sm text-white/80">
                {t('ourPrograms.benefits.demoText')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
export default OurPrograms
