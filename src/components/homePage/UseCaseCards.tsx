// React is used implicitly for JSX
// import React from 'react'
import { useTranslation } from 'react-i18next';

/**
 * UseCaseCards - visually matches the provided screenshot.
 * - "Manage School/Univ" (SaaS)
 * - "Study Helper" (Nonprofit, for students: quizzes, flashcards, mindmaps, etc.)
 * - "Custom Solution" (News/Media)
 * @returns {JSX.Element}
 */
export function UseCaseCards() {
  const { t } = useTranslation('home');
  
  return (
    <section id="use-cases" className="flex flex-col items-center py-16">
      <h2 className="text-2xl font-bold mb-8">
        <span className="bg-purple-400 text-white px-4 py-1 rounded">{t('useCaseCards.title')}</span>
      </h2>
      <div className="flex flex-col md:flex-row gap-8">
        <UseCaseCard
          title={t('useCaseCards.cards.0.title')}
          bgColor="bg-[#b983f7]"
          barColor="#6d28d9"
          patternColor="#a78bfa"
          iconColor="#a21caf"
        />
        <UseCaseCard
          title={t('useCaseCards.cards.1.title')}
          bgColor="bg-[#3b82f6]"
          barColor="#2563eb"
          patternColor="#60a5fa"
          iconColor="#1e40af"
        />
        <UseCaseCard
          title={t('useCaseCards.cards.2.title')}
          bgColor="bg-[#fbbf24]"
          barColor="#78350f"
          patternColor="#fde68a"
          iconColor="#b45309"
        />
      </div>
    </section>
  )
}

/**
 * Card for a single use case, visually styled to match the screenshot.
 * @param {object} props
 * @param {string} props.title - Card title
 * @param {string} props.bgColor - Tailwind background color or hex
 * @param {string} props.barColor - Bar chart color
 * @param {string} props.patternColor - SVG pattern color
 * @param {string} props.iconColor - Overlay icon color
 * @returns {JSX.Element}
 */
function UseCaseCard({
  title,
  bgColor,
  barColor,
  patternColor,
  iconColor
}: {
  title: string
  bgColor: string
  barColor: string
  patternColor: string
  iconColor: string
}) {
  return (
    <div
      className={`relative rounded-xl shadow-lg w-[240px] h-[240px] flex items-end overflow-hidden ${bgColor}`}
      style={{ minWidth: 240, minHeight: 240 }}
    >
      {/* Background pattern */}
      <svg
        className="absolute inset-0 w-full h-full opacity-30"
        viewBox="0 0 240 240"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <pattern id="pattern-bg" patternUnits="userSpaceOnUse" width="40" height="40">
          <text x="0" y="32" fontSize="32" fill={patternColor} opacity="0.2">✎</text>
        </pattern>
        <rect width="240" height="240" fill="url(#pattern-bg)" />
      </svg>
      {/* Bar chart */}
      <div className="absolute left-6 bottom-16">
        <BarChartSVG barColor={barColor} />
        {/* Overlay icon */}
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <CircleLogo color={iconColor} />
        </div>
      </div>
      {/* Title */}
      <div className="absolute left-6 bottom-6">
        <h3 className="text-white text-lg font-bold drop-shadow">{title}</h3>
      </div>
    </div>
  )
}

/**
 * Multi-bar chart SVG, visually similar to the screenshot.
 * @param {object} props
 * @param {string} props.barColor
 * @returns {JSX.Element}
 */
function BarChartSVG({ barColor }: { barColor: string }) {
  // 12 bars, increasing height
  const bars = Array.from({ length: 12 }, (_, i) => ({
    x: i * 12,
    y: 48 - (i + 2) * 3,
    height: (i + 2) * 3
  }))
  return (
    <svg width="144" height="48" viewBox="0 0 144 48" fill="none">
      {bars.map((bar, i) => (
        <rect
          key={i}
          x={bar.x}
          y={bar.y}
          width="8"
          height={bar.height}
          rx="2"
          fill={barColor}
        />
      ))}
    </svg>
  )
}

/**
 * Small circular logo/icon overlay for the bar chart.
 * @param {object} props
 * @param {string} props.color
 * @returns {JSX.Element}
 */
function CircleLogo({ color }: { color: string }) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="10" fill={color} />
      <text x="12" y="17" textAnchor="middle" fontSize="14" fill="#fff" fontWeight="bold">✪</text>
    </svg>
  )
}

export default UseCaseCards

/**
 * Static content and interfaces at file end.
 */
