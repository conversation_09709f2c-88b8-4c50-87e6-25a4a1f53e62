import { useTranslation } from 'react-i18next'

// Define the type for table rows
interface TableRow {
  feature: string;
  jeridSchool: string;
  timetableMaster: string;
  primeTimetable: string;
}

const FeatureComparison = () => {
  const { t } = useTranslation('home')

  // Cast the translation result to the correct type
  const tableRows = t('featureComparison.table.rows', { returnObjects: true }) as TableRow[];

  return (
    <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center text-white mb-12">
          {t('featureComparison.title')}
        </h2>

        <div className="bg-white rounded-lg shadow-xl p-8 max-w-5xl mx-auto">
          <div className="mb-10">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              {t('featureComparison.headline').includes('education team') ? (
                <>
                  {t('featureComparison.headline').split('education team')[0]}
                  <span className="text-green-500">education team</span>
                  {t('featureComparison.headline').split('education team')[1]}
                </>
              ) : (
                t('featureComparison.headline')
              )}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('featureComparison.description1').includes('no clear owner') ? (
                <>
                  {t('featureComparison.description1').split('no clear owner')[0]}
                  <span className="font-semibold">no clear owner</span>
                  {t('featureComparison.description1').split('no clear owner')[1]}
                </>
              ) : (
                t('featureComparison.description1')
              )}
            </p>

            {/* Chart showing performance comparison */}
            <div className="mt-8 mb-6">
              <h4 className="text-lg font-semibold mb-2">{t('featureComparison.chartTitle')}</h4>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="h-48 flex items-end justify-between space-x-1">
                  {/* Generate bars with increasing height */}
                  {[35, 38, 42, 45, 48, 52, 55, 60, 68, 75, 85, 92].map((height, index) => (
                    <div key={index} className="w-full">
                      <div
                        className={`${index >= 8 ? 'bg-blue-500' : 'bg-gray-300'} rounded-t`}
                        style={{ height: `${height}%` }}
                      ></div>
                    </div>
                  ))}
                </div>
                <div className="flex justify-between mt-2">
                  <div className="text-xs text-gray-500">{t('featureComparison.chartLabels.competitors')}</div>
                  <div className="text-xs font-semibold text-blue-600">{t('featureComparison.chartLabels.jeridSchool')}</div>
                </div>

                {/* Add JeridSchool indicator */}
                <div className="relative mt-4">
                  <div className="flex items-center">
                    <div className="w-24 h-0.5 bg-blue-500"></div>
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                    </div>
                    <div className="ml-2 text-sm font-medium">{t('featureComparison.chartLabels.performance')}</div>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-gray-700 mt-6">
              {t('featureComparison.description2').includes('We\'re already helping') ? (
                <>
                  <span className="font-bold">{t('featureComparison.description2').split('We\'re already helping')[0]}</span>
                  <span>{t('featureComparison.description2').split('We\'re already helping')[0] ? '' : t('featureComparison.description2').split('500 schools worldwide')[0]}</span>
                  <span className="font-bold">We're already helping over 500 schools worldwide.</span>
                </>
              ) : (
                <>
                  <span className="font-bold">What if improving education was handled for you?</span> {t('featureComparison.description2')} <span className="font-bold">We're already helping over 500 schools worldwide.</span>
                </>
              )}
            </p>
          </div>

          {/* Comparison Table */}
          <div className="overflow-x-auto mb-12">
            <table className="min-w-full bg-white">
              <thead>
                <tr>
                  <th className="py-3 px-4 bg-indigo-600 text-white text-left rounded-tl-lg">{t('featureComparison.table.headers.feature')}</th>
                  <th className="py-3 px-4 bg-indigo-600 text-white text-center">{t('featureComparison.table.headers.jeridSchool')}</th>
                  <th className="py-3 px-4 bg-indigo-600 text-white text-center">{t('featureComparison.table.headers.timetableMaster')}</th>
                  <th className="py-3 px-4 bg-indigo-600 text-white text-center rounded-tr-lg">{t('featureComparison.table.headers.primeTimetable')}</th>
                </tr>
              </thead>
              <tbody className="text-gray-700">
                {tableRows.map((row, index) => (
                  <tr key={index} className={index < 4 ? "border-b" : ""}>
                    <td className="py-3 px-4 font-medium flex items-center">
                      <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        {index === 0 && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>}
                        {index === 1 && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>}
                        {index === 2 && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path>}
                        {index === 3 && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>}
                        {index === 4 && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>}
                      </svg>
                      {row.feature}
                    </td>
                    <td className="py-3 px-4 text-center font-medium text-indigo-600">{row.jeridSchool}</td>
                    <td className="py-3 px-4 text-center">{row.timetableMaster}</td>
                    <td className="py-3 px-4 text-center">{row.primeTimetable}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-10">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">{t('featureComparison.whyChoose.title')}</h3>
            <p className="text-gray-700 mb-6 text-lg">
              <span className="font-bold text-blue-600">{t('featureComparison.whyChoose.subtitle')}</span> {t('featureComparison.whyChoose.description')}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                <h4 className="font-bold text-lg text-blue-700 mb-2">{t('featureComparison.whyChoose.cards.turboLearn.title')}</h4>
                <p className="text-gray-700">{t('featureComparison.whyChoose.cards.turboLearn.description')}</p>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg border border-purple-100">
                <h4 className="font-bold text-lg text-purple-700 mb-2">{t('featureComparison.whyChoose.cards.timeMaster.title')}</h4>
                <p className="text-gray-700">{t('featureComparison.whyChoose.cards.timeMaster.description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeatureComparison
