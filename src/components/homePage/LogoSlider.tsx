import { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { useTranslation } from 'react-i18next'

function LogoSlider() {
  const { t } = useTranslation('home')
  const sliderRef = useRef<HTMLDivElement>(null)
  const logoContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const slider = sliderRef.current
    const logoContainer = logoContainerRef.current

    if (!slider || !logoContainer) return

    // Clone the logos for infinite scrolling effect
    const logos = Array.from(logoContainer.querySelectorAll('.logo-item'))
    const originalLogoCount = logos.length

    // Ensure enough clones to fill at least 2x the slider width
    let totalWidth = logoContainer.offsetWidth
    while (totalWidth < 2 * slider.offsetWidth) {
      for (let i = 0; i < originalLogoCount; i++) {
        const clone = logos[i].cloneNode(true)
        logoContainer.appendChild(clone)
      }
      totalWidth = logoContainer.offsetWidth
    }

    // Set up the animation
    const tl = gsap.timeline({
      repeat: -1,
      defaults: { ease: 'none' }
    })

    // Calculate the animation distance based on the original logos width
    const singleSetWidth =
      originalLogoCount *
        (logos[0] as HTMLElement).offsetWidth +
      (originalLogoCount - 1) *
        parseInt(window.getComputedStyle(logos[0] as HTMLElement).marginRight)

    tl.to(logoContainer, {
      x: -singleSetWidth,
      duration: 300,
      ease: 'linear',
      repeat: -1
    })

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <section className="py-12 px-8 bg-[#f9f7f2] overflow-hidden border-t border-b border-gray-100 rounded-lg">
      <div className="container mx-auto px-4 mb-6">
        <h3 className="text-center text-xl font-medium text-[#525FE1] mb-8">
          <span className="text-gray-800 mr-2">{t('logoSlider.title.prefix')}</span>
          {t('logoSlider.title.suffix')}
        </h3>
      </div>

      <div ref={sliderRef} className="overflow-hidden px-4">
        <div
          ref={logoContainerRef}
          className="flex items-center space-x-16 py-6"
        >
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/cc.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-12 opacity-80 hover:opacity-100 transition-all duration-300" 
            />
          </div>
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/nvs.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-32 transition-all duration-300" 
            />
          </div>
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/cc.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-12 opacity-80 hover:opacity-100 transition-all duration-300" 
            />
          </div>
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/nvs.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-32 transition-all duration-300" 
            />
          </div>
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/cc.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-12 opacity-80 hover:opacity-100 transition-all duration-300" 
            />
          </div>
          <div className="logo-item flex-shrink-0">
            <img 
              src="/images/landingpage/Company_logos/nvs.svg" 
              alt={t('logoSlider.accessibility.partnerLogo')} 
              className="h-32 transition-all duration-300" 
            />
          </div>
        </div>
      </div>
    </section>
  )
}

export default LogoSlider
