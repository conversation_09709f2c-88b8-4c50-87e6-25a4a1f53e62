import { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'

function Hero() {
  const { t } = useTranslation('home')
  const headingRef = useRef(null)
  const paragraphRef = useRef(null)
  const buttonsRef = useRef(null)
  const featuresRef = useRef(null)

  useEffect(() => {
    // Create a timeline for smooth sequenced animations
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } })

    // Initial state - set elements to be invisible
    gsap.set([headingRef.current, paragraphRef.current, buttonsRef.current, featuresRef.current], {
      opacity: 0,
      y: 50,
    })

    // Animate elements in sequence
    tl.to(headingRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    })
      .to(
        paragraphRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
        },
        '-=0.4'
      )
      .to(
        buttonsRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
        },
        '-=0.4'
      )
      .to(
        featuresRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
        },
        '-=0.2'
      )
  }, [])

  return (
    <section className="m-2 w-full bg-[#f9f7f2] min-h-[110vh] py-20 px-8 relative overflow-hidden rounded-lg">
      {/* Background pattern - subtle grid */}
      <div className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23000000\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'1\'/%3E%3C/g%3E%3C/svg%3E")',
          backgroundSize: '20px 20px'
        }}>
      </div>

      <div className="container mx-auto px-4 md:px-6 pt-16 md:pt-24">
        <div className="flex flex-col items-center justify-center text-center max-w-4xl mx-auto">
          {/* Main heading */}
          <h1
            ref={headingRef}
            className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6"
          >
            {t('hero.title.prefix')}
            <br />
            <span className="text-[#6366F1]">{t('hero.title.highlight')}</span>
          </h1>

          {/* Subtitle */}
          <p
            ref={paragraphRef}
            className="text-gray-700 text-lg md:text-xl max-w-2xl mb-10"
          >
            {t('hero.subtitle')}
          </p>

          {/* CTA Buttons */}
          <div
            ref={buttonsRef}
            className="flex flex-col md:flex-row items-center justify-center gap-4 mb-16"
          >
            <Link to="/join">
              <button className="bg-[#6366F1] hover:bg-[#4F46E5] text-white px-8 py-3 rounded-md font-semibold transition-colors flex items-center">
                {t('hero.buttons.primary')} <span className="ml-2">→</span>
              </button>
            </Link>
            <a
              href="#how-it-works"
              className="bg-white hover:bg-gray-100 text-gray-800 border border-gray-300 px-8 py-3 rounded-md font-semibold transition-colors"
            >
              {t('hero.buttons.secondary')}
            </a>
          </div>

          {/* Experience highlights */}
          <div className="text-sm text-gray-600 mb-12">
            <p className="mb-3">{t('hero.experience.title')}</p>
            <div className="flex flex-wrap justify-center gap-6 text-xs">
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>{t('hero.experience.stats.0')}</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>{t('hero.experience.stats.1')}</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>{t('hero.experience.stats.2')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Feature buttons in Churnkey style - simplified version */}
        <div
          ref={featuresRef}
          className="relative max-w-5xl mx-auto mt-16 mb-8 py-24 px-20"
          style={{ height: '300px' }}
        >
          {/* Background dotted paths */}
          <div className="absolute inset-0 z-0">
            <svg className="w-full h-full" viewBox="0 0 800 300" fill="none" xmlns="http://www.w3.org/2000/svg">
              {/* Left curved path */}
              <path d="M200,150 C200,80 300,50 400,150" stroke="#E5E7EB" strokeWidth="2" strokeDasharray="5,5" />
              {/* Right curved path */}
              <path d="M600,150 C600,80 500,50 400,150" stroke="#E5E7EB" strokeWidth="2" strokeDasharray="5,5" />
              {/* Bottom curved path */}
              <path d="M400,150 C500,250 300,250 400,150" stroke="#E5E7EB" strokeWidth="2" strokeDasharray="5,5" />
            </svg>
          </div>

          {/* Top feature - AI Flashcards */}
          <div className="absolute left-1/2 top-0 transform -translate-x-1/2 z-20">
            <div className="bg-white rounded-lg p-2.5 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer w-48">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                  <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                  </svg>
                </div>
                <span className="text-xs font-medium">{t('hero.features.flashcards.title')}</span>
              </div>
              <p className="text-xs text-gray-500 ml-8">{t('hero.features.flashcards.description')}</p>
            </div>
          </div>

          {/* Center feature - Quiz Generator */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
            <div className="bg-white rounded-lg p-2.5 shadow-md border border-gray-100 w-48 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer">
              <div className="flex items-center mb-1">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                  <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                  </svg>
                </div>
                <span className="text-xs font-medium">{t('hero.features.quizGenerator.title')}</span>
                <span className="text-xs text-gray-500 ml-auto">{t('hero.features.quizGenerator.badge')}</span>
              </div>
              <p className="text-xs text-gray-600 ml-8">{t('hero.features.quizGenerator.description')}</p>
            </div>
          </div>

          {/* Bottom feature - AI Chat Assistant */}
          <div className="absolute left-1/2 bottom-0 transform -translate-x-1/2 z-20">
            <div className="bg-white rounded-lg p-2.5 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:translate-y-1 cursor-pointer w-48">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                  <svg className="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                    <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                  </svg>
                </div>
                <span className="text-xs font-medium">{t('hero.features.chatAssistant.title')}</span>
              </div>
              <p className="text-xs text-gray-500 ml-8">{t('hero.features.chatAssistant.description')}</p>
            </div>
          </div>

          {/* Left feature - Smart Notes */}
          <div className="absolute left-[20%] top-1/2 transform -translate-y-1/2 z-20">
            <div className="bg-white rounded-lg p-2.5 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-x-1 cursor-pointer w-48">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center mr-2">
                  <svg className="w-3 h-3 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                </div>
                <span className="text-xs font-medium">{t('hero.features.smartNotes.title')}</span>
              </div>
              <p className="text-xs text-gray-500 ml-8">{t('hero.features.smartNotes.description')}</p>
            </div>
          </div>

          {/* Right feature - Study Planner */}
          <div className="absolute right-[20%] top-1/2 transform -translate-y-1/2 z-20">
            <div className="bg-white rounded-lg p-2.5 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:translate-x-1 cursor-pointer w-48">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center mr-2">
                  <svg className="w-3 h-3 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                  </svg>
                </div>
                <span className="text-xs font-medium">{t('hero.features.studyPlanner.title')}</span>
              </div>
              <p className="text-xs text-gray-500 ml-8">{t('hero.features.studyPlanner.description')}</p>
            </div>
          </div>

          {/* Left yellow button - Generate Automatic Timetable */}
          <div className="absolute -left-10 top-1/2 transform -translate-y-1/2 z-30">
            <div className="relative group">
              {/* Yellow pill button with glow effect - angled appearance */}
              <div className="bg-gradient-to-r from-[#FFC107] to-[#FFD54F] text-black px-4 py-1.5 rounded-full font-bold text-xs shadow-lg cursor-pointer relative overflow-hidden group-hover:shadow-xl group-hover:scale-105 transition-all duration-300 transform -rotate-1">
                <span className="relative z-10">Generate Automatic Timetable</span>
                {/* Subtle radial glow */}
                <div className="absolute inset-0 bg-gradient-radial from-yellow-300 to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>

              {/* Hand cursor icon */}


              {/* Decorative sun-like rays */}
              <div className="absolute -left-6 -top-6 w-16 h-16 opacity-70">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                  <g fill="#FFC107">
                    <path d="M50,0 L55,20 L45,20 Z" />
                    <path d="M50,100 L55,80 L45,80 Z" />
                    <path d="M0,50 L20,55 L20,45 Z" />
                    <path d="M100,50 L80,55 L80,45 Z" />
                    <path d="M14.6,14.6 L29.3,29.3 L22.1,29.3 L22.1,22.1 Z" />
                    <path d="M85.4,85.4 L70.7,70.7 L77.9,70.7 L77.9,77.9 Z" />
                    <path d="M14.6,85.4 L29.3,70.7 L22.1,70.7 L22.1,77.9 Z" />
                    <path d="M85.4,14.6 L70.7,29.3 L77.9,29.3 L77.9,22.1 Z" />
                  </g>
                </svg>
              </div>
            </div>
          </div>

          {/* Right yellow button - AI Agent */}
          <div className="absolute -right-10 top-1/2 transform -translate-y-1/2 z-30">
            <div className="relative group">
              {/* Yellow pill button with glow effect - angled appearance */}
              <div className="bg-gradient-to-r from-[#FFC107] to-[#FFD54F] text-black px-4 py-1.5 rounded-full font-bold text-xs shadow-lg cursor-pointer relative overflow-hidden group-hover:shadow-xl group-hover:scale-105 transition-all duration-300 transform rotate-1">
                <span className="relative z-10">AI Agent</span>
                {/* Subtle radial glow */}
                <div className="absolute inset-0 bg-gradient-radial from-yellow-300 to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
              </div>

              {/* Warning icon */}
              <div className="absolute -left-2 -top-2 bg-[#FFC107] text-black rounded-full p-1 shadow-md">
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 9v3m0 3h.01M12 21a9 9 0 110-18 9 9 0 010 18z" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </div>

              {/* Decorative sun-like rays */}
              <div className="absolute -right-6 -top-6 w-16 h-16 opacity-70">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                  <g fill="#FFC107">
                    <path d="M50,0 L55,20 L45,20 Z" />
                    <path d="M50,100 L55,80 L45,80 Z" />
                    <path d="M0,50 L20,55 L20,45 Z" />
                    <path d="M100,50 L80,55 L80,45 Z" />
                    <path d="M14.6,14.6 L29.3,29.3 L22.1,29.3 L22.1,22.1 Z" />
                    <path d="M85.4,85.4 L70.7,70.7 L77.9,70.7 L77.9,77.9 Z" />
                    <path d="M14.6,85.4 L29.3,70.7 L22.1,70.7 L22.1,77.9 Z" />
                    <path d="M85.4,14.6 L70.7,29.3 L77.9,29.3 L77.9,22.1 Z" />
                  </g>
                </svg>
              </div>
            </div>
          </div>

          {/* Top stats */}
          <div className="absolute left-0 right-0 -top-6 flex justify-center gap-6 text-xs">
            <div className="flex items-center">
              <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>100+ AI-Generated Quizzes</span>
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>500+ Flashcards Created Daily</span>
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>24/7 AI Homework Support</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
