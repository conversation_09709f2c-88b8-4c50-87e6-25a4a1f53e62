import FeaturesSlider from './FeaturesSlider'
import { BookOpen, Users, Calendar, MessageSquare, Settings, BarChart3 } from 'lucide-react'
import { useTranslation } from 'react-i18next'

/**
 * Example implementation of the FeaturesSlider component
 * to showcase school management system features
 */
function FeaturesExample() {
  const { t } = useTranslation('home')
  
  // Get features from translations
  const translatedFeatures = t('featuresExample.features', { returnObjects: true }) as Array<{
    title: string;
    description: string;
    buttonText: string;
  }>
  
  // Map icons to features
  const icons = [
    <BookOpen className="w-8 h-8" />,
    <MessageSquare className="w-8 h-8" />,
    <Users className="w-8 h-8" />,
    <Calendar className="w-8 h-8" />,
    <BarChart3 className="w-8 h-8" />,
    <Settings className="w-8 h-8" />
  ]
  
  // URLs for buttons
  const urls = ['#lms', '#communication', '#management', '#schedule', '#admin', '#customize']
  
  // Combine translations with icons and URLs
  const features = translatedFeatures.map((feature, index) => ({
    ...feature,
    icon: icons[index] || <Settings className="w-8 h-8" />,
    buttonUrl: urls[index] || '#'
  }))

  return (
    <section className="bg-slate-50 overflow-hidden py-0">
      <div className="text-center mb-12 pt-16 px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
          {t('featuresExample.title')}
        </h2>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          {t('featuresExample.subtitle')}
        </p>
      </div>
      
      <FeaturesSlider features={features} />
    </section>
  )
}

export default FeaturesExample 
