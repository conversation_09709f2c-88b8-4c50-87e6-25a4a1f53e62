.testimonial-text {
  transition: opacity 0.3s ease;
  will-change: opacity;
}

.testimonial-highlight {
  transition: opacity 0.3s ease, color 0.3s ease;
  will-change: opacity, color;
}

/* Initial state - hidden */
.testimonial-text.opacity-0,
.testimonial-highlight.opacity-0 {
  opacity: 0;
}

/* Add a subtle glow effect to the highlighted text */
.testimonial-highlight {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
}

/* Styling for the testimonial section */
.testimonial-text, .testimonial-highlight {
  display: inline;
}

/* Ensure smooth animation */
.testimonial-text, .testimonial-highlight {
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}
