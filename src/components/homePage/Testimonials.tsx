import { useTranslation } from 'react-i18next'

function Testimonials() {
  const { t } = useTranslation('home')

  return (
    <div className="max-w-4xl w-full mx-auto p-4">
      <div className="relative bg-[#4263EB] rounded-2xl p-8 text-white">
        {/* Yellow circle in top right */}
        <div className="absolute -top-2 -right-2">
          <svg
            width="74"
            height="75"
            viewBox="0 0 74 75"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="37" cy="37.6958" r="37" fill="#FFCF59" />
          </svg>
        </div>

        {/* Quote marks (SVG half in and half out) */}
        <div className="mb-4 relative">
          <svg
            className="absolute top-[-100px] left-0"
            width="100"
            height="100"
            viewBox="0 0 66 54"
            xmlns="http://www.w3.org/2000/svg"
            aria-label={t('testimonials.accessibility.quoteMarks')}
          >
            <path
              d="M20.5298 0H27.7298V13.2H20.9298C17.5298 13.2 15.9298 14 15.9298 18.2V22.4H27.9298V53.4H0.729797V18.4C0.729797 6.00001 8.1298 0 20.5298 0ZM57.7298 0H64.9298V13.2H58.3298C54.9298 13.2 53.3298 14 53.3298 18.2V22.4H65.3298V53.4H37.9298V18.4C37.9298 6.00001 45.3298 0 57.7298 0Z"
              fill="black"
            />
          </svg>
        </div>

        {/* Quote text */}
        <p className="text-lg mb-8">
          "{t('testimonials.quote')}"
        </p>
      </div>
      {/* Profile section */}
      <div className="relative bottom-10">
        {/* Profile image (Square) */}
        <div className="absolute rounded-lg left-1/2 transform -translate-x-1/2 w-24 h-24 bg-gray-300 border-orange-50 border-4 overflow-hidden mb-5">
          <img
            src="/images/profile.jpg"
            alt={t('testimonials.accessibility.profileImage')}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="p-12"></div>

        {/* Profile text below the image */}
        <div className="absolute left-1/2 transform -translate-x-1/2 bottom-[-40px] text-center ">
          <h3 className="font-semibold text-black">
            {t('testimonials.name')} <span className="text-primary text-pretty">/</span> {t('testimonials.title')}
          </h3>
          <p className="text-sm text-black/80">{t('testimonials.location')}</p>
        </div>
      </div>

      {/* Adding 20px margin to the bottom of the entire section */}
      <div className="mb-20"></div>
    </div>
  )
}

export default Testimonials
