import { useEffect, useRef } from 'react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { ArrowRight } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import './testimonial.css'

gsap.registerPlugin(ScrollTrigger)

const EffortlessManaging = () => {
  const { t } = useTranslation('home')
  
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const headingRef = useRef<HTMLHeadingElement>(null)
  const paragraphRef = useRef<HTMLParagraphElement>(null)
  const ratingRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const pricingSectionRef = useRef<HTMLDivElement>(null)
  const testimonialRef = useRef<HTMLDivElement>(null)
  const testimonialTextRef = useRef<HTMLSpanElement[]>([])
  const testimonialHighlightRef = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    const container = containerRef.current

    // Set initial states
    gsap.set([headingRef.current, paragraphRef.current, ratingRef.current, buttonRef.current], {
      opacity: 0,
      y: 30,
    })

    gsap.set(imageRef.current, {
      opacity: 0,
      scale: 0.95,
    })

    // Set initial states for pricing and testimonial sections
    gsap.set([pricingSectionRef.current, testimonialRef.current], {
      opacity: 0,
      y: 50,
    })

    // Create main timeline for the first section
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: 'top center',
        end: 'center center',
        toggleActions: 'play none none reverse',
      },
    })

    // Animation sequence
    tl.to(imageRef.current, {
      opacity: 1,
      scale: 1,
      duration: 1,
      ease: 'power2.out',
    })
      .to(
        headingRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
        },
        '-=0.5'
      )
      .to(
        paragraphRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
        },
        '-=0.6'
      )
      .to(
        ratingRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
        },
        '-=0.6'
      )
      .to(
        buttonRef.current,
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: 'power2.out',
        },
        '-=0.7'
      )

    // Create separate scroll triggers for pricing and testimonial sections
    gsap.to(pricingSectionRef.current, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: pricingSectionRef.current,
        start: 'top bottom-=100',
        end: 'top center',
        toggleActions: 'play none none reverse',
        scrub: 0.5
      }
    })

    // Create a timeline for the testimonial text animation
    const testimonialTl = gsap.timeline({
      scrollTrigger: {
        trigger: testimonialRef.current,
        start: 'top bottom-=100',
        end: 'bottom center',
        toggleActions: 'play none none reverse',
        scrub: 1
      }
    })

    // Set initial state for the testimonial container
    gsap.set(testimonialRef.current, {
      opacity: 1,
      y: 0,
    })

    // Animate the first part of the text
    testimonialTl.to(testimonialTextRef.current[0], {
      opacity: 1,
      duration: 0.3,
      ease: 'power1.inOut',
    }, 0)

    // Animate the highlighted part with a different effect
    .to(testimonialHighlightRef.current, {
      opacity: 1,
      color: 'rgba(255, 255, 255, 0.6)',
      duration: 0.3,
      ease: 'power1.inOut',
      delay: 0.1
    }, 0.3)

    // Animate the second part of the text
    .to(testimonialTextRef.current[1], {
      opacity: 1,
      duration: 0.3,
      ease: 'power1.inOut',
      delay: 0.1
    }, 0.6)

    return () => {
      // Clean up ScrollTrigger instances
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  // Get features from translations
  const pricingFeatures = t('effortlessManaging.pricing.features', { returnObjects: true }) as string[];

  return (
    <div
      ref={containerRef}
      className="relative min-h-[600px] bg-[#FFD54F] overflow-hidden"
    >
      {/* Content Container */}
      <div className="relative mx-auto max-w-6xl px-4 py-16">
        <div className="grid gap-8 md:grid-cols-2 items-center">
          {/* Left Column - Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1
                ref={headingRef}
                className="text-4xl font-bold tracking-tight text-gray-800 sm:text-5xl"
              >
                {t('effortlessManaging.heading')}
              </h1>
              <p ref={paragraphRef} className="text-lg text-gray-700">
                {t('effortlessManaging.paragraph')}
              </p>
            </div>

            {/* Talk to Us Button */}
            <button
              ref={buttonRef}
              className="flex items-center space-x-2 bg-white text-gray-800 px-6 py-3 rounded-full font-medium hover:shadow-md transition-shadow duration-300"
            >
              <span>{t('effortlessManaging.button')}</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>

          {/* Right Column - Image */}
          <div className="relative pr-10 pb-10">
            <img
              ref={imageRef}
              src="/images/landingpage/student1.jpg"
              alt="Student using tablet"
              className="rounded-lg shadow-xl max-h-[500px] w-full object-cover"
            />

            {/* Rating Box - Positioned at bottom right of image */}
            <div
              ref={ratingRef}
              className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4 bg-[#3949AB]/90 text-white p-6 rounded-lg shadow-lg z-10"
            >
              <div className="flex items-end gap-2">
                <span className="text-5xl font-bold">{t('effortlessManaging.rating.percentage')}</span>
                <div className="text-sm leading-tight mb-1">
                  <div>{t('effortlessManaging.rating.label')}</div>
                </div>
              </div>
              <p className="text-sm mt-2">
                {t('effortlessManaging.rating.description')}
              </p>
            </div>
          </div>
        </div>

        {/* Pricing Section */}
        <div ref={pricingSectionRef} className="mt-24 bg-[#3949AB] rounded-lg overflow-hidden shadow-xl">
          <div className="p-8 md:p-12">
            <div className="grid gap-8 md:grid-cols-2 items-center">
              {/* Left Column - Pricing Content */}
              <div className="space-y-6">
                <div className="mb-2 text-sm text-white">{t('effortlessManaging.pricing.label')}</div>
                <h2 className="text-3xl md:text-4xl font-bold text-white">
                  {t('effortlessManaging.pricing.title')}
                </h2>
                <p className="text-white">
                  {t('effortlessManaging.pricing.description')}
                </p>

                <ul className="space-y-3 mt-6 text-white">
                  {pricingFeatures.map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="flex-shrink-0 h-5 w-5 rounded-full bg-white flex items-center justify-center">
                        <span className="h-2 w-2 rounded-full bg-green-400"></span>
                      </span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Right Column - School Image */}
              <div className="relative flex justify-center">
                <img
                  src="/images/landingpage/bot.png"
                  alt="School building"
                  className="rounded-lg shadow-xl max-w-[400px] object-cover"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial Section */}
        <div ref={testimonialRef} className="mt-24 bg-[#3949AB] rounded-lg overflow-hidden shadow-xl relative">
          <div className="absolute top-0 right-0 w-24 h-24">
            <div className="absolute top-6 right-6">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 0L23.5 16.5L40 20L23.5 23.5L20 40L16.5 23.5L0 20L16.5 16.5L20 0Z" fill="#ffffff" fillOpacity="0.5" />
              </svg>
            </div>
          </div>
          <div className="p-8 md:p-12">
            <div className="max-w-4xl mx-auto">
              <blockquote>
                <p className="text-2xl md:text-3xl font-light leading-relaxed text-white">
                  <span ref={(el) => { if (el) testimonialTextRef.current[0] = el; }} className="testimonial-text opacity-0">
                    {t('effortlessManaging.testimonial.quote.part1')}
                  </span>
                  <span ref={testimonialHighlightRef} className="testimonial-highlight opacity-0">
                    {t('effortlessManaging.testimonial.quote.highlight')}
                  </span>
                  <span ref={(el) => { if (el) testimonialTextRef.current[1] = el; }} className="testimonial-text opacity-0">
                    {t('effortlessManaging.testimonial.quote.part2')}
                  </span>
                </p>
                <footer className="mt-6 text-white">
                  <p>{t('effortlessManaging.testimonial.author')}</p>
                </footer>
              </blockquote>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EffortlessManaging
