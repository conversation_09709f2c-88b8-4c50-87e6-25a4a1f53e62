"use client"

import { useEffect, useRef } from "react"
import gsap from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { useTranslation } from "react-i18next"

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

function CountBar() {
  const { t } = useTranslation("home")

  const statsData = [
    {
      value: 25,
      label: t("countBar.stats.0.label"),
      suffix: "+",
    },
    {
      value: 300,
      label: t("countBar.stats.1.label"),
      suffix: "+",
    },
    {
      value: 200,
      label: t("countBar.stats.2.label"),
      suffix: "+",
    },
  ]

  const sectionRef = useRef<HTMLElement>(null)
  const numberRefs = useRef<(HTMLDivElement | null)[]>([])
  const textRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    // Initial state
    gsap.set(numberRefs.current, { opacity: 0, y: 20 })
    gsap.set(textRefs.current, { opacity: 0, y: 20 })

    // Create animation for each stat
    statsData.forEach((stat, index) => {
      const numberEl = numberRefs.current[index]
      const textEl = textRefs.current[index]

      if (numberEl && textEl) {
        // Create ScrollTrigger animation
        ScrollTrigger.create({
          trigger: sectionRef.current,
          start: "top 80%",
          onEnter: () => {
            // Animate number counting up
            gsap.to(numberEl, {
              opacity: 1,
              y: 0,
              duration: 0.6,
              ease: "power2.out",
            })

            // Animate the actual number
            gsap.to(numberEl, {
              innerText: stat.value,
              duration: 2,
              snap: { innerText: 1 },
              ease: "power2.out",
              onComplete: () => {
                // Add suffix after counting animation
                numberEl.innerText = `${stat.value}${stat.suffix}`
              },
            })

            // Animate text
            gsap.to(textEl, {
              opacity: 1,
              y: 0,
              duration: 0.6,
              delay: 0.2,
              ease: "power2.out",
            })
          },
          once: true, // Animation plays only once
        })
      }
    })

    // Optional: Add a subtle hover effect
    numberRefs.current.forEach((ref) => {
      if (ref) {
        ref.addEventListener("mouseenter", () => {
          gsap.to(ref, {
            scale: 1.1,
            duration: 0.3,
            ease: "power2.out",
          })
        })

        ref.addEventListener("mouseleave", () => {
          gsap.to(ref, {
            scale: 1,
            duration: 0.3,
            ease: "power2.out",
          })
        })
      }
    })

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="relative bg-[#525FE1] py-8 sm:py-10 md:py-12 lg:py-16">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-5 md:gap-6 text-center">
          {statsData.map((stat, index) => (
            <div key={index} className="space-y-1 sm:space-y-2 py-4 sm:py-6 md:py-8">
              {/* Stat Number */}
              <div
                ref={(el) => (numberRefs.current[index] = el)}
                className="text-4xl sm:text-5xl md:text-6xl font-bold text-white transition-all"
              >
                0
              </div>
              {/* Label Text */}
              <div
                ref={(el) => (textRefs.current[index] = el)}
                className="text-base sm:text-lg md:text-xl font-medium text-white/90"
              >
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CountBar
