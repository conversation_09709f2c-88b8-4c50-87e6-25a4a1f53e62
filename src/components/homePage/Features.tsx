import { Calendar, FileText, Shield } from 'lucide-react'
// <PERSON><PERSON><PERSON> is not used
// import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import UserSay from '../UserSay'
import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'

function Features() {
  // Competitors data is defined but not used in this component
  // Keeping it commented for future reference
  /*
  const competitors = [
    {
      name: "TimetableMaster",
      weakness: "Basic scheduling algorithms",
      ourStrength: "Advanced AI optimization"
    },
    {
      name: "ascTimetables",
      weakness: "Desktop only, installation required",
      ourStrength: "Web-based, cross-platform"
    },
    {
      name: "TurboLearn",
      weakness: "Limited learning tools",
      ourStrength: "AI-powered learning suite"
    }
  ]
  */
  const { t } = useTranslation('home')
  return (
    <section className="bg-white">
      <div className="container mx-auto px-4">
        <UserSay
          blueWord={t('features.title.blueWord')}
          title={t('features.title.main')}
          description={t('features.description')}
        />

        {/* Bento Grid for Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 py-12">
          {/* Large feature card - AI Timetable Generation */}
          <Link
            to="/join"
            className="md:col-span-2 md:row-span-2 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group"
          >
            <div className="flex items-center mb-6">
              <div className="bg-white/20 p-4 rounded-lg mr-4 group-hover:bg-white/30 transition-all duration-300">
                <Calendar className="h-10 w-10" />
              </div>
              <h3 className="text-3xl font-bold">{t('features.cards.timetable.title')}</h3>
            </div>
            <p className="text-xl mb-6">{t('features.cards.timetable.description')}</p>
            <img
              src="/images/landingpage/perfect.png"
              alt="Timetable Generation"
              className="w-4/5 mx-auto mt-6 rounded-lg shadow-lg group-hover:shadow-xl transition-all duration-300"
            />
          </Link>

          {/* Medium feature card - Security */}
          <Link
            to="/join"
            className="bg-indigo-600 rounded-xl p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-white/20 p-4 rounded-lg mr-4 group-hover:bg-white/30 transition-all duration-300">
                <Shield className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold">{t('features.cards.security.title')}</h3>
            </div>
            <p className="text-lg">{t('features.cards.security.description')}</p>
          </Link>

          {/* Small feature card - Multiple Learning Tools */}
          <Link
            to="/join"
            className="bg-blue-50 rounded-xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-blue-100 p-3 rounded-lg mr-4 group-hover:bg-blue-200 transition-all duration-300">
                <FileText className="h-7 w-7 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-blue-800">{t('features.cards.learning.title')}</h3>
            </div>
            <img
              src="/images/landingpage/ai.png"
              alt="AI Learning Assistant"
              className="w-full mt-4 rounded shadow-md group-hover:shadow-lg transition-all duration-300"
            />
          </Link>


        </div>


      </div>
    </section>
  )
}

export default Features
