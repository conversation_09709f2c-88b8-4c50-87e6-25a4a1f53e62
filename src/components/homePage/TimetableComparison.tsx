import { useTranslation } from 'react-i18next'

const TimetableComparison = () => {
  const { t } = useTranslation('home')

  return (
    <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center text-white mb-12">
          {t('timetableComparison.title')}
        </h2>

        <div className="bg-white rounded-lg shadow-xl p-8 max-w-4xl mx-auto">
          <div className="mb-10">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              {t('timetableComparison.headline').split('education team').map((part, i) => 
                i === 0 ? 
                  <>{part}<span className="text-green-500">education team</span></> : 
                  part
              )}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('timetableComparison.description1')
                .replace('this economy', '<em>this economy</em>')
                .replace('Juggling product releases and growth is hard enough.', '<strong>Juggling product releases and growth is hard enough.</strong>')
                .replace('no clear owner', '<strong>no clear owner</strong>')
                .split('<em>').map((part, i) => 
                  i % 2 === 0 ? 
                    part.split('<strong>').map((p, j) => 
                      j % 2 === 0 ? p : <span key={`strong-${i}-${j}`} className="font-semibold">{p}</span>
                    ) : 
                    <span key={`em-${i}`} className="italic">{part.split('</em>')[0]}</span>
                )}
            </p>

            {/* Chart showing performance comparison */}
            <div className="mt-8 mb-6">
              <h4 className="text-lg font-semibold mb-2">{t('timetableComparison.chartTitle')}</h4>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="h-48 flex items-end justify-between space-x-1">
                  {/* Generate bars with increasing height */}
                  {[35, 38, 42, 45, 48, 52, 55, 60, 68, 75, 85, 92].map((height, index) => (
                    <div key={index} className="w-full">
                      <div
                        className={`${index >= 8 ? 'bg-blue-500' : 'bg-gray-300'} rounded-t`}
                        style={{ height: `${height}%` }}
                      ></div>
                    </div>
                  ))}
                </div>
                <div className="flex justify-between mt-2">
                  <div className="text-xs text-gray-500">{t('timetableComparison.chartLabels.competitors')}</div>
                  <div className="text-xs font-semibold text-blue-600">{t('timetableComparison.chartLabels.jeridSchool')}</div>
                </div>

                {/* Add JeridSchool indicator */}
                <div className="relative mt-4">
                  <div className="flex items-center">
                    <div className="w-24 h-0.5 bg-blue-500"></div>
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                    </div>
                    <div className="ml-2 text-sm font-medium">{t('timetableComparison.performanceIndicator')}</div>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-gray-700 mt-6">
              {t('timetableComparison.description2')
                .replace('What if improving education was handled for you?', '<strong>What if improving education was handled for you?</strong>')
                .replace('We\'re already helping over 500 schools worldwide.', '<strong>We\'re already helping over 500 schools worldwide.</strong>')
                .split('<strong>').map((part, i) => 
                  i % 2 === 0 ? part : <span key={i} className="font-bold">{part.split('</strong>')[0]}</span>
                )}
            </p>
          </div>


          <div className="mt-10">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">{t('timetableComparison.whyChoose.title')}</h3>
            <p className="text-gray-700 mb-6 text-lg">
              <span className="font-bold text-blue-600">{t('timetableComparison.whyChoose.subtitle')}</span>
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
                <h4 className="font-bold text-lg text-blue-700 mb-2">
                  {t('timetableComparison.whyChoose.cards.turboLearn.title')}
                </h4>
                <p className="text-gray-700">
                  {t('timetableComparison.whyChoose.cards.turboLearn.description')}
                </p>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg border border-purple-100">
                <h4 className="font-bold text-lg text-purple-700 mb-2">
                  {t('timetableComparison.whyChoose.cards.timeMaster.title')}
                </h4>
                <p className="text-gray-700">
                  {t('timetableComparison.whyChoose.cards.timeMaster.description')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TimetableComparison
