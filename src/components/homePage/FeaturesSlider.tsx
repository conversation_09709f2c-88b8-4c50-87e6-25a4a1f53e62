import { useState, useRef, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '../ui/button'

/**
 * Interface for feature items
 */
interface FeatureItem {
  title: string
  description: string
  icon: React.ReactNode
  buttonText: string
  buttonUrl: string
  badge?: string
}

/**
 * Props for the FeaturesSlider component
 */
interface FeaturesSliderProps {
  features: FeatureItem[]
  className?: string
}

/**
 * A feature slider component with a fixed school image on the right
 * and horizontally scrolling content on the left
 */
function FeaturesSlider({ features, className = '' }: FeaturesSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const sliderRef = useRef<HTMLDivElement>(null)
  const sectionRef = useRef<HTMLDivElement>(null)
  
  // Track if we're scrolling up or down
  const [scrollingDown, setScrollingDown] = useState(true)
  
  // Keep track of accumulated scroll for smoother transitions
  const scrollAccumulator = useRef(0)
  const scrollThreshold = 60 // Lower = more sensitive
  
  // Handle navigation buttons
  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const handleNext = () => {
    if (currentIndex < features.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  // Check if we should allow vertical scrolling
  const shouldAllowVerticalScroll = () => {
    // When scrolling down, allow vertical only at the last slide
    if (scrollingDown && currentIndex === features.length - 1) {
      return true
    }
    
    // When scrolling up, allow vertical only at the first slide
    if (!scrollingDown && currentIndex === 0) {
      return true
    }
    
    // Otherwise, keep capturing scroll events for horizontal navigation
    return false
  }

  // Handle wheel events to control scrolling
  useEffect(() => {
    const section = sectionRef.current
    if (!section) return
    
    let lastScrollY = window.scrollY
    
    // Function to track vertical scroll direction
    const trackScrollDirection = () => {
      const currentY = window.scrollY
      setScrollingDown(currentY > lastScrollY)
      lastScrollY = currentY
    }
    
    // Listen for general scroll to detect direction
    window.addEventListener('scroll', trackScrollDirection)
    
    // Handle wheel events
    const handleWheel = (e: WheelEvent) => {
      // Track which direction we're scrolling
      setScrollingDown(e.deltaY > 0)
      
      // Check if we should allow vertical scrolling
      if (shouldAllowVerticalScroll()) {
        return // Allow default scrolling behavior
      }
      
      // Otherwise prevent vertical scrolling
      e.preventDefault()
      
      // Accumulate scroll amount
      scrollAccumulator.current += e.deltaY
      
      // Only move to next slide when threshold is reached
      if (Math.abs(scrollAccumulator.current) >= scrollThreshold) {
        if (scrollAccumulator.current > 0) {
          // Scrolling down, go to next slide if possible
          if (currentIndex < features.length - 1) {
            setCurrentIndex(prevIndex => prevIndex + 1)
          }
        } else {
          // Scrolling up, go to previous slide if possible
          if (currentIndex > 0) {
            setCurrentIndex(prevIndex => prevIndex - 1)
          }
        }
        
        // Reset accumulator after changing slide
        scrollAccumulator.current = 0
      }
    }
    
    // Add the wheel event listener directly to our section
    section.addEventListener('wheel', handleWheel, { passive: false })
    
    return () => {
      window.removeEventListener('scroll', trackScrollDirection)
      section.removeEventListener('wheel', handleWheel)
    }
  }, [currentIndex, features.length])
  
  // Scroll to current slide when index changes
  useEffect(() => {
    if (sliderRef.current) {
      const slideWidth = sliderRef.current.clientWidth
      sliderRef.current.scrollTo({
        left: currentIndex * slideWidth,
        behavior: 'smooth'
      })
    }
  }, [currentIndex])

  return (
    <div
      ref={sectionRef}
      className={`h-screen w-full flex flex-col justify-center ${className}`}
      id="features-slider-section"
    >
      <div
        className="flex flex-col lg:flex-row items-center h-full w-full"
      >
        {/* Left side content with slider */}
        <div className="w-full lg:w-1/2 h-full relative flex flex-col justify-center">
          <div 
            className="overflow-hidden flex-grow"
            style={{ position: 'relative' }}
          >
            <div 
              ref={sliderRef}
              className="flex snap-x snap-mandatory overflow-x-auto scrollbar-hide h-full"
              style={{ 
                scrollSnapType: 'x mandatory',
                scrollbarWidth: 'none',
                msOverflowStyle: 'none'
              }}
            >
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className="w-full flex-shrink-0 p-8 snap-start h-full flex items-center"
                  style={{ scrollSnapAlign: 'start' }}
                >
                  <div className="bg-white rounded-xl shadow-md p-6 md:p-8 max-w-xl mx-auto">
                    <div className="mb-4 text-primary">{feature.icon}</div>
                    <div className="flex items-center mb-3">
                      <h3 className="text-2xl font-bold text-gray-800">{feature.title}</h3>
                      {feature.badge && (
                        <span className="ml-3 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                          {feature.badge}
                        </span>
                      )}
                    </div>
                    <p className="mb-6 text-gray-600">{feature.description}</p>
                    <Button 
                      variant="default" 
                      className="bg-primary hover:bg-primary/90 text-white"
                      asChild
                    >
                      <a href={feature.buttonUrl}>{feature.buttonText}</a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Navigation controls */}
          <div className="flex justify-center items-center py-6 space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePrev}
              disabled={currentIndex === 0}
              className="rounded-full hover:bg-primary/10 transition-colors"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            
            {/* Section indicators */}
            <div className="flex space-x-2">
              {features.map((_, i) => (
                <button
                  key={i}
                  onClick={() => setCurrentIndex(i)}
                  className={`h-2 rounded-full transition-all ${
                    i === currentIndex ? 'bg-primary w-6' : 'bg-gray-300 w-2 hover:bg-gray-400'
                  }`}
                  aria-label={`Go to feature ${i + 1}`}
                />
              ))}
            </div>
            
            <Button
              variant="outline"
              size="icon"
              onClick={handleNext}
              disabled={currentIndex === features.length - 1}
              className="rounded-full hover:bg-primary/10 transition-colors"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
        
        {/* Right side fixed image */}
        <div className="w-full lg:w-1/2 p-8 hidden lg:flex h-full items-center justify-center">
          <div className="relative w-full h-[80%] max-h-[700px]">
            <img
              src="/images/landingpage/school.webp"
              alt="School building"
              className="rounded-xl object-cover shadow-lg w-full h-full"
            />
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FeaturesSlider 
