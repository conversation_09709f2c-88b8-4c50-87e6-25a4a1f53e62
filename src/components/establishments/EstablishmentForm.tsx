import { useForm } from '@tanstack/react-form'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { PlusCircle, X } from 'lucide-react'
import FileUploader from '@/components/shared/FileUploader'
import CMSTemplateSelector from '@/components/schoolTemplateEditor/CMSTemplateSelector'
import GalleryImagesUploader from '@/components/schoolTemplateEditor/GalleryImagesUploader'
import SocialLinksEditor from '@/components/schoolTemplateEditor/SocialLinksEditor'
import StatsEditor from '@/components/schoolTemplateEditor/StatsEditor'
import { EstablishmentFormValues } from '@/hooks/useEstablishments'

interface EstablishmentFormProps {
  initialValues?: any
  onSubmit: (data: EstablishmentFormValues) => void
  onCancel: () => void
  isSubmitting: boolean
  submitLabel: string
}

export function EstablishmentForm({
  initialValues,
  onSubmit,
  onCancel,
  isSubmitting,
  submitLabel,
}: EstablishmentFormProps) {
  const [newService, setNewService] = useState('')

  const form = useForm({
    defaultValues: initialValues || {
      name: '',
      address: '',
      logo: '',
      url: '',
      CMSContent: '',
      description: '',
      heroImage: '',
      galleryImages: [],
      contactEmail: '',
      contactPhone: '',
      socialLinks: {},
      stats: {},
      services: [],
      isActive: true,
    },
    onSubmit: async ({ value }) => {
      onSubmit(value as EstablishmentFormValues)
    },
  })

  const addService = (field: any) => {
    if (!newService.trim()) return
    const currentServices = field.state.value || []
    field.handleChange([...currentServices, newService.trim()])
    setNewService('')
  }

  const removeService = (index: number) => {
    form.getFieldValue('services').then((services: string[]) => {
      if (!services) return
      const newServices = [...services]
      newServices.splice(index, 1)
      form.setFieldValue('services', newServices)
    })
  }
const validateUrlSlug = (value: string) => {
     console.log('validateUrlSlug called with:', value); // Debug log
    if (!value) return 'URL slug is required'
    
    const reservedNames = ['cdn', 'timetable', 'production', 'prod', 'staging', 'stag', 'http', 'https', 'api', 'rag']
    if (reservedNames.includes(value.toLowerCase())) {
      return 'This name is reserved and cannot be used'
    }
    
    if (/[.\/,$*]/.test(value)) {
      return 'URL cannot contain symbols like . , / $ *'
    }
    
    if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
      return 'URL can only contain letters, numbers, hyphens (-) and underscores (_)'
    }
    
    return undefined
  }
  return (
    <Card>
      <CardHeader>
        <CardTitle>{submitLabel}</CardTitle>
        <CardDescription>
          Enter the details for the establishment
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            void form.handleSubmit()
          }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name Field */}
            <form.Field
              name="name"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return 'Name is required'
                  return undefined
                },
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter establishment name"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Address Field */}
            <form.Field
              name="address"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return 'Address is required'
                  return undefined
                },
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter establishment address"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Logo Field */}
            <form.Field name="logo">
              {(field) => (
                <FileUploader
                  label="Logo"
                  previewComponent={(previewUrl: string) => (
                    <img
                      src={previewUrl}
                      alt="Logo Preview"
                      className="h-32 w-full object-cover rounded-md"
                    />
                  )}
                  defaultPreview={field.state.value}
                  onFileUploaded={(url) => field.handleChange(url)}
                  isAvatar={true}
                />
              )}
            </form.Field>

            {/* URL Field */}
            <form.Field 
              name="url"
              validators={{
                onChange: ({ value }) => value ? validateUrlSlug(value) : undefined,
                onBlur: ({ value }) => validateUrlSlug(value),
                onSubmit: ({ value }) => validateUrlSlug(value),
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="url">URL Slug</Label>
                  <Input
                    id="url"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter URL slug (e.g., my-school)"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>
          </div>

          {/* Description Field */}
          <form.Field name="description">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Enter establishment description"
                  className="min-h-32"
                />
              </div>
            )}
          </form.Field>

          {/* CMS Content Field with Template Selector */}
          <form.Field name="CMSContent">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="CMSContent">CMS Content</Label>
                <CMSTemplateSelector
                  value={field.state.value || ''}
                  onChange={(value) => field.handleChange(value)}
                  onCustomChange={(value) => field.handleChange(value)}
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Hero Image Field */}
            <form.Field name="heroImage">
              {(field) => (
                <FileUploader
                  label="Hero Image"
                  previewComponent={(previewUrl: string) => (
                    <img
                      src={previewUrl}
                      alt="Hero Image Preview"
                      className="h-32 w-full object-cover rounded-md"
                    />
                  )}
                  defaultPreview={field.state.value}
                  onFileUploaded={(url) => field.handleChange(url)}
                  isAvatar={false}
                />
              )}
            </form.Field>

            {/* Gallery Images Field */}
            <form.Field name="galleryImages">
              {(field) => (
                <GalleryImagesUploader
                  value={field.state.value || []}
                  onChange={(urls) => field.handleChange(urls)}
                  maxImages={5}
                />
              )}
            </form.Field>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Email Field */}
            <form.Field name="contactEmail">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter contact email"
                  />
                </div>
              )}
            </form.Field>

            {/* Contact Phone Field */}
            <form.Field name="contactPhone">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter contact phone"
                  />
                </div>
              )}
            </form.Field>
          </div>

          {/* Social Links Field */}
          <form.Field name="socialLinks">
            {(field) => (
              <SocialLinksEditor
                value={field.state.value || {}}
                onChange={(links) => field.handleChange(links)}
              />
            )}
          </form.Field>

          {/* Stats Field */}
          <form.Field name="stats">
            {(field) => (
              <StatsEditor
                value={field.state.value || {}}
                onChange={(stats) => field.handleChange(stats)}
              />
            )}
          </form.Field>

          {/* Services Field */}
          <form.Field name="services">
            {(field) => (
              <div className="space-y-2">
                <Label>Services</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a service"
                    value={newService}
                    onChange={(e) => setNewService(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addService(field)
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={() => addService(field)}
                    variant="outline"
                    size="icon"
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>
                <ScrollArea className="h-24 w-full border rounded-md p-2">
                  <div className="flex flex-wrap gap-2">
                    {field.state.value?.map(
                      (service: string, index: number) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {service}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeService(index)}
                          />
                        </Badge>
                      )
                    )}
                  </div>
                </ScrollArea>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          {/* Active Status Field */}
          <form.Field name="isActive">
            {(field) => (
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={field.state.value}
                  onCheckedChange={field.handleChange}
                />
                <Label htmlFor="isActive">Active Status</Label>
              </div>
            )}
          </form.Field>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : submitLabel}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
