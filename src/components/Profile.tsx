import { useQuery } from '@tanstack/react-query'
import { fetchUserProfile, UserProfile } from '@/lib/user/profile'
import ProfileCard from '@/components/shared/ProfileCard'
import { Spinner } from '@/components/ui/spinner'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { ProfileEditForm } from '@/components/shared/ProfileEditForm'
import { Pencil } from 'lucide-react'

interface ProfilePageProps {
  jwt: string
}

const ProfilePage: React.FC<ProfilePageProps> = ({ jwt }) => {
  const [isEditing, setIsEditing] = useState(false)
  // Fetch user data
  const {
    data: userData,
    isLoading,
    isError,
    error,
  } = useQuery<UserProfile>({
    queryKey: ['userProfile', jwt],
    queryFn: () => fetchUserProfile(jwt),
  })

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-[80vh]">
        <Spinner size="lg" />
      </div>
    )
  if (isError)
    return (
      <div className="text-center py-8 text-red-500">
        Error: {(error as Error).message}
      </div>
    )
  if (!userData)
    return <div className="text-center py-8">No user data found</div>

  // Block access if fees are not paid (if such logic is needed)
  if (userData.isPaid === false) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-6 rounded shadow-md max-w-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Payment Required</h2>
          <p className="mb-4">
            Your school fees have not been paid. Please contact an administrator
            to complete your payment and regain access to your profile.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Profile</h2>
        {!isEditing && (
          <Button
            variant="outline"
            onClick={() => setIsEditing(true)}
            className="flex items-center gap-2"
          >
            <Pencil className="h-4 w-4" />
            Edit Profile
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
          <ProfileEditForm
            userProfile={userData}
            onCancel={() => setIsEditing(false)}
          />
        </div>
      ) : (
        <ProfileCard userProfile={userData} />
      )}
    </div>
  )
}

export default ProfilePage
