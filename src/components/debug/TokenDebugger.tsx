import React, { useState, useEffect } from 'react'
import { fetchUserProfile, UserProfile } from '@/lib/user/profile'
import { useTranslation } from 'react-i18next'

interface DecodedToken {
  [key: string]: any
}

const TokenDebugger: React.FC = () => {
  const { t } = useTranslation('common')
  const [token, setToken] = useState<string>('')
  const [decodedToken, setDecodedToken] = useState<DecodedToken | null>(null)
  const [tokenHeader, setTokenHeader] = useState<DecodedToken | null>(null)
  const [localStorageData, setLocalStorageData] = useState<
    Record<string, string | null>
  >({})
  const [error, setError] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [customToken, setCustomToken] = useState('')
  const [isCustomTokenValid, setIsCustomTokenValid] = useState<boolean | null>(
    null
  )

  useEffect(() => {
    // Get token from localStorage
    const accessToken = localStorage.getItem('access_token')
    setToken(accessToken || '')

    // Get all localStorage values
    const storageData: Record<string, string | null> = {}
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key)
        storageData[key] = value
      }
    }
    setLocalStorageData(storageData)

    // Decode token if available
    if (accessToken) {
      decodeJwtToken(accessToken)
      loadUserProfile(accessToken)
    }
  }, [])

  const decodeJwtToken = (tokenStr: string) => {
    try {
      const parts = tokenStr.split('.')
      if (parts.length !== 3) {
        setError(t('debug-folder.tokenDebugger.error.invalidToken'))
        return false
      }

      // Decode header (first part)
      const headerJson = atob(parts[0].replace(/-/g, '+').replace(/_/g, '/'))
      const header = JSON.parse(headerJson)
      setTokenHeader(header)

      // Decode payload (second part)
      const payloadJson = atob(parts[1].replace(/-/g, '+').replace(/_/g, '/'))
      const payload = JSON.parse(payloadJson)
      setDecodedToken(payload)

      setError(null)
      return true
    } catch (err) {
      setError(
        t('debug-folder.tokenDebugger.error.decodeFailure', {
          message: err instanceof Error ? err.message : String(err)
        })
      )
      return false
    }
  }

  const loadUserProfile = async (tokenStr: string) => {
    try {
      const profile = await fetchUserProfile(tokenStr)
      setUserProfile(profile)
    } catch (err) {
      console.error('Error loading user profile:', err)
    }
  }

  const handleCustomTokenChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setCustomToken(e.target.value)
  }

  const testCustomToken = () => {
    if (!customToken.trim()) {
      setError(t('debug-folder.tokenDebugger.error.emptyToken'))
      setIsCustomTokenValid(false)
      return
    }

    const isValid = decodeJwtToken(customToken)
    setIsCustomTokenValid(isValid)

    if (isValid) {
      loadUserProfile(customToken)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => alert('Copied to clipboard!'))
      .catch((err) => console.error('Failed to copy:', err))
  }

  const formatDate = (timestamp: number | string) => {
    if (!timestamp) return 'N/A'

    try {
      // If it's a string that's not a number, try to parse it as a date string
      if (typeof timestamp === 'string' && isNaN(Number(timestamp))) {
        return new Date(timestamp).toLocaleString()
      }

      // If it's a number or a numeric string, treat it as a timestamp
      return new Date(
        typeof timestamp === 'number'
          ? timestamp * 1000
          : Number(timestamp) * 1000
      ).toLocaleString()
    } catch (e) {
      return timestamp.toString()
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">{t('debug-folder.tokenDebugger.title')}</h1>

      {error && (
        <div className="p-4 mb-4 bg-red-100 text-red-700 rounded-lg">
          <h2 className="font-bold">{t('debug-folder.tokenDebugger.error.title')}</h2>
          <p>{error}</p>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.currentToken.title')}</h2>
        {token ? (
          <div className="bg-gray-100 p-3 rounded-lg">
            <div className="flex justify-between mb-2">
              <span className="text-xs text-gray-500">{t('debug-folder.tokenDebugger.currentToken.fromLocalStorage')}</span>
              <button
                onClick={() => copyToClipboard(token)}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {t('debug-folder.tokenDebugger.currentToken.copyToken')}
              </button>
            </div>
            <div className="overflow-x-auto">
              <code className="whitespace-pre-wrap break-all text-sm">
                {token}
              </code>
            </div>
          </div>
        ) : (
          <p className="text-red-500">{t('debug-folder.tokenDebugger.currentToken.noToken')}</p>
        )}
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.customToken.title')}</h2>
        <div className="space-y-2">
          <textarea
            value={customToken}
            onChange={handleCustomTokenChange}
            className="w-full h-20 border border-gray-300 rounded p-2 text-sm font-mono"
            placeholder={t('debug-folder.tokenDebugger.customToken.placeholder')}
          />
          <div className="flex justify-end">
            <button
              onClick={testCustomToken}
              className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {t('debug-folder.tokenDebugger.customToken.testButton')}
            </button>
          </div>
          {isCustomTokenValid !== null && (
            <div
              className={`text-sm ${isCustomTokenValid ? 'text-green-600' : 'text-red-600'}`}
            >
              {isCustomTokenValid
                ? t('debug-folder.tokenDebugger.customToken.valid')
                : t('debug-folder.tokenDebugger.customToken.invalid')}
            </div>
          )}
        </div>
      </div>

      {tokenHeader && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.tokenHeader.title')}</h2>
          <div className="bg-gray-100 p-3 rounded-lg overflow-x-auto">
            <pre className="text-sm">
              {JSON.stringify(tokenHeader, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {decodedToken && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.tokenPayload.title')}</h2>
          <div className="bg-gray-100 p-3 rounded-lg overflow-x-auto">
            <pre className="text-sm">
              {JSON.stringify(decodedToken, null, 2)}
            </pre>
          </div>

          {/* Display expiration details if available */}
          {(decodedToken.exp || decodedToken.iat) && (
            <div className="mt-2 bg-blue-50 p-3 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-1">
                {t('debug-folder.tokenDebugger.tokenPayload.timing.title')}
              </h3>
              <table className="w-full text-sm">
                <tbody>
                  {decodedToken.iat && (
                    <tr>
                      <td className="pr-4 py-1 font-medium">{t('debug-folder.tokenDebugger.tokenPayload.timing.issuedAt')}</td>
                      <td>{formatDate(decodedToken.iat)}</td>
                    </tr>
                  )}
                  {decodedToken.exp && (
                    <tr>
                      <td className="pr-4 py-1 font-medium">{t('debug-folder.tokenDebugger.tokenPayload.timing.expiresAt')}</td>
                      <td>{formatDate(decodedToken.exp)}</td>
                    </tr>
                  )}
                  {decodedToken.exp && (
                    <tr>
                      <td className="pr-4 py-1 font-medium">{t('debug-folder.tokenDebugger.tokenPayload.timing.status')}</td>
                      <td
                        className={
                          decodedToken.exp * 1000 > Date.now()
                            ? 'text-green-600'
                            : 'text-red-600'
                        }
                      >
                        {decodedToken.exp * 1000 > Date.now()
                          ? t('debug-folder.tokenDebugger.tokenPayload.timing.valid')
                          : t('debug-folder.tokenDebugger.tokenPayload.timing.expired')}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {userProfile && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.userProfile.title')}</h2>
          <div className="bg-gray-100 p-3 rounded-lg overflow-x-auto">
            <pre className="text-sm">
              {JSON.stringify(userProfile, null, 2)}
            </pre>
          </div>
        </div>
      )}

      <div>
        <h2 className="text-xl font-semibold mb-2">{t('debug-folder.tokenDebugger.localStorage.title')}</h2>
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-200">
              <th className="p-2 text-left border border-gray-300">{t('debug-folder.tokenDebugger.localStorage.columns.key')}</th>
              <th className="p-2 text-left border border-gray-300">{t('debug-folder.tokenDebugger.localStorage.columns.value')}</th>
              <th className="p-2 text-left border border-gray-300">{t('debug-folder.tokenDebugger.localStorage.columns.actions')}</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(localStorageData).map(([key, value]) => (
              <tr key={key} className="border-b border-gray-200">
                <td className="p-2 border border-gray-300 font-medium">
                  {key}
                </td>
                <td className="p-2 border border-gray-300 overflow-x-auto max-w-xs">
                  {key === 'access_token' ? (
                    <span className="text-gray-500">{t('debug-folder.tokenDebugger.localStorage.tokenHidden')}</span>
                  ) : key === 'avatar' && value?.startsWith('data:image') ? (
                    <div className="flex items-center">
                      <img
                        src={value}
                        alt="Avatar"
                        className="w-8 h-8 mr-2 rounded-full"
                      />
                      <span className="text-gray-500">{t('debug-folder.tokenDebugger.localStorage.imageData')}</span>
                    </div>
                  ) : (
                    <code className="text-xs">{value}</code>
                  )}
                </td>
                <td className="p-2 border border-gray-300">
                  <button
                    onClick={() => {
                      if (value) copyToClipboard(value)
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 mr-2"
                    disabled={!value}
                  >
                    {t('debug-folder.tokenDebugger.localStorage.copy')}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default TokenDebugger
