import { Card, CardContent } from "@/components/ui/card"
import { BookOpen } from "lucide-react"

interface Subject {
  id: string
  name: string
}

interface SubjectSelectorProps {
  subjects: Subject[]
  selectedSubject?: string
  onSelectSubject: (id: string | null) => void
}

export function SubjectSelector({
  subjects,
  selectedSubject,
  onSelectSubject,
}: SubjectSelectorProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {subjects.map((subject) => (
        <Card
          key={subject.id}
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedSubject === subject.id
              ? "border-primary bg-primary/5 ring-2 ring-primary/20"
              : "hover:border-primary/50"
          }`}
          onClick={() => onSelectSubject(subject.id)}
        >
          <CardContent className="p-4 flex items-center gap-3">
            <div className={`p-2 rounded-full ${selectedSubject === subject.id ? "bg-primary/20" : "bg-muted"}`}>
              <BookOpen className={`h-4 w-4 ${selectedSubject === subject.id ? "text-primary" : "text-muted-foreground"}`} />
            </div>
            <span className="font-medium">{subject.name}</span>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
