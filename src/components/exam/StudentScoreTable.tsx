import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { EditableCell } from "./EditableCell"

interface Student {
  id: string
  name: string
  scores: Record<string, string | number>
}

interface StudentScoreTableProps {
  students: Student[]
  examTypes: string[]
  onScoreChange: (studentId: string, examType: string, value: string) => void
  readOnly?: boolean
}

export function StudentScoreTable({ 
  students, 
  examTypes, 
  onScoreChange,
  readOnly = false
}: StudentScoreTableProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Student Scores</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              {examTypes.map((type) => (
                <TableHead key={type}>{type}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id}>
                <TableCell className="font-medium">{student.name}</TableCell>
                {examTypes.map((type) => {
                  const value = student.scores[type] || "-"
                  return readOnly ? (
                    <TableCell key={type}>{value}</TableCell>
                  ) : (
                    <EditableCell
                      key={type}
                      value={value}
                      onChange={(newValue) => onScoreChange(student.id, type, newValue)}
                    />
                  )
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}