import html2pdf from 'html2pdf.js';
import type { Student } from './AdminScoreTable';

interface StudentReport {
  name: string;
  school: string;
  className: string;
  scores: Record<string, string | number>;
  moyenne?: number;
  subjectName: string;
}

export function generateStudentReport(student: StudentReport) {
  const content = `
    <div dir="rtl" style="padding: 20px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 30px;">
        <div style="background-color:rgb(24, 78, 185); color: white; padding: 12px 24px; border-radius: 8px; display: inline-block; margin-bottom: 16px;">
          <h1 style="font-size: 24px; margin: 0;">بطاقة أعداد ${student.subjectName}</h1>
        </div>
        <div style="color: #666; margin: 8px 0;">السنة الدراسية 2024-2025</div>
      </div>

      <div style="display: flex; flex-direction: column; gap: 24px;">
        <!-- Student Info -->
        <div style="background-color: #f3e8ff; border: 1px solid #d8b4fe; border-radius: 8px; overflow: hidden;">
          <div style="background-color:rgb(24, 78, 185); color: white; text-align: center; padding: 8px;">
            <h3 style="font-size: 14px; margin: 0;">معلومات الطالب</h3>
          </div>
          <div style="padding: 16px;">
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">الإسم: ${student.name}</div>
            <div style="font-size: 14px; color: #666;">القسم: ${student.className}</div>
            <div style="font-size: 14px; color: #666;">المدرسة: ${student.school}</div>
          </div>
        </div>

        <!-- Scores Table -->
        <div style="background-color: white; border-radius: 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.05); overflow: hidden;">
          <div style="background-color: #2563eb; color: white; text-align: center; padding: 8px;">
            <h2 style="margin: 0; font-weight: bold;">${student.subjectName}</h2>
          </div>
          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background-color: rgb(24, 78, 185);">
                  <th style="padding: 12px; text-align: right; border: 1px solid #rgb(24, 78, 185); font-size: 14px;">المادة</th>
                  ${Object.keys(student.scores).map(examType => `
                    <th style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px;">${examType}</th>
                  `).join('')}
                  <th style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px; background-color: #2563eb; color: white;">المعدل</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="padding: 12px; border: 1px solid #ddd; font-size: 14px; font-weight: bold;">${student.subjectName}</td>
                  ${Object.values(student.scores).map(score => `
                    <td style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px;">${score || '-'}</td>
                  `).join('')}
                  <td style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 16px; font-weight: bold; color: #dc2626;">
                    ${student.moyenne?.toFixed(2) || '-'}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `;

  // Configure html2pdf options
  const opt = {
    margin: 1,
    filename: `${student.subjectName}-report-${student.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
  };

  // Generate PDF
  const element = document.createElement('div');
  element.innerHTML = content;
  document.body.appendChild(element);

  html2pdf().set({
    ...opt,
    jsPDF: {
      ...opt.jsPDF,
      orientation: 'portrait' as 'portrait' | 'landscape'
    }
  }).from(element).save().then(() => {
    document.body.removeChild(element);
  });
}

export function generateAllStudentReports(students: Student[], subjects: { id: string, name: string }[]) {
  // Generate report for each student and each subject
  students.forEach((student, studentIndex) => {
    subjects.forEach((subject, subjectIndex) => {
      // Calculate total delay based on both student and subject indices
      const totalDelay = (studentIndex * subjects.length + subjectIndex) * 1000;
      
      // Add a delay between each PDF generation to prevent browser overload
      setTimeout(() => {
        const subjectScores = student.scores?.[subject.id];
        generateStudentReport({
          ...student,
          subjectName: subject.name,
          scores: typeof subjectScores === 'object' && subjectScores !== null ? subjectScores : {},
        });
      }, totalDelay); // 1 second delay between each report
    });
  });
}

export function generateCombinedReport(student: Student, subjects: { id: string, name: string }[]) {
  const content = `
    <div dir="rtl" style="padding: 20px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 24px; margin: 0; color: #333;">بطاقة أعداد الطالب</h1>
        <div style="color: #666; margin: 8px 0;">السنة الدراسية 2024-2025</div>
      </div>

      <div style="display: flex; flex-direction: column; gap: 24px;">
        <!-- Student Info -->
        <div style="background-color: #f3e8ff; border: 1px solid #d8b4fe; border-radius: 8px; overflow: hidden;">
          <div style="background-color: #9333ea; color: white; text-align: center; padding: 8px;">
            <h3 style="font-size: 14px; margin: 0;">معلومات الطالب</h3>
          </div>
          <div style="padding: 16px;">
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">الإسم: ${student.name}</div>
            <div style="font-size: 14px; color: #666;">القسم: ${student.className}</div>
            <div style="font-size: 14px; color: #666;">المدرسة: ${student.school}</div>
          </div>
        </div>

        ${subjects.map(subject => {
          const subjectScores = student.scores?.[subject.id] || {};
          const moyenne = student.moyenne;
          
          return `
            <!-- Scores Table for ${subject.name} -->
            <div style="background-color: white; border-radius: 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.05); overflow: hidden;">
              <div style="background-color: rgb(145, 159, 187); color: white; text-align: center; padding: 8px;">
                <h2 style="margin: 0; font-weight: bold;">${subject.name}</h2>
              </div>
              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background-color: #dbeafe;">
                      <th style="padding: 12px; text-align: right; border: 1px solid #ddd; font-size: 14px;">المادة</th>
                      ${Object.keys(subjectScores).map(examType => `
                        <th style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px;">${examType}</th>
                      `).join('')}
                      <th style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px; background-color: rgb(145, 159, 187); color: white;">المعدل</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td style="padding: 12px; border: 1px solid #ddd; font-size: 14px; font-weight: bold;">${subject.name}</td>
                      ${Object.values(subjectScores).map(score => `
                        <td style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 14px;">${score || '-'}</td>
                      `).join('')}
                      <td style="padding: 12px; text-align: center; border: 1px solid #ddd; font-size: 16px; font-weight: bold; color: #dc2626;">
                        ${moyenne?.toFixed(2) || '-'}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          `;
        }).join('')}
      </div>
    </div>
  `;

  // Configure html2pdf options
  const opt = {
    margin: 1,
    filename: `combined-report-${student.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
  };

  // Generate PDF
  const element = document.createElement('div');
  element.innerHTML = content;
  document.body.appendChild(element);

  html2pdf().set({
    ...opt,
    jsPDF: {
      ...opt.jsPDF,
      orientation: 'portrait' as 'portrait' | 'landscape'
    }
  }).from(element).save().then(() => {
    document.body.removeChild(element);
  });
}