import { useState, useRef, useEffect } from "react"
import { TableCell } from "@/components/ui/table"
import { Input } from "@/components/ui/input"

interface EditableCellProps {
  value: string | number
  onChange: (value: string) => void
}

export function EditableCell({ value, onChange }: EditableCellProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value.toString())
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleDoubleClick = () => {
    setIsEditing(true)
    setEditValue(value.toString())
  }

  const handleBlur = () => {
    setIsEditing(false)
    if (editValue !== value.toString()) {
      onChange(editValue)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setIsEditing(false)
      onChange(editValue)
    } else if (e.key === "Escape") {
      setIsEditing(false)
      setEditValue(value.toString())
    }
  }

  return (
    <TableCell onDoubleClick={handleDoubleClick} className="p-0">
      {isEditing ? (
        <Input
          ref={inputRef}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className="h-8 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
        />
      ) : (
        <div className="px-4 py-2 h-full w-full cursor-pointer">
          {value}
        </div>
      )}
    </TableCell>
  )
}
