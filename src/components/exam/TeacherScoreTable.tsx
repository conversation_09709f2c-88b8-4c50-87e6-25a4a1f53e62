import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { EditableCell } from "./EditableCell"
import { Download, Search, Filter } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useState } from "react"

interface Student {
  id: string
  name: string
  scores: Record<string, string | number>
}

interface TeacherScoreTableProps {
  students: Student[]
  examTypes: string[]
  onScoreChange: (studentId: string, examType: string, value: string) => void
}

export function TeacherScoreTable({ 
  students, 
  examTypes, 
  onScoreChange 
}: TeacherScoreTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  
  const filteredStudents = students.filter(student => 
    student.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="border rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">Name</TableHead>
                {examTypes.map((type) => (
                  <TableHead key={type} className="font-semibold">{type}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student) => (
                  <TableRow key={student.id} className="hover:bg-muted/30">
                    <TableCell className="font-medium">{student.name}</TableCell>
                    {examTypes.map((type) => {
                      const value = student.scores[type] || "-"
                      return (
                        <EditableCell
                          key={type}
                          value={value}
                          onChange={(newValue) => onScoreChange(student.id, type, newValue)}
                        />
                      )
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={examTypes.length + 1} className="h-24 text-center">
                    No students found matching your search.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      
      <div className="flex justify-between items-center text-sm text-muted-foreground">
        <p>
          Showing {filteredStudents.length} of {students.length} students
        </p>
        <p className="italic">
          Double-click on a cell to edit the score
        </p>
      </div>
    </div>
  )
}