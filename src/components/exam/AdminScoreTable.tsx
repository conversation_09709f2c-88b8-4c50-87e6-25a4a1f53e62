import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { EditableCell } from "./EditableCell"
import { Calculator, FileEdit, Search, FileText } from "lucide-react"

export interface Student {
  id: string
  name: string
  school: string
  className: string
  academicYear: string
  birthDate?: string
  scores: Record<string, string | number>
  moyenne?: number
}

interface AdminScoreTableProps {
  students: Student[]
  examTypes: string[]
  onScoreChange: (studentId: string, examType: string, value: string) => void
  onMoyenneChange: (studentId: string, value: string) => void
  onCalculateAll: () => void
  onOpenFormulaDialog: () => void
  onGenerateReports: () => void
  subjectName: string
}

export function AdminScoreTable({ 
  students, 
  examTypes, 
  onScoreChange,
  onMoyenneChange,
  onCalculateAll,
  onOpenFormulaDialog,
  onGenerateReports,
  subjectName
}: AdminScoreTableProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            Showing {students.length} students - {subjectName}
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onGenerateReports}
            className="flex items-center gap-1"
          >
            <FileText className="h-4 w-4" />
            Download Reports
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onOpenFormulaDialog}
            className="flex items-center gap-1"
          >
            <FileEdit className="h-4 w-4" />
            Edit Formula
          </Button>
          <Button 
            size="sm" 
            onClick={onCalculateAll}
            className="flex items-center gap-1"
          >
            <Calculator className="h-4 w-4" />
            Calculate All Averages
          </Button>
        </div>
      </div>

      <div className="border rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">Name</TableHead>
                {examTypes.map((type) => (
                  <TableHead key={type} className="font-semibold">{type}</TableHead>
                ))}
                <TableHead className="font-semibold text-primary">Moyenne</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {students.map((student) => (
                <TableRow key={student.id} className="hover:bg-muted/30">
                  <TableCell className="font-medium">{student.name}</TableCell>
                  {examTypes.map((type) => {
                    const value = student.scores[type] || "-"
                    return (
                      <EditableCell
                        key={type}
                        value={value}
                        onChange={(newValue) => onScoreChange(student.id, type, newValue)}
                      />
                    )
                  })}
                  <TableCell className="bg-primary/5">
                    <Input
                      value={student.moyenne !== undefined ? student.moyenne : ""}
                      onChange={(e) => onMoyenneChange(student.id, e.target.value)}
                      placeholder="Enter average"
                      className="h-8 w-full bg-transparent border-primary/20 focus:border-primary"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}