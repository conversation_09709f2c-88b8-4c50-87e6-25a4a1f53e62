import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calculator, Info } from "lucide-react"

interface FormulaDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  examTypes: string[]
  formula: string
  coefficients: Record<string, number>
  onSave: (formula: string, coefficients: Record<string, number>) => void
}

export function FormulaDialog({
  open,
  onOpenChange,
  examTypes,
  formula,
  coefficients,
  onSave,
}: FormulaDialogProps) {
  const [localFormula, setLocalFormula] = useState(formula)
  const [localCoefficients, setLocalCoefficients] = useState<Record<string, number>>(coefficients)
  const [activeTab, setActiveTab] = useState<string>("formula")

  const handleCoefficientChange = (examType: string, value: string) => {
    const numValue = parseFloat(value) || 0
    setLocalCoefficients((prev) => ({
      ...prev,
      [examType]: numValue,
    }))
  }

  const handleSave = () => {
    onSave(localFormula, localCoefficients)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Average Calculation Settings
          </DialogTitle>
          <DialogDescription>
            Configure how student averages are calculated across different exam types.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="formula">Formula</TabsTrigger>
            <TabsTrigger value="coefficients">Coefficients</TabsTrigger>
          </TabsList>
          
          <TabsContent value="formula" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="formula">Average Calculation Formula</Label>
              <Input
                id="formula"
                value={localFormula}
                onChange={(e) => setLocalFormula(e.target.value)}
                placeholder="e.g., (Control1*0.3 + Control2*0.3 + TP1*0.4)"
              />
              <div className="flex items-start gap-2 p-3 bg-muted rounded-md text-sm">
                <Info className="h-4 w-4 mt-0.5 text-blue-500" />
                <div>
                  <p className="font-medium">Formula Tips:</p>
                  <ul className="list-disc list-inside mt-1 text-muted-foreground">
                    <li>Use exam type names as variables (Control 1, Control 2, etc.)</li>
                    <li>Use basic math operators: +, -, *, /</li>
                    <li>Use parentheses for grouping: (Control 1 + Control 2) / 2</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="coefficients" className="space-y-4">
            <div className="space-y-2">
              <Label>Exam Type Coefficients</Label>
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead>Exam Type</TableHead>
                      <TableHead>Coefficient</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {examTypes.map((type) => (
                      <TableRow key={type}>
                        <TableCell>{type}</TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="0"
                            max="1"
                            step="0.1"
                            value={localCoefficients[type] || 0}
                            onChange={(e) => handleCoefficientChange(type, e.target.value)}
                            className="w-24"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-xs text-muted-foreground">
                Coefficients should typically sum to 1.0 for a weighted average.
              </p>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="flex items-center justify-between sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="flex items-center gap-1">
            <Calculator className="h-4 w-4" />
            Save Formula
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
