import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface ExamScore {
  examType: string
  value: number | string
}

interface StudentSubjectScore {
  subject: string
  scores: ExamScore[]
}

interface StudentExamTableProps {
  subjects: StudentSubjectScore[]
}

export function StudentExamTable({ subjects = [] }: StudentExamTableProps) {
  // Define exam types based on requirements
  const examTypes = ['Control 1', 'Control 2', 'Syntas 1', 'Syntas 2', 'TP1', 'TP2', 'Oral 1', 'Oral 2']

  return (
    <Card>
      <CardHeader>
        <CardTitle>My Exam Scores</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subject</TableHead>
              {examTypes.map((type) => (
                <TableHead key={type}>{type}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {subjects.map((subject) => (
              <TableRow key={subject.subject}>
                <TableCell className="font-medium">{subject.subject}</TableCell>
                {examTypes.map((type) => {
                  const score = subject.scores.find(s => s.examType === type)
                  return (
                    <TableCell key={type}>
                      {score ? score.value : '-'}
                    </TableCell>
                  )
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}