
import html2pdf from 'html2pdf.js';
import type { Student } from './AdminScoreTable';

interface StudentReport {
  name: string;
  school: string;
  className: string;
  academicYear: string;
  birthDate?: string;  // Make birthDate optional
  scores: Record<string, string | number>;
  moyenne?: number;
}

export function generateStudentReport(student: StudentReport) {
  // Filter out subjects with no scores
  const validScores = Object.entries(student.scores).filter(([_, score]) => score !== '-' && score !== '');

  const content = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <!-- School Header -->
      <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="font-size: 28px; color: #2563eb; margin: 0;">${student.school}</h1>
        <div style="color: #666; margin-top: 8px;">Academic Year ${student.academicYear}</div>
      </div>

      <!-- Report Title -->
      <div style="text-align: center; margin-bottom: 30px;">
        <div style="background-color: #2563eb; color: white; padding: 12px 24px; border-radius: 8px; display: inline-block;">
          <h2 style="font-size: 24px; margin: 0;">Academic Report - Term 2</h2>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 24px;">
        <!-- Left Side - Student Info -->
        <div style="display: flex; flex-direction: column; gap: 16px;">
          <!-- Student Details Card -->
          <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px;">
            <h3 style="font-size: 18px; color: #2563eb; margin: 0 0 16px 0;">Student Information</h3>
            <div style="display: grid; gap: 12px;">
              <div>
                <div style="font-weight: 600;">Name</div>
                <div>${student.name}</div>
              </div>
              <div>
                <div style="font-weight: 600;">Class</div>
                <div>${student.className}</div>
              </div>
              <div>
                <div style="font-weight: 600;">Birth Date</div>
                <div>${student.birthDate}</div>
              </div>
            </div>
          </div>

          <!-- Average Card -->
          <div style="background-color: #dbeafe; border: 1px solid #93c5fd; border-radius: 8px; padding: 20px;">
            <h3 style="font-size: 18px; color: #2563eb; margin: 0 0 16px 0;">Term Average</h3>
            <div style="font-size: 32px; font-weight: bold; color: #2563eb; text-align: center;">
              ${student.moyenne?.toFixed(2) || '-'}
            </div>
          </div>
        </div>

        <!-- Right Side - Scores Table -->
        <div style="background-color: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
          <div style="background-color: #2563eb; color: white; padding: 16px; border-radius: 8px 8px 0 0;">
            <h3 style="margin: 0; font-size: 18px;">Subject Scores</h3>
          </div>
          <div style="padding: 16px;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background-color: #f1f5f9;">
                  <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0;">Subject</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 2px solid #e2e8f0;">Score /20</th>
                </tr>
              </thead>
              <tbody>
                ${validScores.map(([subject, score], index) => `
                  <tr style="background-color: ${index % 2 === 0 ? '#ffffff' : '#f8fafc'};">
                    <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;">${subject}</td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0; font-weight: 600;">${score}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `;

  // Configure html2pdf options
  const opt = {
    margin: 1,
    filename: `${student.school.toLowerCase().replace(/\s+/g, '-')}-${student.name.toLowerCase().replace(/\s+/g, '-')}-report.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
  };

  // Generate PDF
  const element = document.createElement('div');
  element.innerHTML = content;
  document.body.appendChild(element);

  html2pdf().set({
    ...opt,
    jsPDF: {
      ...opt.jsPDF,
      orientation: 'portrait' as 'portrait' | 'landscape'
    }
  }).from(element).save().then(() => {
    document.body.removeChild(element);
  });
}

export function generateAllStudentReports(students: Student[]) {
  // Generate report for each student
  students.forEach((student, index) => {
    // Add a small delay between each PDF generation to prevent browser overload
    setTimeout(() => {
      generateStudentReport(student);
    }, index * 1000); // 1 second delay between each report
  });
}

