import { Card, CardContent } from "@/components/ui/card"
import { Users } from "lucide-react"

interface ClassCardProps {
  id: string
  name: string
  onClick: (id: string) => void
  isSelected: boolean
}

export function ClassCard({ id, name, onClick, isSelected }: ClassCardProps) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected 
          ? "border-primary bg-primary/5 ring-2 ring-primary/20" 
          : "hover:border-primary/50"
      }`}
      onClick={() => onClick(id)}
    >
      <CardContent className="p-6 flex items-center gap-4">
        <div className={`p-3 rounded-full ${isSelected ? "bg-primary/20" : "bg-muted"}`}>
          <Users className={`h-6 w-6 ${isSelected ? "text-primary" : "text-muted-foreground"}`} />
        </div>
        <div>
          <h3 className="font-medium">{name}</h3>
          <p className="text-sm text-muted-foreground">Click to select</p>
        </div>
      </CardContent>
    </Card>
  )
}
