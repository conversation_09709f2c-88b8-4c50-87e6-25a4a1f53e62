{"cmsTemplateSelector": {"label": "Select CMS Template", "templates": [{"id": "welcome", "name": "Welcome Template", "preview": "Welcome to our school - A place for learning and growth", "content": {"title": "Welcome to Our School", "subtitle": "A premier educational institution dedicated to excellence.", "features": {"academic": {"title": "Academic Excellence", "description": "Our curriculum is designed to challenge and inspire students to reach their full potential."}, "faculty": {"title": "Dedicated Faculty", "description": "Our teachers are experienced professionals committed to student success."}, "facilities": {"title": "Modern Facilities", "description": "Our campus features state-of-the-art classrooms, laboratories, and recreational areas."}}}}, {"id": "about", "name": "About Us Template", "preview": "About our school - Our history, mission and values", "content": {"title": "About Our School", "history": "Founded in 2010, our school has a rich history of academic excellence and community engagement.", "mission": {"title": "Our Mission", "description": "To provide a nurturing environment where students can excel academically and develop into well-rounded individuals."}, "values": {"title": "Our Values", "items": [{"name": "Excellence", "description": "We strive for excellence in all we do."}, {"name": "Integrity", "description": "We act with honesty and ethical behavior."}, {"name": "Respect", "description": "We value diversity and treat everyone with dignity."}, {"name": "Innovation", "description": "We embrace new ideas and approaches to learning."}]}}}, {"id": "programs", "name": "Programs Template", "preview": "Our educational programs and curriculum", "content": {"title": "Our Educational Programs", "subtitle": "We offer comprehensive programs designed to meet the needs of all students.", "programs": {"elementary": {"title": "Elementary Education", "description": "A strong foundation in core subjects with emphasis on creativity and critical thinking."}, "middle": {"title": "Middle School", "description": "Expanding knowledge and developing independent learning skills."}, "high": {"title": "High School", "description": "College preparatory curriculum with advanced placement options."}, "special": {"title": "Special Programs", "description": "Including arts, athletics, STEM, and language immersion."}}}}, {"id": "custom", "name": "Custom Template", "preview": "Create your own custom content"}], "customPlaceholder": "Enter your custom HTML content here", "previewLabel": "Template Preview"}, "galleryImagesUploader": {"alert": "Changes to gallery images will only be saved when you submit the form.", "label": "Gallery Images", "addButton": "Add Image", "addFirstButton": "Add your first image", "noImagesText": "No gallery images added yet", "uploadLabel": "Upload Gallery Image", "cancelButton": "Cancel", "deleteDialog": {"title": "Are you sure?", "description": "This will remove the image from the gallery. The change will only be saved when you submit the form.", "cancelButton": "Cancel", "removeButton": "Remove"}, "imageAlt": "Gallery image"}, "simpleGalleryUploader": {"alert": "Changes to gallery images will only be saved when you submit the form.", "label": "Gallery Images", "addButton": "Add Image", "noImagesAdded": "No images added yet", "addFirstButton": "Add your first image", "uploadLabel": "Upload Gallery Image", "cancelButton": "Cancel"}, "socialLinksEditor": {"alert": "Changes to social links will only be saved when you submit the form.", "label": "Social Media Links", "placeholders": {"facebook": "Facebook URL", "twitter": "Twitter URL", "instagram": "Instagram URL", "linkedin": "LinkedIn URL"}}, "statsEditor": {"label": "School Statistics", "stats": {"students": "Students", "teachers": "Teachers", "courses": "Courses", "awards": "Awards"}, "placeholders": {"students": "Number of students", "teachers": "Number of teachers", "courses": "Number of courses", "awards": "Number of awards"}}}