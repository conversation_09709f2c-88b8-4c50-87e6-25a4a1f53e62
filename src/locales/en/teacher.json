{"timetableTest": {"title": "Teacher Timetable Test Page", "loading": "Loading timetable...", "error": "Failed to load timetable. Please try again later.", "noData": "No timetable data available.", "sections": {"rawData": {"title": "Raw Data"}, "scheduleData": {"title": "Schedule Data", "noSchedule": "No schedule data available."}}, "classLabel": "Class", "dayLabels": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "lundi": "Monday", "mardi": "Tuesday", "mercredi": "Wednesday", "jeudi": "Thursday", "vendredi": "Friday", "samedi": "Saturday", "dimanche": "Sunday"}, "lessonDetails": {"subject": "Subject", "time": "Time", "room": "Room", "noLessons": "No lessons"}, "debug": {"fetchingTimetable": "Fetching teacher timetable...", "timetableReceived": "Teacher timetable data received", "fetchError": "Error fetching teacher timetable"}, "accessibility": {"timetableSection": "Teacher's weekly schedule", "daySchedule": "Schedule for {day}", "lessonInfo": "{subject} class from {startTime} to {endTime} in room {room}"}}, "timetable": {"title": "Teacher Timetable", "backButton": "Back", "loading": "Loading timetable...", "error": "Failed to load timetable. Please try again later.", "noData": {"title": "No timetable data available.", "contactAdmin": "Please contact your administrator."}, "noSchedule": {"title": "No schedule data found in your timetable.", "contactAdmin": "Please contact your administrator."}, "table": {"headers": {"day": "Day", "time": "Time", "subject": "Subject", "class": "Class", "room": "Room"}, "noLessons": "No lessons", "today": {"highlight": "Today"}}, "debug": {"fetchingTimetable": "Fetching teacher timetable...", "timetableReceived": "Teacher timetable data received:", "processingData": "Processing timetable data into display format", "fetchError": "Error fetching teacher timetable:"}, "accessibility": {"backButton": "Go back to previous page", "timetableIcon": "Calendar icon", "timetableHeading": "Weekly schedule for teacher", "todayHighlight": "Current day's schedule", "timeSlot": "Time slot", "subjectLabel": "Subject name", "classLabel": "Class name", "roomLabel": "Room number or name"}, "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}, "teacherService": {"title": "Services", "services": {"attendanceList": "Attendance List", "markAbsent": "<PERSON>", "classView": "Class View", "timetable": "Timetable", "quizzGenerator": "Quizz Generator", "whiteboard": "Whiteboard", "myNotes": "My Notes", "chat": "Cha<PERSON>", "soon": "Soon"}, "inspirationalQuote": "Success is the sum of small efforts—repeated daily. Show up, even when it's hard.", "emptyCard": "More services coming soon", "logout": {"button": "Logout", "confirmation": {"title": "Confirm <PERSON>ut", "message": "Are you sure you want to log out?", "confirm": "Yes, log out", "cancel": "Cancel"}, "success": "You have been logged out successfully", "redirecting": "Redirecting to login page..."}, "accessibility": {"serviceCard": "Service card for {serviceName}", "serviceIcon": "Icon for {serviceName}", "comingSoon": "Coming soon badge", "inspirationalCard": "Inspirational quote card"}, "debug": {"serviceClick": "Service clicked: {serviceName}, redirecting to {path}", "logoutInitiated": "Logout initiated", "logoutCompleted": "Logout completed, clearing localStorage", "redirecting": "Redirecting to login page"}}, "teacherLayout": {"navigation": {"teacherService": "Teacher Service", "classes": "Classes", "scheduleView": "Schedule View", "attendance": "Attendance", "sessionHistory": "Session History", "studentHomework": "Student Homework", "absenceManagement": "Absence Management", "autoCorrector": "Auto Corrector", "studentNotes": "Student Notes", "studentScore": "Student Score"}, "accessibility": {"sidebar": "Teacher navigation sidebar", "mainContent": "Main content area", "navigationButton": "Navigation button for {pageName}", "activeNavigation": "Currently active page: {pageName}"}}, "teacherHomework": {"title": "Homework Management", "subjectSelection": {"allSubjects": "All Subjects", "mathematics": "Mathematics", "physics": "Physics", "chemistry": "Chemistry"}, "createHomework": {"buttonText": "Create New Homework", "modalTitle": "Create New Homework Assignment", "form": {"title": {"label": "Title"}, "description": {"label": "Description"}, "dueDate": {"label": "Due Date"}, "allowedFileTypes": {"label": "Allowed File Types"}, "buttons": {"cancel": "Cancel", "create": "Create Homework"}}}, "homeworkCard": {"dueDate": "Due Date: {date}", "acceptedFormats": "Accepted formats: {formats}", "submissions": "Submissions ({count})"}, "submissionItem": {"submitted": "Submitted: {date}", "downloadButton": "Download"}}, "teacherHeader": {"title": {"prefix": "Teacher", "suffix": "Dashboard"}, "logout": "Logout"}, "teacherClassesView": {"title": "My Classes", "loading": {"classes": "Loading classes...", "students": "Loading students..."}, "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class", "adminNote": "Students need to be added to this class by an administrator.", "contactAdmin": "Please contact your administrator to add students to this class."}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender", "actions": "Actions"}, "genderNotSpecified": "Not specified", "viewDetails": "View Details"}, "studentDetails": {"backButton": "Back to List", "title": "Student Details", "personalInfo": "Personal Information", "fields": {"email": "Email", "phone": "Phone", "birthday": "Birthday", "gender": "Gender", "cin": "CIN"}}}, "teacherAbsenceManagement": {"title": "Teacher Absence Management", "teacherName": "Teacher: <PERSON><PERSON><PERSON>", "pastAbsences": {"title": "Past Absences:", "noAbsences": "No past absences."}, "futureAbsences": {"title": "Future Absences:", "noAbsences": "No future absences."}, "absenceCard": {"date": "Date: {date}", "reason": "Reason: {reason}"}, "createAbsence": {"title": "Create New Absence Request:", "form": {"date": "Absence Date", "reason": {"label": "Reason for Absence", "placeholder": "Enter reason for absence"}, "button": "Add Absence"}, "validation": {"fillAllFields": "Please fill all fields."}, "success": "New absence recorded:\nDate: {date}\nReason: {reason}"}}, "simpleClassesView": {"loading": "Loading classes...", "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "{className} Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class"}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender"}, "genderNotSpecified": "Not specified"}, "error": {"loadingClasses": "Failed to load class data. Please try again."}}, "sessionHistoryView": {"title": "Session History", "description": "View your past sessions and attendance records", "search": {"placeholder": "Search by class or subject..."}, "filter": {"allStatuses": "All Statuses", "ongoing": "Ongoing", "reported": "Reported", "canceled": "Canceled"}, "table": {"headers": {"date": "Date", "time": "Time", "class": "Class", "subject": "Subject", "status": "Status", "actions": "Actions"}, "actions": {"details": "Details"}}, "loading": {"sessions": "Loading sessions...", "attendance": "Loading attendance records..."}, "noSessions": {"title": "No Sessions Found", "withFilters": "Try adjusting your filters to see more results.", "noFilters": "You don't have any sessions for this week."}, "attendanceDialog": {"title": "Attendance Details", "table": {"headers": {"student": "Student", "status": "Status", "notes": "Notes"}}, "noRecords": "No attendance records found for this session.", "status": {"present": "Present", "absent": "Absent", "leftEarly": "Left Early"}}, "errors": {"fetchingSessions": "Failed to load sessions. Please try again.", "fetchingAttendance": "Failed to load attendance records. Please try again."}}, "markAbsent": {"title": "Class Management", "classDetails": {"title": "Class Details: {className} - {subject}", "noData": "No data found for this class."}, "studentsList": {"title": "Students:"}, "buttons": {"sendData": "Send Absence Data"}, "alerts": {"absentStudents": "Absent students: {students}", "noAbsentStudents": "No students are marked absent."}}, "classesView": {"loading": {"classes": "Loading classes...", "students": "Loading students..."}, "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "{className} Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class"}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender", "actions": "Actions"}, "genderNotSpecified": "Not specified", "viewDetails": "View Details"}, "errors": {"loadingClasses": "Failed to load classes. Please try again.", "loadingStudents": "Failed to load students. Please try again."}}, "autoCorrectorService": {"title": "Auto Corrector", "steps": {"upload": "Upload & Configure", "results": "Correction Results"}, "uploadStep": {"cardTitle": "Upload Student Work", "form": {"subject": {"label": "Subject", "placeholder": "Select the subject to correct", "required": "Please select a subject"}, "gradeLevel": {"label": "Grade Level", "placeholder": "Select the grade level", "required": "Please select a grade level"}, "assignmentTitle": {"label": "Assignment Title (Optional)", "placeholder": "Enter assignment title"}, "studentWork": {"label": "Student Work", "required": "Please upload an image of student work", "button": "Select Image"}}, "buttons": {"startCorrection": "Start Correction", "uploading": "Uploading", "processing": "Processing"}, "processing": {"tip": "Processing image and analyzing content...", "description": "This may take a few moments as we analyze the handwriting and mathematical notation."}}, "resultsStep": {"title": "Correction Results", "newCorrection": "New Correction", "sections": {"studentWork": "Student Work", "aiAnalysis": "AI Analysis"}, "exercise": "Exercise {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "Error", "uploadFailed": "Failed to upload files. Please try again.", "imageOnly": "You can only upload image files!", "sizeLimit": "Image must be smaller than 10MB!"}, "buttons": {"previous": "Previous"}}, "autoCorrector": {"title": "Auto Corrector", "uploadCard": {"title": "Upload Student Work", "description": "Upload an image of student work to automatically analyze and correct it. The AI will identify exercises, evaluate solutions, and provide detailed feedback.", "button": "Select Image", "startCorrection": "Start Correction", "uploading": "Uploading"}, "processing": {"tip": "Processing image and analyzing content...", "description": "This may take a few moments as we analyze the handwriting and mathematical notation."}, "results": {"title": "Correction Results", "newCorrection": "New Correction", "sections": {"studentWork": "Student Work", "aiAnalysis": "AI Analysis"}, "exercise": "Exercise {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "Error", "uploadFailed": "Failed to upload files. Please try again.", "imageOnly": "You can only upload image files!", "sizeLimit": "Image must be smaller than 10MB!"}}, "attendanceView": {"loading": {"sessions": "Loading sessions...", "students": "Loading students..."}, "noSessions": {"title": "No Sessions Today", "description": "You don't have any sessions scheduled for today."}, "sessionCard": {"status": {"ongoing": "Ongoing", "reported": "Reported", "canceled": "Canceled", "unknown": "Unknown"}}, "noStudents": "No students in this class", "table": {"headers": {"name": "Name", "status": "Status", "notes": "Notes", "actions": "Actions"}, "status": {"present": "Present", "absent": "Absent", "leftEarly": "Left Early"}}, "notesDialog": {"title": "Add Notes", "description": "Add notes for student absence or early departure.", "placeholder": "Add notes here...", "buttons": {"cancel": "Cancel", "save": "Save"}}, "buttons": {"saveAttendance": "Save Attendance", "saving": "Saving...", "alreadyReported": "Already Reported"}, "errors": {"sessions": "Failed to load today's sessions. Please try again.", "students": "Failed to load students. Please try again.", "saving": "Failed to save attendance. Please try again."}, "success": {"saved": "Attendance has been saved successfully."}}, "auto-corrector-folder": {"autoCorrectorService": {"title": "Auto Corrector", "steps": {"upload": "Upload & Configure", "results": "Correction Results"}, "uploadStep": {"cardTitle": "Upload Student Work", "form": {"subject": {"label": "Subject", "placeholder": "Select the subject to correct", "required": "Please select a subject"}, "gradeLevel": {"label": "Grade Level", "placeholder": "Select the grade level", "required": "Please select a grade level"}, "assignmentTitle": {"label": "Assignment Title (Optional)", "placeholder": "Enter assignment title"}, "studentWork": {"label": "Student Work", "required": "Please upload an image of student work", "button": "Select Image"}}, "buttons": {"startCorrection": "Start Correction", "uploading": "Uploading", "processing": "Processing"}, "processing": {"tip": "Processing image and analyzing content...", "description": "This may take a few moments as we analyze the handwriting and mathematical notation."}}, "resultsStep": {"title": "Correction Results", "newCorrection": "New Correction", "sections": {"studentWork": "Student Work", "aiAnalysis": "AI Analysis"}, "exercise": "Exercise {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "Error", "uploadFailed": "Failed to upload files. Please try again.", "imageOnly": "You can only upload image files!", "sizeLimit": "Image must be smaller than 10MB!"}, "buttons": {"previous": "Previous"}}}}