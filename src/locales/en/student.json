{"viewAbsent": {"title": "My Absence Record", "studentInfo": {"name": "Student Name: {fullname}", "id": "Student ID: {id}"}, "buttons": {"requestAbsence": "Request Absence"}, "table": {"headers": {"subject": "Subject", "totalAbsences": "Total Absences", "action": "Action"}, "absenceCount": "{count} lessons", "viewDetails": "View Details"}, "detailsDialog": {"title": "{subject} Absence Details", "headers": {"date": "Date", "reason": "Reason"}}, "requestDialog": {"title": "Request Absence", "form": {"date": "Date", "reason": "Reason", "file": "File"}, "buttons": {"submit": "Submit Request"}, "success": "Absence request submitted successfully! Awaiting approval."}}, "timetable": {"loading": "Loading timetable...", "errors": {"studentInfo": "Failed to load student info.", "noClass": "No class found for this student.", "studentLoad": "Failed to load student information.", "timetableLoad": "Failed to load timetable."}, "title": "Timetable for {className}", "buttons": {"back": "Back"}, "table": {"headers": {"day": "Day", "time": "Time", "subject": "Subject", "teacher": "Teacher", "room": "Room"}, "noLessons": "No lessons"}}, "studentTimetable": {"loading": "Loading timetable...", "errors": {"studentInfo": "Failed to load student info.", "timetableLoad": "Failed to load timetable. Please try again later."}, "noData": {"timetable": "No timetable data available. Please contact your administrator.", "schedule": "No schedule data found in your timetable. Please contact your administrator."}, "title": "Timetable for {className}", "buttons": {"back": "Back"}, "table": {"headers": {"day": "Day", "time": "Time", "subject": "Subject", "teacher": "Teacher", "room": "Room"}, "noLessons": "No lessons"}}, "studentService": {"title": "Services", "cards": {"myHomework": "My Homework", "aiAssistant": "AI Assistant"}, "labels": {"soon": "SOON"}, "motivationalQuote": "Success is the sum of small efforts—repeated daily. Show up, even when it's hard."}, "studentLayout": {"navigation": {"chat": "Cha<PERSON>", "attendance": "Attendance", "myAttendance": "My Attendance", "viewAbsent": "View Absent", "scheduleView": "Schedule View", "myNotes": "My Notes", "myHomework": "My Homework", "aiAssistant": "AI Assistant", "flashcards": "Flashcards (Direct Link)"}, "loading": "Loading student information...", "labels": {"soon": "Soon"}}, "studentHeader": {"title": {"prefix": "Student", "suffix": "Dashboard"}}, "studentAttendanceView": {"classInfo": {"title": "My Class Information", "description": "Details about your class and teachers", "class": "Class", "grade": "Grade", "subjects": "My Subjects and Teachers", "noClass": "Not assigned to a class", "noGrade": "Not available", "noSubjects": "No subject or teacher information available", "teacher": "Teacher: {name}"}, "attendance": {"title": "My Attendance Records", "description": "View your attendance records and statistics", "dateRanges": {"thisMonth": "This Month", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days"}, "stats": {"totalSessions": "Total Sessions", "present": "Present", "absent": "Absent", "attendanceRate": "Attendance Rate"}, "tabs": {"all": "All", "present": "Present", "absent": "Absent", "leftEarly": "Left Early"}, "table": {"headers": {"date": "Date", "day": "Day", "time": "Time", "subject": "Subject", "class": "Class", "status": "Status", "actions": "Actions"}, "view": "View"}, "noRecords": {"title": "No attendance records", "description": "No attendance records found for the selected filters."}, "error": {"title": "Error loading data", "tryAgain": "Try Again"}}, "dialog": {"title": "Attendance Record Details", "fields": {"date": "Date", "day": "Day", "timeSlot": "Time Slot", "status": "Status", "class": "Class", "subject": "Subject", "notes": "Notes/Reason"}}, "status": {"present": "Present", "absent": "Absent", "leftEarly": "Left Early"}, "errors": {"studentId": "Student ID not found. Please log in again.", "studentData": "Could not fetch student data. Some information may be missing.", "attendanceRecords": "Could not fetch attendance records. Please try again later."}}, "homework": {"title": "My Homework", "subjectFilter": {"allSubjects": "All Subjects"}, "noHomework": {"title": "No Homework", "message": "No homework assignments found for this subject."}, "homeworkCard": {"teacher": "Teacher: {name}", "dueDate": "Due Date: {date}", "acceptedFormats": "Accepted formats: {formats}", "status": {"pending": "Pending", "submitted": "Submitted", "late": "Late"}}, "fileUpload": {"submitting": "Submitting..."}, "buttons": {"submit": "Submit"}, "successMessage": "Homework submitted successfully"}}