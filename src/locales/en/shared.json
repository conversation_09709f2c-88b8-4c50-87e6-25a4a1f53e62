{"dashboardOverview": {"title": "Dashboard Overview", "recentActivities": {"title": "Recent Activities", "description": "Latest updates from across the platform"}, "enrollmentProgress": {"title": "Enrollment Progress", "description": "Current academic year"}, "upcomingEvents": "Upcoming Events"}, "databaseOverview": {"title": "Database Overview", "lastUpdated": "Last updated:", "cards": {"students": "Students", "teachers": "Teachers", "parents": "Parents", "classes": "Classes", "subjects": "Subjects", "classrooms": "Classrooms", "admins": "Admins", "totalStudents": "Total Students", "totalTeachers": "Total Teachers", "myClasses": "My Classes", "myStudents": "My Students", "assignments": "Assignments", "viewDetails": "View details"}, "recentActivities": {"title": "Recent Database Activities", "description": "Latest changes to the database records", "viewAll": "View All"}, "systemHealth": {"title": "System Health", "storage": "Storage Usage", "records": "Records Count", "lastBackup": "Last Backup:", "nextBackup": "Next Backup:", "systemStatus": "System Status:"}, "scheduledTasks": {"title": "Scheduled Tasks"}, "allActivities": {"title": "All Database Activities", "loading": "Loading activities...", "noActivities": "No activities found."}}, "establishmentCard": {"loading": "Loading...", "official": "OFFICIAL", "property": "This card is the property of {<PERSON><PERSON><PERSON>}", "returnInfo": "If found, please return to the administration office", "signature": "Signature", "downloadButton": "Download ID Card"}, "eventsList": {"loading": "Loading events...", "error": "Failed to load upcoming events", "noEvents": "No upcoming events", "dateFormat": {"today": "Today", "tomorrow": "Tomorrow"}, "eventTypes": {"meeting": "meeting", "exam": "exam", "activity": "activity", "holiday": "holiday"}}, "fileUploader": {"label": "Upload Image", "errorAlert": {"authTitle": "Authentication Error", "uploadTitle": "Upload Error", "troubleshooting": "Troubleshooting:", "verifyToken": "Verify the CDN token in your .env file", "checkService": "Check if the CDN service is running", "contactAdmin": "Contact your administrator for a valid token"}, "buttons": {"clear": "Clear", "clickToBrowse": "Click to browse", "retrying": "Retrying...", "retry": "Retry Upload", "chooseDifferent": "Choose Different File", "uploading": "Uploading...", "changeImage": "Change Image", "selectImage": "Select Image", "cancel": "Cancel"}, "status": {"uploadSuccessful": "Upload successful", "savedFallback": "Saved using fallback mode"}, "Loading": {"loading": "Loading..."}}, "paymentCheck": {"logs": {"usingStorage": "Using payment status from localStorage: PAID", "oneTimeCheck": "One-time check of localStorage for payment status: {status}", "statusUpdate": "Payment status updated: Props={isPaid}, Effective={effectiveIsPaid}"}, "status": {"paid": "PAID", "notPaid": "NOT PAID"}}, "paymentRequired": {"title": "Payment Required", "message": {"withName": "Hello {studentName}, your school fees have not been paid.", "withoutName": "Your school fees have not been paid."}, "instruction": "Please contact an administrator to complete your payment and regain access to this page."}, "profileCard": {"fields": {"email": "Email:", "cin": "CIN:", "birthday": "Birthday:", "gender": "Gender:", "phone": "Phone:", "address": "Address:", "validUntil": "Valid Until:"}, "footer": {"property": "This card is the property of {<PERSON><PERSON><PERSON>}", "returnInfo": "If found, please return to the administration office", "signature": "Signature"}, "buttons": {"download": "Download ID Card", "preparing": "Preparing PDF..."}, "official": "OFFICIAL"}, "profileEditForm": {"fields": {"firstname": {"label": "First Name", "placeholder": "Enter first name", "error": "First name must be at least 2 characters"}, "lastname": {"label": "Last Name", "placeholder": "Enter last name", "error": "Last name must be at least 2 characters"}, "email": {"label": "Email", "placeholder": "Enter email", "error": "Invalid email address"}, "cin": {"label": "CIN", "placeholder": "Enter CIN", "error": "CIN must be at least 8 characters"}, "birthday": {"label": "Birthday", "error": "Birthday is required"}, "gender": {"label": "Gender", "placeholder": "Select gender", "options": {"male": "Male", "female": "Female"}}, "address": {"label": "Address", "placeholder": "Enter your address"}, "phone": {"label": "Phone Number", "placeholder": "Enter your phone number"}, "password": {"label": "New Password (Optional)", "placeholder": "Enter new password", "error": "Password must be at least 8 characters"}}, "buttons": {"cancel": "Cancel", "update": "Update Profile", "updating": "Updating..."}}, "ScjedimeView": {"Modify": "Modify Schedule"}, "scoreCalculator": {"search": {"label": "Search Subject", "placeholder": "Search by subject name"}, "table": {"headers": {"subject": "Subject", "formula": "Formula", "grade": "Grade", "actions": "Actions"}, "formulaPlaceholder": "Enter formula", "updateScore": "Update Score"}, "pagination": {"previous": "Previous", "next": "Next"}, "errors": {"invalidFormula": "Invalid formula:"}}, "studentNotes": {"table": {"headers": {"date": "Date", "subject": "Subject", "note": "Note"}}, "addNote": {"placeholder": "Add a new note...", "button": "Add Note"}, "noNotes": "No notes available for this student.", "subjects": {"general": "General"}}, "studentScore": {"table": {"headers": {"subject": "Subject", "score": "Score", "total": "Total", "date": "Date", "actions": "Actions"}, "buttons": {"edit": "Edit"}}, "noScores": "No scores available for this student."}, "studentTable": {"headers": {"fullName": "Full Name", "cin": "CIN", "actions": "Actions"}, "buttons": {"profile": "Profile", "absences": "Absences", "scores": "Scores"}, "noStudents": "No students found."}, "teacherForm": {"fields": {"cin": {"label": "CIN", "placeholder": "Enter CIN", "error": "CIN is required"}, "firstname": {"label": "First Name", "placeholder": "Enter first name", "error": "First name is required"}, "lastname": {"label": "Last Name", "placeholder": "Enter last name", "error": "Last name is required"}, "email": {"label": "Email", "placeholder": "Enter email", "error": "Invalid email format"}, "password": {"label": "Password", "placeholder": "Enter password", "error": "Password must be at least 6 characters"}, "title": {"label": "Title", "placeholder": "Select title", "options": {"professor": "Professor", "dr": "Dr.", "assistantProfessor": "Assistant Professor"}}, "gender": {"label": "Gender", "placeholder": "Select gender", "options": {"male": "Male", "female": "Female"}}, "birthday": {"label": "Birthday", "placeholder": "Select birthday"}, "avatar": {"label": "Teacher <PERSON><PERSON>"}, "isActive": {"label": "Status", "placeholder": "Select status", "options": {"active": "Active", "inactive": "Inactive"}}}, "buttons": {"cancel": "Cancel", "submit": "Submit"}, "errors": {"title": "Error"}}}