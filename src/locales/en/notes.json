{"noteForm": {"backButton": "Back", "title": {"edit": "Edit Note", "create": "Create New Note"}, "fields": {"title": {"label": "Note Title", "placeholder": "e.g., Cell Structure", "error": "Title is required"}, "content": {"label": "Content", "placeholder": "Start writing your note...", "saveInfo": "Your changes will only be saved when you click the {actionButton} button."}}, "buttons": {"cancel": "Cancel", "update": "Update Note", "create": "Create Note", "saving": "Saving..."}, "toast": {"createSuccess": {"title": "Success", "description": "Note created successfully"}, "updateSuccess": {"title": "Success", "description": "Note updated successfully"}, "error": {"title": "Error", "description": "Failed to {action} note"}}}, "noteFullView": {"buttons": {"back": "Back", "fullscreen": {"maximize": "Maximize", "minimize": "Minimize"}, "close": "Close"}, "dates": {"created": "Created: {date}", "updated": "Updated: {date}"}}, "notesCollection": {"loading": "Loading collection...", "error": {"title": "Error loading collection", "message": "Collection not found", "button": "Back to Collections"}, "header": {"back": "Back", "addNote": "Add Note"}, "info": {"visibility": {"public": "Public", "private": "Private"}, "topic": "Topic: {topic}", "noteCount": "{count} notes", "created": "Created: {date}"}, "emptyState": {"title": "No notes in this collection yet", "description": "Create your first note to start organizing your thoughts in this collection.", "button": "Create Your First Note"}, "noteCard": {"buttons": {"view": "View", "edit": "Edit", "delete": "Delete"}, "dates": {"created": "Created: {date}", "updated": "Updated: {date}"}}, "limitReached": {"message": "You have reached the maximum limit of 100 notes in this collection. Delete existing notes to create new ones."}, "deleteConfirm": "Are you sure you want to delete this note?", "toast": {"deleteSuccess": {"title": "Success", "description": "Note deleted successfully"}, "deleteError": {"title": "Error", "description": "Failed to delete note"}}}, "notesCollectionForm": {"header": {"back": "Back", "edit": "Edit Collection", "create": "Create New Collection"}, "fields": {"name": {"label": "Collection Name", "placeholder": "e.g., Biology Notes", "error": "Name is required"}, "topic": {"label": "Topic", "placeholder": "e.g., Science, Math, History", "error": "Topic is required"}, "visibility": {"label": "Visibility", "placeholder": "Select visibility", "options": {"private": "Private", "public": "Public"}, "descriptions": {"private": "Private collections are only visible to you.", "public": "Public collections can be seen by other users."}}}, "buttons": {"cancel": "Cancel", "update": "Update Collection", "create": "Create Collection", "saving": "Saving..."}, "toast": {"createSuccess": {"title": "Success", "description": "Collection created successfully"}, "updateSuccess": {"title": "Success", "description": "Collection updated successfully"}, "error": {"title": "Error", "description": "Failed to {action} collection"}}}, "notesCollectionList": {"header": {"title": "My Notes Collections", "stats": "{totalCount} collection{plural} • {shownCount} shown", "newButton": "New Collection"}, "search": {"placeholder": "Search collections by name or topic..."}, "filters": {"toggle": {"show": "Show Filters", "hide": "Hide Filters"}, "visibility": {"label": "Visibility", "options": {"all": "All", "public": "Public", "private": "Private"}}, "topic": {"label": "Topic", "allTopics": "All Topics"}}, "loading": "Loading collections...", "error": {"title": "Error Loading Collections", "unknown": "Unknown error occurred", "tryAgain": "Try Again"}, "emptyState": {"title": "No notes collections yet", "description": "Create your first notes collection to start organizing your thoughts. You can add text and organize them by topics.", "button": "Create Your First Collection"}, "noResults": {"message": "No collections match your search criteria", "clearFilters": "Clear Filters"}, "collectionCard": {"noteCount": "notes • Updated", "visibility": {"public": "Public", "private": "Private"}, "buttons": {"view": "View", "edit": "Edit", "delete": "Delete"}}, "deleteConfirm": "Are you sure you want to delete this collection? All notes in this collection will be permanently deleted.", "toast": {"deleteSuccess": {"title": "Success", "description": "Collection deleted successfully"}, "deleteError": {"title": "Error", "description": "Failed to delete collection"}}}, "simpleRichEditor": {"placeholder": "Start writing your note...", "buttons": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "unorderedList": "Bullet List", "orderedList": "Numbered List", "blockquote": "Quote", "codeBlock": "Code Block"}}, "tiptapEditor": {"placeholder": "Start writing your note...", "buttons": {"formatting": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "highlight": "Highlight"}, "color": {"title": "Text Color", "colors": {"black": "Black", "red": "Red", "orange": "Orange", "yellow": "Yellow", "green": "Green", "blue": "Blue", "purple": "Purple", "pink": "Pink"}}, "headings": {"heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3"}, "lists": {"bulletList": "Bullet List", "orderedList": "Numbered List"}, "blocks": {"blockquote": "Quote", "codeBlock": "Code Block"}, "media": {"link": {"button": "Link", "placeholder": "https://example.com", "add": "Add"}, "image": {"button": "Image", "placeholder": "https://example.com/image.jpg", "add": "Add"}}, "history": {"undo": "Undo", "redo": "Redo"}}, "accessibility": {"srOnly": {"view": "View", "edit": "Edit", "delete": "Delete"}}}}