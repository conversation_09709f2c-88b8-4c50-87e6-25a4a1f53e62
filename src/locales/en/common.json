{"profileForm": {"title": "Profile Information", "loading": "Loading profile data...", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "cin": "CIN", "birthday": "Birthday", "gender": "Gender", "address": "Address", "phone": "Phone Number", "password": "New Password (Optional)"}, "placeholders": {"firstName": "Enter first name", "lastName": "Enter last name", "email": "Enter email", "cin": "Enter CIN", "birthday": "Select birthday", "address": "Enter your address", "phone": "Enter your phone number", "password": "Enter new password"}, "genderOptions": {"male": "Male", "female": "Female"}, "buttons": {"cancel": "Cancel", "update": "Update Profile", "updating": "Updating..."}, "notifications": {"success": "Profile updated successfully", "error": "Failed to update profile", "loadError": "Failed to load profile data"}}, "profile": {"title": "Profile", "editButton": "Edit Profile", "loading": "Loading profile data...", "error": "Error: {{message}}", "noData": "No user data found", "paymentRequired": {"title": "Payment Required", "message": "Your school fees have not been paid. Please contact an administrator to complete your payment and regain access to your profile."}}, "footer": {"description": "JeridSchool is a leading platform for modern education management, offering innovative tools to streamline academic processes, enhance communication, and improve overall efficiency for schools and educational institutions.", "quickLinks": {"title": "Quick Links", "home": "Home", "bookDemo": "Book a Demo", "joinNow": "Join Now", "features": "Features"}, "contactUs": {"title": "Contact Us", "location": "Tunis, Tunisia", "email": "<EMAIL>", "website": "www.jeridschool.tech"}, "copyright": "© {{year}} JeridSchool. All rights reserved."}, "focusRoom": {"tasks": {"title": "Tasks", "addPlaceholder": "Add task", "addButton": "Add", "noTasks": "No tasks yet. Add one above."}, "workTimer": {"title": "Work Timer", "startButton": "Start", "pauseButton": "Pause", "resetButton": "Reset"}, "restTimer": {"title": "Rest Timer", "startButton": "Start", "pauseButton": "Pause", "resetButton": "Reset"}, "accessibility": {"timerEdit": "Double-click to edit timer duration", "taskComplete": "Task completed", "taskIncomplete": "Task incomplete", "deleteTask": "Delete task"}}, "about": {"title": "Empowering", "subtitle": "Schools", "description": "with Affordable, Comprehensive Management Solutions", "intro": "Our feature-rich, affordable school management system with top-rated customer support gives small to mid-sized private K-12 schools the power to streamline day-to-day operations, enhance reporting, and transform communication between educators, parents, and students.", "features": [{"title": "AI-Powered Timetable Generation", "description": "Our advanced AI algorithms create conflict-free schedules in seconds, saving administrators hours of manual work and ensuring optimal resource allocation for your school."}, {"title": "Comprehensive Learning Tools", "description": "Empower students with AI-generated flashcards, quizzes, and study materials that adapt to individual learning styles and help improve academic performance."}, {"title": "Seamless Communication", "description": "Connect teachers, students, and parents through our integrated messaging system, ensuring everyone stays informed about assignments, events, and student progress."}, {"title": "Detailed Analytics & Reporting", "description": "Gain valuable insights into student performance, attendance patterns, and resource utilization with our comprehensive analytics dashboard."}, {"title": "Cloud-Based Accessibility", "description": "Access your school management system from anywhere, on any device, with our secure cloud-based platform that ensures your data is always available when you need it."}], "viewFeature": "View", "accessibility": {"backgroundImage": "School Management Background Image", "featureSection": "Feature section"}}, "notfound-folder": {"notfound": {"glitchySearch": {"placeholder": "Search for the lost page..."}}, "glitch404": {"title": "404 Not Found"}, "index": {"title": "404", "description": "Oops! The page you're looking for has gone missing in action.", "debugInfo": {"title": "Debug Info:", "requestedPath": "Requested path:", "hostname": "Hostname:"}, "buttons": {"goBack": "Go Back", "home": "Beam Me Home, <PERSON><PERSON>!"}}}, "debug-folder": {"tokenDebugger": {"title": "JWT Token Debugger", "error": {"title": "Error", "invalidToken": "Invalid token format", "decodeFailure": "Failed to decode token: {{message}}", "emptyToken": "Please enter a token to test"}, "currentToken": {"title": "Current JWT Token", "fromLocalStorage": "From localStorage", "copyToken": "<PERSON><PERSON>", "noToken": "No token found in localStorage"}, "customToken": {"title": "Test Custom Token", "placeholder": "Paste a JWT token here to decode and test it", "testButton": "Test Token", "valid": "Token is valid and properly formatted.", "invalid": "Token is invalid or improperly formatted."}, "tokenHeader": {"title": "<PERSON><PERSON>"}, "tokenPayload": {"title": "Token Payload", "timing": {"title": "Token Timing Information", "issuedAt": "Issued At:", "expiresAt": "Expires At:", "status": "Status:", "valid": "<PERSON><PERSON> (Not Expired)", "expired": "Expired"}}, "userProfile": {"title": "Extracted User Profile"}, "localStorage": {"title": "LocalStorage Data", "columns": {"key": "Key", "value": "Value", "actions": "Actions"}, "tokenHidden": "[TOKEN HIDDEN]", "imageData": "[Base64 Image Data]", "copy": "Copy"}}}}