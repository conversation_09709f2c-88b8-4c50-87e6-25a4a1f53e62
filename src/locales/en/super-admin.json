{"establishmentTour": {"steps": {"welcome": {"title": "Establishments Page", "content": "Welcome to the Establishments page. Here you can manage your school establishments."}, "createButton": {"title": "Create Establishment", "content": "Click here to create a new establishment. This is required before you can access other services."}, "formOverview": {"title": "Establishment Form Overview", "content": "This form allows you to enter all the details for your new establishment. Let's go through each field."}, "name": {"title": "Establishment Name", "content": "Enter your establishment name here. This is a required field and will be displayed prominently on your school's page."}, "address": {"title": "Establishment Address", "content": "Enter your establishment's physical address here. This helps students and parents locate your school."}, "logo": {"title": "School Logo", "content": "Upload your school logo here. Click \"Select Image\" to choose a file from your computer."}, "url": {"title": "Website URL", "content": "If your school has a website, enter the URL here. This will allow students and parents to visit your website directly."}, "cmsContent": {"title": "CMS Content", "content": "Select a template for your school's content management system. This will determine the layout of your school's page."}, "description": {"title": "School Description", "content": "Write a detailed description of your school here. This will help parents and students understand what makes your school special."}, "heroImage": {"title": "Hero Image", "content": "Upload a hero image for your school. This large banner image will appear at the top of your school's page."}, "contactEmail": {"title": "Contact Email", "content": "Enter the main contact email for your school. This will be used for inquiries from parents and students."}, "contactPhone": {"title": "Contact Phone", "content": "Enter the main contact phone number for your school. This will be displayed on your school's page."}, "socialLinks": {"title": "Social Links", "content": "Add links to your school's social media profiles. This helps parents and students connect with your school online."}, "services": {"title": "Services", "content": "Add the services your school offers. Type a service and click the plus button to add it to the list."}, "activeStatus": {"title": "Active Status", "content": "Toggle whether your establishment is active. Active establishments are visible to users."}, "saveButton": {"title": "Save Establishment", "content": "After filling out the form, click here to save your establishment. You can always edit these details later."}}, "buttons": {"back": "Back", "close": "Close", "last": "Finish", "next": "Next", "skip": "<PERSON><PERSON>"}, "logs": {"starting": "EstablishmentTour: Starting tour from event", "startingZustand": "EstablishmentTour: Starting tour via Zustand", "startingDelay": "EstablishmentTour: Starting tour via Zustand after delay", "formPage": "EstablishmentTour: Already on form page, setting initial step to 2", "checkingStatus": "EstablishmentTour: Checking tour status", "autoStarting": "EstablishmentTour: Auto-starting tour", "tourStarted": "EstablishmentTour: Tour started", "tourEnded": "EstablishmentTour: Tour ended with status:", "tourCompleted": "EstablishmentTour: Tour completed", "tourSkipped": "EstablishmentTour: Tour skipped", "stepShown": "EstablishmentTour: Step {current}/{total} has been shown", "stepAboutToShow": "EstablishmentTour: Step {current}/{total} is about to be shown", "afterCreateStep": "EstablishmentTour: After Create Establishment step", "foundCreateButton": "EstablishmentTour: Found create button, clicking it", "foundCreateButtonByText": "EstablishmentTour: Found create button by text, clicking it", "alreadyOnFormPage": "EstablishmentTour: Already on form page, continuing tour", "focusingField": "EstablishmentTour: Focusing on field {selector}", "updatingStep": "EstablishmentTour: Updating <PERSON><PERSON><PERSON> step to {step}", "targetNotFound": "EstablishmentTour: Target element \"{target}\" not found", "attemptingRecover": "EstablishmentTour: Attempting to recover by moving to the next step", "atLastStep": "EstablishmentTour: At last step with error, ending tour"}}, "superAdminManagement": {"pageTitle": "Admins Table", "buttons": {"addAdmin": "Add New Admin", "profile": "Profile", "activate": "Activate", "deactivate": "Deactivate"}, "search": {"placeholder": "Search admins by name or CIN..."}, "filters": {"title": "Filters", "name": "Name", "cin": "CIN", "all": "All", "clearAll": "Clear All Filters", "activeFilters": "Active Filters:"}, "table": {"headers": {"admin": "Admin", "cin": "CIN", "status": "Status", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}}, "pagination": {"adminsCount": "{count} <PERSON><PERSON>", "page": "Page {current} of {total}"}, "addAdminModal": {"title": "Add New Admin", "fields": {"firstname": {"label": "First Name", "placeholder": "Enter first name"}, "lastname": {"label": "Last Name", "placeholder": "Enter last name"}, "email": {"label": "Email", "placeholder": "Enter email address"}, "password": {"label": "Password", "placeholder": "Enter password"}, "cin": {"label": "CIN", "placeholder": "Enter CIN"}, "birthday": {"label": "Birthday"}, "gender": {"label": "Gender", "options": {"male": "Male", "female": "Female"}}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "avatar": {"label": "Avatar (Required)", "note": "Note: Avatar image is required and must be uploaded to display correctly."}}, "buttons": {"cancel": "Cancel", "create": "Create Admin"}}, "profileModal": {"title": "Admin Profile", "fields": {"firstname": {"label": "First Name"}, "lastname": {"label": "Last Name"}, "email": {"label": "Email"}, "cin": {"label": "CIN"}, "birthday": {"label": "Birthday"}, "gender": {"label": "Gender", "options": {"male": "Male", "female": "Female"}}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "status": {"label": "Status", "options": {"active": "Active", "inactive": "Inactive"}}, "password": {"label": "Password (Optional)", "placeholder": "Leave blank to keep current password"}, "avatar": {"label": "Avatar (Optional)"}}, "buttons": {"cancel": "Cancel", "update": "Update Admin"}}, "deleteDialog": {"title": "Are you absolutely sure?", "description": "This action cannot be undone. This will permanently delete the admin {adminName} and remove their data from the server.", "buttons": {"cancel": "Cancel", "delete": "Delete"}}, "notifications": {"adminCreated": {"title": "Admin Created", "description": "The new admin has been successfully created."}, "adminUpdated": {"title": "Admin Updated", "description": "The admin profile has been successfully updated."}, "adminDeleted": {"title": "Admin Deleted", "description": "The admin has been successfully deleted."}, "statusUpdated": {"title": "Status Updated", "description": "The admin has been {status}.", "activated": "activated", "deactivated": "deactivated"}, "statusUpdateFailed": {"title": "Status Update Failed", "description": "Failed to update admin status. Please try again."}, "deletionFailed": {"title": "Deletion Failed", "description": "Failed to delete admin. Please try again."}, "creationFailed": {"title": "Admin Creation Failed", "description": "{errorMessage}"}, "updateFailed": {"title": "Admin Update Failed", "description": "{errorMessage}"}, "error": {"title": "Error", "description": "An error occurred while creating the admin. Please try again."}}}}