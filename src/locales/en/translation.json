{"routes": {"unauthorized": {"title": "Access Denied", "mainMessage": "Sorry, you don't have permission to access this page. This area requires different user privileges.", "roleInfo": {"label": "Current Role:", "message": "If you believe you should have access to this page, please contact your administrator."}, "buttons": {"goBack": "Go Back", "homePage": "Home Page"}}, "subdomain-www-test": {"wwwTest": {"title": "WWW Subdomain Test Page", "currentUrl": {"title": "Current URL Information", "fields": {"fullUrl": "Full URL:", "hostname": "Hostname:", "isSubdomain": "Is Subdomain:", "schoolName": "School Name:", "isWww": "Is WWW Subdomain:"}, "values": {"yes": "Yes", "no": "No"}}, "testing": {"title": "Test WWW Subdomain Handling", "button": "Run Tests", "currentUrlTest": {"title": "Current URL Test:"}, "results": {"title": "Test Results:"}}, "testLinks": {"title": "Test Links", "local": {"noSubdomain": "Local: No Subdomain", "wwwSubdomain": "Local: WWW Subdomain", "testSchool": "Local: Test School Subdomain"}, "production": {"noSubdomain": "Production: No Subdomain", "wwwSubdomain": "Production: WWW Subdomain", "testSchool": "Production: Test School Subdomain"}}}}, "subdomain-test": {"title": "Subdomain Testing Page", "auth": {"title": "Authentication Status", "status": {"label": "Status:", "loggedIn": "Logged In", "notLoggedIn": "Not Logged In"}, "buttons": {"login": "Log In", "logout": "Log Out"}}, "currentUrl": {"title": "Current URL Information"}, "testUrl": {"title": "Test School URL", "input": {"placeholder": "Enter school URL (e.g., 'test-school')"}, "buttons": {"test": "Test URL", "testing": "Testing...", "directApi": "Test Direct API Endpoints"}}, "results": {"title": "Test Results", "success": {"message": "School found successfully!"}, "error": {"failed": "Failed to load school"}, "noAuth": "No authentication token available"}}, "settings": {"title": "Settings", "loginRequired": "Please log in to access your settings"}, "schools": {"title": "All Schools", "loading": "Loading schools...", "error": {"title": "Error Loading Schools", "unknown": "Unknown error occurred"}, "subdomains": {"title": "Access Schools via Subdomains", "description": "You can access any school directly using its subdomain:", "note": "Note: Make sure you've run the setup script to configure your local hosts file for subdomain access."}, "card": {"services": "Services:", "more": "more", "buttons": {"viewDetails": "View Details", "viewSite": "View Site"}}}, "reset-password": {"invalid-link": {"title": "Invalid Reset Link", "description": "Please request a new password reset."}, "success": {"title": "Password Reset Successful", "description": "Redirecting to login page..."}, "form": {"title": {"blue-word": "New", "main": "Password", "description": "Enter your new password below"}, "inputs": {"new-password": "New Password", "confirm-password": "Confirm Password"}, "button": {"loading": "Resetting...", "default": "Reset Password"}, "errors": {"default": "Failed to reset password. Please try again."}}}, "register": {"welcome": {"blue-word": "Welcome", "title": "To your world", "description": "Welcome to JeridSchool. dashboard"}, "form": {"inputs": {"name": "Name", "email": "Email", "password": "Password", "workspace-name": "Workspace Name"}, "button": {"register": "Register"}, "sign-in": {"text": "have an account?", "link": "Sign in"}}, "divider": {"text": "Or continue with"}, "social": {"facebook": "Facebook", "google": "Google"}}, "quizz": {"title": "Quiz Generator", "description": "Enter text, and let us generate a custom quiz for you.", "form": {"language": {"label": "Quiz Language", "options": {"english": "English", "french": "French", "arabic": "Arabic"}}, "type": {"label": "Quiz Type", "options": {"multiple-choice": "Multiple Choice", "true-false": "True/False", "short-answer": "Short Answer"}}, "question-count": {"label": "Number of Questions"}, "input-text": {"label": "Input Text", "placeholder": "Enter your text here..."}, "button": {"generate": "Generate Quiz", "generating": "Generating..."}}, "quiz-section": {"title": "Quiz", "download-button": "Download Quiz PDF", "question-prefix": "Question", "submit-button": "Submit Quiz"}, "results": {"title": "Quiz Results", "score-text": "You got", "out-of": "out of", "correct": "correct!"}, "alerts": {"empty-input": "Please enter some text to generate a quiz from.", "answer-all": "Please answer all questions before submitting."}}, "pricing": {"title": "Pricing", "paymentFrequency": "Payment frequency", "frequencies": {"monthly": "Monthly", "annually": "Annually"}, "tiers": {"free": {"name": "Free", "description": "Get all goodies for free, no credit card required.", "features": ["Multi-platform compatibility", "Real-time notification system", "Advanced user permissions"], "cta": "Sign up"}, "pro": {"name": "Pro", "description": "When you grow, need more power and flexibility.", "features": ["All in the free plan plus", "Customizable templates", "Integration with third-party apps"], "cta": "Get started"}, "scaler": {"name": "Scaler", "description": "When you grow, need more power and flexibility.", "features": ["All in the pro plan plus", "Priority support", "Enterprise-grade security"], "cta": "Get started"}}, "soldOut": "Sold out"}, "offers": {"title": "Our Features", "features": [{"title": "Automated Timetable Scheduling", "hint": "Effortless schedule management for all", "description": "Automatically generate timetables for students and teachers based on available resources. Customize and edit timetables, handle conflicts, and receive notifications for changes."}, {"title": "Quiz Generator", "hint": "Create engaging quizzes in minutes", "description": "Generate quizzes with multiple question types, set time limits, and enjoy automatic grading with manual adjustment options."}, {"title": "Student Management", "hint": "Comprehensive student data at your fingertips", "description": "Store and manage student information, track performance over time, and access progress analytics easily."}, {"title": "Teacher Management", "hint": "Streamline teacher administration effortlessly", "description": "Track teacher profiles, schedules, and subjects. Manage availability and facilitate communication between teachers and administration."}, {"title": "Classroom Partition", "hint": "Efficient classroom resource allocation", "description": "Control classroom schedules, resources, and assignments. Manage and oversee each class's lessons and activities, including room allocation."}, {"title": "Zoom Integration", "hint": "Seamless virtual classroom experience", "description": "Integrate Zoom video calls for virtual classroom sessions. Schedule meetings directly from the platform and provide links and reminders to participants."}, {"title": "Tests and Exams with Auto-Grading", "hint": "Efficient assessment and grading system", "description": "Create tests and exams with various question types. Automatically grade applicable tests and provide immediate feedback to students."}, {"title": "Notifications", "hint": "Stay informed with timely alerts", "description": "Send automatic notifications for various events, announcements, and updates. Customize notification settings based on user roles."}], "pricePerMonth": "${price}/month", "totalPrice": "Total Price: ${price}/month", "buttons": {"addToCart": "Add to Cart", "remove": "Remove", "submit": "Submit Selection"}}, "new-password": {"title": {"blue-word": "Set a new", "main": "PASSWORD", "description": "Enter your new password and confirm password"}, "form": {"inputs": {"new-password": "New Password", "confirm-password": "Confirm Password"}, "button": {"submit": "Set New Password"}, "back-to-login": "Back to login"}, "errors": {"passwords-not-match": "Passwords do not match", "submission-error": "Form submission error"}, "success": {"message": "Password updated successfully"}}, "main-workspace": {"services": {"title": "Services", "items": {"quizz": "Quizz Generator", "storage": "Data Storage", "api": "API Services", "resources": "Resources", "deployments": "Deployments", "more": "More services"}}, "resources": {"title": "Resources", "table": {"headers": {"name": "Name", "type": "Type", "lastViewed": "Last Viewed"}, "timeFormats": {"weeks": "{{count}} weeks ago", "month": "a month ago"}}}}, "login": {"welcome": {"blue-word": "Welcome", "title": "BACK", "description": "Welcome back to JeridSchool. Dashboard"}, "form": {"inputs": {"email": "Email", "password": "Password"}, "remember-me": "Remember me", "forgot-password": "Forgot your password?", "buttons": {"sign-in": "Sign in", "signing-in": "Signing in..."}, "register": {"text": "Don't have an account?", "link": "Sign up"}}, "errors": {"invalid-credentials": "Invalid email or password. Please try again.", "default": "An error occurred during login. Please try again."}, "divider": "Or continue with"}, "join": {"title": "Join Us", "description": "Provide your details to join our community.", "form": {"inputs": {"fullname": {"label": "Full Name", "placeholder": "Full Name"}, "email": {"label": "Email Address", "placeholder": "Email Address"}, "schoolname": {"label": "School Name", "placeholder": "School Name"}, "phonenumber": {"label": "Phone Number", "placeholder": "Phone Number"}}, "buttons": {"submit": "Submit", "submitting": "Submitting..."}}, "alerts": {"success": "Form submitted successfully!", "error": "Failed to submit form. Please try again."}}, "forgot-password": {"title": {"blue-word": "Forgot", "main": "Password", "description": "Enter your email to reset your password"}, "form": {"inputs": {"email": {"placeholder": "Email address"}}, "buttons": {"reset": "Reset Password", "sending": "Sending..."}, "back-to-login": "Back to login"}, "alerts": {"success": "Reset link sent successfully. Please check your email.", "error": {"default": "Failed to send reset link. Please try again.", "network": "Network error. Please check your internet connection and try again.", "timeout": "Connection timed out. The server is taking too long to respond. Please try again later."}}}, "demo": {"header": {"backToHome": "Back to Home"}, "hero": {"title": "Schedule a <span>Demo</span> to See JeridSchool in Action", "description": "Discover how JeridSchool can transform your school operations by scheduling a personalized demo. Experience firsthand how JeridSchool simplifies management and enhances efficiency for administrators, teachers, parents, and students."}, "form": {"title": "Book Your Personalized Demo", "fields": {"firstName": {"label": "First Name*"}, "lastName": {"label": "Last Name*"}, "schoolName": {"label": "School/Institution Name*"}, "email": {"label": "Email Address*"}, "phone": {"label": "Phone Number"}, "enrollmentCount": {"label": "Enrollment Size*", "options": {"placeholder": "Select enrollment size", "small": "1-100 students", "medium": "101-500 students", "large": "501-1000 students", "extraLarge": "1001+ students"}}, "preferredDate": {"label": "Preferred Date*"}, "preferredTime": {"label": "Preferred Time*", "options": {"placeholder": "Select a time", "times": ["9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"]}}, "message": {"label": "Additional Information (Optional)", "placeholder": "Let us know about any specific features you're interested in or questions you have."}}, "buttons": {"submit": "Schedule My Demo", "processing": "Processing..."}, "legalText": "By submitting this form, you agree to our <a>Terms of Service</a> and <a>Privacy Policy</a>."}, "success": {"title": "Thank You!", "message": "Your demo request has been submitted successfully. We'll be in touch soon to confirm your appointment.", "emailNote": "Check your email for a confirmation with all the details."}, "infoPanel": {"title": "What to Expect in Your Demo", "items": [{"title": "30-Minute Personalized Session", "description": "A focused demonstration tailored to your school's specific needs and challenges."}, {"title": "Expert-Led Walkthrough", "description": "Our education specialists will guide you through JeridSchool's features and answer your questions."}, {"title": "Customized Solutions", "description": "See how JeridSchool can be configured to meet your institution's unique requirements."}, {"title": "Implementation Timeline", "description": "Learn about our onboarding process and how quickly you can get up and running."}], "contact": {"title": "Have Questions?", "description": "If you need immediate assistance or have questions before scheduling a demo, our team is here to help.", "buttons": {"email": "Email Us", "call": "Call Us"}}}, "testimonials": {"items": [{"quote": "The demo was incredibly helpful. It showed us exactly how JeridSchool could solve our scheduling challenges.", "name": "<PERSON>", "title": "Principal, Lincoln High School"}, {"quote": "I was impressed by how intuitive the platform is. After the demo, we knew this was the solution we needed.", "name": "<PERSON>", "title": "IT Director, Westfield Academy"}, {"quote": "The personalized demo addressed all our concerns about transitioning to a new system. Implementation was smooth.", "name": "<PERSON><PERSON>", "title": "Administrator, Oakridge Elementary"}]}, "footer": {"tagline": "Transforming education management with AI-powered tools", "links": {"terms": "Terms", "privacy": "Privacy", "contact": "Contact"}, "copyright": "© {year} JeridSchool. All rights reserved."}, "errors": {"submission": "Failed to submit form. Please try again."}}, "contactUs": {"title": "Contact Us", "description": "Got a technical issue? Need details? Let us know.", "form": {"fields": {"email": {"placeholder": "Your email"}, "subject": {"placeholder": "Subject"}, "message": {"placeholder": "Your message"}}, "button": {"submit": "Send Message", "sending": "Sending..."}}, "alerts": {"success": "Message sent successfully! We'll get back to you soon.", "error": "Failed to send message. Please try again."}, "contactInfo": {"title": "Get in touch", "address": "Tunis, Tunisia", "email": "<EMAIL>", "website": "www.jeridschool.tech"}}, "chat": {"backToDashboard": "Back to Dashboard", "loading": {"title": "Loading chat...", "errors": {"password-missing": "Password information is missing. Please start over.", "password-missing-step1": "Password information is missing. Please go back to step 1 and try again.", "form-submission": "An error occurred during form submission", "captcha-required": "Please complete the captcha verification", "invalid-firstname": "First name appears to be invalid. Please go back and correct it.", "invalid-lastname": "Last name appears to be invalid. Please go back and correct it.", "invalid-email": "<PERSON><PERSON> appears to be invalid. Please go back and correct it.", "required-fields": "Please fill in all required fields", "email-invalid": "Please enter a valid email address", "password-too-short": "Password must be at least 6 characters", "password-no-uppercase": "Password must contain at least one uppercase letter", "password-no-number": "Password must contain at least one number", "password-no-special": "Password must contain at least one special character", "passwords-dont-match": "Passwords don't match"}, "timeout": {"title": "Taking longer than expected", "message": "There might be an issue connecting to the server. You can try:", "options": ["Checking your internet connection", "Refreshing the page", "Logging out and logging back in"], "refreshButton": "Refresh Page"}, "userDataError": {"title": "Unable to load user data", "message": "This could be due to:", "options": ["Your session may have expired", "There might be an issue with the server", "You might need to log in again"], "loginButton": "Go to Login"}}, "sidebar": {"userRole": "({role})", "toggleButtons": {"backToChats": "Back to Chats", "newChat": "New Chat"}, "newChatButton": {"teacher": "Start a new chat with a student", "parent": "Start a new chat with a teacher"}, "search": {"teacher": "Search for students...", "parent": "Search for teachers...", "filter": "Filter conversations..."}, "searchResults": {"searching": "Searching...", "noResults": "No results found", "emptyPrompt": {"teacher": "Search for students to start a chat", "parent": "Search for teachers to start a chat"}}, "conversations": {"empty": "No conversations yet", "you": "You: "}}, "chatArea": {"empty": {"title": "Select a conversation", "description": "Choose a conversation from the sidebar or start a new one"}, "messageInput": "Type a message...", "sending": "Sending...", "read": "Read", "error": "Error sending"}}, "root": {"navigation": {"home": "Home", "dashboard": "Dashboard", "about": "About Us"}, "auth": {"login": "<PERSON><PERSON>", "getDemo": "Get a demo", "logout": "Logout"}, "branding": {"jerid": "<PERSON><PERSON>", "school": "School", "alpha": "Alpha"}, "subdomain": {"notFound": {"title": "School Not Found", "message": "We couldn't determine the school from the subdomain."}, "loading": "Loading school information...", "error": {"title": "Error Loading School", "message": "We couldn't find the school you're looking for.", "debugInfo": {"title": "Debug Info:", "requestedSchool": "Requested school:", "subdomainAccess": "Subdomain access:"}}}}, "super-admin-folder": {"database": {"navigation": {"overview": "Overview", "adminManagement": "Admin Management", "back": "Back"}}, "etablisments": {"authRequired": {"title": "Authentication required", "message": "Please log in to manage establishments."}, "loading": "Loading establishments...", "error": {"title": "Failed to load establishments", "message": "You can still create a new establishment using the button above."}, "pageTitle": "Establishments", "createButton": "Create New Establishment", "emptyState": {"message": "No establishments found", "description": "Create your first establishment to get started"}, "form": {"create": {"title": "Create Establishment", "description": "Enter the details for the new establishment"}, "edit": {"title": "Edit Establishment", "description": "Update the establishment information"}, "fields": {"name": {"label": "Establishment Name", "placeholder": "Enter establishment name", "error": "Name is required"}, "address": {"label": "Address", "placeholder": "Enter establishment address", "error": "Address is required"}, "logo": {"label": "School Logo"}, "url": {"label": "Website URL", "placeholder": "Enter website URL"}, "cmsContent": {"label": "CMS Content"}, "description": {"label": "School Description", "placeholder": "Enter a description of your school"}, "heroImage": {"label": "Hero Image"}, "galleryImages": {"label": "Gallery Images"}, "contactEmail": {"label": "Contact Email", "placeholder": "Enter contact email"}, "contactPhone": {"label": "Contact Phone", "placeholder": "Enter contact phone"}, "socialLinks": {"label": "Social Links"}, "stats": {"label": "Statistics"}, "services": {"label": "Services", "placeholder": "Add a service"}, "isActive": {"label": "Active Status"}}, "buttons": {"cancel": "Cancel", "save": "Save", "create": "Create", "update": "Update", "saving": "Saving..."}}, "notifications": {"limitReached": {"title": "Limit Reached", "description": "You can only create one establishment"}, "userIdRequired": {"title": "Error", "description": "User ID is required to create an establishment"}, "createSuccess": {"title": "Success", "description": "Establishment created successfully"}, "createError": {"title": "Error", "description": "Failed to create establishment"}, "updateSuccess": {"title": "Success", "description": "Establishment updated successfully"}, "updateError": {"title": "Error", "description": "Failed to update establishment"}}}, "index": {"tour": {"steps": {"welcome": {"title": "Super Admin Dashboard", "content": "Welcome to the Super Admin Dashboard! This is where you can manage your school system."}, "servicesOverview": {"title": "Services Overview", "content": "This is the main dashboard where you can access various services."}, "serviceCards": {"title": "Service Cards", "content": "These cards represent different services available to you. Click on any card to access that service."}, "complete": {"title": "Tour Complete", "content": "That's it! You now know the basics of the Super Admin dashboard."}}, "buttons": {"back": "Back", "close": "Close", "last": "Finish", "next": "Next", "skip": "<PERSON><PERSON>"}, "restartButton": {"title": "Restart Tour"}}, "layout": {"navigation": {"superAdminService": "Super Admin Service", "cardManagement": "Card Management"}, "currentPage": "Super Admin Service"}}}, "teacher-folder": {"database": {"navigation": {"overview": "Overview", "myClasses": "My Classes", "myStudents": "My Students", "mySubjects": "My Subjects", "assignments": "Assignments", "back": "Back"}, "components": {"overview": "Overview", "myClasses": "My Classes Component", "myStudents": "My Students Component", "mySubjects": "My Subjects Component", "assignments": "Assignments Component"}}, "classes": {"title": "My Classes"}, "timetable-test": {"title": "Teacher Timetable Test Page"}}, "school-folder": {"$schoolName": {"loading": "Loading {{schoolName}} school data...", "notFound": {"title": "School Not Found", "message": "We couldn't find the school you're looking for.", "debugInfo": {"title": "Debug Info:", "requestedSchool": "Requested school:", "urlParameter": "URL parameter:", "subdomainAccess": "Subdomain access:", "currentHostname": "Current hostname:", "apiUrl": "API URL:", "apiEndpoint": "API Endpoint:"}, "buttons": {"retry": "Retry", "returnHome": "Return to Home"}}, "schoolNotFound": {"title": "School Not Found", "message": "We couldn't find a school with the name \"{{schoolName}}\".", "debugInfo": {"title": "Debug Info:", "requestedSchool": "Requested school:", "urlParameter": "URL parameter:", "subdomainAccess": "Subdomain access:", "currentHostname": "Current hostname:", "apiUrl": "API URL:", "apiEndpoint": "API Endpoint:"}, "buttons": {"retry": "Retry", "returnHome": "Return to Home"}}, "schoolNotActive": {"title": "School Not Active", "message": "The school \"{{schoolName}}\" is currently not active.", "debugInfo": {"title": "Debug Info:", "requestedSchool": "Requested school:", "urlParameter": "URL parameter:", "subdomainAccess": "Subdomain access:", "currentHostname": "Current hostname:", "schoolId": "School ID:", "schoolName": "School Name:", "isActive": "Is Active:"}}}}, "student-folder": {"timetable": {"title": "Student Timetable"}}, "public-folder": {"card": {"loading": "Loading...", "error": {"title": "Invalid ID Card", "defaultMessage": "This ID card could not be found or has expired.", "returnButton": "Return to Homepage"}, "card": {"header": {"official": "OFFICIAL"}, "info": {"validUntil": "Valid Until:", "notSpecified": "Not specified"}, "verification": {"button": "Verify Authenticity", "verified": "Verified <PERSON><PERSON><PERSON>c", "invalid": "Invalid Card"}, "footer": {"property": "This card is the property of {{establishment<PERSON><PERSON>}}", "returnInfo": "If found, please return to the administration office", "digitalVersion": "This is a digital version of the ID card.", "scanQR": "Scan the QR code to verify authenticity."}}}}, "ai-demo": {"title": "AI Demo - JeridSchool", "tabs": {"processing": "AI Processing", "results": "Results", "flashcards": "Flashcards", "chat": "AI Chat", "debug": "Debug"}, "processing": {"title": "AI-Powered Learning Assistant", "description": "Transform your learning experience with our advanced AI tools. Upload content, generate flashcards, and get instant help.", "features": {"youtube": {"title": "YouTube Processing", "description": "Extract key insights from educational videos"}, "upload": {"title": "File Upload", "description": "Process documents and create study materials"}, "flashcards": {"title": "Smart Flashcards", "description": "Auto-generate flashcards from any content"}, "chat": {"title": "AI Tutor", "description": "Get instant answers to your questions"}}}, "chat": {"features": {"explanations": {"title": "Detailed Explanations", "description": "Get detailed explanations of complex topics"}, "quickAnswers": {"title": "Quick Answers", "description": "Get instant responses to your questions"}}}, "sidebar": {"processingTime": {"title": "Processing Time", "estimate": "30-60s"}, "examples": {"title": "Try These Examples", "youtube": {"title": "YouTube URLs:", "description": "Educational videos, lectures, tutorials"}, "chat": {"title": "Chat Prompts:", "prompts": ["Explain quantum physics", "Create a study plan", "Help me understand calculus"]}}}}, "classes": {"title": "Classes", "welcome": "Hello \"/classes\"!"}, "profile": {"title": "Profile"}, "test": {"title": "Test Page", "welcome": "Hello \"/test\"!"}, "admin-folder": {"timetable": {"sidebar": {"back": "Back", "dashboard": "Dashboard", "timetableGenerator": "Timetable Generator", "viewGeneratedTimetable": "View Generated Timetable", "generateSessions": "Generate Sessions", "sessionHistory": "Session History", "attendanceList": "Attendance List", "createFirst": "Create First"}, "timetableLoading": "Loading Timetable...", "noTimetable": {"title": "No Timetable Available", "message": "Please generate a timetable first.", "button": "Create Timetable"}, "toasts": {"noTimetableFound": {"title": "No Timetable Found", "description": "Please generate a timetable first."}, "timetableGenerated": {"title": "Timetable Generated", "description": "Your timetable has been generated and is ready to view."}, "timetableDeleted": {"title": "Timetable Deleted", "description": "The timetable has been removed from memory."}, "noTimetableToDelete": {"title": "Error", "description": "No timetable to delete"}, "deleteError": {"title": "Error", "description": "Failed to delete the timetable. Please try again."}}}, "score": {"sampleData": {"subjects": {"math": "Math", "physics": "Physics", "chemistry": "Chemistry"}, "semesters": {"semester1": "Semester 1", "semester2": "Semester 2", "semester3": "Semester 3"}}, "sidebar": {"back": "Back", "classSelection": "Class Selection", "navigation": {"scoreBySubject": "Score by Subject", "semesterAverages": "Semester Averages"}}}, "index": {"navigation": {"adminService": "Admin Service", "classManagement": "Class Management", "teacherManagement": "Teacher Management", "studentManagement": "Student Management", "listEliminateStudent": "List Eliminate Student", "calculateStudentScore": "Calculate Student Score", "cardStudio": "Card Studio", "userManagement": "User Management"}}, "elimination": {"sidebar": {"back": "Back", "navigation": {"overview": "Overview", "users": "Users", "settings": "Settings"}}, "components": {"settings": "Settings Component"}}, "database": {"navigation": {"overview": "Overview", "users": {"title": "Users", "parentManagement": "Parent Management", "teacherManagement": "Teacher Management", "studentManagement": "Student Management", "listEliminate": "List Eliminate", "calculateScore": "Calculate Score"}, "establishment": {"title": "Establishment", "classManagement": "Class Management", "gradeManagement": "Grade Management", "classroomManagement": "Classroom Management", "subjectManagement": "Subject Management"}, "back": "Back"}}}}}