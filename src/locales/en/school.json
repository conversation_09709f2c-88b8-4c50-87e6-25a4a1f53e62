{"schoolLandingPage": {"header": {"navigation": {"home": "Home", "about": "About", "success": "Success", "gallery": "Gallery", "contact": "Contact"}}, "hero": {"welcomePrefix": "Welcome to", "defaultDescription": "A place where education meets excellence.", "buttons": {"contactUs": "Contact Us", "learnMore": "Learn More"}}, "about": {"title": "About Us", "defaultDescription": "At {schoolName}, we believe in providing a nurturing environment where students can thrive academically, socially, and emotionally. Our dedicated faculty and staff are committed to excellence in education and to helping each student reach their full potential.", "foundingVision": "Founded with a vision to create a dynamic learning community, we offer a comprehensive curriculum that prepares students for success in an ever-changing world.", "services": {"title": "Our Services"}, "whyChooseUs": {"title": "Why Choose Us?", "features": {"safeEnvironment": {"title": "Safe Environment", "description": "We prioritize student safety and well-being in all aspects of school life."}, "qualityEducation": {"title": "Quality Education", "description": "Our curriculum is designed to challenge and inspire students to excel."}, "dedicatedFaculty": {"title": "Dedicated Faculty", "description": "Our teachers are experienced professionals committed to student success."}}}}, "stats": {"title": "Our Success in Numbers", "labels": {"students": "Students", "teachers": "Teachers", "courses": "Courses", "awards": "Awards"}}, "gallery": {"title": "School Gallery", "description": "Take a visual tour of our campus, facilities, and student activities."}, "contact": {"title": "Contact Us", "description": "Have questions? We're here to help. Reach out to us using the contact information below.", "form": {"title": "Send us a message", "fields": {"name": {"label": "Your Name", "placeholder": "Enter your name"}, "email": {"label": "Email Address", "placeholder": "Enter your email"}, "subject": {"label": "Subject", "placeholder": "Subject"}, "message": {"label": "Message", "placeholder": "Your message"}}, "submitButton": "Send Message"}, "info": {"title": "Contact Information", "address": {"label": "Address"}, "phone": {"label": "Phone"}, "email": {"label": "Email"}, "followUs": "Follow Us"}, "hours": {"title": "School Hours", "weekdays": "Monday - Friday", "weekdayHours": "8:00 AM - 3:30 PM", "saturday": "Saturday", "saturdayHours": "9:00 AM - 12:00 PM", "sunday": "Sunday", "sundayClosed": "Closed"}}, "footer": {"quickLinks": {"title": "Quick Links", "home": "Home", "about": "About Us", "gallery": "Gallery", "contact": "Contact"}, "services": {"title": "Services"}, "contact": {"title": "Contact"}, "copyright": "All rights reserved."}, "accessibility": {"logo": {"alt": "{schoolName} Logo"}, "heroImage": {"alt": "{schoolName} Campus"}, "menuButton": "<PERSON><PERSON>"}, "debug": {"keyboardShortcut": "Press Ctrl+Shift+D to toggle debug information"}}, "subdomainDebug": {"title": "Subdomain Debug Info", "fields": {"hostname": "Hostname:", "isSubdomain": "Is Subdomain:", "schoolName": "School Name from Subdomain:", "fullUrl": "Full URL:", "apiBaseUrl": "API Base URL:", "authToken": "Auth <PERSON>ken:"}, "values": {"yes": "Yes", "no": "No", "available": "Available", "notAvailable": "Not available", "none": "none"}, "apiStatus": {"title": "API Endpoint Status:", "publicEndpoint": "Public Endpoint:", "protectedEndpoint": "Protected Endpoint (No Auth):", "protectedEndpointWithAuth": "Protected Endpoint (With Auth):", "states": {"notTested": "Not tested", "accessible": "Accessible", "noAuthToken": "No auth token available", "error": "Error: {status} - {message}"}}, "testing": {"publicEndpoint": "Testing public endpoint: {url}", "protectedEndpoint": "Testing protected endpoint (no auth): {url}", "protectedEndpointWithAuth": "Testing protected endpoint (with auth): {url}"}, "errors": {"publicEndpoint": "Public endpoint error: {error}", "protectedEndpoint": "Protected endpoint error: {error}", "protectedEndpointWithAuth": "Protected endpoint with auth error: {error}"}}, "subdomainTest": {"title": "Subdomain Access Test", "fields": {"hostname": "Hostname:", "isSubdomain": "Is Subdomain:", "schoolName": "School Name from Subdomain:", "fullUrl": "Full URL:"}, "values": {"yes": "Yes", "no": "No", "none": "none"}, "accessibility": {"hostnameLabel": "Current hostname of the website", "subdomainStatusLabel": "Whether the current URL is using a subdomain", "schoolNameLabel": "School name extracted from the subdomain", "fullUrlLabel": "Complete URL of the current page"}}}