{"flashcardStudy": {"studyComplete": {"title": "Study Complete!", "stats": {"totalCards": "Total Cards:", "known": "Known:", "needReview": "Need Review:"}, "buttons": {"restartAll": "Restart with All Cards", "studyUnknown": "Study Unknown Cards", "backToDeck": "Back to Deck"}}, "emptyDeck": {"title": "No Cards to Study", "message": "This deck doesn't have any flashcards yet. Add some cards to start studying.", "backButton": "Back to Deck"}, "flashcard": {"navigation": {"back": "Back to Deck", "shuffle": "Shuffle"}, "progress": {"percent": "Progress: {progress}%", "counter": "Card {current} of {total}"}, "card": {"flipHint": "Click to flip"}, "buttons": {"previous": "Previous", "dontKnow": "Don't Know", "nextCard": "Next Card", "know": "Know", "next": "Next"}}}, "flashcardGenerator": {"title": "Flashcards", "buttons": {"generateCards": "Generate Cards", "aiAssistant": "AI Assistant", "newDeck": "New Deck", "createFirstDeck": "Create Your First Deck", "edit": "Edit", "study": "Study", "delete": "Delete", "cancel": "Cancel", "create": "Create", "creating": "Creating...", "logOut": "Log Out and Refresh Session"}, "deckCreation": {"title": "New Flashcard Deck", "form": {"name": {"label": "Name", "placeholder": "Flashcard deck name"}, "visibility": {"label": "Visibility", "placeholder": "Select visibility", "options": {"private": "Private", "public": "Public"}}, "limitInfo": "You can create up to 3 decks with a maximum of 50 cards each."}}, "deckList": {"title": "My Flashcard Deck", "emptyState": {"title": "No flashcard decks yet", "description": "Create your first flashcard deck to start studying. You can add text and images to both sides of your cards."}, "deckInfo": {"cardCount": "{count} cards • Last updated {date}"}, "limitReached": "You have reached the maximum limit of 3 flashcard decks. Delete an existing deck to create a new one."}, "errors": {"authError": "Authentication error. Please log out and log back in to refresh your session.", "loadError": "Error loading flashcard decks. Please try again later.", "deleteConfirm": "Are you sure you want to delete this deck?"}}, "flashcardsService": {"title": "Flashcards", "createButton": "Create Flashcard", "form": {"title": "Create New Flashcard", "fields": {"subject": {"label": "Subject", "placeholder": "Select the subject", "required": "Please select a subject"}, "question": {"label": "Question", "placeholder": "Enter your question", "required": "Please enter a question"}, "answer": {"label": "Answer", "placeholder": "Enter the answer", "required": "Please enter an answer"}, "tags": {"label": "Tags (comma separated)", "placeholder": "e.g. math, algebra, equations"}}, "buttons": {"cancel": "Cancel", "create": "Create"}}, "tabs": {"all": "All Flashcards", "favorites": "Favorites", "recent": "Recently Reviewed", "study": "Study Mode"}, "search": {"placeholder": "Search flashcards"}, "studyMode": {"cardCount": "Card {current} of {total}", "buttons": {"favorite": "Add to Favorites", "favorited": "Favorited", "showAnswer": "Show Answer", "previous": "Previous", "next": "Next"}, "emptyState": {"description": "No flashcards found. Create some flashcards to start studying!"}}, "listMode": {"emptyState": {"description": "No flashcards found"}}, "card": {"lastReviewed": "Last reviewed: {date}"}}, "flashcardDeck": {"navigation": {"backToDecks": "Back to Decks", "studyMode": "Study Mode"}, "editDeck": {"title": "Edit Deck", "fields": {"name": "Name", "visibility": "Visibility"}, "visibilityOptions": {"private": "Private", "public": "Public"}, "instructions": "Changes are saved when you click \"Done\". Click and drag to rearrange flashcards.", "addCard": "Add Card"}, "cardList": {"title": "Flashcards ({count})", "emptyState": {"message": "No flashcards yet", "addCard": "Add Card"}, "card": {"frontPlaceholder": "Front of card", "backPlaceholder": "Back of card", "buttons": {"edit": "Edit", "delete": "Delete"}}}, "cardEditor": {"title": "Edit Card {number}", "front": {"title": "Front", "placeholder": "", "imageLabel": "Front Image", "addImage": "Add Image"}, "back": {"title": "Back", "placeholder": "", "imageLabel": "Back Image", "addImage": "Add Image"}, "buttons": {"cancel": "Cancel", "save": "Save Card", "saving": "Saving..."}}, "validation": {"cardContent": {"title": "Card Content Required", "message": "Please enter text for both the front and back of the card before saving."}}}}