{"dashboardOverview": {"title": "Vue d'Ensemble du Tableau de Bord", "recentActivities": {"title": "Activités Récentes", "description": "Dernières mises à jour de la plateforme"}, "enrollmentProgress": {"title": "Progression des Inscriptions", "description": "Année académique en cours"}, "upcomingEvents": "Événements à Venir"}, "databaseOverview": {"title": "Vue d'Ensemble de la Base de Données", "lastUpdated": "Dernière mise à jour :", "cards": {"students": "Étudiants", "teachers": "Enseignants", "parents": "Parents", "classes": "Classes", "subjects": "<PERSON><PERSON><PERSON>", "classrooms": "Salles de Classe", "admins": "Administrateurs", "totalStudents": "Total des Étudiants", "totalTeachers": "Total des Enseignants", "myClasses": "Mes Classes", "myStudents": "<PERSON>s <PERSON>", "assignments": "Devoirs", "viewDetails": "Voir les détails"}, "recentActivities": {"title": "Activités Récentes de la Base de Données", "description": "Dernières modifications des enregistrements de la base de données", "viewAll": "Voir Tout"}, "systemHealth": {"title": "État du Système", "storage": "Utilisation du Stockage", "records": "Nombre d'Enregistrements", "lastBackup": "Dernière Sauvegarde :", "nextBackup": "<PERSON><PERSON><PERSON> :", "systemStatus": "État du Système :"}, "scheduledTasks": {"title": "Tâches Programmées"}, "allActivities": {"title": "Toutes les Activités de la Base de Données", "loading": "Chargement des activités...", "noActivities": "Aucune activité trouvée."}}, "establishmentCard": {"loading": "Chargement...", "official": "OFFICIEL", "property": "Cette carte est la propriété de {establishmentName}", "returnInfo": "<PERSON> trouvée, ve<PERSON><PERSON><PERSON> la retourner au bureau administratif", "signature": "Signature", "downloadButton": "Télécharger la Carte d'Identité"}, "eventsList": {"loading": "Chargement des événements...", "error": "Échec du chargement des événements à venir", "noEvents": "Aucun événement à venir", "dateFormat": {"today": "<PERSON><PERSON><PERSON>'hui", "tomorrow": "<PERSON><PERSON><PERSON>"}, "eventTypes": {"meeting": "réunion", "exam": "examen", "activity": "activité", "holiday": "congé"}}, "fileUploader": {"label": "Télécharger une Image", "errorAlert": {"authTitle": "Erreur d'Authentification", "uploadTitle": "<PERSON><PERSON><PERSON> de Téléchargement", "troubleshooting": "Dépannage :", "verifyToken": "Vérifiez le jeton CDN dans votre fichier .env", "checkService": "Vérifiez si le service CDN est en cours d'exécution", "contactAdmin": "Contactez votre administrateur pour un jeton valide"}, "buttons": {"clear": "<PERSON><PERSON><PERSON><PERSON>", "clickToBrowse": "Cliquer pour parcourir", "retrying": "Nouvelle tentative...", "retry": "Réessayer le Téléchargement", "chooseDifferent": "<PERSON><PERSON> un Fichier Différent", "uploading": "Téléchargement...", "changeImage": "Changer l'Image", "selectImage": "Sélectionner une Image", "cancel": "Annuler"}, "status": {"uploadSuccessful": "Téléchargement réussi", "savedFallback": "Enregistré en utilisant le mode de secours"}, "Loading": {"loading": "Chargement..."}}, "paymentCheck": {"logs": {"usingStorage": "Utilisation du statut de paiement depuis localStorage : PAYÉ", "oneTimeCheck": "Vérification unique de localStorage pour le statut de paiement : {status}", "statusUpdate": "Statut de paiement mis à jour : Props={isPaid}, Effectif={effectiveIsPaid}"}, "status": {"paid": "PAYÉ", "notPaid": "NON PAYÉ"}}, "paymentRequired": {"title": "Paiement Requis", "message": {"withName": "Bonjour {student<PERSON>ame}, vos frais scolaires n'ont pas été payés.", "withoutName": "Vos frais scolaires n'ont pas été payés."}, "instruction": "Veuillez contacter un administrateur pour effectuer votre paiement et retrouver l'accès à cette page."}, "profileCard": {"fields": {"email": "Email :", "cin": "CIN :", "birthday": "Date de naissance :", "gender": "Genre :", "phone": "Téléphone :", "address": "<PERSON><PERSON><PERSON> :", "validUntil": "<PERSON><PERSON> jusqu'au :"}, "footer": {"property": "Cette carte est la propriété de {establishmentName}", "returnInfo": "<PERSON> trouvée, ve<PERSON><PERSON><PERSON> la retourner au bureau administratif", "signature": "Signature"}, "buttons": {"download": "Télécharger la Carte d'Identité", "preparing": "Préparation du PDF..."}, "official": "OFFICIEL"}, "profileEditForm": {"fields": {"firstname": {"label": "Prénom", "placeholder": "Entrez votre prénom", "error": "Le prénom doit comporter au moins 2 caractères"}, "lastname": {"label": "Nom", "placeholder": "Entrez votre nom", "error": "Le nom doit comporter au moins 2 caractères"}, "email": {"label": "Email", "placeholder": "Entrez votre email", "error": "<PERSON><PERSON><PERSON> email invalide"}, "cin": {"label": "CIN", "placeholder": "Entrez votre CIN", "error": "Le CIN doit comporter au moins 8 caractères"}, "birthday": {"label": "Date de naissance", "error": "La date de naissance est requise"}, "gender": {"label": "Genre", "placeholder": "Sélectionnez votre genre", "options": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Entrez votre adresse"}, "phone": {"label": "Numéro de téléphone", "placeholder": "Entrez votre numéro de téléphone"}, "password": {"label": "Nouveau Mot de Passe (Optionnel)", "placeholder": "Entrez un nouveau mot de passe", "error": "Le mot de passe doit comporter au moins 8 caractères"}}, "buttons": {"cancel": "Annuler", "update": "Mettre à Jour le Profil", "updating": "Mise à jour..."}}, "ScjedimeView": {"Modify": "Modifier l'Emploi du Temps"}, "scoreCalculator": {"search": {"label": "Rechercher une Matière", "placeholder": "Rechercher par nom de matière"}, "table": {"headers": {"subject": "<PERSON><PERSON>", "formula": "Formule", "grade": "Note", "actions": "Actions"}, "formulaPlaceholder": "Entrez une formule", "updateScore": "Mettre à Jour la Note"}, "pagination": {"previous": "Précédent", "next": "Suivant"}, "errors": {"invalidFormula": "Formule invalide :"}}, "studentNotes": {"table": {"headers": {"date": "Date", "subject": "<PERSON><PERSON>", "note": "Note"}}, "addNote": {"placeholder": "Ajouter une nouvelle note...", "button": "Ajouter une Note"}, "noNotes": "Aucune note disponible pour cet étudiant.", "subjects": {"general": "Général"}}, "studentScore": {"table": {"headers": {"subject": "<PERSON><PERSON>", "score": "Score", "total": "Total", "date": "Date", "actions": "Actions"}, "buttons": {"edit": "Modifier"}}, "noScores": "Aucun score disponible pour cet étudiant."}, "studentTable": {"headers": {"fullName": "Nom Co<PERSON>t", "cin": "CIN", "actions": "Actions"}, "buttons": {"profile": "Profil", "absences": "Absences", "scores": "Scores"}, "noStudents": "Aucun étudiant trouvé."}, "teacherForm": {"fields": {"cin": {"label": "CIN", "placeholder": "Entrez le CIN", "error": "Le CIN est requis"}, "firstname": {"label": "Prénom", "placeholder": "Entrez le prénom", "error": "Le prénom est requis"}, "lastname": {"label": "Nom", "placeholder": "Entrez le nom", "error": "Le nom est requis"}, "email": {"label": "Email", "placeholder": "Entrez l'email", "error": "Format d'email invalide"}, "password": {"label": "<PERSON><PERSON>", "placeholder": "Entrez le mot de passe", "error": "Le mot de passe doit comporter au moins 6 caractères"}, "title": {"label": "Titre", "placeholder": "Sélectionnez le titre", "options": {"professor": "Professeur", "dr": "Dr.", "assistantProfessor": "Professeur Assistant"}}, "gender": {"label": "Genre", "placeholder": "Sélectionnez le genre", "options": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}}, "birthday": {"label": "Date de naissance", "placeholder": "Sélectionnez la date de naissance"}, "avatar": {"label": "Avatar de l'Enseignant"}, "isActive": {"label": "Statut", "placeholder": "Sélectionnez le statut", "options": {"active": "Actif", "inactive": "Inactif"}}}, "buttons": {"cancel": "Annuler", "submit": "So<PERSON><PERSON><PERSON>"}, "errors": {"title": "<PERSON><PERSON><PERSON>"}}}