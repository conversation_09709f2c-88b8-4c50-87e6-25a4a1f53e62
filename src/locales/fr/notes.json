{"noteForm": {"backButton": "Retour", "title": {"edit": "Modifier la note", "create": "<PERSON><PERSON>er une nouvelle note"}, "fields": {"title": {"label": "Titre de la note", "placeholder": "ex. : Structure cellulaire", "error": "Le titre est requis"}, "content": {"label": "Contenu", "placeholder": "Commencez à écrire votre note...", "saveInfo": "Vos modifications ne seront enregistrées que lorsque vous cliquerez sur le bouton {actionButton}."}}, "buttons": {"cancel": "Annuler", "update": "Mettre à jour la note", "create": "<PERSON><PERSON><PERSON> la <PERSON>", "saving": "Enregistrement..."}, "toast": {"createSuccess": {"title": "Su<PERSON>ès", "description": "Note créée avec succès"}, "updateSuccess": {"title": "Su<PERSON>ès", "description": "Note mise à jour avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de {action} la note"}}}, "noteFullView": {"buttons": {"back": "Retour", "fullscreen": {"maximize": "<PERSON><PERSON><PERSON><PERSON>", "minimize": "<PERSON><PERSON><PERSON><PERSON>"}, "close": "<PERSON><PERSON><PERSON>"}, "dates": {"created": "Créée : {date}", "updated": "Mise à jour : {date}"}}, "notesCollection": {"loading": "Chargement de la collection...", "error": {"title": "Erreur de chargement de la collection", "message": "Collection introuvable", "button": "Retour aux collections"}, "header": {"back": "Retour", "addNote": "Ajouter une note"}, "info": {"visibility": {"public": "Publique", "private": "Privée"}, "topic": "Sujet : {topic}", "noteCount": "{count} notes", "created": "Créée : {date}"}, "emptyState": {"title": "Aucune note dans cette collection pour le moment", "description": "<PERSON><PERSON>ez votre première note pour commencer à organiser vos idées dans cette collection.", "button": "<PERSON><PERSON><PERSON> votre première note"}, "noteCard": {"buttons": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "dates": {"created": "Créée : {date}", "updated": "Mise à jour : {date}"}}, "limitReached": {"message": "Vous avez atteint la limite maximale de 100 notes dans cette collection. Supprimez des notes existantes pour en créer de nouvelles."}, "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette note ?", "toast": {"deleteSuccess": {"title": "Su<PERSON>ès", "description": "Note supprimée avec succès"}, "deleteError": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de la suppression de la note"}}}, "notesCollectionForm": {"header": {"back": "Retour", "edit": "Modifier la collection", "create": "C<PERSON>er une nouvelle collection"}, "fields": {"name": {"label": "Nom de la collection", "placeholder": "ex. : Notes de biologie", "error": "Le nom est requis"}, "topic": {"label": "Sujet", "placeholder": "ex. : Science, Mathématiques, Histoire", "error": "Le sujet est requis"}, "visibility": {"label": "Visibilité", "placeholder": "Sélectionner la visibilité", "options": {"private": "Privée", "public": "Publique"}, "descriptions": {"private": "Les collections privées ne sont visibles que par vous.", "public": "Les collections publiques peuvent être vues par d'autres utilisateurs."}}}, "buttons": {"cancel": "Annuler", "update": "Mettre à jour la collection", "create": "<PERSON><PERSON>er la collection", "saving": "Enregistrement..."}, "toast": {"createSuccess": {"title": "Su<PERSON>ès", "description": "Collection créée avec succès"}, "updateSuccess": {"title": "Su<PERSON>ès", "description": "Collection mise à jour avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de {action} la collection"}}}, "notesCollectionList": {"header": {"title": "Mes collections de notes", "stats": "{totalCount} collection{plural} • {shownCount} affichée(s)", "newButton": "Nouvelle collection"}, "search": {"placeholder": "Rechercher des collections par nom ou sujet..."}, "filters": {"toggle": {"show": "Afficher les filtres", "hide": "Masquer les filtres"}, "visibility": {"label": "Visibilité", "options": {"all": "Toutes", "public": "Publiques", "private": "Privées"}}, "topic": {"label": "Sujet", "allTopics": "Tous les sujets"}}, "loading": "Chargement des collections...", "error": {"title": "Erreur lors du chargement des collections", "unknown": "Une erreur inconnue est survenue", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>"}, "emptyState": {"title": "Aucune collection de notes pour l’instant", "description": "C<PERSON>ez votre première collection de notes pour commencer à organiser vos idées. Vous pouvez y ajouter du texte et les classer par sujets.", "button": "<PERSON><PERSON>er votre première collection"}, "noResults": {"message": "Aucune collection ne correspond à vos critères de recherche", "clearFilters": "Réinitialiser les filtres"}, "collectionCard": {"noteCount": "notes • Mis à jour", "visibility": {"public": "Publique", "private": "Privée"}, "buttons": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette collection ? Toutes les notes de cette collection seront supprimées définitivement.", "toast": {"deleteSuccess": {"title": "Su<PERSON>ès", "description": "Collection supprimée avec succès"}, "deleteError": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de la suppression de la collection"}}}, "simpleRichEditor": {"placeholder": "Commencez à écrire votre note...", "buttons": {"bold": "Gras", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "heading1": "Titre 1", "heading2": "Titre 2", "heading3": "Titre 3", "unorderedList": "Liste à puces", "orderedList": "Liste numérotée", "blockquote": "Citation", "codeBlock": "Bloc de code"}}, "tiptapEditor": {"placeholder": "Commencez à écrire votre note...", "buttons": {"formatting": {"bold": "Gras", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "highlight": "<PERSON><PERSON><PERSON><PERSON>"}, "color": {"title": "Couleur du texte", "colors": {"black": "Noir", "red": "Rouge", "orange": "Orange", "yellow": "Jaune", "green": "<PERSON>ert", "blue": "Bleu", "purple": "Violet", "pink": "<PERSON>"}}, "headings": {"heading1": "Titre 1", "heading2": "Titre 2", "heading3": "Titre 3"}, "lists": {"bulletList": "Liste à puces", "orderedList": "Liste numérotée"}, "blocks": {"blockquote": "Citation", "codeBlock": "Bloc de code"}, "media": {"link": {"button": "<PERSON><PERSON>", "placeholder": "https://exemple.com", "add": "Ajouter"}, "image": {"button": "Image", "placeholder": "https://exemple.com/image.jpg", "add": "Ajouter"}}, "history": {"undo": "Annuler", "redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accessibility": {"srOnly": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}}}