{"profileForm": {"title": "Informations du Profil", "loading": "Chargement des données du profil...", "fields": {"firstName": "Prénom", "lastName": "Nom", "email": "Email", "cin": "CIN", "birthday": "Date de Naissance", "gender": "Genre", "address": "<PERSON><PERSON><PERSON>", "phone": "Numéro de Téléphone", "password": "Nouveau Mot de Passe (Optionnel)"}, "placeholders": {"firstName": "Entrez votre prénom", "lastName": "Entrez votre nom", "email": "Entrez votre email", "cin": "Entrez votre CIN", "birthday": "Sélectionnez votre date de naissance", "address": "Entrez votre adresse", "phone": "Entrez votre numéro de téléphone", "password": "Entrez un nouveau mot de passe"}, "genderOptions": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}, "buttons": {"cancel": "Annuler", "update": "Mettre à Jour le Profil", "updating": "Mise à jour en cours..."}, "notifications": {"success": "Profil mis à jour avec succès", "error": "Échec de la mise à jour du profil", "loadError": "Échec du chargement des données du profil"}}, "profile": {"title": "Profil", "editButton": "Modifier le Profil", "loading": "Chargement des données du profil...", "error": "Erreur : {{message}}", "noData": "<PERSON><PERSON><PERSON> donnée utilisateur trouvée", "paymentRequired": {"title": "Paiement Requis", "message": "Vos frais de scolarité n'ont pas été payés. Veuillez contacter un administrateur pour finaliser votre paiement et retrouver l'accès à votre profil."}}, "footer": {"description": "JeridSchool est une plateforme leader pour la gestion moderne de l'éducation, offrant des outils innovants pour rationaliser les processus académiques, améliorer la communication et accroître l'efficacité globale des écoles et institutions éducatives.", "quickLinks": {"title": "Liens Rapides", "home": "Accueil", "bookDemo": "Réserver une Démo", "joinNow": "Rejoindre Maintenant", "features": "Fonctionnalités"}, "contactUs": {"title": "Contactez-Nous", "location": "Tu<PERSON>, Tunisie", "email": "<EMAIL>", "website": "www.jeridschool.tech"}, "copyright": "© {{year}} JeridSchool. Tous droits réservés."}, "focusRoom": {"tasks": {"title": "Tâches", "addPlaceholder": "Ajouter une tâche", "addButton": "Ajouter", "noTasks": "Aucune tâche pour le moment. Ajoutez-en une ci-dessus."}, "workTimer": {"title": "Minuteur de Travail", "startButton": "<PERSON><PERSON><PERSON><PERSON>", "pauseButton": "Pause", "resetButton": "Réinitialiser"}, "restTimer": {"title": "<PERSON><PERSON><PERSON> <PERSON> Pause", "startButton": "<PERSON><PERSON><PERSON><PERSON>", "pauseButton": "Pause", "resetButton": "Réinitialiser"}, "accessibility": {"timerEdit": "Double-cliquez pour modifier la durée du minuteur", "taskComplete": "Tâche terminée", "taskIncomplete": "Tâche incomplète", "deleteTask": "Supp<PERSON>er la tâche"}}, "about": {"title": "Renforcer", "subtitle": "les Écoles", "description": "avec des Solutions de Gestion Complètes et Abordables", "intro": "Notre système de gestion scolaire riche en fonctionnalités et abordable, avec un support client hautement noté, donne aux petites et moyennes écoles privées K-12 le pouvoir de rationaliser les opérations quotidiennes, d'améliorer les rapports et de transformer la communication entre les éducateurs, les parents et les élèves.", "features": [{"title": "Génération d'Emploi du Temps par IA", "description": "Nos algorithmes avancés d'IA créent des emplois du temps sans conflit en quelques secondes, économisant des heures de travail manuel aux administrateurs et garantissant une allocation optimale des ressources pour votre école."}, {"title": "Outils d'Apprentissage Complets", "description": "Donnez aux étudiants des flashcards, quiz et supports d'étude générés par IA qui s'adaptent aux styles d'apprentissage individuels et aident à améliorer les performances académiques."}, {"title": "Communication Transparente", "description": "Connectez les enseignants, étudiants et parents via notre système de messagerie intégré, assurant que tout le monde reste informé des devoirs, événements et progrès des étudiants."}, {"title": "Analyses et Rapports Détaillés", "description": "Obtenez des insights précieux sur les performances des étudiants, les modèles de présence et l'utilisation des ressources avec notre tableau de bord d'analyses complet."}, {"title": "Accessibilité via le Cloud", "description": "Accédez à votre système de gestion scolaire depuis n'importe où, sur n'importe quel appareil, avec notre plateforme cloud sécurisée qui garantit que vos données sont toujours disponibles quand vous en avez besoin."}], "viewFeature": "Voir", "accessibility": {"backgroundImage": "Image de fond de la gestion scolaire", "featureSection": "Section des fonctionnalités"}}, "notfound-folder": {"notfound": {"glitchySearch": {"placeholder": "Rechercher la page perdue..."}}, "glitch404": {"title": "404 Non Trouvé"}, "index": {"title": "404", "description": "Oups ! La page que vous recherchez a disparu.", "debugInfo": {"title": "Informations de Débogage :", "requestedPath": "Chemin demandé :", "hostname": "Nom d'hôte :"}, "buttons": {"goBack": "Retour", "home": "Ramenez-moi à la maison !"}}}, "debug-folder": {"tokenDebugger": {"title": "Débogueur de Jeton JWT", "error": {"title": "<PERSON><PERSON><PERSON>", "invalidToken": "Format de jeton invalide", "decodeFailure": "Échec du décodage du jeton : {{message}}", "emptyToken": "Veuillez entrer un jeton à tester"}, "currentToken": {"title": "Jeton JWT Actuel", "fromLocalStorage": "Depuis localStorage", "copyToken": "<PERSON><PERSON><PERSON>", "noToken": "Aucun jeton trouvé dans localStorage"}, "customToken": {"title": "Tester un <PERSON><PERSON>", "placeholder": "Collez un jeton JWT ici pour le décoder et le tester", "testButton": "Tester <PERSON>", "valid": "Le jeton est valide et correctement formaté.", "invalid": "Le jeton est invalide ou mal formaté."}, "tokenHeader": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> du <PERSON>"}, "tokenPayload": {"title": "Contenu du Jeton", "timing": {"title": "Informations Temporelles du Jeton", "issuedAt": "Émis à :", "expiresAt": "Expire à :", "status": "Statut :", "valid": "Valide (Non Expiré)", "expired": "Expiré"}}, "userProfile": {"title": "Profil <PERSON>"}, "localStorage": {"title": "Données LocalStorage", "columns": {"key": "Clé", "value": "<PERSON><PERSON>", "actions": "Actions"}, "tokenHidden": "[JETON MASQUÉ]", "imageData": "[Données d'Image en Base64]", "copy": "<PERSON><PERSON><PERSON>"}}}}