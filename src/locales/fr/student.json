{"viewAbsent": {"title": "<PERSON> Do<PERSON> d'Absences", "studentInfo": {"name": "Nom de l'étudiant : {fullname}", "id": "ID de l'étudiant : {id}"}, "buttons": {"requestAbsence": "Demander une absence"}, "table": {"headers": {"subject": "<PERSON><PERSON>", "totalAbsences": "Total des absences", "action": "Action"}, "absenceCount": "{count} cours", "viewDetails": "Voir les détails"}, "detailsDialog": {"title": "<PERSON><PERSON><PERSON> des absences de {subject}", "headers": {"date": "Date", "reason": "<PERSON>son"}}, "requestDialog": {"title": "Demander une absence", "form": {"date": "Date", "reason": "<PERSON>son", "file": "<PERSON><PERSON><PERSON>"}, "buttons": {"submit": "Soumettre la demande"}, "success": "Demande d'absence soumise avec succès ! En attente d'approbation."}}, "timetable": {"loading": "Chargement de l'emploi du temps...", "errors": {"studentInfo": "Échec du chargement des informations de l'étudiant.", "noClass": "Aucune classe trouvée pour cet étudiant.", "studentLoad": "Échec du chargement des informations de l'étudiant.", "timetableLoad": "Échec du chargement de l'emploi du temps."}, "title": "Emploi du temps de {className}", "buttons": {"back": "Retour"}, "table": {"headers": {"day": "Jour", "time": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "teacher": "Enseignant", "room": "Salle"}, "noLessons": "Aucun cours"}}, "studentTimetable": {"loading": "Chargement de l'emploi du temps...", "errors": {"studentInfo": "Échec du chargement des informations de l'étudiant.", "timetableLoad": "Échec du chargement de l'emploi du temps. Veuillez réessayer plus tard."}, "noData": {"timetable": "Aucune donnée d'emploi du temps disponible. Veuillez contacter votre administrateur.", "schedule": "Aucune donnée de planning trouvée dans votre emploi du temps. Veuillez contacter votre administrateur."}, "title": "Emploi du temps de {className}", "buttons": {"back": "Retour"}, "table": {"headers": {"day": "Jour", "time": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "teacher": "Enseignant", "room": "Salle"}, "noLessons": "Aucun cours"}}, "studentService": {"title": "Services", "cards": {"myHomework": "Mes Devoirs", "aiAssistant": "Assistant IA"}, "labels": {"soon": "Bientôt"}, "motivationalQuote": "Le succès est la somme de petits efforts répétés chaque jour. Soyez présent, même quand c'est difficile."}, "studentLayout": {"navigation": {"chat": "Discussion", "attendance": "Présence", "myAttendance": "<PERSON>", "viewAbsent": "Voir les Absences", "scheduleView": "Vue de l'emploi du temps", "myNotes": "Mes Notes", "myHomework": "Mes Devoirs", "aiAssistant": "Assistant IA", "flashcards": "Flashcards (<PERSON><PERSON> direct)"}, "loading": "Chargement des informations de l'étudiant...", "labels": {"soon": "Bientôt"}}, "studentHeader": {"title": {"prefix": "Étudiant", "suffix": "Tableau de bord"}}, "studentAttendanceView": {"classInfo": {"title": "Informations sur ma classe", "description": "Détails sur votre classe et vos enseignants", "class": "Classe", "grade": "Niveau", "subjects": "Mes matières et enseignants", "noClass": "Non affecté à une classe", "noGrade": "Non disponible", "noSubjects": "Aucune information sur les matières ou les enseignants disponible", "teacher": "Enseignant : {name}"}, "attendance": {"title": "Mes Dossiers de Présence", "description": "Consultez vos présences et statistiques", "dateRanges": {"thisMonth": "Ce mois-ci", "last7Days": "7 derniers jours", "last30Days": "30 derniers jours", "last90Days": "90 derniers jours"}, "stats": {"totalSessions": "Nombre total de sessions", "present": "Présent", "absent": "Absent", "attendanceRate": "<PERSON>x de présence"}, "tabs": {"all": "Tous", "present": "Présent", "absent": "Absent", "leftEarly": "Parti en avance"}, "table": {"headers": {"date": "Date", "day": "Jour", "time": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "class": "Classe", "status": "Statut", "actions": "Actions"}, "view": "Voir"}, "noRecords": {"title": "Aucun dossier de présence", "description": "Aucun dossier trouvé pour les filtres sélectionnés."}, "error": {"title": "Erreur de chargement des données", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>"}}, "dialog": {"title": "<PERSON>é<PERSON> du dossier de présence", "fields": {"date": "Date", "day": "Jour", "timeSlot": "Créneau horaire", "status": "Statut", "class": "Classe", "subject": "<PERSON><PERSON>", "notes": "Notes / Raison"}}, "status": {"present": "Présent", "absent": "Absent", "leftEarly": "Parti en avance"}, "errors": {"studentId": "ID de l'étudiant introuvable. Veuillez vous reconnecter.", "studentData": "Impossible de récupérer les données de l'étudiant. Certaines informations peuvent être manquantes.", "attendanceRecords": "Impossible de récupérer les dossiers de présence. Veuillez réessayer plus tard."}}, "homework": {"title": "Mes Devoirs", "subjectFilter": {"allSubjects": "Toutes les matières"}, "noHomework": {"title": "Aucun devoir", "message": "Aucun devoir trouvé pour cette matière."}, "homeworkCard": {"teacher": "Enseignant : {name}", "dueDate": "Date limite : {date}", "acceptedFormats": "Formats acceptés : {formats}", "status": {"pending": "En attente", "submitted": "<PERSON><PERSON><PERSON>", "late": "En retard"}}, "fileUpload": {"submitting": "Soumission en cours..."}, "buttons": {"submit": "So<PERSON><PERSON><PERSON>"}, "successMessage": "Devoir soumis avec succès"}}