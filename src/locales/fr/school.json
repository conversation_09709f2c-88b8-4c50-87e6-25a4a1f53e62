{"schoolLandingPage": {"header": {"navigation": {"home": "Accueil", "about": "À propos", "success": "Su<PERSON>ès", "gallery": "Galerie", "contact": "Contact"}}, "hero": {"welcomePrefix": "Bienvenue à", "defaultDescription": "Un lieu où l'éducation rencontre l'excellence.", "buttons": {"contactUs": "Nous contacter", "learnMore": "En savoir plus"}}, "about": {"title": "À propos de nous", "defaultDescription": "À {schoolName}, nous croyons en un environnement bienveillant où les élèves peuvent s'épanouir sur les plans académique, social et émotionnel. Notre personnel enseignant et administratif dévoué s'engage à offrir une éducation d'excellence et à aider chaque élève à atteindre son plein potentiel.", "foundingVision": "Fondée avec la vision de créer une communauté d'apprentissage dynamique, nous proposons un programme complet qui prépare les élèves à réussir dans un monde en constante évolution.", "services": {"title": "Nos services"}, "whyChooseUs": {"title": "Pourquoi nous choisir ?", "features": {"safeEnvironment": {"title": "Environnement sûr", "description": "Nous accordons la priorité à la sécurité et au bien-être des élèves dans tous les aspects de la vie scolaire."}, "qualityEducation": {"title": "Éducation de qualité", "description": "Notre programme est conçu pour stimuler et inspirer les élèves à exceller."}, "dedicatedFaculty": {"title": "Corps enseignant dévoué", "description": "Nos enseignants sont des professionnels expérimentés engagés dans la réussite des élèves."}}}}, "stats": {"title": "Notre réussite en chiffres", "labels": {"students": "<PERSON><PERSON><PERSON><PERSON>", "teachers": "Enseignants", "courses": "Cours", "awards": "Récompenses"}}, "gallery": {"title": "Galerie de l'école", "description": "Faites une visite visuelle de notre campus, de nos installations et des activités des élèves."}, "contact": {"title": "Nous contacter", "description": "Des questions ? Nous sommes là pour vous aider. Contactez-nous via les informations ci-dessous.", "form": {"title": "Envoyez-nous un message", "fields": {"name": {"label": "Votre nom", "placeholder": "Entrez votre nom"}, "email": {"label": "<PERSON><PERSON><PERSON> email", "placeholder": "Entrez votre email"}, "subject": {"label": "Sujet", "placeholder": "Sujet"}, "message": {"label": "Message", "placeholder": "Votre message"}}, "submitButton": "Envoyer le message"}, "info": {"title": "Informations de contact", "address": {"label": "<PERSON><PERSON><PERSON>"}, "phone": {"label": "Téléphone"}, "email": {"label": "Email"}, "followUs": "Suivez-nous"}, "hours": {"title": "Heures d'ouverture de l'école", "weekdays": "Lund<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "weekdayHours": "8h00 - 15h30", "saturday": "<PERSON><PERSON>", "saturdayHours": "9h00 - 12h00", "sunday": "<PERSON><PERSON><PERSON>", "sundayClosed": "<PERSON><PERSON><PERSON>"}}, "footer": {"quickLinks": {"title": "Liens rapides", "home": "Accueil", "about": "À propos de nous", "gallery": "Galerie", "contact": "Contact"}, "services": {"title": "Services"}, "contact": {"title": "Contact"}, "copyright": "Tous droits réservés."}, "accessibility": {"logo": {"alt": "Logo de {schoolName}"}, "heroImage": {"alt": "Campus de {schoolName}"}, "menuButton": "<PERSON><PERSON>"}, "debug": {"keyboardShortcut": "Appuyez sur Ctrl+Maj+D pour basculer les informations de débogage"}}, "subdomainDebug": {"title": "Infos de débogage du sous-domaine", "fields": {"hostname": "Nom d'hôte :", "isSubdomain": "Est un sous-domaine :", "schoolName": "Nom de l'école à partir du sous-domaine :", "fullUrl": "URL complète :", "apiBaseUrl": "URL de base de l'API :", "authToken": "Jeton d'authentification :"}, "values": {"yes": "O<PERSON>", "no": "Non", "available": "Disponible", "notAvailable": "Non disponible", "none": "aucun"}, "apiStatus": {"title": "Statut du point de terminaison de l'API :", "publicEndpoint": "Point de terminaison public :", "protectedEndpoint": "Point de terminaison protégé (sans auth) :", "protectedEndpointWithAuth": "Point de terminaison protégé (avec auth) :", "states": {"notTested": "Non testé", "accessible": "Accessible", "noAuthToken": "Aucun jeton d'authentification disponible", "error": "Erreur : {status} - {message}"}}, "testing": {"publicEndpoint": "Test du point de terminaison public : {url}", "protectedEndpoint": "Test du point de terminaison protégé (sans auth) : {url}", "protectedEndpointWithAuth": "Test du point de terminaison protégé (avec auth) : {url}"}, "errors": {"publicEndpoint": "Erreur du point de terminaison public : {error}", "protectedEndpoint": "Erreur du point de terminaison protégé : {error}", "protectedEndpointWithAuth": "Erreur du point de terminaison protégé avec auth : {error}"}}, "subdomainTest": {"title": "Test d'accès au sous-domaine", "fields": {"hostname": "Nom d'hôte :", "isSubdomain": "Est un sous-domaine :", "schoolName": "Nom de l'école à partir du sous-domaine :", "fullUrl": "URL complète :"}, "values": {"yes": "O<PERSON>", "no": "Non", "none": "aucun"}, "accessibility": {"hostnameLabel": "Nom d'hôte actuel du site web", "subdomainStatusLabel": "Si l'URL actuelle utilise un sous-domaine", "schoolNameLabel": "Nom de l'école extrait du sous-domaine", "fullUrlLabel": "URL complète de la page actuelle"}}}