{"establishmentTour": {"steps": {"welcome": {"title": "Page des Établissements", "content": "Bienvenue sur la page des établissements. I<PERSON>, vous pouvez gérer vos établissements scolaires."}, "createButton": {"title": "C<PERSON>er un Établissement", "content": "Cliquez ici pour créer un nouvel établissement. Cela est nécessaire avant d'accéder aux autres services."}, "formOverview": {"title": "Aperçu du Formulaire d'Établissement", "content": "Ce formulaire vous permet de saisir tous les détails de votre nouvel établissement. Parcourons chaque champ."}, "name": {"title": "Nom de l'Établissement", "content": "Saisissez ici le nom de votre établissement. Ce champ est obligatoire et sera affiché en évidence sur la page de votre école."}, "address": {"title": "Adresse de l'Établissement", "content": "Saisissez ici l'adresse physique de votre établissement. <PERSON><PERSON> aide les élèves et les parents à localiser votre école."}, "logo": {"title": "Logo de l'École", "content": "Téléchargez ici le logo de votre école. Cliquez sur « Sélectionner une image » pour choisir un fichier depuis votre ordinateur."}, "url": {"title": "URL du Site Web", "content": "Si votre école possède un site web, saisissez l'URL ici. Cela permettra aux élèves et aux parents de le visiter directement."}, "cmsContent": {"title": "Contenu CMS", "content": "Sélectionnez un modèle pour le système de gestion de contenu de votre école. <PERSON><PERSON> d<PERSON><PERSON><PERSON>ra la mise en page de la page de votre école."}, "description": {"title": "Description de l'École", "content": "R<PERSON><PERSON><PERSON>z ici une description détaillée de votre école. <PERSON><PERSON> aidera les parents et les élèves à comprendre ce qui rend votre école spéciale."}, "heroImage": {"title": "Image Principale", "content": "Téléchargez une image principale pour votre école. Cette grande bannière apparaîtra en haut de la page de votre école."}, "contactEmail": {"title": "<PERSON><PERSON> de Contact", "content": "Saisissez l'email de contact principal de votre école. Il sera utilisé pour les demandes des parents et des élèves."}, "contactPhone": {"title": "Téléphone de Contact", "content": "Saisis<PERSON>z le numéro de téléphone principal de votre école. Il sera affiché sur la page de votre école."}, "socialLinks": {"title": "<PERSON><PERSON>", "content": "Ajoutez des liens vers les profils sociaux de votre école. <PERSON><PERSON> aide les parents et les élèves à se connecter avec votre école en ligne."}, "services": {"title": "Services", "content": "Ajoutez les services que votre école propose. Saisissez un service et cliquez sur le bouton plus pour l'ajouter à la liste."}, "activeStatus": {"title": "Statut Actif", "content": "<PERSON>z ou désactivez la visibilité de votre établissement. Les établissements actifs sont visibles par les utilisateurs."}, "saveButton": {"title": "Enregistrer l'Établissement", "content": "Après avoir rempli le formulaire, cliquez ici pour enregistrer votre établissement. Vous pourrez modifier ces détails plus tard."}}, "buttons": {"back": "Retour", "close": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "next": "Suivant", "skip": "Passer"}, "logs": {"starting": "EstablishmentTour : Démarrage du tour à partir de l'événement", "startingZustand": "EstablishmentTour : Démarrage du tour via Zustand", "startingDelay": "EstablishmentTour : Démarrage du tour via Zustand après un délai", "formPage": "EstablishmentTour : D<PERSON><PERSON><PERSON> sur la page du formulaire, définition de l'étape initiale à 2", "checkingStatus": "EstablishmentTour : Vérification du statut du tour", "autoStarting": "EstablishmentTour : Démarrage automatique du tour", "tourStarted": "EstablishmentTour : Tour démarré", "tourEnded": "EstablishmentTour : Tour terminé avec le statut :", "tourCompleted": "EstablishmentTour : Tour complété", "tourSkipped": "EstablishmentTour : Tour passé", "stepShown": "EstablishmentTour : L'étape {current}/{total} a été affichée", "stepAboutToShow": "EstablishmentTour : L'étape {current}/{total} est sur le point d'être affichée", "afterCreateStep": "EstablishmentTour : <PERSON><PERSON> l'étape de création d'établissement", "foundCreateButton": "EstablishmentTour : Bouton de création trouvé, clic en cours", "foundCreateButtonByText": "EstablishmentTour : Bouton de création trouvé par le texte, clic en cours", "alreadyOnFormPage": "EstablishmentTour : <PERSON><PERSON><PERSON><PERSON> sur la page du formulaire, poursuite du tour", "focusingField": "EstablishmentTour : Focus sur le champ {selector}", "updatingStep": "EstablishmentTour : Mise à jour de l'étape Zustand à {step}", "targetNotFound": "EstablishmentTour : É<PERSON>ment cible \"{target}\" introuvable", "attemptingRecover": "EstablishmentTour : Tentative de récupération en passant à l'étape suivante", "atLastStep": "EstablishmentTour : <PERSON><PERSON><PERSON> é<PERSON> avec erreur, fin du tour"}}, "superAdminManagement": {"pageTitle": "Tableau des Admins", "buttons": {"addAdmin": "Ajouter un Nouvel Admin", "profile": "Profil", "activate": "Activer", "deactivate": "Désactiver"}, "search": {"placeholder": "Rechercher des admins par nom ou CIN..."}, "filters": {"title": "Filtres", "name": "Nom", "cin": "CIN", "all": "Tous", "clearAll": "Efface<PERSON> <PERSON> les Filtres", "activeFilters": "Filtres Actifs :"}, "table": {"headers": {"admin": "Admin", "cin": "CIN", "status": "Statut", "actions": "Actions"}, "status": {"active": "Actif", "inactive": "Inactif"}}, "pagination": {"adminsCount": "{count} <PERSON><PERSON>", "page": "Page {current} sur {total}"}, "addAdminModal": {"title": "Ajouter un Nouvel Admin", "fields": {"firstname": {"label": "Prénom", "placeholder": "<PERSON><PERSON> le prénom"}, "lastname": {"label": "Nom", "placeholder": "<PERSON><PERSON> le nom"}, "email": {"label": "Email", "placeholder": "<PERSON><PERSON> l'adresse email"}, "password": {"label": "Mot de passe", "placeholder": "<PERSON><PERSON> le mot de passe"}, "cin": {"label": "CIN", "placeholder": "Saisir le CIN"}, "birthday": {"label": "Date de naissance"}, "gender": {"label": "<PERSON>e", "options": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON> l'adresse"}, "phone": {"label": "Numéro de téléphone", "placeholder": "Sai<PERSON> le numéro de téléphone"}, "avatar": {"label": "Avatar (Obligatoire)", "note": "Remarque : L'image de l'avatar est obligatoire et doit être téléchargée pour s'afficher correctement."}}, "buttons": {"cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>"}}, "profileModal": {"title": "<PERSON>il <PERSON> l'Admin", "fields": {"firstname": {"label": "Prénom"}, "lastname": {"label": "Nom"}, "email": {"label": "Email"}, "cin": {"label": "CIN"}, "birthday": {"label": "Date de naissance"}, "gender": {"label": "<PERSON>e", "options": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON> l'adresse"}, "phone": {"label": "Numéro de téléphone", "placeholder": "Sai<PERSON> le numéro de téléphone"}, "status": {"label": "Statut", "options": {"active": "Actif", "inactive": "Inactif"}}, "password": {"label": "Mot de passe (Optionnel)", "placeholder": "Laisser vide pour conserver le mot de passe actuel"}, "avatar": {"label": "Avatar (Optionnel)"}}, "buttons": {"cancel": "Annuler", "update": "Mettre à jour Admin"}}, "deleteDialog": {"title": "Êtes-vous absolument sûr ?", "description": "Cette action est irréversible. Cela supprimera définitivement l'admin {adminName} et ses données du serveur.", "buttons": {"cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "notifications": {"adminCreated": {"title": "<PERSON><PERSON>", "description": "Le nouvel admin a été créé avec succès."}, "adminUpdated": {"title": "Admin Mis à Jour", "description": "Le profil de l'admin a été mis à jour avec succès."}, "adminDeleted": {"title": "<PERSON><PERSON>", "description": "L'admin a été supprimé avec succès."}, "statusUpdated": {"title": "Statut Mis à Jour", "description": "L'admin a été {status}.", "activated": "activé", "deactivated": "désactiv<PERSON>"}, "statusUpdateFailed": {"title": "Échec de la Mise à Jour du Statut", "description": "Échec de la mise à jour du statut de l'admin. Veuillez réessayer."}, "deletionFailed": {"title": "Échec de la Suppression", "description": "Échec de la suppression de l'admin. Veuillez réessayer."}, "creationFailed": {"title": "Échec de la Création de l'Admin", "description": "{errorMessage}"}, "updateFailed": {"title": "Échec de la Mise à Jour de l'Admin", "description": "{errorMessage}"}, "error": {"title": "<PERSON><PERSON><PERSON>", "description": "Une erreur s'est produite lors de la création de l'admin. Veuillez réessayer."}}}}