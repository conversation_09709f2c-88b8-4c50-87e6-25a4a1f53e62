{"userManagement": {"title": "Entity Manager", "entitySelector": {"label": "Select entity type", "options": {"subject": "Subject", "class": "Class", "teacher": "Teacher", "student": "Student", "parent": "Parent", "seance": "<PERSON><PERSON>", "classroom": "Classroom"}}, "form": {"submitButton": "Add {entity}"}, "messages": {"success": "Successfully added {entity}", "error": "Error adding {entity}"}}, "superAdminService": {"alerts": {"noEstablishment": {"title": "Important!", "description": "You must create an establishment before accessing other services. Please click on the Establishments card below to create your first establishment."}}, "header": {"title": "Services", "tourButton": "Take Tour"}, "services": {"dataStorage": "Data Storage", "establishments": "Establishments", "invoice": "Invoice", "smartRapport": "Smart Rapport", "addService": "Add Service"}, "labels": {"soon": "SOON", "locked": "LOCKED"}, "adminManagement": {"title": "Admin Management", "description": "Manage all admin users from the Data Storage section.", "button": "Go to Admin Management", "requiresEstablishment": "You must create an establishment first"}}, "adminService": {"header": {"title": "Services"}, "services": {"dataStorage": "Data Storage", "timeTable": "TimeTable", "elimination": "Elimination", "score": "Score", "addService": "Add Service"}, "labels": {"soon": "Soon"}, "quickStats": {"title": "Quick Stats"}, "quickActions": {"title": "Quick Actions", "buttons": {"addStudent": "Add New Student", "addTeacher": "Add New Teacher", "generateReport": "Generate Report"}}, "tour": {"trigger": {"checkingMessage": "Checking if tour should be triggered", "settingFlag": "Setting trigger flag for tour"}}}, "adminHeader": {"logo": {"prefix": "<PERSON><PERSON>", "suffix": "School"}, "navigation": {"adminService": "AdminService", "tableView": "TableView", "classManagement": "ClassManagement", "teacherManagement": "TeacherManagement", "studentManagement": "StudentManagement", "userManagement": "UserMangment", "addParent": "addParent"}, "buttons": {"logout": "Logout"}, "welcome": {"title": "Welcome to School Management System"}}, "addClass": {"title": "Add Class", "form": {"fields": {"nameClass": {"label": "Class Name", "placeholder": "Class Name", "validation": "Class Name is required"}, "gradeId": {"label": "Grade ID", "placeholder": "Grade ID", "validation": "Grade ID is required"}, "supervisorId": {"label": "Supervisor ID", "placeholder": "Supervisor ID", "validation": "Supervisor ID is required"}}, "buttons": {"submit": "Create Class", "submitting": "Creating..."}}, "messages": {"success": "Class created successfully!", "error": "Error creating class: {message}"}}, "User-folder": {"teacher": {"form": {"fields": {"cin": {"label": "CIN", "placeholder": "Enter CIN", "validation": {"required": "CIN is required", "minLength": "CIN must be at least 8 characters"}}, "firstname": {"label": "First Name", "placeholder": "First Name", "validation": {"required": "First name is required"}}, "lastname": {"label": "Last Name", "placeholder": "Last Name", "validation": {"required": "Last name is required"}}, "email": {"label": "Email", "placeholder": "Email", "validation": {"required": "Email is required", "invalid": "Invalid email address"}}, "password": {"label": "Password", "placeholder": "Password", "validation": {"required": "Password is required", "minLength": "Password must be at least 6 characters"}}, "title": {"label": "Title", "placeholder": "Title", "validation": {"required": "Title is required"}}, "subjectIds": {"label": "Select Subjects", "validation": {"required": "Please select at least one subject"}}}, "buttons": {"submit": "Submit"}, "loading": "Loading subjects...", "error": "Failed to load subjects"}, "messages": {"success": "Teacher added successfully!", "error": "Failed to add teacher. Please try again."}}, "student": {"form": {"fields": {"cin": {"label": "CIN", "placeholder": "CIN", "validation": {"required": "CIN is required", "minLength": "CIN must be at least 8 characters"}}, "firstname": {"label": "First Name", "placeholder": "First Name", "validation": {"required": "First name is required"}}, "lastname": {"label": "Last Name", "placeholder": "Last Name", "validation": {"required": "Last name is required"}}, "email": {"label": "Email", "placeholder": "Email", "validation": {"required": "Email is required", "invalid": "Invalid email address"}}, "password": {"label": "Password", "placeholder": "Password", "validation": {"required": "Password is required", "minLength": "Password must be at least 6 characters"}}, "gradeLevel": {"label": "Grade Level", "placeholder": "Grade Level", "validation": {"required": "Grade Level is required"}}, "enrolledDate": {"label": "Enrolled Date", "placeholder": "Enrolled Date", "title": "Enrolled Date:", "validation": {"required": "Enrolled Date is required"}}, "parentId": {"label": "Parent ID", "placeholder": "Parent ID", "validation": {"required": "Parent ID is required"}}, "classId": {"label": "Class ID", "placeholder": "Class ID", "validation": {"required": "Class ID is required"}}}, "buttons": {"submit": "Submit"}}}, "seance": {"form": {"fields": {"day": {"placeholder": "Day"}, "time": {"placeholder": "Time"}, "salle_id": {"placeholder": "Classroom ID"}, "teacher_id": {"placeholder": "Teacher ID"}, "class_id": {"placeholder": "Class ID"}, "matiere_id": {"placeholder": "Matiere ID"}}}}, "class": {"form": {"fields": {"nameClass": {"placeholder": "Class Name", "validation": "Class Name is required"}, "gradeId": {"placeholder": "Grade ID", "validation": "Grade ID is required"}, "supervisorId": {"placeholder": "Supervisor ID", "validation": "Supervisor ID is required"}}, "buttons": {"submit": "Submit"}}}, "matiere": {"form": {"fields": {"name": {"label": "Subject Name", "placeholder": "Enter subject name", "validation": {"required": "Subject Name is required", "minLength": "Subject Name must be at least 2 characters"}}, "description": {"label": "Description", "placeholder": "Enter subject description", "validation": {"required": "Description is required", "minLength": "Description must be at least 10 characters"}}}, "buttons": {"submit": "Create Subject", "submitting": "Creating Subject..."}}, "messages": {"success": "Subject created successfully", "error": {"default": "Failed to create subject", "adminNotFound": "Admin ID not found"}}}}, "teacher-folder": {"teacherManagement": {"title": "Teachers Table", "buttons": {"addTeacher": "Add New Teacher", "profile": "Profile", "delete": "Delete"}, "badges": {"teacherCount": "{count} Teachers"}, "search": {"placeholder": "Search teachers by name or CIN..."}, "filter": {"button": "Filter", "title": "Filter Teachers", "fields": {"title": {"label": "Title", "options": {"all": "All Titles", "noTitle": "No Title"}}}, "clearAll": "Clear all"}, "table": {"headers": {"teacher": "Teacher", "cin": "CIN", "title": "Title", "actions": "Actions"}, "noTitle": "No Title"}, "pagination": {"previous": "Previous", "next": "Next"}, "modals": {"profile": {"title": "Teacher Profile"}, "add": {"title": "Add New Teacher"}, "delete": {"title": "Are you sure?", "description": "This action will permanently delete the teacher \"{name}\" and cannot be undone.", "buttons": {"cancel": "Cancel", "confirm": "Delete"}}}, "notifications": {"update": {"title": "Teacher Updated", "description": "The teacher has been successfully updated."}, "create": {"title": "Teacher Created", "description": "The teacher has been successfully created."}, "delete": {"title": "Teacher Deleted", "description": "The teacher has been successfully deleted."}, "error": {"title": "Deletion Failed", "description": "Failed to delete teacher. Please try again."}}}, "teacherForm": {"fields": {"cin": {"label": "CIN", "placeholder": "Enter CIN", "validation": {"required": "CIN is required", "minLength": "CIN must be at least 8 characters"}}, "firstname": {"label": "First Name", "placeholder": "Enter first name", "validation": {"required": "First name is required", "minLength": "First name must be at least 2 characters"}}, "lastname": {"label": "Last Name", "placeholder": "Enter last name", "validation": {"required": "Last name is required", "minLength": "Last name must be at least 2 characters"}}, "email": {"label": "Email", "placeholder": "Enter email", "validation": {"required": "Email is required", "format": "Invalid email format"}}, "password": {"label": "Password", "placeholder": "Enter password", "validation": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "uppercase": "Password must contain at least one uppercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character"}, "strength": {"title": "Password strength:", "weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong", "requirements": {"title": "Password must contain:", "length": "At least 6 characters", "uppercase": "At least one uppercase letter", "number": "At least one number", "special": "At least one special character"}}}, "title": {"label": "Title", "placeholder": "Select title", "options": {"professor": "Professor", "dr": "Dr.", "assistantProfessor": "Assistant Professor"}, "validation": {"required": "Title is required"}}, "subjects": {"label": "Subjects", "placeholder": "Select subjects", "refreshButton": "Refresh Subjects", "noSubjects": "No subjects available.", "validation": {"required": "At least one subject is required"}}, "birthday": {"label": "Birthday", "placeholder": "Select birthday"}, "gender": {"label": "Gender", "placeholder": "Select gender", "options": {"male": "Male", "female": "Female"}, "validation": {"required": "Gender is required"}}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "avatar": {"label": "Avatar", "uploadLabel": "Teacher <PERSON><PERSON>"}}, "buttons": {"cancel": "Cancel", "submit": "Submit"}, "debug": {"initialData": "TeacherForm initialData:", "subjects": "TeacherForm subjects:"}}}, "student-folder": {"studentManagement": {"title": "Students Table", "buttons": {"addStudent": "Add New Student", "profile": "Profile", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "markAsUnpaid": "<PERSON> as Unpaid", "delete": "Delete"}, "badges": {"studentCount": "{count} Students"}, "paymentManagement": {"title": "Payment Management", "buttons": {"downloadAll": "Download All Students", "downloadPaid": "Download Paid Students", "downloadUnpaid": "Download Unpaid Students", "resetPayments": "Reset All Payments"}}, "search": {"placeholder": "Search students by name or CIN..."}, "filter": {"button": "Filter", "title": "Filter Students", "fields": {"gradeLevel": {"label": "Grade Level", "options": {"all": "All Grade Levels"}}, "gender": {"label": "Gender", "options": {"all": "All Genders", "male": "Male", "female": "Female"}}, "isPaid": {"label": "Payment Status", "options": {"all": "All Statuses", "paid": "Paid", "unpaid": "Unpaid"}}, "class": {"label": "Class", "options": {"all": "All Classes"}}, "parent": {"label": "Parent", "placeholder": "Search parents...", "options": {"all": "All Parents"}}}, "activeFilters": {"title": "Active Filters:", "grade": "Grade: {value}", "gender": "Gender: {value}", "payment": "Payment: {value}", "class": "Class: {value}", "parent": "Parent: {value}"}, "clearAll": "Clear all"}, "table": {"headers": {"student": "Student", "cin": "CIN", "gradeLevel": "Grade Level", "class": "Class", "payment": "Payment", "actions": "Actions"}, "noGradeLevel": "No Grade Level", "noClass": "No Class", "paymentStatus": {"paid": "Paid", "unpaid": "Unpaid"}}, "pagination": {"previous": "Previous", "next": "Next"}, "modals": {"profile": {"title": "Student Profile"}, "add": {"title": "Add New Student"}, "payment": {"title": "Change Payment Status", "description": "Are you sure you want to mark {name} as {status}?", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "reset": {"title": "Reset All Payments", "description": "This will mark ALL students as UNPAID for the selected month and year. This action cannot be undone.", "fields": {"month": {"label": "Month", "placeholder": "Select month"}, "year": {"label": "Year", "placeholder": "Select year"}}, "buttons": {"cancel": "Cancel", "reset": "Reset All Payments", "resetting": "Resetting..."}}, "download": {"all": {"title": "Download All Students", "description": "Generate a PDF report of all students for the selected month and year. Please enter the month, year, and filename for this report:"}, "paid": {"title": "Download Paid Students", "description": "Generate a PDF report of paid students for the selected month and year. Please enter the month, year, and filename for this report:"}, "unpaid": {"title": "Download Unpaid Students", "description": "Generate a PDF report of unpaid students for the selected month and year. Please enter the month, year, and filename for this report:"}, "fields": {"month": {"label": "Month", "placeholder": "Select month"}, "year": {"label": "Year", "placeholder": "Select year"}, "filename": {"label": "Filename", "placeholder": "Enter filename"}}, "buttons": {"cancel": "Cancel", "download": "Download", "downloading": "Downloading..."}, "validation": {"title": "Validation Error", "missingFields": "Please enter month, year, and filename.", "yearFormat": "Year must be a 4-digit number."}}, "delete": {"title": "Delete Student", "description": "Are you sure you want to delete {name}? This action cannot be undone.", "buttons": {"cancel": "Cancel", "delete": "Delete", "deleting": "Deleting..."}, "confirmationText": "This action will permanently delete the student and cannot be undone."}}, "notifications": {"reset": {"title": "Reset Successful", "description": "All students have been marked as unpaid for {month} {year}"}, "download": {"title": "Download Started", "description": "The {type} PDF is being downloaded."}, "delete": {"title": "Student Deleted", "description": "The student has been successfully deleted."}, "error": {"reset": {"title": "Reset Failed", "description": "There was an error resetting student payment status."}, "download": {"title": "Download Failed", "description": "There was an error generating the PDF file."}, "delete": {"title": "Deletion Failed", "description": "Failed to delete student. Please try again."}}, "payment": {"title": "Payment Status Updated", "description": "The student's payment status has been updated successfully."}, "update": {"title": "Student Updated", "description": "The student has been successfully updated."}, "create": {"title": "Student Created", "description": "The student has been successfully created."}}, "pdf": {"titles": {"all": "All Students", "paid": "Paid Students", "unpaid": "Unpaid Students"}, "generatedOn": "Generated on: {date}", "headers": {"name": "Name", "cin": "CIN", "gradeLevel": "Grade Level", "class": "Class", "paymentStatus": "Payment Status"}, "status": {"paid": "Paid", "unpaid": "Unpaid"}}}, "studentScoreDetails": {"title": "Score Details - {firstName} {lastName}", "buttons": {"backToGrades": "Back to Grades"}, "loading": "Loading...", "errors": {"general": "Error: {errorMessage}", "studentNotFound": "Student not found"}}, "studentForm": {"fields": {"cin": {"label": "CIN", "placeholder": "Enter CIN", "validation": {"required": "CIN is required", "length": "CIN must be 8 characters"}}, "firstname": {"label": "First Name", "placeholder": "Enter first name", "validation": {"required": "First name is required", "minLength": "First name must be at least 2 characters"}}, "lastname": {"label": "Last Name", "placeholder": "Enter last name", "validation": {"required": "Last name is required", "minLength": "Last name must be at least 2 characters"}}, "email": {"label": "Email", "placeholder": "Enter email", "validation": {"required": "Email is required", "format": "Invalid email format"}}, "password": {"label": "Password", "placeholder": "Enter password", "validation": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "uppercase": "Password must contain at least one uppercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character"}, "strength": {"title": "Password strength:", "weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong", "requirements": {"title": "Password must contain:", "length": "At least 6 characters", "uppercase": "At least one uppercase letter", "number": "At least one number", "special": "At least one special character"}}, "buttons": {"show": "Show", "hide": "<PERSON>de", "clear": "Clear"}}, "gradeLevel": {"label": "Grade Level", "placeholder": "Enter grade level"}, "enrolledDate": {"label": "Enrollment Date", "placeholder": "Select enrollment date"}, "birthday": {"label": "Birthday", "placeholder": "Select birthday"}, "gender": {"label": "Gender", "placeholder": "Select gender", "options": {"male": "Male", "female": "Female"}, "validation": {"required": "Gender is required"}}, "parentId": {"label": "Parent", "placeholder": "Select parent", "search": {"placeholder": "Search parents by name or CIN...", "noResults": "No parents match your search", "noParents": "No parents available"}, "buttons": {"clear": "Clear Selection"}}, "classId": {"label": "Class", "placeholder": "Select class", "options": {"none": "No Class"}, "moreClasses": "+ {count} more classes..."}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "avatar": {"label": "Student Avatar"}, "isActive": {"label": "Status", "options": {"active": "Active", "inactive": "Inactive"}}}, "buttons": {"cancel": "Cancel", "submit": "{submitLabel}"}}, "listEliminate": {"title": "Student Management", "buttons": {"eliminateAll": "Eliminate All", "eliminate": "Eliminate", "confirmElimination": "Confirm Elimination", "removeElimination": "Remove Elimination"}, "dialogs": {"eliminateAll": {"title": "Set Elimination Thresholds"}, "eliminateStudent": {"title": "Eliminate {fullname}", "absences": "{absences} absences"}}, "table": {"headers": {"fullName": "Full Name", "action": "Action"}}}, "eliminationOverview": {"title": "Data Storage Overview", "cards": {"academicHistory": {"title": "Academic History", "description": "Access complete academic records including grades, transcripts, and performance history."}, "attendance": {"title": "Attendance Records", "description": "Track student and teacher attendance with detailed reports and analytics."}, "exportTools": {"title": "Export & Reporting Tools", "description": "Generate custom reports and export data in various formats including PDF, Excel, and CSV."}, "backup": {"title": "Data Backup & Recovery", "description": "Create and manage data backups to ensure your school information is always safe."}}, "buttons": {"backToDashboard": "Back to Dashboard"}}, "calculateStudentScore": {"title": "Student Grades", "dialog": {"title": "Edit Formulas and Coefficients for Subjects"}, "buttons": {"editFormulas": "Edit Formulas and Coefficients", "details": "Details"}, "table": {"headers": {"fullName": "Full Name", "cin": "CIN", "globalScore": "Global Score", "actions": "Actions"}}, "loading": "Loading...", "errors": {"general": "Error: {errorMessage}"}, "scoreCalculator": {"searchSubject": "Search Subject", "searchPlaceholder": "Search by subject name", "table": {"headers": {"subject": "Subject", "formula": "Formula", "grade": "Grade", "actions": "Actions"}, "formulaPlaceholder": "Enter formula", "updateScore": "Update Score"}}}}, "score-folder": {"scoreStudent": {"title": "Average Scores by Subject", "table": {"headers": {"subject": "Subject", "averageScore": "Average Score"}}, "noData": "No subject data available"}, "moyeneStudent": {"title": "Semester Averages", "table": {"headers": {"semester": "<PERSON><PERSON><PERSON>", "averageScore": "Average Score"}}, "noData": "No semester data available"}}, "parent-folder": {"parentManagment": {"title": "Parents Management", "badges": {"totalParents": "{count} Parents"}, "buttons": {"addNew": "Add New Parent", "profile": "Profile", "clearAll": "Clear all"}, "search": {"placeholder": "Search parents by name or CIN..."}, "filters": {"button": "Filter", "title": "Filter <PERSON>", "gender": {"label": "Gender", "all": "All Genders", "male": "Male", "female": "Female"}, "student": {"label": "Student", "search": "Search student by name or CIN...", "all": "All Students"}}, "table": {"headers": {"parent": "Parent", "cin": "CIN", "gender": "Gender", "students": "Students", "actions": "Actions"}, "studentsCount": "{count} students"}, "pagination": {"previous": "Previous", "next": "Next"}, "dialogs": {"profile": {"title": "Parent Profile"}, "addNew": {"title": "Add New Parent"}, "delete": {"title": "Are you sure?", "description": "This action will permanently delete the parent {name} and cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}}, "notifications": {"update": {"title": "Parent Updated", "description": "The parent has been successfully updated."}, "create": {"title": "Parent Created", "description": "The parent has been successfully created."}, "delete": {"title": "<PERSON><PERSON> Deleted", "description": "The parent has been successfully deleted."}, "deleteError": {"title": "Deletion Failed", "description": "Failed to delete parent. Please try again."}}}, "parentManagement": {"title": "Parents Table", "buttons": {"addNew": "Add New Parent", "profile": "Profile"}, "search": {"placeholder": "..."}, "filters": {"button": "Filters", "gender": {"label": "Gender", "placeholder": "Select gender", "all": "All Genders", "male": "Male", "female": "Female"}, "hasStudents": {"label": "Has Students", "placeholder": "Select status", "all": "All Parents", "yes": "Has Students", "no": "No Students"}}, "badges": {"parentsCount": "{count} Parents", "pagination": "Page {current} of {total}"}, "pagination": {"previous": "Previous", "next": "Next"}, "dialogs": {"profile": {"title": "Parent Profile"}, "addNew": {"title": "Add New Parent"}}, "errors": {"general": "Error: {errorMessage}", "update": "Failed to update parent. Please try again.", "create": "Failed to create parent. Please try again."}}, "parentForm": {"fields": {"cin": {"label": "CIN", "placeholder": "Enter CIN", "validation": {"required": "CIN is required", "length": "CIN must be 8 characters"}}, "firstname": {"label": "First Name", "placeholder": "Enter first name", "validation": {"required": "First name is required", "minLength": "First name must be at least 2 characters"}}, "lastname": {"label": "Last Name", "placeholder": "Enter last name", "validation": {"required": "Last name is required", "minLength": "Last name must be at least 2 characters"}}, "email": {"label": "Email", "placeholder": "Enter email", "validation": {"required": "Email is required", "format": "Invalid email format"}}, "password": {"label": "Password", "placeholder": "Enter password", "validation": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "uppercase": "Password must contain at least one uppercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character"}, "strength": {"title": "Password strength:", "weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong", "requirements": {"title": "Password must contain:", "length": "At least 6 characters", "uppercase": "At least one uppercase letter", "number": "At least one number", "special": "At least one special character"}}}, "birthday": {"label": "Birthday", "placeholder": "Select birthday"}, "gender": {"label": "Gender", "placeholder": "Select gender", "options": {"male": "Male", "female": "Female"}, "validation": {"required": "Gender is required"}}, "address": {"label": "Address", "placeholder": "Enter address"}, "phone": {"label": "Phone Number", "placeholder": "Enter phone number"}, "avatar": {"label": "<PERSON><PERSON>"}, "isActive": {"label": "Status", "options": {"active": "Active", "inactive": "Inactive"}}, "students": {"label": "Assigned Students", "search": {"placeholder": "Search students by name or CIN...", "noResults": "No students match your search", "noStudents": "No students available"}}}, "buttons": {"cancel": "Cancel", "submit": "{submitLabel}"}}}, "classroom-folder": {"classroomForm": {"fields": {"name": {"label": "Name", "placeholder": "Room 101", "validation": {"required": "Name is required"}}, "type": {"label": "Type", "placeholder": "Regular Classroom", "validation": {"required": "Type is required"}}, "capacity": {"label": "Capacity", "placeholder": "30", "validation": {"required": "Capacity is required", "min": "Capacity must be at least 1"}}, "floor": {"label": "Floor", "placeholder": "1st Floor", "validation": {"required": "Floor is required"}}, "building": {"label": "Building", "placeholder": "Main Building", "validation": {"required": "Building is required"}}, "features": {"label": "Features", "placeholder": "Projector, Whiteboard, Air Conditioning", "hint": "Enter features separated by commas"}, "notes": {"label": "Notes", "placeholder": "Additional information about this classroom"}, "isActive": {"label": "Active Status", "description": "Inactive classrooms won't appear in scheduling options"}}, "buttons": {"cancel": "Cancel", "submit": "{submitLabel}"}}, "classroomManagement": {"title": "Classroom Management", "description": "Manage classrooms, labs, and other spaces in your establishment", "badges": {"totalClassrooms": "{count} Classrooms"}, "buttons": {"addClassroom": "Add Classroom", "refresh": "Refresh"}, "search": {"placeholder": "Search classrooms by name, type, or building..."}, "table": {"caption": "List of classrooms in your establishment", "headers": {"name": "Name", "type": "Type", "capacity": "Capacity", "location": "Location", "features": "Features", "status": "Status", "actions": "Actions"}, "noClassrooms": "No classrooms found. Click \"Add Classroom\" to create one.", "featuresMore": "+{count} more", "status": {"active": "Active", "inactive": "Inactive"}}, "dialogs": {"add": {"title": "Add New Classroom"}, "edit": {"title": "Edit Classroom"}, "delete": {"title": "Confirm Deletion", "description": "Are you sure you want to delete the classroom \"{name}\"? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}}, "notifications": {"fetchError": {"title": "Error", "description": "Failed to load classrooms. Please try again."}, "createSuccess": {"title": "Success", "description": "Classroom created successfully"}, "createError": {"title": "Error", "description": "Failed to create classroom. Please try again."}, "updateSuccess": {"title": "Success", "description": "Classroom updated successfully"}, "updateError": {"title": "Error", "description": "Failed to update classroom. Please try again."}, "deleteSuccess": {"title": "Success", "description": "Classroom deleted successfully"}, "deleteError": {"title": "Error", "description": "Failed to delete classroom. Please try again."}}}}, "dashboard-folder": {"quickTimetableView": {"title": "Quick Timetable View", "description": "View the current timetable by class, teacher, or room", "viewTypes": {"class": "Class", "teacher": "Teacher", "room": "Room"}, "entitySelection": {"placeholder": "Select entity"}, "timetable": {"timeColumn": "Time", "emptyCell": "-", "noData": "No data available for the selected view"}, "buttons": {"delete": "Delete", "viewComplete": "View Complete Schedule"}, "deleteDialog": {"title": "Delete Timetable", "description": "Are you sure you want to delete this timetable? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete"}, "noTimetable": {"title": "No Active Timetable", "description": "You don't have an active timetable. Create a timetable and set it as active.", "button": "Create Timetable"}, "notifications": {"deleteError": {"title": "Error", "description": "No timetable selected for deletion"}, "deleteSuccess": {"title": "Timetable Deleted", "description": "The timetable has been successfully deleted."}, "deleteFailed": {"title": "Error", "description": "Failed to delete the timetable. Please try again."}}}, "timetableDashboard": {"title": "Timetables", "description": "View and manage your school timetables", "buttons": {"refresh": "Refresh", "createNew": "Create New", "backToList": "Back to List"}, "table": {"headers": {"description": "Description", "academicYear": "Academic Year", "status": "Status", "created": "Created", "updated": "Updated", "actions": "Actions"}, "values": {"untitledTimetable": "Untitled Timetable", "notSpecified": "Not specified", "active": "Active", "inactive": "Inactive"}}, "dropdownMenu": {"label": "Actions", "items": {"view": "View", "edit": "Edit", "delete": "Delete"}}, "editDialog": {"title": "Edit Timetable", "description": "Update the details of your timetable", "fields": {"description": "Description", "academicYear": "Academic Year", "academicYearPlaceholder": "e.g. 2023-2024", "isActive": "Active", "activeStatus": {"active": "Active", "inactive": "Inactive"}}, "buttons": {"cancel": "Cancel", "save": "Save Changes", "saving": "Saving..."}}, "deleteDialog": {"title": "Confirm Deletion", "description": "Are you sure you want to delete this timetable? This action cannot be undone.", "buttons": {"cancel": "Cancel", "delete": "Delete", "deleting": "Deleting..."}}, "emptyState": {"title": "No Timetables Found", "description": "You haven't created any timetables yet. Create your first timetable to get started.", "button": "Create Timetable"}, "loadingState": {"title": "Error Loading Timetables", "description": "There was a problem loading your timetables. Please try again.", "button": "Try Again"}, "notifications": {"deleteSuccess": {"title": "Timetable Deleted", "description": "The timetable has been successfully deleted."}, "deleteError": {"title": "Error", "description": "Failed to delete timetable: {error}"}, "updateSuccess": {"title": "Timetable Updated", "description": "The timetable has been successfully updated."}, "updateError": {"title": "Error", "description": "Failed to update timetable: {error}"}}}}, "etablissment-folder": {"subjectManagement": {"title": "Subject Management", "description": "Manage subjects and their properties in your establishment", "badges": {"count": "{count} Subjects"}, "buttons": {"addSubject": "Add Subject", "refresh": "Refresh"}, "search": {"placeholder": "Search subjects by name or description..."}, "table": {"caption": "List of subjects in your establishment", "headers": {"name": "Name", "description": "Description", "passingGrade": "Passing Grade", "formula": "Formula", "actions": "Actions"}, "emptyState": "No subjects found. Click \"Add Subject\" to create one.", "notSet": "Not set", "noFormula": "No formula"}, "dialogs": {"add": {"title": "Add New Subject"}, "edit": {"title": "Edit Subject"}, "delete": {"title": "Confirm Deletion", "description": "Are you sure you want to delete the subject \"{name}\"? This action cannot be undone."}, "buttons": {"cancel": "Cancel", "delete": "Delete"}}, "notifications": {"fetchError": {"title": "Error", "description": "Failed to load subjects. Please try again."}, "updateSuccess": {"title": "Success", "description": "Subject updated successfully"}, "updateError": {"title": "Error", "description": "Failed to update subject: {error}"}, "deleteSuccess": {"title": "Success", "description": "Subject deleted successfully"}, "deleteError": {"title": "Error", "description": "Failed to delete subject: {error}"}, "createSuccess": {"title": "Success", "description": "Subject created successfully"}, "createError": {"title": "Error", "description": "Failed to create subject. Please try again."}}, "errors": {"loading": "Error loading subjects. Please try again."}}, "subjectForm": {"fields": {"name": {"label": "Subject Name", "placeholder": "Mathematics", "validation": {"required": "Name is required"}}}, "buttons": {"cancel": "Cancel"}}, "gradeManagement": {"title": "Grade Management", "description": "Manage grade levels in your establishment", "badge": "{count} Grades", "addButton": "Add Grade", "searchPlaceholder": "Search grades by name...", "refreshButton": "Refresh", "tableCaption": "List of grade levels in your establishment", "tableHeaders": {"name": "Name", "classes": "Classes", "actions": "Actions"}, "noGrades": "No grades found. Click \"Add Grade\" to create one.", "classesCount": "{count} Classes", "noClasses": "No classes", "addDialogTitle": "Add New Grade", "editDialogTitle": "Edit Grade", "deleteDialogTitle": "Confirm Deletion", "deleteConfirmation": "Are you sure you want to delete the grade \"{name}\"? This action cannot be undone.", "cancelButton": "Cancel", "deleteButton": "Delete", "notifications": {"fetchError": "Failed to load grades. Please try again.", "createSuccess": "Grade created successfully", "createError": "Failed to create grade. Please try again.", "updateSuccess": "Grade updated successfully", "updateError": "Failed to update grade. Please try again.", "deleteSuccess": "Grade deleted successfully", "deleteError": "Failed to delete grade. Please try again."}, "error": "Error loading grades. Please try again."}, "gradeForm": {"fields": {"name": {"label": "Grade Name", "placeholder": "Grade 1", "validation": {"required": "Name is required"}}}, "buttons": {"cancel": "Cancel"}}, "classManagement": {"title": "Classes Management", "description": "Manage classes and assign teachers in your establishment", "badge": "{count} Classes", "addButton": "Add Class", "searchPlaceholder": "Search classes by name...", "tableCaption": "List of classes in your establishment", "tableHeaders": {"name": "Name", "grade": "Grade", "supervisor": "Supervisor", "actions": "Actions"}, "noClasses": "No classes found. Click \"Add Class\" to create one.", "noGrade": "No Grade", "noSupervisor": "No Supervisor", "addDialogTitle": "Add New Class", "editDialogTitle": "Edit Class", "deleteDialogTitle": "Confirm Deletion", "deleteConfirmation": "Are you sure you want to delete the class \"{name}\"? This action cannot be undone.", "cancelButton": "Cancel", "deleteButton": "Delete", "notifications": {"updateSuccess": "Class updated successfully", "updateError": "Failed to update class: {error}", "createSuccess": "Class created successfully", "createError": "Failed to create class: {error}", "deleteSuccess": "Class deleted successfully", "deleteError": "Failed to delete class: {error}"}, "error": "Error loading classes. Please try again."}, "classForm": {"fields": {"name": {"label": "Class Name", "placeholder": "Enter class name", "validation": {"required": "Name is required", "minLength": "Name must be at least 2 characters"}}, "gradeId": {"label": "Grade", "placeholder": "Select grade", "validation": {"required": "Grade is required"}}, "supervisorId": {"label": "Supervisor", "placeholder": "Select supervisor", "validation": {"required": "Supervisor is required"}}}, "buttons": {"cancel": "Cancel"}}}, "establishment-folder": {"studentCardDesigner": {"title": "Student ID Card Designer", "description": "Customize the appearance of student ID cards for your establishment", "tabs": {"preview": "Preview", "design": "Design", "content": "Content"}, "preview": {"studentSelect": "Preview with Student", "defaultPreview": "Default Preview", "cardLayout": "Card Layout", "layoutOptions": {"horizontal": "Horizontal", "vertical": "Vertical"}, "saveButton": "Save Card Design"}, "content": {"validUntil": "<PERSON>id <PERSON>", "validUntilHelp": "This date will appear on the card as the expiration date", "toggles": {"showQrCode": "Show QR Code", "showLogo": "Show Establishment Logo", "enableCustomFields": "Enable Custom Fields"}, "customFields": {"field1Label": "Custom Field 1 Label", "field2Label": "Custom Field 2 Label"}, "buttons": {"saveSettings": "Save Settings", "resetDefaults": "Reset to Defaults"}}, "notifications": {"saveSuccess": "Card design settings saved successfully", "resetInfo": "Card design settings reset to defaults"}}, "modernCardTemplate": {"header": {"official": "OFFICIAL"}, "userInfo": {"emailLabel": "Email:", "validUntilLabel": "Valid Until:"}, "footer": {"property": "This card is the property of {<PERSON><PERSON><PERSON>}", "returnInfo": "If found, please return to the administration office", "signature": "Signature"}, "downloadButton": "Download ID Card"}, "establishmentServiceCard": {"badge": {"active": "Active", "inactive": "Inactive"}, "services": {"title": "Services:", "studentCards": {"title": "Student Cards", "description": "ID card management"}, "customServices": "Custom Services"}, "buttons": {"edit": "Edit", "viewSite": "View Site"}}, "establishmentManagement": {"title": "Establishment Management", "addButton": "Add New Establishment", "establishmentsList": {"title": "Establishments", "status": {"active": "Active", "inactive": "Inactive"}, "visitWebsite": "Visit Website", "viewDetails": "View Details"}, "addEstablishmentModal": {"title": "Add New Establishment", "basicInfo": {"nameLabel": "Name", "namePlaceholder": "Enter establishment name", "addressLabel": "Address", "addressPlaceholder": "Enter establishment address"}, "urls": {"websiteLabel": "Website URL (Optional)", "websitePlaceholder": "Enter website URL", "logoLabel": "Logo URL (Optional)", "logoPlaceholder": "Enter logo URL"}, "cmsContent": {"label": "CMS Content (Optional)", "placeholder": "Enter CMS content"}, "services": {"label": "Services"}, "status": {"label": "Status", "placeholder": "Select status", "active": "Active", "inactive": "Inactive"}, "admins": {"label": "Select Admins", "loading": "Loading admins...", "error": "Error loading admins: {message}"}, "teachers": {"label": "Select Teachers", "loading": "Loading teachers...", "error": "Error loading teachers: {message}"}, "students": {"label": "Select Students", "loading": "Loading students...", "error": "Error loading students: {message}"}, "buttons": {"cancel": "Cancel", "add": "Add Establishment"}}, "validation": {"name": "Name is required", "address": "Address is required", "url": "Invalid URL format", "logo": "Invalid logo URL format"}, "notifications": {"addSuccess": "Establishment added successfully!", "addError": "Failed to create establishment"}, "loading": "Loading", "error": {"title": "Error Loading Data", "message": "{message}"}}, "establishmentDatabaseService": {"button": {"dataStorage": "Data Storage"}}, "establishmentCardService": {"button": "Student Cards", "dialog": {"title": "Student ID Card Design", "description": "Customize the appearance of student ID cards for {establishmentName}", "tabs": {"preview": "Preview", "design": "Design"}, "preview": {"description": "This is how student ID cards will look for your establishment.", "cardLayout": "Card Layout", "layoutOptions": {"horizontal": "Horizontal", "vertical": "Vertical"}}, "buttons": {"cancel": "Cancel", "save": "Save Card Design", "saving": "Saving..."}}, "notifications": {"updateSuccess": "Card design updated successfully", "updateError": "Failed to update card design"}}, "establishmentApiTester": {"title": "Direct API Tester", "description": "Test API calls directly to troubleshoot issues", "tabs": {"request": "Request", "response": "Response", "debug": "Debug"}, "request": {"url": "API URL", "method": "Request Method", "methodOptions": {"fetch": "Fetch API", "xhr": "XMLHttpRequest", "xhrDirect": "XHR Direct"}, "body": {"label": "Request Body (JSON)", "loadExample": "Load Postman Example", "placeholder": "Paste your JSON data here..."}}, "response": {"placeholder": "Response will appear here..."}, "debug": {"request": {"title": "Request", "headers": "Headers:", "body": "Body:"}, "response": {"title": "Response", "status": "Status:", "headers": "Headers:", "body": "Body:"}, "error": {"title": "Error:"}}, "button": {"send": "Send API Request", "sending": "Sending..."}, "notifications": {"invalidJson": {"title": "Invalid JSON", "description": "Please enter valid JSON data"}, "authError": {"title": "Authentication Error", "description": "No authentication token found"}, "success": {"title": "Success", "description": "API call successful using XHR Direct"}, "error": {"title": "Error", "description": "Unknown error occurred"}}}, "cardManagement": {"title": "Student ID Card Design", "description": "Customize the appearance of student ID cards for {establishmentName}", "tabs": {"preview": "Preview", "design": "Design"}, "design": {"cardTheme": {"title": "Card Theme", "description": "Select a theme for the student ID card"}, "cardLayout": {"title": "Card Layout", "description": "Choose the orientation of the card", "options": {"horizontal": "Horizontal", "vertical": "Vertical"}}, "cardPreview": {"title": "Card Preview"}}, "buttons": {"toggleView": {"toDesign": "Edit Design", "toPreview": "View Preview"}, "submit": "Submit"}, "notifications": {"createSuccess": "Card design saved successfully", "createError": "Failed to save card design", "updateSuccess": "Card design updated successfully", "updateError": "Failed to update card design"}}, "cardPreview": {"defaultUser": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "class": "Class 10-A"}, "profileUrl": {"student": "/student-profile/{id}", "teacher": "/teacher-profile/{id}", "admin": "/admin-profile/{id}", "parent": "/parent-profile/{id}", "default": "/profile/{id}"}}, "cardTemplates": {"template1": {"title": "{role} ID CARD", "fields": {"id": "ID", "class": "Class", "email": "Email", "validUntil": "<PERSON>id <PERSON>"}}, "template2": {"frontSide": {"idNo": "ID no:", "joinDate": "Join Date:", "phone": "Phone:"}, "backSide": {"termsTitle": "Terms And Conditions:", "terms": ["This card is the property of {<PERSON><PERSON><PERSON>} and must be returned upon request.", "If found, please return to the administration office."], "contactTitle": "Contact us", "website": "www.{establishmentDomain}.com", "validUntil": "Valid until: {validUntil}"}}, "template3": {"title": "{role} Card", "fields": {"name": "Name", "idNumber": "ID Number", "email": "Email", "expireDate": "Expire Date"}}}, "cardThemeSelector": {"title": "Card Theme", "description": "Select a theme for the student ID card", "selectTheme": "Select Theme", "customTheme": {"title": "Custom Theme Colors", "fields": {"primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "textColor": "Text Color", "accentColor": "Accent Color"}}, "themes": {"blue": "Blue Professional", "green": "Green Academic", "red": "Red Classic", "purple": "Purple Elite", "dark": "Dark Modern", "custom": "Custom Theme"}}}}