{"timetableTest": {"title": "Page de Test d'Emploi du Temps de l'Enseignant", "loading": "Chargement de l'emploi du temps...", "error": "Échec du chargement de l'emploi du temps. Veuillez réessayer plus tard.", "noData": "<PERSON><PERSON><PERSON> donnée d'emploi du temps disponible.", "sections": {"rawData": {"title": "<PERSON><PERSON><PERSON>"}, "scheduleData": {"title": "Données d'Emploi du Temps", "noSchedule": "<PERSON><PERSON><PERSON> donnée d'emploi du temps disponible."}}, "classLabel": "Classe", "dayLabels": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "lundi": "<PERSON><PERSON>", "mardi": "<PERSON><PERSON>", "mercredi": "<PERSON><PERSON><PERSON><PERSON>", "jeudi": "<PERSON><PERSON>", "vendredi": "<PERSON><PERSON><PERSON><PERSON>", "samedi": "<PERSON><PERSON>", "dimanche": "<PERSON><PERSON><PERSON>"}, "lessonDetails": {"subject": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "room": "Salle", "noLessons": "Pas de cours"}, "debug": {"fetchingTimetable": "Récupération de l'emploi du temps de l'enseignant...", "timetableReceived": "Données d'emploi du temps de l'enseignant reçues", "fetchError": "Erreur lors de la récupération de l'emploi du temps de l'enseignant"}, "accessibility": {"timetableSection": "Emploi du temps hebdomadaire de l'enseignant", "daySchedule": "Emploi du temps pour {day}", "lessonInfo": "Cours de {subject} de {startTime} à {endTime} dans la salle {room}"}}, "timetable": {"title": "Emploi du Temps de l'Enseignant", "backButton": "Retour", "loading": "Chargement de l'emploi du temps...", "error": "Échec du chargement de l'emploi du temps. Veuillez réessayer plus tard.", "noData": {"title": "<PERSON><PERSON><PERSON> donnée d'emploi du temps disponible.", "contactAdmin": "Veuillez contacter votre administrateur."}, "noSchedule": {"title": "<PERSON><PERSON>ne donnée d'emploi du temps trouvée dans votre emploi du temps.", "contactAdmin": "Veuillez contacter votre administrateur."}, "table": {"headers": {"day": "Jour", "time": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "class": "Classe", "room": "Salle"}, "noLessons": "Pas de cours", "today": {"highlight": "<PERSON><PERSON><PERSON>'hui"}}, "debug": {"fetchingTimetable": "Récupération de l'emploi du temps de l'enseignant...", "timetableReceived": "Données d'emploi du temps de l'enseignant reçues :", "processingData": "Traitement des données d'emploi du temps au format d'affichage", "fetchError": "Erreur lors de la récupération de l'emploi du temps de l'enseignant :"}, "accessibility": {"backButton": "Re<PERSON><PERSON> à la page précédente", "timetableIcon": "Icône de calendrier", "timetableHeading": "Emploi du temps hebdomadaire pour l'enseignant", "todayHighlight": "Emploi du temps du jour actuel", "timeSlot": "Créneau horaire", "subjectLabel": "Nom de la matière", "classLabel": "Nom de la classe", "roomLabel": "Numéro ou nom de la salle"}, "days": {"sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>"}}, "teacherService": {"title": "Services", "services": {"attendanceList": "Liste de Présence", "markAbsent": "<PERSON><PERSON> Absent", "classView": "Vue de Classe", "timetable": "Emp<PERSON>i du Temps", "quizzGenerator": "Générateur de Quiz", "whiteboard": "Tableau Blanc", "myNotes": "Mes Notes", "chat": "Discussion", "soon": "Bientôt"}, "inspirationalQuote": "Le succès est la somme de petits efforts répétés quotidiennement. Soyez présent, même quand c'est difficile.", "emptyCard": "Plus de services à venir", "logout": {"button": "Déconnexion", "confirmation": {"title": "Confirmer la Déconnexion", "message": "Êtes-vous sûr de vouloir vous déconnecter ?", "confirm": "<PERSON><PERSON>, me déconnecter", "cancel": "Annuler"}, "success": "Vous avez été déconnecté avec succès", "redirecting": "Redirection vers la page de connexion..."}, "accessibility": {"serviceCard": "Carte de service pour {serviceName}", "serviceIcon": "Icône pour {serviceName}", "comingSoon": "Badge à venir", "inspirationalCard": "Carte de citation inspirante"}, "debug": {"serviceClick": "Service cliqué : {serviceName}, redirection vers {path}", "logoutInitiated": "Déconnexion initiée", "logoutCompleted": "Déconnexion terminée, effacement du localStorage", "redirecting": "Redirection vers la page de connexion"}}, "teacherLayout": {"navigation": {"teacherService": "Service Enseignant", "classes": "Classes", "scheduleView": "Vue d'Emploi du Temps", "attendance": "Présence", "sessionHistory": "Historique des Sessions", "studentHomework": "Devoirs des Élèves", "absenceManagement": "Gestion des Absences", "autoCorrector": "Correcteur Automatique", "studentNotes": "Notes des Élèves", "studentScore": "Scores des Élèves"}, "accessibility": {"sidebar": "Barre latérale de navigation de l'enseignant", "mainContent": "Zone de contenu principal", "navigationButton": "Bouton de navigation pour {pageName}", "activeNavigation": "Page actuellement active : {pageName}"}}, "teacherHomework": {"title": "Gestion des Devoirs", "subjectSelection": {"allSubjects": "Toutes les Matières", "mathematics": "Mathématiques", "physics": "Physique", "chemistry": "<PERSON><PERSON>"}, "createHomework": {"buttonText": "Créer un Nouveau Devoir", "modalTitle": "<PERSON><PERSON>er une Nouvelle Tâche de Devoir", "form": {"title": {"label": "Titre"}, "description": {"label": "Description"}, "dueDate": {"label": "Date d'Échéance"}, "allowedFileTypes": {"label": "Types de Fichiers Autorisés"}, "buttons": {"cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> Devoir"}}}, "homeworkCard": {"dueDate": "Due Date: {date}", "acceptedFormats": "Accepted formats: {formats}", "submissions": "Submissions ({count})"}, "submissionItem": {"submitted": "Submitted: {date}", "downloadButton": "Download"}}, "teacherHeader": {"title": {"prefix": "Teacher", "suffix": "Dashboard"}, "logout": "Logout"}, "teacherClassesView": {"title": "My Classes", "loading": {"classes": "Loading classes...", "students": "Loading students..."}, "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class", "adminNote": "Students need to be added to this class by an administrator.", "contactAdmin": "Please contact your administrator to add students to this class."}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender", "actions": "Actions"}, "genderNotSpecified": "Not specified", "viewDetails": "View Details"}, "studentDetails": {"backButton": "Back to List", "title": "Student Details", "personalInfo": "Personal Information", "fields": {"email": "Email", "phone": "Phone", "birthday": "Birthday", "gender": "Gender", "cin": "CIN"}}}, "teacherAbsenceManagement": {"title": "Teacher Absence Management", "teacherName": "Teacher: <PERSON><PERSON><PERSON>", "pastAbsences": {"title": "Past Absences:", "noAbsences": "No past absences."}, "futureAbsences": {"title": "Future Absences:", "noAbsences": "No future absences."}, "absenceCard": {"date": "Date: {date}", "reason": "Reason: {reason}"}, "createAbsence": {"title": "Create New Absence Request:", "form": {"date": "Absence Date", "reason": {"label": "Reason for Absence", "placeholder": "Enter reason for absence"}, "button": "Add Absence"}, "validation": {"fillAllFields": "Please fill all fields."}, "success": "New absence recorded:\nDate: {date}\nReason: {reason}"}}, "simpleClassesView": {"loading": "Loading classes...", "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "{className} Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class"}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender"}, "genderNotSpecified": "Not specified"}, "error": {"loadingClasses": "Failed to load class data. Please try again."}}, "sessionHistoryView": {"title": "Session History", "description": "View your past sessions and attendance records", "search": {"placeholder": "Search by class or subject..."}, "filter": {"allStatuses": "All Statuses", "ongoing": "Ongoing", "reported": "Reported", "canceled": "Canceled"}, "table": {"headers": {"date": "Date", "time": "Time", "class": "Class", "subject": "Subject", "status": "Status", "actions": "Actions"}, "actions": {"details": "Details"}}, "loading": {"sessions": "Loading sessions...", "attendance": "Loading attendance records..."}, "noSessions": {"title": "No Sessions Found", "withFilters": "Try adjusting your filters to see more results.", "noFilters": "You don't have any sessions for this week."}, "attendanceDialog": {"title": "Attendance Details", "table": {"headers": {"student": "Student", "status": "Status", "notes": "Notes"}}, "noRecords": "No attendance records found for this session.", "status": {"present": "Present", "absent": "Absent", "leftEarly": "Left Early"}}, "errors": {"fetchingSessions": "Failed to load sessions. Please try again.", "fetchingAttendance": "Failed to load attendance records. Please try again."}}, "markAbsent": {"title": "Class Management", "classDetails": {"title": "Class Details: {className} - {subject}", "noData": "No data found for this class."}, "studentsList": {"title": "Students:"}, "buttons": {"sendData": "Send Absence Data"}, "alerts": {"absentStudents": "Absent students: {students}", "noAbsentStudents": "No students are marked absent."}}, "classesView": {"loading": {"classes": "Loading classes...", "students": "Loading students..."}, "noClasses": {"title": "No Classes Assigned", "description": "You don't have any classes assigned to you yet."}, "classCard": {"studentsTitle": "{className} Students", "description": "View all students in {className}"}, "search": {"placeholder": "Search students..."}, "studentCount": "{count} Students", "noStudents": {"withSearch": "No students match your search", "noSearch": "No students in this class"}, "table": {"headers": {"name": "Name", "email": "Email", "gender": "Gender", "actions": "Actions"}, "genderNotSpecified": "Not specified", "viewDetails": "View Details"}, "errors": {"loadingClasses": "Failed to load classes. Please try again.", "loadingStudents": "Failed to load students. Please try again."}}, "autoCorrectorService": {"title": "Correcteur Automatique", "steps": {"upload": "Télécharger & Configurer", "results": "Résultats de Correction"}, "uploadStep": {"cardTitle": "Télécharger le Travail de l'Élève", "form": {"subject": {"label": "<PERSON><PERSON>", "placeholder": "Sélectionnez la matière à corriger", "required": "<PERSON><PERSON><PERSON><PERSON> sélectionner une matière"}, "gradeLevel": {"label": "Niveau Scolaire", "placeholder": "Sélectionnez le niveau scolaire", "required": "Veuillez sélectionner un niveau scolaire"}, "assignmentTitle": {"label": "<PERSON><PERSON><PERSON> (Optionnel)", "placeholder": "Entrez le titre du devoir"}, "studentWork": {"label": "Travail de l'Élève", "required": "Veuillez télécharger une image du travail de l'élève", "button": "Sélectionner une Image"}}, "buttons": {"startCorrection": "Commencer la Correction", "uploading": "Téléchargement", "processing": "Traitement"}, "processing": {"tip": "Traitement de l'image et analyse du contenu...", "description": "<PERSON><PERSON> peut prendre quelques instants pendant que nous analysons l'écriture manuscrite et la notation mathématique."}}, "resultsStep": {"title": "Résultats de Correction", "newCorrection": "Nouvelle Correction", "sections": {"studentWork": "Travail de l'Élève", "aiAnalysis": "Analyse IA"}, "exercise": "Exercice {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "<PERSON><PERSON><PERSON>", "uploadFailed": "Échec du téléchargement des fichiers. Veuillez réessayer.", "imageOnly": "Vous ne pouvez télécharger que des fichiers image !", "sizeLimit": "L'image doit être inférieure à 10 Mo !"}, "buttons": {"previous": "Précédent"}}, "autoCorrector": {"title": "Correcteur Automatique", "uploadCard": {"title": "Télécharger le Travail de l'Élève", "description": "Téléchargez une image du travail de l'élève pour l'analyser et le corriger automatiquement. L'IA identifiera les exercices, évaluera les solutions et fournira des commentaires détaillés.", "button": "Sélectionner une Image", "startCorrection": "Commencer la Correction", "uploading": "Téléchargement"}, "processing": {"tip": "Traitement de l'image et analyse du contenu...", "description": "<PERSON><PERSON> peut prendre quelques instants pendant que nous analysons l'écriture manuscrite et la notation mathématique."}, "results": {"title": "Résultats de Correction", "newCorrection": "Nouvelle Correction", "sections": {"studentWork": "Travail de l'Élève", "aiAnalysis": "Analyse IA"}, "exercise": "Exercice {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "<PERSON><PERSON><PERSON>", "uploadFailed": "Échec du téléchargement des fichiers. Veuillez réessayer.", "imageOnly": "Vous ne pouvez télécharger que des fichiers image !", "sizeLimit": "L'image doit être inférieure à 10 Mo !"}}, "attendanceView": {"loading": {"sessions": "Chargement des sessions...", "students": "Chargement des élèves..."}, "noSessions": {"title": "<PERSON><PERSON> <PERSON>jourd'hui", "description": "Vous n'avez aucune session programmée pour aujourd'hui."}, "sessionCard": {"status": {"ongoing": "En cours", "reported": "Signalée", "canceled": "<PERSON><PERSON><PERSON>", "unknown": "Inconnue"}}, "noStudents": "Pas d'élèves dans cette classe", "table": {"headers": {"name": "Nom", "status": "Statut", "notes": "Notes", "actions": "Actions"}, "status": {"present": "Présent", "absent": "Absent", "leftEarly": "Parti tôt"}}, "notesDialog": {"title": "Ajouter des Notes", "description": "Ajouter des notes pour l'absence ou le départ anticipé de l'élève.", "placeholder": "Ajouter des notes ici...", "buttons": {"cancel": "Annuler", "save": "Enregistrer"}}, "buttons": {"saveAttendance": "Enregistrer la Présence", "saving": "Enregistrement...", "alreadyReported": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"sessions": "Échec du chargement des sessions d'aujourd'hui. Veuillez réessayer.", "students": "Échec du chargement des élèves. Veuillez réessayer.", "saving": "Échec de l'enregistrement de la présence. Veuillez réessayer."}, "success": {"saved": "La présence a été enregistrée avec succès."}}, "auto-corrector-folder": {"autoCorrectorService": {"title": "Correcteur Automatique", "steps": {"upload": "Télécharger & Configurer", "results": "Résultats de Correction"}, "uploadStep": {"cardTitle": "Télécharger le Travail de l'Élève", "form": {"subject": {"label": "<PERSON><PERSON>", "placeholder": "Sélectionnez la matière à corriger", "required": "<PERSON><PERSON><PERSON><PERSON> sélectionner une matière"}, "gradeLevel": {"label": "Niveau Scolaire", "placeholder": "Sélectionnez le niveau scolaire", "required": "Veuillez sélectionner un niveau scolaire"}, "assignmentTitle": {"label": "<PERSON><PERSON><PERSON> (Optionnel)", "placeholder": "Entrez le titre du devoir"}, "studentWork": {"label": "Travail de l'Élève", "required": "Veuillez télécharger une image du travail de l'élève", "button": "Sélectionner une Image"}}, "buttons": {"startCorrection": "Commencer la Correction", "uploading": "Téléchargement", "processing": "Traitement"}, "processing": {"tip": "Traitement de l'image et analyse du contenu...", "description": "<PERSON><PERSON> peut prendre quelques instants pendant que nous analysons l'écriture manuscrite et la notation mathématique."}}, "resultsStep": {"title": "Résultats de Correction", "newCorrection": "Nouvelle Correction", "sections": {"studentWork": "Travail de l'Élève", "aiAnalysis": "Analyse IA"}, "exercise": "Exercice {number}", "points": "{score}/{maxScore}"}, "errors": {"title": "<PERSON><PERSON><PERSON>", "uploadFailed": "Échec du téléchargement des fichiers. Veuillez réessayer.", "imageOnly": "Vous ne pouvez télécharger que des fichiers image !", "sizeLimit": "L'image doit être inférieure à 10 Mo !"}, "buttons": {"previous": "Précédent"}}}}