{"routes": {"unauthorized": {"title": "<PERSON><PERSON><PERSON>", "mainMessage": "<PERSON><PERSON><PERSON><PERSON>, vous n'avez pas la permission d'accéder à cette page. Cette zone nécessite des privilèges utilisateur différents.", "roleInfo": {"label": "Rôle Actuel :", "message": "Si vous pensez avoir besoin d'accéder à cette page, veuillez contacter votre administrateur."}, "buttons": {"goBack": "Retour", "homePage": "<PERSON> d'Accueil"}}, "subdomain-www-test": {"wwwTest": {"title": "Page de Test de Sous-domaine WWW", "currentUrl": {"title": "Informations sur l'URL Actuelle", "fields": {"fullUrl": "URL Complète :", "hostname": "Nom d'hôte :", "isSubdomain": "Est un Sous-domaine :", "schoolName": "Nom de l'École :", "isWww": "Est un Sous-domaine WWW :"}, "values": {"yes": "O<PERSON>", "no": "Non"}}, "testing": {"title": "Tester la Gestion des Sous-domaines WWW", "button": "Exécuter les <PERSON>", "currentUrlTest": {"title": "Test de l'URL Actuelle :"}, "results": {"title": "Résultats des Tests :"}}, "testLinks": {"title": "<PERSON><PERSON>", "local": {"noSubdomain": "Local : Sans Sous-domaine", "wwwSubdomain": "Local : Sous-domaine WWW", "testSchool": "Local : Sous-domaine de l'École Test"}, "production": {"noSubdomain": "Production : Sans Sous-domaine", "wwwSubdomain": "Production : Sous-domaine WWW", "testSchool": "Production : Sous-domaine de l'École Test"}}}}, "subdomain-test": {"title": "Page de Test de Sous-domaine", "auth": {"title": "Statut d'Authentification", "status": {"label": "Statut :", "loggedIn": "Connecté", "notLoggedIn": "Non Connecté"}, "buttons": {"login": "Se Connecter", "logout": "Se Déconnecter"}}, "currentUrl": {"title": "Informations sur l'URL Actuelle"}, "testUrl": {"title": "URL de l'École Test", "input": {"placeholder": "Entrez l'URL de l'école (ex : 'école-test')"}, "buttons": {"test": "Tester l'URL", "testing": "Test en Cours...", "directApi": "Tester les Points d'Accès API Directs"}}, "results": {"title": "Résultats des Tests", "success": {"message": "École trouvée avec succès !"}, "error": {"failed": "Échec du chargement de l'école"}, "noAuth": "Aucun jeton d'authentification disponible"}}, "settings": {"title": "Paramètres", "loginRequired": "Veuillez vous connecter pour accéder à vos paramètres"}, "schools": {"title": "Toutes les Écoles", "loading": "Chargement des écoles...", "error": {"title": "Erreur de Chargement des Écoles", "unknown": "Une erreur inconnue est survenue"}, "subdomains": {"title": "Accéder aux Écoles via les Sous-domaines", "description": "V<PERSON> pouvez accéder à n'importe quelle école directement en utilisant son sous-domaine :", "note": "Remarque : Assurez-vous d'avoir exécuté le script d'installation pour configurer votre fichier hosts local pour l'accès par sous-domaine."}, "card": {"services": "Services :", "more": "plus", "buttons": {"viewDetails": "Voir les Détails", "viewSite": "Voir le Site"}}}, "reset-password": {"invalid-link": {"title": "Lien de Réinitialisation Invalide", "description": "Veuillez demander une nouvelle réinitialisation de mot de passe."}, "success": {"title": "Réinitialisation du Mot de Passe Réussie", "description": "Redirection vers la page de connexion..."}, "form": {"title": {"blue-word": "Nouveau", "main": "<PERSON><PERSON>", "description": "Entrez votre nouveau mot de passe ci-dessous"}, "inputs": {"new-password": "Nouveau Mot de Passe", "confirm-password": "Confirm<PERSON> le Mot de Passe"}, "button": {"loading": "Réinitialisation...", "default": "Réinitialiser le Mot de Passe"}, "errors": {"default": "Échec de la réinitialisation du mot de passe. Veuillez réessayer."}}}, "register": {"welcome": {"blue-word": "Bienvenue", "title": "Dans votre monde", "description": "Bienvenue sur le tableau de bord de JeridSchool."}, "form": {"inputs": {"name": "Nom", "email": "Email", "password": "<PERSON><PERSON>", "workspace-name": "Nom de l'Espace de Travail"}, "button": {"register": "S'inscrire"}, "sign-in": {"text": "Vous avez un compte ?", "link": "Se Connecter"}}, "divider": {"text": "Ou continuer avec"}, "social": {"facebook": "Facebook", "google": "Google"}}, "quizz": {"title": "Générateur de Quiz", "description": "Entrez du texte et laissez-nous générer un quiz personnalisé pour vous.", "form": {"language": {"label": "Langue du Quiz", "options": {"english": "<PERSON><PERSON><PERSON>", "french": "Français", "arabic": "<PERSON><PERSON>"}}, "type": {"label": "Type de Quiz", "options": {"multiple-choice": "<PERSON><PERSON>", "true-false": "Vrai/Faux", "short-answer": "Réponse Courte"}}, "question-count": {"label": "Nombre de Questions"}, "input-text": {"label": "Texte d'Entrée", "placeholder": "Entrez votre texte ici..."}, "button": {"generate": "Gén<PERSON>rer le Quiz", "generating": "Génération en Cours..."}}, "quiz-section": {"title": "Quiz", "download-button": "Télécharger le Quiz en PDF", "question-prefix": "Question", "submit-button": "Soumettre le Quiz"}, "results": {"title": "Résultats du Quiz", "score-text": "Vous avez obtenu", "out-of": "sur", "correct": "correctes !"}, "alerts": {"empty-input": "Veuillez entrer du texte pour générer un quiz.", "answer-all": "Veuillez répondre à toutes les questions avant de soumettre."}}, "pricing": {"title": "Tarification", "paymentFrequency": "Fréquence de paiement", "frequencies": {"monthly": "<PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON>"}, "tiers": {"free": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>bt<PERSON><PERSON> toutes les fonctionnalités gratuitement, sans carte de crédit requise.", "features": ["Compatibilité multi-plateforme", "Système de notification en temps réel", "Permissions utilisateur avancées"], "cta": "S'inscrire"}, "pro": {"name": "Pro", "description": "Lorsque vous grandissez et avez besoin de plus de puissance et de flexibilité.", "features": ["Tout inclus dans l'offre gratuite plus", "<PERSON><PERSON><PERSON><PERSON> personnalisables", "Intégration avec des applications tierces"], "cta": "Commencer"}, "scaler": {"name": "Scaler", "description": "Lorsque vous grandissez et avez besoin de plus de puissance et de flexibilité.", "features": ["Tout inclus dans l'offre pro plus", "Support prioritaire", "Sécurité de niveau entreprise"], "cta": "Commencer"}}, "soldOut": "<PERSON><PERSON><PERSON><PERSON>"}, "offers": {"title": "Nos Fonctionnalités", "features": [{"title": "Planification Automatisée des Emplois du Temps", "hint": "Gestion d'emploi du temps sans effort pour tous", "description": "Générez automatiquement des emplois du temps pour les étudiants et les enseignants en fonction des ressources disponibles. Personnalisez et modifiez les emplois du temps, gérez les conflits et recevez des notifications pour les changements."}, {"title": "Générateur de Quiz", "hint": "Créez des quiz engageants en quelques minutes", "description": "Générez des quiz avec différents types de questions, définissez des limites de temps et bénéficiez d'une évaluation automatique avec des options d'ajustement manuel."}, {"title": "Gestion des Étudiants", "hint": "Données complètes sur les étudiants à portée de main", "description": "<PERSON><PERSON> et gérez les informations des étudiants, suivez leurs performances dans le temps et accédez facilement aux analyses de progression."}, {"title": "Gestion des Enseignants", "hint": "Simplifiez l'administration des enseignants sans effort", "description": "Suivez les profils des enseignants, leurs emplois du temps et les matières enseignées. G<PERSON>rez les disponibilités et facilitez la communication entre les enseignants et l'administration."}, {"title": "Partition des Salles de Classe", "hint": "Allocation efficace des ressources des salles de classe", "description": "Contrôlez les emplois du temps des salles, les ressources et les affectations. G<PERSON>rez et supervisez les leçons et activités de chaque classe, y compris l'allocation des salles."}, {"title": "Intégration Zoom", "hint": "Expérience de classe virtuelle transparente", "description": "Intégrez les appels vidéo Zoom pour les sessions de classe virtuelle. Planifiez des réunions directement depuis la plateforme et fournissez des liens et des rappels aux participants."}, {"title": "Tests et Examens avec Correction Automatique", "hint": "Système d'évaluation et de notation efficace", "description": "Créez des tests et examens avec différents types de questions. Notez automatiquement les tests applicables et fournissez un retour immédiat aux étudiants."}, {"title": "Notifications", "hint": "Restez informé avec des alertes en temps opportun", "description": "Envoyez des notifications automatiques pour divers événements, annonces et mises à jour. Personnalisez les paramètres de notification en fonction des rôles des utilisateurs."}], "pricePerMonth": "${price}/mois", "totalPrice": "Prix Total : ${price}/mois", "buttons": {"addToCart": "<PERSON><PERSON><PERSON> au Panier", "remove": "<PERSON><PERSON><PERSON>", "submit": "Soumettre la Sélection"}}, "new-password": {"title": {"blue-word": "Définir un nouveau", "main": "MOT DE PASSE", "description": "Entrez votre nouveau mot de passe et confirmez-le"}, "form": {"inputs": {"new-password": "Nouveau Mot de Passe", "confirm-password": "Confirm<PERSON> le Mot de Passe"}, "button": {"submit": "Définir un Nouveau Mot de Passe"}, "back-to-login": "Retour à la connexion"}, "errors": {"passwords-not-match": "Les mots de passe ne correspondent pas", "submission-error": "Erreur lors de la soumission du formulaire"}, "success": {"message": "Mot de passe mis à jour avec succès"}}, "main-workspace": {"services": {"title": "Services", "items": {"quizz": "Générateur de Quiz", "storage": "Stockage de Données", "api": "Services API", "resources": "Ressources", "deployments": "Déploiements", "more": "Plus de services"}}, "resources": {"title": "Ressources", "table": {"headers": {"name": "Nom", "type": "Type", "lastViewed": "Dernière Consultation"}, "timeFormats": {"weeks": "il y a {{count}} semaines", "month": "il y a un mois"}}}}, "login": {"welcome": {"blue-word": "Bienvenue", "title": "RETOUR", "description": "Bienvenue sur le tableau de bord de JeridSchool."}, "form": {"inputs": {"email": "Email", "password": "<PERSON><PERSON>"}, "remember-me": "Se souvenir de moi", "forgot-password": "Mot de passe oublié ?", "buttons": {"sign-in": "Se Connecter", "signing-in": "Connexion en Cours..."}, "register": {"text": "Vous n'avez pas de compte ?", "link": "S'inscrire"}}, "errors": {"invalid-credentials": "Email ou mot de passe incorrect. Veuillez réessayer.", "default": "Une erreur est survenue lors de la connexion. Veuillez réessayer."}, "divider": "Ou continuer avec"}, "join": {"title": "Rejoignez-Nous", "description": "Fournissez vos détails pour rejoindre notre communauté.", "form": {"inputs": {"fullname": {"label": "Nom Co<PERSON>t", "placeholder": "Nom Co<PERSON>t"}, "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON>"}, "schoolname": {"label": "Nom de l'École", "placeholder": "Nom de l'École"}, "phonenumber": {"label": "Numéro de Téléphone", "placeholder": "Numéro de Téléphone"}}, "buttons": {"submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en Cours..."}}, "alerts": {"success": "Formulaire soumis avec succès !", "error": "Échec de la soumission du formulaire. Veuillez réessayer."}}, "forgot-password": {"title": {"blue-word": "<PERSON><PERSON>", "main": "<PERSON><PERSON><PERSON><PERSON>", "description": "Entrez votre email pour réinitialiser votre mot de passe"}, "form": {"inputs": {"email": {"placeholder": "<PERSON><PERSON><PERSON> email"}}, "buttons": {"reset": "Réinitialiser le Mot de Passe", "sending": "Envoi en Cours..."}, "back-to-login": "Retour à la connexion"}, "alerts": {"success": "Lien de réinitialisation envoyé avec succès. Veuillez vérifier votre email.", "error": {"default": "Échec de l'envoi du lien de réinitialisation. Veuillez réessayer.", "network": "Erreur réseau. Veuillez vérifier votre connexion internet et réessayer.", "timeout": "Délai de connexion dépassé. Le serveur met trop de temps à répondre. Veuillez réessayer plus tard."}}}, "demo": {"header": {"backToHome": "Retour à l'Accueil"}, "hero": {"title": "Planifiez une <span>Démo</span> pour Voir JeridSchool en Action", "description": "Découvrez comment JeridSchool peut transformer les opérations de votre école en planifiant une démonstration personnalisée. Expérimentez directement comment JeridSchool simplifie la gestion et améliore l'efficacité pour les administrateurs, enseignants, parents et étudiants."}, "form": {"title": "Réservez Votre Démonstration Personnalisée", "fields": {"firstName": {"label": "Prénom*"}, "lastName": {"label": "Nom*"}, "schoolName": {"label": "Nom de l'École/Institution*"}, "email": {"label": "<PERSON><PERSON><PERSON>*"}, "phone": {"label": "Numéro de Téléphone"}, "enrollmentCount": {"label": "Taille de l'Effectif*", "options": {"placeholder": "Sélectionnez la taille de l'effectif", "small": "1-100 étudiants", "medium": "101-500 étudiants", "large": "501-1000 étudiants", "extraLarge": "1001+ étudiants"}}, "preferredDate": {"label": "Date Préférée*"}, "preferredTime": {"label": "<PERSON>ure Préférée*", "options": {"placeholder": "Sélectionnez une heure", "times": ["9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00"]}}, "message": {"label": "Informations Complémentaires (Optionnel)", "placeholder": "Faites-nous part des fonctionnalités spécifiques qui vous intéressent ou des questions que vous avez."}}, "buttons": {"submit": "Planifier Ma <PERSON>", "processing": "Traitement en Cours..."}, "legalText": "En soumettant ce formulaire, vous acceptez nos <a>Conditions d'Utilisation</a> et notre <a>Politique de Confidentialité</a>."}, "success": {"title": "<PERSON><PERSON><PERSON> !", "message": "Votre demande de démonstration a été soumise avec succès. Nous vous contacterons bientôt pour confirmer votre rendez-vous.", "emailNote": "Vérifiez votre email pour une confirmation avec tous les détails."}, "infoPanel": {"title": "À Quoi S'attendre Lors de Votre Démonstration", "items": [{"title": "Session Personnal<PERSON><PERSON> de 30 Minutes", "description": "Une démonstration ciblée adaptée aux besoins et défis spécifiques de votre école."}, {"title": "Visite Guidée par un Expert", "description": "Nos spécialistes en éducation vous guideront à travers les fonctionnalités de JeridSchool et répondront à vos questions."}, {"title": "Solutions Personnalisées", "description": "Voyez comment JeridSchool peut être configuré pour répondre aux exigences uniques de votre institution."}, {"title": "<PERSON><PERSON><PERSON> de Mise en Œuvre", "description": "Apprenez-en plus sur notre processus d'intégration et la rapidité avec laquelle vous pouvez être opérationnel."}], "contact": {"title": "Des Questions ?", "description": "Si vous avez besoin d'une assistance immédiate ou avez des questions avant de planifier une démonstration, notre équipe est là pour vous aider.", "buttons": {"email": "Nous Envoyer un Email", "call": "<PERSON><PERSON>"}}}, "testimonials": {"items": [{"quote": "La démonstration a été extrêmement utile. Elle nous a montré exactement comment JeridSchool pouvait résoudre nos problèmes d'emploi du temps.", "name": "<PERSON>", "title": "Principale, Lycée Lincoln"}, {"quote": "J'ai été impressionné par l'intuitivité de la plateforme. Après la démonstration, nous savions que c'était la solution dont nous avions besoin.", "name": "<PERSON>", "title": "Directeur IT, Académie Westfield"}, {"quote": "La démonstration personnalisée a abordé toutes nos préoccupations concernant la transition vers un nouveau système. La mise en œuvre s'est déroulée sans problème.", "name": "<PERSON><PERSON>", "title": "Administratrice, École Primaire Oakridge"}]}, "footer": {"tagline": "Transformer la gestion de l'éducation avec des outils alimentés par l'IA", "links": {"terms": "Conditions", "privacy": "Confidentialité", "contact": "Contact"}, "copyright": "© {year} JeridSchool. Tous droits réservés."}, "errors": {"submission": "Échec de la soumission du formulaire. Veuillez réessayer."}}, "contactUs": {"title": "Contactez-Nous", "description": "Un problème technique ? Besoin de détails ? Faites-le nous savoir.", "form": {"fields": {"email": {"placeholder": "Votre email"}, "subject": {"placeholder": "Sujet"}, "message": {"placeholder": "Votre message"}}, "button": {"submit": "Envoyer le <PERSON>", "sending": "Envoi en Cours..."}}, "alerts": {"success": "Message envoyé avec succès ! Nous vous contacterons bient<PERSON>t.", "error": "Échec de l'envoi du message. Veuillez réessayer."}, "contactInfo": {"title": "Contactez-Nous", "address": "Tu<PERSON>, Tunisie", "email": "<EMAIL>", "website": "www.jeridschool.tech"}}, "chat": {"backToDashboard": "Retour au Tableau de Bord", "loading": {"title": "Chargement du chat...", "timeout": {"title": "Prend plus de temps que prévu", "message": "Il pourrait y avoir un problème de connexion au serveur. <PERSON><PERSON> pouvez essayer :", "options": ["Vérifier votre connexion internet", "Actualiser la page", "Vous déconnecter et vous reconnecter"], "refreshButton": "Actualiser la Page"}, "userDataError": {"title": "Impossible de charger les données utilisateur", "message": "<PERSON><PERSON> pourrait être dû à :", "options": ["Votre session a peut-être expiré", "Il pourrait y avoir un problème avec le serveur", "Vous pourriez avoir besoin de vous reconnecter"], "loginButton": "Aller à la Connexion"}}, "sidebar": {"userRole": "({role})", "toggleButtons": {"backToChats": "Retour aux Chats", "newChat": "Nouveau Chat"}, "newChatButton": {"teacher": "Démarrer un nouveau chat avec un étudiant", "parent": "<PERSON><PERSON><PERSON>rer un nouveau chat avec un enseignant"}, "search": {"teacher": "Rechercher des étudiants...", "parent": "Rechercher des enseignants...", "filter": "Filtrer les conversations..."}, "searchResults": {"searching": "Recherche en Cours...", "noResults": "Aucun résultat trouvé", "emptyPrompt": {"teacher": "Recherchez des étudiants pour démarrer un chat", "parent": "Recherchez des enseignants pour démarrer un chat"}}, "conversations": {"empty": "Aucune conversation pour le moment", "you": "Vous : "}}, "chatArea": {"empty": {"title": "Sélectionnez une conversation", "description": "Choisissez une conversation dans la barre latérale ou démarrez-en une nouvelle"}, "messageInput": "Tapez un message...", "sending": "Envoi en Cours...", "read": "<PERSON>", "error": "<PERSON><PERSON><PERSON> d'envoi"}}, "root": {"navigation": {"home": "Accueil", "dashboard": "Tableau <PERSON>", "about": "À Propos"}, "auth": {"login": "Connexion", "getDemo": "Obt<PERSON>r une Démo", "logout": "Déconnexion"}, "branding": {"jerid": "<PERSON><PERSON>", "school": "School", "alpha": "Alpha"}, "subdomain": {"notFound": {"title": "École Non Trouvée", "message": "Nous n'avons pas pu déterminer l'école à partir du sous-domaine."}, "loading": "Chargement des informations de l'école...", "error": {"title": "Erreur de Chargement de l'École", "message": "Nous n'avons pas trouvé l'école que vous recherchez.", "debugInfo": {"title": "Informations de Débogage :", "requestedSchool": "École demandée :", "subdomainAccess": "Accès par sous-domaine :"}}}}, "super-admin-folder": {"database": {"navigation": {"overview": "Vue d'Ensemble", "adminManagement": "Gestion des Administrateurs", "back": "Retour"}}, "etablisments": {"authRequired": {"title": "Authentification Requise", "message": "Veuillez vous connecter pour gérer les établissements."}, "loading": "Chargement des établissements...", "error": {"title": "Échec du Chargement des Établissements", "message": "Vous pouvez toujours créer un nouvel établissement en utilisant le bouton ci-dessus."}, "pageTitle": "Établissements", "createButton": "Créer un Nouvel Établissement", "emptyState": {"message": "Aucun établissement trouvé", "description": "Créez votre premier établissement pour commencer"}, "form": {"create": {"title": "C<PERSON>er un Établissement", "description": "Entrez les détails du nouvel établissement"}, "edit": {"title": "Modifier l'Établissement", "description": "Mettez à jour les informations de l'établissement"}, "fields": {"name": {"label": "Nom de l'Établissement", "placeholder": "Entrez le nom de l'établissement", "error": "Le nom est requis"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Entrez l'adresse de l'établissement", "error": "L'adresse est requise"}, "logo": {"label": "Logo de l'École"}, "url": {"label": "URL du Site Web", "placeholder": "Entrez l'URL du site web"}, "cmsContent": {"label": "Contenu CMS"}, "description": {"label": "Description de l'École", "placeholder": "Entrez une description de votre école"}, "heroImage": {"label": "Image Principale"}, "galleryImages": {"label": "Images de la Galerie"}, "contactEmail": {"label": "<PERSON><PERSON> de Contact", "placeholder": "Entrez l'email de contact"}, "contactPhone": {"label": "Téléphone de Contact", "placeholder": "Entrez le téléphone de contact"}, "socialLinks": {"label": "<PERSON><PERSON>"}, "stats": {"label": "Statistiques"}, "services": {"label": "Services", "placeholder": "Ajouter un service"}, "isActive": {"label": "Statut Actif"}}, "buttons": {"cancel": "Annuler", "save": "Enregistrer", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à Jour", "saving": "Enregistrement en Cours..."}}, "notifications": {"limitReached": {"title": "Limite <PERSON>te", "description": "Vous ne pouvez créer qu'un seul établissement"}, "userIdRequired": {"title": "<PERSON><PERSON><PERSON>", "description": "Un ID utilisateur est requis pour créer un établissement"}, "createSuccess": {"title": "Su<PERSON>ès", "description": "Établissement créé avec succès"}, "createError": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de la création de l'établissement"}, "updateSuccess": {"title": "Su<PERSON>ès", "description": "Établissement mis à jour avec succès"}, "updateError": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de la mise à jour de l'établissement"}}}, "index": {"tour": {"steps": {"welcome": {"title": "Tableau de Bord Super Admin", "content": "Bienvenue sur le Tableau de Bord Super Admin ! C'est ici que vous pouvez gérer votre système scolaire."}, "servicesOverview": {"title": "Aperçu des Services", "content": "Ceci est le tableau de bord principal où vous pouvez accéder à divers services."}, "serviceCards": {"title": "Cartes de Service", "content": "Ces cartes représentent les différents services à votre disposition. Cliquez sur n'importe quelle carte pour accéder à ce service."}, "complete": {"title": "Visite Terminée", "content": "C'est tout ! Vous connaissez maintenant les bases du tableau de bord Super Admin."}}, "buttons": {"back": "Retour", "close": "<PERSON><PERSON><PERSON>", "last": "<PERSON><PERSON><PERSON>", "next": "Suivant", "skip": "Passer"}, "restartButton": {"title": "Redémarrer la Visite"}}, "layout": {"navigation": {"superAdminService": "Service Super Admin", "cardManagement": "Gestion des Cartes"}, "currentPage": "Service Super Admin"}}}, "teacher-folder": {"database": {"navigation": {"overview": "Vue d'Ensemble", "myClasses": "Mes Classes", "myStudents": "<PERSON>s <PERSON>", "mySubjects": "<PERSON><PERSON>", "assignments": "Devoirs", "back": "Retour"}, "components": {"overview": "Vue d'Ensemble", "myClasses": "Composant Mes Classes", "myStudents": "Composant Mes Étudiants", "mySubjects": "Composant Mes Matières", "assignments": "Composant Devoirs"}}, "classes": {"title": "Mes Classes"}, "timetable-test": {"title": "Page de Test d'Emploi du Temps Enseignant"}}, "school-folder": {"$schoolName": {"loading": "Chargement des données de l'école {{schoolName}}...", "notFound": {"title": "École Non Trouvée", "message": "Nous n'avons pas trouvé l'école que vous recherchez.", "debugInfo": {"title": "Informations de Débogage :", "requestedSchool": "École demandée :", "urlParameter": "Paramètre URL :", "subdomainAccess": "Accès par sous-domaine :", "currentHostname": "Nom d'hôte actuel :", "apiUrl": "URL API :", "apiEndpoint": "Point d'accès API :"}, "buttons": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "returnHome": "Retour à l'Accueil"}}, "schoolNotFound": {"title": "École Non Trouvée", "message": "Nous n'avons pas trouvé d'école portant le nom \"{{schoolName}}\".", "debugInfo": {"title": "Informations de Débogage :", "requestedSchool": "École demandée :", "urlParameter": "Paramètre URL :", "subdomainAccess": "Accès par sous-domaine :", "currentHostname": "Nom d'hôte actuel :", "apiUrl": "URL API :", "apiEndpoint": "Point d'accès API :"}, "buttons": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "returnHome": "Retour à l'Accueil"}}, "schoolNotActive": {"title": "École Non Active", "message": "L'école \"{{schoolName}}\" n'est actuellement pas active.", "debugInfo": {"title": "Informations de Débogage :", "requestedSchool": "École demandée :", "urlParameter": "Paramètre URL :", "subdomainAccess": "Accès par sous-domaine :", "currentHostname": "Nom d'hôte actuel :", "schoolId": "ID de l'École :", "schoolName": "Nom de l'École :", "isActive": "Est Active :"}}}}, "student-folder": {"timetable": {"title": "Emploi du Temps Étudiant"}}, "public-folder": {"card": {"loading": "Chargement...", "error": {"title": "Carte d'Identité Invalide", "defaultMessage": "Cette carte d'identité n'a pas pu être trouvée ou a expiré.", "returnButton": "Retour à la Page d'Accueil"}, "card": {"header": {"official": "OFFICIEL"}, "info": {"validUntil": "<PERSON><PERSON> :", "notSpecified": "Non spécifié"}, "verification": {"button": "Vérifier l'Authenticité", "verified": "Authenticité Vérifiée", "invalid": "Carte Invalide"}, "footer": {"property": "Cette carte est la propriété de {{establishmentName}}", "returnInfo": "<PERSON> trouvée, ve<PERSON><PERSON><PERSON> la retourner au bureau d'administration", "digitalVersion": "Ceci est une version numérique de la carte d'identité.", "scanQR": "Scannez le code QR pour vérifier l'authenticité."}}}}, "ai-demo": {"title": "Démo IA - JeridSchool", "tabs": {"processing": "Traitement IA", "results": "Résultats", "flashcards": "Cartes Mémoire", "chat": "Chat IA", "debug": "Débogage"}, "processing": {"title": "Assistant d'Apprentissage Alimenté par l'IA", "description": "Transformez votre expérience d'apprentissage avec nos outils IA avancés. Téléchargez du contenu, générez des cartes mémoire et obtenez une aide instantanée.", "features": {"youtube": {"title": "Traitement YouTube", "description": "Extrayez des informations clés des vidéos éducatives"}, "upload": {"title": "Téléchargement de Fichiers", "description": "Traitez des documents et créez du matériel d'étude"}, "flashcards": {"title": "Cartes Mémoire Intelligentes", "description": "Générez automatiquement des cartes mémoire à partir de n'importe quel contenu"}, "chat": {"title": "Tuteur IA", "description": "Obtenez des réponses instantanées à vos questions"}}}, "chat": {"features": {"explanations": {"title": "Explications Détail<PERSON>", "description": "Obtenez des explications détaillées de sujets complexes"}, "quickAnswers": {"title": "Réponses Rapides", "description": "Obtenez des réponses instantanées à vos questions"}}}, "sidebar": {"processingTime": {"title": "Temps de Traitement", "estimate": "30-60s"}, "examples": {"title": "Essayez Ces Exemples", "youtube": {"title": "URLs YouTube :", "description": "Vidéos éducatives, conférences, tutoriels"}, "chat": {"title": "<PERSON><PERSON><PERSON> :", "prompts": ["Expliquez la physique quantique", "<PERSON><PERSON>ez un plan d'étude", "Aidez-moi à comprendre le calcul"]}}}}, "classes": {"title": "Classes", "welcome": "Bonjour \"/classes\" !"}, "profile": {"title": "Profil"}, "test": {"title": "<PERSON> de <PERSON>", "welcome": "Bonjour \"/test\" !"}, "admin-folder": {"timetable": {"sidebar": {"back": "Retour", "dashboard": "Tableau <PERSON>", "timetableGenerator": "Générateur d'Emploi du Temps", "viewGeneratedTimetable": "Voir l'Emploi du Temps Généré", "generateSessions": "<PERSON><PERSON><PERSON><PERSON>", "sessionHistory": "Historique des Sessions", "attendanceList": "Liste de Présence", "createFirst": "<PERSON><PERSON>er le Premier"}, "timetableLoading": "Chargement de l'Emploi du Temps...", "noTimetable": {"title": "Aucun Emploi du Temps Disponible", "message": "Veuillez d'abord générer un emploi du temps.", "button": "<PERSON><PERSON>er un Emploi du Temps"}, "toasts": {"noTimetableFound": {"title": "Aucun Emploi du Temps Trouvé", "description": "Veuillez d'abord générer un emploi du temps."}, "timetableGenerated": {"title": "Emploi du Temps Généré", "description": "Votre emploi du temps a été généré et est prêt à être consulté."}, "timetableDeleted": {"title": "Emploi du Temps Supprimé", "description": "L'emploi du temps a été supprimé de la mémoire."}, "noTimetableToDelete": {"title": "<PERSON><PERSON><PERSON>", "description": "Aucun emploi du temps à supprimer"}, "deleteError": {"title": "<PERSON><PERSON><PERSON>", "description": "Échec de la suppression de l'emploi du temps. Veuillez réessayer."}}}, "score": {"sampleData": {"subjects": {"math": "Mathématiques", "physics": "Physique", "chemistry": "<PERSON><PERSON>"}, "semesters": {"semester1": "Semestre 1", "semester2": "Semestre 2", "semester3": "Semestre 3"}}, "sidebar": {"back": "Retour", "classSelection": "Sélection de Classe", "navigation": {"scoreBySubject": "Notes par Matière", "semesterAverages": "Moyennes des Semestres"}}}, "index": {"navigation": {"adminService": "Service Admin", "classManagement": "Gestion des Classes", "teacherManagement": "Gestion des Enseignants", "studentManagement": "Gestion des Étudiants", "listEliminateStudent": "Liste des Étudiants Éliminés", "calculateStudentScore": "Calculer les Notes des Étudiants", "cardStudio": "Studio de Cartes", "userManagement": "Gestion des Utilisateurs"}}, "elimination": {"sidebar": {"back": "Retour", "navigation": {"overview": "Vue d'Ensemble", "users": "Utilisateurs", "settings": "Paramètres"}}, "components": {"settings": "Composant Paramètres"}}, "database": {"navigation": {"overview": "Vue d'Ensemble", "users": {"title": "Utilisateurs", "parentManagement": "Gestion des Parents", "teacherManagement": "Gestion des Enseignants", "studentManagement": "Gestion des Étudiants", "listEliminate": "Liste des Éliminés", "calculateScore": "Calcul des Notes"}, "establishment": {"title": "Établissement", "classManagement": "Gestion des Classes", "gradeManagement": "Gestion des Niveaux", "classroomManagement": "Gestion des Salles de Classe", "subjectManagement": "Gestion des Matières"}, "back": "Retour"}}}}}