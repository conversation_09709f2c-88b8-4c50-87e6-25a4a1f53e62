import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface TourState {
  // Establishment Tour status
  establishmentTourCompleted: boolean
  establishmentTourSkipped: boolean
  establishmentTourActive: boolean
  establishmentTourStep: number

  // Admin Timetable Tour status
  adminTimetableTourCompleted: boolean
  adminTimetableTourSkipped: boolean
  adminTimetableTourActive: boolean
  adminTimetableTourStep: number

  // Establishment Tour Actions
  startEstablishmentTour: () => void
  completeEstablishmentTour: () => void
  skipEstablishmentTour: () => void
  setEstablishmentTourStep: (step: number) => void
  resetEstablishmentTour: () => void

  // Admin Timetable Tour Actions
  startAdminTimetableTour: () => void
  completeAdminTimetableTour: () => void
  skipAdminTimetableTour: () => void
  setAdminTimetableTourStep: (step: number) => void
  resetAdminTimetableTour: () => void
}

export const useTourStore = create<TourState>()(
  persist(
    (set) => ({
      // Initial state for Establishment Tour
      establishmentTourCompleted: false,
      establishmentTourSkipped: false,
      establishmentTourActive: false,
      establishmentTourStep: 0,

      // Initial state for Admin Timetable Tour
      adminTimetableTourCompleted: false,
      adminTimetableTourSkipped: false,
      adminTimetableTourActive: false,
      adminTimetableTourStep: 0,

      // Establishment Tour Actions
      startEstablishmentTour: () =>
        set({
          establishmentTourActive: true,
          establishmentTourCompleted: false,
          establishmentTourSkipped: false,
        }),

      completeEstablishmentTour: () =>
        set({
          establishmentTourActive: false,
          establishmentTourCompleted: true,
          establishmentTourSkipped: false,
        }),

      skipEstablishmentTour: () =>
        set({
          establishmentTourActive: false,
          establishmentTourCompleted: false,
          establishmentTourSkipped: true,
        }),

      setEstablishmentTourStep: (step: number) =>
        set({
          establishmentTourStep: step,
        }),

      resetEstablishmentTour: () =>
        set({
          establishmentTourCompleted: false,
          establishmentTourSkipped: false,
          establishmentTourActive: false,
          establishmentTourStep: 0,
        }),

      // Admin Timetable Tour Actions
      startAdminTimetableTour: () =>
        set({
          adminTimetableTourActive: true,
          adminTimetableTourCompleted: false,
          adminTimetableTourSkipped: false,
        }),

      completeAdminTimetableTour: () =>
        set({
          adminTimetableTourActive: false,
          adminTimetableTourCompleted: true,
          adminTimetableTourSkipped: false,
        }),

      skipAdminTimetableTour: () =>
        set({
          adminTimetableTourActive: false,
          adminTimetableTourCompleted: false,
          adminTimetableTourSkipped: true,
        }),

      setAdminTimetableTourStep: (step: number) =>
        set({
          adminTimetableTourStep: step,
        }),

      resetAdminTimetableTour: () =>
        set({
          adminTimetableTourCompleted: false,
          adminTimetableTourSkipped: false,
          adminTimetableTourActive: false,
          adminTimetableTourStep: 0,
        }),
    }),
    {
      name: 'jerid-school-tour-storage',
    }
  )
)
