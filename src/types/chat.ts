export interface User {
  id: string
  firstname: string
  lastname: string
  role: 'Teacher' | 'Student' | 'Parent'
  avatar?: string
  email?: string
  displayName?: string
  username?: string
}

export interface Message {
  id: string
  content: string
  senderId: string
  sender?: User
  conversationId: string
  isRead: boolean
  createdAt: string
  temporary?: boolean
  error?: boolean
}

export interface Conversation {
  id: string
  name: string
  isGroupChat: boolean
  participants: User[]
  messages: Message[]
  createdAt: string
  updatedAt: string
}

export interface ChatUtils {
  formatTime: (dateString: string) => string
  formatDate: (dateString: string) => string
  getInitials: (user: User) => string
  getAvatarUrl: (user: User) => string
  getLastMessage: (conversation: Conversation) => string
  getLastMessageTime: (conversation: Conversation) => string
  getUnreadCount: (conversation: Conversation, currentUserId: string) => number
  getConversationDisplayName: (
    conversation: Conversation,
    currentUser: User
  ) => string
  getConversationInitials: (
    conversation: Conversation,
    currentUser: User
  ) => string
}

export interface ExtendedUser {
  id: string
  firstname: string
  lastname: string
  role: 'Teacher' | 'Student' | 'Parent'
  displayName?: string
  username?: string
  avatar?: string
}

export interface ExtendedConversation {
  id: string
  name?: string
  participants: ExtendedUser[]
  messages: any[]
  createdAt: string
  updatedAt: string
  createdBy: string
  isGroupChat?: boolean
}
