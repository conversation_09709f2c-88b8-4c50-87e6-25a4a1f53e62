export interface TimetableAssignment {
  subject: string
  teacher: string
  teacherID: number
  salle?: string
  time: string
  day: string
  class: string
  group1?: {
    subject: string
    teacher: string
    teacherID: number
    salle?: string
  } | null
  group2?: {
    subject: string
    teacher: string
    teacherID: number
    salle?: string
  } | null
}

export interface AvailableSlot {
  day: string
  time: string
  available_rooms: string[]
}

export interface UnscheduledSubject {
  class: string
  subject: string
  required_hours: number
  scheduled_hours: number
  uncompleted_hours: number
  teacherID: number
  teacherName: string
  available_slots: AvailableSlot[]
}

export interface ClassSchedule {
  class: string
  [day: string]: any[] | string // Can be either an array of assignments or the class name
}

export interface TimetableData {
  scheduleData: ClassSchedule[]
  schoolName?: string
  address?: string
  name?: string
  // Fields for timetable data storage in port 3000
  originalInputData?: any // Original data sent to port 8000
  inputData?: any // Data used for generation at port 8000
  port8000Data?: any // Alternative field for data sent to port 8000
  description?: string
  academicYear?: string
  isActive?: boolean
  etablissementId?: string
  unscheduledAssignments?: TimetableAssignment[] // Added to fix TypeScript errors
}

export interface TimetableAnalysisResponse {
  unscheduledSubjects: UnscheduledSubject[]
}
