/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as WhiteboardImport } from './routes/whiteboard'
import { Route as UnauthorizedImport } from './routes/unauthorized'
import { Route as TestImport } from './routes/test'
import { Route as SubdomainWwwTestImport } from './routes/subdomain-www-test'
import { Route as SubdomainTestImport } from './routes/subdomain-test'
import { Route as SettingsImport } from './routes/settings'
import { Route as SchoolsImport } from './routes/schools'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RegisterImport } from './routes/register'
import { Route as QuizzImport } from './routes/quizz'
import { Route as ProfileImport } from './routes/profile'
import { Route as PricingImport } from './routes/pricing'
import { Route as ParentImport } from './routes/parent'
import { Route as OffersImport } from './routes/offers'
import { Route as NotesImport } from './routes/notes'
import { Route as NewPasswordImport } from './routes/new-password'
import { Route as MainWorkspaceImport } from './routes/main-workspace'
import { Route as LoginImport } from './routes/login'
import { Route as JoinImport } from './routes/join'
import { Route as ForgotPasswordImport } from './routes/forgot-password'
import { Route as FocusRoomImport } from './routes/focus-room'
import { Route as FlashcardsImport } from './routes/flashcards'
import { Route as DemoImport } from './routes/demo'
import { Route as DebugTokenImport } from './routes/debug-token'
import { Route as ContactUsImport } from './routes/contactUs'
import { Route as ClassesImport } from './routes/classes'
import { Route as ChatImport } from './routes/chat'
import { Route as CardTemplateImport } from './routes/card-template'
import { Route as CardImport } from './routes/card'
import { Route as AiDemoImport } from './routes/ai-demo'
import { Route as AboutImport } from './routes/about'
import { Route as R404Import } from './routes/404'
import { Route as IndexImport } from './routes/index'
import { Route as TeacherIndexImport } from './routes/teacher/index'
import { Route as SuperadminIndexImport } from './routes/super_admin/index'
import { Route as StudentIndexImport } from './routes/student/index'
import { Route as B2cstudentIndexImport } from './routes/b2c_student/index'
import { Route as AdminIndexImport } from './routes/admin/index'
import { Route as TeacherTimetableTestImport } from './routes/teacher/timetable-test'
import { Route as TeacherTimetableImport } from './routes/teacher/timetable'
import { Route as TeacherMarkabsentImport } from './routes/teacher/markabsent'
import { Route as TeacherExamImport } from './routes/teacher/exam'
import { Route as TeacherDatabaseImport } from './routes/teacher/database'
import { Route as TeacherClassesImport } from './routes/teacher/classes'
import { Route as TeacherAttendanceImport } from './routes/teacher/attendance'
import { Route as TeacherAttandencehistoryImport } from './routes/teacher/attandencehistory'
import { Route as SuperadminEtablismentsImport } from './routes/super_admin/etablisments'
import { Route as SuperadminDatabaseImport } from './routes/super_admin/database'
import { Route as StudentVirtualClassroomImport } from './routes/student/virtual-classroom'
import { Route as StudentTimetableImport } from './routes/student/timetable'
import { Route as StudentFlashcardsImport } from './routes/student/flashcards'
import { Route as StudentExamImport } from './routes/student/exam'
import { Route as StudentAllInOneImport } from './routes/student/all-in-one'
import { Route as SchoolSchoolNameImport } from './routes/school/$schoolName'
import { Route as PublicCardImport } from './routes/public/card'
import { Route as AdminTimetableImport } from './routes/admin/timetable'
import { Route as AdminScoreImport } from './routes/admin/score'
import { Route as AdminGradeImport } from './routes/admin/grade'
import { Route as AdminExamImport } from './routes/admin/exam'
import { Route as AdminEliminationImport } from './routes/admin/elimination'
import { Route as AdminDatabaseImport } from './routes/admin/database'
import { Route as AdminClassroomImport } from './routes/admin/classroom'
import { Route as AdminAttendanceImport } from './routes/admin/attendance'
import { Route as TeacherAutoCorrectorIndexImport } from './routes/teacher/auto-corrector/index'
import { Route as StudentFlashcardsIndexImport } from './routes/student/flashcards/index'
import { Route as StudentAttendanceIndexImport } from './routes/student/attendance/index'
import { Route as AdminDatabaseIndexImport } from './routes/admin/database/index'
import { Route as SuperadminEditEstablishmentIdImport } from './routes/super_admin/edit-establishment/$id'
import { Route as SuperadminDirectUpdateEstablishmentIdImport } from './routes/super_admin/direct-update-establishment/$id'
import { Route as PublicCardIdImport } from './routes/public/card/$id'
import { Route as AdminDatabaseStudentGradesImport } from './routes/admin/database/student-grades'
import { Route as AdminDatabaseStudentScoreDetailsStudentIdImport } from './routes/admin/database/student-score-details.$studentId'

// Create/Update Routes

const WhiteboardRoute = WhiteboardImport.update({
  id: '/whiteboard',
  path: '/whiteboard',
  getParentRoute: () => rootRoute,
} as any)

const UnauthorizedRoute = UnauthorizedImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRoute,
} as any)

const TestRoute = TestImport.update({
  id: '/test',
  path: '/test',
  getParentRoute: () => rootRoute,
} as any)

const SubdomainWwwTestRoute = SubdomainWwwTestImport.update({
  id: '/subdomain-www-test',
  path: '/subdomain-www-test',
  getParentRoute: () => rootRoute,
} as any)

const SubdomainTestRoute = SubdomainTestImport.update({
  id: '/subdomain-test',
  path: '/subdomain-test',
  getParentRoute: () => rootRoute,
} as any)

const SettingsRoute = SettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRoute,
} as any)

const SchoolsRoute = SchoolsImport.update({
  id: '/schools',
  path: '/schools',
  getParentRoute: () => rootRoute,
} as any)

const ResetPasswordRoute = ResetPasswordImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const RegisterRoute = RegisterImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRoute,
} as any)

const QuizzRoute = QuizzImport.update({
  id: '/quizz',
  path: '/quizz',
  getParentRoute: () => rootRoute,
} as any)

const ProfileRoute = ProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => rootRoute,
} as any)

const PricingRoute = PricingImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => rootRoute,
} as any)

const ParentRoute = ParentImport.update({
  id: '/parent',
  path: '/parent',
  getParentRoute: () => rootRoute,
} as any)

const OffersRoute = OffersImport.update({
  id: '/offers',
  path: '/offers',
  getParentRoute: () => rootRoute,
} as any)

const NotesRoute = NotesImport.update({
  id: '/notes',
  path: '/notes',
  getParentRoute: () => rootRoute,
} as any)

const NewPasswordRoute = NewPasswordImport.update({
  id: '/new-password',
  path: '/new-password',
  getParentRoute: () => rootRoute,
} as any)

const MainWorkspaceRoute = MainWorkspaceImport.update({
  id: '/main-workspace',
  path: '/main-workspace',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const JoinRoute = JoinImport.update({
  id: '/join',
  path: '/join',
  getParentRoute: () => rootRoute,
} as any)

const ForgotPasswordRoute = ForgotPasswordImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const FocusRoomRoute = FocusRoomImport.update({
  id: '/focus-room',
  path: '/focus-room',
  getParentRoute: () => rootRoute,
} as any)

const FlashcardsRoute = FlashcardsImport.update({
  id: '/flashcards',
  path: '/flashcards',
  getParentRoute: () => rootRoute,
} as any)

const DemoRoute = DemoImport.update({
  id: '/demo',
  path: '/demo',
  getParentRoute: () => rootRoute,
} as any)

const DebugTokenRoute = DebugTokenImport.update({
  id: '/debug-token',
  path: '/debug-token',
  getParentRoute: () => rootRoute,
} as any)

const ContactUsRoute = ContactUsImport.update({
  id: '/contactUs',
  path: '/contactUs',
  getParentRoute: () => rootRoute,
} as any)

const ClassesRoute = ClassesImport.update({
  id: '/classes',
  path: '/classes',
  getParentRoute: () => rootRoute,
} as any)

const ChatRoute = ChatImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => rootRoute,
} as any)

const CardTemplateRoute = CardTemplateImport.update({
  id: '/card-template',
  path: '/card-template',
  getParentRoute: () => rootRoute,
} as any)

const CardRoute = CardImport.update({
  id: '/card',
  path: '/card',
  getParentRoute: () => rootRoute,
} as any)

const AiDemoRoute = AiDemoImport.update({
  id: '/ai-demo',
  path: '/ai-demo',
  getParentRoute: () => rootRoute,
} as any)

const AboutRoute = AboutImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRoute,
} as any)

const R404Route = R404Import.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const TeacherIndexRoute = TeacherIndexImport.update({
  id: '/teacher/',
  path: '/teacher/',
  getParentRoute: () => rootRoute,
} as any)

const SuperadminIndexRoute = SuperadminIndexImport.update({
  id: '/super_admin/',
  path: '/super_admin/',
  getParentRoute: () => rootRoute,
} as any)

const StudentIndexRoute = StudentIndexImport.update({
  id: '/student/',
  path: '/student/',
  getParentRoute: () => rootRoute,
} as any)

const B2cstudentIndexRoute = B2cstudentIndexImport.update({
  id: '/b2c_student/',
  path: '/b2c_student/',
  getParentRoute: () => rootRoute,
} as any)

const AdminIndexRoute = AdminIndexImport.update({
  id: '/admin/',
  path: '/admin/',
  getParentRoute: () => rootRoute,
} as any)

const TeacherTimetableTestRoute = TeacherTimetableTestImport.update({
  id: '/teacher/timetable-test',
  path: '/teacher/timetable-test',
  getParentRoute: () => rootRoute,
} as any)

const TeacherTimetableRoute = TeacherTimetableImport.update({
  id: '/teacher/timetable',
  path: '/teacher/timetable',
  getParentRoute: () => rootRoute,
} as any)

const TeacherMarkabsentRoute = TeacherMarkabsentImport.update({
  id: '/teacher/markabsent',
  path: '/teacher/markabsent',
  getParentRoute: () => rootRoute,
} as any)

const TeacherExamRoute = TeacherExamImport.update({
  id: '/teacher/exam',
  path: '/teacher/exam',
  getParentRoute: () => rootRoute,
} as any)

const TeacherDatabaseRoute = TeacherDatabaseImport.update({
  id: '/teacher/database',
  path: '/teacher/database',
  getParentRoute: () => rootRoute,
} as any)

const TeacherClassesRoute = TeacherClassesImport.update({
  id: '/teacher/classes',
  path: '/teacher/classes',
  getParentRoute: () => rootRoute,
} as any)

const TeacherAttendanceRoute = TeacherAttendanceImport.update({
  id: '/teacher/attendance',
  path: '/teacher/attendance',
  getParentRoute: () => rootRoute,
} as any)

const TeacherAttandencehistoryRoute = TeacherAttandencehistoryImport.update({
  id: '/teacher/attandencehistory',
  path: '/teacher/attandencehistory',
  getParentRoute: () => rootRoute,
} as any)

const SuperadminEtablismentsRoute = SuperadminEtablismentsImport.update({
  id: '/super_admin/etablisments',
  path: '/super_admin/etablisments',
  getParentRoute: () => rootRoute,
} as any)

const SuperadminDatabaseRoute = SuperadminDatabaseImport.update({
  id: '/super_admin/database',
  path: '/super_admin/database',
  getParentRoute: () => rootRoute,
} as any)

const StudentVirtualClassroomRoute = StudentVirtualClassroomImport.update({
  id: '/student/virtual-classroom',
  path: '/student/virtual-classroom',
  getParentRoute: () => rootRoute,
} as any)

const StudentTimetableRoute = StudentTimetableImport.update({
  id: '/student/timetable',
  path: '/student/timetable',
  getParentRoute: () => rootRoute,
} as any)

const StudentFlashcardsRoute = StudentFlashcardsImport.update({
  id: '/student/flashcards',
  path: '/student/flashcards',
  getParentRoute: () => rootRoute,
} as any)

const StudentExamRoute = StudentExamImport.update({
  id: '/student/exam',
  path: '/student/exam',
  getParentRoute: () => rootRoute,
} as any)

const StudentAllInOneRoute = StudentAllInOneImport.update({
  id: '/student/all-in-one',
  path: '/student/all-in-one',
  getParentRoute: () => rootRoute,
} as any)

const SchoolSchoolNameRoute = SchoolSchoolNameImport.update({
  id: '/school/$schoolName',
  path: '/school/$schoolName',
  getParentRoute: () => rootRoute,
} as any)

const PublicCardRoute = PublicCardImport.update({
  id: '/public/card',
  path: '/public/card',
  getParentRoute: () => rootRoute,
} as any)

const AdminTimetableRoute = AdminTimetableImport.update({
  id: '/admin/timetable',
  path: '/admin/timetable',
  getParentRoute: () => rootRoute,
} as any)

const AdminScoreRoute = AdminScoreImport.update({
  id: '/admin/score',
  path: '/admin/score',
  getParentRoute: () => rootRoute,
} as any)

const AdminGradeRoute = AdminGradeImport.update({
  id: '/admin/grade',
  path: '/admin/grade',
  getParentRoute: () => rootRoute,
} as any)

const AdminExamRoute = AdminExamImport.update({
  id: '/admin/exam',
  path: '/admin/exam',
  getParentRoute: () => rootRoute,
} as any)

const AdminEliminationRoute = AdminEliminationImport.update({
  id: '/admin/elimination',
  path: '/admin/elimination',
  getParentRoute: () => rootRoute,
} as any)

const AdminDatabaseRoute = AdminDatabaseImport.update({
  id: '/admin/database',
  path: '/admin/database',
  getParentRoute: () => rootRoute,
} as any)

const AdminClassroomRoute = AdminClassroomImport.update({
  id: '/admin/classroom',
  path: '/admin/classroom',
  getParentRoute: () => rootRoute,
} as any)

const AdminAttendanceRoute = AdminAttendanceImport.update({
  id: '/admin/attendance',
  path: '/admin/attendance',
  getParentRoute: () => rootRoute,
} as any)

const TeacherAutoCorrectorIndexRoute = TeacherAutoCorrectorIndexImport.update({
  id: '/teacher/auto-corrector/',
  path: '/teacher/auto-corrector/',
  getParentRoute: () => rootRoute,
} as any)

const StudentFlashcardsIndexRoute = StudentFlashcardsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => StudentFlashcardsRoute,
} as any)

const StudentAttendanceIndexRoute = StudentAttendanceIndexImport.update({
  id: '/student/attendance/',
  path: '/student/attendance/',
  getParentRoute: () => rootRoute,
} as any)

const AdminDatabaseIndexRoute = AdminDatabaseIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AdminDatabaseRoute,
} as any)

const SuperadminEditEstablishmentIdRoute =
  SuperadminEditEstablishmentIdImport.update({
    id: '/super_admin/edit-establishment/$id',
    path: '/super_admin/edit-establishment/$id',
    getParentRoute: () => rootRoute,
  } as any)

const SuperadminDirectUpdateEstablishmentIdRoute =
  SuperadminDirectUpdateEstablishmentIdImport.update({
    id: '/super_admin/direct-update-establishment/$id',
    path: '/super_admin/direct-update-establishment/$id',
    getParentRoute: () => rootRoute,
  } as any)

const PublicCardIdRoute = PublicCardIdImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => PublicCardRoute,
} as any)

const AdminDatabaseStudentGradesRoute = AdminDatabaseStudentGradesImport.update(
  {
    id: '/student-grades',
    path: '/student-grades',
    getParentRoute: () => AdminDatabaseRoute,
  } as any,
)

const AdminDatabaseStudentScoreDetailsStudentIdRoute =
  AdminDatabaseStudentScoreDetailsStudentIdImport.update({
    id: '/student-score-details/$studentId',
    path: '/student-score-details/$studentId',
    getParentRoute: () => AdminDatabaseRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/404': {
      id: '/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof R404Import
      parentRoute: typeof rootRoute
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutImport
      parentRoute: typeof rootRoute
    }
    '/ai-demo': {
      id: '/ai-demo'
      path: '/ai-demo'
      fullPath: '/ai-demo'
      preLoaderRoute: typeof AiDemoImport
      parentRoute: typeof rootRoute
    }
    '/card': {
      id: '/card'
      path: '/card'
      fullPath: '/card'
      preLoaderRoute: typeof CardImport
      parentRoute: typeof rootRoute
    }
    '/card-template': {
      id: '/card-template'
      path: '/card-template'
      fullPath: '/card-template'
      preLoaderRoute: typeof CardTemplateImport
      parentRoute: typeof rootRoute
    }
    '/chat': {
      id: '/chat'
      path: '/chat'
      fullPath: '/chat'
      preLoaderRoute: typeof ChatImport
      parentRoute: typeof rootRoute
    }
    '/classes': {
      id: '/classes'
      path: '/classes'
      fullPath: '/classes'
      preLoaderRoute: typeof ClassesImport
      parentRoute: typeof rootRoute
    }
    '/contactUs': {
      id: '/contactUs'
      path: '/contactUs'
      fullPath: '/contactUs'
      preLoaderRoute: typeof ContactUsImport
      parentRoute: typeof rootRoute
    }
    '/debug-token': {
      id: '/debug-token'
      path: '/debug-token'
      fullPath: '/debug-token'
      preLoaderRoute: typeof DebugTokenImport
      parentRoute: typeof rootRoute
    }
    '/demo': {
      id: '/demo'
      path: '/demo'
      fullPath: '/demo'
      preLoaderRoute: typeof DemoImport
      parentRoute: typeof rootRoute
    }
    '/flashcards': {
      id: '/flashcards'
      path: '/flashcards'
      fullPath: '/flashcards'
      preLoaderRoute: typeof FlashcardsImport
      parentRoute: typeof rootRoute
    }
    '/focus-room': {
      id: '/focus-room'
      path: '/focus-room'
      fullPath: '/focus-room'
      preLoaderRoute: typeof FocusRoomImport
      parentRoute: typeof rootRoute
    }
    '/forgot-password': {
      id: '/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof ForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/join': {
      id: '/join'
      path: '/join'
      fullPath: '/join'
      preLoaderRoute: typeof JoinImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/main-workspace': {
      id: '/main-workspace'
      path: '/main-workspace'
      fullPath: '/main-workspace'
      preLoaderRoute: typeof MainWorkspaceImport
      parentRoute: typeof rootRoute
    }
    '/new-password': {
      id: '/new-password'
      path: '/new-password'
      fullPath: '/new-password'
      preLoaderRoute: typeof NewPasswordImport
      parentRoute: typeof rootRoute
    }
    '/notes': {
      id: '/notes'
      path: '/notes'
      fullPath: '/notes'
      preLoaderRoute: typeof NotesImport
      parentRoute: typeof rootRoute
    }
    '/offers': {
      id: '/offers'
      path: '/offers'
      fullPath: '/offers'
      preLoaderRoute: typeof OffersImport
      parentRoute: typeof rootRoute
    }
    '/parent': {
      id: '/parent'
      path: '/parent'
      fullPath: '/parent'
      preLoaderRoute: typeof ParentImport
      parentRoute: typeof rootRoute
    }
    '/pricing': {
      id: '/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof PricingImport
      parentRoute: typeof rootRoute
    }
    '/profile': {
      id: '/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof ProfileImport
      parentRoute: typeof rootRoute
    }
    '/quizz': {
      id: '/quizz'
      path: '/quizz'
      fullPath: '/quizz'
      preLoaderRoute: typeof QuizzImport
      parentRoute: typeof rootRoute
    }
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterImport
      parentRoute: typeof rootRoute
    }
    '/reset-password': {
      id: '/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof ResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/schools': {
      id: '/schools'
      path: '/schools'
      fullPath: '/schools'
      preLoaderRoute: typeof SchoolsImport
      parentRoute: typeof rootRoute
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsImport
      parentRoute: typeof rootRoute
    }
    '/subdomain-test': {
      id: '/subdomain-test'
      path: '/subdomain-test'
      fullPath: '/subdomain-test'
      preLoaderRoute: typeof SubdomainTestImport
      parentRoute: typeof rootRoute
    }
    '/subdomain-www-test': {
      id: '/subdomain-www-test'
      path: '/subdomain-www-test'
      fullPath: '/subdomain-www-test'
      preLoaderRoute: typeof SubdomainWwwTestImport
      parentRoute: typeof rootRoute
    }
    '/test': {
      id: '/test'
      path: '/test'
      fullPath: '/test'
      preLoaderRoute: typeof TestImport
      parentRoute: typeof rootRoute
    }
    '/unauthorized': {
      id: '/unauthorized'
      path: '/unauthorized'
      fullPath: '/unauthorized'
      preLoaderRoute: typeof UnauthorizedImport
      parentRoute: typeof rootRoute
    }
    '/whiteboard': {
      id: '/whiteboard'
      path: '/whiteboard'
      fullPath: '/whiteboard'
      preLoaderRoute: typeof WhiteboardImport
      parentRoute: typeof rootRoute
    }
    '/admin/attendance': {
      id: '/admin/attendance'
      path: '/admin/attendance'
      fullPath: '/admin/attendance'
      preLoaderRoute: typeof AdminAttendanceImport
      parentRoute: typeof rootRoute
    }
    '/admin/classroom': {
      id: '/admin/classroom'
      path: '/admin/classroom'
      fullPath: '/admin/classroom'
      preLoaderRoute: typeof AdminClassroomImport
      parentRoute: typeof rootRoute
    }
    '/admin/database': {
      id: '/admin/database'
      path: '/admin/database'
      fullPath: '/admin/database'
      preLoaderRoute: typeof AdminDatabaseImport
      parentRoute: typeof rootRoute
    }
    '/admin/elimination': {
      id: '/admin/elimination'
      path: '/admin/elimination'
      fullPath: '/admin/elimination'
      preLoaderRoute: typeof AdminEliminationImport
      parentRoute: typeof rootRoute
    }
    '/admin/exam': {
      id: '/admin/exam'
      path: '/admin/exam'
      fullPath: '/admin/exam'
      preLoaderRoute: typeof AdminExamImport
      parentRoute: typeof rootRoute
    }
    '/admin/grade': {
      id: '/admin/grade'
      path: '/admin/grade'
      fullPath: '/admin/grade'
      preLoaderRoute: typeof AdminGradeImport
      parentRoute: typeof rootRoute
    }
    '/admin/score': {
      id: '/admin/score'
      path: '/admin/score'
      fullPath: '/admin/score'
      preLoaderRoute: typeof AdminScoreImport
      parentRoute: typeof rootRoute
    }
    '/admin/timetable': {
      id: '/admin/timetable'
      path: '/admin/timetable'
      fullPath: '/admin/timetable'
      preLoaderRoute: typeof AdminTimetableImport
      parentRoute: typeof rootRoute
    }
    '/public/card': {
      id: '/public/card'
      path: '/public/card'
      fullPath: '/public/card'
      preLoaderRoute: typeof PublicCardImport
      parentRoute: typeof rootRoute
    }
    '/school/$schoolName': {
      id: '/school/$schoolName'
      path: '/school/$schoolName'
      fullPath: '/school/$schoolName'
      preLoaderRoute: typeof SchoolSchoolNameImport
      parentRoute: typeof rootRoute
    }
    '/student/all-in-one': {
      id: '/student/all-in-one'
      path: '/student/all-in-one'
      fullPath: '/student/all-in-one'
      preLoaderRoute: typeof StudentAllInOneImport
      parentRoute: typeof rootRoute
    }
    '/student/exam': {
      id: '/student/exam'
      path: '/student/exam'
      fullPath: '/student/exam'
      preLoaderRoute: typeof StudentExamImport
      parentRoute: typeof rootRoute
    }
    '/student/flashcards': {
      id: '/student/flashcards'
      path: '/student/flashcards'
      fullPath: '/student/flashcards'
      preLoaderRoute: typeof StudentFlashcardsImport
      parentRoute: typeof rootRoute
    }
    '/student/timetable': {
      id: '/student/timetable'
      path: '/student/timetable'
      fullPath: '/student/timetable'
      preLoaderRoute: typeof StudentTimetableImport
      parentRoute: typeof rootRoute
    }
    '/student/virtual-classroom': {
      id: '/student/virtual-classroom'
      path: '/student/virtual-classroom'
      fullPath: '/student/virtual-classroom'
      preLoaderRoute: typeof StudentVirtualClassroomImport
      parentRoute: typeof rootRoute
    }
    '/super_admin/database': {
      id: '/super_admin/database'
      path: '/super_admin/database'
      fullPath: '/super_admin/database'
      preLoaderRoute: typeof SuperadminDatabaseImport
      parentRoute: typeof rootRoute
    }
    '/super_admin/etablisments': {
      id: '/super_admin/etablisments'
      path: '/super_admin/etablisments'
      fullPath: '/super_admin/etablisments'
      preLoaderRoute: typeof SuperadminEtablismentsImport
      parentRoute: typeof rootRoute
    }
    '/teacher/attandencehistory': {
      id: '/teacher/attandencehistory'
      path: '/teacher/attandencehistory'
      fullPath: '/teacher/attandencehistory'
      preLoaderRoute: typeof TeacherAttandencehistoryImport
      parentRoute: typeof rootRoute
    }
    '/teacher/attendance': {
      id: '/teacher/attendance'
      path: '/teacher/attendance'
      fullPath: '/teacher/attendance'
      preLoaderRoute: typeof TeacherAttendanceImport
      parentRoute: typeof rootRoute
    }
    '/teacher/classes': {
      id: '/teacher/classes'
      path: '/teacher/classes'
      fullPath: '/teacher/classes'
      preLoaderRoute: typeof TeacherClassesImport
      parentRoute: typeof rootRoute
    }
    '/teacher/database': {
      id: '/teacher/database'
      path: '/teacher/database'
      fullPath: '/teacher/database'
      preLoaderRoute: typeof TeacherDatabaseImport
      parentRoute: typeof rootRoute
    }
    '/teacher/exam': {
      id: '/teacher/exam'
      path: '/teacher/exam'
      fullPath: '/teacher/exam'
      preLoaderRoute: typeof TeacherExamImport
      parentRoute: typeof rootRoute
    }
    '/teacher/markabsent': {
      id: '/teacher/markabsent'
      path: '/teacher/markabsent'
      fullPath: '/teacher/markabsent'
      preLoaderRoute: typeof TeacherMarkabsentImport
      parentRoute: typeof rootRoute
    }
    '/teacher/timetable': {
      id: '/teacher/timetable'
      path: '/teacher/timetable'
      fullPath: '/teacher/timetable'
      preLoaderRoute: typeof TeacherTimetableImport
      parentRoute: typeof rootRoute
    }
    '/teacher/timetable-test': {
      id: '/teacher/timetable-test'
      path: '/teacher/timetable-test'
      fullPath: '/teacher/timetable-test'
      preLoaderRoute: typeof TeacherTimetableTestImport
      parentRoute: typeof rootRoute
    }
    '/admin/': {
      id: '/admin/'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminIndexImport
      parentRoute: typeof rootRoute
    }
    '/b2c_student/': {
      id: '/b2c_student/'
      path: '/b2c_student'
      fullPath: '/b2c_student'
      preLoaderRoute: typeof B2cstudentIndexImport
      parentRoute: typeof rootRoute
    }
    '/student/': {
      id: '/student/'
      path: '/student'
      fullPath: '/student'
      preLoaderRoute: typeof StudentIndexImport
      parentRoute: typeof rootRoute
    }
    '/super_admin/': {
      id: '/super_admin/'
      path: '/super_admin'
      fullPath: '/super_admin'
      preLoaderRoute: typeof SuperadminIndexImport
      parentRoute: typeof rootRoute
    }
    '/teacher/': {
      id: '/teacher/'
      path: '/teacher'
      fullPath: '/teacher'
      preLoaderRoute: typeof TeacherIndexImport
      parentRoute: typeof rootRoute
    }
    '/admin/database/student-grades': {
      id: '/admin/database/student-grades'
      path: '/student-grades'
      fullPath: '/admin/database/student-grades'
      preLoaderRoute: typeof AdminDatabaseStudentGradesImport
      parentRoute: typeof AdminDatabaseImport
    }
    '/public/card/$id': {
      id: '/public/card/$id'
      path: '/$id'
      fullPath: '/public/card/$id'
      preLoaderRoute: typeof PublicCardIdImport
      parentRoute: typeof PublicCardImport
    }
    '/super_admin/direct-update-establishment/$id': {
      id: '/super_admin/direct-update-establishment/$id'
      path: '/super_admin/direct-update-establishment/$id'
      fullPath: '/super_admin/direct-update-establishment/$id'
      preLoaderRoute: typeof SuperadminDirectUpdateEstablishmentIdImport
      parentRoute: typeof rootRoute
    }
    '/super_admin/edit-establishment/$id': {
      id: '/super_admin/edit-establishment/$id'
      path: '/super_admin/edit-establishment/$id'
      fullPath: '/super_admin/edit-establishment/$id'
      preLoaderRoute: typeof SuperadminEditEstablishmentIdImport
      parentRoute: typeof rootRoute
    }
    '/admin/database/': {
      id: '/admin/database/'
      path: '/'
      fullPath: '/admin/database/'
      preLoaderRoute: typeof AdminDatabaseIndexImport
      parentRoute: typeof AdminDatabaseImport
    }
    '/student/attendance/': {
      id: '/student/attendance/'
      path: '/student/attendance'
      fullPath: '/student/attendance'
      preLoaderRoute: typeof StudentAttendanceIndexImport
      parentRoute: typeof rootRoute
    }
    '/student/flashcards/': {
      id: '/student/flashcards/'
      path: '/'
      fullPath: '/student/flashcards/'
      preLoaderRoute: typeof StudentFlashcardsIndexImport
      parentRoute: typeof StudentFlashcardsImport
    }
    '/teacher/auto-corrector/': {
      id: '/teacher/auto-corrector/'
      path: '/teacher/auto-corrector'
      fullPath: '/teacher/auto-corrector'
      preLoaderRoute: typeof TeacherAutoCorrectorIndexImport
      parentRoute: typeof rootRoute
    }
    '/admin/database/student-score-details/$studentId': {
      id: '/admin/database/student-score-details/$studentId'
      path: '/student-score-details/$studentId'
      fullPath: '/admin/database/student-score-details/$studentId'
      preLoaderRoute: typeof AdminDatabaseStudentScoreDetailsStudentIdImport
      parentRoute: typeof AdminDatabaseImport
    }
  }
}

// Create and export the route tree

interface AdminDatabaseRouteChildren {
  AdminDatabaseStudentGradesRoute: typeof AdminDatabaseStudentGradesRoute
  AdminDatabaseIndexRoute: typeof AdminDatabaseIndexRoute
  AdminDatabaseStudentScoreDetailsStudentIdRoute: typeof AdminDatabaseStudentScoreDetailsStudentIdRoute
}

const AdminDatabaseRouteChildren: AdminDatabaseRouteChildren = {
  AdminDatabaseStudentGradesRoute: AdminDatabaseStudentGradesRoute,
  AdminDatabaseIndexRoute: AdminDatabaseIndexRoute,
  AdminDatabaseStudentScoreDetailsStudentIdRoute:
    AdminDatabaseStudentScoreDetailsStudentIdRoute,
}

const AdminDatabaseRouteWithChildren = AdminDatabaseRoute._addFileChildren(
  AdminDatabaseRouteChildren,
)

interface PublicCardRouteChildren {
  PublicCardIdRoute: typeof PublicCardIdRoute
}

const PublicCardRouteChildren: PublicCardRouteChildren = {
  PublicCardIdRoute: PublicCardIdRoute,
}

const PublicCardRouteWithChildren = PublicCardRoute._addFileChildren(
  PublicCardRouteChildren,
)

interface StudentFlashcardsRouteChildren {
  StudentFlashcardsIndexRoute: typeof StudentFlashcardsIndexRoute
}

const StudentFlashcardsRouteChildren: StudentFlashcardsRouteChildren = {
  StudentFlashcardsIndexRoute: StudentFlashcardsIndexRoute,
}

const StudentFlashcardsRouteWithChildren =
  StudentFlashcardsRoute._addFileChildren(StudentFlashcardsRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/about': typeof AboutRoute
  '/ai-demo': typeof AiDemoRoute
  '/card': typeof CardRoute
  '/card-template': typeof CardTemplateRoute
  '/chat': typeof ChatRoute
  '/classes': typeof ClassesRoute
  '/contactUs': typeof ContactUsRoute
  '/debug-token': typeof DebugTokenRoute
  '/demo': typeof DemoRoute
  '/flashcards': typeof FlashcardsRoute
  '/focus-room': typeof FocusRoomRoute
  '/forgot-password': typeof ForgotPasswordRoute
  '/join': typeof JoinRoute
  '/login': typeof LoginRoute
  '/main-workspace': typeof MainWorkspaceRoute
  '/new-password': typeof NewPasswordRoute
  '/notes': typeof NotesRoute
  '/offers': typeof OffersRoute
  '/parent': typeof ParentRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/quizz': typeof QuizzRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/schools': typeof SchoolsRoute
  '/settings': typeof SettingsRoute
  '/subdomain-test': typeof SubdomainTestRoute
  '/subdomain-www-test': typeof SubdomainWwwTestRoute
  '/test': typeof TestRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/whiteboard': typeof WhiteboardRoute
  '/admin/attendance': typeof AdminAttendanceRoute
  '/admin/classroom': typeof AdminClassroomRoute
  '/admin/database': typeof AdminDatabaseRouteWithChildren
  '/admin/elimination': typeof AdminEliminationRoute
  '/admin/exam': typeof AdminExamRoute
  '/admin/grade': typeof AdminGradeRoute
  '/admin/score': typeof AdminScoreRoute
  '/admin/timetable': typeof AdminTimetableRoute
  '/public/card': typeof PublicCardRouteWithChildren
  '/school/$schoolName': typeof SchoolSchoolNameRoute
  '/student/all-in-one': typeof StudentAllInOneRoute
  '/student/exam': typeof StudentExamRoute
  '/student/flashcards': typeof StudentFlashcardsRouteWithChildren
  '/student/timetable': typeof StudentTimetableRoute
  '/student/virtual-classroom': typeof StudentVirtualClassroomRoute
  '/super_admin/database': typeof SuperadminDatabaseRoute
  '/super_admin/etablisments': typeof SuperadminEtablismentsRoute
  '/teacher/attandencehistory': typeof TeacherAttandencehistoryRoute
  '/teacher/attendance': typeof TeacherAttendanceRoute
  '/teacher/classes': typeof TeacherClassesRoute
  '/teacher/database': typeof TeacherDatabaseRoute
  '/teacher/exam': typeof TeacherExamRoute
  '/teacher/markabsent': typeof TeacherMarkabsentRoute
  '/teacher/timetable': typeof TeacherTimetableRoute
  '/teacher/timetable-test': typeof TeacherTimetableTestRoute
  '/admin': typeof AdminIndexRoute
  '/b2c_student': typeof B2cstudentIndexRoute
  '/student': typeof StudentIndexRoute
  '/super_admin': typeof SuperadminIndexRoute
  '/teacher': typeof TeacherIndexRoute
  '/admin/database/student-grades': typeof AdminDatabaseStudentGradesRoute
  '/public/card/$id': typeof PublicCardIdRoute
  '/super_admin/direct-update-establishment/$id': typeof SuperadminDirectUpdateEstablishmentIdRoute
  '/super_admin/edit-establishment/$id': typeof SuperadminEditEstablishmentIdRoute
  '/admin/database/': typeof AdminDatabaseIndexRoute
  '/student/attendance': typeof StudentAttendanceIndexRoute
  '/student/flashcards/': typeof StudentFlashcardsIndexRoute
  '/teacher/auto-corrector': typeof TeacherAutoCorrectorIndexRoute
  '/admin/database/student-score-details/$studentId': typeof AdminDatabaseStudentScoreDetailsStudentIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/about': typeof AboutRoute
  '/ai-demo': typeof AiDemoRoute
  '/card': typeof CardRoute
  '/card-template': typeof CardTemplateRoute
  '/chat': typeof ChatRoute
  '/classes': typeof ClassesRoute
  '/contactUs': typeof ContactUsRoute
  '/debug-token': typeof DebugTokenRoute
  '/demo': typeof DemoRoute
  '/flashcards': typeof FlashcardsRoute
  '/focus-room': typeof FocusRoomRoute
  '/forgot-password': typeof ForgotPasswordRoute
  '/join': typeof JoinRoute
  '/login': typeof LoginRoute
  '/main-workspace': typeof MainWorkspaceRoute
  '/new-password': typeof NewPasswordRoute
  '/notes': typeof NotesRoute
  '/offers': typeof OffersRoute
  '/parent': typeof ParentRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/quizz': typeof QuizzRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/schools': typeof SchoolsRoute
  '/settings': typeof SettingsRoute
  '/subdomain-test': typeof SubdomainTestRoute
  '/subdomain-www-test': typeof SubdomainWwwTestRoute
  '/test': typeof TestRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/whiteboard': typeof WhiteboardRoute
  '/admin/attendance': typeof AdminAttendanceRoute
  '/admin/classroom': typeof AdminClassroomRoute
  '/admin/elimination': typeof AdminEliminationRoute
  '/admin/exam': typeof AdminExamRoute
  '/admin/grade': typeof AdminGradeRoute
  '/admin/score': typeof AdminScoreRoute
  '/admin/timetable': typeof AdminTimetableRoute
  '/public/card': typeof PublicCardRouteWithChildren
  '/school/$schoolName': typeof SchoolSchoolNameRoute
  '/student/all-in-one': typeof StudentAllInOneRoute
  '/student/exam': typeof StudentExamRoute
  '/student/timetable': typeof StudentTimetableRoute
  '/student/virtual-classroom': typeof StudentVirtualClassroomRoute
  '/super_admin/database': typeof SuperadminDatabaseRoute
  '/super_admin/etablisments': typeof SuperadminEtablismentsRoute
  '/teacher/attandencehistory': typeof TeacherAttandencehistoryRoute
  '/teacher/attendance': typeof TeacherAttendanceRoute
  '/teacher/classes': typeof TeacherClassesRoute
  '/teacher/database': typeof TeacherDatabaseRoute
  '/teacher/exam': typeof TeacherExamRoute
  '/teacher/markabsent': typeof TeacherMarkabsentRoute
  '/teacher/timetable': typeof TeacherTimetableRoute
  '/teacher/timetable-test': typeof TeacherTimetableTestRoute
  '/admin': typeof AdminIndexRoute
  '/b2c_student': typeof B2cstudentIndexRoute
  '/student': typeof StudentIndexRoute
  '/super_admin': typeof SuperadminIndexRoute
  '/teacher': typeof TeacherIndexRoute
  '/admin/database/student-grades': typeof AdminDatabaseStudentGradesRoute
  '/public/card/$id': typeof PublicCardIdRoute
  '/super_admin/direct-update-establishment/$id': typeof SuperadminDirectUpdateEstablishmentIdRoute
  '/super_admin/edit-establishment/$id': typeof SuperadminEditEstablishmentIdRoute
  '/admin/database': typeof AdminDatabaseIndexRoute
  '/student/attendance': typeof StudentAttendanceIndexRoute
  '/student/flashcards': typeof StudentFlashcardsIndexRoute
  '/teacher/auto-corrector': typeof TeacherAutoCorrectorIndexRoute
  '/admin/database/student-score-details/$studentId': typeof AdminDatabaseStudentScoreDetailsStudentIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/404': typeof R404Route
  '/about': typeof AboutRoute
  '/ai-demo': typeof AiDemoRoute
  '/card': typeof CardRoute
  '/card-template': typeof CardTemplateRoute
  '/chat': typeof ChatRoute
  '/classes': typeof ClassesRoute
  '/contactUs': typeof ContactUsRoute
  '/debug-token': typeof DebugTokenRoute
  '/demo': typeof DemoRoute
  '/flashcards': typeof FlashcardsRoute
  '/focus-room': typeof FocusRoomRoute
  '/forgot-password': typeof ForgotPasswordRoute
  '/join': typeof JoinRoute
  '/login': typeof LoginRoute
  '/main-workspace': typeof MainWorkspaceRoute
  '/new-password': typeof NewPasswordRoute
  '/notes': typeof NotesRoute
  '/offers': typeof OffersRoute
  '/parent': typeof ParentRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/quizz': typeof QuizzRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/schools': typeof SchoolsRoute
  '/settings': typeof SettingsRoute
  '/subdomain-test': typeof SubdomainTestRoute
  '/subdomain-www-test': typeof SubdomainWwwTestRoute
  '/test': typeof TestRoute
  '/unauthorized': typeof UnauthorizedRoute
  '/whiteboard': typeof WhiteboardRoute
  '/admin/attendance': typeof AdminAttendanceRoute
  '/admin/classroom': typeof AdminClassroomRoute
  '/admin/database': typeof AdminDatabaseRouteWithChildren
  '/admin/elimination': typeof AdminEliminationRoute
  '/admin/exam': typeof AdminExamRoute
  '/admin/grade': typeof AdminGradeRoute
  '/admin/score': typeof AdminScoreRoute
  '/admin/timetable': typeof AdminTimetableRoute
  '/public/card': typeof PublicCardRouteWithChildren
  '/school/$schoolName': typeof SchoolSchoolNameRoute
  '/student/all-in-one': typeof StudentAllInOneRoute
  '/student/exam': typeof StudentExamRoute
  '/student/flashcards': typeof StudentFlashcardsRouteWithChildren
  '/student/timetable': typeof StudentTimetableRoute
  '/student/virtual-classroom': typeof StudentVirtualClassroomRoute
  '/super_admin/database': typeof SuperadminDatabaseRoute
  '/super_admin/etablisments': typeof SuperadminEtablismentsRoute
  '/teacher/attandencehistory': typeof TeacherAttandencehistoryRoute
  '/teacher/attendance': typeof TeacherAttendanceRoute
  '/teacher/classes': typeof TeacherClassesRoute
  '/teacher/database': typeof TeacherDatabaseRoute
  '/teacher/exam': typeof TeacherExamRoute
  '/teacher/markabsent': typeof TeacherMarkabsentRoute
  '/teacher/timetable': typeof TeacherTimetableRoute
  '/teacher/timetable-test': typeof TeacherTimetableTestRoute
  '/admin/': typeof AdminIndexRoute
  '/b2c_student/': typeof B2cstudentIndexRoute
  '/student/': typeof StudentIndexRoute
  '/super_admin/': typeof SuperadminIndexRoute
  '/teacher/': typeof TeacherIndexRoute
  '/admin/database/student-grades': typeof AdminDatabaseStudentGradesRoute
  '/public/card/$id': typeof PublicCardIdRoute
  '/super_admin/direct-update-establishment/$id': typeof SuperadminDirectUpdateEstablishmentIdRoute
  '/super_admin/edit-establishment/$id': typeof SuperadminEditEstablishmentIdRoute
  '/admin/database/': typeof AdminDatabaseIndexRoute
  '/student/attendance/': typeof StudentAttendanceIndexRoute
  '/student/flashcards/': typeof StudentFlashcardsIndexRoute
  '/teacher/auto-corrector/': typeof TeacherAutoCorrectorIndexRoute
  '/admin/database/student-score-details/$studentId': typeof AdminDatabaseStudentScoreDetailsStudentIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/404'
    | '/about'
    | '/ai-demo'
    | '/card'
    | '/card-template'
    | '/chat'
    | '/classes'
    | '/contactUs'
    | '/debug-token'
    | '/demo'
    | '/flashcards'
    | '/focus-room'
    | '/forgot-password'
    | '/join'
    | '/login'
    | '/main-workspace'
    | '/new-password'
    | '/notes'
    | '/offers'
    | '/parent'
    | '/pricing'
    | '/profile'
    | '/quizz'
    | '/register'
    | '/reset-password'
    | '/schools'
    | '/settings'
    | '/subdomain-test'
    | '/subdomain-www-test'
    | '/test'
    | '/unauthorized'
    | '/whiteboard'
    | '/admin/attendance'
    | '/admin/classroom'
    | '/admin/database'
    | '/admin/elimination'
    | '/admin/exam'
    | '/admin/grade'
    | '/admin/score'
    | '/admin/timetable'
    | '/public/card'
    | '/school/$schoolName'
    | '/student/all-in-one'
    | '/student/exam'
    | '/student/flashcards'
    | '/student/timetable'
    | '/student/virtual-classroom'
    | '/super_admin/database'
    | '/super_admin/etablisments'
    | '/teacher/attandencehistory'
    | '/teacher/attendance'
    | '/teacher/classes'
    | '/teacher/database'
    | '/teacher/exam'
    | '/teacher/markabsent'
    | '/teacher/timetable'
    | '/teacher/timetable-test'
    | '/admin'
    | '/b2c_student'
    | '/student'
    | '/super_admin'
    | '/teacher'
    | '/admin/database/student-grades'
    | '/public/card/$id'
    | '/super_admin/direct-update-establishment/$id'
    | '/super_admin/edit-establishment/$id'
    | '/admin/database/'
    | '/student/attendance'
    | '/student/flashcards/'
    | '/teacher/auto-corrector'
    | '/admin/database/student-score-details/$studentId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/404'
    | '/about'
    | '/ai-demo'
    | '/card'
    | '/card-template'
    | '/chat'
    | '/classes'
    | '/contactUs'
    | '/debug-token'
    | '/demo'
    | '/flashcards'
    | '/focus-room'
    | '/forgot-password'
    | '/join'
    | '/login'
    | '/main-workspace'
    | '/new-password'
    | '/notes'
    | '/offers'
    | '/parent'
    | '/pricing'
    | '/profile'
    | '/quizz'
    | '/register'
    | '/reset-password'
    | '/schools'
    | '/settings'
    | '/subdomain-test'
    | '/subdomain-www-test'
    | '/test'
    | '/unauthorized'
    | '/whiteboard'
    | '/admin/attendance'
    | '/admin/classroom'
    | '/admin/elimination'
    | '/admin/exam'
    | '/admin/grade'
    | '/admin/score'
    | '/admin/timetable'
    | '/public/card'
    | '/school/$schoolName'
    | '/student/all-in-one'
    | '/student/exam'
    | '/student/timetable'
    | '/student/virtual-classroom'
    | '/super_admin/database'
    | '/super_admin/etablisments'
    | '/teacher/attandencehistory'
    | '/teacher/attendance'
    | '/teacher/classes'
    | '/teacher/database'
    | '/teacher/exam'
    | '/teacher/markabsent'
    | '/teacher/timetable'
    | '/teacher/timetable-test'
    | '/admin'
    | '/b2c_student'
    | '/student'
    | '/super_admin'
    | '/teacher'
    | '/admin/database/student-grades'
    | '/public/card/$id'
    | '/super_admin/direct-update-establishment/$id'
    | '/super_admin/edit-establishment/$id'
    | '/admin/database'
    | '/student/attendance'
    | '/student/flashcards'
    | '/teacher/auto-corrector'
    | '/admin/database/student-score-details/$studentId'
  id:
    | '__root__'
    | '/'
    | '/404'
    | '/about'
    | '/ai-demo'
    | '/card'
    | '/card-template'
    | '/chat'
    | '/classes'
    | '/contactUs'
    | '/debug-token'
    | '/demo'
    | '/flashcards'
    | '/focus-room'
    | '/forgot-password'
    | '/join'
    | '/login'
    | '/main-workspace'
    | '/new-password'
    | '/notes'
    | '/offers'
    | '/parent'
    | '/pricing'
    | '/profile'
    | '/quizz'
    | '/register'
    | '/reset-password'
    | '/schools'
    | '/settings'
    | '/subdomain-test'
    | '/subdomain-www-test'
    | '/test'
    | '/unauthorized'
    | '/whiteboard'
    | '/admin/attendance'
    | '/admin/classroom'
    | '/admin/database'
    | '/admin/elimination'
    | '/admin/exam'
    | '/admin/grade'
    | '/admin/score'
    | '/admin/timetable'
    | '/public/card'
    | '/school/$schoolName'
    | '/student/all-in-one'
    | '/student/exam'
    | '/student/flashcards'
    | '/student/timetable'
    | '/student/virtual-classroom'
    | '/super_admin/database'
    | '/super_admin/etablisments'
    | '/teacher/attandencehistory'
    | '/teacher/attendance'
    | '/teacher/classes'
    | '/teacher/database'
    | '/teacher/exam'
    | '/teacher/markabsent'
    | '/teacher/timetable'
    | '/teacher/timetable-test'
    | '/admin/'
    | '/b2c_student/'
    | '/student/'
    | '/super_admin/'
    | '/teacher/'
    | '/admin/database/student-grades'
    | '/public/card/$id'
    | '/super_admin/direct-update-establishment/$id'
    | '/super_admin/edit-establishment/$id'
    | '/admin/database/'
    | '/student/attendance/'
    | '/student/flashcards/'
    | '/teacher/auto-corrector/'
    | '/admin/database/student-score-details/$studentId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  R404Route: typeof R404Route
  AboutRoute: typeof AboutRoute
  AiDemoRoute: typeof AiDemoRoute
  CardRoute: typeof CardRoute
  CardTemplateRoute: typeof CardTemplateRoute
  ChatRoute: typeof ChatRoute
  ClassesRoute: typeof ClassesRoute
  ContactUsRoute: typeof ContactUsRoute
  DebugTokenRoute: typeof DebugTokenRoute
  DemoRoute: typeof DemoRoute
  FlashcardsRoute: typeof FlashcardsRoute
  FocusRoomRoute: typeof FocusRoomRoute
  ForgotPasswordRoute: typeof ForgotPasswordRoute
  JoinRoute: typeof JoinRoute
  LoginRoute: typeof LoginRoute
  MainWorkspaceRoute: typeof MainWorkspaceRoute
  NewPasswordRoute: typeof NewPasswordRoute
  NotesRoute: typeof NotesRoute
  OffersRoute: typeof OffersRoute
  ParentRoute: typeof ParentRoute
  PricingRoute: typeof PricingRoute
  ProfileRoute: typeof ProfileRoute
  QuizzRoute: typeof QuizzRoute
  RegisterRoute: typeof RegisterRoute
  ResetPasswordRoute: typeof ResetPasswordRoute
  SchoolsRoute: typeof SchoolsRoute
  SettingsRoute: typeof SettingsRoute
  SubdomainTestRoute: typeof SubdomainTestRoute
  SubdomainWwwTestRoute: typeof SubdomainWwwTestRoute
  TestRoute: typeof TestRoute
  UnauthorizedRoute: typeof UnauthorizedRoute
  WhiteboardRoute: typeof WhiteboardRoute
  AdminAttendanceRoute: typeof AdminAttendanceRoute
  AdminClassroomRoute: typeof AdminClassroomRoute
  AdminDatabaseRoute: typeof AdminDatabaseRouteWithChildren
  AdminEliminationRoute: typeof AdminEliminationRoute
  AdminExamRoute: typeof AdminExamRoute
  AdminGradeRoute: typeof AdminGradeRoute
  AdminScoreRoute: typeof AdminScoreRoute
  AdminTimetableRoute: typeof AdminTimetableRoute
  PublicCardRoute: typeof PublicCardRouteWithChildren
  SchoolSchoolNameRoute: typeof SchoolSchoolNameRoute
  StudentAllInOneRoute: typeof StudentAllInOneRoute
  StudentExamRoute: typeof StudentExamRoute
  StudentFlashcardsRoute: typeof StudentFlashcardsRouteWithChildren
  StudentTimetableRoute: typeof StudentTimetableRoute
  StudentVirtualClassroomRoute: typeof StudentVirtualClassroomRoute
  SuperadminDatabaseRoute: typeof SuperadminDatabaseRoute
  SuperadminEtablismentsRoute: typeof SuperadminEtablismentsRoute
  TeacherAttandencehistoryRoute: typeof TeacherAttandencehistoryRoute
  TeacherAttendanceRoute: typeof TeacherAttendanceRoute
  TeacherClassesRoute: typeof TeacherClassesRoute
  TeacherDatabaseRoute: typeof TeacherDatabaseRoute
  TeacherExamRoute: typeof TeacherExamRoute
  TeacherMarkabsentRoute: typeof TeacherMarkabsentRoute
  TeacherTimetableRoute: typeof TeacherTimetableRoute
  TeacherTimetableTestRoute: typeof TeacherTimetableTestRoute
  AdminIndexRoute: typeof AdminIndexRoute
  B2cstudentIndexRoute: typeof B2cstudentIndexRoute
  StudentIndexRoute: typeof StudentIndexRoute
  SuperadminIndexRoute: typeof SuperadminIndexRoute
  TeacherIndexRoute: typeof TeacherIndexRoute
  SuperadminDirectUpdateEstablishmentIdRoute: typeof SuperadminDirectUpdateEstablishmentIdRoute
  SuperadminEditEstablishmentIdRoute: typeof SuperadminEditEstablishmentIdRoute
  StudentAttendanceIndexRoute: typeof StudentAttendanceIndexRoute
  TeacherAutoCorrectorIndexRoute: typeof TeacherAutoCorrectorIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  R404Route: R404Route,
  AboutRoute: AboutRoute,
  AiDemoRoute: AiDemoRoute,
  CardRoute: CardRoute,
  CardTemplateRoute: CardTemplateRoute,
  ChatRoute: ChatRoute,
  ClassesRoute: ClassesRoute,
  ContactUsRoute: ContactUsRoute,
  DebugTokenRoute: DebugTokenRoute,
  DemoRoute: DemoRoute,
  FlashcardsRoute: FlashcardsRoute,
  FocusRoomRoute: FocusRoomRoute,
  ForgotPasswordRoute: ForgotPasswordRoute,
  JoinRoute: JoinRoute,
  LoginRoute: LoginRoute,
  MainWorkspaceRoute: MainWorkspaceRoute,
  NewPasswordRoute: NewPasswordRoute,
  NotesRoute: NotesRoute,
  OffersRoute: OffersRoute,
  ParentRoute: ParentRoute,
  PricingRoute: PricingRoute,
  ProfileRoute: ProfileRoute,
  QuizzRoute: QuizzRoute,
  RegisterRoute: RegisterRoute,
  ResetPasswordRoute: ResetPasswordRoute,
  SchoolsRoute: SchoolsRoute,
  SettingsRoute: SettingsRoute,
  SubdomainTestRoute: SubdomainTestRoute,
  SubdomainWwwTestRoute: SubdomainWwwTestRoute,
  TestRoute: TestRoute,
  UnauthorizedRoute: UnauthorizedRoute,
  WhiteboardRoute: WhiteboardRoute,
  AdminAttendanceRoute: AdminAttendanceRoute,
  AdminClassroomRoute: AdminClassroomRoute,
  AdminDatabaseRoute: AdminDatabaseRouteWithChildren,
  AdminEliminationRoute: AdminEliminationRoute,
  AdminExamRoute: AdminExamRoute,
  AdminGradeRoute: AdminGradeRoute,
  AdminScoreRoute: AdminScoreRoute,
  AdminTimetableRoute: AdminTimetableRoute,
  PublicCardRoute: PublicCardRouteWithChildren,
  SchoolSchoolNameRoute: SchoolSchoolNameRoute,
  StudentAllInOneRoute: StudentAllInOneRoute,
  StudentExamRoute: StudentExamRoute,
  StudentFlashcardsRoute: StudentFlashcardsRouteWithChildren,
  StudentTimetableRoute: StudentTimetableRoute,
  StudentVirtualClassroomRoute: StudentVirtualClassroomRoute,
  SuperadminDatabaseRoute: SuperadminDatabaseRoute,
  SuperadminEtablismentsRoute: SuperadminEtablismentsRoute,
  TeacherAttandencehistoryRoute: TeacherAttandencehistoryRoute,
  TeacherAttendanceRoute: TeacherAttendanceRoute,
  TeacherClassesRoute: TeacherClassesRoute,
  TeacherDatabaseRoute: TeacherDatabaseRoute,
  TeacherExamRoute: TeacherExamRoute,
  TeacherMarkabsentRoute: TeacherMarkabsentRoute,
  TeacherTimetableRoute: TeacherTimetableRoute,
  TeacherTimetableTestRoute: TeacherTimetableTestRoute,
  AdminIndexRoute: AdminIndexRoute,
  B2cstudentIndexRoute: B2cstudentIndexRoute,
  StudentIndexRoute: StudentIndexRoute,
  SuperadminIndexRoute: SuperadminIndexRoute,
  TeacherIndexRoute: TeacherIndexRoute,
  SuperadminDirectUpdateEstablishmentIdRoute:
    SuperadminDirectUpdateEstablishmentIdRoute,
  SuperadminEditEstablishmentIdRoute: SuperadminEditEstablishmentIdRoute,
  StudentAttendanceIndexRoute: StudentAttendanceIndexRoute,
  TeacherAutoCorrectorIndexRoute: TeacherAutoCorrectorIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/404",
        "/about",
        "/ai-demo",
        "/card",
        "/card-template",
        "/chat",
        "/classes",
        "/contactUs",
        "/debug-token",
        "/demo",
        "/flashcards",
        "/focus-room",
        "/forgot-password",
        "/join",
        "/login",
        "/main-workspace",
        "/new-password",
        "/notes",
        "/offers",
        "/parent",
        "/pricing",
        "/profile",
        "/quizz",
        "/register",
        "/reset-password",
        "/schools",
        "/settings",
        "/subdomain-test",
        "/subdomain-www-test",
        "/test",
        "/unauthorized",
        "/whiteboard",
        "/admin/attendance",
        "/admin/classroom",
        "/admin/database",
        "/admin/elimination",
        "/admin/exam",
        "/admin/grade",
        "/admin/score",
        "/admin/timetable",
        "/public/card",
        "/school/$schoolName",
        "/student/all-in-one",
        "/student/exam",
        "/student/flashcards",
        "/student/timetable",
        "/student/virtual-classroom",
        "/super_admin/database",
        "/super_admin/etablisments",
        "/teacher/attandencehistory",
        "/teacher/attendance",
        "/teacher/classes",
        "/teacher/database",
        "/teacher/exam",
        "/teacher/markabsent",
        "/teacher/timetable",
        "/teacher/timetable-test",
        "/admin/",
        "/b2c_student/",
        "/student/",
        "/super_admin/",
        "/teacher/",
        "/super_admin/direct-update-establishment/$id",
        "/super_admin/edit-establishment/$id",
        "/student/attendance/",
        "/teacher/auto-corrector/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/404": {
      "filePath": "404.tsx"
    },
    "/about": {
      "filePath": "about.tsx"
    },
    "/ai-demo": {
      "filePath": "ai-demo.tsx"
    },
    "/card": {
      "filePath": "card.tsx"
    },
    "/card-template": {
      "filePath": "card-template.tsx"
    },
    "/chat": {
      "filePath": "chat.tsx"
    },
    "/classes": {
      "filePath": "classes.tsx"
    },
    "/contactUs": {
      "filePath": "contactUs.tsx"
    },
    "/debug-token": {
      "filePath": "debug-token.tsx"
    },
    "/demo": {
      "filePath": "demo.tsx"
    },
    "/flashcards": {
      "filePath": "flashcards.tsx"
    },
    "/focus-room": {
      "filePath": "focus-room.tsx"
    },
    "/forgot-password": {
      "filePath": "forgot-password.tsx"
    },
    "/join": {
      "filePath": "join.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/main-workspace": {
      "filePath": "main-workspace.tsx"
    },
    "/new-password": {
      "filePath": "new-password.tsx"
    },
    "/notes": {
      "filePath": "notes.tsx"
    },
    "/offers": {
      "filePath": "offers.tsx"
    },
    "/parent": {
      "filePath": "parent.tsx"
    },
    "/pricing": {
      "filePath": "pricing.tsx"
    },
    "/profile": {
      "filePath": "profile.tsx"
    },
    "/quizz": {
      "filePath": "quizz.tsx"
    },
    "/register": {
      "filePath": "register.tsx"
    },
    "/reset-password": {
      "filePath": "reset-password.tsx"
    },
    "/schools": {
      "filePath": "schools.tsx"
    },
    "/settings": {
      "filePath": "settings.tsx"
    },
    "/subdomain-test": {
      "filePath": "subdomain-test.tsx"
    },
    "/subdomain-www-test": {
      "filePath": "subdomain-www-test.tsx"
    },
    "/test": {
      "filePath": "test.tsx"
    },
    "/unauthorized": {
      "filePath": "unauthorized.tsx"
    },
    "/whiteboard": {
      "filePath": "whiteboard.tsx"
    },
    "/admin/attendance": {
      "filePath": "admin/attendance.tsx"
    },
    "/admin/classroom": {
      "filePath": "admin/classroom.tsx"
    },
    "/admin/database": {
      "filePath": "admin/database.tsx",
      "children": [
        "/admin/database/student-grades",
        "/admin/database/",
        "/admin/database/student-score-details/$studentId"
      ]
    },
    "/admin/elimination": {
      "filePath": "admin/elimination.tsx"
    },
    "/admin/exam": {
      "filePath": "admin/exam.tsx"
    },
    "/admin/grade": {
      "filePath": "admin/grade.tsx"
    },
    "/admin/score": {
      "filePath": "admin/score.tsx"
    },
    "/admin/timetable": {
      "filePath": "admin/timetable.tsx"
    },
    "/public/card": {
      "filePath": "public/card.tsx",
      "children": [
        "/public/card/$id"
      ]
    },
    "/school/$schoolName": {
      "filePath": "school/$schoolName.tsx"
    },
    "/student/all-in-one": {
      "filePath": "student/all-in-one.tsx"
    },
    "/student/exam": {
      "filePath": "student/exam.tsx"
    },
    "/student/flashcards": {
      "filePath": "student/flashcards.tsx",
      "children": [
        "/student/flashcards/"
      ]
    },
    "/student/timetable": {
      "filePath": "student/timetable.tsx"
    },
    "/student/virtual-classroom": {
      "filePath": "student/virtual-classroom.tsx"
    },
    "/super_admin/database": {
      "filePath": "super_admin/database.tsx"
    },
    "/super_admin/etablisments": {
      "filePath": "super_admin/etablisments.tsx"
    },
    "/teacher/attandencehistory": {
      "filePath": "teacher/attandencehistory.tsx"
    },
    "/teacher/attendance": {
      "filePath": "teacher/attendance.tsx"
    },
    "/teacher/classes": {
      "filePath": "teacher/classes.tsx"
    },
    "/teacher/database": {
      "filePath": "teacher/database.tsx"
    },
    "/teacher/exam": {
      "filePath": "teacher/exam.tsx"
    },
    "/teacher/markabsent": {
      "filePath": "teacher/markabsent.tsx"
    },
    "/teacher/timetable": {
      "filePath": "teacher/timetable.tsx"
    },
    "/teacher/timetable-test": {
      "filePath": "teacher/timetable-test.tsx"
    },
    "/admin/": {
      "filePath": "admin/index.tsx"
    },
    "/b2c_student/": {
      "filePath": "b2c_student/index.tsx"
    },
    "/student/": {
      "filePath": "student/index.tsx"
    },
    "/super_admin/": {
      "filePath": "super_admin/index.tsx"
    },
    "/teacher/": {
      "filePath": "teacher/index.tsx"
    },
    "/admin/database/student-grades": {
      "filePath": "admin/database/student-grades.tsx",
      "parent": "/admin/database"
    },
    "/public/card/$id": {
      "filePath": "public/card/$id.tsx",
      "parent": "/public/card"
    },
    "/super_admin/direct-update-establishment/$id": {
      "filePath": "super_admin/direct-update-establishment/$id.tsx"
    },
    "/super_admin/edit-establishment/$id": {
      "filePath": "super_admin/edit-establishment/$id.tsx"
    },
    "/admin/database/": {
      "filePath": "admin/database/index.tsx",
      "parent": "/admin/database"
    },
    "/student/attendance/": {
      "filePath": "student/attendance/index.tsx"
    },
    "/student/flashcards/": {
      "filePath": "student/flashcards/index.tsx",
      "parent": "/student/flashcards"
    },
    "/teacher/auto-corrector/": {
      "filePath": "teacher/auto-corrector/index.tsx"
    },
    "/admin/database/student-score-details/$studentId": {
      "filePath": "admin/database/student-score-details.$studentId.tsx",
      "parent": "/admin/database"
    }
  }
}
ROUTE_MANIFEST_END */
