/**
 * Domain Debugger Utility
 *
 * This utility helps diagnose domain and subdomain issues by providing
 * detailed information about the current URL, hostname, and subdomain status.
 */

import { schoolService } from '@/services/schoolService'

/**
 * Run a comprehensive domain diagnostic test
 * @returns Object with diagnostic information
 */
export function runDomainDiagnostic() {
  const hostname = window.location.hostname
  const pathname = window.location.pathname
  const fullUrl = window.location.href

  // Get subdomain status
  const isSubdomain = schoolService.isSubdomainUrl()
  const isWwwSubdomain = hostname.startsWith('www.')

  // Get school information if on a subdomain
  const schoolName = isSubdomain
    ? schoolService.getCurrentSchoolFromSubdomain()
    : null

  // Check for path-based school route
  const schoolPathMatch = pathname.match(/^\/school\/([^\/]+)$/)
  const schoolNameFromPath = schoolPathMatch ? schoolPathMatch[1] : null

  // Determine if we're in development or production
  const isDev = hostname.includes('localhost') || hostname.includes('127.0.0.1')

  // Collect all diagnostic information
  const diagnosticInfo = {
    url: {
      full: fullUrl,
      hostname,
      pathname,
      protocol: window.location.protocol,
      port:
        window.location.port ||
        (window.location.protocol === 'https:' ? '443' : '80'),
    },
    domain: {
      isSubdomain,
      isWwwSubdomain,
      isDev,
      isProduction: !isDev,
    },
    school: {
      nameFromSubdomain: schoolName,
      nameFromPath: schoolNameFromPath,
      hasSchoolInfo: !!schoolName || !!schoolNameFromPath,
    },
  }

  // Log the diagnostic information to the console
  console.group('Domain Diagnostic Results')
  console.log('URL Information:', diagnosticInfo.url)
  console.log('Domain Status:', diagnosticInfo.domain)
  console.log('School Information:', diagnosticInfo.school)
  console.groupEnd()

  return diagnosticInfo
}

/**
 * Check if the current domain configuration is valid
 * @returns Object with validation results
 */
export function validateDomainConfig() {
  const hostname = window.location.hostname
  const isWww = hostname.startsWith('www.')
  const isSubdomain = schoolService.isSubdomainUrl()

  // Potential issues
  const issues = []

  // Check for common issues
  if (isWww && isSubdomain) {
    issues.push(
      'www is detected as both a subdomain and a special case. This may cause routing issues.'
    )
  }

  if (hostname.includes('.jeridschool.tech') && !isWww && !isSubdomain) {
    issues.push(
      'Hostname contains jeridschool.tech but is not detected as a subdomain or www.'
    )
  }

  // Validation result
  const result = {
    isValid: issues.length === 0,
    issues,
    hostname,
    isWww,
    isSubdomain,
  }

  // Log the validation results
  console.group('Domain Configuration Validation')
  console.log('Validation Result:', result.isValid ? 'VALID ✅' : 'INVALID ❌')

  if (issues.length > 0) {
    console.warn('Issues Found:', issues)
  } else {
    console.log('No issues detected')
  }

  console.log('Configuration Details:', {
    hostname,
    isWww,
    isSubdomain,
  })
  console.groupEnd()

  return result
}

// Instructions for manual testing:
// 1. Open browser console
// 2. Import the functions:
//    import { runDomainDiagnostic, validateDomainConfig } from '@/utils/domainDebugger'
// 3. Run the functions:
//    runDomainDiagnostic()
//    validateDomainConfig()
