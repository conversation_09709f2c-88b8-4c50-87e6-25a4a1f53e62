/**
 * Utility for making direct fetch requests that mimic Postman behavior
 * This bypasses axios and other libraries to ensure compatibility with the backend
 */

/**
 * Makes a direct PATCH request using XMLHttpRequest
 * @param url - The URL to send the request to
 * @param data - The data to send in the request body
 * @returns Promise with the response data
 */
export const xhrPatchRequest = async (url: string, data: any): Promise<any> => {
  // Get the token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('Authentication required - no token found');
  }

  // Format the token correctly
  const formattedToken = token.startsWith('Bearer ') ? token : `Bearer ${token}`;

  console.log('Making XHR PATCH request to:', url);
  console.log('With data:', JSON.stringify(data, null, 2));

  return new Promise((resolve, reject) => {
    try {
      // Create a clean copy of the data, removing properties that shouldn't be sent
      const cleanData = { ...data };

      // List of properties that should be removed before sending to the API
      const propertiesToRemove = [
        'superAdminId',
        'superAdminProfileId',
        'etablissementId',
        'id',
        'createdAt',
        'updatedAt',
        'createdBy',
        'updatedBy',
        'adminProfiles',
        'teacherProfiles',
        'studentProfiles',
        'superAdminProfile',
        'cardDesign' // This property is causing the 400 error
      ];

      // Remove all properties that shouldn't be sent
      propertiesToRemove.forEach(prop => {
        if (prop in cleanData) {
          delete cleanData[prop];
          console.log(`Removed ${prop} from update data in XHR`);
        }
      });

      console.log('Cleaned data for XHR:', JSON.stringify(cleanData, null, 2));

      // Create and configure the XHR request
      const xhr = new XMLHttpRequest();
      xhr.open('PATCH', url, true);

      // Set minimal headers to avoid CORS issues
      xhr.setRequestHeader('Authorization', formattedToken);
      xhr.setRequestHeader('Content-Type', 'application/json');

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Success
            try {
              const responseData = JSON.parse(xhr.responseText);
              console.log('XHR request successful:', responseData);
              resolve(responseData);
            } catch (error) {
              console.log('XHR request successful but could not parse JSON:', xhr.responseText);
              resolve(xhr.responseText);
            }
          } else {
            // Error
            console.error('XHR request failed:', {
              status: xhr.status,
              statusText: xhr.statusText,
              response: xhr.responseText
            });

            try {
              const errorData = JSON.parse(xhr.responseText);
              reject(new Error(`Server error (${xhr.status}): ${errorData.message || 'Unknown error'}`));
            } catch (error) {
              reject(new Error(`Server error (${xhr.status}): ${xhr.statusText || 'Unknown error'}`));
            }
          }
        }
      };

      xhr.onerror = function() {
        console.error('XHR network error');
        reject(new Error('Network error occurred'));
      };

      // Send the request
      xhr.send(JSON.stringify(cleanData));
    } catch (error) {
      console.error('Error in XHR request:', error);
      reject(error);
    }
  });
};

/**
 * Makes a direct PATCH request using the fetch API
 * @param url - The URL to send the request to
 * @param data - The data to send in the request body
 * @returns Promise with the response data
 */
export const directPatchRequest = async (url: string, data: any): Promise<any> => {
  // Get the token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('Authentication required - no token found');
  }

  // Format the token correctly
  const formattedToken = token.startsWith('Bearer ') ? token : `Bearer ${token}`;

  console.log('Making direct PATCH request to:', url);
  console.log('With data:', JSON.stringify(data, null, 2));

  try {
    // Create a clean copy of the data, removing properties that shouldn't be sent
    const cleanData = { ...data };

    // List of properties that should be removed before sending to the API
    const propertiesToRemove = [
      'superAdminId',
      'superAdminProfileId',
      'etablissementId',
      'id',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'adminProfiles',
      'teacherProfiles',
      'studentProfiles',
      'superAdminProfile',
      'cardDesign' // This property is causing the 400 error
    ];

    // Remove all properties that shouldn't be sent
    propertiesToRemove.forEach(prop => {
      if (prop in cleanData) {
        delete cleanData[prop];
        console.log(`Removed ${prop} from update data in directFetch`);
      }
    });

    console.log('Cleaned data for direct fetch:', JSON.stringify(cleanData, null, 2));

    // Make the request using the fetch API with minimal headers to avoid CORS issues
    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Authorization': formattedToken,
        'Content-Type': 'application/json',
        // Only include essential headers to avoid CORS issues
      },
      body: JSON.stringify(cleanData),
      mode: 'cors',
      // Don't set cache or credentials to avoid CORS issues
      redirect: 'follow',
    });

    // Check if the response is ok
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from server:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      try {
        // Try to parse the error as JSON
        const errorJson = JSON.parse(errorText);
        throw new Error(`Server error (${response.status}): ${errorJson.message || 'Unknown error'}`);
      } catch (parseError) {
        // If parsing fails, just use the text
        throw new Error(`Server error (${response.status}): ${errorText || 'Unknown error'}`);
      }
    }

    // Parse the response
    const responseData = await response.json();
    console.log('Direct fetch successful:', responseData);
    return responseData;
  } catch (error) {
    console.error('Error in direct fetch:', error);
    throw error;
  }
};

/**
 * Makes a direct POST request using the fetch API
 * @param url - The URL to send the request to
 * @param data - The data to send in the request body
 * @returns Promise with the response data
 */
export const directPostRequest = async (url: string, data: any): Promise<any> => {
  // Get the token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('Authentication required - no token found');
  }

  // Format the token correctly
  const formattedToken = token.startsWith('Bearer ') ? token : `Bearer ${token}`;

  console.log('Making direct POST request to:', url);
  console.log('With data:', JSON.stringify(data, null, 2));

  try {
    // Create a clean copy of the data, removing properties that shouldn't be sent
    const cleanData = { ...data };

    // List of properties that should be removed before sending to the API
    const propertiesToRemove = [
      'id',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'adminProfiles',
      'teacherProfiles',
      'studentProfiles',
      'superAdminProfile',
      'cardDesign' // This property is causing the 400 error
    ];

    // Remove all properties that shouldn't be sent
    propertiesToRemove.forEach(prop => {
      if (prop in cleanData) {
        delete cleanData[prop];
        console.log(`Removed ${prop} from create data in directFetch`);
      }
    });

    console.log('Cleaned data for direct fetch:', JSON.stringify(cleanData, null, 2));

    // Make the request using the fetch API with minimal headers to avoid CORS issues
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': formattedToken,
        'Content-Type': 'application/json',
        // Only include essential headers to avoid CORS issues
      },
      body: JSON.stringify(cleanData),
      mode: 'cors',
      // Don't set cache or credentials to avoid CORS issues
      redirect: 'follow',
    });

    // Check if the response is ok
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from server:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      try {
        // Try to parse the error as JSON
        const errorJson = JSON.parse(errorText);
        throw new Error(`Server error (${response.status}): ${errorJson.message || 'Unknown error'}`);
      } catch (parseError) {
        // If parsing fails, just use the text
        throw new Error(`Server error (${response.status}): ${errorText || 'Unknown error'}`);
      }
    }

    // Parse the response
    const responseData = await response.json();
    console.log('Direct fetch successful:', responseData);
    return responseData;
  } catch (error) {
    console.error('Error in direct fetch:', error);
    throw error;
  }
};
