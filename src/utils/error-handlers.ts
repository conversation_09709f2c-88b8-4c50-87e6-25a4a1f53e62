/**
 * Utility functions for handling API errors
 */

/**
 * Formats an error message to ensure it's always a string
 * @param message The error message which could be a string, array, or object
 * @returns A formatted string error message
 */
export function formatErrorMessage(message: any): string {
  if (!message) return 'Unknown error';

  if (Array.isArray(message)) {
    return message.map((msg: any) => {
      if (msg && typeof msg === 'object' && 'field' in msg && 'message' in msg) {
        return `${msg.field}: ${msg.message}`;
      }
      return String(msg);
    }).join(', ');
  }

  if (message && typeof message === 'object') {
    if ('field' in message && 'message' in message) {
      return `${message.field}: ${message.message}`;
    }

    // Try to convert object to string in a meaningful way
    try {
      return JSON.stringify(message);
    } catch (e) {
      return 'Error object could not be converted to string';
    }
  }

  return String(message);
}

/**
 * Parses a 409 Conflict error response to determine if it's an email or CIN conflict
 * @param error The error object from axios
 * @returns A user-friendly error message
 */
export function parseConflictError(error: any): string {
  // Default generic message
  let errorMessage = "A conflict occurred. This record may already exist.";

  // Check if it's an axios error with a response
  if (error && error.response && error.response.data) {
    const { data } = error.response;

    if (data.message) {
      // Format the message to ensure it's a string
      const formattedMessage = formatErrorMessage(data.message);

      // Check for specific keywords in the formatted message
      if (formattedMessage.toLowerCase().includes('email')) {
        return "This email address is already in use. Please use a different email.";
      }

      if (formattedMessage.toLowerCase().includes('cin')) {
        return "This CIN is already registered in the system. Please verify the CIN number.";
      }

      // If no specific keywords are found, return the formatted message
      return formattedMessage;
    }
  }

  return errorMessage;
}
