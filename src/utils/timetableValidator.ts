/**
 * Timetable Validator
 *
 * This utility analyzes a timetable schedule to identify:
 * - Unassigned classes (subjects that should be scheduled but aren't)
 * - Conflicts (teachers or rooms scheduled in multiple places at the same time)
 * - Missing teachers or rooms
 * - Other scheduling issues
 */

interface TimeSlot {
  day: string
  time: string
}

interface ScheduleEntry {
  class: string
  day: string
  time: string
  subject: string
  teacherID: number
  teacher: string
  salle: string
}

interface ClassSubject {
  subject: string
  hours: number
  teacherID: number
  divide?: number[]
  salleNeeded?: string
}

interface TimetableData {
  timeSlots: {
    [day: string]: {
      studyTimes: string[]
      restTime: string
    }
  }
  salles: string[]
  classes: {
    className: string
    subjects: ClassSubject[]
  }[]
  teachers: {
    teacherId: number
    teacherName: string
    subjects: string[]
    unavailableTimes?: {
      [day: string]: string[]
    }
    minimumHours?: number
    maximumHours?: number
  }[]
}

interface ValidationResult {
  unassignedClasses: {
    className: string
    subject: string
    hours: number
    assignedHours: number
    teacherID: number
    teacherName: string
  }[]
  teacherConflicts: {
    teacher: string
    teacherID: number
    timeSlot: TimeSlot
    conflicts: {
      class: string
      subject: string
      salle: string
    }[]
  }[]
  roomConflicts: {
    salle: string
    timeSlot: TimeSlot
    conflicts: {
      class: string
      subject: string
      teacher: string
      teacherID: number
    }[]
  }[]
  teacherUnavailableAssignments: {
    teacher: string
    teacherID: number
    timeSlot: TimeSlot
    class: string
    subject: string
  }[]
  teacherWorkloadIssues: {
    teacher: string
    teacherID: number
    minimumHours: number
    maximumHours: number
    actualHours: number
    status: 'underworked' | 'overworked'
  }[]
  summary: {
    totalUnassignedHours: number
    totalTeacherConflicts: number
    totalRoomConflicts: number
    totalUnavailableAssignments: number
    totalWorkloadIssues: number
  }
}

/**
 * Validates a timetable schedule against the input data
 *
 * @param inputData The original timetable input data
 * @param scheduleData The generated schedule data
 * @returns Validation results with any issues found
 */
export function validateTimetable(
  inputData: TimetableData,
  scheduleData: ScheduleEntry[]
): ValidationResult {
  // Initialize the result object
  const result: ValidationResult = {
    unassignedClasses: [],
    teacherConflicts: [],
    roomConflicts: [],
    teacherUnavailableAssignments: [],
    teacherWorkloadIssues: [],
    summary: {
      totalUnassignedHours: 0,
      totalTeacherConflicts: 0,
      totalRoomConflicts: 0,
      totalUnavailableAssignments: 0,
      totalWorkloadIssues: 0,
    },
  }

  // Create a map of teacher IDs to names for easier lookup
  const teacherMap = new Map<number, string>()
  inputData.teachers.forEach((teacher) => {
    teacherMap.set(teacher.teacherId, teacher.teacherName)
  })

  // Check for unassigned classes
  inputData.classes.forEach((classData) => {
    classData.subjects.forEach((subject) => {
      // Count how many hours are assigned for this subject in the schedule
      const assignedHours = scheduleData.filter(
        (entry) =>
          entry.class === classData.className &&
          entry.subject === subject.subject &&
          entry.teacherID === subject.teacherID
      ).length

      // If not all hours are assigned, add to unassigned classes
      if (assignedHours < subject.hours) {
        result.unassignedClasses.push({
          className: classData.className,
          subject: subject.subject,
          hours: subject.hours,
          assignedHours,
          teacherID: subject.teacherID,
          teacherName: teacherMap.get(subject.teacherID) || 'Unknown Teacher',
        })
        result.summary.totalUnassignedHours += subject.hours - assignedHours
      }
    })
  })

  // Check for teacher conflicts (same teacher in multiple places at the same time)
  const teacherSchedule = new Map<string, ScheduleEntry[]>()

  scheduleData.forEach((entry) => {
    const key = `${entry.teacherID}-${entry.day}-${entry.time}`
    if (!teacherSchedule.has(key)) {
      teacherSchedule.set(key, [])
    }
    teacherSchedule.get(key)?.push(entry)
  })

  teacherSchedule.forEach((entries, key) => {
    if (entries.length > 1) {
      const [teacherID, day, time] = key.split('-')
      result.teacherConflicts.push({
        teacher: entries[0].teacher,
        teacherID: parseInt(teacherID),
        timeSlot: { day, time },
        conflicts: entries.map((entry) => ({
          class: entry.class,
          subject: entry.subject,
          salle: entry.salle,
        })),
      })
      result.summary.totalTeacherConflicts++
    }
  })

  // Check for room conflicts (same room used by multiple classes at the same time)
  const roomSchedule = new Map<string, ScheduleEntry[]>()

  scheduleData.forEach((entry) => {
    const key = `${entry.salle}-${entry.day}-${entry.time}`
    if (!roomSchedule.has(key)) {
      roomSchedule.set(key, [])
    }
    roomSchedule.get(key)?.push(entry)
  })

  roomSchedule.forEach((entries, key) => {
    if (entries.length > 1) {
      const [salle, day, time] = key.split('-')
      result.roomConflicts.push({
        salle,
        timeSlot: { day, time },
        conflicts: entries.map((entry) => ({
          class: entry.class,
          subject: entry.subject,
          teacher: entry.teacher,
          teacherID: entry.teacherID,
        })),
      })
      result.summary.totalRoomConflicts++
    }
  })

  // Check for teachers scheduled during their unavailable times
  inputData.teachers.forEach((teacher) => {
    if (teacher.unavailableTimes) {
      Object.entries(teacher.unavailableTimes).forEach(([day, times]) => {
        times.forEach((time) => {
          const conflictingEntries = scheduleData.filter(
            (entry) =>
              entry.teacherID === teacher.teacherId &&
              entry.day === day &&
              entry.time === time
          )

          conflictingEntries.forEach((entry) => {
            result.teacherUnavailableAssignments.push({
              teacher: teacher.teacherName,
              teacherID: teacher.teacherId,
              timeSlot: { day, time },
              class: entry.class,
              subject: entry.subject,
            })
            result.summary.totalUnavailableAssignments++
          })
        })
      })
    }
  })

  // Check for teacher workload issues
  inputData.teachers.forEach((teacher) => {
    if (teacher.minimumHours || teacher.maximumHours) {
      const actualHours = scheduleData.filter(
        (entry) => entry.teacherID === teacher.teacherId
      ).length

      if (
        (teacher.minimumHours && actualHours < teacher.minimumHours) ||
        (teacher.maximumHours && actualHours > teacher.maximumHours)
      ) {
        result.teacherWorkloadIssues.push({
          teacher: teacher.teacherName,
          teacherID: teacher.teacherId,
          minimumHours: teacher.minimumHours || 0,
          maximumHours: teacher.maximumHours || 0,
          actualHours,
          status:
            teacher.minimumHours && actualHours < teacher.minimumHours
              ? 'underworked'
              : 'overworked',
        })
        result.summary.totalWorkloadIssues++
      }
    }
  })

  return result
}

/**
 * Formats validation results as a human-readable string
 *
 * @param results The validation results
 * @returns A formatted string with the validation results
 */
export function formatValidationResults(results: ValidationResult): string {
  let output = '# Timetable Validation Results\n\n'

  // Add summary
  output += '## Summary\n\n'
  output += `- Total unassigned hours: ${results.summary.totalUnassignedHours}\n`
  output += `- Total teacher conflicts: ${results.summary.totalTeacherConflicts}\n`
  output += `- Total room conflicts: ${results.summary.totalRoomConflicts}\n`
  output += `- Total unavailable assignments: ${results.summary.totalUnavailableAssignments}\n`
  output += `- Total workload issues: ${results.summary.totalWorkloadIssues}\n\n`

  // Add unassigned classes
  if (results.unassignedClasses.length > 0) {
    output += '## Unassigned Classes\n\n'
    results.unassignedClasses.forEach((unassigned) => {
      output += `- ${unassigned.className}: ${unassigned.subject} (${unassigned.assignedHours}/${unassigned.hours} hours assigned) - Teacher: ${unassigned.teacherName}\n`
    })
    output += '\n'
  }

  // Add teacher conflicts
  if (results.teacherConflicts.length > 0) {
    output += '## Teacher Conflicts\n\n'
    results.teacherConflicts.forEach((conflict) => {
      output += `- Teacher ${conflict.teacher} is scheduled in multiple places on ${conflict.timeSlot.day} at ${conflict.timeSlot.time}:\n`
      conflict.conflicts.forEach((c) => {
        output += `  - Class ${c.class}, Subject: ${c.subject}, Room: ${c.salle}\n`
      })
    })
    output += '\n'
  }

  // Add room conflicts
  if (results.roomConflicts.length > 0) {
    output += '## Room Conflicts\n\n'
    results.roomConflicts.forEach((conflict) => {
      output += `- Room ${conflict.salle} is double-booked on ${conflict.timeSlot.day} at ${conflict.timeSlot.time}:\n`
      conflict.conflicts.forEach((c) => {
        output += `  - Class ${c.class}, Subject: ${c.subject}, Teacher: ${c.teacher}\n`
      })
    })
    output += '\n'
  }

  // Add teacher unavailable assignments
  if (results.teacherUnavailableAssignments.length > 0) {
    output += '## Teachers Scheduled During Unavailable Times\n\n'
    results.teacherUnavailableAssignments.forEach((unavailable) => {
      output += `- Teacher ${unavailable.teacher} is scheduled during unavailable time on ${unavailable.timeSlot.day} at ${unavailable.timeSlot.time}:\n`
      output += `  - Class ${unavailable.class}, Subject: ${unavailable.subject}\n`
    })
    output += '\n'
  }

  // Add teacher workload issues
  if (results.teacherWorkloadIssues.length > 0) {
    output += '## Teacher Workload Issues\n\n'
    results.teacherWorkloadIssues.forEach((workload) => {
      output += `- Teacher ${workload.teacher} is ${workload.status} (${workload.actualHours} hours assigned, min: ${workload.minimumHours}, max: ${workload.maximumHours})\n`
    })
    output += '\n'
  }

  return output
}
