/**
 * This is a utility script to test the public endpoint for school data
 * It can be run in the browser console to verify that the endpoint works correctly
 */

import axios from 'axios'
import { getProxiedPath } from '@/lib/corsProxy'

const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:3000'
const IS_DEV = import.meta.env.DEV

export async function testPublicEndpoint(schoolUrl: string) {
  console.group('Public Endpoint Test')

  try {
    console.log(`Testing public endpoint for school URL: ${schoolUrl}`)

    // Construct the endpoint URL
    const endpointPath = `/public/etablissement/url/${schoolUrl}`

    // Use the proxy in development mode
    const requestUrl = IS_DEV
      ? getProxiedPath(endpointPath, 'api')
      : `${API_BASE_URL}${endpointPath}`

    console.log('Making request to:', requestUrl)

    // Test the public endpoint
    const response = await axios.get(requestUrl, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    })

    console.log('Response status:', response.status)
    console.log('Response data:', response.data)

    console.log('Test successful! The public endpoint is working correctly.')

    return {
      success: true,
      data: response.data,
    }
  } catch (error: any) {
    console.error('Error testing public endpoint:', error)

    return {
      success: false,
      error: error.message,
      response: error.response,
    }
  } finally {
    console.groupEnd()
  }
}

// Instructions for manual testing:
// 1. Open browser console
// 2. Import the function: import { testPublicEndpoint } from '@/utils/testPublicEndpoint'
// 3. Run the function: testPublicEndpoint('your-school-url')
