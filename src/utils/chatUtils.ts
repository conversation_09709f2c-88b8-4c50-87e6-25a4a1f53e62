import { User, Conversation, ChatUtils } from '@/types/chat'

export const chatUtils: ChatUtils = {
  formatTime: (dateString: string): string => {
    try {
      const date = new Date(dateString)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (error) {
      console.error('Error formatting time:', error)
      return ''
    }
  },

  formatDate: (dateString: string): string => {
    try {
      const date = new Date(dateString)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      if (date.toDateString() === today.toDateString()) {
        return 'Today'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday'
      } else {
        return date.toLocaleDateString()
      }
    } catch (error) {
      console.error('Error formatting date:', error)
      return ''
    }
  },

  getInitials: (user: User): string => {
    if (!user) return 'U'

    // Get user data from localStorage if not available in the user object
    if (!user.firstname || !user.lastname) {
      const storedFirstname = localStorage.getItem('firstname')
      const storedLastname = localStorage.getItem('lastname')

      // Use stored values or fallbacks
      const firstname = user.firstname || storedFirstname || 'U'
      const lastname = user.lastname || storedLastname || ''

      return `${firstname.charAt(0)}${lastname.charAt(0) || ''}`
    }

    return `${user.firstname.charAt(0)}${user.lastname.charAt(0)}`
  },

  getAvatarUrl: (user: User): string => {
    if (!user) return ''

    try {
      return (
        user.avatar ||
        `https://api.dicebear.com/7.x/initials/svg?seed=${chatUtils.getInitials(user)}`
      )
    } catch (error) {
      console.error('Error generating avatar URL:', error)
      return `https://api.dicebear.com/7.x/initials/svg?seed=U`
    }
  },

  getLastMessage: (conversation: Conversation): string => {
    if (
      !conversation ||
      !conversation.messages ||
      conversation.messages.length === 0
    )
      return 'Start a conversation...'
    return conversation.messages[conversation.messages.length - 1].content
  },

  getLastMessageTime: (conversation: Conversation): string => {
    if (
      !conversation ||
      !conversation.messages ||
      conversation.messages.length === 0
    )
      return ''
    return chatUtils.formatTime(
      conversation.messages[conversation.messages.length - 1].createdAt
    )
  },

  getUnreadCount: (
    conversation: Conversation,
    currentUserId: string
  ): number => {
    if (!currentUserId || !conversation || !conversation.messages) return 0
    return conversation.messages.filter(
      (msg) => !msg.isRead && msg.senderId !== currentUserId
    ).length
  },

  // Get display name for a conversation based on the current user
  getConversationDisplayName: (
    conversation: Conversation,
    currentUser: User
  ): string => {
    // If we have participants, try to get the other participant
    if (conversation.participants && conversation.participants.length > 0) {
      // For one-on-one conversations
      if (!conversation.isGroupChat && conversation.participants.length === 2) {
        const otherParticipant = conversation.participants.find(
          (p) => p.id !== currentUser.id
        )

        if (otherParticipant) {
          return `${otherParticipant.firstname} ${otherParticipant.lastname}`
        }
      }

      // For group chats, use the conversation name
      if (conversation.name) {
        return conversation.name
      }

      // Fallback for group chats without a name
      return 'Group Chat'
    }

    // If no participants but we have a name, try to extract a better name
    if (conversation.name) {
      // If the name is in format "user1 - user2", extract the part that's not the current user
      if (conversation.name.includes('-')) {
        const parts = conversation.name.split('-').map((p) => p.trim())
        const currentUserFullName =
          `${currentUser.firstname} ${currentUser.lastname}`.toLowerCase()

        if (parts[0].toLowerCase() === currentUserFullName) {
          return parts[1]
        } else if (parts[1].toLowerCase() === currentUserFullName) {
          return parts[0]
        }
      }

      // Just return the name if we can't extract a better one
      return conversation.name
    }

    // Final fallback
    return conversation.isGroupChat ? 'Group Chat' : 'Chat'
  },

  // Get initials for a conversation avatar
  getConversationInitials: (
    conversation: Conversation,
    currentUser: User
  ): string => {
    // If we have participants, try to get the other participant's initials
    if (conversation.participants && conversation.participants.length > 0) {
      // For one-on-one conversations
      if (!conversation.isGroupChat && conversation.participants.length === 2) {
        const otherParticipant = conversation.participants.find(
          (p) => p.id !== currentUser.id
        )

        if (otherParticipant) {
          return chatUtils.getInitials(otherParticipant)
        }
      }

      // For group chats, use the first letter of the conversation name
      if (conversation.name) {
        return conversation.name[0]
      }

      // Fallback for group chats
      return 'G'
    }

    // If no participants but we have a name, extract initials from it
    if (conversation.name) {
      if (conversation.name.includes('-')) {
        const parts = conversation.name.split('-').map((p) => p.trim())
        const currentUserFullName =
          `${currentUser.firstname} ${currentUser.lastname}`.toLowerCase()

        let otherUserName
        if (parts[0].toLowerCase() === currentUserFullName) {
          otherUserName = parts[1]
        } else {
          otherUserName = parts[0]
        }

        const nameParts = otherUserName.split(' ')
        if (nameParts.length >= 2) {
          return `${nameParts[0][0]}${nameParts[1][0]}`
        } else if (nameParts.length === 1) {
          return nameParts[0][0]
        }
      }

      // Just get first letter of the name
      return conversation.name[0]
    }

    // Final fallback
    return conversation.isGroupChat ? 'G' : 'C'
  },
}
