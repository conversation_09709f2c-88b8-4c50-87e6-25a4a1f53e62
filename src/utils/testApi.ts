import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

export const checkToken = () => {
  // Check localStorage
  const localToken = localStorage.getItem('access_token');

  // Check sessionStorage
  const sessionToken = sessionStorage.getItem('access_token');

  // Check other common token keys
  const otherTokens = {
    token: localStorage.getItem('token'),
    authToken: localStorage.getItem('authToken'),
    jwt: localStorage.getItem('jwt'),
    idToken: localStorage.getItem('id_token')
  };

  // Log all localStorage keys
  const allLocalStorageKeys = Object.keys(localStorage);

  return {
    hasLocalToken: !!localToken,
    localTokenLength: localToken ? localToken.length : 0,
    localTokenPreview: localToken ? `${localToken.substring(0, 10)}...` : null,

    hasSessionToken: !!sessionToken,
    sessionTokenLength: sessionToken ? sessionToken.length : 0,
    sessionTokenPreview: sessionToken ? `${sessionToken.substring(0, 10)}...` : null,

    otherTokens,
    allLocalStorageKeys
  };
};

export const testCreateCollection = async () => {
  try {
    console.log('testCreateCollection - Starting test');

    // Get token
    const token = localStorage.getItem('access_token');
    console.log('testCreateCollection - Token available:', !!token);

    if (!token) {
      console.error('testCreateCollection - No token available');
      return { success: false, error: 'No token available' };
    }

    // Create test data
    const testData = {
      name: "Test Collection " + new Date().toISOString(),
      visibility: "private" as const,
      topic: "API Test"
    };

    console.log('testCreateCollection - Test data:', testData);
    console.log('testCreateCollection - Making POST request to:', `${BASE_URL}/notes/collections`);

    // Make API call
    const response = await axios.post(`${BASE_URL}/notes/collections`, testData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
    });

    console.log('testCreateCollection - Response received:', response.status);
    console.log('testCreateCollection - Response data:', response.data);

    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('testCreateCollection - Error:', error.message);
    console.error('testCreateCollection - Full error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers,
        baseURL: error.config?.baseURL,
        data: error.config?.data
      }
    });

    return {
      success: false,
      error: error.message || 'Unknown error',
      details: error.response?.data
    };
  }
};

export const testGetCollections = async () => {
  try {
    console.log('testGetCollections - Starting test');

    // Get token
    const token = localStorage.getItem('access_token');
    console.log('testGetCollections - Token available:', !!token);

    if (!token) {
      console.error('testGetCollections - No token available');
      return { success: false, error: 'No token available' };
    }

    console.log('testGetCollections - Making GET request to:', `${BASE_URL}/notes/collections`);

    // Make API call
    const response = await axios.get(`${BASE_URL}/notes/collections`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    console.log('testGetCollections - Response received:', response.status);
    console.log('testGetCollections - Response data:', response.data);

    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('testGetCollections - Error:', error.message);
    console.error('testGetCollections - Full error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    return {
      success: false,
      error: error.message || 'Unknown error',
      details: error.response?.data
    };
  }
};
