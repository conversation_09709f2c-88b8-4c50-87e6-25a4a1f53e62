import axios from 'axios';

/**
 * Utility function to update an establishment directly using the API
 * This bypasses the regular update flow to ensure compatibility with the backend
 *
 * @param id - The ID of the establishment to update
 * @param data - The data to update the establishment with
 * @returns Promise with the updated establishment data
 */
export const updateEstablishmentDirect = async (id: string, data: any): Promise<any> => {
  // Get the token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('Authentication required - no token found');
  }

  // Format the token correctly
  const formattedToken = token.startsWith('Bearer ') ? token : `Bearer ${token}`;

  // API URL
  const url = `http://localhost:3000/etablissement/${id}`;

  console.log('Updating establishment with direct API call');
  console.log('URL:', url);
  console.log('Data:', JSON.stringify(data, null, 2));

  try {
    // Create a clean copy of the data, removing properties that shouldn't be sent
    const cleanData = { ...data };

    // List of properties that should be removed before sending to the API
    const propertiesToRemove = [
      'superAdminId',
      'superAdminProfileId',
      'etablissementId',
      'id',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'adminProfiles',
      'teacherProfiles',
      'studentProfiles',
      'superAdminProfile',
      'cardDesign' // This property is causing the 400 error
    ];

    // Remove all properties that shouldn't be sent
    propertiesToRemove.forEach(prop => {
      if (prop in cleanData) {
        delete cleanData[prop];
        console.log(`Removed ${prop} from update data`);
      }
    });

    console.log('Cleaned data for update:', JSON.stringify(cleanData, null, 2));

    // Make the API call with axios
    const response = await axios.patch(url, cleanData, {
      headers: {
        'Authorization': formattedToken,
        'Content-Type': 'application/json',
      },
    });

    console.log('Establishment updated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating establishment:', error);

    // Provide detailed error information
    if (axios.isAxiosError(error) && error.response) {
      console.error('Server responded with error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          data: error.config?.data,
          headers: error.config?.headers,
        }
      });

      // If it's a 400 error, try to provide more specific information
      if (error.response.status === 400) {
        console.error('Bad Request Details:', {
          message: error.response.data?.message,
          errors: error.response.data?.errors,
          sentData: error.config?.data
        });

        // Try to parse the error message for more details
        const errorMessage = error.response.data?.message || 'Invalid data format';
        const errorDetails = error.response.data?.errors
          ? JSON.stringify(error.response.data.errors)
          : '';

        throw new Error(`Bad Request (400): ${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);
      }

      throw new Error(`Server error (${error.response.status}): ${
        error.response.data?.message || 'Unknown error'
      }`);
    }

    throw error;
  }
};
