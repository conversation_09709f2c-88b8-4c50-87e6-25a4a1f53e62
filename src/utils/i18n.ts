import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
//en
import enTranslation from '../locales/en/translation.json';
import enCommon from '../locales/en/common.json';
import enHome from '../locales/en/home.json';
import enStudent from '../locales/en/student.json';
import enSuperAdmin from '../locales/en/super-admin.json';
import enTeacher from '../locales/en/teacher.json';
// import enParent from '../locales/en/parent.json';
import enAdmin from '../locales/en/admin.json';
import enEstablishment from '../locales/en/establishments.json';
// import enFeature from '../locales/en/feature.json'
import enFlashcard from '../locales/en/flashcard.json'
import enNotes from '../locales/en/notes.json'
// import enPublic from '../locales/en/public.json'
import enSchool from '../locales/en/school.json'
import enSchoolTemplateEditor from '../locales/en/school-template-editor.json'
import enShared from '../locales/en/shared.json'

//fr
import frTranslation from '../locales/fr/translation.json';
import frCommon from '../locales/fr/common.json';
import frHome from '../locales/fr/home.json';
import frStudent from '../locales/fr/student.json';
import frSuperAdmin from '../locales/fr/super-admin.json';
import frTeacher from '../locales/fr/teacher.json';
// import frParent from '../locales/fr/parent.json';
import frAdmin from '../locales/fr/admin.json';
import frEstablishment from '../locales/fr/establishments.json';
// import frFeature from '../locales/fr/feature.json'
import frFlashcard from '../locales/fr/flashcard.json'
import frNotes from '../locales/fr/notes.json'
// import frPublic from '../locales/fr/public.json'
import frSchool from '../locales/fr/school.json'
import frSchoolTemplateEditor from '../locales/fr/school-template-editor.json'
import frShared from '../locales/fr/shared.json'

// Initialize i18next
i18n
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    resources: {
      en: {
        translation: enTranslation,
        common: enCommon,
        home: enHome,
        student: enStudent,
        superAdmin: enSuperAdmin,
        teacher: enTeacher,
        // parent: enParent,
        admin: enAdmin,
        establishment: enEstablishment,
        // feature: enFeature,
        flashcard: enFlashcard,
        notes: enNotes,
        // public: enPublic,
        school: enSchool,
        schoolTemplateEditor: enSchoolTemplateEditor,
        shared: enShared,
      },
      fr: {
        translation: frTranslation,
        common: frCommon,
        home: frHome,
        student: frStudent,
        superAdmin: frSuperAdmin,
        teacher: frTeacher,
        // parent: frParent,
        admin: frAdmin,
        establishment: frEstablishment,
        // feature: frFeature,
        flashcard: frFlashcard,
        notes: frNotes,
        // public: frPublic,
        school: frSchool,
        schoolTemplateEditor: frSchoolTemplateEditor,
        shared: frShared,
      },
    },
    fallbackLng: 'en',
    supportedLngs: ['en', 'fr'],
    
    // Detect language from browser
    detection: {
      order: ['navigator', 'querystring', 'cookie', 'localStorage', 'htmlTag'],
      lookupQuerystring: 'lng',
      lookupCookie: 'i18next',
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage', 'cookie'],
    },
    
    interpolation: {
      escapeValue: false,
    },
    
    // Enable debug mode to see what's happening
    debug: true,
    
    // Make sure i18n is initialized before rendering
    react: {
      useSuspense: false,
    },
  });

// Log the detected language
console.log('Detected language:', i18n.language);
console.log('i18n resources loaded:', i18n.options.resources);

// Add a function to manually change the language
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

export default i18n;
