import axios from 'axios'

/**
 * Checks if the current superadmin has created an establishment
 * @returns Promise<boolean> - True if at least one establishment exists, false otherwise
 */
export const hasEstablishment = async (): Promise<boolean> => {
  try {
    const userId = localStorage.getItem('id')
    const token = localStorage.getItem('access_token')

    if (!userId || !token) {
      console.error('User ID or token not found in localStorage')
      return false
    }

    const formattedToken = token.startsWith('Bearer ')
      ? token
      : `Bearer ${token}`
    const API_BASE_URL =
      import.meta.env.VITE_API_URL || 'http://localhost:3000'

    // Fetch establishments for the current superadmin
    const response = await axios.get(
      `${API_BASE_URL}/etablissement/super-admin/${userId}`,
      {
        headers: {
          Authorization: formattedToken,
        },
      }
    )

    // Check if there's at least one establishment
    const establishments = response.data
    const hasEstablishments =
      Array.isArray(establishments) && establishments.length > 0

    // If there's at least one establishment, store its ID in localStorage
    if (hasEstablishments && establishments[0]?.id) {
      localStorage.setItem('etablissementId', establishments[0].id)
    }

    return hasEstablishments
  } catch (error) {
    console.error('Error checking for establishments:', error)
    return false
  }
}
