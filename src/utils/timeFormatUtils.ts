/**
 * Utility functions for handling time formats in the timetable system
 */

/**
 * Normalizes a time string to the format expected by the API
 * Handles various input formats like "8 => 9", "8:00 => 9:00", etc.
 *
 * @param timeStr The time string to normalize
 * @returns Normalized time string in the format "8 => 9"
 */
export function normalizeTimeString(timeStr: string): string {
  if (!timeStr) return ''

  // Parse the time string
  let [start, end] = ['', '']

  if (timeStr.includes(' => ')) {
    ;[start, end] = timeStr.split(' => ')
  } else if (timeStr.includes(' - ')) {
    ;[start, end] = timeStr.split(' - ')
  } else {
    // Try to extract time using regex
    const timeMatch = timeStr.match(
      /([0-9]{1,2}:[0-9]{2}|[0-9]{1,2}).*?([0-9]{1,2}:[0-9]{2}|[0-9]{1,2})/
    )
    if (timeMatch) {
      ;[, start, end] = timeMatch
    } else {
      return timeStr // Return original if we can't parse
    }
  }

  // Remove minutes if they are ":00"
  if (start.endsWith(':00')) {
    start = start.replace(':00', '')
  }
  if (end.endsWith(':00')) {
    end = end.replace(':00', '')
  }

  return `${start} => ${end}`
}

/**
 * Formats a time string to the display format
 *
 * @param timeStr The time string to format
 * @returns Formatted time string for display
 */
export function formatTimeForDisplay(timeStr: string): string {
  if (!timeStr) return ''

  // Parse the time string
  let [start, end] = ['', '']

  if (timeStr.includes(' => ')) {
    ;[start, end] = timeStr.split(' => ')
  } else if (timeStr.includes(' - ')) {
    ;[start, end] = timeStr.split(' - ')
  } else {
    return timeStr // Return original if we can't parse
  }

  // Add minutes if missing
  if (!start.includes(':')) start = `${start}:00`
  if (!end.includes(':')) end = `${end}:00`

  // Add leading zero if needed
  if (start.length === 4) start = `0${start}`
  if (end.length === 4) end = `0${end}`

  return `${start} => ${end}`
}

/**
 * Converts a time string to the API format
 *
 * @param timeStr The time string to convert
 * @returns Time string in API format
 */
export function convertToApiTimeFormat(timeStr: string): string {
  return normalizeTimeString(timeStr)
}

/**
 * Parses a time string and returns the hours and minutes
 *
 * @param timeStr The time string to parse (e.g., "8:30" or "8")
 * @returns An object with hours and minutes
 */
export function parseTime(timeStr: string): { hours: number; minutes: number } {
  if (!timeStr) return { hours: 0, minutes: 0 }

  if (timeStr.includes(':')) {
    const [hours, minutes] = timeStr.split(':').map(Number)
    return { hours, minutes }
  } else {
    return { hours: parseInt(timeStr), minutes: 0 }
  }
}
