/**
 * Utility functions for generating structured data (JSON-LD)
 * for better search engine understanding and rich results
 */

/**
 * Generate structured data for an educational organization (school)
 */
export function generateSchoolStructuredData(schoolData: {
  name: string
  description?: string
  url?: string
  logo?: string
  telephone?: string
  email?: string
  address?: {
    streetAddress?: string
    addressLocality?: string
    addressRegion?: string
    postalCode?: string
    addressCountry?: string
  }
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'EducationalOrganization',
    name: schoolData.name,
    description:
      schoolData.description ||
      `${schoolData.name} - K12 Educational Institution`,
    url:
      schoolData.url ||
      `https://${schoolData.name.toLowerCase().replace(/\s+/g, '')}.jeridschool.tech`,
    logo: schoolData.logo || 'https://jeridschool.tech/logo.png',
    ...(schoolData.telephone && { telephone: schoolData.telephone }),
    ...(schoolData.email && { email: schoolData.email }),
    ...(schoolData.address && {
      address: {
        '@type': 'PostalAddress',
        streetAddress: schoolData.address.streetAddress,
        addressLocality: schoolData.address.addressLocality,
        addressRegion: schoolData.address.addressRegion,
        postalCode: schoolData.address.postalCode,
        addressCountry: schoolData.address.addressCountry,
      },
    }),
  }
}

/**
 * Generate structured data for a course offering
 */
export function generateCourseStructuredData(courseData: {
  name: string
  description: string
  provider: string
  url?: string
  courseCode?: string
  startDate?: string
  endDate?: string
  educationalLevel?: string
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: courseData.name,
    description: courseData.description,
    provider: {
      '@type': 'Organization',
      name: courseData.provider,
    },
    url: courseData.url,
    ...(courseData.courseCode && { courseCode: courseData.courseCode }),
    ...(courseData.startDate && { startDate: courseData.startDate }),
    ...(courseData.endDate && { endDate: courseData.endDate }),
    ...(courseData.educationalLevel && {
      educationalLevel: courseData.educationalLevel,
    }),
  }
}

/**
 * Generate structured data for a person (teacher, student, etc.)
 */
export function generatePersonStructuredData(personData: {
  name: string
  jobTitle?: string
  description?: string
  image?: string
  url?: string
  email?: string
  telephone?: string
  worksFor?: string
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: personData.name,
    ...(personData.jobTitle && { jobTitle: personData.jobTitle }),
    ...(personData.description && { description: personData.description }),
    ...(personData.image && { image: personData.image }),
    ...(personData.url && { url: personData.url }),
    ...(personData.email && { email: personData.email }),
    ...(personData.telephone && { telephone: personData.telephone }),
    ...(personData.worksFor && {
      worksFor: {
        '@type': 'EducationalOrganization',
        name: personData.worksFor,
      },
    }),
  }
}

/**
 * Generate structured data for the JeridSchool platform
 */
export function generatePlatformStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'JeridSchool',
    applicationCategory: 'EducationalApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    description:
      'Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication.',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '256',
    },
  }
}

/**
 * Generate structured data for FAQ page
 */
export function generateFAQStructuredData(
  faqs: Array<{ question: string; answer: string }>
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  }
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbStructuredData(
  items: Array<{ name: string; url: string }>
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }
}
