import {
  generateSchoolStructuredData,
  generatePlatformStructuredData,
  generateFAQStructuredData,
} from './structuredData'

/**
 * Predefined SEO configurations for different page types
 */

// Home page SEO configuration
export const homeSEO = {
  title: 'JeridSchool - K12 Education Management System',
  description:
    'Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication.',
  keywords:
    'education management, K12, school ERP, student management, teacher management, EdTech, school administration',
  ogImage: 'https://jeridschool.tech/og-images/home.jpg',
  ogType: 'website',
  structuredData: generatePlatformStructuredData(),
}

// About page SEO configuration
export const aboutSEO = {
  title: 'About JeridSchool - Our Mission and Vision',
  description:
    "Learn about JeridSchool's mission to transform K12 education management with innovative technology solutions for schools, teachers, students, and parents.",
  keywords:
    'about JeridSchool, education technology mission, K12 management vision, EdTech company',
  ogImage: 'https://jeridschool.tech/og-images/about.jpg',
  ogType: 'website',
  structuredData: {
    '@context': 'https://schema.org',
    '@type': 'AboutPage',
    name: 'About JeridSchool',
    description:
      "Learn about JeridSchool's mission to transform K12 education management with innovative technology solutions.",
  },
}

// Contact page SEO configuration
export const contactSEO = {
  title: 'Contact JeridSchool - Get in Touch with Our Team',
  description:
    "Contact the JeridSchool team for inquiries about our K12 education management system. We're here to help schools, teachers, students, and parents.",
  keywords:
    'contact JeridSchool, education management support, K12 software help, EdTech contact',
  ogImage: 'https://jeridschool.tech/og-images/contact.jpg',
  ogType: 'website',
  structuredData: {
    '@context': 'https://schema.org',
    '@type': 'ContactPage',
    name: 'Contact JeridSchool',
    description:
      'Get in touch with the JeridSchool team for inquiries about our K12 education management system.',
  },
}

// Pricing page SEO configuration
export const pricingSEO = {
  title: 'JeridSchool Pricing - Affordable K12 Management Solutions',
  description:
    "Explore JeridSchool's flexible and affordable pricing plans for our comprehensive K12 education management system. Find the perfect plan for your school.",
  keywords:
    'JeridSchool pricing, K12 management cost, education software plans, school ERP pricing',
  ogImage: 'https://jeridschool.tech/og-images/pricing.jpg',
  ogType: 'website',
  structuredData: {
    '@context': 'https://schema.org',
    '@type': 'PriceSpecification',
    name: 'JeridSchool Pricing Plans',
    description:
      'Flexible and affordable pricing plans for our comprehensive K12 education management system.',
  },
}

// Login page SEO configuration
export const loginSEO = {
  title: 'Log In to JeridSchool - Access Your Education Management Dashboard',
  description:
    'Securely log in to your JeridSchool account to access your personalized education management dashboard for schools, teachers, students, or parents.',
  keywords:
    'JeridSchool login, education management access, K12 dashboard, school ERP login',
  ogImage: 'https://jeridschool.tech/og-images/login.jpg',
  ogType: 'website',
  noIndex: true, // Login pages typically shouldn't be indexed
}

// Registration page SEO configuration
export const registerSEO = {
  title:
    'Register for JeridSchool - Join Our K12 Education Management Platform',
  description:
    'Create your JeridSchool account to access our comprehensive K12 education management system. Start streamlining your school administration today.',
  keywords:
    'JeridSchool registration, education management signup, K12 platform account, school ERP register',
  ogImage: 'https://jeridschool.tech/og-images/register.jpg',
  ogType: 'website',
  noIndex: true, // Registration pages typically shouldn't be indexed
}

// Admin dashboard SEO configuration
export const adminDashboardSEO = {
  title: 'JeridSchool Admin Dashboard - School Management Control Center',
  description:
    "Access your JeridSchool admin dashboard to manage your school's operations, students, teachers, classes, and more in one centralized platform.",
  keywords:
    'school admin dashboard, education management system, K12 administration, school ERP control panel',
  ogImage: 'https://jeridschool.tech/og-images/admin-dashboard.jpg',
  ogType: 'website',
  noIndex: true, // Admin dashboards typically shouldn't be indexed
}

// Teacher dashboard SEO configuration
export const teacherDashboardSEO = {
  title: 'JeridSchool Teacher Dashboard - Manage Your Classes and Students',
  description:
    'Access your JeridSchool teacher dashboard to manage your classes, students, assignments, grades, and communication in one integrated platform.',
  keywords:
    'teacher dashboard, education management, class management, student tracking, K12 teaching platform',
  ogImage: 'https://jeridschool.tech/og-images/teacher-dashboard.jpg',
  ogType: 'website',
  noIndex: true, // Teacher dashboards typically shouldn't be indexed
}

// Student dashboard SEO configuration
export const studentDashboardSEO = {
  title: 'JeridSchool Student Dashboard - Track Your Academic Progress',
  description:
    'Access your JeridSchool student dashboard to view your classes, assignments, grades, schedule, and communicate with teachers in one place.',
  keywords:
    'student dashboard, education portal, academic tracking, K12 learning platform, school management',
  ogImage: 'https://jeridschool.tech/og-images/student-dashboard.jpg',
  ogType: 'website',
  noIndex: true, // Student dashboards typically shouldn't be indexed
}

// Parent dashboard SEO configuration
export const parentDashboardSEO = {
  title: "JeridSchool Parent Dashboard - Monitor Your Child's Education",
  description:
    "Access your JeridSchool parent dashboard to monitor your child's academic progress, attendance, assignments, and communicate with teachers.",
  keywords:
    'parent dashboard, child education tracking, K12 parent portal, school communication platform',
  ogImage: 'https://jeridschool.tech/og-images/parent-dashboard.jpg',
  ogType: 'website',
  noIndex: true, // Parent dashboards typically shouldn't be indexed
}

// School profile page SEO configuration generator
export function generateSchoolSEO(school: {
  name: string
  description?: string
  location?: string
  logo?: string
  subdomain?: string
}) {
  const schoolName = school.name
  const schoolDescription =
    school.description ||
    `${schoolName} - A leading K12 educational institution using JeridSchool management system`
  const subdomain =
    school.subdomain || schoolName.toLowerCase().replace(/\s+/g, '')

  return {
    title: `${schoolName} - K12 Education Management | JeridSchool`,
    description: schoolDescription,
    keywords: `${schoolName}, K12 education, school management, ${school.location || ''}, education institution`,
    ogImage:
      school.logo || 'https://jeridschool.tech/og-images/school-default.jpg',
    ogUrl: `https://${subdomain}.jeridschool.tech`,
    ogType: 'website',
    canonicalUrl: `https://${subdomain}.jeridschool.tech`,
    structuredData: generateSchoolStructuredData({
      name: schoolName,
      description: schoolDescription,
      url: `https://${subdomain}.jeridschool.tech`,
      logo: school.logo,
    }),
  }
}

// FAQ page SEO configuration
export function generateFAQSEO(
  faqs: Array<{ question: string; answer: string }>
) {
  return {
    title: 'Frequently Asked Questions - JeridSchool K12 Management System',
    description:
      "Find answers to common questions about JeridSchool's K12 education management system for schools, teachers, students, and parents.",
    keywords:
      'JeridSchool FAQ, education management questions, K12 platform help, school ERP support',
    ogImage: 'https://jeridschool.tech/og-images/faq.jpg',
    ogType: 'website',
    structuredData: generateFAQStructuredData(faqs),
  }
}
