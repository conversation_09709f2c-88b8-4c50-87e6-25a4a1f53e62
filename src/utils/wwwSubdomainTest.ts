/**
 * Test utility for www subdomain handling
 * This file contains functions to test how the application handles the www subdomain
 */

import { schoolService } from '@/services/schoolService'

// Constants for testing
const PRODUCTION_DOMAIN = 'jeridschool.tech'

/**
 * Mock implementation of isSubdomainUrl for testing
 */
function mockIsSubdomainUrl(hostname: string): boolean {
  // Special handling for 'www' subdomain
  if (hostname.startsWith('www.')) {
    return false
  }

  // Check for development subdomain (localhost with subdomain)
  if (hostname.includes('.localhost')) {
    return hostname !== 'localhost'
  }

  // Check for production subdomain
  if (hostname.includes(`.${PRODUCTION_DOMAIN}`)) {
    return true
  }

  return false
}

/**
 * Mock implementation of getCurrentSchoolFromSubdomain for testing
 */
function mockGetCurrentSchoolFromSubdomain(
  hostname: string,
  isSubdomain: boolean
): string | null {
  if (!isSubdomain) {
    return null
  }

  // Special handling for 'www' subdomain - it's not a school
  if (hostname.startsWith('www.')) {
    return null
  }

  if (hostname.includes('.localhost')) {
    const schoolName = hostname.split('.')[0]
    // Skip 'www' as a school name
    if (schoolName === 'www') {
      return null
    }
    return schoolName
  }

  if (hostname.includes(`.${PRODUCTION_DOMAIN}`)) {
    const schoolName = hostname.split('.')[0]
    // Skip 'www' as a school name
    if (schoolName === 'www') {
      return null
    }
    return schoolName
  }

  return null
}

/**
 * Test how the application handles the www subdomain
 * @returns Object with test results
 */
export function testWwwSubdomainHandling() {
  console.group('WWW Subdomain Handling Test')

  // Mock hostname for testing
  const testCases = [
    {
      hostname: 'www.jeridschool.tech',
      description: 'www subdomain in production',
    },
    {
      hostname: 'www.localhost:5173',
      description: 'www subdomain in development',
    },
    {
      hostname: 'testschool.jeridschool.tech',
      description: 'regular school subdomain in production',
    },
    {
      hostname: 'testschool.localhost:5173',
      description: 'regular school subdomain in development',
    },
    { hostname: 'localhost:5173', description: 'no subdomain in development' },
    { hostname: 'jeridschool.tech', description: 'no subdomain in production' },
  ]

  const results: Record<string, any> = {}

  // Test each case
  testCases.forEach((testCase) => {
    console.log(`Testing: ${testCase.description} (${testCase.hostname})`)

    // Extract hostname (remove port if present)
    const hostname = testCase.hostname.split(':')[0]

    // Use our mock implementations for testing
    const isSubdomain = mockIsSubdomainUrl(hostname)
    const schoolName = mockGetCurrentSchoolFromSubdomain(hostname, isSubdomain)

    results[testCase.hostname] = {
      description: testCase.description,
      isSubdomain,
      schoolName,
    }

    console.log(`  Is subdomain: ${isSubdomain}`)
    console.log(`  School name: ${schoolName || 'null'}`)
  })

  console.groupEnd()
  return results
}

/**
 * Test the current URL with the actual schoolService implementation
 */
export function testCurrentUrl() {
  const hostname = window.location.hostname
  const isSubdomain = schoolService.isSubdomainUrl()
  const schoolName = schoolService.getCurrentSchoolFromSubdomain()

  console.group('Current URL Test')
  console.log(`Hostname: ${hostname}`)
  console.log(`Is subdomain: ${isSubdomain}`)
  console.log(`School name: ${schoolName || 'null'}`)
  console.groupEnd()

  return {
    hostname,
    isSubdomain,
    schoolName,
  }
}

// Instructions for manual testing:
// 1. Open browser console
// 2. Import the function: import { testWwwSubdomainHandling, testCurrentUrl } from '@/utils/wwwSubdomainTest'
// 3. Run the functions: testWwwSubdomainHandling(), testCurrentUrl()
