/**
 * Utility functions for handling lesson groups and alternating subjects
 */

/**
 * Parse a division string into an array of numbers
 * @param divisionStr Division string in format "2,2" or "[2,2]"
 * @returns Array of numbers representing the division
 */
export function parseDivision(divisionStr?: string): number[] | undefined {
  if (!divisionStr) return undefined

  try {
    // Handle format like "2,2" or "[2,2]"
    const cleanStr = divisionStr.replace(/[\[\]]/g, '')
    return cleanStr.split(',').map((num) => parseInt(num.trim(), 10))
  } catch (error) {
    console.error('Error parsing division string:', error)
    return undefined
  }
}

/**
 * Validate a division array
 * @param division Array of division hours
 * @param totalHours Total hours that should match the sum of division
 * @returns True if division is valid
 */
export function validateDivision(
  division?: number[],
  totalHours?: number
): boolean {
  if (!division || division.length === 0) return true // No division is valid

  // Check if all numbers are positive integers
  const allPositive = division.every((num) => Number.isInteger(num) && num > 0)

  // If totalHours is provided, check if sum matches
  if (totalHours !== undefined) {
    const sum = division.reduce((acc, curr) => acc + curr, 0)
    return allPositive && sum === totalHours
  }

  return allPositive
}

/**
 * Create lesson entries for a divided subject
 * @param subject Subject information
 * @param className Class name
 * @param teacherId Teacher ID
 * @param teacherName Teacher name
 * @param roomId Room ID
 * @param roomName Room name
 * @param divide Division array
 * @returns Array of lesson entries
 */
export function createDividedLessons(
  subject: string,
  className: string,
  teacherId: string | number,
  teacherName: string,
  roomId: string | undefined,
  roomName: string | undefined,
  divide: number[]
) {
  return divide.map((hours, index) => ({
    subject: `${subject} - Group ${index + 1}`,
    className,
    teacherId,
    teacherName,
    roomId,
    roomName,
    hours,
    groupNumber: index + 1,
    isGrouped: true,
    totalGroups: divide.length,
  }))
}

/**
 * Create lesson entries for alternating subjects
 * @param subject1 First subject
 * @param subject2 Second subject
 * @param className Class name
 * @param teacher1Id First teacher ID
 * @param teacher1Name First teacher name
 * @param teacher2Id Second teacher ID
 * @param teacher2Name Second teacher name
 * @param roomId Room ID
 * @param roomName Room name
 * @param hours Hours per week
 * @param pattern Alternation pattern
 * @returns Array of lesson entries
 */
export function createAlternatingLessons(
  subject1: string,
  subject2: string,
  className: string,
  teacher1Id: string | number,
  teacher1Name: string,
  teacher2Id: string | number,
  teacher2Name: string,
  roomId: string | undefined,
  roomName: string | undefined,
  hours: number,
  pattern: 'weekly' | 'biweekly' = 'weekly'
) {
  return [
    {
      subject: subject1,
      className,
      teacherId: teacher1Id,
      teacherName: teacher1Name,
      roomId,
      roomName,
      hours,
      alternateWith: subject2,
      alternateWithTeacherId: teacher2Id,
      alternateWithTeacherName: teacher2Name,
      alternationPattern: pattern,
      weekPattern: 'odd',
    },
    {
      subject: subject2,
      className,
      teacherId: teacher2Id,
      teacherName: teacher2Name,
      roomId,
      roomName,
      hours,
      alternateWith: subject1,
      alternateWithTeacherId: teacher1Id,
      alternateWithTeacherName: teacher1Name,
      alternationPattern: pattern,
      weekPattern: 'even',
    },
  ]
}

/**
 * Format a division array to a string
 * @param division Division array
 * @returns Formatted string
 */
export function formatDivision(division?: number[]): string {
  if (!division || division.length === 0) return ''
  return division.join(',')
}

/**
 * Calculate total hours from a division array
 * @param division Division array
 * @returns Total hours
 */
export function calculateTotalHours(division?: number[]): number {
  if (!division || division.length === 0) return 0
  return division.reduce((sum, hours) => sum + hours, 0)
}
