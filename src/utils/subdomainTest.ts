/**
 * This is a utility script to test subdomain access functionality
 * It can be run in the browser console to verify that the subdomain handling works correctly
 */

import { schoolService } from '@/services/schoolService'

export function testSubdomainAccess() {
  console.group('Subdomain Access Test')

  // Test current URL
  const currentUrl = window.location.href
  console.log('Current URL:', currentUrl)

  // Test hostname
  const hostname = window.location.hostname
  console.log('Hostname:', hostname)

  // Test if current URL is a subdomain
  const isSubdomain = schoolService.isSubdomainUrl()
  console.log('Is subdomain URL:', isSubdomain)

  // Test extracting school name from URL
  const extractedSchoolName = schoolService.extractSchoolNameFromUrl(currentUrl)
  console.log('Extracted school name from URL:', extractedSchoolName)

  // Test getting school name from subdomain
  const subdomainSchoolName = schoolService.getCurrentSchoolFromSubdomain()
  console.log('School name from subdomain:', subdomainSchoolName)

  // Test generating school URL
  if (extractedSchoolName) {
    const generatedUrl = schoolService.getSchoolUrl(extractedSchoolName)
    console.log('Generated URL for school:', generatedUrl)
  }

  console.groupEnd()

  return {
    currentUrl,
    hostname,
    isSubdomain,
    extractedSchoolName,
    subdomainSchoolName,
  }
}

// Instructions for manual testing:
// 1. Open browser console
// 2. Import the function: import { testSubdomainAccess } from '@/utils/subdomainTest'
// 3. Run the function: testSubdomainAccess()
