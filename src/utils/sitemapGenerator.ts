/**
 * Sitemap Generator Utility
 *
 * This utility helps generate XML sitemaps for better search engine indexing.
 * It can be used to create a static sitemap.xml file during build time or
 * to dynamically generate sitemaps based on application data.
 */

interface SitemapUrl {
  url: string
  lastmod?: string
  changefreq?:
    | 'always'
    | 'hourly'
    | 'daily'
    | 'weekly'
    | 'monthly'
    | 'yearly'
    | 'never'
  priority?: number
}

/**
 * Generate a sitemap XML string from an array of URL objects
 */
export function generateSitemapXml(
  urls: SitemapUrl[],
  baseUrl: string = 'https://jeridschool.tech'
): string {
  const urlElements = urls
    .map((urlObj) => {
      const { url, lastmod, changefreq, priority } = urlObj
      const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`

      return `
  <url>
    <loc>${fullUrl}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
    ${changefreq ? `<changefreq>${changefreq}</changefreq>` : ''}
    ${priority !== undefined ? `<priority>${priority}</priority>` : ''}
  </url>`
    })
    .join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`
}

/**
 * Generate a sitemap index XML string from an array of sitemap URLs
 */
export function generateSitemapIndexXml(
  sitemaps: { url: string; lastmod?: string }[],
  baseUrl: string = 'https://jeridschool.tech'
): string {
  const sitemapElements = sitemaps
    .map((sitemap) => {
      const { url, lastmod } = sitemap
      const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`

      return `
  <sitemap>
    <loc>${fullUrl}</loc>
    ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
  </sitemap>`
    })
    .join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapElements}
</sitemapindex>`
}

/**
 * Generate a basic sitemap for the main application routes
 */
export function generateBasicSitemap(): string {
  const today = new Date().toISOString().split('T')[0]

  const urls: SitemapUrl[] = [
    { url: '/', lastmod: today, changefreq: 'weekly', priority: 1.0 },
    { url: '/aboutus', lastmod: today, changefreq: 'monthly', priority: 0.8 },
    { url: '/contactUs', lastmod: today, changefreq: 'monthly', priority: 0.8 },
    { url: '/pricing', lastmod: today, changefreq: 'monthly', priority: 0.9 },
    { url: '/offers', lastmod: today, changefreq: 'weekly', priority: 0.9 },
    { url: '/login', lastmod: today, changefreq: 'monthly', priority: 0.7 },
    { url: '/register', lastmod: today, changefreq: 'monthly', priority: 0.7 },
    { url: '/schools', lastmod: today, changefreq: 'weekly', priority: 0.8 },
  ]

  return generateSitemapXml(urls)
}

/**
 * Save the sitemap to a file (for build scripts)
 */
export async function saveSitemapToFile(
  sitemap: string,
  filePath: string
): Promise<void> {
  try {
    // This function would use Node.js fs module in a build script
    // For client-side, we'd need to use a different approach
    console.log('Would save sitemap to:', filePath)
    console.log(sitemap)

    // In a real implementation with Node.js:
    // const fs = require('fs');
    // fs.writeFileSync(filePath, sitemap);
  } catch (error) {
    console.error('Error saving sitemap:', error)
  }
}
