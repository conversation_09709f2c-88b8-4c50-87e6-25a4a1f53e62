import { createFileRoute, <PERSON>, useNavigate } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { services } from '@/lib/api/api'
import { useMutation } from '@tanstack/react-query'
import UserSay from '@/components/UserSay'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'

export const Route = createFileRoute('/forgot-password')({
  component: RouteComponent,
})

function RouteComponent() {
  return <ForgotPassword />
}

function ForgotPassword() {
  const navigate = useNavigate()

  const { mutate, isPending, error } = useMutation({
    mutationFn: services.auth.forgotPassword,
    onSuccess: (data) => {
      console.log('Reset link sent successfully:', data)
      navigate({ to: '/login' })
    },
    onError: (error) => {
      console.error('Forgot password error:', error)
    },
  })

  const form = useForm({
    defaultValues: {
      email: '',
    },
    onSubmit: async ({ value }) => {
      try {
        mutate(value.email)
      } catch (error) {
        console.error('Form submission error:', error)
      }
    },
  })

  return (
    <div className="flex h-screen mt-[1px]">
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-250px]">
          <UserSay
            blueWord="Forgot"
            title="Password"
            description="Enter your email to reset your password"
          />
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                <p className="text-sm text-red-700">{error.message}</p>
              </div>
            )}
            <div className="space-y-4">
              <div>
                <form.Field
                  name="email"
                  children={(field) => (
                    <input
                      type="email"
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="Email address"
                    />
                  )}
                />
              </div>
            </div>

            <div>
              <Button
                type="submit"
                disabled={isPending}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-[#FCD34D] hover:bg-[#F59E0B] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FCD34D] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isPending ? 'Sending...' : 'Reset Password'}
              </Button>
            </div>

            <div className="flex items-center justify-center mt-4">
              <Link
                to="/login"
                className="flex items-center text-sm text-[#F59E0B] hover:text-[#FCD34D] font-medium"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to login
              </Link>
            </div>
          </form>
        </div>
      </div>

      {/* Add right side image for consistency with login */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/images/auth/signin.png"
          alt="Login"
          className="absolute inset-0 w-full h-full object-cover"
        />
      </div>
    </div>
  )
}

export default ForgotPassword
