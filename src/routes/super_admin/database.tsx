import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import AdminManagement from '@/components/superAdminPage/SuperAdminManagemnt'
import SuperAdminOverview from '@/components/superAdminPage/SuperAdminOverview'
import { Home, Users, ArrowLeftCircle, LucideIcon } from 'lucide-react'
import { createFileRoute, Outlet, useSearch } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import { SuperAdminOnboardingTour } from '@/features/onboarding/SuperAdminOnboardingTour'

// Define interfaces for type safety
interface NavItem {
  label: string
  component: string
  icon: LucideIcon
}

interface SearchParams {
  section?: string
}

// Define the navigation items
const navItems: NavItem[] = [
  { label: 'Overview', component: 'Overview', icon: Home },
  {
    label: 'Admin Management',
    component: 'AdminManagement',
    icon: Users,
  },
]

// Define the route for the current view
export const Route = createFileRoute('/super_admin/database')({
  component: RouteComponent,
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const navigate = useNavigate()
  const search = useSearch({ from: '/super_admin/database' }) as SearchParams

  // Check if there's a section in the URL and update the active components accordingly
  useEffect(() => {
    if (search.section) {
      // If it's a top-level component
      setActiveComponent(search.section)
      setActiveSubComponent('')
    } else {
      // Default to Overview if no section is specified
      setActiveComponent('Overview')
      setActiveSubComponent('')
    }
  }, [search.section])

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Overview: <SuperAdminOverview />,
    AdminManagement: <AdminManagement />,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/super_admin' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
        navigate={navigate}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6 data-storage-header">
        {componentsMap[activeComponent]}
        <Outlet />

        {/* Include the SuperAdminOnboardingTour component */}
        <SuperAdminOnboardingTour currentPage="dataStorage" />
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  navigate,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
  navigate: any
}) {
  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left font-medium"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span>Back</span>
        </button>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon }) => (
          <div key={component}>
            <button
              onClick={() => {
                setActiveComponent(component)
                setActiveSubComponent('')
                // Update the URL to reflect the change
                navigate({
                  search: (prev: any) => ({
                    ...prev,
                    section: component,
                  }),
                })
              }}
              className={cn(
                'flex items-center gap-2 p-2 rounded-lg w-full text-left font-medium',
                activeComponent === component
                  ? 'bg-primary text-white'
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}
