import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import Joyride, { Step, CallBackProps } from 'react-joyride'
import { Button } from '@/components/ui/button'
import { RefreshCcw } from 'lucide-react'
import { guardSuperAdminOnly } from '../../lib/auth/routeGuards'

export const Route = createFileRoute('/super_admin/')({
  beforeLoad: guardSuperAdminOnly,
  component: RouteComponent,
})

function RouteComponent() {
  // State for controlling the tour
  const [runTour, setRunTour] = useState(false)
  const [stepIndex, setStepIndex] = useState(0)
  const [tourSteps, setTourSteps] = useState<Step[]>([])

  // Reset tour function
  const resetTour = () => {
    console.log('Resetting tour...')
    // Clear tour state in localStorage
    localStorage.removeItem('tour_completed_super_admin')
    localStorage.setItem('userOnboarding', 'true')
    localStorage.removeItem('tour_step_index')

    // Reset the tour state
    setStepIndex(0)
    setRunTour(false)

    // Start the tour after a short delay
    setTimeout(() => {
      setRunTour(true)
    }, 100)
  }

  useEffect(() => {
    const userRole = localStorage.getItem('role')
    const token = localStorage.getItem('token')

    // Check database for onboarding status
    if (token) {
      console.log('SuperAdmin Route: Checking onboarding status from database')

      // Fetch the user's onboarding status from the database
      fetch('http://localhost:3000/user-controller/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((data) => {
          console.log('SuperAdmin Route: User data from API:', data)

          // Check if userOnboarding is TRUE in the database
          if (data.userOnboarding === true) {
            console.log(
              'SuperAdmin Route: User needs onboarding according to database'
            )

            // Set localStorage to match database
            localStorage.setItem('userOnboarding', 'true')
            localStorage.removeItem('tour_completed_super_admin')

            // Force start the tour
            setTimeout(() => {
              console.log(
                'SuperAdmin Route: Force starting tour based on database status'
              )
              setStepIndex(0)
              setRunTour(true)
            }, 500)
          } else {
            console.log(
              'SuperAdmin Route: User does not need onboarding according to database'
            )
            localStorage.setItem('userOnboarding', 'false')
            localStorage.setItem('tour_completed_super_admin', 'true')
          }
        })
        .catch((error) => {
          console.error('SuperAdmin Route: Error fetching user data:', error)

          // Fallback to localStorage check
          checkLocalStorage()
        })
    } else {
      // Fallback if no token
      checkLocalStorage()
    }

    function checkLocalStorage() {
      // For new users, we need to set userOnboarding to true
      if (localStorage.getItem('userOnboarding') === null) {
        console.log(
          'SuperAdmin Route: New user detected, setting userOnboarding to true'
        )
        localStorage.setItem('userOnboarding', 'true')
      }

      // Check if we have a saved step index from a previous page
      const savedStepIndex = localStorage.getItem('tour_step_index')
      if (savedStepIndex) {
        console.log(
          `SuperAdmin Route: Found saved step index: ${savedStepIndex}`
        )
        setStepIndex(parseInt(savedStepIndex, 10))
      }
    }

    // Get current values for logging
    const currentUserOnboarding = localStorage.getItem('userOnboarding')
    const currentTourCompleted =
      localStorage.getItem('tour_completed_super_admin') === 'true'
    const currentStepIndex = localStorage.getItem('tour_step_index')

    console.log('SuperAdmin Route: Checking onboarding and tour status', {
      userRole,
      userOnboarding: currentUserOnboarding,
      tourCompleted: currentTourCompleted,
      stepIndex: currentStepIndex,
    })

    // Define simplified tour steps that only show the services page
    const steps: Step[] = [
      {
        target: 'body',
        content:
          'Welcome to the Super Admin Dashboard! This is where you can manage your school system.',
        placement: 'center',
        title: 'Super Admin Dashboard',
        disableBeacon: true,
        disableOverlay: false,
        disableScrolling: false,
        isFixed: true,
      },
      {
        target: '.super-admin-dashboard-header, h2',
        content:
          'This is the main dashboard where you can access various services.',
        placement: 'bottom',
        title: 'Services Overview',
        disableBeacon: true,
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15,
      },
      {
        target: '.card, .service-card',
        content:
          'These cards represent different services available to you. Click on any card to access that service.',
        placement: 'bottom',
        title: 'Service Cards',
        disableBeacon: true,
        disableOverlay: false,
        disableScrolling: false,
        spotlightPadding: 15,
      },
      {
        target: 'body',
        content:
          "That's it! You now know the basics of the Super Admin dashboard.",
        placement: 'center',
        title: 'Tour Complete',
        disableBeacon: true,
        disableOverlay: false,
        disableScrolling: false,
        isFixed: true,
      },
    ]
    setTourSteps(steps)

    // Logic to automatically start the tour on first visit or if onboarding
    if (
      (!currentTourCompleted && userRole?.toLowerCase().includes('super')) ||
      currentUserOnboarding === 'true'
    ) {
      // Add significant delay to ensure target elements are rendered
      setTimeout(() => {
        console.log('SuperAdmin Route: Starting tour automatically')
        setStepIndex(0)
        setRunTour(false) // First set to false to reset

        // Use a longer delay to ensure the DOM is fully loaded
        setTimeout(() => {
          console.log('SuperAdmin Route: Setting runTour to true')
          setRunTour(true) // Then set to true to start
        }, 500)
      }, 1000)
    }

    // Additional check - if we're coming from a database check that found userOnboarding=true
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('startTour') === 'true') {
      console.log(
        'SuperAdmin Route: URL parameter startTour=true detected, starting tour'
      )
      setTimeout(() => {
        setStepIndex(0)
        setRunTour(true)
      }, 800)
    }

    // Listen for the start-super-admin-tour event
    const handleStartTour = () => {
      console.log('SuperAdmin Route: Received start-super-admin-tour event')

      // Force reset any tour completion status
      localStorage.removeItem('tour_completed_super_admin')
      localStorage.setItem('userOnboarding', 'true')
      localStorage.removeItem('tour_step_index')

      // Reset the tour state
      setStepIndex(0)
      setRunTour(false) // First set to false to reset

      // Start the tour after a short delay
      setTimeout(() => {
        console.log('SuperAdmin Route: Setting runTour to true after event')
        setRunTour(true) // Then set to true to start

        // Try again after a delay in case it didn't work
        setTimeout(() => {
          console.log('SuperAdmin Route: Setting runTour to true again (retry)')
          setRunTour(false)
          setTimeout(() => {
            setRunTour(true)
          }, 100)
        }, 1000)
      }, 100)
    }

    // Add a click handler to the Reset Tour button - using a more compatible selector
    setTimeout(() => {
      const resetTourButton = document.querySelector(
        '[data-testid="reset-tour-button"]'
      )
      if (resetTourButton) {
        console.log(
          'SuperAdmin Route: Found Reset Tour button, adding click handler'
        )
        resetTourButton.addEventListener('click', () => {
          console.log('SuperAdmin Route: Reset Tour button clicked')
          localStorage.removeItem('tour_completed_super_admin')
          localStorage.setItem('userOnboarding', 'true')
          localStorage.removeItem('tour_step_index')
          setStepIndex(0)
          setRunTour(false) // First set to false to reset
          setTimeout(() => {
            setRunTour(true) // Then set to true to start
          }, 100)
        })
      }
    }, 1000) // Delay to ensure the button is rendered

    window.addEventListener('start-super-admin-tour', handleStartTour)

    return () => {
      window.removeEventListener('start-super-admin-tour', handleStartTour)

      // Clean up the Reset Tour button event listener - using a more compatible selector
      const resetTourButton = document.querySelector(
        '[data-testid="reset-tour-button"]'
      )
      if (resetTourButton) {
        // Note: We can't remove an anonymous function directly, but this is fine for cleanup
        console.log(
          'SuperAdmin Route: Removing Reset Tour button event listener'
        )
      }
    }
  }, []) // Run only once on mount

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { action, index, status, type } = data

    // Debug what's happening with the tour
    console.log('Joyride callback:', {
      action,
      index,
      status,
      type,
      step: tourSteps[index],
    })

    // Handle actions based on the callback type
    switch (type) {
      case 'step:before':
        // Log when a step is about to be shown
        console.log(
          `Step ${index + 1}/${tourSteps.length} is about to be shown`
        )
        break

      case 'step:after':
        // Log when a step has been shown
        console.log(`Step ${index + 1}/${tourSteps.length} has been shown`)

        // Just update the step index without any navigation
        if (action === 'next') {
          console.log('Next button clicked, current index:', index)

          // Update the step index
          setStepIndex((prevIndex) => {
            const newIndex = prevIndex + 1
            console.log(`Updating step index from ${prevIndex} to ${newIndex}`)
            localStorage.setItem('tour_step_index', String(newIndex))
            return newIndex
          })
        } else if (action === 'prev') {
          // Handle going back
          setStepIndex((prevIndex) => {
            const newIndex = Math.max(0, prevIndex - 1)
            console.log(`Updating step index from ${prevIndex} to ${newIndex}`)
            localStorage.setItem('tour_step_index', String(newIndex))
            return newIndex
          })
        }
        break

      case 'tour:start':
        // Log when the tour starts
        console.log('Tour started')
        break

      case 'tour:end':
        // Log when the tour ends
        console.log('Tour ended with status:', status)

        // Handle tour completion
        if (status === 'finished' || status === 'skipped') {
          console.log('Tour completed with status:', status)
          setRunTour(false)
          localStorage.setItem('tour_completed_super_admin', 'true')
          localStorage.setItem('userOnboarding', 'false')
          localStorage.removeItem('tour_step_index')

          // Call the API to update onboarding status in the backend
          try {
            const token = localStorage.getItem('token')

            console.log(
              'SuperAdmin Route: Updating onboarding status in backend'
            )

            // Try all endpoints in parallel to ensure at least one succeeds
            const updatePromises = [
              // First endpoint
              fetch('http://localhost:3000/users/onboarding', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ userOnboarding: false }), // Backend expects false to mark as complete
              })
                .then((response) => {
                  if (response.ok) {
                    console.log(
                      'SuperAdmin Route: Onboarding status updated with users/onboarding endpoint',
                      response.status
                    )
                    return true
                  }
                  return false
                })
                .catch(() => false),

              // Second endpoint
              fetch('http://localhost:3000/user/onboarding', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ userOnboarding: false }),
              })
                .then((response) => {
                  if (response.ok) {
                    console.log(
                      'SuperAdmin Route: Onboarding status updated with user/onboarding endpoint',
                      response.status
                    )
                    return true
                  }
                  return false
                })
                .catch(() => false),

              // Third endpoint
              fetch(
                'http://localhost:3000/user-controller/update-onboarding',
                {
                  method: 'PATCH',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                  },
                  body: JSON.stringify({ userOnboarding: false }),
                }
              )
                .then((response) => {
                  if (response.ok) {
                    console.log(
                      'SuperAdmin Route: Onboarding status updated with user-controller/update-onboarding endpoint',
                      response.status
                    )
                    return true
                  }
                  return false
                })
                .catch(() => false),
            ]

            // Wait for all promises to complete
            Promise.all(updatePromises).then((results) => {
              const anySucceeded = results.some((result) => result === true)
              if (anySucceeded) {
                console.log(
                  'SuperAdmin Route: Successfully updated onboarding status in backend'
                )
              } else {
                console.error(
                  'SuperAdmin Route: All API calls to update onboarding status failed'
                )

                // Try one more time with a different approach
                fetch('http://localhost:3000/user-controller/me', {
                  method: 'GET',
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                })
                  .then((response) => response.json())
                  .then((userData) => {
                    console.log(
                      'SuperAdmin Route: Got user data for update:',
                      userData
                    )

                    // Update the user with the complete user object
                    const updatedUserData = {
                      ...userData,
                      userOnboarding: false,
                    }

                    return fetch(
                      'http://localhost:3000/user-controller',
                      {
                        method: 'PUT',
                        headers: {
                          'Content-Type': 'application/json',
                          Authorization: `Bearer ${token}`,
                        },
                        body: JSON.stringify(updatedUserData),
                      }
                    )
                  })
                  .then((response) => {
                    if (response.ok) {
                      console.log(
                        'SuperAdmin Route: Updated user data with userOnboarding=false'
                      )
                    } else {
                      console.error(
                        'SuperAdmin Route: Failed to update user data'
                      )
                    }
                  })
                  .catch((error) => {
                    console.error(
                      'SuperAdmin Route: Error in fallback update:',
                      error
                    )
                  })
              }
            })
          } catch (error) {
            console.error(
              'SuperAdmin Route: Error updating onboarding status',
              error
            )
          }

          // Dispatch an event to notify other components
          window.dispatchEvent(new CustomEvent('tour-status-change'))
        }
        break

      case 'error:target_not_found':
        // Handle case where target element is not found
        console.error(`Target element "${tourSteps[index]?.target}" not found`)

        // Try to recover by moving to the next step
        if (index < tourSteps.length - 1) {
          console.log('Attempting to recover by moving to the next step')
          setStepIndex(index + 1)
          localStorage.setItem('tour_step_index', String(index + 1))
        } else {
          // If we're at the last step, just end the tour
          console.log('At last step with error, ending tour')
          setRunTour(false)
          localStorage.setItem('tour_completed_super_admin', 'true')
          localStorage.removeItem('tour_step_index')
        }
        break
    }
  }

  return (
    <>
      <div className="fixed bottom-5 right-5 z-50">
        <Button
          size="icon"
          variant="outline"
          className="bg-white hover:bg-gray-100 text-blue-500 rounded-full shadow-md h-10 w-10"
          onClick={resetTour}
          data-testid="reset-tour-button"
          title="Restart Tour"
        >
          <RefreshCcw className="h-5 w-5" />
        </Button>
      </div>

      <SuperAdminLayout />

      {tourSteps.length > 0 && (
        <Joyride
          steps={tourSteps}
          run={runTour}
          stepIndex={stepIndex}
          continuous={true}
          showProgress={true}
          showSkipButton={true}
          disableOverlayClose={true}
          disableCloseOnEsc={true}
          spotlightClicks={true}
          scrollToFirstStep={false}
          scrollOffset={0}
          hideBackButton={false}
          disableScrolling={true}
          disableScrollParentFix={true}
          disableOverlay={false}
          locale={{
            back: 'Back',
            close: 'Close',
            last: 'Finish',
            next: 'Next',
            skip: 'Skip',
          }}
          floaterProps={{
            disableAnimation: true,
            hideArrow: false,
            offset: 0,
            styles: {
              arrow: {
                length: 8,
                spread: 12,
              },
              floater: {
                filter: 'drop-shadow(0 0 10px rgba(0, 0, 0, 0.5))',
              },
            },
          }}
          callback={handleJoyrideCallback}
          debug={true}
          styles={{
            options: {
              zIndex: 10000,
              primaryColor: '#007bff',
              backgroundColor: '#ffffff',
              arrowColor: '#ffffff',
              overlayColor: 'rgba(0, 0, 0, 0.7)',
              textColor: '#333',
            },
            tooltip: {
              borderRadius: '8px',
              fontSize: '16px',
              padding: '20px',
              backgroundColor: '#ffffff',
              color: '#333',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
            },
            tooltipContainer: {
              textAlign: 'center',
              padding: '10px',
            },
            tooltipTitle: {
              fontSize: '20px',
              fontWeight: 'bold',
              marginBottom: '15px',
              color: '#007bff',
              textAlign: 'center',
            },
            tooltipContent: {
              fontSize: '16px',
              lineHeight: '1.6',
              marginBottom: '15px',
              textAlign: 'center',
            },
            buttonNext: {
              backgroundColor: '#007bff',
              fontSize: '16px',
              padding: '10px 20px',
              borderRadius: '4px',
              color: '#fff',
              fontWeight: 'bold',
              border: 'none',
              cursor: 'pointer',
            },
            buttonBack: {
              color: '#555',
              marginRight: '15px',
              fontSize: '16px',
              padding: '10px 20px',
              fontWeight: 'bold',
            },
            buttonSkip: {
              color: '#999',
              fontSize: '16px',
              fontWeight: 'bold',
            },
            spotlight: {
              backgroundColor: 'transparent',
              borderRadius: 0,
              boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.85)',
            },
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.85)',
              mixBlendMode: 'normal',
            },
            beaconInner: {
              backgroundColor: '#007bff',
            },
            beaconOuter: {
              backgroundColor: 'rgba(0, 123, 255, 0.2)',
              borderColor: '#007bff',
            },
          }}
        />
      )}
    </>
  )
}

import { LayoutDashboard, CreditCard } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Import your components
import SuperAdminService from '@/components/adminPage/SuperAdminService'

const navigation = [
  {
    name: 'Super Admin Service',
    icon: LayoutDashboard,
    component: SuperAdminService,
  },
  {
    name: 'Card Management',
    icon: CreditCard,
    component: () => (window.location.href = '/super_admin/card-management'),
  },
]

export default function SuperAdminLayout() {
  const [currentPage] = useState('Super Admin Service')

  // Check if this is a new user or if onboarding is needed and force the tour to start
  useEffect(() => {
    // Get the token
    const token = localStorage.getItem('token')

    // Check if we have a token
    if (token) {
      console.log('SuperAdminLayout: Checking onboarding status from database')

      // Fetch the user's onboarding status from the database
      fetch('http://localhost:3000/user-controller/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
        .then((response) => response.json())
        .then((data) => {
          console.log('SuperAdminLayout: User data from API:', data)

          // Check if userOnboarding is TRUE in the database
          if (data.userOnboarding === true) {
            console.log(
              'SuperAdminLayout: User needs onboarding according to database'
            )

            // Set localStorage to match database
            localStorage.setItem('userOnboarding', 'true')

            // Trigger the tour after a delay to ensure everything is loaded
            setTimeout(() => {
              console.log(
                'SuperAdminLayout: Triggering tour for user with onboarding=true'
              )
              window.dispatchEvent(new CustomEvent('start-super-admin-tour'))
            }, 2000)
          } else {
            console.log(
              'SuperAdminLayout: User does not need onboarding according to database'
            )
            localStorage.setItem('userOnboarding', 'false')
          }
        })
        .catch((error) => {
          console.error('SuperAdminLayout: Error fetching user data:', error)

          // Fallback to localStorage check if API fails
          if (localStorage.getItem('userOnboarding') === null) {
            console.log(
              'SuperAdminLayout: New user detected (fallback), setting userOnboarding to true'
            )
            localStorage.setItem('userOnboarding', 'true')

            // Trigger the tour after a delay
            setTimeout(() => {
              console.log(
                'SuperAdminLayout: Triggering tour for new user (fallback)'
              )
              window.dispatchEvent(new CustomEvent('start-super-admin-tour'))
            }, 2000)
          }
        })
    } else {
      // Fallback if no token is available
      if (localStorage.getItem('userOnboarding') === null) {
        console.log(
          'SuperAdminLayout: New user detected (no token), setting userOnboarding to true'
        )
        localStorage.setItem('userOnboarding', 'true')

        // Trigger the tour after a delay
        setTimeout(() => {
          console.log(
            'SuperAdminLayout: Triggering tour for new user (no token)'
          )
          window.dispatchEvent(new CustomEvent('start-super-admin-tour'))
        }, 2000)
      }
    }
  }, [])

  // Add effect to add classes to important elements for tour targeting
  useEffect(() => {
    // Find important elements and add id/classes for tour targeting
    const addClassesToElements = () => {
      console.log('SuperAdminLayout: Adding tour target classes to elements')

      // Profile button targeting - try multiple selectors
      const profileButton =
        document.querySelector('.admin-profile-menu') ||
        document.querySelector('.profile-button') ||
        document.querySelector('#admin-profile-button') ||
        document.querySelector('button.rounded-full') ||
        document.querySelector('button:has(img.rounded-full)') ||
        document.querySelector('button:has(.avatar)') ||
        document.querySelector('[data-testid="profile-menu-button"]') ||
        document.querySelector('.avatar') ||
        document.querySelector('img.rounded-full')?.parentElement

      if (profileButton) {
        console.log('SuperAdminLayout: Found profile button, adding class')
        profileButton.classList.add('super-admin-profile-menu')
        profileButton.setAttribute('data-testid', 'profile-button')
      } else {
        console.warn(
          'SuperAdminLayout: Profile button not found for tour targeting'
        )

        // If we can't find the profile button, let's add a class to the header
        const header = document.querySelector('header')
        if (header) {
          header.classList.add('super-admin-profile-menu')
          header.setAttribute('data-testid', 'header')
          console.log('SuperAdminLayout: Added class to header as fallback')
        }
      }

      // Add IDs to main content cards
      const mainCard =
        document.querySelector('main .card') ||
        document.querySelector('main > div > div') ||
        document.querySelector('main > div')

      if (mainCard) {
        mainCard.id = 'superadmin-service-card'
        mainCard.setAttribute('data-testid', 'main-card')
        console.log('SuperAdminLayout: Added ID to main service card')
      }

      // Find the reset tour button and add data-testid - using valid selectors
      try {
        // Use a more reliable approach to find the reset tour button
        const allButtons = document.querySelectorAll('button')
        let resetTourButton = null

        // Look through all buttons to find one with "Reset Tour" text
        for (let i = 0; i < allButtons.length; i++) {
          if (allButtons[i].textContent?.includes('Reset Tour')) {
            resetTourButton = allButtons[i]
            break
          }
        }

        // Also check for the data-testid
        if (!resetTourButton) {
          resetTourButton = document.querySelector(
            '[data-testid="reset-tour-button"]'
          )
        }

        if (resetTourButton) {
          resetTourButton.setAttribute('data-testid', 'reset-tour-button')
          console.log(
            'SuperAdminLayout: Added data-testid to reset tour button'
          )
        }
      } catch (error) {
        console.error('Error finding reset tour button:', error)
      }

      // Log all important elements found for debugging
      console.log('Tour target elements found:', {
        profileButton: !!profileButton,
        mainCard: !!mainCard,
        resetTourButton: !!document.querySelector(
          '[data-testid="reset-tour-button"]'
        ),
        mainContent: !!document.getElementById('super-admin-main-content'),
      })
    }

    // Try multiple times to ensure elements are loaded
    setTimeout(addClassesToElements, 500)
    setTimeout(addClassesToElements, 1500)
    setTimeout(addClassesToElements, 3000)

    // Also add a mutation observer to detect when new elements are added
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          addClassesToElements()
        }
      }
    })

    observer.observe(document.body, { childList: true, subtree: true })

    return () => {
      observer.disconnect()
    }
  }, [])

  const renderContent = () => {
    const route = navigation.find((item) => item.name === currentPage)
    const Component = route?.component || SuperAdminService
    return <Component />
  }

  return (
    <div className="flex min-h-screen w-[80%] m-auto">
      {/* Main Content with ID for tour targeting */}
      <main
        id="super-admin-main-content"
        className="p-4 w-full sm:w-full md:w-10/12 lg:w-10/12 xl:w-10/12"
      >
        <Card id="superadmin-service-card">
          <CardHeader>
            <CardTitle>{currentPage}</CardTitle>
          </CardHeader>
          <CardContent>{renderContent()}</CardContent>
        </Card>
      </main>
    </div>
  )
}
