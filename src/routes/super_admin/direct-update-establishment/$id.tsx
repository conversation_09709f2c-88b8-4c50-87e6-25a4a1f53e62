import { createFileRoute } from '@tanstack/react-router';
import { useParams } from '@tanstack/react-router';
import { useEstablishments } from '@/hooks/useEstablishments';
import EstablishmentDirectUpdate from '@/components/card/EstablishmentDirectUpdate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@tanstack/react-router';

export const Route = createFileRoute('/super_admin/direct-update-establishment/$id')({
  component: DirectUpdateEstablishmentPage,
});

function DirectUpdateEstablishmentPage() {
  const { id } = useParams({ from: '/super_admin/direct-update-establishment/$id' });
  const { getEstablishmentQuery } = useEstablishments();
  const { data: establishment, isLoading, isError } = getEstablishmentQuery(id);

  if (isLoading) {
    return <div className="p-4">Loading establishment data...</div>;
  }

  if (isError || !establishment) {
    return <div className="p-4">Error loading establishment data</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-4">
        <Link to="/super_admin/etablisments">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Establishments
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Establishment Information</CardTitle>
            <CardDescription>
              Current information for {establishment.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <strong>ID:</strong> {establishment.id}
              </div>
              <div>
                <strong>Name:</strong> {establishment.name}
              </div>
              <div>
                <strong>Address:</strong> {establishment.address}
              </div>
            </div>
          </CardContent>
        </Card>

        <EstablishmentDirectUpdate establishmentId={id} />

        <Card>
          <CardHeader>
            <CardTitle>Example JSON</CardTitle>
            <CardDescription>
              Copy and modify this example to update the establishment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-xs">
              {JSON.stringify({
                name: "Example School Name",
                address: "123 Education Avenue, Jerid, Tunisia",
                logo: "https://example.com/logo.png",
                url: "example-school",
                CMSContent: "<h1>Welcome to Our School</h1><p>A premier educational institution dedicated to excellence.</p>",
                description: "Example School is a leading educational institution offering comprehensive programs.",
                heroImage: "https://example.com/hero-image.jpg",
                galleryImages: [
                  "https://example.com/gallery1.jpg",
                  "https://example.com/gallery2.jpg"
                ],
                contactEmail: "<EMAIL>",
                contactPhone: "+21612345678",
                socialLinks: {
                  facebook: "https://facebook.com/example",
                  twitter: "https://twitter.com/example",
                  instagram: "https://instagram.com/example",
                  linkedin: "https://linkedin.com/company/example"
                },
                stats: {
                  students: 850,
                  teachers: 65,
                  courses: 42,
                  awards: 28
                },
                services: [
                  "Academic Excellence",
                  "Sports Programs",
                  "Arts & Music"
                ],
                isActive: true
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default DirectUpdateEstablishmentPage;
