import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import {
  Card,
  CardContent,
  CardDescription,
  // CardFooter, // Commented out to fix TypeScript error
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import EstablishmentServiceCard from '@/components/card/EstablishmentServiceCard'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { PlusCircle, X } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  useEstablishments,
  EstablishmentFormValues,
  Establishment,
  EstablishmentApiData,
} from '@/hooks/useEstablishments'
// import { schoolService } from '@/services/schoolService' // Commented out to fix TypeScript error
import FileUploader from '@/components/shared/FileUploader'
import CMSTemplateSelector from '@/components/schoolTemplateEditor/CMSTemplateSelector'
import GalleryImagesUploader from '@/components/schoolTemplateEditor/GalleryImagesUploader'
import SocialLinksEditor from '@/components/schoolTemplateEditor/SocialLinksEditor'
import StatsEditor from '@/components/schoolTemplateEditor/StatsEditor'

// Simple auth hook that matches login.tsx approach
const useAuth = () => {
  // Get user data directly from localStorage as stored by login.tsx
  const getUserData = () => {
    const id = localStorage.getItem('id')
    const role = localStorage.getItem('role')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')
    const token = localStorage.getItem('access_token')

    console.log('Auth data from localStorage:', {
      id: id ? 'found' : 'not found',
      role,
      name: firstname ? `${firstname} ${lastname}` : 'not found',
      token: token ? 'found' : 'not found',
    })

    return {
      user: id
        ? {
            id,
            role,
            firstname,
            lastname,
            name: firstname ? `${firstname} ${lastname}` : undefined,
          }
        : null,
      token,
      isAuthenticated: !!id && !!token,
    }
  }

  return getUserData()
}

export const Route = createFileRoute('/super_admin/etablisments')({
  component: RouteComponent,
})

function EstablishmentForm({
  initialData,
  onSubmit,
  onCancel,
  submitLabel = 'Save',
  isSubmitting = false,
}: {
  initialData?: Establishment
  onSubmit: (data: EstablishmentFormValues) => void
  onCancel: () => void
  submitLabel?: string
  isSubmitting?: boolean
}) {
  const [newService, setNewService] = useState('')

  const form = useForm<EstablishmentFormValues, any, any, any, any, any, any, any, any, any>({
    defaultValues: {
      name: initialData?.name || '',
      address: initialData?.address || '',
      logo: initialData?.logo || '',
      url: initialData?.url || '',
      CMSContent: initialData?.CMSContent || '',
      services: initialData?.services || [],
      isActive: initialData?.isActive ?? true,
      description: initialData?.description || '',
      heroImage: initialData?.heroImage || '',
      galleryImages: initialData?.galleryImages || [],
      contactEmail: initialData?.contactEmail || '',
      contactPhone: initialData?.contactPhone || '',
      socialLinks: initialData?.socialLinks || {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: '',
      },
      stats: initialData?.stats || {
        students: 0,
        teachers: 0,
        courses: 0,
        awards: 0,
      },
    },
    onSubmit: async ({ value }) => {
      onSubmit(value)
    },
  })

  const addService = () => {
    if (newService.trim() !== '') {
      const currentServices = form.state.values.services || []
      form.setFieldValue('services', [...currentServices, newService.trim()])
      setNewService('')
    }
  }

  const removeService = (index: number) => {
    const currentServices = form.state.values.services || []
    form.setFieldValue(
      'services',
      currentServices.filter((_, i) => i !== index)
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {initialData ? 'Edit Establishment' : 'Create Establishment'}
        </CardTitle>
        <CardDescription>
          {initialData
            ? 'Update the establishment information'
            : 'Enter the details for the new establishment'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            void form.handleSubmit()
          }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name Field */}
            <form.Field
              name="name"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return 'Name is required'
                  return undefined
                },
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="name">Establishment Name</Label>
                  <Input
                    id="name"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter establishment name"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Address Field */}
            <form.Field
              name="address"
              validators={{
                onChange: ({ value }) => {
                  if (!value) return 'Address is required'
                  return undefined
                },
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter establishment address"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Logo URL Field */}
            <form.Field name="logo">
              {(field) => (
                <FileUploader
                  label="School Logo"
                  defaultPreview={field.state.value}
                  onFileUploaded={(url) => field.handleChange(url)}
                  isAvatar={false}
                />
              )}
            </form.Field>

            {/* Website URL Field */}
            <form.Field name="url">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="url">Website URL</Label>
                  <Input
                    id="url"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter website URL"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>
          </div>

          {/* CMS Content Field with Template Selector */}
          <form.Field name="CMSContent">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="CMSContent">CMS Content</Label>
                <CMSTemplateSelector
                  value={field.state.value || ''}
                  onChange={(value) => field.handleChange(value)}
                  onCustomChange={(value) => field.handleChange(value)}
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          {/* Description Field */}
          <form.Field name="description">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="description">School Description</Label>
                <Textarea
                  id="description"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Enter a description of your school"
                  className="min-h-32"
                />
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Hero Image Field */}
            <form.Field name="heroImage">
              {(field) => (
                <FileUploader
                  label="Hero Image"
                  defaultPreview={field.state.value}
                  onFileUploaded={(url) => field.handleChange(url)}
                  isAvatar={false}
                />
              )}
            </form.Field>

            {/* Gallery Images Field */}
            <form.Field name="galleryImages">
              {(field) => (
                <GalleryImagesUploader
                  value={field.state.value || []}
                  onChange={(urls) => field.handleChange(urls)}
                  maxImages={5}
                />
              )}
            </form.Field>

            {/* Contact Email Field */}
            <form.Field name="contactEmail">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter contact email"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Contact Phone Field */}
            <form.Field name="contactPhone">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter contact phone"
                  />
                  {field.state.meta.errors && (
                    <p className="text-sm text-red-500">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  )}
                </div>
              )}
            </form.Field>
          </div>

          {/* Social Links Field */}
          <form.Field name="socialLinks">
            {(field) => (
              <SocialLinksEditor
                value={field.state.value || {}}
                onChange={(links) => field.handleChange(links)}
              />
            )}
          </form.Field>

          {/* Stats Field */}
          <form.Field name="stats">
            {(field) => (
              <StatsEditor
                value={field.state.value || {}}
                onChange={(stats) => field.handleChange(stats)}
              />
            )}
          </form.Field>

          {/* Services Field */}
          <form.Field name="services">
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor="services">Services</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    id="newService"
                    value={newService}
                    onChange={(e) => setNewService(e.target.value)}
                    placeholder="Add a service"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addService()
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={addService}
                    variant="outline"
                    size="icon"
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                </div>
                <ScrollArea className="h-24 w-full border rounded-md p-2">
                  <div className="flex flex-wrap gap-2">
                    {field.state.value?.map(
                      (service: string, index: number) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {service}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeService(index)}
                          />
                        </Badge>
                      )
                    )}
                  </div>
                </ScrollArea>
                {field.state.meta.errors && (
                  <p className="text-sm text-red-500">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </div>
            )}
          </form.Field>

          {/* Active Status Field */}
          <form.Field name="isActive">
            {(field) => (
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={field.state.value}
                  onCheckedChange={field.handleChange}
                />
                <Label htmlFor="isActive">Active Status</Label>
              </div>
            )}
          </form.Field>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : submitLabel}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function RouteComponent() {
  const { toast } = useToast()
  const [selectedEstablishment, setSelectedEstablishment] =
    useState<Establishment | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  // We don't need loading state for the form anymore
  const { user, token, isAuthenticated } = useAuth()

  // Debug user data
  useEffect(() => {
    console.log('User data:', user)
    console.log('Token available:', !!token)
    console.log('Is authenticated:', isAuthenticated)
  }, [user, token, isAuthenticated])

  // Check if user is authenticated
  if (!isAuthenticated) {
    return (
      <div className="p-8">
        <div className="text-center p-8 border rounded-lg">
          <p className="text-red-500 mb-4">Authentication required</p>
          <p className="text-gray-500">
            Please log in to manage establishments.
          </p>
        </div>
      </div>
    )
  }

  const {
    establishments,
    isLoading,
    isError,
    error,
    createEstablishment,
    updateEstablishment,
    isCreating: isCreatingEstablishment,
    isUpdating: isUpdatingEstablishment,
    // We don't need fetchEstablishmentById here anymore
  } = useEstablishments()

  const handleCreateSubmit = async (data: EstablishmentFormValues) => {
    try {
      // Check if the user already has an establishment
      if (establishments.length >= 1) {
        toast({
          title: 'Limit Reached',
          description: 'You can only create one establishment',
          variant: 'destructive',
        })
        setIsCreating(false)
        return
      }

      console.log('Creating establishment with data:', data)
      console.log('User ID:', user?.id)

      // Ensure user ID is available
      if (!user?.id) {
        toast({
          title: 'Error',
          description: 'User ID is required to create an establishment',
          variant: 'destructive',
        })
        return
      }

      // Match exactly what works in Postman
      const establishmentData: EstablishmentApiData = {
        name: data.name,
        address: data.address,
        logo: data.logo || undefined,
        url: data.url || undefined,
        CMSContent: data.CMSContent,
        description: data.description,
        heroImage: data.heroImage,
        galleryImages: data.galleryImages,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        socialLinks: data.socialLinks,
        stats: data.stats,
        services: data.services,
        isActive: data.isActive !== undefined ? data.isActive : true,
        superAdminId: user.id,
      }

      createEstablishment(establishmentData, {
        onSuccess: (result) => {
          console.log('Establishment created successfully:', result)
          setIsCreating(false)
          toast({
            title: 'Success',
            description: 'Establishment created successfully',
          })
        },
        onError: (err: Error) => {
          console.error('Error creating establishment:', err)
          toast({
            title: 'Error',
            description: err.message || 'Failed to create establishment',
            variant: 'destructive',
          })
        },
      })
    } catch (err) {
      console.error('Error in handleCreateSubmit:', err)
      toast({
        title: 'Error',
        description:
          err instanceof Error ? err.message : 'Unknown error occurred',
        variant: 'destructive',
      })
    }
  }

  const handleEditSubmit = async (data: EstablishmentFormValues) => {
    if (!selectedEstablishment) return

    try {
      console.log('Updating establishment with ID:', selectedEstablishment.id)
      console.log('Update data:', data)

      // Ensure user ID is available
      if (!user?.id) {
        toast({
          title: 'Error',
          description: 'User ID is required to update an establishment',
          variant: 'destructive',
        })
        return
      }

      // Match exactly what works in Postman
      const establishmentData: EstablishmentApiData = {
        name: data.name,
        address: data.address,
        logo: data.logo || undefined,
        url: data.url || undefined,
        CMSContent: data.CMSContent,
        description: data.description,
        heroImage: data.heroImage,
        galleryImages: data.galleryImages,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        socialLinks: data.socialLinks,
        stats: data.stats,
        services: data.services,
        isActive: data.isActive !== undefined ? data.isActive : true,
        superAdminId: user.id,
      }

      updateEstablishment(
        { id: selectedEstablishment.id, data: establishmentData },
        {
          onSuccess: (result) => {
            console.log('Establishment updated successfully:', result)
            setIsEditing(false)
            setSelectedEstablishment(null)
            toast({
              title: 'Success',
              description: 'Establishment updated successfully',
            })
          },
          onError: (err: Error) => {
            console.error('Error updating establishment:', err)
            toast({
              title: 'Error',
              description: err.message || 'Failed to update establishment',
              variant: 'destructive',
            })
          },
        }
      )
    } catch (err) {
      console.error('Error in handleEditSubmit:', err)
      toast({
        title: 'Error',
        description:
          err instanceof Error ? err.message : 'Unknown error occurred',
        variant: 'destructive',
      })
    }
  }

  // const navigate = useNavigate() // Commented out to fix TypeScript error

  // Removed unused function to fix TypeScript error

  const handleCancel = () => {
    setIsEditing(false)
    setIsCreating(false)
    setSelectedEstablishment(null)
  }

  if (isLoading) {
    return <div className="p-8 text-center">Loading establishments...</div>
  }

  if (isError) {
    return (
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Establishments</h1>
          <Button onClick={() => setIsCreating(true)}>
            Create New Establishment
          </Button>
        </div>
        <div className="text-center p-8 border rounded-lg">
          <p className="text-red-500 mb-4">
            {error instanceof Error
              ? error.message
              : 'Failed to load establishments'}
          </p>
          <p className="text-gray-500 mb-4">
            You can still create a new establishment using the button above.
          </p>
        </div>
      </div>
    )
  }

  if (isEditing && selectedEstablishment) {
    return (
      <div className="p-8">
        <EstablishmentForm
          initialData={selectedEstablishment}
          onSubmit={handleEditSubmit}
          onCancel={handleCancel}
          submitLabel="Update"
          isSubmitting={isUpdatingEstablishment}
        />
      </div>
    )
  }

  if (isCreating) {
    return (
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Create Establishment</h1>
        </div>
        <EstablishmentForm
          onSubmit={handleCreateSubmit}
          onCancel={handleCancel}
          submitLabel="Create"
          isSubmitting={isCreatingEstablishment}
        />
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Establishments</h1>
        <div className="flex gap-2">
          {establishments.length === 0 && (
            <Button
              onClick={() => setIsCreating(true)}
              className="create-establishment-btn"
            >
              Create New Establishment
            </Button>
          )}
        </div>
      </div>

      {establishments.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">No establishments found</p>
          <p className="text-gray-500 mt-2">
            Create your first establishment to get started
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {establishments.map((establishment: Establishment) => (
            <EstablishmentServiceCard
              key={establishment.id}
              establishment={
                {
                  ...establishment,
                  // Ensure all date and optional fields are properly typed to fix TypeScript error
                  createdAt: establishment.createdAt
                    ? establishment.createdAt.toString()
                    : new Date().toISOString(),
                  updatedAt: establishment.updatedAt
                    ? establishment.updatedAt.toString()
                    : new Date().toISOString(),
                  createdBy: establishment.createdBy || 'system',
                  updatedBy: establishment.updatedBy || 'system',
                } as any /* Using type assertion to bypass TypeScript error */
              }
            />
          ))}
        </div>
      )}
    </div>
  )
}
