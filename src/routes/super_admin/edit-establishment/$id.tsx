import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { PlusCircle, X, ArrowLeft } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import FileUploader from '@/components/shared/FileUploader'
import CMSTemplateSelector from '@/components/schoolTemplateEditor/CMSTemplateSelector'
import SimpleGalleryUploader from '@/components/schoolTemplateEditor/SimpleGalleryUploader'
import SocialLinksEditor from '@/components/schoolTemplateEditor/SocialLinksEditor'
import StatsEditor from '@/components/schoolTemplateEditor/StatsEditor'
import StudentCardDesigner, {
  CardDesignSettings,
} from '@/components/card/StudentCardDesigner'
import { Tabs, TabsContent} from '@/components/ui/tabs'
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

// Simple auth hook that matches login.tsx approach
const useAuth = () => {
  // Use useState to prevent re-renders
  const [authData] = useState(() => {
    const id = localStorage.getItem('id')
    const role = localStorage.getItem('role')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')
    const token = localStorage.getItem('access_token')

    console.log('Auth data from localStorage:', {
      id: id ? 'found' : 'not found',
      role,
      name: firstname ? `${firstname} ${lastname}` : 'not found',
      token: token ? 'found' : 'not found',
    })

    return {
      user: id
        ? {
            id,
            role,
            firstname,
            lastname,
            name: firstname ? `${firstname} ${lastname}` : undefined,
          }
        : null,
      token,
      isAuthenticated: !!id && !!token,
    }
  })

  return authData
}

import {
  useEstablishments,
  EstablishmentFormValues,
  Establishment,
  EstablishmentApiData,
} from '@/hooks/useEstablishments'
import { CardTemplate } from '@/components/card/card-template-selector'

// Define the route params type
export const Route = createFileRoute('/super_admin/edit-establishment/$id')({
  component: EditEstablishmentPage,
  // Add params parsing/validation if needed
  parseParams: (params) => ({
    id: params.id,
  }),
  stringifyParams: ({ id }) => ({ id }),
})

function EditEstablishmentPage() {
  // Type the params explicitly
  const { id } = useParams({ from: '/super_admin/edit-establishment/$id' })
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [establishment, setEstablishment] = useState<Establishment | null>(null)

  const {
    updateEstablishment,
    isUpdating: isUpdatingEstablishment,
    fetchEstablishmentById,
  } = useEstablishments()

  useEffect(() => {
    // Skip if id is not available
    if (!id) return

    let isMounted = true

    const loadEstablishment = async () => {
      setIsLoading(true)
      try {
        console.log('Fetching establishment with ID:', id)
        const data = await fetchEstablishmentById(id)

        // Only update state if component is still mounted
        if (isMounted) {
          console.log('Fetched establishment data:', data)
          if (data) {
            setEstablishment(data)
          } else {
            toast({
              title: 'Error',
              description: 'Failed to load establishment details',
              variant: 'destructive',
            })
            navigate({ to: '/super_admin/etablisments' })
          }
          setIsLoading(false)
        }
      } catch (err) {
        // Only update state if component is still mounted
        if (isMounted) {
          console.error('Error fetching establishment details:', err)
          toast({
            title: 'Error',
            description:
              err instanceof Error
                ? err.message
                : 'Failed to load establishment details',
            variant: 'destructive',
          })
          navigate({ to: '/super_admin/etablisments' })
          setIsLoading(false)
        }
      }
    }

    loadEstablishment()

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false
    }
  }, [id])

  const handleUpdateSubmit = async (data: EstablishmentFormValues) => {
    try {
      console.log('Updating establishment with ID:', id);
      console.log('Update data:', data);

      // Validate URL before submitting
      if (data.url) {
        validateUrlSlug(data.url);
        if (urlError) {
          toast({
            title: 'Validation Error',
            description: `URL Slug: ${urlError}`,
            variant: 'destructive',
          });
          return;
        }
      }

      if (!user?.id) {
        toast({
          title: 'Error',
          description: 'User ID is required to update an establishment',
          variant: 'destructive',
        })
        return
      }

      const establishmentData: EstablishmentApiData = {
        name: data.name,
        address: data.address,
        logo: data.logo || undefined,
        url: data.url || undefined,
        CMSContent: data.CMSContent,
        description: data.description,
        heroImage: data.heroImage,
        galleryImages: data.galleryImages,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        socialLinks: data.socialLinks,
        stats: data.stats,
        services: data.services,
        isActive: data.isActive !== undefined ? data.isActive : true,
        superAdminId: user.id,
        cardDesign: data.cardDesign || defaultCardDesign,
      }

      updateEstablishment({
        id,
        data: establishmentData,
      })

      toast({
        title: 'Success',
        description: 'Establishment updated successfully',
      })

      navigate({ to: '/super_admin/etablisments' })
    } catch (err) {
      console.error('Error updating establishment:', err)
      toast({
        title: 'Error',
        description:
          err instanceof Error ? err.message : 'Failed to update establishment',
        variant: 'destructive',
      })
    }
  }

  // Default card design settings
  const defaultCardDesign: CardDesignSettings = {
    theme: {
      id: 'blue',
      name: 'Blue Professional',
      primaryColor: '#1a237e',
      secondaryColor: '#3949ab',
      textColor: '#ffffff',
      accentColor: '#ffeb3b',
    },
    layout: 'horizontal',
    validUntilDate: 'DEC 2024',
    showQrCode: true,
    showLogo: true,
    customFields: {
      enabled: false,
      field1Label: 'Parent',
      field2Label: 'Contact',
    },
    template: {
      id: 'default',
      name: 'Default Template',
      description: '',
      style: 'modern',
      orientation: 'horizontal',
      features: [],
      preview: '',
      config: {
        html: ''
      }
    }
  }

  // Create a form state with the establishment data
  const [formState, setFormState] = useState<EstablishmentFormValues>(
    establishment || {
      name: '',
      address: '',
      logo: '',
      url: '',
      CMSContent: '',
      description: '',
      heroImage: '',
      galleryImages: [],
      contactEmail: '',
      contactPhone: '',
      socialLinks: {},
      stats: {},
      services: [],
      isActive: true,
      cardDesign: defaultCardDesign,
    }
  )

  // Add this state for URL validation errors
  const [urlError, setUrlError] = useState<string | null>(null);

  // Add this validation function
  const validateUrlSlug = (value: string) => {
    if (!value) {
      setUrlError(null);
      return;
    }
    
    const reservedNames = ['cdn', 'timetable', 'production', 'prod', 'staging', 'stag', 'http', 'https', 'api', 'rag', 'jeridschool', 'jerid-school', 'jerid_school'];
    
    if (reservedNames.includes(value.toLowerCase())) {
      setUrlError('This name is reserved and cannot be used');
      return;
    }
    
    if (/[.\/,$*]/.test(value)) {
      setUrlError('URL cannot contain symbols like . , / $ *');
      return;
    }
    
    if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
      setUrlError('URL can only contain letters, numbers, hyphens (-) and underscores (_)');
      return;
    }
    
    setUrlError(null);
  };

  // Update form state when establishment data changes
  useEffect(() => {
    if (establishment) {
      setFormState(establishment)
    }
  }, [establishment])

  // Handle form field changes
  const handleFieldChange = (field: string, value: any) => {
    setFormState((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Handle services
  const [newService, setNewService] = useState('')

  const addService = () => {
    if (!newService.trim()) return
    const currentServices = formState.services || []
    handleFieldChange('services', [...currentServices, newService.trim()])
    setNewService('')
  }

  const removeService = (index: number) => {
    const newServices = [...(formState.services || [])]
    newServices.splice(index, 1)
    handleFieldChange('services', newServices)
  }

  // Handle card design changes
  const handleCardDesignChange = (cardDesign: CardDesignSettings) => {
    handleFieldChange('cardDesign', cardDesign)
  }

  const handleCancel = () => {
    navigate({ to: '/super_admin/etablisments' })
  }

  if (isLoading) {
    return (
      <div className="p-8 text-center">Loading establishment details...</div>
    )
  }

  if (!establishment) {
    return (
      <div className="p-8 text-center">
        <p>Establishment not found</p>
        <Button
          className="mt-4"
          onClick={() => navigate({ to: '/super_admin/etablisments' })}
        >
          Back to Establishments
        </Button>
      </div>
    )
  }

  return (
    <ScrollArea className="h-[calc(100vh-80px)]">
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate({ to: '/super_admin/etablisments' })}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">Edit Establishment</h1>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Edit Establishment</CardTitle>
            <CardDescription>
              Update the details for {establishment.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="general">
              {/* <TabsList className="mb-6">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="cards">Student Cards</TabsTrigger>
              </TabsList> */}

              <TabsContent value="general">
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    handleUpdateSubmit(formState)
                  }}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Name Field */}
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={formState.name || ''}
                        onChange={(e) =>
                          handleFieldChange('name', e.target.value)
                        }
                        placeholder="Enter establishment name"
                      />
                    </div>

                    {/* Address Field */}
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={formState.address || ''}
                        onChange={(e) =>
                          handleFieldChange('address', e.target.value)
                        }
                        placeholder="Enter establishment address"
                      />
                    </div>

                    {/* Logo Field */}
                    <FileUploader
                      label="Logo"
                      defaultPreview={formState.logo}
                      onFileUploaded={(url) => handleFieldChange('logo', url)}
                      isAvatar={true}
                    />

                    {/* URL Field */}
                    <div className="space-y-2">
                      <Label htmlFor="url">URL Slug</Label>
                      <Input
                        id="url"
                        value={formState.url || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          handleFieldChange('url', value);
                          validateUrlSlug(value);
                        }}
                        onBlur={(e) => validateUrlSlug(e.target.value)}
                        placeholder="Enter URL slug (e.g., my-school)"
                      />
                      {urlError && (
                        <p className="text-sm text-red-500">{urlError}</p>
                      )}
                    </div>
                  </div>

                  {/* Description Field */}
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formState.description || ''}
                      onChange={(e) =>
                        handleFieldChange('description', e.target.value)
                      }
                      placeholder="Enter establishment description"
                      className="min-h-32"
                    />
                  </div>

                  {/* CMS Content Field with Template Selector */}
                  <div className="space-y-2">
                    <Label htmlFor="CMSContent">CMS Content</Label>
                    <CMSTemplateSelector
                      value={formState.CMSContent || ''}
                      onChange={(value) => handleFieldChange('CMSContent', value)}
                      onCustomChange={(value) =>
                        handleFieldChange('CMSContent', value)
                      }
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Hero Image Field */}
                    <FileUploader
                      label="Hero Image"
                      defaultPreview={formState.heroImage}
                      onFileUploaded={(url) =>
                        handleFieldChange('heroImage', url)
                      }
                      isAvatar={false}
                    />

                    {/* Gallery Images Field */}
                    <SimpleGalleryUploader
                      value={formState.galleryImages || []}
                      onChange={(urls) =>
                        handleFieldChange('galleryImages', urls)
                      }
                      maxImages={5}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Contact Email Field */}
                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">Contact Email</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        value={formState.contactEmail || ''}
                        onChange={(e) =>
                          handleFieldChange('contactEmail', e.target.value)
                        }
                        placeholder="Enter contact email"
                      />
                    </div>

                    {/* Contact Phone Field */}
                    <div className="space-y-2">
                      <Label htmlFor="contactPhone">Contact Phone</Label>
                      <Input
                        id="contactPhone"
                        value={formState.contactPhone || ''}
                        onChange={(e) =>
                          handleFieldChange('contactPhone', e.target.value)
                        }
                        placeholder="Enter contact phone"
                      />
                    </div>
                  </div>

                  {/* Social Links Field */}
                  <SocialLinksEditor
                    value={formState.socialLinks || {}}
                    onChange={(links) => handleFieldChange('socialLinks', links)}
                  />

                  {/* Stats Field */}
                  <StatsEditor
                    value={formState.stats || {}}
                    onChange={(stats) => handleFieldChange('stats', stats)}
                  />

                  {/* Services Field */}
                  <div className="space-y-2">
                    <Label>Services</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a service"
                        value={newService}
                        onChange={(e) => setNewService(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addService()
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={addService}
                        variant="outline"
                        size="icon"
                      >
                        <PlusCircle className="h-4 w-4" />
                      </Button>
                    </div>
                    <ScrollArea className="h-24 w-full border rounded-md p-2">
                      <div className="flex flex-wrap gap-2">
                        {(formState.services || []).map(
                          (service: string, index: number) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="flex items-center gap-1"
                            >
                              {service}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => removeService(index)}
                              />
                            </Badge>
                          )
                        )}
                      </div>
                    </ScrollArea>
                  </div>

                  {/* Active Status Field */}
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={
                        formState.isActive !== undefined
                          ? formState.isActive
                          : true
                      }
                      onCheckedChange={(checked) =>
                        handleFieldChange('isActive', checked)
                      }
                    />
                    <Label htmlFor="isActive">Active Status</Label>
                  </div>

                  <div className="flex justify-end space-x-2 mt-6">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isUpdatingEstablishment}>
                      {isUpdatingEstablishment
                        ? 'Saving...'
                        : 'Update Establishment'}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="cards">
                <div className="space-y-6">
                  <p className="text-muted-foreground">
                    Design custom student ID cards for your establishment. These
                    cards can be printed or displayed digitally.
                  </p>

                  <StudentCardDesigner
                    establishmentId={id}
                    establishmentName={formState.name}
                    establishmentLogo={formState.logo}
                    initialSettings={{
                      ...(formState.cardDesign || defaultCardDesign),
                      template: 'default' as unknown as CardTemplate
                    }}
                    onSave={handleCardDesignChange}
                  />

                  <div className="flex justify-end space-x-2 mt-6">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="button"
                      disabled={isUpdatingEstablishment}
                      onClick={() => handleUpdateSubmit(formState)}
                    >
                      {isUpdatingEstablishment ? 'Saving...' : 'Save Card Design'}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  )
}
