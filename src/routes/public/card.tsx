import { createFileRoute, useParams } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { QRCodeSVG } from 'qrcode.react'
import CdnImage from '@/components/shared/CdnImage'
import { DEFAULT_AVATAR } from '@/constants'
import { Spinner } from '@/components/ui/spinner'
import { Button } from '@/components/ui/button'
import { XCircle } from 'lucide-react'

interface PublicUserData {
  id: string
  firstname: string
  lastname: string
  fullname?: string
  role: string
  avatar?: string
  validUntil?: string
  etablissement?: {
    name: string
    logo?: string
  }
  cardInfo?: {
    issueDate: string
    cardNumber: string
  }
}

// Create the route
export const Route = createFileRoute('/public/card')({
  component: PublicCardPage,
})

function PublicCardPage() {
  const { id } = useParams({ from: '/public/card/$id' as any })

  const {
    data,
    error,
    isLoading,
  } = useQuery({
    queryKey: ['publicUser', id],
    queryFn: async () => {
      const res = await fetch(`http://localhost:3000/public/users/${id}`)
      const json = await res.json()

      if (!res.ok || json.success === false) {
        throw new Error(json.message || 'User not found or card is invalid')
      }

      return json.data as PublicUserData
    },
  })

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <Spinner size="lg" />
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100 p-4">
        <div className="bg-white p-6 rounded-lg shadow-md max-w-md w-full text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Invalid ID Card
          </h1>
          <p className="text-gray-600 mb-6">
            {(error as Error)?.message || 'This ID card is not valid or expired.'}
          </p>
          <Button
            onClick={() => (window.location.href = 'https://jeridschool.tech')}
          >
            Return to Homepage
          </Button>
        </div>
      </div>
    )
  }

  const userData = data

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100 p-4">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden max-w-md w-full">
        {/* Header */}
        <div className="w-full p-4 flex items-center justify-between bg-blue-600 text-white">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-black flex items-center justify-center">
              <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                {userData.etablissement?.logo ? (
                  <CdnImage
                    src={userData.etablissement.logo}
                    alt="Establishment Logo"
                    className="w-full h-full object-cover rounded-full"
                    fallbackSrc={DEFAULT_AVATAR}
                  />
                ) : (
                  <span className="text-xs font-bold text-black">
                    {userData.etablissement?.name
                      ? userData.etablissement.name.substring(0, 2).toUpperCase()
                      : 'JS'}
                  </span>
                )}
              </div>
            </div>
            <div>
              <div className="font-bold uppercase text-xs">
                {userData.etablissement?.name || 'JeridSchool'}
              </div>
              <div className="text-[10px] uppercase">OFFICIAL</div>
            </div>
          </div>

          {/* QR Code */}
          <div className="bg-white p-1 rounded-md">
            <QRCodeSVG
              value={`https://jeridschool.tech/public/card/${userData.id}`}
              size={50}
              level="M"
            />
          </div>
        </div>

        {/* Avatar */}
        <div className="flex justify-center -mt-6">
          <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-white bg-gray-200">
            <CdnImage
              src={userData.avatar}
              alt={userData.fullname || `${userData.firstname} ${userData.lastname}`}
              className="w-full h-full object-cover"
              fallbackSrc={DEFAULT_AVATAR}
            />
          </div>
        </div>

        {/* User Info */}
        <div className="p-6 pt-8">
          <div className="text-center mb-6">
            <div className="mb-1 text-indigo-500 text-xs font-semibold uppercase tracking-wider bg-gray-100 py-1 px-4 rounded-full inline-block">
              {userData.etablissement?.name || 'JeridSchool'}
            </div>
            <h2 className="text-lg font-bold mt-2">
              {userData.fullname || `${userData.firstname} ${userData.lastname}`}
            </h2>
            <p className="text-xs text-gray-500 capitalize">{userData.role}</p>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Card Number:</span>
              <span className="text-gray-700 text-xs">
                {userData.cardInfo?.cardNumber || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500 text-xs">Issue Date:</span>
              <span className="text-gray-700 text-xs">
                {userData.cardInfo?.issueDate
                  ? new Date(userData.cardInfo.issueDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: '2-digit',
                    })
                  : 'Not specified'}
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 pb-4 text-center text-[10px] text-gray-500">
          <p>This card is the property of {userData.etablissement?.name || 'JeridSchool'}</p>
          <p className="text-[9px]">
            If found, please return to the administration office
          </p>
        </div>
      </div>

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>This is a digital version of the ID card.</p>
        <p>Scan the QR code to verify authenticity.</p>
      </div>
    </div>
  )
}