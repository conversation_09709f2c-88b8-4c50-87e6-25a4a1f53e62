import { useState } from "react"
import { createFileRoute } from "@tanstack/react-router"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { TeacherScoreTable } from "@/components/exam/TeacherScoreTable"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, Users, GraduationCap, ClipboardCheck } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

// Define types for our data
interface Class {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
}

interface Student {
  id: string
  name: string
  scores: Record<string, string | number>
}

// Mock data
const mockClasses: Class[] = [
  { id: "class-a", name: "Class A" },
  { id: "class-b", name: "Class B" },
  { id: "class-c", name: "Class C" },
]

const mockSubjects: Subject[] = [
  { id: "math", name: "Math" },
  { id: "physics", name: "Physics" },
  { id: "art", name: "Art" },
]

// Initial mock students data
const initialMockStudents: Record<string, Student[]> = {
  "class-a": [
    {
      id: "stu1",
      name: "Alice Johnson",
      scores: {
        "Control 1": 19,
        "Control 2": 15,
        "Syntas 1": 16,
        "Syntas 2": "bull",
        "TP1": 14,
      },
    },
    {
      id: "stu2",
      name: "Bob Smith",
      scores: {
        "Control 1": 14,
        "Control 2": 10,
        "Syntas 1": 12,
      },
    },
  ],
  "class-b": [
    {
      id: "stu3",
      name: "Charlie Brown",
      scores: {
        "Control 1": 17,
        "Control 2": 18,
        "TP1": 15,
      },
    },
    {
      id: "stu4",
      name: "Diana Prince",
      scores: {
        "Control 1": 20,
        "Control 2": 19,
        "Syntas 1": 18,
        "TP2": 17,
      },
    },
  ],
}

// Define exam types
const examTypes = [
  "Control 1",
  "Control 2",
  "Syntas 1",
  "Syntas 2",
  "TP1",
  "TP2",
  "Oral 1",
  "Oral 2",
]

export const Route = createFileRoute("/teacher/exam")({
  component: TeacherExamPage,
})

function TeacherExamPage() {
  const [selectedClass, setSelectedClass] = useState<string | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Fetch classes
  const { data: classes = [], isLoading: isLoadingClasses } = useQuery<Class[]>({
    queryKey: ["teacherClasses"],
    queryFn: async () => {
      // In a real app, this would be an API call
      return new Promise<Class[]>((resolve) => {
        setTimeout(() => resolve(mockClasses), 300)
      })
    },
  })

  // Fetch subjects when a class is selected
  const { data: subjects = [], isLoading: isLoadingSubjects } = useQuery<Subject[]>({
    queryKey: ["teacherSubjects", selectedClass],
    queryFn: async () => {
      // In a real app, this would filter subjects by class
      return new Promise<Subject[]>((resolve) => {
        setTimeout(() => resolve(mockSubjects), 300)
      })
    },
    enabled: !!selectedClass,
  })

  // Fetch students when both class and subject are selected
  const { data: students = [], isLoading: isLoadingStudents } = useQuery<Student[]>({
    queryKey: ["teacherClassStudents", selectedClass, selectedSubject],
    queryFn: async () => {
      // In a real app, this would filter students by class and subject
      if (!selectedClass) return []
      return new Promise<Student[]>((resolve) => {
        setTimeout(() => resolve(initialMockStudents[selectedClass] || []), 300)
      })
    },
    enabled: !!selectedClass && !!selectedSubject,
  })

  // Mutation to update a student's score
  const updateScoreMutation = useMutation({
    mutationFn: async ({
      studentId,
      examType,
      value,
    }: {
      studentId: string
      examType: string
      value: string
    }) => {
      // In a real app, this would be an API call
      console.log(`Updating score for student ${studentId}, exam ${examType} to ${value}`)
      return new Promise<void>((resolve) => setTimeout(resolve, 300))
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teacherClassStudents"] })
      toast({
        title: "Score Updated",
        description: "The student's score has been updated successfully.",
      })
    },
  })

  const handleScoreChange = (studentId: string, examType: string, value: string) => {
    updateScoreMutation.mutate({ studentId, examType, value })
  }

  return (
    <div className="flex h-screen">
      {/* Left Sidebar - Classes */}
      <div className="w-64 border-r bg-muted/30 h-full overflow-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            My Classes
          </h2>
          {isLoadingClasses ? (
            <div className="flex justify-center p-6">
              <div className="animate-pulse text-muted-foreground">Loading classes...</div>
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-100px)]">
              <div className="space-y-2 pr-4">
                {classes.map((cls: Class) => (
                  <div
                    key={cls.id}
                    className={cn(
                      "p-3 rounded-md cursor-pointer transition-colors",
                      selectedClass === cls.id
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                    onClick={() => setSelectedClass(cls.id)}
                  >
                    <div className="font-medium">{cls.name}</div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>

      {/* Middle Section - Subjects */}
      <div className="w-64 border-r h-full overflow-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Subjects
          </h2>
          {!selectedClass ? (
            <div className="text-muted-foreground text-sm p-3">
              Please select a class first
            </div>
          ) : isLoadingSubjects ? (
            <div className="flex justify-center p-6">
              <div className="animate-pulse text-muted-foreground">Loading subjects...</div>
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-100px)]">
              <div className="space-y-2 pr-4">
                {subjects.map((subject: Subject) => (
                  <div
                    key={subject.id}
                    className={cn(
                      "p-3 rounded-md cursor-pointer transition-colors",
                      selectedSubject === subject.id
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                    onClick={() => setSelectedSubject(subject.id)}
                  >
                    <div className="font-medium">{subject.name}</div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>

      {/* Main Content - Student Scores */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Teacher Exam Dashboard</h1>
          </div>

          {selectedClass && selectedSubject ? (
            isLoadingStudents ? (
              <div className="flex justify-center p-6">
                <div className="animate-pulse text-muted-foreground">Loading student data...</div>
              </div>
            ) : students.length > 0 ? (
              <Card className="h-[calc(100vh-150px)]">
                <CardHeader className="bg-muted/50">
                  <CardTitle className="flex items-center gap-2">
                    <ClipboardCheck className="h-5 w-5" />
                    Student Scores
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6 h-[calc(100%-80px)] overflow-auto">
                  <Tabs defaultValue="scores" className="w-full">
                    <TabsList className="mb-4">
                      <TabsTrigger value="scores">Scores</TabsTrigger>
                      <TabsTrigger value="info">Information</TabsTrigger>
                    </TabsList>
                    <TabsContent value="scores" className="h-full">
                      <TeacherScoreTable
                        students={students}
                        examTypes={examTypes}
                        onScoreChange={handleScoreChange}
                      />
                    </TabsContent>
                    <TabsContent value="info">
                      <div className="space-y-4">
                        <div className="p-4 bg-muted rounded-lg">
                          <h3 className="font-medium mb-2">Instructions</h3>
                          <ul className="list-disc list-inside space-y-1">
                            <li>Double-click on a cell to edit a student's score</li>
                            <li>Press Enter to save the score</li>
                            <li>Press Escape to cancel editing</li>
                            <li>Empty cells indicate that no score has been entered yet</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-muted rounded-lg">
                          <h3 className="font-medium mb-2">Class Information</h3>
                          <p>
                            <span className="font-medium">Class:</span> {classes.find(c => c.id === selectedClass)?.name}
                          </p>
                          <p>
                            <span className="font-medium">Subject:</span> {subjects.find(s => s.id === selectedSubject)?.name}
                          </p>
                          <p>
                            <span className="font-medium">Students:</span> {students.length}
                          </p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ) : (
              <div className="flex flex-col items-center justify-center h-[calc(100vh-150px)] text-center">
                <GraduationCap className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">No Students Found</h3>
                <p className="text-muted-foreground mt-2 max-w-md">
                  There are no students assigned to this class and subject combination.
                </p>
              </div>
            )
          ) : (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-150px)] text-center">
              <BookOpen className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium">No Data Selected</h3>
              <p className="text-muted-foreground mt-2 max-w-md">
                Please select a class and subject from the sidebar to view and manage student scores.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
