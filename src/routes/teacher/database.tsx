import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import TeacherOverview from '@/components/teacherPage/TeacherOverview'
import {
  Home,
  Users,
  ArrowLeftCircle,
  BookOpen,
  GraduationCap,
  FileText,
  LucideIcon,
} from 'lucide-react'
import { createFileRoute, Outlet, useSearch } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'

// Define interfaces for type safety
interface NavItem {
  label: string
  component: string
  icon: LucideIcon
  nested?: NestedNavItem[]
}

interface NestedNavItem {
  label: string
  component: string
  icon: LucideIcon
}

interface SearchParams {
  section?: string
}

// Define the navigation items
const navItems: NavItem[] = [
  { label: 'Overview', component: 'Overview', icon: Home },
  {
    label: 'My Classes',
    component: 'MyClasses',
    icon: GraduationCap,
  },
  {
    label: 'My Students',
    component: 'MyStudents',
    icon: Users,
  },
  {
    label: 'My Subjects',
    component: 'MySubjects',
    icon: BookOpen,
  },
  {
    label: 'Assignments',
    component: 'Assignments',
    icon: FileText,
  },
]

// Define the route for the current view
export const Route = createFileRoute('/teacher/database')({
  component: RouteComponent,
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const navigate = useNavigate()
  const search = useSearch({ from: '/teacher/database' }) as SearchParams

  // Check if there's a section in the URL and update the active components accordingly
  useEffect(() => {
    if (search.section) {
      // Find which parent component contains this section
      const parentComponent = navItems.find((item) =>
        item.nested?.some(
          (nestedItem: NestedNavItem) => nestedItem.component === search.section
        )
      )

      if (parentComponent) {
        setActiveComponent(parentComponent.component)
        setActiveSubComponent(search.section)
      } else {
        // If it's a top-level component
        setActiveComponent(search.section)
        setActiveSubComponent('')
      }
    }
  }, [search.section])

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Overview: <TeacherOverview />,
    MyClasses: <div>My Classes Component</div>,
    MyStudents: <div>My Students Component</div>,
    MySubjects: <div>My Subjects Component</div>,
    Assignments: <div>Assignments Component</div>,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/teacher' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">
        {componentsMap[activeSubComponent || activeComponent]}
        <Outlet />
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  activeSubComponent,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
}) {
  // Using the navItems defined at the top of the file

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm">Back</span>
        </button>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon, nested }) => (
          <div key={component}>
            <button
              onClick={() => {
                if (nested) {
                  // If there are nested items, show them when clicked
                  setActiveComponent(component)
                  setActiveSubComponent('')
                } else {
                  // If no nested items, navigate directly
                  setActiveComponent(component)
                }
              }}
              className={cn(
                'flex items-center gap-2 p-2 rounded-lg w-full text-left',
                activeComponent === component
                  ? 'bg-primary text-white' // Primary color when active
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </button>

            {/* Render nested items if the parent component is selected */}
            {nested && activeComponent === component && (
              <div className="ml-4 space-y-2">
                {nested.map(
                  ({ label, component, icon: Icon }: NestedNavItem) => (
                    <button
                      key={component}
                      onClick={() => {
                        setActiveSubComponent(component)
                        setActiveComponent(activeComponent) // Ensure parent component remains active
                      }}
                      className={cn(
                        'flex items-center gap-2 p-2 rounded-lg w-full text-left',
                        activeSubComponent === component
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                      )}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{label}</span>
                    </button>
                  )
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
