import { createFileRoute } from '@tanstack/react-router'
import { ShieldAlert, Home, ArrowLeft } from 'lucide-react'
import { getUserRole } from '@/lib/auth/routeGuards'
import { useTranslation } from 'react-i18next'

export const Route = createFileRoute('/unauthorized')({
  component: UnauthorizedRoute,
})

function UnauthorizedRoute() {
  const { t } = useTranslation()
  const role = getUserRole()
  const goBack = () => {
    window.history.back()
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        <div className="flex justify-center mb-4">
          <ShieldAlert size={64} className="text-red-500" />
        </div>

        <h1 className="text-2xl font-bold text-gray-800 mb-2">{t('routes.unauthorized.title')}</h1>
        <p className="text-gray-600 mb-6">
          {t('routes.unauthorized.mainMessage')}
        </p>

        <div className="p-4 bg-gray-50 rounded-md mb-6 text-left">
          <p className="text-sm font-medium text-gray-700">
            {t('routes.unauthorized.roleInfo.label')}{' '}
            <span className="font-bold text-blue-600">{role}</span>
          </p>
          <p className="text-sm text-gray-500 mt-1">
            {t('routes.unauthorized.roleInfo.message')}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={goBack}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
          >
            <ArrowLeft size={16} />
            {t('routes.unauthorized.buttons.goBack')}
          </button>

          <a
            href="/"
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center justify-center gap-2 hover:bg-blue-700 transition-colors"
          >
            <Home size={16} />
            {t('routes.unauthorized.buttons.homePage')}
          </a>
        </div>
      </div>
    </div>
  )
}
