import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { Button } from '@/components/ui/button'
import { Download, Loader2, Plus } from 'lucide-react'
import { Label } from '@radix-ui/react-label'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { useState } from 'react'
import {
  Question,
  QuestionType,
  downloadQuiz,
  submitQuiz as submitQuizToServer,
} from '@/lib/Quiz'
import { guardQuizz } from '../lib/auth/routeGuards';

interface GenerateQuizParams {
  inputText: string
  questionCount: number
  quizType: string
  language: string
}

export const Route = createFileRoute('/quizz')({
  beforeLoad: guardQuizz,
  component: QuizApp,
})

function QuizApp() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [quiz, setQuiz] = useState<Question[]>([])
  const [score, setScore] = useState<{ score: number; total: number } | null>(
    null
  )
  const [selectedAnswers, setSelectedAnswers] = useState<
    Record<number, string[]>
  >({})
  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(
    null
  )
  const [editedQuestion, setEditedQuestion] = useState<Partial<Question>>({})
  const [isAddingQuestion, setIsAddingQuestion] = useState(false)
  const [newQuestion, setNewQuestion] = useState({
    text: '',
    type: 'multiple-choice' as QuestionType,
    options: ['', '', '', ''],
    correctAnswer: [] as string[], // Initialize as empty array
  })

  const form = useForm({
    defaultValues: {
      questionCount: 5,
      quizType: 'multiple-choice',
      language: 'english',
      inputText: '',
    },
    onSubmit: async ({ value }) => {
      if (!value.inputText.trim()) {
        alert('Please enter some text to generate a quiz from.')
        return
      }

      setIsGenerating(true)
      setQuiz([])
      setScore(null)
      setSelectedAnswers({})

      try {
        const generatedQuiz = await generateQuizWithOpenAI(value)
        setQuiz(generatedQuiz)
      } catch (error) {
        console.error('Quiz generation error:', error)
        alert(
          error instanceof Error
            ? error.message
            : 'Failed to generate quiz. Please try again.'
        )
      } finally {
        setIsGenerating(false)
      }
    },
  })

  async function generateQuizWithOpenAI({
    inputText,
    questionCount,
    quizType,
    language,
  }: GenerateQuizParams): Promise<Question[]> {
    const prompt = `
Generate a ${quizType} quiz based on the following text. The response must be valid JSON.
Text: ${inputText}

Requirements:
- Create exactly ${questionCount} questions
- Language: ${language}
- Each question should have 4 options for multiple-choice, 2 for true/false
- Include the correct answer in the response
- For Arabic language, ensure all text is in proper Arabic script and right-to-left formatting
- Response format must be a JSON array like this:
[
  {
    id: crypto.randomUUID(),
    "question": "What is the capital of France?",
    "options": ["Paris", "London", "Rome", "Berlin"],
    "correctAnswer": "Paris"
  }
]`

    try {
      const response = await fetch(
        'https://api.openai.com/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization:
              'Bearer ********************************************************************************************************************************************************************',
          },
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content:
                  'You are a quiz generator. Always respond with valid JSON in the specified format. For Arabic language, ensure proper Arabic script and right-to-left formatting.',
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.7,
          }),
        }
      )

      if (!response.ok) {
        const error = await response.json()
        throw new Error(
          error.error?.message || 'Failed to fetch OpenAI response'
        )
      }

      const result = await response.json()
      const quizContent = result.choices[0]?.message?.content

      if (!quizContent) {
        throw new Error('Empty response from OpenAI')
      }

      try {
        const parsedQuiz = JSON.parse(quizContent.trim()).map((q: any) => ({
          ...q,
          id: crypto.randomUUID(), // or use any other ID generation method
        }))
        if (!Array.isArray(parsedQuiz)) {
          throw new Error('Quiz response is not an array')
        }
        return parsedQuiz
      } catch (parseError) {
        console.error('Parse error:', parseError)
        throw new Error('Failed to parse quiz content - invalid JSON format')
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  const handleAnswerSelect = (questionIndex: number, answers: string[]) => {
    setSelectedAnswers((prev) => ({
      ...prev,
      [questionIndex]: answers,
    }))
  }

  const submitQuiz = () => {
    const unansweredQuestions = quiz.filter(
      (q, index) => q.type !== 'open-answer' && !selectedAnswers[index]
    )

    if (unansweredQuestions.length > 0) {
      alert(
        `Please answer all ${unansweredQuestions.length} remaining questions`
      )
      return
    }

    const result = submitQuizToServer(quiz, selectedAnswers)
    setScore(result)
  }

  const handleEditQuestion = (question: Question) => {
    setEditingQuestionId(question.id)
    setEditedQuestion(question)
  }

  const saveEditedQuestion = () => {
    if (!editingQuestionId) return

    setQuiz((prevQuiz) =>
      prevQuiz.map((q) =>
        q.id === editingQuestionId
          ? {
              ...q,
              ...editedQuestion,
              options: editedQuestion.options || q.options,
              correctAnswer: editedQuestion.correctAnswer || q.correctAnswer,
            }
          : q
      )
    )

    setEditingQuestionId(null)
  }

  const cancelEditing = () => {
    setEditingQuestionId(null)
  }

  const handleAddQuestionClick = () => {
    setIsAddingQuestion(true)
    setNewQuestion({
      text: '',
      type: 'multiple-choice',
      options: ['', '', '', ''],
      correctAnswer: [],
    })
  }

  const saveNewQuestion = () => {
    if (!newQuestion.text.trim()) {
      alert('Please enter a question')
      return
    }

    if (
      newQuestion.type !== 'open-answer' &&
      (Array.isArray(newQuestion.correctAnswer)
        ? newQuestion.correctAnswer.length === 0
        : !newQuestion.correctAnswer)
    ) {
      alert('Please select at least one correct answer')
      return
    }

    const questionToAdd: Question = {
      id: crypto.randomUUID(),
      question: newQuestion.text,
      type: newQuestion.type,
      options:
        newQuestion.type === 'true-false'
          ? ['True', 'False']
          : newQuestion.type === 'multiple-choice'
            ? newQuestion.options.filter((opt) => opt.trim())
            : [],
      correctAnswer:
        newQuestion.type !== 'open-answer'
          ? newQuestion.correctAnswer
          : undefined,
    }

    setQuiz([...quiz, questionToAdd])
    setIsAddingQuestion(false)
  }

  const updateNewQuestionOption = (index: number, value: string) => {
    const newOptions = [...newQuestion.options]
    newOptions[index] = value
    setNewQuestion({ ...newQuestion, options: newOptions })
  }

  return (
    <div className="mt-2">
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-3xl mx-auto">
              <h1 className="text-4xl font-bold text-primary text-center mb-2">
                Quiz Generator
              </h1>
              <p className="text-gray-600 text-center italic mb-8">
                Enter text, and let us generate a custom quiz for you.
              </p>

              <div className="bg-white shadow-md rounded-lg p-6 mb-8">
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    form.handleSubmit()
                  }}
                  className="space-y-6"
                >
                  <div className="space-y-4">
                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        Quiz Language
                      </Label>
                      <form.Field
                        name="language"
                        children={(field) => (
                          <select
                            name={field.name}
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                          >
                            <option value="english">English</option>
                            <option value="french">French</option>
                            <option value="arabic">Arabic</option>
                          </select>
                        )}
                      />
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        Quiz Type
                      </Label>
                      <form.Field
                        name="quizType"
                        children={(field) => (
                          <select
                            name={field.name}
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                          >
                            <option value="multiple-choice">
                              Multiple Choice
                            </option>
                            <option value="true-false">True/False</option>
                            <option value="short-answer">Short Answer</option>
                          </select>
                        )}
                      />
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        Number of Questions
                      </Label>
                      <form.Field
                        name="questionCount"
                        children={(field) => (
                          <Input
                            type="number"
                            name={field.name}
                            value={field.state.value}
                            onChange={(e) =>
                              field.handleChange(parseInt(e.target.value))
                            }
                            min="1"
                            max="20"
                            className="w-full"
                          />
                        )}
                      />
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-2">
                        Input Text
                      </Label>
                      <form.Field
                        name="inputText"
                        children={(field) => (
                          <textarea
                            name={field.name}
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                            rows={5}
                            placeholder="Enter your text here..."
                          />
                        )}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" disabled={isGenerating}>
                        {isGenerating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          'Generate Quiz'
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </div>

              {quiz.length > 0 && (
                <div className="bg-white shadow-md rounded-lg p-6 mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-2xl font-semibold">Quiz</h2>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => downloadQuiz(quiz, false, form.state.values.language === 'arabic' ? 'ar' : form.state.values.language === 'french' ? 'fr' : 'en')}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Download Quiz
                      </Button>
                      <Button
                        onClick={() => downloadQuiz(quiz, true, form.state.values.language === 'arabic' ? 'ar' : form.state.values.language === 'french' ? 'fr' : 'en')}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Download with Answers
                      </Button>
                    </div>
                  </div>
                  {quiz.map((question, index) => (
                    <div
                      key={question.id}
                      className="mb-6 p-4 border rounded-lg hover:bg-gray-50"
                    >
                      {editingQuestionId === question.id ? (
                        <div className="space-y-4">
                          <Input
                            value={editedQuestion.question}
                            onChange={(e) =>
                              setEditedQuestion({
                                ...editedQuestion,
                                question: e.target.value,
                              })
                            }
                            className="w-full"
                          />
                          <div className="space-y-2">
                            {(editedQuestion.options || []).map(
                              (option, optionIndex) => (
                                <div
                                  key={optionIndex}
                                  className="flex items-center space-x-2"
                                >
                                  <Input
                                    value={option}
                                    onChange={(e) => {
                                      const newOptions = [
                                        ...(editedQuestion.options || []),
                                      ]
                                      newOptions[optionIndex] = e.target.value
                                      setEditedQuestion({
                                        ...editedQuestion,
                                        options: newOptions,
                                      })
                                    }}
                                    className="flex-1"
                                  />
                                  <input
                                    type="checkbox"
                                    checked={
                                      Array.isArray(
                                        editedQuestion.correctAnswer
                                      )
                                        ? editedQuestion.correctAnswer.includes(
                                            option
                                          )
                                        : false
                                    }
                                    onChange={(e) => {
                                      const currentAnswers = Array.isArray(
                                        editedQuestion.correctAnswer
                                      )
                                        ? editedQuestion.correctAnswer
                                        : []

                                      const updatedAnswers = e.target.checked
                                        ? [...currentAnswers, option]
                                        : currentAnswers.filter(
                                            (answer) => answer !== option
                                          )

                                      setEditedQuestion({
                                        ...editedQuestion,
                                        correctAnswer: updatedAnswers,
                                      })
                                    }}
                                    id={`edit-q${index}-option${optionIndex}`}
                                  />
                                  <Label
                                    htmlFor={`edit-q${index}-option${optionIndex}`}
                                  >
                                    Correct
                                  </Label>
                                </div>
                              )
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <Button onClick={saveEditedQuestion}>Save</Button>
                            <Button variant="outline" onClick={cancelEditing}>
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div
                            className="font-medium mb-2 cursor-pointer"
                            onClick={() => handleEditQuestion(question)}
                          >
                            Question {index + 1}: {question.question}
                          </div>
                          <div className="space-y-2">
                            {(question.options || []).map(
                              (option, optionIndex) => (
                                <div
                                  key={optionIndex}
                                  className={`flex items-center space-x-2 ${
                                    Array.isArray(question.correctAnswer)
                                      ? question.correctAnswer.includes(option)
                                        ? 'text-green-600 font-medium'
                                        : ''
                                      : ''
                                  }`}
                                >
                                  <input
                                    type="checkbox"
                                    checked={selectedAnswers[index]?.includes(
                                      option
                                    )}
                                    onChange={(e) => {
                                      const currentAnswers =
                                        selectedAnswers[index] || []
                                      const updatedAnswers = e.target.checked
                                        ? [...currentAnswers, option]
                                        : currentAnswers.filter(
                                            (answer) => answer !== option
                                          )
                                      handleAnswerSelect(index, updatedAnswers)
                                    }}
                                    id={`q${index}-option${optionIndex}`}
                                  />
                                  <Label
                                    htmlFor={`q${index}-option${optionIndex}`}
                                  >
                                    {option}
                                    {(Array.isArray(question.correctAnswer)
                                      ? question.correctAnswer.includes(option)
                                      : false) && (
                                      <span className="ml-2 text-green-600">
                                        {' '}
                                        ✓ Correct Answer
                                      </span>
                                    )}
                                  </Label>
                                </div>
                              )
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                  <Button onClick={submitQuiz} className="w-full">
                    Submit Quiz
                  </Button>
                </div>
              )}

              {!isAddingQuestion && (
                <Button
                  onClick={handleAddQuestionClick}
                  variant="outline"
                  className="w-full mt-4 gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Custom Question
                </Button>
              )}

              {isAddingQuestion && (
                <div className="bg-gray-50 p-4 rounded-lg mt-4">
                  <h3 className="font-medium mb-3">Add New Question</h3>

                  <div className="space-y-4">
                    <div>
                      <Label>Question Text</Label>
                      <Input
                        value={newQuestion.text}
                        onChange={(e) =>
                          setNewQuestion({
                            ...newQuestion,
                            text: e.target.value,
                          })
                        }
                        className="w-full mt-1"
                      />
                    </div>

                    <div>
                      <Label>Question Type</Label>
                      <select
                        value={newQuestion.type}
                        onChange={(e) =>
                          setNewQuestion({
                            ...newQuestion,
                            type: e.target.value as QuestionType,
                            options:
                              e.target.value === 'true-false'
                                ? ['True', 'False']
                                : ['', '', '', ''],
                            correctAnswer: [],
                          })
                        }
                        className="w-full px-3 py-2 border rounded-md mt-1"
                      >
                        <option value="multiple-choice">Multiple Choice</option>
                        <option value="true-false">True/False</option>
                        <option value="open-answer">Open Answer</option>
                      </select>
                    </div>

                    {newQuestion.type === 'multiple-choice' && (
                      <div className="space-y-2">
                        <Label>Options (leave blank to remove)</Label>
                        <div className="space-y-2">
                          {newQuestion.options.map((option, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <Input
                                value={option}
                                onChange={(e) =>
                                  updateNewQuestionOption(index, e.target.value)
                                }
                                className="flex-1"
                              />
                              <input
                                type="checkbox"
                                id={`new-option-${index}`}
                                checked={
                                  Array.isArray(newQuestion.correctAnswer) &&
                                  newQuestion.correctAnswer.includes(option)
                                }
                                onChange={(e) => {
                                  const currentAnswers = Array.isArray(
                                    newQuestion.correctAnswer
                                  )
                                    ? newQuestion.correctAnswer
                                    : []

                                  const updatedAnswers = e.target.checked
                                    ? [...currentAnswers, option]
                                    : currentAnswers.filter(
                                        (answer) => answer !== option
                                      )

                                  setNewQuestion({
                                    ...newQuestion,
                                    correctAnswer: updatedAnswers,
                                  })
                                }}
                                disabled={!option.trim()}
                              />
                              <Label htmlFor={`new-option-${index}`}>
                                Correct
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {newQuestion.type === 'true-false' && (
                      <RadioGroup
                        value={
                          Array.isArray(newQuestion.correctAnswer)
                            ? newQuestion.correctAnswer[0] || ''
                            : ''
                        }
                        onValueChange={(value) =>
                          setNewQuestion({
                            ...newQuestion,
                            correctAnswer: [value],
                          })
                        }
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="True" id="true-option" />
                          <Label htmlFor="true-option">True</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="False" id="false-option" />
                          <Label htmlFor="false-option">False</Label>
                        </div>
                      </RadioGroup>
                    )}

                    <div className="flex space-x-2">
                      <Button onClick={saveNewQuestion}>Save Question</Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddingQuestion(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {score !== null && (
                <div className="bg-white shadow-md rounded-lg p-6 mb-8 text-center">
                  <h2 className="text-2xl font-semibold mb-4">Quiz Results</h2>
                  <p className="text-lg">
                    You got{' '}
                    <span className="font-bold text-primary">
                      {score.score}
                    </span>{' '}
                    out of{' '}
                    <span className="font-bold text-primary">
                      {score.total}
                    </span>{' '}
                    correct!
                  </p>
                </div>
              )}
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
