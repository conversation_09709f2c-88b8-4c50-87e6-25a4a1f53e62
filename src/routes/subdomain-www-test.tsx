import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { schoolService } from '@/services/schoolService'
import {
  testWwwSubdomainHandling,
  testCurrentUrl,
} from '@/utils/wwwSubdomainTest'

export const Route = createFileRoute('/subdomain-www-test')({
  component: SubdomainWwwTest,
})

function SubdomainWwwTest() {
  const [info, setInfo] = useState({
    hostname: '',
    isSubdomain: false,
    schoolName: '',
    fullUrl: '',
    isWww: false,
  })

  const [testResults, setTestResults] = useState<Record<string, any> | null>(
    null
  )
  const [currentUrlTest, setCurrentUrlTest] = useState<Record<
    string,
    any
  > | null>(null)

  useEffect(() => {
    // Get information about the current URL
    const hostname = window.location.hostname
    const isSubdomain = schoolService.isSubdomainUrl()
    const schoolName = schoolService.getCurrentSchoolFromSubdomain() || 'none'
    const fullUrl = window.location.href
    const isWww = hostname.startsWith('www.')

    setInfo({
      hostname,
      isSubdomain,
      schoolName,
      fullUrl,
      isWww,
    })
  }, [])

  const runTests = () => {
    const results = testWwwSubdomainHandling()
    setTestResults(results)

    // Also test the current URL
    const currentTest = testCurrentUrl()
    setCurrentUrlTest(currentTest)
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">WWW Subdomain Test Page</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Current URL Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>Full URL:</strong> {info.fullUrl}
            </p>
            <p>
              <strong>Hostname:</strong> {info.hostname}
            </p>
            <p>
              <strong>Is Subdomain:</strong> {info.isSubdomain ? 'Yes' : 'No'}
            </p>
            <p>
              <strong>School Name:</strong> {info.schoolName}
            </p>
            <p>
              <strong>Is WWW Subdomain:</strong> {info.isWww ? 'Yes' : 'No'}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test WWW Subdomain Handling</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={runTests} className="mb-4">
            Run Tests
          </Button>

          {currentUrlTest && (
            <div className="mb-4 p-3 border rounded bg-blue-50">
              <h3 className="text-lg font-semibold mb-2">Current URL Test:</h3>
              <p>
                <strong>Hostname:</strong> {currentUrlTest.hostname}
              </p>
              <p>
                <strong>Is Subdomain:</strong>{' '}
                {currentUrlTest.isSubdomain ? 'Yes' : 'No'}
              </p>
              <p>
                <strong>School Name:</strong>{' '}
                {currentUrlTest.schoolName || 'null'}
              </p>
            </div>
          )}

          {testResults && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">Test Results:</h3>
              <div className="space-y-4">
                {Object.entries(testResults).map(([hostname, result]) => (
                  <div key={hostname} className="p-3 border rounded">
                    <p className="font-medium">{hostname}</p>
                    <p className="text-sm text-gray-600">
                      {result.description}
                    </p>
                    <p className="text-sm">
                      Is Subdomain: {result.isSubdomain ? 'Yes' : 'No'}
                    </p>
                    <p className="text-sm">
                      School Name: {result.schoolName || 'null'}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Links</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <a
                href="http://localhost:5173/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Local: No Subdomain
              </a>
            </p>
            <p>
              <a
                href="http://www.localhost:5173/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Local: WWW Subdomain
              </a>
            </p>
            <p>
              <a
                href="http://testschool.localhost:5173/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Local: Test School Subdomain
              </a>
            </p>
            <p>
              <a
                href="https://jeridschool.tech/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Production: No Subdomain
              </a>
            </p>
            <p>
              <a
                href="https://www.jeridschool.tech/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Production: WWW Subdomain
              </a>
            </p>
            <p>
              <a
                href="https://testschool.jeridschool.tech/subdomain-www-test"
                className="text-blue-500 hover:underline"
              >
                Production: Test School Subdomain
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
