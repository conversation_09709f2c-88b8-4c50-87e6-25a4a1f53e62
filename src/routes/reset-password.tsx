import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { useNavigate, useSearch } from '@tanstack/react-router'
import UserSay from '@/components/UserSay'
import { resetPasswordService } from '@/lib/api/api'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export const Route = createFileRoute('/reset-password')({
  validateSearch: (search: Record<string, unknown>) => {
    return { token: search.token as string }
  },
  component: RouteComponent,
})

function RouteComponent() {
  return <ResetPassword />
}

function ResetPassword() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const search = useSearch({ from: '/reset-password' })
  const token = search.token
  const [resetError, setResetError] = useState('')
  const [resetSuccess, setResetSuccess] = useState(false)

  const { mutate, isPending: isLoading } = useMutation({
    mutationFn: resetPasswordService,
    onSuccess: () => {
      console.log('Password reset successfully')
      setResetSuccess(true)
      setResetError('')
      setTimeout(() => navigate({ to: '/login' }), 2000)
    },
    onError: (error) => {
      console.error('Failed to reset password:', error)
      setResetError(
        error.message || 'Failed to reset password. Please try again.'
      )
      setResetSuccess(false)
    },
  })

  const form = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    onSubmit: async ({ value }) => {
      if (value.password !== value.confirmPassword) {
        console.error('Passwords do not match')
        return
      }
      try {
        mutate({ token: token as string, password: value.password })
      } catch (error) {
        console.error('Form submission error:', error)
      }
    },
  })

  if (!token) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">
            {t('routes.reset-password.invalid-link.title')}
          </h1>
          <p className="mt-2 text-gray-600">
            {t('routes.reset-password.invalid-link.description')}
          </p>
        </div>
      </div>
    )
  }

  if (resetSuccess) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-green-600">
            {t('routes.reset-password.success.title')}
          </h1>
          <p className="mt-2 text-gray-600">{t('routes.reset-password.success.description')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen mt-[1px]">
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-250px]">
          <UserSay
            blueWord={t('routes.reset-password.form.title.blue-word')}
            title={t('routes.reset-password.form.title.main')}
            description={t('routes.reset-password.form.title.description')}
          />
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div>
                <form.Field
                  name="password"
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="password"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('routes.reset-password.form.inputs.new-password')}
                    />
                  )}
                />
              </div>
              <div>
                <form.Field
                  name="confirmPassword"
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="password"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('routes.reset-password.form.inputs.confirm-password')}
                    />
                  )}
                />
              </div>
            </div>
            {resetError && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4">
                <div className="flex">
                  <div>
                    <p className="text-sm text-red-700">{resetError}</p>
                  </div>
                </div>
              </div>
            )}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-[#FCD34D] hover:bg-[#F59E0B] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FCD34D] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? t('routes.reset-password.form.button.loading') : t('routes.reset-password.form.button.default')}
            </button>
          </form>
        </div>
      </div>

      {/* Add right side image for consistency with login */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/images/auth/signin.png"
          alt="Login"
          className="absolute inset-0 w-full h-full object-cover"
        />
      </div>
    </div>
  )
}
