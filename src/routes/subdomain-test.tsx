import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { schoolService } from '@/services/schoolService'
import { SubdomainDebug } from '@/components/school/SubdomainDebug'
import axios from 'axios'
import { getProxiedPath } from '@/lib/corsProxy'
import { useTranslation } from 'react-i18next'
// import { t } from 'i18next'

export const Route = createFileRoute('/subdomain-test')({
  component: SubdomainTestPage,
})

function SubdomainTestPage() {
  const navigate = useNavigate()
  const [testUrl, setTestUrl] = useState('')
  const [testResults, setTestResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [authStatus, setAuthStatus] = useState({
    isLoggedIn: false,
    token: null as string | null,
  })

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('access_token')
    setAuthStatus({
      isLoggedIn: !!token,
      token,
    })
  }, [])

  const handleTestUrl = async () => {
    if (!testUrl.trim()) return

    setIsLoading(true)
    setTestResults(null)

    try {
      // Try to get the school by URL
      const school = await schoolService.getSchoolByUrl(testUrl)
      setTestResults({
        success: true,
        school,
        message: 'School found successfully!',
      })
    } catch (error: any) {
      console.error('Error testing URL:', error)
      setTestResults({
        success: false,
        error: {
          message: error.message || 'Failed to load school',
          status: error.status || 500,
          isAuthError: error.status === 401,
          details: error.details || null,
        },
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogin = () => {
    // Store the current URL to redirect back after login
    localStorage.setItem('redirectAfterLogin', window.location.href)
    navigate({ to: '/login' })
  }

  const handleLogout = () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    setAuthStatus({
      isLoggedIn: false,
      token: null,
    })
    // Reload the page to clear any cached data
    window.location.reload()
  }

  const testDirectEndpoints = async () => {
    setIsLoading(true)
    const results: any = {
      publicEndpoint: null,
      protectedEndpoint: null,
    }

    const API_BASE_URL =
      import.meta.env.VITE_API_URL || 'http://localhost:3000'
    const IS_DEV = import.meta.env.DEV
    const testSchoolName = testUrl.trim() || 'test'

    // Test public endpoint
    try {
      // Construct the endpoint URL
      const publicEndpointPath = `/public/etablissement/url/${testSchoolName}`

      // Use the proxy in development mode
      const publicRequestUrl = IS_DEV
        ? getProxiedPath(publicEndpointPath, 'api')
        : `${API_BASE_URL}${publicEndpointPath}`

      console.log('Testing public endpoint:', publicRequestUrl)

      const response = await axios.get(publicRequestUrl, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      })
      results.publicEndpoint = {
        success: true,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.error('Public endpoint error:', error)
      results.publicEndpoint = {
        success: false,
        status: error.response?.status,
        message: error.message,
      }
    }

    // Test protected endpoint with auth if available
    const token = localStorage.getItem('access_token')
    if (token) {
      try {
        // Construct the endpoint URL
        const protectedEndpointPath = `/etablissement/url/${testSchoolName}`

        // Use the proxy in development mode
        const protectedRequestUrl = IS_DEV
          ? getProxiedPath(protectedEndpointPath, 'api')
          : `${API_BASE_URL}${protectedEndpointPath}`

        console.log('Testing protected endpoint (with auth):', protectedRequestUrl)

        const response = await axios.get(protectedRequestUrl, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        })
        results.protectedEndpoint = {
          success: true,
          status: response.status,
          data: response.data,
        }
      } catch (error: any) {
        console.error('Protected endpoint error:', error)
        results.protectedEndpoint = {
          success: false,
          status: error.response?.status,
          message: error.message,
        }
      }
    } else {
      results.protectedEndpoint = {
        success: false,
        message: 'No authentication token available',
      }
    }

    setTestResults(results)
    setIsLoading(false)
  }

  const { t } = useTranslation()

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">{t('routes.subdomain-test.title')}</h1>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">{t('routes.subdomain-test.auth.title')}</h2>
        <div className="mb-4">
          <p className="mb-2">
            <span className="font-medium">{t('routes.subdomain-test.auth.status.label')}</span>{' '}
            {authStatus.isLoggedIn ? (
              <span className="text-green-600 font-semibold">{t('routes.subdomain-test.auth.status.loggedIn')}</span>
            ) : (
              <span className="text-red-600 font-semibold">{t('routes.subdomain-test.auth.status.notLoggedIn')}</span>
            )}
          </p>
          {authStatus.isLoggedIn ? (
            <button
              onClick={handleLogout}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            >
              {t('routes.subdomain-test.auth.buttons.logout')}
            </button>
          ) : (
            <button
              onClick={handleLogin}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              {t('routes.subdomain-test.auth.buttons.login')}
            </button>
          )}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">{t('routes.subdomain-test.currentUrl.title')}</h2>
        <SubdomainDebug />
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">{t('routes.subdomain-test.testUrl.title')}</h2>
        <div className="flex mb-4">
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            placeholder="Enter school URL (e.g., 'test-school')"
            className="flex-1 border border-gray-300 rounded-l px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleTestUrl}
            disabled={isLoading}
            className={`bg-blue-600 text-white px-4 py-2 rounded-r hover:bg-blue-700 transition-colors ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isLoading ? 'Testing...' : 'Test URL'}
          </button>
        </div>
        <button
          onClick={testDirectEndpoints}
          disabled={isLoading}
          className={`bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors ${
            isLoading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {t('routes.subdomain-test.buttons.testDirectEndpoints')}
        </button>
      </div>

      {testResults && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">{t('routes.subdomain-test.results.title')}</h2>
          {testResults.success === true && (
            <div className="p-4 bg-green-100 border border-green-300 rounded mb-4">
              <p className="text-green-700 font-medium">
                {testResults.message}
              </p>
              <div className="mt-4">
                <h3 className="font-semibold mb-2">{t('routes.subdomain-test.results.schoolInfo')}</h3>
                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(testResults.school, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {testResults.success === false && (
            <div className="p-4 bg-red-100 border border-red-300 rounded mb-4">
              <p className="text-red-700 font-medium">
                {testResults.error.message}
              </p>
              <p className="text-red-700">
                Status: {testResults.error.status}
                {testResults.error.isAuthError && ' (Authentication Required)'}
              </p>
              {testResults.error.isAuthError && !authStatus.isLoggedIn && (
                <div className="mt-2">
                  <button
                    onClick={handleLogin}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors mt-2"
                  >
                    {t('routes.subdomain-test.auth.buttons.login')}
                  </button>
                </div>
              )}
              {testResults.error.details && (
                <div className="mt-4">
                  <h3 className="font-semibold mb-2">{t('routes.subdomain-test.results.errorDetails')}</h3>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(testResults.error.details, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* Direct API test results */}
          {testResults.publicEndpoint && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">{t('routes.subdomain-test.results.publicEndpoint')}</h3>
              <div
                className={`p-4 ${
                  testResults.publicEndpoint.success
                    ? 'bg-green-100 border-green-300'
                    : 'bg-red-100 border-red-300'
                } border rounded mb-4`}
              >
                <p
                  className={`${
                    testResults.publicEndpoint.success
                      ? 'text-green-700'
                      : 'text-red-700'
                  } font-medium`}
                >
                  Status:{' '}
                  {testResults.publicEndpoint.status ||
                    (testResults.publicEndpoint.success ? '200' : 'Error')}
                </p>
                {testResults.publicEndpoint.message && (
                  <p className="text-red-700">
                    {t('routes.subdomain-test.results.error.failed')}
                  </p>
                )}
                {testResults.publicEndpoint.data && (
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto mt-2">
                    {JSON.stringify(testResults.publicEndpoint.data, null, 2)}
                  </pre>
                )}
              </div>
            </div>
          )}

          {testResults.protectedEndpoint && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">{t('routes.subdomain-test.results.protectedEndpoint')}</h3>
              <div
                className={`p-4 ${
                  testResults.protectedEndpoint.success
                    ? 'bg-green-100 border-green-300'
                    : 'bg-red-100 border-red-300'
                } border rounded mb-4`}
              >
                <p
                  className={`${
                    testResults.protectedEndpoint.success
                      ? 'text-green-700'
                      : 'text-red-700'
                  } font-medium`}
                >
                  Status:{' '}
                  {testResults.protectedEndpoint.status ||
                    (testResults.protectedEndpoint.success ? '200' : 'Error')}
                </p>
                {testResults.protectedEndpoint.message && (
                  <p className="text-red-700">
                    {t('routes.subdomain-test.results.noAuth')}
                  </p>
                )}
                {testResults.protectedEndpoint.data && (
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto mt-2">
                    {JSON.stringify(
                      testResults.protectedEndpoint.data,
                      null,
                      2
                    )}
                  </pre>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 text-center">
        <button
          onClick={() => navigate({ to: '/' })}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
        >
          Back to Home
        </button>
      </div>
    </div>
  )
}
