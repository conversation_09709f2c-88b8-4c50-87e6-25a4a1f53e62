import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import {
  Home,
  ArrowLeftCircle,
  Sparkles,
  Table,
} from 'lucide-react'
import { createFileRoute } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import { Overview } from '@/components/adminPage/timetable/Overview'
import TimetableGenerator from '@/components/adminPage/timetable/TimetableGenerator'
import { TimetableDisplay } from '@/components/adminPage/timetable/TimetableDisplay'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { TimetableResult } from '@/lib/api/services/timetable-service'
import { dashboardTimetableService } from '@/lib/api/services/dashboard-timetable-service'
import { useQueryClient } from '@tanstack/react-query'

// Define the route for the current view
export const Route = createFileRoute('/admin/timetable')({
  component: RouteComponent,
  loader: async () => {
    return null
  },
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const [generatedTimetable, setGeneratedTimetable] =
    useState<TimetableResult | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()
  const navigate = useNavigate() // Get the navigate function here
  const queryClient = useQueryClient()

  // Load existing timetable on component mount
  useEffect(() => {
    const loadExistingTimetable = async () => {
      try {
        setIsLoading(true)
        console.log('Checking for existing timetables...')
        const timetables =
          await dashboardTimetableService.getCurrentEstablishmentTimetables()

        if (timetables && timetables.length > 0) {
          // Find active timetable or use the first one
          const activeTimetable =
            timetables.find((t) => t.isActive) || timetables[0]
          console.log('Found existing timetable:', activeTimetable.id)

          // Set the timetable data
          // Handle different data structures
          let timetableData: TimetableResult = { scheduleData: [], stats: {} }

          // Check if data is a string (JSON) and parse it
          if (typeof activeTimetable.data === 'string') {
            try {
              const parsedData = JSON.parse(activeTimetable.data)
              console.log('Parsed timetable data from string:', parsedData)

              if (parsedData.scheduleData) {
                timetableData.scheduleData = parsedData.scheduleData
              } else if (parsedData.schedule) {
                timetableData.schedule = parsedData.schedule
              }

              if (parsedData.stats) {
                timetableData.stats = parsedData.stats
              }
            } catch (e) {
              console.error('Error parsing timetable data:', e)
            }
          } else if (activeTimetable.data) {
            // Data is already an object
            console.log('Using timetable data as object')

            if (activeTimetable.data.scheduleData) {
              timetableData.scheduleData = activeTimetable.data.scheduleData
            } else if (activeTimetable.data.schedule) {
              timetableData.schedule = activeTimetable.data.schedule
            }

            if (activeTimetable.data.stats) {
              timetableData.stats = activeTimetable.data.stats
            }
          }

          console.log(
            'DETAILED DATA FROM PORT 3000:',
            JSON.stringify(activeTimetable.data, null, 2)
          )
          console.log(
            'Final timetable data structure:',
            JSON.stringify(timetableData, null, 2)
          )
          setGeneratedTimetable(timetableData)

          // Log the exact structure that will be passed to TimetableDisplay
          const displayData = formatTimetableForDisplay(timetableData)
          console.log(
            'Data passed to TimetableDisplay:',
            JSON.stringify(displayData, null, 2)
          )
        } else {
          console.log('No existing timetables found')
        }
      } catch (error) {
        console.error('Error loading existing timetable:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadExistingTimetable()
  }, [])

  // Listen for events from child components
  useEffect(() => {
    const handleNavigateToGenerator = () => {
      setActiveComponent('TimetableGenerator')
    }

    const handleNavigateToDisplay = () => {
      // If we already have a generated timetable, navigate to it
      if (generatedTimetable) {
        setActiveComponent('DisplayTimetable')
      } else {
        // Try to load a saved timetable
        // For now, we'll just redirect to the generator
        setActiveComponent('TimetableGenerator')

        toast({
          title: 'No Timetable Found',
          description: 'Please generate a timetable first.',
          variant: 'default',
        })
      }
    }

    // Handle toast events from the sidebar
    const handleShowToast = (event: CustomEvent) => {
      if (event.detail) {
        toast({
          title: event.detail.title,
          description: event.detail.description,
          variant: 'default',
        })
      }
    }

    // Add event listeners
    window.addEventListener(
      'navigate-timetable-generator',
      handleNavigateToGenerator
    )
    window.addEventListener(
      'navigate-timetable-display',
      handleNavigateToDisplay
    )
    window.addEventListener(
      'show-toast',
      handleShowToast as EventListener
    )

    // Clean up
    return () => {
      window.removeEventListener(
        'navigate-timetable-generator',
        handleNavigateToGenerator
      )
      window.removeEventListener(
        'navigate-timetable-display',
        handleNavigateToDisplay
      )
      window.removeEventListener(
        'show-toast',
        handleShowToast as EventListener
      )
    }
  }, [generatedTimetable, toast])

  // Handle timetable generation
  const handleTimetableGenerated = (data: TimetableResult) => {
    console.log('Timetable generated:', data)
    setGeneratedTimetable(data)
    setActiveComponent('DisplayTimetable')

    toast({
      title: 'Timetable Generated',
      description: 'Your timetable has been generated and is ready to view.',
    })
  }

  // Format the timetable data for display
  const formatTimetableForDisplay = (data: TimetableResult | null) => {
    console.log('Formatting timetable data:', JSON.stringify(data, null, 2))

    // Check if schedule data exists
    if (!data) {
      console.warn('No schedule data to format')
      return null
    }

    // IMPORTANT: If the data already has scheduleData, just return it directly
    // This preserves the original structure for the TimetableDisplay component
    if (
      data.scheduleData &&
      Array.isArray(data.scheduleData) &&
      data.scheduleData.length > 0
    ) {
      console.log('Using original scheduleData format')
      return {
        name: 'Generated Timetable',
        scheduleData: data.scheduleData,
        stats: data.stats || {
          classes: 0,
          teachers: 0,
          rooms: 0,
          periods: 0,
          days: 0,
          assignments: 0,
        },
      }
    }

    // For other formats, convert to the expected structure
    // Create a standardized format for the timetable display component
    const formattedData = {
      name: 'Generated Timetable',
      schedule: {} as Record<
        string,
        Array<{
          subject: string
          teacher: string
          room: string
          day: string
          period: number // Use period instead of slot
        }>
      >,
      stats: data.stats || {
        classes: 0,
        teachers: 0,
        rooms: 0,
        periods: 0,
        days: 0,
        assignments: 0,
      },
    }

    // Check which format we have (schedule or scheduleData)
    if (data.schedule) {
      // Convert the schedule data to the format expected by TimetableDisplay
      Object.entries(data.schedule).forEach(([className, assignments]) => {
        formattedData.schedule[className] = assignments.map(
          (assignment: any) => {
            // Make sure each assignment has a 'period' field (not 'slot')
            return {
              subject: assignment.subject,
              teacher: assignment.teacher,
              room: assignment.room || 'Not specified',
              day: assignment.day,
              period: assignment.period || 1, // Ensure we use 'period'
              time:
                assignment.time ||
                `${assignment.period} => ${assignment.period + 1}`,
              // Preserve group information if present
              ...(assignment.group1 ? { group1: assignment.group1 } : {}),
              ...(assignment.group2 ? { group2: assignment.group2 } : {}),
            }
          }
        )
      })
    }

    console.log('Formatted timetable data:', formattedData)
    return formattedData
  }

  // No sample data - we only use real data from the server

  // Handle timetable deletion
  const handleDeleteTimetable = async () => {
    if (!generatedTimetable) {
      toast({
        title: 'Error',
        description: 'No timetable to delete',
        variant: 'destructive',
      })
      return
    }

    try {
      // For local deletion (when the timetable is only in memory)
      toast({
        title: 'Timetable Deleted',
        description: 'The timetable has been removed from memory.',
      })

      // Update UI
      setGeneratedTimetable(null)
      setActiveComponent('Overview')

      // Try to delete from server if it exists there
      try {
        console.log('Fetching timetables to find active one for deletion')
        const timetables =
          await dashboardTimetableService.getCurrentEstablishmentTimetables()
        console.log('Retrieved timetables:', timetables)

        if (!timetables || timetables.length === 0) {
          console.log('No timetables found on server, only deleted from memory')
          return
        }

        const activeTimetable = timetables.find((t) => t.isActive)

        if (!activeTimetable) {
          console.log('No active timetable found on server')
          return
        }

        console.log('Found active timetable to delete:', activeTimetable.id)

        // Delete the timetable using the NestJS endpoint
        await dashboardTimetableService.deleteTimetable(activeTimetable.id)
        console.log('Delete request sent successfully to server')

        // Update cache
        queryClient.invalidateQueries({ queryKey: ['active-timetable'] })
      } catch (serverError) {
        console.error('Error deleting timetable from server:', serverError)
        // We don't show an error toast here since we already deleted from memory
      }
    } catch (error) {
      console.error('Error in delete operation:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete the timetable. Please try again.',
        variant: 'destructive',
      })
    }
  }

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Overview: <Overview />,
    TimetableGenerator: (
      <TimetableGenerator onTimetableGenerated={handleTimetableGenerated} />
    ),
    DisplayTimetable: isLoading ? (
      // Show loading state
      <div className="flex flex-col items-center justify-center h-[80vh] space-y-4">
        <h2 className="text-2xl font-bold">Loading Timetable...</h2>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    ) : generatedTimetable ? (
      <TimetableDisplay
        timetableData={formatTimetableForDisplay(generatedTimetable)}
        onBack={() => setActiveComponent('TimetableGenerator')}
        onRegenerate={() => setActiveComponent('TimetableGenerator')}
        onDelete={handleDeleteTimetable}
      />
    ) : (
      // If no timetable is generated, show a message and redirect to generator
      <div className="flex flex-col items-center justify-center h-[80vh] space-y-4">
        <h2 className="text-2xl font-bold">No Timetable Available</h2>
        <p className="text-gray-500">Please generate a timetable first.</p>
        <Button onClick={() => setActiveComponent('TimetableGenerator')}>
          Create Timetable
        </Button>
      </div>
    ),
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/admin' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
        hasGeneratedTimetable={!!generatedTimetable}
        isLoading={isLoading}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">
        {componentsMap[activeSubComponent || activeComponent]}
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  hasGeneratedTimetable,
  isLoading,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
  hasGeneratedTimetable: boolean
  isLoading: boolean
}) {
  // Basic navigation items
  let navItems = [
    { label: 'Dashboard', component: 'Overview', icon: Home },
    {
      label: 'Timetable Generator',
      component: 'TimetableGenerator',
      icon: Sparkles,
    },
    {
      label: 'View Generated Timetable',
      component: 'DisplayTimetable',
      icon: Table,
    },
  ]

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm">Back</span>
        </button>

        {/* Navigation items */}
        <div className="sidebar">
          {navItems.map(({ label, component, icon: Icon }, index) => (
            <div key={component}>
              <button
                onClick={() => {
                  // If trying to view timetable but none exists, redirect to generator
                  if (component === 'DisplayTimetable' && !hasGeneratedTimetable) {
                    setActiveComponent('TimetableGenerator')
                    // Show a toast notification
                    const event = new CustomEvent('show-toast', {
                      detail: {
                        title: 'No Timetable Available',
                        description: 'Please generate a timetable first.',
                      }
                    });
                    window.dispatchEvent(event);
                  } else {
                    setActiveComponent(component)
                  }
                  setActiveSubComponent('')
                }}
                className={cn(
                  'flex items-center gap-2 p-2 rounded-lg w-full text-left',
                  activeComponent === component
                    ? 'bg-primary text-white' // Primary color when active
                    : component === 'DisplayTimetable'
                      ? isLoading
                        ? 'bg-blue-100 text-blue-800 hover:bg-blue-200' // Loading state
                        : hasGeneratedTimetable
                          ? 'bg-green-100 text-green-800 hover:bg-green-200' // Highlight for generated timetable
                          : 'bg-gray-200 text-gray-500 hover:bg-gray-300' // Gray button when no timetable
                      : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                )}
                data-tour={`sidebar-button-${index}`}
              >
                <Icon className="h-5 w-5" />
                <span>{label}</span>
                {component === 'DisplayTimetable' && !hasGeneratedTimetable && (
                  <span className="ml-2 text-xs bg-gray-300 text-gray-700 px-1 py-0.5 rounded">
                    Create First
                  </span>
                )}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
