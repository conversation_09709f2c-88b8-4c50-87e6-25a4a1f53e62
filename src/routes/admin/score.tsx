import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { ScoreStudent } from '../../components/adminPage/score/ScoreStudent'
import { MoyeneStudent } from '../../components/adminPage/score/MoyeneStudent'
import { cn } from '@/lib/utils'
import { Home, Calendar, ArrowLeftCircle } from 'lucide-react'
import { useNavigate } from '@tanstack/react-router'

// Sample data for scores
const scoreData = {
  Ti1: {
    subjects: [
      { subject: 'Math', average: 85 },
      { subject: 'Physics', average: 78 },
      { subject: 'Chemistry', average: 82 },
    ],
    semesters: [
      { semester: 'Semester 1', average: 80 },
      { semester: 'Semester 2', average: 85 },
      { semester: 'Semester 3', average: 88 },
    ],
  },
  Ti2: {
    subjects: [
      { subject: 'Math', average: 90 },
      { subject: 'Physics', average: 85 },
      { subject: 'Chemistry', average: 87 },
    ],
    semesters: [
      { semester: 'Semester 1', average: 85 },
      { semester: 'Semester 2', average: 88 },
      { semester: 'Semester 3', average: 90 },
    ],
  },
}

// Define the route for the current view
export const Route = createFileRoute('/admin/score')({
  component: RouteComponent,
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('ScoreStudent')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const [selectedClass, setSelectedClass] = useState('Ti1') // Default selected class
  const navigate = useNavigate() // Get the navigate function here

  // Get the score data for the selected class
  const getClassScores = () => {
    return (
      scoreData[selectedClass as keyof typeof scoreData] || {
        subjects: [],
        semesters: [],
      }
    )
  }

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    ScoreStudent: (
      <ScoreStudent studentId="default" subjects={getClassScores().subjects} />
    ),
    MoyeneStudent: (
      <MoyeneStudent
        studentId="default"
        semesters={getClassScores().semesters}
      />
    ),
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/admin' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
        selectedClass={selectedClass}
        setSelectedClass={setSelectedClass}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">
        {componentsMap[activeSubComponent || activeComponent]}
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  selectedClass,
  setSelectedClass,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
  selectedClass: string
  setSelectedClass: (classId: string) => void
}) {
  const navItems = [
    { label: 'Score by Subject', component: 'ScoreStudent', icon: Home },
    { label: 'Semester Averages', component: 'MoyeneStudent', icon: Calendar },
  ]

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm">Back</span>
        </button>

        {/* Class Selection Dropdown */}
        <select
          value={selectedClass}
          onChange={(e) => setSelectedClass(e.target.value)}
          className="p-2 border rounded w-full"
        >
          {Object.keys(scoreData).map((cls) => (
            <option key={cls} value={cls}>
              {cls}
            </option>
          ))}
        </select>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon }) => (
          <div key={component}>
            <button
              onClick={() => {
                setActiveComponent(component)
                setActiveSubComponent('')
              }}
              className={cn(
                'flex items-center gap-2 p-2 rounded-lg w-full text-left',
                activeComponent === component
                  ? 'bg-primary text-white' // Primary color when active
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </button>
          </div>
        ))}
      </div>
    </div>
  )
}
