import { useState } from 'react'
import { cn } from '@/lib/utils'
import { Home, Users, Settings, ArrowLeftCircle } from 'lucide-react'
import { createFileRoute } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import ListEliminate from '@/components/adminPage/student/ListEliminate'
import EliminationOverview from '@/components/adminPage/student/EliminationOverView'

// Define the route for the current view
export const Route = createFileRoute('/admin/elimination')({
  component: RouteComponent,
})

function SettingsComponent() {
  return <div>Settings Component</div>
}

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const navigate = useNavigate() // Get the navigate function here

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Overview: <EliminationOverview />,
    Users: <ListEliminate />,
    Settings: <SettingsComponent />,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/admin' }) // Navigate back to the previous route
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        goBack={goBack}
        activeComponent={activeComponent} // Pass the active component to Sidebar
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">{componentsMap[activeComponent]}</div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  goBack,
  activeComponent,
}: {
  setActiveComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
}) {
  const navItems = [
    { label: 'Overview', component: 'Overview', icon: Home },
    { label: 'Users', component: 'Users', icon: Users },
    { label: 'Settings', component: 'Settings', icon: Settings },
  ]

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm">Back</span>
        </button>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon }) => (
          <button
            key={component}
            onClick={() => setActiveComponent(component)}
            className={cn(
              'flex items-center gap-2 p-2 rounded-lg w-full text-left',
              activeComponent === component
                ? 'bg-primary text-white' // Primary color when active
                : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
            )}
          >
            <Icon className="h-5 w-5" />
            <span>{label}</span>
          </button>
        ))}
      </div>
    </div>
  )
}
