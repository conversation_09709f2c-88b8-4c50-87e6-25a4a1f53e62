import { useState } from "react"
import { createFileRoute } from "@tanstack/react-router"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { AdminScoreTable, Student } from "@/components/exam/AdminScoreTable"
import { FormulaDialog } from "@/components/exam/FormulaDialog"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, Calculator, Users } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { generateCombinedReport } from "@/components/exam/academic-report"

// Define types for our data
interface Class {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
}

// Mock data
const mockClasses: Class[] = [
  { id: "class-a", name: "Class A" },
  { id: "class-b", name: "Class B" },
  { id: "class-c", name: "Class C" },
]

const mockSubjects: Subject[] = [
  { id: "math", name: "Math" },
  { id: "physics", name: "Physics" },
  { id: "art", name: "Art" },
]

// Initial mock students data
const initialMockStudents: Record<string, Student[]> = {
  "class-a": [
    {
      id: "stu1",
      name: "Alice Johnson",
      school: "Prestige International Academy",
      className: "Class A",
      academicYear: "2023-2024",
      birthDate: "2008-05-15",
      scores: {
        "Control 1": 19,
        "Control 2": 15,
        "Syntas 1": 16,
        "TP1": 14,
      },
      moyenne: 16,
    },
    {
      id: "stu2",
      name: "Bob Smith",
      school: "Prestige International Academy", 
      className: "Class A",
      academicYear: "2023-2024",
      birthDate: "2007-11-22",
      scores: {
        "Control 1": 14,
        "Control 2": 10,
        "Syntas 1": 12,
      },
      moyenne: 12,
    },
  ],
  "class-b": [
    {
      id: "stu3",
      name: "Charlie Brown",
      school: "Prestige International Academy",
      className: "Class B", 
      academicYear: "2023-2024",
      birthDate: "2008-03-08",
      scores: {
        "Control 1": 17,
        "Control 2": 18,
        "TP1": 15,
      },
      moyenne: 16.7,
    },
    {
      id: "stu4",
      name: "Diana Prince",
      school: "Prestige International Academy",
      className: "Class B",
      academicYear: "2023-2024",
      birthDate: "2007-09-30",
      scores: {
        "Control 1": 20,
        "Control 2": 19,
        "Syntas 1": 18,
        "TP2": 17,
      },
      moyenne: 18.5,
    },
  ]
}

// Define exam types
const examTypes = [
  "Control 1",
  "Control 2",
  "Syntas 1",
  "Syntas 2",
  "TP1",
  "TP2",
  "Oral 1",
  "Oral 2",
]

export const Route = createFileRoute("/admin/exam")({
  component: AdminExamPage,
})

function AdminExamPage() {
  const [selectedClass, setSelectedClass] = useState<string | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
  const [isFormulaDialogOpen, setIsFormulaDialogOpen] = useState(false)
  const [formula, setFormula] = useState("(Control 1*0.3 + Control 2*0.3 + Syntas 1*0.2 + TP1*0.2)")
  const [coefficients, setCoefficients] = useState<Record<string, number>>({
    "Control 1": 0.3,
    "Control 2": 0.3,
    "Syntas 1": 0.2,
    "TP1": 0.2,
  })
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Fetch classes
  const { data: classes = [] } = useQuery<Class[]>({
    queryKey: ["adminClasses"],
    queryFn: async () => {
      // In a real app, this would be an API call
      return new Promise<Class[]>((resolve) => {
        setTimeout(() => resolve(mockClasses), 300)
      })
    },
  })

  // Fetch subjects when a class is selected
  const { data: subjects = [] } = useQuery<Subject[]>({
    queryKey: ["adminSubjects", selectedClass],
    queryFn: async () => {
      // In a real app, this would filter subjects by class
      return new Promise<Subject[]>((resolve) => {
        setTimeout(() => resolve(mockSubjects), 300)
      })
    },
    enabled: !!selectedClass,
  })

  // Fetch students when both class and subject are selected
  const { data: students = [] } = useQuery<Student[]>({
    queryKey: ["adminClassStudents", selectedClass, selectedSubject],
    queryFn: async () => {
      // In a real app, this would filter students by class and subject
      if (!selectedClass) return []
      return new Promise<Student[]>((resolve) => {
        setTimeout(() => resolve(initialMockStudents[selectedClass] || []), 300)
      })
    },
    enabled: !!selectedClass && !!selectedSubject,
  })

  // Mutation to update a student's score
  const updateScoreMutation = useMutation({
    mutationFn: async ({
      studentId,
      examType,
      value,
    }: {
      studentId: string
      examType: string
      value: string
    }) => {
      // In a real app, this would be an API call
      console.log(`Updating score for student ${studentId}, exam ${examType} to ${value}`)
      return new Promise<void>((resolve) => setTimeout(resolve, 300))
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["adminClassStudents"] })
      toast({
        title: "Score Updated",
        description: "The student's score has been updated successfully.",
      })
    },
  })

  // Mutation to update a student's moyenne
  const updateMoyenneMutation = useMutation({
    mutationFn: async ({
      studentId,
      value,
    }: {
      studentId: string
      value: string
    }) => {
      // In a real app, this would be an API call
      console.log(`Updating moyenne for student ${studentId} to ${value}`)
      return new Promise<void>((resolve) => setTimeout(resolve, 300))
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["adminClassStudents"] })
      toast({
        title: "Average Updated",
        description: "The student's average has been updated successfully.",
      })
    },
  })

  // Function to calculate all averages using the formula
  const calculateAllAverages = async () => {
    if (!students.length) return

    // In a real app, this might be a single API call
    // Here we'll simulate calculating averages for each student using the formula
    const updatedStudents = students.map((student: Student) => {
      try {
        // Replace exam type names in formula with actual scores
        let calculationFormula = formula;
        
        // Replace each exam type in the formula with its score value
        Object.keys(student.scores).forEach(examType => {
          const score = student.scores[examType];
          // Only replace if score is a number
          if (typeof score === 'number') {
            // Create a regex that matches the exam type as a whole word
            const regex = new RegExp(`\\b${examType}\\b`, 'g');
            calculationFormula = calculationFormula.replace(regex, score.toString());
          }
        });
        
        // Replace any remaining exam types with 0 (for missing scores)
        examTypes.forEach(examType => {
          const regex = new RegExp(`\\b${examType}\\b`, 'g');
          calculationFormula = calculationFormula.replace(regex, '0');
        });
        
        // Evaluate the formula
        // eslint-disable-next-line no-eval
        const calculatedAverage = eval(calculationFormula);
        
        // Round to 1 decimal place
        const roundedAverage = Math.round(calculatedAverage * 10) / 10;
        
        return {
          ...student,
          moyenne: roundedAverage
        };
      } catch (error) {
        console.error("Error calculating average:", error);
        // If calculation fails, use a simple average as fallback
        const numericScores = Object.values(student.scores)
          .filter(score => typeof score === 'number')
          .map(score => Number(score));
        
        const average = numericScores.length 
          ? numericScores.reduce((sum, score) => sum + score, 0) / numericScores.length
          : 0;
        
        return {
          ...student,
          moyenne: Math.round(average * 10) / 10
        };
      }
    });

    // Update the cache directly for immediate UI update
    queryClient.setQueryData(
      ["adminClassStudents", selectedClass, selectedSubject],
      updatedStudents
    )

    // In a real app, you would also send this to the server
    console.log("Calculated averages:", updatedStudents.map((s: Student) => ({ id: s.id, moyenne: s.moyenne })))
    
    toast({
      title: "Averages Calculated",
      description: "All student averages have been calculated successfully using the formula.",
    })
  }

  const handleScoreChange = (studentId: string, examType: string, value: string) => {
    updateScoreMutation.mutate({ studentId, examType, value })
  }

  const handleMoyenneChange = (studentId: string, value: string) => {
    updateMoyenneMutation.mutate({ studentId, value })
  }

  const handleSaveFormula = (newFormula: string, newCoefficients: Record<string, number>) => {
    setFormula(newFormula)
    setCoefficients(newCoefficients)
    
    toast({
      title: "Formula Updated",
      description: "The average calculation formula has been updated.",
    })
  }

  // Add this function to handle report generation
  const handleGenerateReports = () => {
    if (!selectedClass || !selectedSubject) return;
    
    // Get all subjects for the selected class
    const classSubjects = subjects.map(subject => ({
      id: subject.id,
      name: subject.name
    }));

    // Generate only combined reports for each student
    students.forEach((student, index) => {
      setTimeout(() => {
        generateCombinedReport(student, classSubjects);
      }, index * 1000); // 1 second delay between each combined report
    });

    toast({
      title: "Reports Generated",
      description: "Student reports are being generated. Please wait for the downloads to complete.",
    });
  };

  return (
    <div className="flex h-screen">
      {/* Left Sidebar - Classes */}
      <div className="w-64 border-r bg-muted/30 h-full overflow-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Classes
          </h2>
          <ScrollArea className="h-[calc(100vh-100px)]">
            <div className="space-y-2 pr-4">
              {classes.map((cls: Class) => (
                <div
                  key={cls.id}
                  className={cn(
                    "p-3 rounded-md cursor-pointer transition-colors",
                    selectedClass === cls.id
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  )}
                  onClick={() => setSelectedClass(cls.id)}
                >
                  <div className="font-medium">{cls.name}</div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Middle Section - Subjects */}
      <div className="w-64 border-r h-full overflow-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Subjects
          </h2>
          {selectedClass ? (
            <ScrollArea className="h-[calc(100vh-100px)]">
              <div className="space-y-2 pr-4">
                {subjects.map((subject: Subject) => (
                  <div
                    key={subject.id}
                    className={cn(
                      "p-3 rounded-md cursor-pointer transition-colors",
                      selectedSubject === subject.id
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                    onClick={() => setSelectedSubject(subject.id)}
                  >
                    <div className="font-medium">{subject.name}</div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-muted-foreground text-sm p-3">
              Please select a class first
            </div>
          )}
        </div>
      </div>

      {/* Main Content - Student Scores */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Exam Management</h1>
          </div>

          {selectedClass && selectedSubject && students.length > 0 ? (
            <Card className="h-[calc(100vh-150px)]">
              <CardHeader className="bg-muted/50">
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Student Scores and Averages
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6 h-[calc(100%-80px)] overflow-auto">
                <Tabs defaultValue="scores" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="scores">Scores</TabsTrigger>
                    <TabsTrigger value="info">Information</TabsTrigger>
                  </TabsList>
                  <TabsContent value="scores" className="h-full">
                    <AdminScoreTable
                      students={students}
                      examTypes={examTypes}
                      onScoreChange={handleScoreChange}
                      onMoyenneChange={handleMoyenneChange}
                      onCalculateAll={calculateAllAverages}
                      onOpenFormulaDialog={() => setIsFormulaDialogOpen(true)}
                      onGenerateReports={handleGenerateReports}
                      subjectName={subjects.find(s => s.id === selectedSubject)?.name || ''}
                    />
                  </TabsContent>
                  <TabsContent value="info">
                    <div className="space-y-4">
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Current Formula</h3>
                        <code className="bg-background p-2 rounded block">{formula}</code>
                      </div>
                      <div className="p-4 bg-muted rounded-lg">
                        <h3 className="font-medium mb-2">Instructions</h3>
                        <ul className="list-disc list-inside space-y-1">
                          <li>Double-click on a cell to edit a student's score</li>
                          <li>Enter averages manually or use the "Calculate All" button</li>
                          <li>Click "Edit Formula" to customize the average calculation</li>
                        </ul>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-150px)] text-center">
              <BookOpen className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium">No Data Selected</h3>
              <p className="text-muted-foreground mt-2 max-w-md">
                Please select a class and subject from the sidebar to view and manage student scores.
              </p>
            </div>
          )}
        </div>
      </div>

      <FormulaDialog
        open={isFormulaDialogOpen}
        onOpenChange={setIsFormulaDialogOpen}
        examTypes={examTypes}
        formula={formula}
        coefficients={coefficients}
        onSave={handleSaveFormula}
      />
    </div>
  )
}
