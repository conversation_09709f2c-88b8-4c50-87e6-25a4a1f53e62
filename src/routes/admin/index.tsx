import { createFileRoute } from '@tanstack/react-router'
import { guardAdminOnly } from '@/lib/auth/routeGuards'
import {
  LayoutDashboard,
  Users,
  BookOpen,
  GraduationCap,
  UserCheck,
  Calculator,
  Settings,
  CreditCard,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Import your components
import AdminService from '@/components/adminPage/AdminService'
import ClassManagement from '@/components/adminPage/etablissment/ClassManagment'
import UserManagement from '@/components/adminPage/UserManagement'
import TeacherManagement from '@/components/adminPage/teacher/TeacherManagement'
import StudentManagement from '@/components/adminPage/student/StudentManagement'
import ListEliminate from '@/components/adminPage/student/ListEliminate'
import CalculateStudentScore from '@/components/adminPage/student/CalculateStudentScore'
// import { ScheduleView } from '@/components/shared'

export const Route = createFileRoute('/admin/')({
  component: AdminLayout,
  beforeLoad: guardAdminOnly,
})

const navigation = [
  { name: 'Admin Service', icon: LayoutDashboard, component: AdminService },
  // { name: 'Schedule View', icon: Calendar, component: ScheduleView },
  { name: 'Class Management', icon: BookOpen, component: ClassManagement },
  { name: 'Teacher Management', icon: Users, component: TeacherManagement },
  {
    name: 'Student Management',
    icon: GraduationCap,
    component: StudentManagement,
  },
  { name: 'List Eliminate Student', icon: UserCheck, component: ListEliminate },
  {
    name: 'Calculate Student Score',
    icon: Calculator,
    component: CalculateStudentScore,
  },
  {
    name: 'Card Studio',
    icon: CreditCard,
    component: () =>
      (window.location.href = '/admin/establishment/card-studio'),
  },
  { name: 'User Management', icon: Settings, component: UserManagement },
]

function AdminLayout() {
  const currentPage = 'Admin Service'

  const renderContent = () => {
    const route = navigation.find((item) => item.name === currentPage)
    const Component = route?.component || AdminService
    return <Component />
  }

  return (
    <div className="flex min-h-screen w-[80%] m-auto ">
      {/* Main Content */}
      <main className="p-4 w-full sm:w-full md:w-10/12 lg:w-10/12 xl:w-10/12">
        <Card>
          <CardHeader>
            <CardTitle>{currentPage}</CardTitle>
          </CardHeader>
          <CardContent>{renderContent()}</CardContent>
        </Card>
      </main>
    </div>
  )
}
