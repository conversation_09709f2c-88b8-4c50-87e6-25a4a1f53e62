import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import {
  Home,
  ArrowLeftCircle,
  Calendar,
  Users,
  BarChart,
  // FileText,
  PlusCircle,
} from 'lucide-react'
import { createFileRoute } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import {
  AttendanceDashboard,
  StatusOverview,
  TeacherAbsence,
  Reports,
  SessionGenerator,
  SessionManagement
} from '@/components/adminPage/attendance'

export const Route = createFileRoute('/admin/attendance')({
  component: AttendanceRoute
})

function AttendanceRoute() {
  const [activeComponent, setActiveComponent] = useState('Dashboard')
  const navigate = useNavigate()
  const search = Route.useSearch()

  // Set active component based on search parameter
  useEffect(() => {
    const searchParams = search as { component?: string }
    if (searchParams.component && typeof searchParams.component === 'string') {
      setActiveComponent(searchParams.component)
    }
  }, [search])

  // Map of components to render based on active component
  const componentsMap = {
    Dashboard: <AttendanceDashboard />,
    SessionManagement: <SessionManagement />,
    StatusOverview: <StatusOverview dateRange={{ from: new Date(), to: new Date() }} />,
    Reports: <Reports />,
    TeacherAbsence: <TeacherAbsence />,
    SessionGenerator: <SessionGenerator />,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/admin' })
  }

  // Layout that works with the navbar
  return (
    <div className="flex h-[calc(100vh-64px)]">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        goBack={goBack}
        activeComponent={activeComponent}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Smart Attendance System</h1>
          <p className="text-muted-foreground">
            Manage attendance, sessions, and generate reports
          </p>
        </div>
        {componentsMap[activeComponent as keyof typeof componentsMap]}
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  goBack,
  activeComponent,
}: {
  setActiveComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
}) {
  // Navigation items
  const navItems = [
    { label: 'Dashboard', component: 'Dashboard', icon: Home, description: 'Overview and metrics' },
    {
      label: 'Session Management',
      component: 'SessionManagement',
      icon: Calendar,
      description: 'View and manage sessions'
    },
    {
      label: 'Generate Sessions',
      component: 'SessionGenerator',
      icon: PlusCircle,
      description: 'Create sessions from timetable'
    },
    {
      label: 'Status Overview',
      component: 'StatusOverview',
      icon: BarChart,
      description: 'Attendance statistics'
    },
    {
      label: 'Teacher Absence',
      component: 'TeacherAbsence',
      icon: Users,
      description: 'Record teacher absences'
    },
    // {
    //   label: 'Reports',
    //   component: 'Reports',
    //   icon: FileText,
    //   description: 'Generate attendance reports'
    // },
  ]

  return (
    <div
      className={cn(
        'w-64 bg-gray-100 border-r border-gray-200 flex flex-col overflow-y-auto'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm">Back</span>
        </button>

        <div className="py-2">
          <h2 className="px-2 mb-2 text-lg font-semibold tracking-tight">
            Smart Attendance
          </h2>
        </div>

        {/* Navigation items */}
        <div className="sidebar">
          {navItems.map(({ label, component, icon: Icon, description }) => (
            <div key={component} className="mb-1">
              <button
                onClick={() => {
                  setActiveComponent(component)
                }}
                className={cn(
                  'flex flex-col items-start w-full p-2 rounded-lg text-left',
                  activeComponent === component
                    ? 'bg-primary text-white' // Primary color when active
                    : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                )}
              >
                <div className="flex items-center gap-2">
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{label}</span>
                </div>
                <span className={cn(
                  "text-xs mt-1 ml-7",
                  activeComponent === component
                    ? 'text-white/70'
                    : 'text-gray-500'
                )}>
                  {description}
                </span>
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}