import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import {
  Home,
  Users,
  Building,
  ArrowLeftCircle,
  UserCheck,
  UserPlus,
  User,
  BookOpen,
  GraduationCap,
  School,
  Layers,
} from 'lucide-react'
import { createFileRoute, Outlet, useSearch } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import EliminationOverview from '@/components/adminPage/student/EliminationOverView'
import StudentManagement from '@/components/adminPage/student/StudentManagement'
import TeacherManagment from '@/components/adminPage/teacher/TeacherManagement'
import ClassManagement from '@/components/adminPage/etablissment/ClassManagment'
import SubjectManagement from '@/components/adminPage/etablissment/Subject'

import ParentManagment from '@/components/adminPage/parent/ParentManagment'
import ListEliminate from '@/components/adminPage/student/ListEliminate'
import CalculateStudentScore from '@/components/adminPage/student/CalculateStudentScore'
import { ClassroomManagement } from '@/components/adminPage/classroom/ClassroomManagement'
import { GradeManagement } from '@/components/adminPage/etablissment/GradeManagement'

// Define the navigation items
const navItems = [
  { label: 'Overview', component: 'Overview', icon: Home },
  {
    label: 'Users',
    component: 'Users',
    icon: Users,
    nested: [
      {
        label: 'Parent Management',
        component: 'ParentManagement',
        icon: User,
      },
      {
        label: 'Teacher Management',
        component: 'TeacherManagement',
        icon: UserCheck,
      },
      {
        label: 'Student Management',
        component: 'StudentManagement',
        icon: UserPlus,
      },
      // {
      //   label: 'List Eliminate',
      //   component: 'ListEliminate',
      //   icon: Delete,
      // },
      // {
      //   label: 'Claculate Score',
      //   component: 'CalculateStudentScore',
      //   icon: Calculator,
      // },
    ],
  },
  {
    label: 'Establishment',
    component: 'Establishment',
    icon: Building,
    nested: [
      {
        label: 'Class Management',
        component: 'ClassManagement',
        icon: GraduationCap,
      },
      {
        label: 'Grade Management',
        component: 'GradeManagement',
        icon: School,
      },
      {
        label: 'Classroom Management',
        component: 'ClassroomManagement',
        icon: Layers,
      },
      {
        label: 'Subject Management',
        component: 'SubjectManagement',
        icon: BookOpen,
      },
    ],
  },
]

// Define the route for the current view
export const Route = createFileRoute('/admin/database')({
  component: RouteComponent,
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const navigate = useNavigate()
  const search = useSearch({ from: '/admin/database' }) as { section?: string }

  // Check if there's a section in the URL and update the active components accordingly
  useEffect(() => {
    console.log('URL search params changed:', search)
    if (search.section) {
      // Find which parent component contains this section
      const parentComponent = navItems.find((item) =>
        item.nested?.some(
          (nestedItem) => nestedItem.component === search.section
        )
      )

      if (parentComponent) {
        setActiveComponent(parentComponent.component)
        setActiveSubComponent(search.section)
      } else {
        // If it's a top-level component
        setActiveComponent(search.section)
        setActiveSubComponent('')
      }
    }
  }, [search.section])

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Overview: <EliminationOverview />,
    Users: <EliminationOverview />,
    TeacherManagement: <TeacherManagment />,
    StudentManagement: <StudentManagement />,
    ParentManagement: <ParentManagment />,
    Establishment: <ClassManagement />, // Default component for Establishment menu
    ClassManagement: <ClassManagement />,
    ClassroomManagement: <ClassroomManagement />,
    SubjectManagement: <SubjectManagement />,
    GradeManagement: <GradeManagement />,
    Elimnate: <EliminationOverview />,
    ListEliminate: <ListEliminate />,
    CalculateStudentScore: <CalculateStudentScore />,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/admin' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
        navigate={navigate}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">
        {componentsMap[activeSubComponent || activeComponent]}
        <Outlet />
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  activeSubComponent,
  navigate,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
  navigate: any // Using any for simplicity
}) {
  // Using the navItems defined at the top of the file

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left font-medium"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span>Back</span>
        </button>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon, nested }) => (
          <div key={component}>
            <button
              onClick={() => {
                // Always reset the activeSubComponent when clicking a main navigation item
                console.log('Clicked on main navigation item:', component)
                setActiveComponent(component)
                setActiveSubComponent('')

                // Update the URL to reflect the change
                // Use the navigate function from the parent component
                navigate({
                  search: (prev: any) => ({
                    ...prev,
                    section: component,
                  }),
                })
              }}
              className={cn(
                'flex items-center gap-2 p-2 rounded-lg w-full text-left font-medium',
                activeComponent === component
                  ? 'bg-primary text-white' // Primary color when active
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </button>

            {/* Render nested items if the parent component is selected */}
            {nested && activeComponent === component && (
              <div className="ml-6 mt-2 space-y-2">
                {nested.map(({ label, component, icon: Icon }) => (
                  <button
                    key={component}
                    onClick={() => {
                      setActiveSubComponent(component)
                      setActiveComponent(activeComponent) // Ensure parent component remains active

                      // Update the URL to reflect the change
                      navigate({
                        search: (prev: any) => ({
                          ...prev,
                          section: component,
                        }),
                      })
                    }}
                    className={cn(
                      'flex items-center gap-2 p-2 rounded-lg w-full text-left text-sm',
                      activeSubComponent === component
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
