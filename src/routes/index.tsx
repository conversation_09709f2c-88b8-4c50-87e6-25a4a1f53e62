import { createFileRoute } from '@tanstack/react-router'
import Hero from '@/components/homePage/Hero'
import Features from '@/components/homePage/Features'
import EffortlessManaging from '@/components/homePage/EffortlessManaging'
import OurPrograms from '@/components/homePage/OurPrograms'
import Testimonials from '@/components/homePage/Testimonials'
import FeaturesExample from '@/components/homePage/FeaturesExample'
import { useEffect } from 'react'
import UseCaseCards from '@/components/homePage/UseCaseCards'

// Commented out unused imports
// import CountBar from '@/components/homePage/CountBar'
// import About from '@/components/About'
// import LogoSlider from '@/components/homePage/LogoSlider'

// Enable this for debugging only
const DEBUG = false

export const Route = createFileRoute('/')({
  component: HomeRoute,
  beforeLoad: async () => {
    return {}
  },
})

function HomeRoute() {
  // Add debugging to help troubleshoot
  useEffect(() => {
    if (!DEBUG) return // Skip debug logging if not needed

    const hostname = window.location.hostname
    const isSubdomain =
      hostname.includes('.localhost') || hostname.includes('.jeridschool.tech')
    console.log('HomeRoute rendered with hostname:', hostname)
    console.log('Is subdomain (client-side check):', isSubdomain)

    if (isSubdomain) {
      const schoolName = hostname.split('.')[0]
      console.log('School name from subdomain (client-side):', schoolName)
    }
  }, [])

  return (
    <div className="min-h-screen">
      <Hero />
      <Testimonials />

      {/* <CountBar /> */}
      <Features />
      <FeaturesExample />
      <UseCaseCards/>
      <EffortlessManaging />
      <OurPrograms />


    </div>
  )
}
