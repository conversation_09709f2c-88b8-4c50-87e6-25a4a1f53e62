import { useState } from 'react'
import { cn } from '@/lib/utils'
import { ClipboardList, Ban, ArrowLeftCircle } from 'lucide-react'
import { createFileRoute, Outlet } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import ViewAbsent from '@/components/studentPage/ViewAbsent'
import StudentAttendanceView from '@/components/studentPage/StudentAttendanceView'
import { LucideIcon } from 'lucide-react'

// Define types for navigation items
interface NavSubItem {
  label: string
  component: string
  icon: LucideIcon
}

interface NavItem {
  label: string
  component: string
  icon: LucideIcon
  nested?: NavSubItem[]
}

// Define the route for the current view
export const Route = createFileRoute('/student/attendance/')({
  component: RouteComponent,
})

export default function RouteComponent() {
  const [activeComponent, setActiveComponent] = useState('Overview')
  const [activeSubComponent, setActiveSubComponent] = useState('')
  const navigate = useNavigate()

  // Component mapping
  const componentsMap: { [key: string]: JSX.Element } = {
    Attandence: <StudentAttendanceView />,
    ViewAbsent: <ViewAbsent />,
  }

  // Function to go back to the previous route
  function goBack() {
    navigate({ to: '/student' })
  }

  return (
    <div className="flex">
      {/* Sidebar */}
      <Sidebar
        setActiveComponent={setActiveComponent}
        setActiveSubComponent={setActiveSubComponent}
        goBack={goBack}
        activeComponent={activeComponent}
        activeSubComponent={activeSubComponent}
      />

      {/* Main View - Dynamically rendered components */}
      <div className="flex-1 p-6">
        {componentsMap[activeSubComponent || activeComponent]}
        <Outlet />
      </div>
    </div>
  )
}

// Sidebar Component
function Sidebar({
  setActiveComponent,
  setActiveSubComponent,
  goBack,
  activeComponent,
  activeSubComponent,
}: {
  setActiveComponent: (component: string) => void
  setActiveSubComponent: (component: string) => void
  goBack: () => void
  activeComponent: string
  activeSubComponent: string
}) {
  const navItems: NavItem[] = [
    { label: 'Attandence', component: 'Attandence', icon: ClipboardList },
    {
      label: 'View Absent',
      component: 'ViewAbsent',
      icon: Ban,
    },
  ]

  return (
    <div
      className={cn(
        'h-screen w-64 bg-gray-100 border-r border-gray-200 flex flex-col'
      )}
    >
      <div className="p-4 space-y-4">
        {/* Back Button with improved design */}
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 w-full text-left font-medium"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span>Back</span>
        </button>

        {/* Navigation items */}
        {navItems.map(({ label, component, icon: Icon, nested }) => (
          <div key={component}>
            <button
              onClick={() => {
                if (nested) {
                  // If there are nested items, show them when clicked
                  setActiveComponent(component)
                  setActiveSubComponent('')
                } else {
                  // If no nested items, navigate directly
                  setActiveComponent(component)
                }
              }}
              className={cn(
                'flex items-center gap-2 p-2 rounded-lg w-full text-left font-medium',
                activeComponent === component
                  ? 'bg-primary text-white' // Primary color when active
                  : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span>{label}</span>
            </button>

            {/* Render nested items if the parent component is selected */}
            {nested && activeComponent === component && (
              <div className="ml-6 mt-2 space-y-2">
                {nested.map(({ label, component, icon: Icon }) => (
                  <button
                    key={component}
                    onClick={() => {
                      setActiveSubComponent(component)
                      setActiveComponent(activeComponent) // Ensure parent component remains active
                    }}
                    className={cn(
                      'flex items-center gap-2 p-2 rounded-lg w-full text-left text-sm',
                      activeSubComponent === component
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
