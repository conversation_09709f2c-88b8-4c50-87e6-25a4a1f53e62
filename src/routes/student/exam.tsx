import { createFileRoute } from "@tanstack/react-router"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ScoreStudent } from "@/components/adminPage/score/ScoreStudent"
import { MoyeneStudent } from "@/components/adminPage/score/MoyeneStudent"
import { BookOpen, GraduationCap, BarChart, Calendar } from "lucide-react"
import { useState } from "react"

// Define types for student data
interface SubjectScore {
  id: string
  name: string
  scores: Record<string, number | string | null>
  average: number
}

interface SemesterAverage {
  semester: string
  average: number
}

interface StudentData {
  id: string
  name: string
  class: string
  gradeLevel: string
  subjects: SubjectScore[]
  semesters: SemesterAverage[]
  overallAverage: number
}

// Mock data for student scores
const mockStudentData: StudentData = {
  id: "stu1",
  name: "<PERSON>",
  class: "Class A",
  gradeLevel: "10th Grade",
  subjects: [
    {
      id: "math",
      name: "Math",
      scores: {
        "Control 1": 19,
        "Control 2": 15,
        "Syntas 1": 16,
        "Syntas 2": "bull",
        "TP1": 14,
        "TP2": null,
        "Oral 1": null,
        "Oral 2": null,
      },
      average: 16,
    },
    {
      id: "physics",
      name: "Physics",
      scores: {
        "Control 1": 17,
        "Control 2": 16,
        "Syntas 1": 14,
        "Syntas 2": 15,
        "TP1": 18,
        "TP2": 17,
        "Oral 1": null,
        "Oral 2": null,
      },
      average: 16.2,
    },
    {
      id: "chemistry",
      name: "Chemistry",
      scores: {
        "Control 1": 14,
        "Control 2": 13,
        "Syntas 1": 15,
        "Syntas 2": null,
        "TP1": 16,
        "TP2": null,
        "Oral 1": 14,
        "Oral 2": null,
      },
      average: 14.4,
    },
    {
      id: "biology",
      name: "Biology",
      scores: {
        "Control 1": 18,
        "Control 2": 17,
        "Syntas 1": 16,
        "Syntas 2": null,
        "TP1": 19,
        "TP2": null,
        "Oral 1": 15,
        "Oral 2": null,
      },
      average: 17,
    },
    {
      id: "english",
      name: "English",
      scores: {
        "Control 1": 15,
        "Control 2": 16,
        "Syntas 1": 14,
        "Syntas 2": null,
        "TP1": null,
        "TP2": null,
        "Oral 1": 17,
        "Oral 2": 16,
      },
      average: 15.6,
    },
  ],
  semesters: [
    { semester: "Semester 1", average: 15.8 },
    { semester: "Semester 2", average: 16.2 },
  ],
  overallAverage: 16.0,
}

// Define exam types
const examTypes = [
  "Control 1",
  "Control 2",
  "Syntas 1",
  "Syntas 2",
  "TP1",
  "TP2",
  "Oral 1",
  "Oral 2",
]

export const Route = createFileRoute("/student/exam")({
  component: StudentExamPage,
})

function StudentExamPage() {
  const [activeTab, setActiveTab] = useState("scores")

  // Fetch student data
  const { data: student, isLoading } = useQuery<StudentData>({
    queryKey: ["studentScores"],
    queryFn: async () => {
      // In a real app, this would be an API call
      return new Promise<StudentData>((resolve) => {
        setTimeout(() => resolve(mockStudentData), 500)
      })
    },
  })

  // Format subject data for ScoreStudent component
  const subjectAverages = student?.subjects.map(subject => ({
    subject: subject.name,
    average: subject.average
  })) || []

  // Format semester data for MoyeneStudent component
  const semesterAverages = student?.semesters || []

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center">
        <div className="animate-pulse text-muted-foreground">Loading your scores...</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">My Academic Performance</h1>
          <p className="text-muted-foreground mt-1">
            View your scores and academic progress
          </p>
        </div>
        <div className="flex items-center gap-2 bg-muted p-2 rounded-lg">
          <GraduationCap className="h-5 w-5 text-primary" />
          <div>
            <div className="text-sm font-medium">{student?.class}</div>
            <div className="text-xs text-muted-foreground">{student?.gradeLevel}</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Overall Average</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student?.overallAverage.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Current Semester</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student?.semesters[1].average.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student?.subjects.length}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full md:w-auto grid-cols-3">
          <TabsTrigger value="scores" className="flex items-center gap-1">
            <BookOpen className="h-4 w-4" />
            <span className="hidden md:inline">Subject</span> Scores
          </TabsTrigger>
          <TabsTrigger value="averages" className="flex items-center gap-1">
            <BarChart className="h-4 w-4" />
            <span className="hidden md:inline">Subject</span> Averages
          </TabsTrigger>
          <TabsTrigger value="semesters" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span className="hidden md:inline">Semester</span> Progress
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="scores" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Subject Scores
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead className="font-semibold">Subject</TableHead>
                        {examTypes.map((type) => (
                          <TableHead key={type} className="font-semibold">{type}</TableHead>
                        ))}
                        <TableHead className="font-semibold">Average</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {student?.subjects.map((subject) => (
                        <TableRow key={subject.id} className="hover:bg-muted/30">
                          <TableCell className="font-medium">{subject.name}</TableCell>
                          {examTypes.map((type) => {
                            const score = subject.scores[type]
                            return (
                              <TableCell key={type}>
                                {score === null || score === undefined ? (
                                  <span className="text-muted-foreground">-</span>
                                ) : (
                                  <Badge 
                                    variant={getScoreVariant(score)} 
                                    className="font-normal"
                                  >
                                    {score}
                                  </Badge>
                                )}
                              </TableCell>
                            )
                          })}
                          <TableCell className="font-medium">
                            <Badge variant="outline" className="bg-primary/10">
                              {subject.average.toFixed(1)}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="averages">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ScoreStudent subjects={subjectAverages} studentId={student?.id || ""} />
            <Card>
              <CardHeader>
                <CardTitle>Performance Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Strongest Subjects</h3>
                  <div className="space-y-2">
                    {student?.subjects
                      .sort((a, b) => b.average - a.average)
                      .slice(0, 2)
                      .map(subject => (
                        <div key={subject.id} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                          <span>{subject.name}</span>
                          <Badge variant="outline" className="bg-primary/10">
                            {subject.average.toFixed(1)}
                          </Badge>
                        </div>
                      ))
                    }
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium">Areas for Improvement</h3>
                  <div className="space-y-2">
                    {student?.subjects
                      .sort((a, b) => a.average - b.average)
                      .slice(0, 2)
                      .map(subject => (
                        <div key={subject.id} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                          <span>{subject.name}</span>
                          <Badge variant="outline" className="bg-primary/10">
                            {subject.average.toFixed(1)}
                          </Badge>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="semesters">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <MoyeneStudent semesters={semesterAverages} studentId={student?.id || ""} />
            <Card>
              <CardHeader>
                <CardTitle>Semester Comparison</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="font-medium mb-2">Progress Analysis</h3>
                  {student && student.semesters.length >= 2 ? (
                    <>
                      <p className="mb-2">
                        Your average has {getProgressText(student.semesters[0].average, student.semesters[1].average)} 
                        from {student.semesters[0].average.toFixed(1)} to {student.semesters[1].average.toFixed(1)}.
                      </p>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Change:</span>
                        <Badge variant={getProgressVariant(student.semesters[0].average, student.semesters[1].average)}>
                          {getProgressDifference(student.semesters[0].average, student.semesters[1].average)}
                        </Badge>
                      </div>
                    </>
                  ) : (
                    <p>Not enough semester data to compare progress.</p>
                  )}
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="font-medium mb-2">Academic Standing</h3>
                  <p className="mb-2">
                    Based on your current average of {student?.overallAverage.toFixed(1)}, 
                    your academic standing is:
                  </p>
                  <Badge variant={getAcademicStandingVariant(student?.overallAverage || 0)}>
                    {getAcademicStanding(student?.overallAverage || 0)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper functions for UI display
function getScoreVariant(score: number | string): "default" | "outline" | "secondary" | "destructive" {
  if (score === "bull") return "destructive"
  if (typeof score === "number") {
    if (score >= 16) return "default"
    if (score >= 10) return "secondary"
    return "destructive"
  }
  return "outline"
}

function getProgressText(prev: number, current: number): string {
  const diff = current - prev
  if (diff > 0) return "improved"
  if (diff < 0) return "decreased"
  return "remained the same"
}

function getProgressDifference(prev: number, current: number): string {
  const diff = current - prev
  if (diff > 0) return `+${diff.toFixed(1)}`
  return diff.toFixed(1)
}

function getProgressVariant(prev: number, current: number): "default" | "destructive" | "outline" {
  const diff = current - prev
  if (diff > 0) return "default"
  if (diff < 0) return "destructive"
  return "outline"
}

function getAcademicStanding(average: number): string {
  if (average >= 16) return "Excellent"
  if (average >= 14) return "Very Good"
  if (average >= 12) return "Good"
  if (average >= 10) return "Satisfactory"
  return "Needs Improvement"
}

function getAcademicStandingVariant(average: number): "default" | "secondary" | "destructive" | "outline" {
  if (average >= 16) return "default"
  if (average >= 12) return "secondary"
  if (average >= 10) return "outline"
  return "destructive"
}
