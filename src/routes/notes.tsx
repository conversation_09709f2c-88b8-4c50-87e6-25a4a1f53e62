import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import NotesCollectionList from '@/components/notes/NotesCollectionList'
import NotesCollection from '@/components/notes/NotesCollection'
import { guardNotes } from '../lib/auth/routeGuards';

export const Route = createFileRoute('/notes')({
  beforeLoad: guardNotes,
  component: NotesComponent,
})

function NotesComponent() {
  const [collectionId, setCollectionId] = useState<string | null>(null)

  // Check if there's a collection ID in the URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const id = urlParams.get('collectionId')
    if (id) {
      setCollectionId(id)
    }
  }, [])

  // Function to view a collection
  const handleViewCollection = (id: string) => {
    setCollectionId(id)
    // Update URL without navigation
    const url = new URL(window.location.href)
    url.searchParams.set('collectionId', id)
    window.history.pushState({}, '', url)
  }

  // Function to go back to collections list
  const handleBackToCollections = () => {
    setCollectionId(null)
    // Remove collection ID from URL
    const url = new URL(window.location.href)
    url.searchParams.delete('collectionId')
    window.history.pushState({}, '', url)
  }

  return (
    <div className="container mx-auto py-6">
      {collectionId ? (
        <NotesCollection
          collectionId={collectionId}
          onBack={handleBackToCollections}
        />
      ) : (
        <NotesCollectionList onViewCollection={handleViewCollection} />
      )}
    </div>
  )
}
