import { Feature } from '@/types/features'
import { createFileRoute } from '@tanstack/react-router'
import {
  Calendar,
  BookOpen,
  Users,
  UserCheck,
  Layout,
  Video,
  FileText,
  Bell,
  Check,
  X,
} from 'lucide-react'
import { useState } from 'react'

const features: Feature[] = [
  {
    icon: Calendar,
    title: 'Automated Timetable Scheduling',
    price: 29.99,
    hint: 'Effortless schedule management for all',
    description:
      'Automatically generate timetables for students and teachers based on available resources. Customize and edit timetables, handle conflicts, and receive notifications for changes.',
    image: '/images/profile.png',
  },
  {
    icon: BookOpen,
    title: 'Quiz Generator',
    price: 19.99,
    hint: 'Create engaging quizzes in minutes',
    description:
      'Generate quizzes with multiple question types, set time limits, and enjoy automatic grading with manual adjustment options.',
    image: '/images/profile.png',
  },
  {
    icon: Users,
    title: 'Student Management',
    price: 24.99,
    hint: 'Comprehensive student data at your fingertips',
    description:
      'Store and manage student information, track performance over time, and access progress analytics easily.',
    image: '/images/profile.png',
  },
  {
    icon: User<PERSON>he<PERSON>,
    title: 'Teacher Management',
    price: 24.99,
    hint: 'Streamline teacher administration effortlessly',
    description:
      'Track teacher profiles, schedules, and subjects. Manage availability and facilitate communication between teachers and administration.',
    image: '/images/profile.png',
  },
  {
    icon: Layout,
    title: 'Classroom Partition',
    price: 14.99,
    hint: 'Efficient classroom resource allocation',
    description:
      "Control classroom schedules, resources, and assignments. Manage and oversee each class's lessons and activities, including room allocation.",
    image: '/images/profile.png',
  },
  {
    icon: Video,
    title: 'Zoom Integration',
    price: 9.99,
    hint: 'Seamless virtual classroom experience',
    description:
      'Integrate Zoom video calls for virtual classroom sessions. Schedule meetings directly from the platform and provide links and reminders to participants.',
    image: '/images/profile.png',
  },
  {
    icon: FileText,
    title: 'Tests and Exams with Auto-Grading',
    price: 34.99,
    hint: 'Efficient assessment and grading system',
    description:
      'Create tests and exams with various question types. Automatically grade applicable tests and provide immediate feedback to students.',
    image: '/images/profile.png',
  },
  {
    icon: Bell,
    title: 'Notifications',
    price: 9.99,
    hint: 'Stay informed with timely alerts',
    description:
      'Send automatic notifications for various events, announcements, and updates. Customize notification settings based on user roles.',
    image: '/images/profile.png',
  },
]
export const Route = createFileRoute('/offers')({
  component: RouteComponent,
})

function RouteComponent() {
  const [selectedFeatures, setSelectedFeatures] = useState<number[]>([])
  const [popupFeature, setPopupFeature] = useState<number | null>(null)

  const toggleFeature = (index: number, event: React.MouseEvent) => {
    event.stopPropagation()
    setSelectedFeatures((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    )
  }

  const openPopup = (index: number) => {
    setPopupFeature(index)
  }

  const closePopup = () => {
    setPopupFeature(null)
  }

  const totalPrice = selectedFeatures.reduce(
    (sum, index) => sum + features[index].price,
    0
  )

  const handleSubmit = () => {
    console.log(
      'Selected features:',
      selectedFeatures.map((index) => features[index].title)
    )
    console.log('Total price:', totalPrice.toFixed(2))
    // Here you would typically send this data to a server or perform further actions
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">Our Features</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => {
          const FeatureIcon = feature.icon
          return (
            <div
              key={index}
              className="bg-white rounded-lg shadow-md overflow-hidden relative cursor-pointer flex flex-col"
              onClick={() => openPopup(index)}
            >
              <div className="p-6 flex-grow flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <FeatureIcon className="w-8 h-8 text-blue-500" />
                  <div className="flex justify-center mb-4 mt-4">
                    <span className="text-lg font-semibold text-blue-600">
                      ${feature.price.toFixed(2)}/month
                    </span>
                  </div>
                  <div
                    className="w-6 h-6 rounded-full border-2 border-blue-500 flex items-center justify-center bg-white cursor-pointer"
                    onClick={(e) => toggleFeature(index, e)}
                  >
                    {selectedFeatures.includes(index) && (
                      <Check className="w-4 h-4 text-blue-500" />
                    )}
                  </div>
                </div>
                <h2 className="text-xl font-semibold mb-2">{feature.title}</h2>
                <p className="text-sm text-gray-600 mb-4">{feature.hint}</p>
                <p className="text-sm text-gray-700 mb-4 line-clamp-3 flex-grow">
                  {feature.description}
                </p>
                <img
                  src={feature.image}
                  alt={feature.title}
                  className="w-full h-48 object-cover mt-auto"
                />
              </div>
            </div>
          )
        })}
      </div>

      {/* Improved Popup Design */}
      {popupFeature !== null && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full flex">
            {/* Left Side - Image */}
            <div className="w-1/2 relative">
              <img
                src={features[popupFeature].image}
                alt={features[popupFeature].title}
                className="w-full h-full object-cover rounded-l-2xl"
              />
              <div className="absolute top-4 right-4">
                <span className="bg-white/80 rounded-full p-2 inline-block">
                  {(() => {
                    const PopupIcon = features[popupFeature].icon
                    return <PopupIcon className="w-6 h-6 text-blue-500" />
                  })()}
                </span>
              </div>
            </div>

            {/* Right Side - Content */}
            <div className="w-1/2 p-8 flex flex-col justify-between">
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold">
                    {features[popupFeature].title}
                  </h2>
                  <button
                    onClick={closePopup}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
                <p className="text-gray-700 mb-6">
                  {features[popupFeature].description}
                </p>
              </div>

              <div>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-xl font-semibold text-blue-600">
                    ${features[popupFeature].price.toFixed(2)}/month
                  </span>
                  <button
                    onClick={(e) => {
                      toggleFeature(popupFeature, e)
                      closePopup()
                    }}
                    className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    {selectedFeatures.includes(popupFeature)
                      ? 'Remove'
                      : 'Add to Cart'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 text-center">
        <p className="text-xl font-bold mb-4">
          Total Price: ${totalPrice.toFixed(2)}/month
        </p>
        <button
          onClick={handleSubmit}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Submit Selection
        </button>
      </div>
    </div>
  )
}
