import UserSay from '@/components/UserSay'
import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { services } from '@/lib/api'
import { useMutation } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { Link } from '@tanstack/react-router'

type UserRole =
  | 'Admin'
  | 'SuperAdmin'
  | 'Teacher'
  | 'Parent'
  | 'Student'
  | 'B2C_Client'
  | string

export const Route = createFileRoute('/login')({
  component: RouteComponent,
})

function RouteComponent() {
  return <Login />
}

function Login() {
  const navigate = useNavigate()
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [loginError, setLoginError] = useState<string | null>(null)

  const togglePasswordVisibility = () => {
    setIsPasswordVisible((prev) => !prev)
  }

  useEffect(() => {
    const role = localStorage.getItem('role') as UserRole
    if (role) {
      let redirectPath = '/'
      switch (role) {
        case 'Admin':
          redirectPath = '/admin'
          break
        case 'SuperAdmin':
          redirectPath = '/super_admin'
          break
        case 'Teacher':
          redirectPath = '/teacher'
          break
        case 'Parent':
          redirectPath = '/parent'
          break
        case 'Student':
          redirectPath = '/student'
          break
          case ' B2C_Client':
          redirectPath = '/b2c_student'
          break
         

        default:
          return
      }
      if (redirectPath !== '/login') {
        navigate({ to: redirectPath })
      }
    }
  }, [navigate])

  const { mutate, isPending } = useMutation({
    mutationFn: services.auth.login,
    onSuccess: (data) => {
      console.log('Login successful:', data)
      setLoginError(null)

      const parseJwt = (token: string) => {
        try {
          const base64Url = token.split('.')[1]
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
          const jsonPayload = decodeURIComponent(
            atob(base64)
              .split('')
              .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
              .join('')
          )
          return JSON.parse(jsonPayload)
        } catch (e) {
          console.error('Error parsing JWT:', e)
          return null
        }
      }

      const tokenData = parseJwt(data.access_token)
      console.log('PARSED JWT TOKEN DATA:', tokenData)

      localStorage.setItem('access_token', data.access_token)
      localStorage.setItem('id', data.user.id)
      localStorage.setItem('role', data.user.role)
      localStorage.setItem('firstname', data.user.firstname)
      localStorage.setItem('lastname', data.user.lastname)
      localStorage.setItem('email', data.user.email)

      // Store onboarding status
      if (data.user.userOnboarding !== undefined) {
        console.log(
          'Setting userOnboarding in localStorage:',
          data.user.userOnboarding
        )
        localStorage.setItem(
          'userOnboarding',
          data.user.userOnboarding.toString()
        )
      }

      const userData = data.user as any
      if (userData.fullname) localStorage.setItem('fullname', userData.fullname)
      if (userData.avatar) localStorage.setItem('avatar', userData.avatar)

      if (data.user.role === 'Student' && userData.isPaid !== undefined) {
        localStorage.setItem('isPaid', userData.isPaid.toString())
      }

      window.dispatchEvent(new Event('user-login'))

      let redirectPath = '/'
      switch (data.user.role as UserRole) {
        case 'Admin':
          redirectPath = '/admin'
          break
        case 'SuperAdmin':
          redirectPath = '/super_admin'
          break
        case 'Teacher':
          redirectPath = '/teacher'
          break
        case 'Parent':
          redirectPath = '/parent'
          break
        case 'Student':
          redirectPath = '/student'
          break
        case 'B2C_Client':
          redirectPath = '/b2c_student'
          break
        default:
          redirectPath = '/'
      }

      navigate({ to: redirectPath })
    },
    onError: (error: any) => {
      console.error('Login error:', error)
      setLoginError(null)

      if (error.response?.status === 401) {
        setLoginError('Invalid email or password. Please try again.')
      } else if (error.response?.data?.message) {
        setLoginError(error.response.data.message)
      } else if (error.message) {
        setLoginError(error.message)
      } else {
        setLoginError('An error occurred during login. Please try again.')
      }
    },
  })

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    onSubmit: async ({ value }) => {
      try {
        setLoginError(null)
        mutate(value)
      } catch (error) {
        console.error('Form submission error:', error)
      }
    },
  })

  return (
    <div className="flex h-screen mt-[1px]">
      <div className="flex-1 flex items-center justify-center ">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-250px]">
          <UserSay
            blueWord="Welcome"
            title="BACK"
            description="Welcome back to JeridSchool. Dashboard"
          />
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div>
                <form.Field
                  name="email"
                  children={(field) => (
                    <input
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      type="email"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder="Email"
                    />
                  )}
                />
              </div>
              <div>
                <form.Field
                  name="password"
                  children={(field) => (
                    <div>
                      <div className="relative">
                        <input
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          type={isPasswordVisible ? 'text' : 'password'}
                          className={`w-full px-4 py-2 border ${loginError ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none`}
                          placeholder="Password"
                        />
                        <button
                          type="button"
                          onClick={togglePasswordVisibility}
                          className="absolute inset-y-0 right-0 flex items-center pr-3"
                        >
                          {isPasswordVisible ? (
                            <EyeOff size={20} />
                          ) : (
                            <Eye size={20} />
                          )}
                        </button>
                      </div>
                      {loginError && (
                        <p className="mt-1 text-sm text-red-600">
                          {loginError}
                        </p>
                      )}
                    </div>
                  )}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-secondary focus:ring-secondary border-gray-300 rounded"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Remember me
                  </label>
                </div>
                <div className="text-sm">
                  <Link
                    to="/forgot-password"
                    className="font-medium text-black hover:text-[#F59E0B]"
                  >
                    Forgot your password?
                  </Link>
                </div>
              </div>
              <div>
                <button
                  type="submit"
                  disabled={isPending}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-[#FCD34D] hover:bg-[#F59E0B] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FCD34D] disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isPending ? 'Signing in...' : 'Sign in'}
                </button>
                  {/* Registration Link */}
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-600">
                    Don&apos;t have an account?{' '}
                    <Link
                      to="/register"
                      className="text-[#F59E0B] hover:text-[#FCD34D] font-medium"
                    >
                      Sign up
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </form>
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  Or continue with
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/images/auth/jeirdooo_superman.png"
          alt="Login Page Jeridoo Character in the same of superman postion"
          className="absolute inset-0 w-full h-full object-cover"
        />
      </div>
    </div>
  )
}
