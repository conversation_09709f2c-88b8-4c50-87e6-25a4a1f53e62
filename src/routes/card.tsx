import ProfilePage from '@/components/Profile'
import { createFileRoute } from '@tanstack/react-router'
import { guardAuthenticated } from '@/lib/auth/routeGuards'

export const Route = createFileRoute('/card')({
  component: RouteComponent,
  beforeLoad: guardAuthenticated,
})

function RouteComponent() {
  // Retrieve the JWT token from localStorage
  const jwt = localStorage.getItem('access_token')

  // Log the token for debugging
  console.log('Retrieved JWT from localStorage:', jwt)

  return (
    <div className="App">
      <ProfilePage jwt={jwt || ''} />
    </div>
  )
}
