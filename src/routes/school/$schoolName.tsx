import { createFileRoute, useParams } from '@tanstack/react-router'
import { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { schoolService } from '@/services/schoolService'
import { SchoolLandingPage } from '@/components/school/SchoolLandingPage'

// Define the school data interface
export interface SchoolData {
  id: string
  name: string
  address: string
  logo?: string
  url?: string
  CMSContent?: string
  services?: string[]
  isActive: boolean
  superAdminId?: string
  // Additional fields for the landing page
  description?: string
  heroImage?: string
  galleryImages?: string[]
  contactEmail?: string
  contactPhone?: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    instagram?: string
    linkedin?: string
  }
  stats?: {
    students?: number
    teachers?: number
    courses?: number
    awards?: number
  }
  banner: string
  primaryColor: string
  secondaryColor: string
}

// Define the loader data interface
interface SchoolLoaderData {
  schoolName: string
  isSubdomain: boolean
  originalSchoolName: string
}

// Create the route with a dynamic parameter for the school name
export const Route = createFileRoute('/school/$schoolName')({
  component: SchoolRoute,
  loader: ({ params }): SchoolLoaderData => {
    const { schoolName } = params
    console.log('School route loader - schoolName from params:', schoolName)

    // Check if we're on a subdomain
    let effectiveSchoolName = schoolName
    let isSubdomain = false

    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname
      console.log('School route loader - hostname:', hostname)

      // Check for subdomain format (anything.localhost or anything.jeridschool.tech)
      if (hostname.includes('.')) {
        // Skip www subdomain as it's not a school
        if (hostname.startsWith('www.')) {
          console.log(
            'School route loader - www subdomain detected, not a school subdomain'
          )
        } else {
          const parts = hostname.split('.')
          if (parts.length >= 2) {
            const subdomain = parts[0]
            // Skip 'localhost' and 'www' as school names
            if (subdomain !== 'localhost' && subdomain !== 'www') {
              isSubdomain = true
              console.log(
                'School route loader - detected subdomain:',
                subdomain
              )
              effectiveSchoolName = subdomain
            }
          }
        }
      }

      console.log('School route loader - isSubdomain:', isSubdomain)
      console.log(
        'School route loader - effectiveSchoolName:',
        effectiveSchoolName
      )

      // If we're not on a subdomain and we're accessing via /school/schoolname path,
      // redirect to the subdomain format
      if (!isSubdomain && schoolName) {
        console.log('Redirecting to subdomain format')
        // Redirect to the subdomain format
        schoolService.redirectToSubdomain(schoolName)
        // Return empty data as we're redirecting
        return {
          schoolName: effectiveSchoolName,
          isSubdomain,
          originalSchoolName: schoolName,
        }
      }
    }

    return {
      schoolName: effectiveSchoolName,
      isSubdomain,
      originalSchoolName: schoolName,
    }
  },
})

function SchoolRoute() {
  const { schoolName } = useParams({ from: '/school/$schoolName' })
  const loaderData = Route.useLoaderData() as SchoolLoaderData
  const effectiveSchoolName = loaderData.schoolName

  // Log information for debugging
  useEffect(() => {
    console.log('SchoolRoute component rendered')
    console.log('URL param schoolName:', schoolName)
    console.log('Effective schoolName:', effectiveSchoolName)
    console.log('Is subdomain access:', loaderData.isSubdomain)
    console.log('Original schoolName:', loaderData.originalSchoolName)
    console.log('Current hostname:', window.location.hostname)
  }, [schoolName, effectiveSchoolName, loaderData])

  // Fetch school data
  const {
    data: school,
    isLoading,
    error,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['school', effectiveSchoolName],
    queryFn: () => schoolService.getSchoolByUrl(effectiveSchoolName),
    retry: 2, // Increase retries to handle potential network issues
    staleTime: 5 * 60 * 1000, // Cache data for 5 minutes
    enabled: !!effectiveSchoolName, // Only run the query if we have a school name
  })

  // Retry fetching data if there's an error
  useEffect(() => {
    if (isError && effectiveSchoolName) {
      console.log('Error fetching school data, retrying...')
      const timer = setTimeout(() => {
        refetch()
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [isError, effectiveSchoolName, refetch])

  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <p className="text-gray-600">
          Loading {effectiveSchoolName} school data...
        </p>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4">
          <h2 className="text-xl font-bold mb-2">School Not Found</h2>
          <p>We couldn't find the school you're looking for.</p>
          <p className="mt-2 text-sm text-red-600">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
          <div className="mt-4 p-3 bg-gray-100 rounded text-sm font-mono">
            <p>Debug Info:</p>
            <p>Requested school: {effectiveSchoolName}</p>
            <p>URL parameter: {schoolName}</p>
            <p>Subdomain access: {loaderData.isSubdomain ? 'Yes' : 'No'}</p>
            <p>
              Current hostname:{' '}
              {typeof window !== 'undefined' ? window.location.hostname : 'N/A'}
            </p>
            <p>
              API URL:{' '}
              {import.meta.env.VITE_API_URL || 'http://localhost:3000'}
            </p>
            <p>
              API Endpoint:{' '}
              {`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/public/etablissement/url/${effectiveSchoolName}`}
            </p>
            <button
              onClick={() => refetch()}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
            <button
              onClick={() =>
                (window.location.href = 'https://www.jeridschool.tech')
              }
              className="mt-2 ml-2 px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!school) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg p-4">
          <h2 className="text-xl font-bold mb-2">School Not Found</h2>
          <p>
            We couldn't find a school with the name "{effectiveSchoolName}".
          </p>
          <div className="mt-4 p-3 bg-gray-100 rounded text-sm font-mono">
            <p>Debug Info:</p>
            <p>Requested school: {effectiveSchoolName}</p>
            <p>URL parameter: {schoolName}</p>
            <p>Subdomain access: {loaderData.isSubdomain ? 'Yes' : 'No'}</p>
            <p>
              Current hostname:{' '}
              {typeof window !== 'undefined' ? window.location.hostname : 'N/A'}
            </p>
            <p>
              API URL:{' '}
              {import.meta.env.VITE_API_URL || 'http://localhost:3000'}
            </p>
            <p>
              API Endpoint:{' '}
              {`${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/public/etablissement/url/${effectiveSchoolName}`}
            </p>
            <button
              onClick={() => refetch()}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
            <button
              onClick={() =>
                (window.location.href = 'https://www.jeridschool.tech')
              }
              className="mt-2 ml-2 px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Ensure the school is active
  if (!school.isActive) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg p-4">
          <h2 className="text-xl font-bold mb-2">School Not Active</h2>
          <p>The school "{school.name}" is currently not active.</p>
          <div className="mt-4 p-3 bg-gray-100 rounded text-sm font-mono">
            <p>Debug Info:</p>
            <p>Requested school: {effectiveSchoolName}</p>
            <p>URL parameter: {schoolName}</p>
            <p>Subdomain access: {loaderData.isSubdomain ? 'Yes' : 'No'}</p>
            <p>
              Current hostname:{' '}
              {typeof window !== 'undefined' ? window.location.hostname : 'N/A'}
            </p>
            <p>School ID: {school.id}</p>
            <p>School Name: {school.name}</p>
            <p>Is Active: {school.isActive ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    )
  }

  return <SchoolLandingPage school={school} />
}
