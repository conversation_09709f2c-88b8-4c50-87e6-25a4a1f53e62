import { useForm } from '@tanstack/react-form'
import { createFileRoute } from '@tanstack/react-router'

interface FormData {
  fullname: string
  email: string
  schoolname: string
  phonenumber: string
}

export const Route = createFileRoute('/join')({
  component: JoinForm,
})

function JoinForm(): JSX.Element {
  const form = useForm({
    defaultValues: {
      fullname: '',
      email: '',
      schoolname: '',
      phonenumber: '',
    },
    onSubmit: async ({ value }: { value: FormData }) => {
      try {
        // Ensure data exactly matches the column names
        const dataToSubmit = {
          data: [
            {
              fullname: value.fullname,
              email: value.email,
              schoolname: value.schoolname,
              phonenumber: value.phonenumber,
            },
          ],
        }

        const response = await fetch(
          'https://sheetdb.io/api/v1/b81ea9nhoaxhx',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(dataToSubmit),
          }
        )

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to submit form')
        }

        const result = await response.json()
        console.log('Form submitted successfully:', result)
        alert('Form submitted successfully!')

        form.reset()
      } catch (error) {
        console.error('Submission error:', error)
        alert('Failed to submit form. Please try again.')
      }
    },
  })

  // Rest of the component remains the same
  return (
    <div className="flex h-screen mt-[1px]">
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4">
          <div className="text-center">
            <h2 className="text-4xl font-extrabold text-gray-900 dark:text-white">
              Join Us
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Provide your details to join our community.
            </p>
          </div>

          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              void form.handleSubmit()
            }}
            className="space-y-6"
          >
            {/* Full Name Field */}
            <div>
              <form.Field name="fullname">
                {(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Full Name"
                    required
                  />
                )}
              </form.Field>
            </div>

            {/* Email Field */}
            <div>
              <form.Field name="email">
                {(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="email"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Email Address"
                    required
                  />
                )}
              </form.Field>
            </div>

            {/* School Name Field */}
            <div>
              <form.Field name="schoolname">
                {(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="School Name"
                    required
                  />
                )}
              </form.Field>
            </div>

            {/* Phone Number Field */}
            <div>
              <form.Field name="phonenumber">
                {(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="tel"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Phone Number"
                    required
                  />
                )}
              </form.Field>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={form.state.isSubmitting}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {form.state.isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default JoinForm
