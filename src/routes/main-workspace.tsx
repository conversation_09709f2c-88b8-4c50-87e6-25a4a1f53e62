import { createFileRoute } from '@tanstack/react-router'
import {
  ClipboardCheck,
  Database,
  Server,
  Box,
  Rocket,
  MoreHorizontal,
  Clock,
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

export const Route = createFileRoute('/main-workspace')({
  component: RouteComponent,
})

function RouteComponent() {
  return <MainWorkSpace />
}

function MainWorkSpace() {
  const services = [
    {
      icon: <ClipboardCheck className="h-6 w-6" />,
      name: 'Quizz Generator',
      href: '/quizz',
    },
    {
      icon: <Database className="h-6 w-6" />,
      name: 'Data Storage',
      href: '/storage',
    },
    {
      icon: <Server className="h-6 w-6" />,
      name: 'API Services',
      href: '/api',
    },
    {
      icon: <Box className="h-6 w-6" />,
      name: 'Resources',
      href: '/resources',
    },
    {
      icon: <Rocket className="h-6 w-6" />,
      name: 'Deployments',
      href: '/deploy',
    },
  ]

  const resources = [
    { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
    { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
    { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
  ]

  return (
    <div className="flex flex-col max-w-5xl mx-auto p-6 space-y-8">
      <section>
        <h2 className="text-2xl font-bold mb-4">Services</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {services.map((service, index) => (
            <Card key={index} className="hover:bg-muted/50 transition-colors">
              <CardContent className="p-4">
                <a
                  href={service.href}
                  className="flex flex-col items-center text-center space-y-2"
                >
                  {service.icon}
                  <span className="text-sm font-medium">{service.name}</span>
                </a>
              </CardContent>
            </Card>
          ))}
          <Card className="hover:bg-muted/50 transition-colors">
            <CardContent className="p-4">
              <Button
                variant="ghost"
                className="w-full h-full flex flex-col items-center space-y-2"
              >
                <MoreHorizontal className="h-6 w-6" />
                <span className="text-sm font-medium">More services</span>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold mb-4">Resources</h2>
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Last Viewed</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {resources.map((resource, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <Server className="h-4 w-4" />
                      <span>{resource.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{resource.type}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span>{resource.lastViewed}</span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      </section>
    </div>
  )
}
