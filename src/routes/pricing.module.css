.fancyOverlay,
.fancyOverlay,
.fancyGlass {
  --primary-light: theme('colors.blue.300'); /* Update with light shade */
  --primary-main: theme('colors.blue.500'); /* Main primary color */
  --primary-darker: theme('colors.blue.900'); /* Darker primary */
  --secondary-light: theme('colors.green.300'); /* Light secondary color */
  --secondary-main: theme('colors.green.500'); /* Main secondary */
  --secondary-darker: theme('colors.green.900'); /* Darker secondary */
  --glass-color: 72, 187, 120; /* Example RGB for the glass effect */
}

/**
  * Overlay gradients & animation - used as page background.
  */
@property --fancy-x {
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}
@property --fancy-y {
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}

@keyframes roundabout {
  0% {
    --fancy-x: 60%;
    --fancy-y: 20%;

    opacity: 0;
  }

  5% {
    --fancy-x: 80%;
    --fancy-y: 10%;
  }

  20% {
    --fancy-x: 95%;
    --fancy-y: 5%;

    opacity: var(--maximum-opacity);
  }

  100% {
    --fancy-x: 100%;
    --fancy-y: 0%;

    opacity: var(--maximum-opacity);
  }
}

.fancyOverlay::after {
  --maximum-opacity: 0.1;

  content: '';
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
    54deg,
    var(--primary-light) var(--fancy-y) var(--fancy-y),
    var(--secondary-light) var(--fancy-x) var(--fancy-x)
  );
  animation: roundabout 10s ease-in-out both;
}

/**
  * Glass effect with a gradient background and blur - used for highlighting pricing cards.
  */
.fancyGlass,
.fancyGlassContrast {
  background:
    radial-gradient(
      63.94% 63.94% at 50% 0%,
      rgba(var(--glass-color), 0.12) 0%,
      rgba(var(--glass-color), 0) 100%
    ),
    rgba(var(--glass-color), 0.01);
  backdrop-filter: blur(6px);
  position: relative;
  overflow: hidden;
}

.fancyGlassContrast:after {
  content: '';
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background: var(--primary-darker);
  opacity: 0.1;
  position: absolute;
  top: -1px;
  left: -1px;
  z-index: -1;
}

.fancyGlassContrast:before,
.fancyGlass:before {
  content: '';
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background:
    linear-gradient(
      rgba(var(--glass-color), 0.12) 0%,
      rgba(var(--glass-color), 0) 74.04%
    ),
    linear-gradient(
      0deg,
      rgba(var(--glass-color), 0.04),
      rgba(var(--glass-color), 0.04)
    );
  position: absolute;
  top: -1px;
  left: -1px;
  mask: url("data:image/svg+xml,%3Csvg width='402' height='202' viewBox='0 0 402 202' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0.5' y='0.5' width='401' height='201' rx='9.5' /%3E%3C/svg%3E%0A");
  pointer-events: none;
}
