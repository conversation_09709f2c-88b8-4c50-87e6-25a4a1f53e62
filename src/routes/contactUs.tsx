import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'

export const Route = createFileRoute('/contactUs')({
  component: RouteComponent,
})

function RouteComponent() {
  return <ContactUs />
}

function ContactUs() {
  const form = useForm({
    defaultValues: {
      email: '',
      subject: '',
      message: '',
    },
    onSubmit: async ({ value }) => {
      try {
        console.log('Form submitted successfully:', value)
        // Handle form submission, e.g., send to an API
      } catch (error) {
        console.error('Submission error:', error)
      }
    },
  })

  return (
    <div className="flex h-screen mt-[1px]">
      {/* Left side form */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4">
          <div className="text-center">
            <h2 className="text-4xl font-extrabold text-gray-900 dark:text-white">
              Contact Us
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Got a technical issue? Need details? Let us know.
            </p>
          </div>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            <div>
              <form.Field
                name="email"
                children={(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="email"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Your email"
                  />
                )}
              />
            </div>
            <div>
              <form.Field
                name="subject"
                children={(field) => (
                  <input
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="text"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Subject"
                  />
                )}
              />
            </div>
            <div>
              <form.Field
                name="message"
                children={(field) => (
                  <textarea
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    rows={6}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                    placeholder="Your message"
                  />
                )}
              />
            </div>
            <button
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
