import SettingsPage from '@/components/Settings'
import { createFileRoute } from '@tanstack/react-router'
import { guardAuthenticated } from '@/lib/auth/routeGuards'

export const Route = createFileRoute('/settings')({
  component: RouteComponent,
  beforeLoad: guardAuthenticated,
})

function RouteComponent() {
  const jwtToken = localStorage.getItem('access_token') || ''

  console.log('Settings page loaded')
  console.log('JWT Token:', jwtToken)

  // Debug function to decode JWT
  const decodeJWT = (token: string) => {
    try {
      if (!token) return { error: 'No token provided' }

      const parts = token.split('.')
      if (parts.length !== 3) return { error: 'Invalid token format' }

      const payload = JSON.parse(
        atob(parts[1].replace(/-/g, '+').replace(/_/g, '/'))
      )
      console.log('Decoded JWT payload:', payload)
      return payload
    } catch (error) {
      console.error('Error decoding token:', error)
      return { error: 'Failed to decode token' }
    }
  }

  // Log decoded token
  const decodedToken = decodeJWT(jwtToken)
  console.log('JWT decoded in route:', decodedToken)

  // Log localStorage items
  console.log('LocalStorage items:')
  Object.keys(localStorage).forEach((key) => {
    console.log(
      `${key}: ${key === 'access_token' ? 'TOKEN_HIDDEN' : localStorage.getItem(key)}`
    )
  })

  if (!jwtToken) {
    return <div className="p-4">Please log in to access your settings</div>
  }

  return <SettingsPage jwt={jwtToken} />
}
