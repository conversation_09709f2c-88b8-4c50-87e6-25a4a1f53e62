import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  Brain,
  Youtube,
  Upload,
  MessageSquare,
  BookOpen,
  StickyNote,
  Zap,
  Bug
} from 'lucide-react'
import AIProcessingInterface from '@/components/b2c_student/AIProcessingInterface'
import AIResultsDisplay from '@/components/b2c_student/AIResultsDisplay'
import AIChatbot from '@/components/b2c_student/AIChatbot'
import AIHealthStatus from '@/components/b2c_student/AIHealthStatus'
import FlashcardDemo from '@/components/b2c_student/FlashcardDemo'
import AIDebugPanel from '@/components/b2c_student/AIDebugPanel'
import { useState } from 'react'
import { ProcessAIResponse } from '@/services/aiService'

export const Route = createFileRoute('/ai-demo')({
  component: AIDemoPage,
})

function AIDemoPage() {
  const [aiResults, setAiResults] = useState<ProcessAIResponse | null>(null)
  const [showResults, setShowResults] = useState(false)

  const handleResultsGenerated = (results: ProcessAIResponse) => {
    setAiResults(results)
    setShowResults(true)
  }

  const handleBackToProcessing = () => {
    setShowResults(false)
    setAiResults(null)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Brain className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AI Learning Assistant Demo</h1>
            <p className="text-muted-foreground">
              Experience the power of AI-driven educational content generation
            </p>
          </div>
        </div>

        {/* Feature badges */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Youtube className="h-3 w-3" />
            YouTube Processing
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Upload className="h-3 w-3" />
            File Upload
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            Quiz Generation
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <StickyNote className="h-3 w-3" />
            Smart Notes
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <MessageSquare className="h-3 w-3" />
            AI Chat
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content Area */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="processing" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="processing" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                AI Processing
              </TabsTrigger>
              <TabsTrigger value="demo" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Flashcard Demo
              </TabsTrigger>
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                AI Assistant
              </TabsTrigger>
              <TabsTrigger value="debug" className="flex items-center gap-2">
                <Bug className="h-4 w-4" />
                Debug
              </TabsTrigger>
            </TabsList>

            {/* AI Processing Tab */}
            <TabsContent value="processing" className="space-y-6">
              {showResults && aiResults ? (
                <AIResultsDisplay
                  results={aiResults}
                  onBack={handleBackToProcessing}
                />
              ) : (
                <div className="space-y-6">
                  {/* Instructions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Brain className="h-5 w-5" />
                        How to Use AI Processing
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <h4 className="font-medium flex items-center gap-2">
                            <Youtube className="h-4 w-4" />
                            YouTube Processing
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Paste any YouTube URL to generate quizzes, flashcards, and notes from the video content.
                          </p>
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium flex items-center gap-2">
                            <Upload className="h-4 w-4" />
                            File Upload
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Upload PDFs, documents, or images to extract and process educational content.
                          </p>
                        </div>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h5 className="font-medium text-blue-900 mb-2">💡 Pro Tips:</h5>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>• Use custom prompts to focus on specific topics</li>
                          <li>• Processing takes 30-60 seconds for AI analysis</li>
                          <li>• Supported formats: PDF, DOC, TXT, CSV, JPG, PNG</li>
                          <li>• YouTube videos are transcribed and analyzed</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  {/* AI Processing Interface */}
                  <AIProcessingInterface
                    onResultsGenerated={handleResultsGenerated}
                  />
                </div>
              )}
            </TabsContent>

            {/* Flashcard Demo Tab */}
            <TabsContent value="demo" className="space-y-6">
              <FlashcardDemo />
            </TabsContent>

            {/* AI Chat Tab */}
            <TabsContent value="chat" className="space-y-6">
              {/* Chat Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    AI Assistant Features
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <BookOpen className="h-6 w-6 mx-auto mb-2 text-green-600" />
                      <h5 className="font-medium text-green-900">Study Help</h5>
                      <p className="text-green-700">Ask questions about your study materials</p>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <Brain className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                      <h5 className="font-medium text-blue-900">Concept Explanation</h5>
                      <p className="text-blue-700">Get detailed explanations of complex topics</p>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <Zap className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                      <h5 className="font-medium text-purple-900">Quick Answers</h5>
                      <p className="text-purple-700">Get instant responses to your questions</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* AI Chatbot */}
              <AIChatbot />
            </TabsContent>

            {/* Debug Tab */}
            <TabsContent value="debug" className="space-y-6">
              <AIDebugPanel />
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* AI Health Status */}
          <AIHealthStatus />

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Demo Features</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">AI Models:</span>
                <span className="font-medium">GPT-3.5-Turbo</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Transcription:</span>
                <span className="font-medium">OpenAI Whisper</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Document Processing:</span>
                <span className="font-medium">IBM Docling</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Response Time:</span>
                <span className="font-medium">30-60s</span>
              </div>
            </CardContent>
          </Card>

          {/* Sample Content */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Try These Examples</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h5 className="font-medium mb-1">YouTube URLs:</h5>
                <p className="text-muted-foreground text-xs">
                  Educational videos, lectures, tutorials
                </p>
              </div>
              <div>
                <h5 className="font-medium mb-1">Chat Prompts:</h5>
                <ul className="text-muted-foreground text-xs space-y-1">
                  <li>• "Explain quantum physics"</li>
                  <li>• "Create a study plan"</li>
                  <li>• "Help me understand calculus"</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
