import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import UserSay from '@/components/UserSay'
import { ArrowLeft, Eye, EyeOff } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/new-password')({
  component: RouteComponent,
})

function RouteComponent() {
  return <ForgotPassword />
}

function ForgotPassword() {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false)

  // // Use the mutation hook for forgot password
  // const { mutate } = useMutation({
  //   mutationFn: services.auth.forgotPassword,
  //   onSuccess: (data) => {
  //     console.log('Reset link sent successfully:', data)
  //     navigate({ to: '/login' })
  //   },
  //   onError: (error) => {
  //     console.error('Forgot password error:', error)
  //   },
  // })

  const form = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    onSubmit: async ({}) => {
      try {
        // mutate(value)
      } catch (error) {
        console.error('Form submission error:', error)
      }
    },
  })

  const togglePasswordVisibility = () => {
    setIsPasswordVisible((prev) => !prev)
  }

  const toggleConfirmPasswordVisibility = () => {
    setIsConfirmPasswordVisible((prev) => !prev)
  }

  return (
    <div className="flex h-screen mt-[1px]">
      {/* Left side form */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-250px]">
          <UserSay
            blueWord="Set a new"
            title="PASSWORD"
            description="Enter your new password and confirm password"
          />
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div>
                <form.Field
                  name="password"
                  children={(field) => (
                    <div className="relative">
                      <input
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type={isPasswordVisible ? 'text' : 'password'}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        placeholder="New Password"
                      />
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                      >
                        {isPasswordVisible ? (
                          <EyeOff size={20} />
                        ) : (
                          <Eye size={20} />
                        )}
                      </button>
                    </div>
                  )}
                />
              </div>
              <div>
                <form.Field
                  name="confirmPassword"
                  children={(field) => (
                    <div className="relative">
                      <input
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type={isConfirmPasswordVisible ? 'text' : 'password'}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                        placeholder="Confirm Password"
                      />
                      <button
                        type="button"
                        onClick={toggleConfirmPasswordVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                      >
                        {isConfirmPasswordVisible ? (
                          <EyeOff size={20} />
                        ) : (
                          <Eye size={20} />
                        )}
                      </button>
                    </div>
                  )}
                />
              </div>
              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-3xl shadow-sm text-sm font-medium text-white bg-[#FCD34D] hover:bg-[#F59E0B] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FCD34D]"
                >
                  Set New Password
                </button>
                <div className="text-center mt-4">
                  <Link
                    to="/login"
                    className="inline-flex items-center gap-2 text-sm text-[#F59E0B] hover:text-[#FCD34D] font-medium"
                  >
                    <ArrowLeft size={16} />
                    Back to login
                  </Link>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Right side image for lg screens */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/images/auth/signin.png"
          alt="Login"
          className="absolute inset-0 w-full h-full object-cover"
        />
      </div>
    </div>
  )
}

export default ForgotPassword
