import { createFile<PERSON>out<PERSON>, useNavigate } from '@tanstack/react-router'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useChat } from '@/hooks/useChat'
import { Search, Send, Plus, ArrowLeftCircle } from 'lucide-react'
import { useRef, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { Badge } from '@/components/ui/badge'
import { guardTeacherStudentParent } from '@/lib/auth/routeGuards'
import { ExtendedUser, Message, ExtendedConversation } from '@/types/chat'

// Define API URL with fallback options
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

// Extended User interface to include the properties used in this component

// Extended Conversation interface to include isGroupChat

// Utility functions for chat
const chatUtils = {
  getInitials: (user: ExtendedUser) => {
    return `${user.firstname.charAt(0)}${user.lastname.charAt(0)}`.toUpperCase()
  },
  getAvatarUrl: (user: ExtendedUser) => {
    // First try to use the user's actual avatar if available
    if (user.avatar) {
      // Use CdnImage component's approach to handle CDN URLs
      if (user.avatar.includes('cdn.jeridschool.tech') || 
          user.avatar.includes('**************')) {
        return `${API_URL}/health/proxy/image?url=${encodeURIComponent(user.avatar)}`
      }
      return user.avatar
    }
    
    // Fall back to placeholder if no avatar
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      user.displayName ||
        `${user.firstname} ${user.lastname}`.trim() ||
        user.username ||
        'User'
    )}&size=128&background=random`
  },
}

export const Route = createFileRoute('/chat')({
  beforeLoad: guardTeacherStudentParent,
  component: ChatPage,
})

function ChatPage() {
  return <ChatContent />
}

function ChatContent() {
  const navigate = useNavigate()
  const {
    conversations,
    selectedConversation,
    selectedConversationId,
    message,
    setMessage,
    isLoading,
    searchTerm,
    setSearchTerm,
    searchResults,
    isSearching,
    isSearchLoading,
    messagesEndRef,
    handleSendMessage,
    startNewConversation,
    handleSelectConversation,
    getOtherParticipant,
    toggleSearch,
    conversationFilter,
    setConversationFilter,
  } = useChat()

  // Function to go back to the appropriate dashboard based on user role
  function goBack() {
    const userRole = localStorage.getItem('role')
    let redirectPath = '/'

    switch (userRole) {
      case 'Admin':
        redirectPath = '/admin'
        break
      case 'SuperAdmin':
        redirectPath = '/super_admin'
        break
      case 'Teacher':
        redirectPath = '/teacher'
        break
      case 'Parent':
        redirectPath = '/parent'
        break
      case 'Student':
        redirectPath = '/student'
        break
      default:
        redirectPath = '/'
    }

    navigate({ to: redirectPath })
  }

  // For backward compatibility, get currentUser from localStorage
  const [currentUser, setCurrentUser] = useState<ExtendedUser | null>(null)

  // Create a ref for the messages container
  const messagesContainerRef = useRef<HTMLDivElement | null>(null)

  // Create a map of message refs for intersection observer
  const messageRefs = useRef<Map<string, HTMLDivElement>>(new Map())

  // Set up intersection observer to mark messages as read when they become visible
  useEffect(() => {
    if (!selectedConversation || !selectedConversationId) return

    // Create an intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const messageId = entry.target.getAttribute('data-message-id')
            if (messageId) {
              // Find the message in the selected conversation
              const message = selectedConversation.messages.find(
                (msg: Message) => msg.id === messageId
              )

              // If the message is from another user and not read, mark it as read
              if (
                message &&
                message.senderId !== currentUser?.id &&
                !message.isRead
              ) {
                // The message is now visible, so it's considered read
                // The actual marking as read is handled by the handleSelectConversation function
                // This is just to trigger a UI update if needed
                console.log('Message visible and marked as read:', messageId)
              }
            }
          }
        })
      },
      { threshold: 0.5 } // 50% of the message must be visible
    )

    // Observe all message elements
    messageRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref)
      }
    })

    return () => {
      // Clean up observer
      observer.disconnect()
    }
  }, [selectedConversation, selectedConversationId, currentUser])

  // Function to store message refs
  const setMessageRef = (id: string, element: HTMLDivElement | null) => {
    if (element) {
      messageRefs.current.set(id, element)
    } else {
      messageRefs.current.delete(id)
    }
  }

  // Initialize currentUser from localStorage
  useEffect(() => {
    const userId = localStorage.getItem('id')
    const userRole = localStorage.getItem('role')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')

    if (userId && userRole && firstname && lastname) {
      setCurrentUser({
        id: userId,
        firstname,
        lastname,
        role: userRole as 'Teacher' | 'Student' | 'Parent',
      })
    }
  }, [])

  // Add a state to track loading timeout
  const [loadingTimeout, setLoadingTimeout] = useState(false)

  // Set a timeout for loading
  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    if (isLoading) {
      timeoutId = setTimeout(() => {
        setLoadingTimeout(true)
      }, 10000) // 10 seconds timeout
    } else {
      setLoadingTimeout(false)
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [isLoading])

  // Scroll to bottom when component mounts or when conversation changes
  useEffect(() => {
    if (selectedConversation && messagesEndRef.current) {
      // Scroll to bottom immediately
      messagesEndRef.current.scrollIntoView({
        behavior: 'auto',
        block: 'end',
      })

      // Also scroll after a short delay to ensure all content is loaded
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'auto',
            block: 'end',
          })
        }
      }, 100)
    }
  }, [selectedConversation?.id])

  // Fix the handleSelectConversation call
  const onSelectConversation = (conversation: ExtendedConversation) => {
    if (conversation && conversation.id) {
      handleSelectConversation(conversation.id)
    }
  }

  // Add a useEffect to automatically select the most recent conversation if none is selected
  useEffect(() => {
    // If there are conversations but none is selected, select the most recent one
    if (conversations.length > 0 && !selectedConversationId) {
      // Find the most recent conversation with unread messages
      const unreadConversation = conversations.find((conv) =>
        conv.messages.some(
          (msg) => !msg.isRead && msg.senderId !== currentUser?.id
        )
      )

      // If there's a conversation with unread messages, select it
      if (unreadConversation) {
        handleSelectConversation(unreadConversation.id)
      }
      // Otherwise, select the most recent conversation for all users
      else if (conversations[0]) {
        handleSelectConversation(conversations[0].id)
      }
    }
  }, [
    conversations,
    selectedConversationId,
    currentUser,
    handleSelectConversation,
  ])

  // Add a useEffect to detect and select new conversations with messages
  useEffect(() => {
    // This will run whenever the conversations list changes
    if (conversations.length > 0) {
      // Check if there's a new conversation with unread messages
      const newConversationWithMessages = conversations.find(
        (conv) =>
          // Has unread messages not from the current user
          conv.messages.some(
            (msg) => !msg.isRead && msg.senderId !== currentUser?.id
          ) &&
          // Is not the currently selected conversation
          conv.id !== selectedConversationId
      )

      // If there's a new conversation with messages, select it for all users
      if (newConversationWithMessages) {
        handleSelectConversation(newConversationWithMessages.id)
      }
    }
  }, [
    conversations,
    selectedConversationId,
    currentUser,
    handleSelectConversation,
  ])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-lg font-medium text-gray-700">Loading chat...</p>

        {loadingTimeout && (
          <div className="mt-8 max-w-md text-center p-4 bg-white rounded-lg shadow">
            <p className="text-amber-600 font-medium mb-2">
              Taking longer than expected
            </p>
            <p className="text-gray-600 mb-4">
              There might be an issue connecting to the server. You can try:
            </p>
            <ul className="text-left text-gray-600 mb-4 space-y-2">
              <li>• Checking your internet connection</li>
              <li>• Refreshing the page</li>
              <li>• Logging out and logging back in</li>
            </ul>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        )}
      </div>
    )
  }

  if (!currentUser) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-lg font-medium text-gray-700">
          Loading user data...
        </p>

        {loadingTimeout && (
          <div className="mt-8 max-w-md text-center p-4 bg-white rounded-lg shadow">
            <p className="text-amber-600 font-medium mb-2">
              Unable to load user data
            </p>
            <p className="text-gray-600 mb-4">This could be due to:</p>
            <ul className="text-left text-gray-600 mb-4 space-y-2">
              <li>• Your session may have expired</li>
              <li>• There might be an issue with the server</li>
              <li>• You might need to log in again</li>
            </ul>
            <button
              onClick={() => (window.location.href = '/login')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Go to Login
            </button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex flex-col h-[calc(100vh-60px)] bg-gray-100">
      {/* Back button */}
      <div className="bg-white p-2 border-b border-gray-200">
        <button
          onClick={goBack}
          className="flex items-center gap-2 p-2 rounded-lg text-gray-700 hover:bg-gray-200 hover:text-gray-900 text-left"
        >
          <ArrowLeftCircle className="h-5 w-5 text-gray-700" />
          <span className="text-sm font-medium">Back to Dashboard</span>
        </button>
      </div>

      {/* Chat container */}
      <div className="flex flex-1 overflow-hidden">
      {/* Sidebar - reduced width */}
      <div className="w-1/5 bg-white border-r border-gray-200 flex flex-col">
        {/* User info - more compact */}
        <div className="p-2 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            {/* <Avatar className="h-8 w-8">
              <AvatarImage src={chatUtils.getAvatarUrl(currentUser)} />
              <AvatarFallback>
                {chatUtils.getInitials(currentUser)}
              </AvatarFallback>
            </Avatar> */}
            <div>
              <p className="font-medium text-sm">
                {currentUser.firstname} {currentUser.lastname}
              </p>
              <p className="text-xs text-gray-500">({currentUser.role})</p>
            </div>
          </div>
        </div>

        {/* Search or conversation list toggle - more compact */}
        <div className="p-2 border-b border-gray-200">
          <div className="flex space-x-1">
            <Button
              variant={isSearching ? 'default' : 'outline'}
              className="flex-1 text-xs h-8"
              onClick={toggleSearch}
            >
              {isSearching ? 'Back to Chats' : 'New Chat'}
            </Button>
            {(currentUser?.role === 'Teacher' ||
              currentUser?.role === 'Parent') && (
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={toggleSearch}
                title={
                  currentUser.role === 'Teacher'
                    ? 'Start a new chat with a student'
                    : 'Start a new chat with a teacher'
                }
              >
                <Plus className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Search UI or Conversation list */}
        {isSearching ? (
          <div className="flex-1 overflow-auto">
            <div className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={
                    currentUser.role === 'Teacher'
                      ? 'Search for students...'
                      : 'Search for teachers...'
                  }
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Search results */}
            <div className="p-4">
              {isSearchLoading ? (
                <p className="text-center text-gray-500">Searching...</p>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2">
                  {searchResults.map((user: ExtendedUser) => (
                    <div
                      key={user.id}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 cursor-pointer"
                      onClick={() => startNewConversation(user.id)}
                    >
                      <Avatar>
                        <AvatarImage src={chatUtils.getAvatarUrl(user)} />
                        <AvatarFallback>
                          {chatUtils.getInitials(user)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">
                          {user.firstname} {user.lastname}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {user.role}
                        </p>
                        {user.displayName &&
                          user.displayName !==
                            `${user.firstname} ${user.lastname}` && (
                            <p className="text-xs text-gray-400 truncate">
                              ({user.displayName})
                            </p>
                          )}
                        {user.username && (
                          <p className="text-xs text-gray-400 truncate">
                            @{user.username}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchTerm ? (
                <p className="text-center text-gray-500">No results found</p>
              ) : (
                <p className="text-center text-gray-500">
                  {currentUser.role === 'Teacher'
                    ? 'Search for students to start a chat'
                    : 'Search for teachers to start a chat'}
                </p>
              )}
            </div>
          </div>
        ) : (
          <div className="flex-1 overflow-auto">
            {/* Filter conversations */}
            <div className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Filter conversations..."
                  className="pl-10"
                  value={conversationFilter}
                  onChange={(e) => setConversationFilter(e.target.value)}
                />
              </div>
            </div>

            {/* Conversations list */}
            <div className="space-y-1 p-2">
              {conversations.length > 0 ? (
                conversations.map((conversation) => {
                  const otherUser = getOtherParticipant(conversation)

                  // Count unread messages more accurately
                  const unreadMessages = conversation.messages.filter(
                    (msg) => !msg.isRead && msg.senderId !== currentUser.id
                  )
                  const hasUnread = unreadMessages.length > 0

                  // Get the last message for preview
                  const lastMessage =
                    conversation.messages.length > 0
                      ? conversation.messages[conversation.messages.length - 1]
                      : null

                  // Determine if this is the selected conversation
                  const isSelected =
                    selectedConversation?.id === conversation.id
                  
                  // Get display name - show only the other person's name
                  let displayName = '';
                  if (otherUser) {
                    // If we have the other user object, use their name
                    displayName = `${otherUser.firstname} ${otherUser.lastname}`;
                  } else if (conversation.name && conversation.name.includes('-')) {
                    // If name is in format "User1 - User2", extract the other user's name
                    const parts = conversation.name.split('-').map(p => p.trim());
                    const currentUserName = `${currentUser.firstname} ${currentUser.lastname}`;
                    
                    if (parts[0] === currentUserName) {
                      displayName = parts[1];
                    } else {
                      displayName = parts[0];
                    }
                  } else {
                    // Fallback to the conversation name or "Chat"
                    displayName = conversation.name || 'Chat';
                  }
                  return (
                    <div
                      key={conversation.id}
                      className={cn(
                        'flex items-center space-x-3 p-3 rounded-lg cursor-pointer',
                        isSelected
                          ? 'bg-blue-50 hover:bg-blue-100'
                          : 'hover:bg-gray-100',
                        hasUnread && 'font-semibold'
                      )}
                      onClick={() => onSelectConversation(conversation)}
                    >
                      {/* <Avatar>
                        <AvatarImage
                          src={
                            otherUser ? chatUtils.getAvatarUrl(otherUser) : ''
                          }
                        />
                        <AvatarFallback>
                          {otherUser ? chatUtils.getInitials(otherUser) : 'GC'}
                        </AvatarFallback>
                      </Avatar> */}
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-center">
                          <p
                            className={cn(
                              'truncate',
                              hasUnread && 'font-semibold'
                            )}
                          >
                            {displayName}
                          </p>
                          {lastMessage && (
                            <p className="text-xs text-gray-500">
                              {format(new Date(lastMessage.createdAt), 'HH:mm')}
                            </p>
                          )}
                        </div>
                        {otherUser && otherUser.role && (
                          <Badge variant="outline" className="text-xs">
                            {otherUser.role}
                          </Badge>
                        )}
                        {lastMessage && (
                          <p
                            className={cn(
                              'text-sm text-gray-500 truncate',
                              hasUnread && 'text-gray-900 font-semibold'
                            )}
                          >
                            {lastMessage.sender?.firstname ||
                            lastMessage.senderId === currentUser.id
                              ? 'You: '
                              : ''}{' '}
                            {lastMessage.content}
                          </p>
                        )}
                      </div>
                      {hasUnread && (
                        <div className="flex flex-col items-center">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mb-1"></div>
                          {unreadMessages.length > 1 && (
                            <span className="text-xs bg-blue-500 text-white rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                              {unreadMessages.length}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })
              ) : (
                <p className="text-center text-gray-500 p-4">
                  No conversations yet
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Chat area - adjusted for better spacing */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Chat header - more compact */}
            <div className="p-2 border-b border-gray-200 bg-white">
              <div className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={
                      getOtherParticipant(selectedConversation)
                        ? chatUtils.getAvatarUrl(
                            getOtherParticipant(selectedConversation)!
                          )
                        : ''
                    }
                  />
                  <AvatarFallback>
                    {getOtherParticipant(selectedConversation)
                      ? chatUtils.getInitials(
                          getOtherParticipant(selectedConversation)!
                        )
                      : 'GC'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">
                    {getOtherParticipant(selectedConversation)
                      ? `${
                          getOtherParticipant(selectedConversation)!.firstname
                        } ${
                          getOtherParticipant(selectedConversation)!.lastname
                        }`
                      : selectedConversation.name || 'Group Chat'}
                  </p>
                  {getOtherParticipant(selectedConversation) && (
                    <p className="text-xs text-gray-500">
                      {getOtherParticipant(selectedConversation)!.role}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Messages container - adjusted height */}
            <div
              className="flex-1 overflow-y-auto p-3 bg-gray-50 relative"
              ref={messagesContainerRef}
              style={{
                height: 'calc(100vh - 180px)', // Adjusted for back button
                overflowY: 'auto',
                scrollBehavior: 'auto',
                scrollbarWidth: 'thin',
                overscrollBehavior: 'contain',
              }}
            >
              <div className="space-y-3 min-h-full flex flex-col justify-end">
                {selectedConversation.messages.map((msg: Message) => {
                  const isCurrentUser = msg.senderId === currentUser.id
                  return (
                    <div
                      key={msg.id}
                      className={cn(
                        'flex',
                        isCurrentUser ? 'justify-end' : 'justify-start'
                      )}
                      ref={(el) => setMessageRef(msg.id, el)}
                      data-message-id={msg.id}
                    >
                      <div
                        className={cn(
                          'max-w-[70%] rounded-lg p-2',
                          isCurrentUser
                            ? 'bg-blue-500 text-white'
                            : 'bg-white border border-gray-200'
                        )}
                      >
                        <p className="text-sm">{msg.content}</p>
                        <div
                          className={cn(
                            'text-xs mt-1 flex justify-between items-center',
                            isCurrentUser ? 'text-blue-100' : 'text-gray-400'
                          )}
                        >
                          <span>
                            {format(new Date(msg.createdAt), 'HH:mm')}
                          </span>
                          {msg.temporary && (
                            <span className="ml-2">Sending...</span>
                          )}
                          {msg.error && (
                            <span className="ml-2 text-red-500">
                              Error sending
                            </span>
                          )}
                          {!msg.temporary &&
                            !msg.error &&
                            isCurrentUser &&
                            msg.isRead && <span className="ml-2">Read</span>}
                        </div>
                      </div>
                    </div>
                  )
                })}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Message input - more compact and fixed at bottom */}
            <div className="sticky bottom-0 z-10 bg-white p-2 border-t border-gray-200">
              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  handleSendMessage(e)
                }}
                className="flex items-center space-x-2"
              >
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  autoComplete="off"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage(e)
                    }
                  }}
                />
                <button
                  type="submit"
                  className="p-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  disabled={!message.trim()}
                >
                  <Send size={16} />
                </button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900">
                Select a conversation
              </h3>
              <p className="text-gray-500 mt-1">
                Choose a conversation from the sidebar or start a new one
              </p>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}

export default ChatPage
