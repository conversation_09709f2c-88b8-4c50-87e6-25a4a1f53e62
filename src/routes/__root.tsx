import { Outlet, createRootRoute, <PERSON> } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'sonner'
import Footer from '@/components/Footer'
import React, { useState, useEffect } from 'react'
import { Menu, X } from 'lucide-react'
import { schoolService } from '@/services/schoolService'
import { SchoolLandingPage } from '@/components/school/SchoolLandingPage'
import { useQuery } from '@tanstack/react-query'
import UserProfileMenu from '@/components/UserProfileMenu'
import MessageIcon from '@/components/MessageIcon'
import { SubdomainDebugger } from '@/components/SubdomainDebugger'
import { SEOProvider } from '@/contexts/SEOContext'
import { OnboardingProvider, Onboarding } from '@/features/onboarding'
// Import the shared queryClient instance
import { queryClient } from '@/lib/queryClient'

export const Route = createRootRoute({
  component: RootComponent,
  beforeLoad: async ({ location }) => {
    // Check if the current path matches the school path pattern
    const schoolPathMatch = location.pathname.match(/^\/school\/([^\/]+)$/)

    // If it's a school path and we're not already on a subdomain
    if (schoolPathMatch && !schoolService.isSubdomainUrl()) {
      const schoolName = schoolPathMatch[1]

      // Redirect to the subdomain format
      if (typeof window !== 'undefined') {
        schoolService.redirectToSubdomain(schoolName)
      }
    }

    return {}
  },
  notFoundComponent: () => {
    // Instead of redirecting, render the NotFound component directly
    const NotFound = React.lazy(() => import('@/components/notfound'))
    return (
      <React.Suspense fallback={<div>Loading...</div>}>
        <NotFound />
      </React.Suspense>
    )
  },
})

function RootComponent() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // Check if user is logged in
  useEffect(() => {
    const role = localStorage.getItem('role')

    // Set logged in state based on role presence
    setIsLoggedIn(!!role)

    // Listen for login event to update UI immediately after login
    const handleUserLogin = () => {
      setIsLoggedIn(true)
    }
    window.addEventListener('user-login', handleUserLogin)
    return () => {
      window.removeEventListener('user-login', handleUserLogin)
    }
  }, [])

  // Handle logout
  const handleLogout = () => {
    // Clear all localStorage items
    localStorage.removeItem('access_token')
    localStorage.removeItem('id')
    localStorage.removeItem('role')
    localStorage.removeItem('firstname')
    localStorage.removeItem('lastname')

    setIsLoggedIn(false)

    // Redirect to login page
    window.location.href = '/login'
  }

  // Get user role if logged in
  const userRole = isLoggedIn ? localStorage.getItem('role') : null

  // Determine dashboard path based on role
  const getDashboardPath = () => {
    if (!userRole) return '/'

    switch (userRole) {
      case 'Admin':
        return '/admin'
      case 'SuperAdmin':
        return '/super_admin'
      case 'Teacher':
        return '/teacher'
      case 'Parent':
        return '/parent'
      case 'Student':
        return '/student'
      case 'B2C_Client':
       return'/b2c_student'
      default:
        return '/'
    }
  }

  // Check if the current route is a school route or if we're on a subdomain
  const isSchoolRoute = window.location.pathname.startsWith('/school/')
  const isSubdomain = schoolService.isSubdomainUrl()

  // Check if we're on the www subdomain
  const isWwwSubdomain =
    typeof window !== 'undefined' && window.location.hostname.startsWith('www.')

  // If we're on a subdomain (but not www), we'll render the school landing page directly
  if (isSubdomain && !isWwwSubdomain) {
    return <SubdomainSchoolComponent />
  }

  // If we're on the www subdomain, we'll render the main layout
  // This ensures www.jeridschool.tech works correctly

  // If it's a regular school route with path /school/schoolName, don't render the main layout
  if (isSchoolRoute) {
    return (
      <QueryClientProvider client={queryClient}>
        <Outlet />
        <Toaster position="top-right" richColors />
        {import.meta.env.DEV && <TanStackRouterDevtools />}
        {import.meta.env.DEV && <ReactQueryDevtools />}
      </QueryClientProvider>
    )
  }

  const NavigationLinks = () => {
    return (
      <>
        <Link
          to={isLoggedIn ? getDashboardPath() : '/'}
          className="text-gray-800 hover:text-blue-600 font-medium"
        >
          Home
        </Link>

        {isLoggedIn && (
          <>
            <Link
              to={getDashboardPath()}
              className="text-gray-800 hover:text-blue-600 font-medium"
            >
              Dashboard
            </Link>
          </>
        )}

        <a
          href="/about"
          className="text-gray-800 hover:text-blue-600 font-medium"
        >
          About Us
        </a>
      </>
    )
  }

  const AuthButtons = () => (
    <>
      {isLoggedIn ? (
        <div className="flex items-center space-x-4">
          {/* Show message icon only for Student and Teacher roles */}
          {(userRole === 'Student' || userRole === 'Teacher') && (
            <MessageIcon className="mr-2" />
          )}
          <UserProfileMenu onLogout={handleLogout} />
        </div>
      ) : (
        <>
          <Link
            to="/login"
            className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-blue-600 transition-colors mr-2"
          >
            Login
          </Link>
          <Link
            to="/join"
            className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-blue-600 font-bold"
          >
            Get a demo
          </Link>
        </>
      )}
    </>
  )

  return (
    <QueryClientProvider client={queryClient}>
      <SEOProvider>
        <OnboardingProvider>
          <Onboarding>
            <div className="flex flex-col min-h-screen">
              <header className="bg-white shadow-md">
                <div className="container mx-auto px-4 py-4 flex justify-between items-center md:px-1 md:container-none">
                  <div className="text-[22px] font-bold font-poppins text-gray-800">
                    <Link
                      to={isLoggedIn ? getDashboardPath() : '/'}
                      className="flex items-center"
                    >
                      <span className="text-[#525FE1] font-bold">Jerid</span>
                      <span className="text font-bold">School</span>
                      <span className="text-primary text-xs ml-1 font-semibold">
                        Alpha
                      </span>
                    </Link>
                  </div>

                  {/* Desktop Navigation */}
                  <nav className="hidden md:flex space-x-6">
                    <NavigationLinks />
                  </nav>

                  <div className="hidden md:flex space-x-4 items-center">
                    <AuthButtons />
                  </div>

                  <button
                    className="md:hidden flex items-center text-gray-800 focus:outline-none"
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  >
                    {isMobileMenuOpen ? (
                      <X className="h-6 w-6" />
                    ) : (
                      <Menu className="h-6 w-6" />
                    )}
                  </button>
                </div>

                {/* Mobile Navigation */}
                {isMobileMenuOpen && (
                  <div className="md:hidden">
                    <nav className="flex flex-col space-y-2 p-4">
                      <NavigationLinks />
                      <AuthButtons />
                    </nav>
                  </div>
                )}
              </header>
              <main className="flex-grow">
                <div className="flex-1 overflow-auto">
                  <Outlet />
                </div>
              </main>
              <Footer />
            </div>
            <Toaster position="top-right" richColors />
            {import.meta.env.DEV && <TanStackRouterDevtools />}
            {import.meta.env.DEV && <ReactQueryDevtools />}
          </Onboarding>
        </OnboardingProvider>
      </SEOProvider>
    </QueryClientProvider>
  )
}

// Component to handle subdomain access
function SubdomainSchoolComponent() {
  // Get the school name from the subdomain
  const schoolName = schoolService.getCurrentSchoolFromSubdomain()

  // Fetch school data using React Query
  const {
    data: school,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['school', schoolName],
    queryFn: () =>
      schoolName
        ? schoolService.getSchoolByUrl(schoolName)
        : Promise.reject('No school name found'),
    enabled: !!schoolName, // Only run the query if we have a school name
    staleTime: 1000 * 60 * 10, // Cache for 10 minutes
    gcTime: 1000 * 60 * 30, // Keep in cache for 30 minutes (formerly cacheTime)
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    retry: 1, // Only retry once to prevent excessive API calls
  })

  if (!schoolName) {
    return (
      <QueryClientProvider client={queryClient}>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg p-4">
            <h2 className="text-xl font-bold mb-2">School Not Found</h2>
            <p>We couldn't determine the school from the subdomain.</p>
          </div>
          <SubdomainDebugger />
        </div>
        <Toaster position="top-right" richColors />
      </QueryClientProvider>
    )
  }

  if (isLoading) {
    return (
      <QueryClientProvider client={queryClient}>
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
        <Toaster position="top-right" richColors />
      </QueryClientProvider>
    )
  }

  if (error || !school) {
    return (
      <QueryClientProvider client={queryClient}>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4">
            <h2 className="text-xl font-bold mb-2">Error Loading School</h2>
            <p>We couldn't find the school you're looking for.</p>
            <p className="mt-2 text-sm text-red-600">
              {error instanceof Error
                ? error.message
                : 'Unknown error occurred'}
            </p>
            <div className="mt-4 p-3 bg-gray-100 rounded text-sm font-mono">
              <p>Debug Info:</p>
              <p>Requested school: {schoolName}</p>
              <p>Subdomain access: Yes</p>
            </div>
          </div>
          <SubdomainDebugger />
        </div>
        <Toaster position="top-right" richColors />
      </QueryClientProvider>
    )
  }

  // Render the school landing page with the fetched data
  return (
    <QueryClientProvider client={queryClient}>
      <SEOProvider>
        <SchoolLandingPage school={school} />
        <Toaster position="top-right" richColors />
        {import.meta.env.DEV && <TanStackRouterDevtools />}
        {import.meta.env.DEV && <ReactQueryDevtools />}
      </SEOProvider>
    </QueryClientProvider>
  )
}
