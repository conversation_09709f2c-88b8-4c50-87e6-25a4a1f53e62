import UserSay from '@/components/UserSay'
import { createFileRoute } from '@tanstack/react-router'
import { useForm } from '@tanstack/react-form'
import { authService } from '@/lib/api/services/auth-service'
import { useMutation } from '@tanstack/react-query'
import { useNavigate, Link } from '@tanstack/react-router'
import { useState, useRef } from 'react'
import { Eye, EyeOff, ArrowLeft } from 'lucide-react'
import { z } from 'zod'
import CloudflareTurnstile, { CloudflareTurnstileRef } from '@/components/shared/CloudflareTurnstile'
import BLACKLISTED_DOMAINS from '@/lib/utils/BlackListMails'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { YearMonthPicker } from '@/components/ui/year-month-picker'
import FileUploader from '@/components/shared/FileUploader'
import { DEFAULT_AVATAR } from '@/constants'
import { toast } from 'sonner'

/**
 * Cloudflare Turnstile Captcha Integration
 *
 * - Uses @marsidev/react-turnstile for the widget
 * - siteKey must match the Cloudflare dashboard
 * - Allowed domains in Cloudflare must include both production and local dev domains
 * - Captcha token is sent to backend as captchaToken
 * - On error, the widget is reset
 * - Backend must verify the token using the secret key
 */

// B2C client registration schema
  const b2cClientSchema = z.object({
  firstname: z.string().min(2, 'First name must be at least 2 characters'),
  lastname: z.string().min(2, 'Last name must be at least 2 characters'),
    email: z
      .string()
    .email('Invalid email format')
      .refine((email) => {
        const domain = email.split('@')[1]
        return !BLACKLISTED_DOMAINS.includes(domain)
    }, 'This email domain is not allowed. Please use a different email address.'),
    password: z
      .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
      .regex(
        /[!@#$%^&*(),.?":{}|<>]/,
      'Password must contain at least one special character'
      ),
    confirmPassword: z.string(),
    gender: z.enum(['MALE', 'FEMALE']),
    birthday: z.date().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    avatar: z.string().url().default(DEFAULT_AVATAR),
  schoolName: z.string().min(2, 'School name must be at least 2 characters'),
    enrolledDate: z.date().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
    path: ["confirmPassword"],
  })

  type B2CClientFormValues = z.infer<typeof b2cClientSchema>

export const Route = createFileRoute('/register')({
  component: RouteComponent,
})

function RouteComponent() {
  return <Register />
}

function Register() {
  const navigate = useNavigate()
  const [step, setStep] = useState(1)
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false)
  const [registrationError, setRegistrationError] = useState<string | null>(null)
  const [captchaToken, setCaptchaToken] = useState<string | null>(null)
  const captchaRef = useRef<CloudflareTurnstileRef>(null)

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible)
  }

  const toggleConfirmPasswordVisibility = () => {
    setIsConfirmPasswordVisible(!isConfirmPasswordVisible)
  }

  const { mutate, isPending } = useMutation({
    mutationFn: (data: any) => authService.register(data),
    onSuccess: () => {
      toast.success('Registration successful! You can now log in with your credentials.')
      navigate({ to: '/login' })
    },
    onError: (error: any) => {
      console.error('Registration error:', error)
      setRegistrationError(null)

      // Reset captcha on any error
      if (captchaRef.current) {
        captchaRef.current.reset()
        setCaptchaToken(null)
      }

      if (error.response?.data?.message) {
        setRegistrationError(error.response.data.message)
        toast.error(error.response.data.message)
      } else if (error.message) {
        setRegistrationError(error.message)
        toast.error(error.message)
      } else {
        const errorMessage = 'An error occurred during registration. Please try again.'
        setRegistrationError(errorMessage)
        toast.error(errorMessage)
      }
    },
  })

  // Create a ref to store the original form values for step 1
  const step1ValuesRef = useRef({
    firstname: '',
    lastname: '',
    email: '',
    password: '',
    confirmPassword: '',
  });

  // Add a state to track if we've already initialized step 1 values
  // We don't need this state anymore as we're handling the steps differently
  // const [step1Initialized, setStep1Initialized] = useState(false);

  const form = useForm<
    B2CClientFormValues, // TValues
    any,                 // TResponse
    any,                 // TFieldMeta
    any,                 // TFormMeta
    any,                 // TFieldValues
    any,                 // TFieldInput
    any,                 // TFieldOutput
    any,                 // TFieldError
    any,                 // TFormError
    any                  // TFormInput
  >({
    defaultValues: {
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      confirmPassword: '',
      gender: 'MALE',
      birthday: new Date(),
      address: '',
      phone: '',
      avatar: DEFAULT_AVATAR,
      schoolName: '',
      enrolledDate: new Date(),
    },
    onSubmit: async (data: { value: B2CClientFormValues }) => {
      try {
        // Extract the form values from the data object
        const formValues = data.value;

        console.log('onSubmit received data type:', typeof data,
                   'with keys:', Object.keys(data).join(', '));

        // Validate captcha token
        if (!captchaToken) {
          setRegistrationError('Please complete the captcha verification')
          toast.error('Please complete the captcha verification')
          return
        }

        console.log('Form submission with captcha token:', captchaToken.substring(0, 10) + '...')
        setRegistrationError(null)

        // Verify we have the password from step 1
        if (!step1ValuesRef.current.password || step1ValuesRef.current.password.length < 1) {
          console.error('Password is missing from step 1 values in onSubmit!');
          setRegistrationError('Password information is missing. Please go back to step 1 and try again.');
          toast.error('Password information is missing. Please go back to step 1 and try again.');
          return;
        }

        // Log the step 1 values to verify they're intact
        console.log('Step 1 values in onSubmit:', {
          firstname: step1ValuesRef.current.firstname,
          lastname: step1ValuesRef.current.lastname,
          email: step1ValuesRef.current.email,
          password: '[REDACTED]',
          confirmPassword: '[REDACTED]'
        });

        // Create a clean registration data object with only the fields needed for API submission
        const registrationData = {
          // Step 1 values - personal information (from our saved ref)
          firstname: step1ValuesRef.current.firstname,
          lastname: step1ValuesRef.current.lastname,
          email: step1ValuesRef.current.email,
          password: step1ValuesRef.current.password,

          // Step 2 values - additional details (from current form submission)
          gender: formValues.gender,
          birthday: formValues.birthday,
          address: formValues.address,
          phone: formValues.phone,
          schoolName: formValues.schoolName,
          enrolledDate: formValues.enrolledDate,
          avatar: formValues.avatar,
        };

        // Log the registration data for debugging
        console.log('Registration data before submission:', {
          ...registrationData,
          password: '[REDACTED]'
        });

        // Validate critical fields to ensure they're not mixed up
        if (!registrationData.firstname ||
            registrationData.firstname === 'MALE' ||
            registrationData.firstname === 'FEMALE') {
          setRegistrationError('First name appears to be invalid. Please go back and correct it.')
          toast.error('First name appears to be invalid')
          return
        }

        if (!registrationData.lastname ||
            (typeof registrationData.lastname === 'string' && registrationData.lastname.includes('GMT'))) {
          setRegistrationError('Last name appears to be invalid. Please go back and correct it.')
          toast.error('Last name appears to be invalid')
          return
        }

        if (!registrationData.email ||
            (typeof registrationData.email === 'string' && !registrationData.email.includes('@'))) {
          setRegistrationError('Email appears to be invalid. Please go back and correct it.')
          toast.error('Email appears to be invalid')
          return
        }

        // Create a clean object for the API
        const formData = {
          ...registrationData,
          // Add any additional fields needed by the API
        }

        // Create a minimal object with only the essential data
        // This ensures we don't send any unexpected properties to the backend
        const finalRegistrationData = {
          firstname: formData.firstname,
          lastname: formData.lastname,
          email: formData.email,
          password: formData.password,
          gender: formData.gender || 'MALE',
          birthday: formData.birthday instanceof Date ? formData.birthday : new Date(),
          address: formData.address || '',
          phone: formData.phone || '',
          avatar: formData.avatar || '',
          captchaToken: captchaToken,
          // Required boolean fields
          isActive: false, // Required by BaseUserDto
          userOnboarding: true // Optional but included for completeness
          // The backend will set role, etablissementId, etc.
        } as const

        // Submit the form data
        console.log('Submitting final registration data:', { ...finalRegistrationData, password: '[REDACTED]' })
        mutate(finalRegistrationData)
      } catch (error) {
        console.error('Form submission error:', error)
        toast.error('An error occurred while submitting the form')

        // Reset captcha on error
        if (captchaRef.current) {
          captchaRef.current.reset()
          setCaptchaToken(null)
        }
      }
    },
  })

  const validateStep1 = () => {
    const values = form.state.values;

    // Log the current values for debugging
    console.log('Validating step 1 with values:', {
      ...values,
      password: values.password ? '[REDACTED]' : '',
      confirmPassword: values.confirmPassword ? '[REDACTED]' : ''
    });

    // Check for empty required fields
    if (!values.firstname || !values.lastname || !values.email || !values.password || !values.confirmPassword) {
      toast.error('Please fill in all required fields');
      return false;
    }

    // Validate email format
    if (!values.email.includes('@')) {
      toast.error('Please enter a valid email address');
      return false;
    }

    // Validate password match
    if (values.password !== values.confirmPassword) {
      toast.error('Passwords do not match');
      return false;
    }

    // Check for potential data mixups
    if (values.firstname === 'MALE' || values.firstname === 'FEMALE') {
      toast.error('First name appears to be invalid');
      return false;
    }

    if (typeof values.lastname === 'string' && values.lastname.includes('GMT')) {
      toast.error('Last name appears to be invalid');
      return false;
    }

    return true;
  }

  const nextStep = () => {
    if (validateStep1()) {
      // Save step 1 values to the ref (this is now redundant as we save in validateStep1, but keeping for safety)
      step1ValuesRef.current = {
        firstname: form.state.values.firstname,
        lastname: form.state.values.lastname,
        email: form.state.values.email,
        password: form.state.values.password,
        confirmPassword: form.state.values.confirmPassword,
      };

      // We don't need to mark step 1 as initialized anymore
      // setStep1Initialized(true);

      // Log the saved step 1 values for debugging
      console.log('Saved step 1 values:', {
        ...step1ValuesRef.current,
        password: '[REDACTED]',
        confirmPassword: '[REDACTED]'
      });

      // Create a completely new object for step 2 with ONLY the fields needed for step 2
      // This prevents any data leakage from step 1 to step 2
      const step2InitialValues = {
        // Set default values for step 2 fields only
        gender: 'MALE', // Default gender
        birthday: new Date(), // Default birthday
        address: '', // Empty address
        phone: '', // Empty phone
        schoolName: '', // Empty school name
        enrolledDate: new Date(), // Default enrolled date
        avatar: DEFAULT_AVATAR, // Default avatar
      };

      // First, reset the form to clear any existing values
      // This ensures we don't have any leftover values from step 1
      form.reset();

      // Then update with only the step 2 values
      // Set each field individually with proper typing
      // Use type assertion for gender to ensure it matches the expected type
      form.setFieldValue('gender', step2InitialValues.gender as 'MALE' | 'FEMALE');
      form.setFieldValue('birthday', step2InitialValues.birthday);
      form.setFieldValue('address', step2InitialValues.address);
      form.setFieldValue('phone', step2InitialValues.phone);
      form.setFieldValue('schoolName', step2InitialValues.schoolName);
      form.setFieldValue('enrolledDate', step2InitialValues.enrolledDate);
      form.setFieldValue('avatar', step2InitialValues.avatar);

      // Log the form state after setting values
      console.log('Form state after setting step 2 values:', {
        ...form.state.values,
        password: form.state.values.password ? '[REDACTED]' : 'NOT PRESENT',
        confirmPassword: form.state.values.confirmPassword ? '[REDACTED]' : 'NOT PRESENT'
      });

      // Set the step after validating the form values are correct
      setStep(2);
    }
  }

  const prevStep = () => {
    // Get current step 2 values
    const step2Values = form.state.values;

    // Log the current step 2 values for debugging
    console.log('Current step 2 values before going back:', {
      ...step2Values,
      password: step2Values.password ? '[REDACTED]' : 'NOT PRESENT',
      confirmPassword: step2Values.confirmPassword ? '[REDACTED]' : 'NOT PRESENT'
    });

    // Verify we have the password from step 1
    if (!step1ValuesRef.current.password || step1ValuesRef.current.password.length < 1) {
      console.error('Password is missing from step 1 values in prevStep!');
      toast.error('Password information is missing. Please start over.');
      return;
    }

    // Log the step 1 values to verify they're intact
    console.log('Step 1 values in prevStep:', {
      firstname: step1ValuesRef.current.firstname,
      lastname: step1ValuesRef.current.lastname,
      email: step1ValuesRef.current.email,
      password: '[REDACTED]',
      confirmPassword: '[REDACTED]'
    });

    // Create a completely new object with ONLY step 1 values
    // This ensures we don't carry over any step 2 values that might cause issues
    const restoredValues = {
      // Restore step 1 values from our ref
      firstname: step1ValuesRef.current.firstname,
      lastname: step1ValuesRef.current.lastname,
      email: step1ValuesRef.current.email,
      password: step1ValuesRef.current.password,
      confirmPassword: step1ValuesRef.current.confirmPassword,
    };

    // First, reset the form to clear any existing values
    // This ensures we don't have any leftover values from step 2
    form.reset();

    // Then update with only the step 1 values
    // Set each field individually with proper typing
    form.setFieldValue('firstname', restoredValues.firstname);
    form.setFieldValue('lastname', restoredValues.lastname);
    form.setFieldValue('email', restoredValues.email);
    form.setFieldValue('password', restoredValues.password);
    form.setFieldValue('confirmPassword', restoredValues.confirmPassword);

    // Log the restored values for debugging
    console.log('Restored values when going back to step 1:', {
      ...restoredValues,
      password: '[REDACTED]',
      confirmPassword: '[REDACTED]'
    });

    setStep(1);
  }

  return (
    <div className="flex h-screen mt-[1px]">
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md space-y-8 px-4 mt-[-50px]">
          <UserSay
            blueWord="Create"
            title="ACCOUNT"
            description={`Step ${step} of 2: ${step === 1 ? 'Basic Information' : 'Additional Details'}`}
          />
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()

              // Ensure we're working with the current form state
              const currentFormState = form.state.values;

              // Debug log to check form values at submission time
              console.log('Form submission attempt with values:', {
                ...currentFormState,
                password: currentFormState.password ? '[REDACTED]' : '',
                confirmPassword: currentFormState.confirmPassword ? '[REDACTED]' : ''
              });

              if (step === 1) {
                // Validate step 1 fields before proceeding
                if (validateStep1()) {
                  nextStep();
                }
              } else {
                // For step 2, ensure we're using the correct values from step 1
                // This is a safeguard in case the form state got corrupted
                const step1Values = step1ValuesRef.current;

                // Verify we have the password from step 1
                if (!step1Values.password || step1Values.password.length < 1) {
                  console.error('Password is missing from step 1 values!');
                  toast.error('Password information is missing. Please go back to step 1 and try again.');
                  return;
                }

                // Log the step 1 values to verify they're intact
                console.log('Step 1 values before combining:', {
                  firstname: step1Values.firstname,
                  lastname: step1Values.lastname,
                  email: step1Values.email,
                  password: '[REDACTED]',
                  confirmPassword: '[REDACTED]'
                });

                // Create a clean object for submission with only the fields we need
                // This ensures no cross-contamination between steps
                const combinedValues = {
                  // Step 1 values - personal information
                  firstname: step1Values.firstname,
                  lastname: step1Values.lastname,
                  email: step1Values.email,
                  password: step1Values.password,

                  // Step 2 values - additional details
                  // Get these directly from the current form state
                  gender: currentFormState.gender,
                  birthday: currentFormState.birthday,
                  address: currentFormState.address,
                  phone: currentFormState.phone,
                  schoolName: currentFormState.schoolName,
                  enrolledDate: currentFormState.enrolledDate,
                  avatar: currentFormState.avatar,
                };

                // Instead of updating the form with combined values,
                // we'll directly use the combinedValues for submission
                // This prevents any cross-contamination between steps

                // Create a clean registration data object for submission
                const registrationData = { ...combinedValues };

                // Log the final data being submitted
                console.log('Final registration data being submitted:', {
                  ...registrationData,
                  password: '[REDACTED]'
                });

                // Log the combined values
                console.log('Combined values before final submission:', {
                  ...combinedValues,
                  password: '[REDACTED]',
                  confirmPassword: '[REDACTED]'
                });

                // Instead of manually calling onSubmit, we'll use the form's handleSubmit method
                // This ensures proper type checking and validation
                try {
                  // First, update the form with the combined values
                  // Set each field individually with proper typing
                  form.setFieldValue('firstname', step1ValuesRef.current.firstname);
                  form.setFieldValue('lastname', step1ValuesRef.current.lastname);
                  form.setFieldValue('email', step1ValuesRef.current.email);
                  form.setFieldValue('password', step1ValuesRef.current.password);
                  form.setFieldValue('confirmPassword', step1ValuesRef.current.confirmPassword);

                  // Now call the form's handleSubmit method
                  form.handleSubmit();
                } catch (error) {
                  console.error('Error during form submission:', error);
                  toast.error('An error occurred during form submission');
                }
              }
            }}
            className="space-y-6"
          >
            {step === 1 ? (
              <div className="space-y-4">
                {/* First Name Field */}
                <div>
                  <form.Field
                    name="firstname"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="firstname">First Name</Label>
                        <Input
                          id="firstname"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="First Name"
                          required
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Last Name Field */}
                <div>
                  <form.Field
                    name="lastname"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="lastname">Last Name</Label>
                        <Input
                          id="lastname"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Last Name"
                          required
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Email Field */}
                <div>
                  <form.Field
                    name="email"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          type="email"
                          placeholder="Email"
                          required
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Password Field */}
                <div>
                  <form.Field
                    name="password"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <div className="relative">
                          <Input
                            id="password"
                            name={field.name}
                            value={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type={isPasswordVisible ? 'text' : 'password'}
                            placeholder="Password"
                            required
                          />
                          <button
                            type="button"
                            onClick={togglePasswordVisibility}
                            className="absolute inset-y-0 right-0 flex items-center pr-3"
                          >
                            {isPasswordVisible ? (
                              <EyeOff size={20} />
                            ) : (
                              <Eye size={20} />
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  />
                </div>

                {/* Confirm Password Field */}
                <div>
                  <form.Field
                    name="confirmPassword"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm Password</Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            name={field.name}
                            value={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type={isConfirmPasswordVisible ? 'text' : 'password'}
                            placeholder="Confirm Password"
                            required
                          />
                          <button
                            type="button"
                            onClick={toggleConfirmPasswordVisibility}
                            className="absolute inset-y-0 right-0 flex items-center pr-3"
                          >
                            {isConfirmPasswordVisible ? (
                              <EyeOff size={20} />
                            ) : (
                              <Eye size={20} />
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  />
                </div>

                {/* Next Button */}
                <Button
                  type="submit"
                  className="w-full bg-[#FCD34D] hover:bg-[#F59E0B] text-white"
                >
                  Next Step
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Gender Field */}
                <div>
                  <form.Field
                    name="gender"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="gender">Gender</Label>
                        <Select
                          value={field.state.value}
                          onValueChange={(value: 'MALE' | 'FEMALE') => field.handleChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="MALE">Male</SelectItem>
                            <SelectItem value="FEMALE">Female</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  />
                </div>

                {/* Birthday Field */}
                <div>
                  <form.Field
                    name="birthday"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="birthday">Birthday</Label>
                        <YearMonthPicker
                          date={field.state.value instanceof Date ? field.state.value : new Date()}
                          setDate={(date) => {
                            // Ensure we're setting the birthday field correctly
                            console.log('Setting birthday to:', date);

                            // Only update if we have a valid date
                            if (date && date instanceof Date && !isNaN(date.getTime())) {
                              field.handleChange(date);

                              // Verify the field was updated correctly
                              setTimeout(() => {
                                console.log('Birthday field after update:', field.state.value);
                              }, 0);
                            } else {
                              console.error('Invalid date provided to birthday field:', date);
                            }
                          }}
                          placeholder="Select birthday"
                        />

                        {/* Add a hidden input to ensure the date is properly serialized */}
                        <input
                          type="hidden"
                          id="birthday-hidden"
                          name="birthday-hidden"
                          value={field.state.value instanceof Date ? field.state.value.toISOString() : ''}
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Address Field */}
                <div>
                  <form.Field
                    name="address"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="address">Address</Label>
                        <Input
                          id="address"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Address"
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Phone Field */}
                <div>
                  <form.Field
                    name="phone"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Phone"
                        />
                      </div>
                    )}
                  />
                </div>

                {/* School Name Field */}
                <div>
                  <form.Field
                    name="schoolName"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="schoolName">School Name</Label>
                        <Input
                          id="schoolName"
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="School Name"
                          required
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Enrolled Date Field */}
                <div>
                  <form.Field
                    name="enrolledDate"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="enrolledDate">Enrolled Date</Label>
                        <YearMonthPicker
                          date={field.state.value instanceof Date ? field.state.value : new Date()}
                          setDate={(date) => {
                            // Ensure we're setting the enrolled date field correctly
                            console.log('Setting enrolled date to:', date);

                            // Only update if we have a valid date
                            if (date && date instanceof Date && !isNaN(date.getTime())) {
                              field.handleChange(date);

                              // Verify the field was updated correctly
                              setTimeout(() => {
                                console.log('Enrolled date field after update:', field.state.value);
                              }, 0);
                            } else {
                              console.error('Invalid date provided to enrolled date field:', date);
                            }
                          }}
                          placeholder="Select enrolled date"
                        />

                        {/* Add a hidden input to ensure the date is properly serialized */}
                        <input
                          type="hidden"
                          id="enrolledDate-hidden"
                          name="enrolledDate-hidden"
                          value={field.state.value instanceof Date ? field.state.value.toISOString() : ''}
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Avatar Upload */}
                <div>
                  <form.Field
                    name="avatar"
                    children={(field) => (
                      <div className="space-y-2">
                        <Label htmlFor="avatar">Profile Picture</Label>
                        <FileUploader
                          onFileUploaded={field.handleChange}
                          onError={(error) => {
                            console.error('Upload error:', error)
                            toast.error('Failed to upload profile picture')
                          }}
                          previewUrl={field.state.value}
                          defaultPreview={DEFAULT_AVATAR}
                          isAvatar={true}
                        />
                      </div>
                    )}
                  />
                </div>

                {/* Cloudflare Turnstile */}
                <CloudflareTurnstile
                  ref={captchaRef}
                  onSuccess={(token) => {
                    console.log('Captcha validated successfully')
                    setCaptchaToken(token)
                  }}
                  onError={(error) => {
                    console.error('Captcha validation error:', error)
                    setCaptchaToken(null)
                  }}
                  onExpire={() => {
                    console.warn('Captcha token expired')
                    setCaptchaToken(null)
                  }}
                  className="mt-4 mb-2"
                />

                {/* Error Message */}
                {registrationError && (
                  <div className="text-red-500 text-sm">{registrationError}</div>
                )}

                {/* Navigation Buttons */}
                <div className="flex gap-4">
                  <Button
                    type="button"
                    onClick={prevStep}
                    className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800"
                  >
                    Previous
                  </Button>
                  <Button
                    type="submit"
                    disabled={isPending}
                    className="flex-1 bg-[#FCD34D] hover:bg-[#F59E0B] text-white"
                  >
                    {isPending ? 'Registering...' : 'Register'}
                  </Button>
                </div>
              </div>
            )}

            <div className="text-center mt-4">
              <Link
                to="/login"
                className="inline-flex items-center gap-2 text-sm text-[#F59E0B] hover:text-[#FCD34D] font-medium"
              >
                <ArrowLeft size={16} />
                Back to Login
              </Link>
            </div>
          </form>
        </div>
      </div>

      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/images/auth/banner.png"
          alt="Register"
          className="absolute inset-0 w-full h-full object-cover"
        />
      </div>
    </div>
  )
}
