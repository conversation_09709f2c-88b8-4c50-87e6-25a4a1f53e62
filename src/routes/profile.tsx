import { createFileRoute } from '@tanstack/react-router'
import { ProfileForm } from '@/components/ProfileForm'
import { guardAuthenticated } from '@/lib/auth/routeGuards'

export const Route = createFileRoute('/profile')({
  component: ProfilePage,
  beforeLoad: guardAuthenticated,
})

function ProfilePage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Profile</h1>
        
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
          <ProfileForm />
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
