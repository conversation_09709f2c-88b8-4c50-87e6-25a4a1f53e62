import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { ArrowRight, Check, Calendar, Clock, Users, Building, ChevronLeft } from 'lucide-react'

export const Route = createFileRoute('/demo')({
  component: BookDemo,
})

function BookDemo() {
  const [formData, setFormData] = useState({
    schoolName: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    enrollmentCount: '',
    preferredDate: '',
    preferredTime: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Format data to match the SheetDB API requirements
      const dataToSubmit = {
        data: [
          {
            fullname: `${formData.firstName} ${formData.lastName}`,
            email: formData.email,
            schoolname: formData.schoolName,
            phonenumber: formData.phone,
            enrollmentcount: formData.enrollmentCount,
            preferreddate: formData.preferredDate,
            preferredtime: formData.preferredTime,
            message: formData.message,
            type: 'demo' // Add a type field to distinguish demo requests
          },
        ],
      }

      // Send data to SheetDB API
      const response = await fetch(
        'https://sheetdb.io/api/v1/b81ea9nhoaxhx',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(dataToSubmit),
        }
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit form')
      }

      // Success
      setIsSubmitting(false)
      setIsSubmitted(true)

      // Reset form after 5 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setFormData({
          schoolName: '',
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          enrollmentCount: '',
          preferredDate: '',
          preferredTime: '',
          message: '',
        })
      }, 5000)
    } catch (error) {
      console.error('Submission error:', error)
      alert('Failed to submit form. Please try again.')
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8f9fa] to-[#e9ecef]">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link to="/" className="flex items-center">
            <span className="text-[#525FE1] text-2xl font-bold">Jerid</span>
            <span className="text-gray-900 text-2xl font-bold">School</span>
            <span className="text-primary text-xs ml-1 font-semibold">Alpha</span>
          </Link>

          <Link to="/" className="text-gray-600 hover:text-gray-900 flex items-center">
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back to Home
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Schedule a <span className="text-[#525FE1]">Demo</span> to See JeridSchool in Action
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover how JeridSchool can transform your school operations by scheduling a personalized demo. Experience firsthand how JeridSchool simplifies management and enhances efficiency for administrators, teachers, parents, and students.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-xl overflow-hidden">
            <div className="grid md:grid-cols-2">
              {/* Left side - Form */}
              <div className="p-8 md:p-12">
                {isSubmitted ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                    <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                      <Check className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-2xl font-semibold text-green-800 mb-3">Thank You!</h3>
                    <p className="text-green-700 mb-4">
                      Your demo request has been submitted successfully. We'll be in touch soon to confirm your appointment.
                    </p>
                    <p className="text-green-700">
                      Check your email for a confirmation with all the details.
                    </p>
                  </div>
                ) : (
                  <>
                    <h2 className="text-2xl font-bold mb-6">Book Your Personalized Demo</h2>

                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                            First Name*
                          </label>
                          <input
                            type="text"
                            id="firstName"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleChange}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                            Last Name*
                          </label>
                          <input
                            type="text"
                            id="lastName"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleChange}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <label htmlFor="schoolName" className="block text-sm font-medium text-gray-700 mb-1">
                          School/Institution Name*
                        </label>
                        <input
                          type="text"
                          id="schoolName"
                          name="schoolName"
                          value={formData.schoolName}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                          required
                        />
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                            Email Address*
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                          />
                        </div>
                      </div>

                      <div>
                        <label htmlFor="enrollmentCount" className="block text-sm font-medium text-gray-700 mb-1">
                          Enrollment Size*
                        </label>
                        <select
                          id="enrollmentCount"
                          name="enrollmentCount"
                          value={formData.enrollmentCount}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                          required
                        >
                          <option value="">Select enrollment size</option>
                          <option value="1-100">1-100 students</option>
                          <option value="101-500">101-500 students</option>
                          <option value="501-1000">501-1000 students</option>
                          <option value="1001+">1001+ students</option>
                        </select>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <label htmlFor="preferredDate" className="block text-sm font-medium text-gray-700 mb-1">
                            Preferred Date*
                          </label>
                          <input
                            type="date"
                            id="preferredDate"
                            name="preferredDate"
                            value={formData.preferredDate}
                            onChange={handleChange}
                            min={new Date().toISOString().split('T')[0]}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="preferredTime" className="block text-sm font-medium text-gray-700 mb-1">
                            Preferred Time*
                          </label>
                          <select
                            id="preferredTime"
                            name="preferredTime"
                            value={formData.preferredTime}
                            onChange={handleChange}
                            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                            required
                          >
                            <option value="">Select a time</option>
                            <option value="9:00 AM">9:00 AM</option>
                            <option value="10:00 AM">10:00 AM</option>
                            <option value="11:00 AM">11:00 AM</option>
                            <option value="12:00 PM">12:00 PM</option>
                            <option value="1:00 PM">1:00 PM</option>
                            <option value="2:00 PM">2:00 PM</option>
                            <option value="3:00 PM">3:00 PM</option>
                            <option value="4:00 PM">4:00 PM</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                          Additional Information (Optional)
                        </label>
                        <textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          rows={4}
                          placeholder="Let us know about any specific features you're interested in or questions you have."
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-[#525FE1] focus:border-[#525FE1] outline-none"
                        ></textarea>
                      </div>

                      <div>
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className={`w-full bg-[#525FE1] hover:bg-[#4549c0] text-white px-6 py-3 rounded-md font-medium transition-colors flex items-center justify-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Processing...
                            </>
                          ) : (
                            <>
                              Schedule My Demo <ArrowRight className="ml-2 h-4 w-4" />
                            </>
                          )}
                        </button>
                      </div>

                      <p className="text-xs text-gray-500">
                        By submitting this form, you agree to our <a href="#" className="text-[#525FE1] hover:underline">Terms of Service</a> and <a href="#" className="text-[#525FE1] hover:underline">Privacy Policy</a>.
                      </p>
                    </form>
                  </>
                )}
              </div>

              {/* Right side - Info */}
              <div className="bg-[#525FE1] text-white p-8 md:p-12">
                <h3 className="text-2xl font-bold mb-8">What to Expect in Your Demo</h3>

                <div className="space-y-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                      <Calendar className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-1">30-Minute Personalized Session</h4>
                      <p className="text-white/80">
                        A focused demonstration tailored to your school's specific needs and challenges.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-1">Expert-Led Walkthrough</h4>
                      <p className="text-white/80">
                        Our education specialists will guide you through JeridSchool's features and answer your questions.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                      <Building className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-1">Customized Solutions</h4>
                      <p className="text-white/80">
                        See how JeridSchool can be configured to meet your institution's unique requirements.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                      <Clock className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-1">Implementation Timeline</h4>
                      <p className="text-white/80">
                        Learn about our onboarding process and how quickly you can get up and running.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-12 p-6 bg-white/10 rounded-lg">
                  <h4 className="text-lg font-semibold mb-3">Have Questions?</h4>
                  <p className="mb-4">
                    If you need immediate assistance or have questions before scheduling a demo, our team is here to help.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a href="mailto:<EMAIL>" className="inline-block bg-white text-[#525FE1] px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors text-center">
                      Email Us
                    </a>
                    <a href="tel:+1234567890" className="inline-block bg-transparent border border-white text-white px-4 py-2 rounded-md font-medium hover:bg-white/10 transition-colors text-center">
                      Call Us
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Testimonials */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            {[
              {
                quote: "The demo was incredibly helpful. It showed us exactly how JeridSchool could solve our scheduling challenges.",
                name: "Sarah Johnson",
                title: "Principal, Lincoln High School"
              },
              {
                quote: "I was impressed by how intuitive the platform is. After the demo, we knew this was the solution we needed.",
                name: "Michael Chen",
                title: "IT Director, Westfield Academy"
              },
              {
                quote: "The personalized demo addressed all our concerns about transitioning to a new system. Implementation was smooth.",
                name: "Aisha Patel",
                title: "Administrator, Oakridge Elementary"
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                <div className="mb-4">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className="text-yellow-400">★</span>
                  ))}
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.quote}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-600">{testimonial.title}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-100 mt-20 py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-4">
            <span className="text-[#525FE1] text-xl font-bold">Jerid</span>
            <span className="text-gray-900 text-xl font-bold">School</span>
          </div>
          <p className="text-gray-600 mb-4">Transforming education management with AI-powered tools</p>
          <div className="flex justify-center space-x-4">
            <a href="#" className="text-gray-600 hover:text-gray-900">Terms</a>
            <a href="#" className="text-gray-600 hover:text-gray-900">Privacy</a>
            <a href="#" className="text-gray-600 hover:text-gray-900">Contact</a>
          </div>
          <p className="text-sm text-gray-500 mt-6">© {new Date().getFullYear()} JeridSchool. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default BookDemo
