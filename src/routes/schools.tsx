import { createFileRoute } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { schoolService } from '@/services/schoolService'
import { SchoolData } from '@/routes/school/$schoolName'
import { useTranslation } from 'react-i18next'

export const Route = createFileRoute('/schools')({
  component: AllSchoolsRoute,
})

function AllSchoolsRoute() {
  const { t } = useTranslation()
  // Use React Query to fetch schools data
  const {
    data: schools = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['schools'],
    queryFn: () => schoolService.getAllSchools(),
  })

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4">
          <h2 className="text-xl font-bold mb-2">{t('routes.schools.error.title')}</h2>
          <p>
            {error instanceof Error ? error.message : t('routes.schools.error.unknown')}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">{t('routes.schools.title')}</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {schools.map((school) => (
          <SchoolCard key={school.id} school={school} />
        ))}
      </div>

      <div className="mt-12 bg-gray-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4">
          {t('routes.schools.subdomains.title')}
        </h2>
        <p className="mb-4">
          {t('routes.schools.subdomains.description')}
        </p>
        <ul className="space-y-2 mb-6">
          {schools.map((school) => {
            const subdomain = school.url || school.id.replace('-id', '')
            const currentPort = window.location.port || '5173'
            return (
              <li
                key={`subdomain-${school.id}`}
                className="flex flex-col sm:flex-row sm:items-center"
              >
                <span className="font-semibold mr-2">{school.name}:</span>
                <a
                  href={schoolService.getSchoolUrl(subdomain)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {subdomain}.localhost:{currentPort}
                </a>
              </li>
            )
          })}
        </ul>
        <p className="text-sm text-gray-600">
          {t('routes.schools.subdomains.note')}
        </p>
      </div>
    </div>
  )
}

function SchoolCard({ school }: { school: SchoolData }) {
  const { t } = useTranslation()
  // Extract the subdomain from the school ID or URL
  const subdomain = school.url || school.id.replace('-id', '')

  // Generate the school subdomain URL
  const schoolSubdomainUrl = schoolService.getSchoolUrl(subdomain)

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300">
      <div
        className="h-40 bg-cover bg-center"
        style={{
          backgroundImage: `url(${school.banner})`,
          backgroundColor: school.primaryColor,
        }}
      ></div>

      <div className="p-6">
        <div className="flex items-center mb-4">
          <div
            className="w-12 h-12 rounded-full mr-4 bg-cover bg-center border-2"
            style={{
              backgroundImage: `url(${school.logo})`,
              borderColor: school.primaryColor,
            }}
          ></div>
          <h2 className="text-xl font-bold">{school.name}</h2>
        </div>

        <p className="text-gray-600 mb-4 line-clamp-3">{school.description}</p>

        <div className="mb-4">
          <h3 className="font-semibold mb-2">{t('routes.schools.card.services')}</h3>
          <div className="flex flex-wrap gap-2">
            {school.services?.slice(0, 3).map((service, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs rounded-full"
                style={{
                  backgroundColor: school.secondaryColor,
                  color: school.primaryColor,
                }}
              >
                {service}
              </span>
            ))}
            {(school.services?.length || 0) > 3 && (
              <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                +{(school.services?.length || 0) - 3} {t('routes.schools.card.more')}
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 mt-4">
          <a
            href={schoolSubdomainUrl}
            className="px-4 py-2 bg-gray-100 text-gray-800 rounded text-center hover:bg-gray-200 transition-colors"
          >
            {t('routes.schools.card.buttons.viewDetails')}
          </a>
          <a
            href={schoolSubdomainUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 rounded text-center text-white transition-colors"
            style={{ backgroundColor: school.primaryColor }}
          >
            {t('routes.schools.card.buttons.viewSite')}
          </a>
        </div>
      </div>
    </div>
  )
}
