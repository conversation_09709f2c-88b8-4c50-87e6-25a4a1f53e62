// Simplified version without NestJS dependencies
// These are just type definitions for TypeScript

export interface ThemeDto {
  id: string
  name: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  textColor: string
}

export interface CustomFieldsDto {
  cardLayout?: 'horizontal' | 'vertical'
  showQRCode?: boolean
  additionalFields?: Record<string, any>
  [key: string]: any
}

export interface CardPreferencesDto {
  theme?: ThemeDto
  fontStyle?: string
  additionalInfo?: string
  customFields?: CustomFieldsDto
}

export interface CreateCardDto {
  themeNumber?: number
  colorNumber?: number
  preferences?: CardPreferencesDto
  etablissementId?: string
  frontText: string
  backText: string
  frontImage?: string
  backImage?: string
  deckId: string
}

export interface UpdateCardDto {
  themeNumber?: number
  colorNumber?: number
  preferences?: CardPreferencesDto
  frontText?: string
  backText?: string
  frontImage?: string
  backImage?: string
  forceCreate?: boolean
}
