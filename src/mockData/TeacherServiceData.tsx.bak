import { Database, School, Receipt, Globe } from 'lucide-react'
export const services = [
  {
    icon: <Database className="h-6 w-6" />,
    name: 'Data Storage',
    href: '/super_admin/database',
  },
  {
    icon: <School className="h-6 w-6" />,
    name: 'Etablisments',
    href: '/super_admin/etablisments',
  },

  {
    icon: <Receipt className="h-6 w-6" />,
    name: 'Invoice',
    href: '/super_admin/invoice',
  },
  {
    icon: <Globe className="h-6 w-6" />,
    name: 'Website Management',
    href: '/super_admin/websiteDashboard',
  },
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
