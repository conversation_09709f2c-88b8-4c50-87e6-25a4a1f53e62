interface Subject {
  id: number
  name: string
  passingGrade: number
  examTypes: { id: string; name: string; coefficient: number }[]
  formula: string
}

interface Student {
  id: number
  fullname: string
  subjects: {
    subjectId: number
    grades: { examType: string; value: number }[]
  }[]
}

export const initialSubjects: Subject[] = [
  {
    id: 1,
    name: 'Math',
    passingGrade: 10,
    examTypes: [
      { id: 'ex', name: 'Exam 1', coefficient: 0.4 },
      { id: 'cr', name: 'Control 1', coefficient: 0.2 },
      { id: 'ex2', name: 'Exam 2', coefficient: 0.4 },
    ],
    formula: '(cr + ex*2)/10',
  },
  {
    id: 2,
    name: 'Physics',
    passingGrade: 10,
    examTypes: [
      { id: 'ex', name: 'Exam 1', coefficient: 0.4 },
      { id: 'cr', name: 'Control 1', coefficient: 0.2 },
      { id: 'TP', name: 'TP 1', coefficient: 0.1 },
    ],
    formula: '(cr + TP + cr2 + ex)/10',
  },
]

export const initialStudents: Student[] = [
  {
    id: 1,
    fullname: '<PERSON>',
    subjects: [
      {
        subjectId: 1,
        grades: [
          { examType: 'ex', value: 18 },
          { examType: 'cr', value: 12 },
          { examType: 'ex2', value: 15 },
        ],
      },
      {
        subjectId: 2,
        grades: [
          { examType: 'ex', value: 16 },
          { examType: 'cr', value: 10 },
          { examType: 'TP', value: 14 },
        ],
      },
    ],
  },
]
