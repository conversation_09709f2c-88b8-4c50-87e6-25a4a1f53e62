import {
  Icon,
  School2,
  Presentation,
  ClipboardCheck,
  Brain,
  ClockAlert,
  ListChecks,
  Calendar,
  StickyNote,
  BookOpen,
} from 'lucide-react'
import { fruit } from '@lucide/lab';

export const services = [
  {
    icon: <ClockAlert className="h-6 w-6" />,
    name: 'Attendance List',
    href: '/teacher/attandencehistory',
  },
  {
    icon: <ListChecks className="h-6 w-6" />,
    name: '<PERSON>',
    href: '/teacher/markabsent',
  },
  {
    icon: <School2 className="h-6 w-6" />,
    name: 'Class View',
    href: '/teacher/classes',
  },

  {
    icon: <Calendar className="h-6 w-6" />,
    name: 'timetable',
    href: '/teacher/timetable',
  },
  {
    icon: <ClipboardCheck className="h-6 w-6" />,
    name: 'Quizz Generator',
    href: '/quizz',
  },
  {
    icon: <Presentation className="h-6 w-6" />,
    name: 'Whiteboard',
    href: '/whiteboard',
  },
  {
    icon: <StickyNote className="h-6 w-6" />,
    name: 'My Notes',
    href: '/notes',
  },
  {
    icon: <Icon iconNode={fruit} className="h-6 w-6" />,
    name: 'Focus Room',
    href: '/focus-room',
  },
  {
    icon: <BookOpen className="h-6 w-6" />,
    name: 'Exam',
    href: '/teacher/exam',
  },
  {
    icon: <Brain className="h-6 w-6" />,
    name: 'Auto Corrector',
    href: '#',
    soon:'true'
  },
  {
    icon: <Presentation className="h-6 w-6" />,
    name: 'AI Presentation',
    href: '#',
    soon: true,
  },
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
