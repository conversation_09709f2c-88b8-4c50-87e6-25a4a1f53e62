interface Student {
  id: number
  fullname: string
  subjects: { subjectId: number; absences: number; isEliminated: boolean }[]
}

interface Subject {
  id: number
  name: string
  eliminationThreshold: number
}

export const initialStudents: Student[] = [
  {
    id: 1,
    fullname: 'Johodow',
    subjects: [
      { subjectId: 1, absences: 3, isEliminated: false },
      { subjectId: 2, absences: 1, isEliminated: false },
      { subjectId: 3, absences: 2, isEliminated: false },
    ],
  },
  {
    id: 2,
    fullname: 'Jason',
    subjects: [
      { subjectId: 1, absences: 1, isEliminated: false },
      { subjectId: 2, absences: 1, isEliminated: false },
      { subjectId: 3, absences: 2, isEliminated: false },
    ],
  },
]

export const subjects: Subject[] = [
  { id: 1, name: 'Math', eliminationThreshold: 3 },
  { id: 2, name: 'Physics', eliminationThreshold: 2 },
  { id: 3, name: 'Chemistry', eliminationThreshold: 2 },
]
