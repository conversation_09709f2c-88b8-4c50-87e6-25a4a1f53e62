import { UserX, Calendar, Database, Sigma, ClipboardCheck } from 'lucide-react'
export const services = [
  {
    icon: <Database className="h-6 w-6" />,
    name: 'Data Storage',
    href: '/admin/database',
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    name: 'TimeTable',
    href: '/admin/timetable',
  },
  {
    icon: <ClipboardCheck className="h-6 w-6" />,
    name: 'Attendance',
    href: '/admin/attendance',
  },
  {
    icon: <UserX className="h-6 w-6" />,
    name: 'Elimination',
    href: '#elimination',
  },
  {
    icon: <Sigma className="h-6 w-6" />,
    name: 'Score',
    href: '#score',
  },
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
