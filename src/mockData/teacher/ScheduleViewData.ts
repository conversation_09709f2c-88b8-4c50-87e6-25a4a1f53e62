export const scheduleByDay = {
  Lundi: [
    {
      time: '8h30 à 10h00',
      subject: 'Environnement de développement',
      teacher: '<PERSON><PERSON>HOUMA',
      salle: 'LI 06',
      class: 'DSI 31',
    },
    {
      time: '10h10 à 11h40',
      subject: 'Environnement de développement',
      teacher: '<PERSON><PERSON>',
      salle: 'LI 06',
      class: 'DSI 31',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Environnement de développement',
      teacher: '<PERSON><PERSON>HOUM<PERSON>',
      salle: 'LI 06',
      class: 'DSI 31',
    },
    {
      time: '14h30 à 16h00',
      subject: 'Web 3.0',
      teacher: '<PERSON><PERSON>',
      salle: 'LI 06',
      class: 'DSI 31',
    },
    {
      time: '16h10 à 17h40',
      subject: 'web 3.0',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      class: 'DSI 31',
    },
  ],
  Mardi: [
    {
      time: '8h30 à 10h00',
      subject: 'Environnement de développement',
      teacher: '<PERSON><PERSON>',
      salle: 'LI 06',
      class: 'DSI 32',
    },
    {
      time: '10h10 à 11h40',
      subject: 'Environnement de développement',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      class: 'DSI 32',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Environnement de développement',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      class: 'DSI 32',
    },
    {
      time: '14h30 à 16h00',
      subject: 'Web 3.0',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      class: 'DSI 32',
    },
    {
      time: '16h10 à 17h40',
      subject: 'Web 3.0',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      class: 'DSI 32',
    },
  ],
  Mercredi: [],
  Jeudi: [],
  Vendredi: [],
  Samedi: [],
}

export const timeSlots = [
  '8h30 à 10h00',
  '10h10 à 11h40',
  '11h50 à 13h20',
  '14h30 à 16h00',
  '16h10 à 17h40',
]

export const teachers = ['Maher RHOUMA']

export const salles = ['SI 01', 'SI 02', 'SI 03', 'LG 04', 'LI 06']

export const days = [
  'Lundi',
  'Mardi',
  'Mercredi',
  'Jeudi',
  'Vendredi',
  'Samedi',
]
