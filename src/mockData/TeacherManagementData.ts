import { Teacher } from '../interface/Teacherinterface'

export const mockTeachers: Teacher[] = Array.from(
  { length: 50 },
  (_, index) => ({
    id: index + 1, // Changed to number to match the interface
    cin: `CIN${(index + 1).toString().padStart(5, '0')}`,
    email: `teacher${index + 1}@example.com`,
    fullname: `Teacher${index + 1} Lastname${index + 1}`, // Combined name and lastname
    absences: [
      { date: '2024-12-20', reason: 'Sick leave' },
      { date: '2024-12-15', reason: 'Personal' },
    ],
  })
)
