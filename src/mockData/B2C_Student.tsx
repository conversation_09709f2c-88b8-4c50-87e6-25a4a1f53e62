import {
  Icon,
  BookMarked,
  ClipboardCheck,
  Presentation,
  StickyNote,
  Layers,
} from 'lucide-react'
import { fruit } from '@lucide/lab';

export const services = [
  {
    icon: <Layers className="h-6 w-6" />,
    name: 'All in One',
    href: '/student/all-in-one',
  },
  {
    icon: <BookMarked className="h-6 w-6" />,
    name: 'Flashcards',
    href: '/student/flashcards/',
  },
  {
    icon: <Presentation className="h-6 w-6" />,
    name: 'White Board',
    href: '/whiteboard',
  },
  {
    icon: <ClipboardCheck className="h-6 w-6" />,
    name: 'Quizz Generator',
    href: '/quizz',
  },

  {
    icon: <StickyNote className="h-6 w-6" />,
    name: 'My Notes',
    href: '/notes',
  },
  {
    icon: <Icon iconNode={fruit} className="h-6 w-6" />,
    name: 'Focus Room',
    href: '/focus-room',
  },
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
