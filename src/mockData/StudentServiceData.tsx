import {
  Icon,
  BookMarked,
  MessageSquare,
  ClipboardCheck,
  Presentation,
  Calendar,
  StickyNote,
  Box,
  BookOpen,
} from 'lucide-react'
import { fruit } from '@lucide/lab';

export const services = [
  {
    icon: <MessageSquare className="h-6 w-6" />,
    name: 'Cha<PERSON>',
    href: '/chat',
  },
  {
    icon: <ClipboardCheck className="h-6 w-6" />,
    name: 'Attendance',
    href: '/student/attendance',
  },
  {
    icon: <BookMarked className="h-6 w-6" />,
    name: 'Flashcards',
    href: '/student/flashcards/',
  },
  {
    icon: <Presentation className="h-6 w-6" />,
    name: 'White Board',
    href: '/whiteboard',
  },
  {
    icon: <ClipboardCheck className="h-6 w-6" />,
    name: 'Quizz Generator',
    href: '/quizz',
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    name: 'Schedule View',
    href: '/student/timetable',
  },
  {
    icon: <StickyNote className="h-6 w-6" />,
    name: 'My Notes',
    href: '/notes',
  },
  {
    icon: <Icon iconNode={fruit} className="h-6 w-6" />,
    name: 'Focus Room',
    href: '/focus-room',
  },
  {
    icon: <Box className="h-6 w-6" />,
    name: 'Virtual Classroom',
    href: '/student/virtual-classroom',
  
  },
  {
    icon: <BookOpen className="h-6 w-6" />,
    name: 'Exam',
    href: '/student/exam',
  }
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
