// No longer importing Class since we're using our own MockClass interface

// Define a custom interface for our mock classes that includes subject
interface MockClass {
  id: number
  name: string
  subject: string
}

export const classes: MockClass[] = [
  { id: 1, name: 'DSI 31', subject: 'web 3.0' },
  { id: 2, name: 'DSI 31', subject: 'web 3.0' },
  { id: 3, name: 'DSI 32', subject: 'dev ENV' },
  { id: 4, name: 'DSI 32', subject: 'dev ENV' },
]

// Define a custom interface for our mock class details
interface MockClassDetails {
  [key: number]: {
    teachers: Array<{
      id: number
      fullname: string
      subject: string
      cin: string
      email: string
      class: string
    }>
    students: Array<{
      id: number
      fullname: string
      cin: string
      email: string
      classId: string
      subjects: any[]
    }>
  }
}

export const classDetails: MockClassDetails = {
  1: {
    teachers: [
      {
        id: 1,
        fullname: 'Mr<PERSON> <PERSON>',
        subject: 'Math',
        cin: 'T12345',
        email: '<EMAIL>',
        class: 'Class A',
      },
      {
        id: 2,
        fullname: 'Mrs. <PERSON>',
        subject: 'Science',
        cin: 'T23456',
        email: '<EMAIL>',
        class: 'Class A',
      },
    ],
    students: [
      {
        id: 1,
        fullname: '<PERSON>',
        cin: 'S12345',
        email: '<EMAIL>',
        classId: 'Class A',
        subjects: [],
      },
      {
        id: 2,
        fullname: 'Emma Davis',
        cin: 'S23456',
        email: '<EMAIL>',
        classId: 'Class A',
        subjects: [],
      },
    ],
  },
  2: {
    teachers: [
      {
        id: 3,
        fullname: 'Mr. Clark',
        subject: 'History',
        cin: 'T34567',
        email: '<EMAIL>',
        class: 'Class B',
      },
    ],
    students: [
      {
        id: 3,
        fullname: 'Michael Wilson',
        cin: 'S34567',
        email: '<EMAIL>',
        classId: 'Class B',
        subjects: [],
      },
      {
        id: 4,
        fullname: 'Sarah Johnson',
        cin: 'S45678',
        email: '<EMAIL>',
        classId: 'Class B',
        subjects: [],
      },
    ],
  },
}
