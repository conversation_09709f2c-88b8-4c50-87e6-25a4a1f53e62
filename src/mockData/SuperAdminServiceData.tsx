import { Database, School, Receipt, FileText } from 'lucide-react'
export const services = [
  {
    icon: <Database className="h-6 w-6" />,
    name: 'Data Storage',
    href: '/super_admin/database',
    soon: false,
  },
  {
    icon: <School className="h-6 w-6" />,
    name: 'Establishments',
    href: '/super_admin/etablisments',
    soon: false,
  },

  {
    icon: <Receipt className="h-6 w-6" />,
    name: 'Invoice',
    href: '#',
    soon: true,
  },
  {
    icon: <FileText className="h-6 w-6" />,
    name: 'Smart Rapport',
    href: '#',
    soon: true,
  },
]

export const resources = [
  { name: 'quiz-app-api', type: 'App Service', lastViewed: '3 weeks ago' },
  { name: 'quiz-backend', type: 'App Service', lastViewed: '4 weeks ago' },
  { name: 'quiz-storage', type: 'Resource group', lastViewed: 'a month ago' },
]
