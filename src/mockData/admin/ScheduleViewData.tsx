// export const days = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'];

// export const scheduleData = {
//   'Lundi': [
//     { time: '8h30 à 10h00', subject: "Web 3.0", teacher: "<PERSON><PERSON>", salle: "SI 03" },
//     { time: '10h10 à 11h40', subject: "SOA", teacher: "<PERSON><PERSON><PERSON>", salle: "LG 04" },
//     { time: '11h50 à 13h20', subject: "Atelier SOA", teacher: "<PERSON>Z<PERSON>O<PERSON>", salle: "LI 06" },
//     { time: '14h30 à 16h00', subject: "Atelier Framework cross-platform", teacher: "<PERSON>", salle: "SI 02" }
//   ],
//   'Mardi': [
//     { time: '8h30 à 10h00', subject: "Méthodologie de Conception Objet", teacher: "<PERSON><PERSON>", salle: "SI 03" },
//     { time: '10h10 à 11h40', subject: "Projet d'Intégration", teacher: "<PERSON><PERSON>", salle: "LG 04" },
//     { time: '11h50 à 13h20', subject: "Atelier Base de Données Avancée", teacher: "Rayen BEN SALAH", salle: "LI 06" },
//     { time: '14h30 à 16h00', subject: "Environnement de développement", teacher: "Maher RHOUMA", salle: "LI 06" }
//   ],
//   'Mercredi': [
//     { time: '8h30 à 10h00', subject: "Gestion des données Massives", teacher: "Rayen BEN SALAH", salle: "LG 04" },
//     { time: '10h10 à 11h40', subject: "SOA", teacher: "Wahid HAMDI", salle: "LI 06" },
//     { time: '11h50 à 13h20', subject: "Atelier développement Mobile natif", teacher: "Maher RHOUMA", salle: "LI 06" },
//     { time: '14h30 à 16h00', subject: "Atelier Base de Données Avancée", teacher: "Mohamed TOUMI", salle: "LI 06" }
//   ],
//   'Jeudi': [
//     { time: '8h30 à 10h00', subject: "Preparing TOEIC", teacher: "Dziriya ARFAOUI", salle: "SI 01" },
//     { time: '10h10 à 11h40', subject: "Projet d'Intégration", teacher: "Haithem HAFSI", salle: "LI 06" },
//     { time: '11h50 à 13h20', subject: "Technique de recherche d'emploi et marketing de soi", teacher: "Rayen BEN SALAH", salle: "LI 06" },
//     { time: '14h30 à 16h00', subject: "Environnement de développement", teacher: "Maher RHOUMA", salle: "LI 06" }
//   ],
//   'Vendredi': [
//     { time: '8h30 à 10h00', subject: "Preparing TOEIC", teacher: "Dziriya ARFAOUI", salle: "SI 01" },
//     { time: '10h10 à 11h40', subject: "Atelier SOA", teacher: "Rayen BEN SALAH", salle: "LG 04" },
//     { time: '11h50 à 13h20', subject: "Atelier développement Mobile natif", teacher: "Maher RHOUMA", salle: "LI 06" }
//   ]
// };

// export const timeSlots = [
//   '8h30 à 10h00',
//   '10h10 à 11h40',
//   '11h50 à 13h20',
//   '14h30 à 16h00',
//   '16h10 à 17h40',
// ];

// export  const teachers = [
//   "Mariem JERIDI", "Wahid HAMDI", "Ahmed NEFZAOUI", "Mohamed TOUMI", "Hamed BENNEJI", "Rayen BEN SALAH", "Maher RHOUMA", "Dziriya ARFAOUI", "Haithem HAFSI", "Soufiene BEN MAHMOUD"
// ];

// export const salles = [
//   "SI 01", "SI 02", "SI 03", "LG 04", "LI 06"
// ];

// export const days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];

// export const timeSlots = [
//   '8h30 à 10h00',
//   '10h10 à 11h40',
//   '11h50 à 13h20',
//   '14h30 à 16h00',
//   '16h10 à 17h40',
// ];

// export const teachers = [
//   "Mariem JERIDI", "Wahid HAMDI", "Ahmed NEFZAOUI", "Mohamed TOUMI", "Hamed BENNEJI",
//   "Rayen BEN SALAH", "Maher RHOUMA", "Dziriya ARFAOUI", "Haithem HAFSI", "Soufiene BEN MAHMOUD"
// ];

// export const salles = [
//   "SI 01", "SI 02", "SI 03", "LG 04", "LI 06"
// ];

// type ScheduleEntry = {
//   time: string;
//   subject: string;
//   teacher: string;
//   salle: string;
// };

// type DaySchedule = ScheduleEntry[];

// type WeekSchedule = {
//   [key: string]: DaySchedule;
// };

// export const scheduleData: {
//   students: { [key: string]: WeekSchedule };
//   teachers: { [key: string]: WeekSchedule };
//   classrooms: { [key: string]: WeekSchedule };
// } = {
//   students: {
//     "DSI 31": {
//       'Lundi': [
//         { time: '8h30 à 10h00', subject: "Web 3.0", teacher: "Mariem JERIDI", salle: "SI 03" },
//         { time: '10h10 à 11h40', subject: "SOA", teacher: "Wahid HAMDI", salle: "LG 04" },
//         { time: '11h50 à 13h20', subject: "Atelier SOA", teacher: "Ahmed NEFZAOUI", salle: "LI 06" },
//         { time: '14h30 à 16h00', subject: "Atelier Framework cross-platform", teacher: "Mohamed TOUMI", salle: "SI 02" }
//       ],
//       'Mardi': [
//         { time: '8h30 à 10h00', subject: "Méthodologie de Conception Objet", teacher: "Mariem JERIDI", salle: "SI 03" },
//         { time: '10h10 à 11h40', subject: "Projet d'Intégration", teacher: "Hamed BENNEJI", salle: "LG 04" },
//         { time: '11h50 à 13h20', subject: "Atelier Base de Données Avancée", teacher: "Rayen BEN SALAH", salle: "LI 06" },
//         { time: '14h30 à 16h00', subject: "Environnement de développement", teacher: "Maher RHOUMA", salle: "LI 06" }
//       ],
//       'Mercredi': [
//         { time: '8h30 à 10h00', subject: "Gestion des données Massives", teacher: "Rayen BEN SALAH", salle: "LG 04" },
//         { time: '10h10 à 11h40', subject: "SOA", teacher: "Wahid HAMDI", salle: "LI 06" },
//         { time: '11h50 à 13h20', subject: "Atelier développement Mobile natif", teacher: "Maher RHOUMA", salle: "LI 06" },
//         { time: '14h30 à 16h00', subject: "Atelier Base de Données Avancée", teacher: "Mohamed TOUMI", salle: "LI 06" }
//       ],
//       'Jeudi': [
//         { time: '8h30 à 10h00', subject: "Preparing TOEIC", teacher: "Dziriya ARFAOUI", salle: "SI 01" },
//         { time: '10h10 à 11h40', subject: "Projet d'Intégration", teacher: "Haithem HAFSI", salle: "LI 06" },
//         { time: '11h50 à 13h20', subject: "Technique de recherche d'emploi et marketing de soi", teacher: "Rayen BEN SALAH", salle: "LI 06" },
//         { time: '14h30 à 16h00', subject: "Environnement de développement", teacher: "Maher RHOUMA", salle: "LI 06" }
//       ],
//       'Vendredi': [
//         { time: '8h30 à 10h00', subject: "Preparing TOEIC", teacher: "Dziriya ARFAOUI", salle: "SI 01" },
//         { time: '10h10 à 11h40', subject: "Atelier SOA", teacher: "Rayen BEN SALAH", salle: "LG 04" },
//         { time: '11h50 à 13h20', subject: "Atelier développement Mobile natif", teacher: "Maher RHOUMA", salle: "LI 06" }
//       ],
//       'Samedi': []
//     }
//   },
//   teachers: {
//     "Mariem JERIDI": {
//       'Lundi': [
//         { time: '8h30 à 10h00', subject: "Web 3.0", teacher: "Mariem JERIDI", salle: "SI 03" }
//       ],
//       'Mardi': [
//         { time: '8h30 à 10h00', subject: "Méthodologie de Conception Objet", teacher: "Mariem JERIDI", salle: "SI 03" }
//       ],
//       'Mercredi': [],
//       'Jeudi': [],
//       'Vendredi': [],
//       'Samedi': []
//     },
//     // Add other teachers' schedules here
//   },
//   classrooms: {
//     "SI 03": {
//       'Lundi': [
//         { time: '8h30 à 10h00', subject: "Web 3.0", teacher: "Mariem JERIDI", salle: "SI 03" }
//       ],
//       'Mardi': [
//         { time: '8h30 à 10h00', subject: "Méthodologie de Conception Objet", teacher: "Mariem JERIDI", salle: "SI 03" }
//       ],
//       'Mercredi': [],
//       'Jeudi': [],
//       'Vendredi': [],
//       'Samedi': []
//     },
//     // Add other classrooms' schedules here
//   }
// };

// Define interfaces for the by-group feature
export interface CourseGroup {
  subject: string
  teacher: string
  teacherID?: number
  salle: string
  groupNumber?: number
}

export interface Course {
  time: string
  subject: string
  teacher: string
  teacherID?: number
  salle: string
  // New fields for group division
  divide?: number[]
  isGrouped?: boolean
  group1?: CourseGroup
  group2?: CourseGroup
  // New fields for alternating subjects
  alternateWith?: string
  alternateWithTeacher?: string
  alternateWithTeacherID?: number
  alternationPattern?: 'weekly' | 'biweekly'
}

export const scheduleByDay = {
  Lundi: [
    {
      time: '8h30 à 10h00',
      subject: 'Web 3.0',
      teacher: 'Mariem JERIDI',
      salle: 'SI 03',
    },
    {
      time: '10h10 à 11h40',
      subject: 'SOA',
      teacher: 'Wahid HAMDI',
      salle: 'LG 04',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Atelier SOA',
      teacher: 'Ahmed NEFZAOUI',
      salle: 'LI 06',
      // Example of a divided subject with groups
      divide: [2, 2],
      isGrouped: true,
      group1: {
        subject: 'Atelier SOA - Group 1',
        teacher: 'Ahmed NEFZAOUI',
        teacherID: 3,
        salle: 'LI 06',
        groupNumber: 1,
      },
      group2: {
        subject: 'Atelier SOA - Group 2',
        teacher: 'Rayen BEN SALAH',
        teacherID: 6,
        salle: 'LG 04',
        groupNumber: 2,
      },
    },
    {
      time: '14h30 à 16h00',
      subject: 'Atelier Framework cross-platform',
      teacher: 'Mohamed TOUMI',
      salle: 'SI 02',
    },
  ],
  Mardi: [
    {
      time: '8h30 à 10h00',
      subject: 'Méthodologie de Conception Objet',
      teacher: 'Mariem JERIDI',
      salle: 'SI 03',
    },
    {
      time: '10h10 à 11h40',
      subject: "Projet d'Intégration",
      teacher: 'Hamed BENNEJI',
      salle: 'LG 04',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Atelier Base de Données Avancée',
      teacher: 'Rayen BEN SALAH',
      salle: 'LI 06',
    },
    {
      time: '14h30 à 16h00',
      subject: 'Environnement de développement',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
    },
  ],
  Mercredi: [
    {
      time: '8h30 à 10h00',
      subject: 'Gestion des données Massives',
      teacher: 'Rayen BEN SALAH',
      salle: 'LG 04',
    },
    {
      time: '10h10 à 11h40',
      subject: 'SOA',
      teacher: 'Wahid HAMDI',
      salle: 'LI 06',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Atelier développement Mobile natif',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
      // Example of a divided subject with groups
      divide: [2, 1],
      isGrouped: true,
      group1: {
        subject: 'Mobile Dev - Group 1',
        teacher: 'Maher RHOUMA',
        teacherID: 7,
        salle: 'LI 06',
        groupNumber: 1,
      },
      group2: {
        subject: 'Mobile Dev - Group 2',
        teacher: 'Mohamed TOUMI',
        teacherID: 4,
        salle: 'SI 02',
        groupNumber: 2,
      },
    },
    {
      time: '14h30 à 16h00',
      subject: 'Atelier Base de Données Avancée',
      teacher: 'Mohamed TOUMI',
      salle: 'LI 06',
    },
  ],
  Jeudi: [
    {
      time: '8h30 à 10h00',
      subject: 'Preparing TOEIC',
      teacher: 'Dziriya ARFAOUI',
      salle: 'SI 01',
    },
    {
      time: '10h10 à 11h40',
      subject: "Projet d'Intégration",
      teacher: 'Haithem HAFSI',
      salle: 'LI 06',
    },
    {
      time: '11h50 à 13h20',
      subject: "Technique de recherche d'emploi et marketing de soi",
      teacher: 'Rayen BEN SALAH',
      salle: 'LI 06',
    },
    {
      time: '14h30 à 16h00',
      subject: 'Environnement de développement',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
    },
  ],
  Vendredi: [
    {
      time: '8h30 à 10h00',
      subject: 'Preparing TOEIC',
      teacher: 'Dziriya ARFAOUI',
      salle: 'SI 01',
    },
    {
      time: '10h10 à 11h40',
      subject: 'Art',
      teacher: 'Teacher 9',
      teacherID: 9,
      salle: 'SI 03',
      // Example of alternating subjects
      alternateWith: 'Music',
      alternateWithTeacher: 'Teacher 14',
      alternateWithTeacherID: 14,
      alternationPattern: 'weekly',
    },
    {
      time: '11h50 à 13h20',
      subject: 'Atelier développement Mobile natif',
      teacher: 'Maher RHOUMA',
      salle: 'LI 06',
    },
  ],
}

export const timeSlots = [
  '8h30 à 10h00',
  '10h10 à 11h40',
  '11h50 à 13h20',
  '14h30 à 16h00',
  '16h10 à 17h40',
]

export const teachers = [
  'Mariem JERIDI',
  'Wahid HAMDI',
  'Ahmed NEFZAOUI',
  'Mohamed TOUMI',
  'Hamed BENNEJI',
  'Rayen BEN SALAH',
  'Maher RHOUMA',
  'Dziriya ARFAOUI',
  'Haithem HAFSI',
  'Soufiene BEN MAHMOUD',
]

export const salles = ['SI 01', 'SI 02', 'SI 03', 'LG 04', 'LI 06']

export const days = [
  'Lundi',
  'Mardi',
  'Mercredi',
  'Jeudi',
  'Vendredi',
  'Samedi',
]
