export interface Student {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  dateOfBirth: string
  grades: Grade[]
  isPaid: boolean
}

export interface Grade {
  id: string
  studentId: string
  subjectId: string
  score: number
  examType: 'final' | 'midterm' | 'quiz'
  date: string
}

export interface Subject {
  id: string
  name: string
  description: string
  formula: string
}
