import { Teacher } from './Teacherinterface'

// Base interfaces
export interface Parent {
  id: number
  cin: string
  fullname: string
  email?: string
  password?: string
  role?: string
  isActive?: boolean
  studentIds: string[]
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

export interface Student {
  id: number
  fullname: string
  cin?: number
  email?: string
  class?: string
  subjects?: (StudentSubject | SubjectGrades)[]
  isPassed?: boolean
}

export interface Subject {
  id: number
  name: string
  eliminationThreshold?: number
  passingGrade?: number
  examTypes?: ExamType[]
  formula?: string
}

export interface Class {
  id: number
  name: string
  students?: Student[]
}

// Attendance tracking
export interface Absent {
  id: number
  studentId: number
  subject: string
  date: string
  reason: string
}

export interface StudentSubject {
  subjectId: number
  absences: number
  isEliminated?: boolean
}

// Grading system
export interface ExamType {
  id: string
  name: string
  coefficient: number
}

export interface Grade {
  examType: string
  value: number
}

export interface SubjectGrades {
  subjectId: number
  grades: Grade[]
}

// Utility types
export interface ClassDetails {
  [key: number]: {
    teachers?: Teacher[]
    students: Student[]
  }
}

export interface ProfileFormValues {
  fullname: string
  cin: string
  email: string
  class?: string
  subject?: string
}

// Parent-specific types
export interface ParentFormValues {
  cin: string
  firstname: string
  lastname: string
  email: string
  password: string
  studentIds: string[] // Array of student IDs associated with the parent
}

export interface ParentDetails {
  id: number
  fullname: string
  cin?: number
  email?: string
  students?: Student[] // Array of students associated with the parent
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
}
