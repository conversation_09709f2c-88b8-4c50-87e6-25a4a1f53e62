export interface Absence {
  date: string
  reason: string
}

export interface Teacher {
  id?: number
  fullname: string
  cin: string
  email: string
  password?: string
  class?: string
  subject?: string
  title?: string
  absences?: Absence[]
  subjectIds?: string[] // Optional, array of subject IDs
  role?: string // Optional, e.g., "Teacher"
  isActive?: boolean // Optional, e.g., true/false
  createdAt?: string // Optional, timestamp
  updatedAt?: string // Optional, timestamp
  createdBy?: string // Optional, ID of the user who created the teacher
  updatedBy?: string // Optional, ID of the user who updated the teacher
}

export interface ViewAbsentProps {
  onBack?: () => void
  onSearch?: () => void
}

export interface SearchFormValues {
  cin: string
  name: string
}

export interface ProfileFormValues {
  fullname: string
  email: string
  cin: string
}

export interface AbsenceFormValues {
  date: string
  reason: string
}

export interface SearchFormValues {
  cin: string // CIN to search for
  name: string // Name to search for
}

export interface ViewAbsentProps {
  onBack?: () => void // Callback function to navigate back to the previous screen
}

export interface Course {
  time: string
  subject: string
  teacher: string
  salle: string
  class?: string // Optional, depending on your data
}
