// Re-exports from central type system
import type { Student, Subject, StudentSubject, Grade, Score } from './types'

export type { Student, Subject, StudentSubject, Grade, Score }

/**
 * Type utilities for components that need more flexibility than the strict backend types
 */

/**
 * A utility type that accepts both string and number for ID fields
 */
export type ID = string | number

/**
 * A simpler StatusBadge type for UI components
 */
export type StatusType =
  | 'pending'
  | 'approved'
  | 'rejected'
  | 'submitted'
  | 'late'

/**
 * A more flexible type for handling null checks in components
 */
export type Nullable<T> = T | null | undefined

/**
 * Convert a strict object type to a more flexible one for UI components
 * Makes all properties optional and allows string or number for id fields
 */
export type UIFriendly<T> = {
  [K in keyof T]?: T[K] extends string ? string | number : T[K]
}

/**
 * Helper functions for null safety
 */
export const safeString = (value: any): string => {
  if (value === null || value === undefined) return ''
  return String(value)
}

export const safeNumber = (value: any): number => {
  if (value === null || value === undefined) return 0
  const num = Number(value)
  return isNaN(num) ? 0 : num
}

export const safeArray = <T>(value: any): T[] => {
  if (Array.isArray(value)) return value
  return []
}

/**
 * Helper for password updates
 * This addresses the error with updateUserPassword expecting 1 arg but getting 2
 */
export const updatePasswordWrapper = (updateFn: any) => {
  return (password: string) => {
    // Call the update function with just the password
    // The JWT should be obtained within the function
    return updateFn(password)
  }
}
