// Re-exports from central type system
import type {
  Student,
  Subject,
  StudentSubject,
  Grade,
  Class,
  Schedule,
  Course,
  Absence,
} from './types'

export type {
  Student,
  Subject,
  StudentSubject,
  Grade,
  Class,
  Schedule,
  Course,
  Absence,
}

// Types needed for specific components
export interface ClassDetails {
  id: string
  name: string
  students: Student[]
  subjects: Subject[]
}

// Student-related interfaces for use in components
import type {
  StudentSubject as BaseStudentSubject,
  Student as BaseStudent,
} from './types'

/**
 * Extended Absent interface with additional fields needed by components
 */
export interface Absent {
  id: string | number
  date: string
  reason: string
  studentId?: string | number
  teacherId?: string | number
  subjectId?: string | number
  subject?: string // For convenience in UI
  status: 'pending' | 'approved' | 'rejected'
  notes?: string
  teacherName?: string
  lesson?: string
}

/**
 * A mock StudentSubject for use in UI components
 */
export interface MockStudentSubject extends Partial<BaseStudentSubject> {
  id?: string | number
  name?: string
  totalAbsences?: number
  absences?: number | Array<Absent>
}

/**
 * Helper to ensure a Student object has grades array
 */
export function ensureStudentHasGrades(
  student: Partial<BaseStudent>
): BaseStudent {
  return {
    id: student.id || '0',
    firstname: student.firstname || '',
    lastname: student.lastname || '',
    email: student.email || '',
    cin: student.cin || '',
    role: 'student',
    isActive: student.isActive || true,
    isPaid: student.isPaid || true,
    gradeLevel: student.gradeLevel || '',
    enrolledDate: student.enrolledDate || new Date().toISOString(),
    createdAt: student.createdAt || new Date().toISOString(),
    updatedAt: student.updatedAt || new Date().toISOString(),
    createdBy: student.createdBy || 'system',
    updatedBy: student.updatedBy || 'system',
    subjects: student.subjects || [],
    ...student,
  }
}
