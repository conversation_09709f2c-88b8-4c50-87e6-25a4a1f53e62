// Re-exports from central type system for compatibility with existing imports
import type { Student as CoreStudent, Subject as CoreSubject } from './types'
export type { CoreStudent as Student, CoreSubject as Subject }

// Local simplified versions for mock data
interface MockStudent {
  id: number
  fullname: string
  subjects: { subjectId: number; absences: number; isEliminated: boolean }[]
}

interface MockSubject {
  id: number
  name: string
  eliminationThreshold: number
}

export const initialStudents: MockStudent[] = [
  {
    id: 1,
    fullname: '<PERSON><PERSON>do<PERSON>',
    subjects: [
      { subjectId: 1, absences: 3, isEliminated: false },
      { subjectId: 2, absences: 1, isEliminated: false },
      { subjectId: 3, absences: 2, isEliminated: false },
    ],
  },
  {
    id: 2,
    fullname: '<PERSON>',
    subjects: [
      { subjectId: 1, absences: 1, isEliminated: false },
      { subjectId: 2, absences: 1, isEliminated: false },
      { subjectId: 3, absences: 2, isEliminated: false },
    ],
  },
]

export const subjects: MockSubject[] = [
  { id: 1, name: 'Math', eliminationThreshold: 3 },
  { id: 2, name: 'Physics', eliminationThreshold: 2 },
  { id: 3, name: 'Chemistry', eliminationThreshold: 2 },
]

import type { Student, StudentSubject } from './types'

/**
 * Specialized Student type for the ListEliminate component
 * This model is more lenient with id types and required fields
 */
export interface EliminateStudent {
  id: string | number
  firstname: string
  lastname: string
  fullname?: string
  cin?: string
  subjects?: EliminateSubject[]
  [key: string]: any // Allow additional properties
}

/**
 * Specialized Subject type for the ListEliminate component
 */
export interface EliminateSubject {
  id?: string | number
  subjectId: string | number
  name?: string
  absences?: number
  isEliminated?: boolean
  [key: string]: any // Allow additional properties
}

/**
 * Converts a Student object to an EliminateStudent
 */
export function toEliminateStudent(
  student: Partial<Student>
): EliminateStudent {
  return {
    id: student.id || '0',
    firstname: student.firstname || '',
    lastname: student.lastname || '',
    fullname: `${student.firstname || ''} ${student.lastname || ''}`.trim(),
    cin: student.cin,
    subjects: student.subjects?.map(toEliminateSubject) || [],
  }
}

/**
 * Converts a StudentSubject to an EliminateSubject
 */
export function toEliminateSubject(
  subject: Partial<StudentSubject>
): EliminateSubject {
  return {
    subjectId: subject.subjectId || '0',
    absences: typeof subject.absences === 'number' ? subject.absences : 0,
    isEliminated: subject.isEliminated || false,
  }
}

/**
 * A mapping from subject IDs to elimination thresholds
 */
export interface EliminationThresholds {
  [subjectId: string]: number
}
