import { Class } from './academic'

export interface Grade {
  id: string
  name: string
  classes?: Class[]
  createdAt?: Date
  updatedAt?: Date
  createdBy?: string
  updatedBy?: string
  establishmentId?: string
  etablissementId?: string // Adding this field as the API returns it
}

export interface CreateGradeDTO {
  name: string
  establishmentId?: string
  etablissementId?: string // Adding this field as the API expects it
  createdBy?: string
}

export interface UpdateGradeDTO {
  name?: string
  establishmentId?: string
  etablissementId?: string // Adding this field as the API expects it
  updatedBy?: string
}
