import { BaseEntity, BaseCreateDTO, BaseUpdateDTO } from './base'

/**
 * Classroom interfaces
 */
export interface Classroom extends BaseEntity {
  id: string // Ensure id is always a string
  name: string
  type: string
  capacity: number
  floor: string
  building: string
  features: string[]
  notes?: string
  isActive: boolean
  etablissementId: string
}

export interface CreateClassroomDTO extends BaseCreateDTO {
  name: string
  type: string
  capacity: number
  floor: string
  building: string
  features: string[]
  notes?: string
  isActive: boolean
  etablissementId: string
}

export interface UpdateClassroomDTO extends BaseUpdateDTO {
  name?: string
  type?: string
  capacity?: number
  floor?: string
  building?: string
  features?: string[]
  notes?: string
  isActive?: boolean
  etablissementId?: string
}

// Response can be either an array of classrooms or a paginated response
export type ClassroomListResponse =
  | Classroom[]
  | {
      data: Classroom[]
      meta: {
        totalItems: number
        itemCount: number
        itemsPerPage: number
        totalPages: number
        currentPage: number
      }
    }
