// Import base types
import { BaseEntity, BaseCreateDTO, BaseUpdateDTO } from './base'

/**
 * Lesson interfaces
 */
export interface Lesson extends BaseEntity {
  name: string
  subjectId: string
  teacherId: string
  classId: string
}

export interface CreateLessonDTO extends BaseCreateDTO {
  name: string
  subjectId: string
  teacherId: string
  classId: string
}

export interface UpdateLessonDTO extends BaseUpdateDTO {
  name?: string
  subjectId?: string
  teacherId?: string
  classId?: string
}

/**
 * Attendance interfaces
 */
export interface Attendance extends BaseEntity {
  cin: string
  studentId: string
  lessonId: string
  status: 'present' | 'absent' | 'late' | 'excused'
}

export interface CreateAttendanceDTO extends BaseCreateDTO {
  cin: string
  studentId: string
  lessonId: string
  status: 'present' | 'absent' | 'late' | 'excused'
}

export interface UpdateAttendanceDTO extends BaseUpdateDTO {
  status?: 'present' | 'absent' | 'late' | 'excused'
}

/**
 * Request/Response interfaces for authentication
 */
export interface RequestLogin {
  email: string
  password: string
}

export interface RequestRegister {
  email: string
  password: string
  firstname: string
  lastname: string
  role: string
}
