import { Homework } from './index'

/**
 * A safer version of the Homework interface for mock data
 * with guaranteed non-optional fields for UI rendering
 */
export interface MockHomework
  extends Omit<Homework, 'allowedFileTypes' | 'status'> {
  allowedFileTypes: string[]
  status: 'pending' | 'late' | 'submitted'
}

/**
 * Safely converts a Homework object to a MockHomework with defaults for optional fields
 */
export function toSafeHomework(homework: Homework): MockHomework {
  return {
    ...homework,
    allowedFileTypes: homework.allowedFileTypes || ['pdf', 'doc', 'docx'],
    status: (homework.status as any) || 'pending',
  }
}
