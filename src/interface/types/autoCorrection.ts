// Types for the auto-correction feature

export interface CorrectionItem {
  id: string
  title: string
  description?: string
  points: string
  details?: CorrectionDetail[]
}

export interface CorrectionDetail {
  id: string
  title: string
  description?: string
}

export interface ExerciseResult {
  id: string
  exerciseNumber: number
  title: string
  score: string
  maxScore: string
  corrections: CorrectionItem[]
}

export interface AutoCorrectionResult {
  studentId?: string
  assignmentId?: string
  exercises: ExerciseResult[]
  totalScore?: string
  totalMaxScore?: string
  feedback?: string
  createdAt?: string
}
