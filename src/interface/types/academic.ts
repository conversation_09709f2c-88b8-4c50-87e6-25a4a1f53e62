import { BaseEntity, BaseCreateDTO, BaseUpdateDTO } from './base'

/**
 * Subject interfaces
 */
export interface Subject extends BaseEntity {
  name: string
  description: string
  formula?: string
  eliminationThreshold?: number
  passingGrade?: number
  examTypes?: ExamType[]
  teachers?: string[] // Array of teacher IDs
  absences?: any[] // Added for backward compatibility
  etablissementId: string
}

export interface CreateSubjectDTO extends BaseCreateDTO {
  name: string
  description: string
  formula?: string
  eliminationThreshold?: number
  passingGrade?: number
  teacherIds?: string[]
  etablissementId: string
}

export interface UpdateSubjectDTO extends BaseUpdateDTO {
  name?: string
  description?: string
  formula?: string
  eliminationThreshold?: number
  passingGrade?: number
  teacherIds?: string[]
  etablissementId?: string
}

/**
 * Class interfaces
 */
export interface Class extends BaseEntity {
  name: string
  gradeId: string
  supervisorId: string
  students?: string[]
  subjects?: string[]
}

export interface CreateClassDTO extends BaseCreateDTO {
  name: string
  gradeId: string
  supervisorId: string
}

export interface UpdateClassDTO extends BaseUpdateDTO {
  name?: string
  gradeId?: string
  supervisorId?: string
}

/**
 * Grade level interfaces
 */
export interface GradeLevel extends BaseEntity {
  name: string
  classes?: string[]
}

export interface CreateGradeLevelDTO extends BaseCreateDTO {
  name: string
}

export interface UpdateGradeLevelDTO extends BaseUpdateDTO {
  name?: string
}

/**
 * Exam and grade related interfaces
 */
export interface ExamType {
  id: string
  name: string
  coefficient: number
}

export interface StudentGrade {
  id: string
  examType: 'final' | 'midterm' | 'quiz'
  value: number
  date: string
}

/**
 * Lesson interfaces
 */
export interface Lesson extends BaseEntity {
  name: string
  subjectId: string
  teacherId: string
  classId: string
}

export interface CreateLessonDTO extends BaseCreateDTO {
  name: string
  subjectId: string
  teacherId: string
  classId: string
}

export interface UpdateLessonDTO extends BaseUpdateDTO {
  name?: string
  subjectId?: string
  teacherId?: string
  classId?: string
}

/**
 * Attendance interfaces
 */
export interface Attendance extends BaseEntity {
  cin: string
  studentId: string
  lessonId: string
  status: 'present' | 'absent' | 'late' | 'excused'
}

export interface CreateAttendanceDTO extends BaseCreateDTO {
  cin: string
  studentId: string
  lessonId: string
  status: 'present' | 'absent' | 'late' | 'excused'
}

export interface UpdateAttendanceDTO extends BaseUpdateDTO {
  status?: 'present' | 'absent' | 'late' | 'excused'
}

/**
 * Student subject interfaces
 */
export interface StudentSubject {
  subjectId: string
  grades: StudentGrade[]
  absences?: number
  isEliminated?: boolean
}

export interface SemesterAverage {
  semester: string
  average: number
}

export interface SubjectScore {
  subject: string
  average: number
}

/**
 * Course and Schedule related types
 */
export interface CourseGroup {
  subject: string | null
  teacherID: number | null
  teacher: string | null
  salle: string | null
  divide?: number[]
  classroomType?: string
}

export interface Course {
  time: string
  subject: string | null
  teacher: string | null
  teacherID?: number | null
  room?: string | null
  salle?: string | null
  class?: string | null
  day?: string
  group1?: CourseGroup | null
  group2?: CourseGroup | null
  divide?: number[]
  classroomType?: string
}

export interface Schedule {
  [key: string]: Course[]
}

/**
 * Score and Grade related types
 */
export interface Score {
  subject: string
  score: number
  totalScore: number
  date: string
}

/**
 * Absence related types
 */
export interface Absence {
  id: string
  date: string
  reason: string
  studentId?: string
  teacherId?: string
  subjectId?: string
  status: 'pending' | 'approved' | 'rejected'
  notes?: string
}

export interface Establishment {
  id: string
  name: string
  address?: string
  phone?: string
  email?: string
  createdAt: string
  updatedAt: string
}
