/**
 * Base entity interface that all entities extend
 */
export interface BaseEntity {
  id: string | number
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

/**
 * Base user interface that all user types extend
 */
export interface BaseUser extends BaseEntity {
  firstname: string
  lastname: string
  email: string
  cin: string
  role: 'student' | 'teacher' | 'parent' | 'admin'
  isActive: boolean
}

/**
 * Base DTO interface for creating entities
 */
export interface BaseCreateDTO {
  createdBy?: string | null
  updatedBy?: string | null
}

/**
 * Base DTO interface for updating entities
 */
export interface BaseUpdateDTO {
  updatedBy?: string | null
}
