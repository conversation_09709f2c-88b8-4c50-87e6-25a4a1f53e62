import { BaseCreateDTO, BaseUpdateDTO } from './base'
import { Class } from './academic'

/**
 * Base user interface that other user types extend
 */
export interface BaseUser {
  id: string
  firstname: string
  lastname: string
  email: string
  cin: string
  role: 'student' | 'teacher' | 'parent' | 'admin'
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

/**
 * Teacher interfaces
 */
export interface Teacher extends BaseUser {
  role: 'teacher'
  title?: string
  subjects?: string[]
  classes?: string[]
  etablissementId?: string
  gender?: 'male' | 'female'
  birthday?: string
  avatar?: string
  isActive: boolean
  address?: string
  phone?: string
}

export interface TeacherProfileFormValues {
  firstname: string
  lastname: string
  email: string
  cin: string
}

export interface CreateTeacherDTO extends BaseCreateDTO {
  cin: string | number
  firstname: string
  lastname: string
  email: string
  password: string
  birthday: string
  gender: 'male' | 'female'
  etablissementId?: string
  avatar?: string
  isActive?: boolean
  title: string
  subjectIds?: string[]
  address?: string
  phone?: string
}

export interface UpdateTeacherDTO extends BaseUpdateDTO {
  firstname?: string
  lastname?: string
  email?: string
  cin?: string | number
  birthday?: string
  gender?: 'male' | 'female'
  title?: string
  subjectIds?: string[]
  etablissementId?: string
  avatar?: string
  isActive?: boolean
  address?: string
  phone?: string
  password?: string
}

/**
 * Student interfaces
 */
export interface Student extends BaseUser {
  role: 'student'
  gradeLevel: string
  enrolledDate: string
  parent?: Parent
  class?: Class
  subjects?: StudentSubject[]
  gender?: string
  birthday?: string
  avatar?: string | null
  fullname?: string
  scores?: any[]
  isPaid: boolean
  address?: string
  phone?: string
}

export interface CreateStudentDTO extends BaseCreateDTO {
  firstname: string
  lastname: string
  email: string
  password: string
  cin: string
  gradeLevel: string
  enrolledDate: string
  parent?: string // Changed from parentId
  class?: string // Changed from classId
  gender?: string
  birthday?: string
  avatar?: string | null
  isActive?: boolean
  // role field is not accepted by the API
  isPaid?: boolean
}

export interface UpdateStudentDTO extends BaseUpdateDTO {
  firstname?: string
  lastname?: string
  email?: string
  gradeLevel?: string
  parentId?: string
  classId?: string
  gender?: string
  birthday?: string
  avatar?: string | null
  isActive?: boolean
  isPaid?: boolean
}

export interface StudentSubject {
  subjectId: string | number
  grades: Grade[]
  absences?: number | any[]
  isEliminated?: boolean
  name?: string
  totalAbsences?: number
  id?: string | number
}

export interface Grade {
  id: string
  examType: 'final' | 'midterm' | 'quiz'
  value: number
  date: string
}

/**
 * Parent interfaces
 */
export interface Parent {
  id: string
  cin: string
  firstname: string
  lastname: string
  email: string
  gender: 'male' | 'female'
  birthday?: string
  role?: string
  students?: Array<{
    class: any
    id: string
    firstname: string
    lastname: string
    cin: string
  }>
  createdAt: string
  updatedAt: string
  avatar?: string
  address?: string
  phone?: string
}

export interface CreateParentDTO {
  cin: string
  firstname: string
  lastname: string
  email: string
  password: string
  studentIds?: string[]
  birthday?: string
  gender: 'male' | 'female'
  isActive: boolean
  avatar?: string
  address?: string
  phone?: string
}

export interface UpdateParentDTO {
  firstname?: string
  lastname?: string
  email?: string
  studentIds?: string[]
  birthday?: string
  gender?: 'male' | 'female'
  avatar?: string
  address?: string
  phone?: string
  cin?: string
  password?: string
}

/**
 * Admin interfaces
 */
export interface Admin extends BaseUser {
  role: 'admin'
  permissions?: string[]
  avatar?: string
  birthday: string | Date
  gender: 'MALE' | 'FEMALE'
  // Ensure cin is explicitly defined
  cin: string
  address?: string
  phone?: string
}

export interface CreateAdminDTO extends BaseCreateDTO {
  firstname: string
  lastname: string
  email: string
  password: string
  cin: string
  permissions?: string[]
  avatar?: string
  // Birthday can be a string (ISO format) or a Date object
  birthday: string | Date
  gender: 'MALE' | 'FEMALE'
  address?: string
  phone?: string
  isActive?: boolean
  etablissementId?: string
}

export interface UpdateAdminDTO extends BaseUpdateDTO {
  firstname?: string
  lastname?: string
  email?: string
  avatar?: string
  // Birthday can be a string (ISO format) or a Date object
  birthday?: string | Date
  gender?: 'MALE' | 'FEMALE'
  address?: string
  phone?: string
  isActive?: boolean
  etablissementId?: string
  // Adding these fields since they seem to be required by backend validation
  cin?: string
  password?: string
}

/**
 * Authentication interfaces
 */
export interface LoginDTO {
  email: string
  password: string
}

export interface AuthResponse {
  access_token: string
  user: BaseUser
}
