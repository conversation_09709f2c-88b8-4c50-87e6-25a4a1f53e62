/**
 * Authentication related types
 */

export interface RequestRegister {
  email: string
  password: string
  workspaceName?: string
  name?: string
  firstname?: string
  lastname?: string
  gender?: string
  birthday?: Date
  address?: string
  phone?: string
  avatar?: string
  schoolName?: string
  enrolledDate?: Date
  role?: string
  captchaToken?: string
  csrfToken?: string
  isActive?: boolean
  userOnboarding?: boolean
  createdBy?: string
  updatedBy?: string
}

export interface RequestLogin {
  email: string
  password: string
}
