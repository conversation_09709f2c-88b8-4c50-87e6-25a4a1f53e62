// types/index.ts - Central types hub for the application
// This file is designed to expose all types needed throughout the application

// Re-export core types
// Note: We're explicitly importing and re-exporting to avoid naming conflicts
import type { BaseEntity, BaseCreateDTO, BaseUpdateDTO } from './base'
export type { BaseEntity, BaseCreateDTO, BaseUpdateDTO }

// User types
import type {
  Teacher,
  CreateTeacherDTO,
  UpdateTeacherDTO,
  Student,
  CreateStudentDTO,
  UpdateStudentDTO,
  Parent,
  CreateParentDTO,
  UpdateParentDTO,
  Admin,
  CreateAdminDTO,
  UpdateAdminDTO,
  StudentSubject,
  LoginDTO,
  AuthResponse,
} from './user'
export type {
  Teacher,
  CreateTeacherDTO,
  UpdateTeacherDTO,
  Student,
  CreateStudentDTO,
  UpdateStudentDTO,
  Parent,
  CreateParentDTO,
  UpdateParentDTO,
  Admin,
  CreateAdminDTO,
  UpdateAdminDTO,
  StudentSubject,
  LoginDTO,
  AuthResponse,
}

// Academic types
import type {
  Subject,
  CreateSubjectDTO,
  UpdateSubjectDTO,
  Class,
  CreateClassDTO,
  UpdateClassDTO,
  GradeLevel,
  CreateGradeLevelDTO,
  UpdateGradeLevelDTO,
  ExamType,
  StudentGrade,
  Course,
  Schedule,
  Score,
  Absence,
} from './academic'
export type {
  Subject,
  CreateSubjectDTO,
  UpdateSubjectDTO,
  Class,
  CreateClassDTO,
  UpdateClassDTO,
  GradeLevel,
  CreateGradeLevelDTO,
  UpdateGradeLevelDTO,
  ExamType,
  StudentGrade,
  Course,
  Schedule,
  Score,
  Absence,
}

// Education types
import type {
  Lesson,
  CreateLessonDTO,
  UpdateLessonDTO,
  Attendance,
  CreateAttendanceDTO,
  UpdateAttendanceDTO,
  RequestLogin,
  RequestRegister,
} from './education'
export type {
  Lesson,
  CreateLessonDTO,
  UpdateLessonDTO,
  Attendance,
  CreateAttendanceDTO,
  UpdateAttendanceDTO,
  RequestLogin,
  RequestRegister,
}

// Establishment types
import type {
  Establishment,
  CreateEstablishmentDTO,
  UpdateEstablishmentDTO,
} from './establishment'
export type { Establishment, CreateEstablishmentDTO, UpdateEstablishmentDTO }

// Grade types
import type { Grade, CreateGradeDTO, UpdateGradeDTO } from './grade'
export type { Grade, CreateGradeDTO, UpdateGradeDTO }

// Mock data types for components
import type { MockSubject } from './MockSubject'
import type { MockHomework } from './MockHomework'
export type { MockSubject, MockHomework }
export { toMockSubject } from './MockSubject'
export { toSafeHomework } from './MockHomework'

// Custom types for component props
export interface Homework {
  id: string | number
  title: string
  description: string
  dueDate: string
  subjectId: string | number
  teacherName?: string
  allowFileSubmission?: boolean
  allowedFileTypes?: string[]
  status?: string
}

export interface StudentData {
  id: string | number
  firstname: string
  lastname: string
  fullname?: string
  studentId?: string
  subjects: StudentSubject[]
  class?: Class
}

export interface MoyeneStudentProps {
  studentId: string
  semesters?: any[]
}

export interface ScoreStudentProps {
  studentId: string
  subjects?: any[]
}

// Export any form-related types
export * from './forms'
