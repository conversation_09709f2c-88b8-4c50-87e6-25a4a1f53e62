/**
 * A simplified Subject interface for mock data
 * This allows components to use mock data without having to provide
 * all the properties required by the full Subject interface
 */
export interface MockSubject {
  id: string | number
  name: string
  description?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  formula?: string
  eliminationThreshold?: number
  passingGrade?: number
  examTypes?: any[]
  teachers?: string[]
  absences?: any[]
}

/**
 * Helper to convert a simple { id, name } object to a MockSubject
 */
export function toMockSubject(data: {
  id: string | number
  name: string
}): MockSubject {
  return {
    id: data.id,
    name: data.name,
    description: 'Mock subject description',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    updatedBy: 'system',
  }
}
