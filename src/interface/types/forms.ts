/**
 * Form related types
 */

/**
 * Lesson group for "by group" lessons (two lessons in the same hour)
 */
export interface LessonGroup {
  subjectId?: string
  teacherId?: string
  periodsPerWeek?: number
  divide?: number[]
}

/**
 * Lesson form values for lesson configuration (single or by group)
 */
export interface LessonFormValues {
  class?: string
  byGroup?: boolean
  subjectId?: string
  teacherId?: string
  periodsPerWeek?: number
  divide?: number[]
  group1?: LessonGroup
  group2?: LessonGroup
}

export interface SearchFormValues {
  cin?: string
  name?: string
  grade?: string
}

export interface ClassFormValues {
  name: string
  grade: string
  teacherId: string
}

export interface AbsenceFormValues {
  date: string
  reason: string
  studentId?: string
  subjectId?: string
}

export interface ProfileFormValues {
  name: string
  cin: string
  email: string
  class?: string
  subject?: string
}

export interface QuizFormValues {
  questionCount: number
  quizType: string
  language: string
  inputText: string
  pdfFile: File | null
}
