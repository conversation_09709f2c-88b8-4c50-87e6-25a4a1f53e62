import { BaseEntity, BaseCreateDTO, BaseUpdateDTO } from './base'
import { CardTheme } from '@/components/card/CardThemeSelector'

/**
 * Card design settings interface
 */
export interface CardDesignSettings {
  theme: CardTheme
  layout: 'horizontal' | 'vertical'
  validUntilDate: string
  showQrCode: boolean
  showLogo: boolean
  customFields: {
    enabled: boolean
    field1Label: string
    field2Label: string
    [key: string]: any
  }
}

/**
 * Card entity from backend
 */
export interface CardEntity {
  id: string
  themeNumber: number
  colorNumber: number
  preferences: {
    theme?: CardTheme
    fontStyle?: string
    additionalInfo?: string
    customFields?: {
      cardLayout?: 'horizontal' | 'vertical'
      showQRCode?: boolean
      [key: string]: any
    }
  }
  etablissementId: string
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

/**
 * DTO for creating a new card
 */
export interface CreateCardDto {
  themeNumber: number
  colorNumber: number
  preferences: {
    theme?: CardTheme
    fontStyle?: string
    additionalInfo?: string
    customFields?: {
      cardLayout?: 'horizontal' | 'vertical'
      showQRCode?: boolean
      [key: string]: any
    }
  }
  etablissementId?: string
}

/**
 * DTO for updating an existing card
 */
export interface UpdateCardDto {
  themeNumber?: number
  colorNumber?: number
  preferences?: {
    theme?: CardTheme
    fontStyle?: string
    additionalInfo?: string
    customFields?: {
      cardLayout?: 'horizontal' | 'vertical'
      showQRCode?: boolean
      [key: string]: any
    }
  }
}

/**
 * Establishment interfaces
 */
export interface Establishment extends BaseEntity {
  name: string
  address: string
  logo?: string
  url?: string
  CMSContent?: string
  services?: string[]
  isActive: boolean
  admins?: string[]
  teachers?: string[]
  students?: string[]
  cardDesign?: CardDesignSettings
}

export interface CreateEstablishmentDTO extends BaseCreateDTO {
  name: string
  address: string
  logo?: string
  url?: string
  CMSContent?: string
  services?: string[]
  isActive?: boolean
  adminIds?: string[]
  teacherIds?: string[]
  studentIds?: string[]
  cardDesign?: CardDesignSettings
}

export interface UpdateEstablishmentDTO extends BaseUpdateDTO {
  name?: string
  address?: string
  logo?: string
  url?: string
  CMSContent?: string
  services?: string[]
  isActive?: boolean
  adminIds?: string[]
  teacherIds?: string[]
  studentIds?: string[]
  cardDesign?: CardDesignSettings
}
