import { createContext, useContext, useState, ReactNode } from 'react'
import SEO from '@/components/shared/SEO'
import { generatePlatformStructuredData } from '@/utils/structuredData'

interface SEOContextType {
  updateSEO: (props: SEOProps) => void
}

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  ogImage?: string
  ogUrl?: string
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image'
  canonicalUrl?: string
  noIndex?: boolean
  structuredData?: object
}

const SEOContext = createContext<SEOContextType | undefined>(undefined)

export function SEOProvider({ children }: { children: ReactNode }) {
  const [seoProps, setSeoProps] = useState<SEOProps>({
    title: 'JeridSchool - K12 Education Management System',
    description:
      'Comprehensive K12 education management system for schools, teachers, students, and parents. Streamline administration, enhance learning, and improve communication.',
    keywords:
      'education management, K12, school ERP, student management, teacher management, EdTech, school administration',
    ogImage: 'https://jeridschool.tech/og-image.jpg',
    ogType: 'website',
    twitterCard: 'summary_large_image',
    structuredData: generatePlatformStructuredData(),
  })

  const updateSEO = (props: SEOProps) => {
    setSeoProps((prevProps) => ({
      ...prevProps,
      ...props,
    }))
  }

  return (
    <SEOContext.Provider value={{ updateSEO }}>
      <SEO {...seoProps} />
      {children}
    </SEOContext.Provider>
  )
}

export function useSEO() {
  const context = useContext(SEOContext)
  if (context === undefined) {
    throw new Error('useSEO must be used within a SEOProvider')
  }
  return context
}
