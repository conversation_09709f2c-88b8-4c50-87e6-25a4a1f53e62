import { z } from 'zod'

// Deck schemas
export const createDeckSchema = z.object({
  name: z
    .string()
    .min(1, 'Deck name is required')
    .max(100, 'Deck name must be less than 100 characters'),
  visibility: z.enum(['public', 'private']),
})

export const updateDeckSchema = z.object({
  name: z
    .string()
    .min(1, 'Deck name is required')
    .max(100, 'Deck name must be less than 100 characters')
    .optional(),
  visibility: z.enum(['public', 'private']).optional(),
})

// Card schemas
export const createCardSchema = z.object({
  frontText: z.string().min(1, 'Front text is required'),
  frontImage: z.string().url('Invalid image URL').optional().nullable(),
  backText: z.string().min(1, 'Back text is required'),
  backImage: z.string().url('Invalid image URL').optional().nullable(),
  deckId: z.string().uuid('Invalid deck ID'),
})

export const updateCardSchema = z.object({
  frontText: z.string().min(1, 'Front text is required').optional(),
  frontImage: z.string().url('Invalid image URL').optional().nullable(),
  backText: z.string().min(1, 'Back text is required').optional(),
  backImage: z.string().url('Invalid image URL').optional().nullable(),
})

// Types
export type CreateDeckFormValues = z.infer<typeof createDeckSchema>
export type UpdateDeckFormValues = z.infer<typeof updateDeckSchema>
export type CreateCardFormValues = z.infer<typeof createCardSchema>
export type UpdateCardFormValues = z.infer<typeof updateCardSchema>
