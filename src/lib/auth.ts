// Simple auth utilities for token management

/**
 * Get the authentication token from localStorage
 */
export const getToken = (): string | null => {
  return localStorage.getItem('token')
}

/**
 * Set the authentication token in localStorage
 */
export const setToken = (token: string): void => {
  localStorage.setItem('token', token)
}

/**
 * Remove the authentication token from localStorage
 */
export const removeToken = (): void => {
  localStorage.removeItem('token')
}

/**
 * Check if the user is authenticated (has a token)
 */
export const isAuthenticated = (): boolean => {
  return !!getToken()
}

/**
 * Parse the JWT token to get user information
 */
export const parseToken = (token: string): any => {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Error parsing token:', error)
    return null
  }
}

/**
 * Get the user's role from the token
 */
export const getUserRole = (): string | null => {
  const token = getToken()
  if (!token) return null

  const decoded = parseToken(token)
  return decoded?.role || null
}

/**
 * Get the user's ID from the token
 */
export const getUserId = (): string | null => {
  const token = getToken()
  if (!token) return null

  const decoded = parseToken(token)
  return decoded?.sub || null
}

export default {
  getToken,
  setToken,
  removeToken,
  isAuthenticated,
  parseToken,
  getUserRole,
  getUserId,
}
