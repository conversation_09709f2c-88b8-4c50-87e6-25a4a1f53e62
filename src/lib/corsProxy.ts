/**
 * CORS Proxy Helper
 *
 * This utility helps manage API requests in development and production environments
 * to avoid CORS issues.
 */

/**
 * Transforms a URL to use the local proxy in development or direct URL in production
 *
 * @param url The original API URL
 * @returns The transformed URL
 */
export function getProxiedUrl(url: string): string {
  // Check if we're in development mode
  const isDev = import.meta.env.DEV

  if (!isDev) {
    // In production, use the URL as is
    return url
  }

  try {
    // Parse the URL to extract the path
    const urlObj = new URL(url)

    // If it's an API URL, use the /api proxy
    if (urlObj.hostname === 'api.jeridschool.tech') {
      return `/api${urlObj.pathname}${urlObj.search}`
    }

    // If it's a CDN URL, use the /cdn-proxy proxy
    if (urlObj.hostname === 'cdn.jeridschool.tech') {
      return `/cdn-proxy${urlObj.pathname}${urlObj.search}`
    }

    // For other URLs, return as is
    return url
  } catch (error) {
    // If URL parsing fails, assume it's already a relative path
    console.warn('Error parsing URL for proxy:', error)
    return url
  }
}

/**
 * Transforms API paths to use the appropriate proxy in development
 *
 * @param path The API path (without domain)
 * @param type The API type ('api' or 'cdn')
 * @returns The proxied path
 */
export function getProxiedPath(
  path: string,
  type: 'api' | 'cdn' = 'api'
): string {
  // Check if we're in development mode
  const isDev = import.meta.env.DEV

  // If not in development, construct the full URL
  if (!isDev) {
    const baseUrl =
      type === 'api'
        ? 'http://localhost:3000'
        : 'https://cdn.jeridschool.tech'

    // Ensure path starts with /
    const normalizedPath = path.startsWith('/') ? path : `/${path}`
    return `${baseUrl}${normalizedPath}`
  }

  // In development, use the proxy
  const proxyPrefix = type === 'api' ? '/api' : '/cdn-proxy'

  // Ensure path starts with /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${proxyPrefix}${normalizedPath}`
}

/**
 * Creates headers with CORS settings for fetch requests
 *
 * @returns Headers object with CORS settings
 */
export function getCorsHeaders(): HeadersInit {
  return {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  }
}
