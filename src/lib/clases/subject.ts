import { Subject } from '@/interface/types'
import { api } from '../axios'

/**
 * Get all subjects
 */
export const getAll = async (): Promise<Subject[]> => {
  const response = await api.get<Subject[]>('/subjects')
  return response.data
}

/**
 * Get a subject by ID
 */
export const getById = async (id: string): Promise<Subject> => {
  const response = await api.get<Subject>(`/subjects/${id}`)
  return response.data
}

/**
 * Create a new subject
 */
export const createSubject = async (
  data: Omit<Subject, 'id'>
): Promise<Subject> => {
  const response = await api.post<Subject>('/subjects', data)
  return response.data
}

/**
 * Update a subject
 */
export const updateSubject = async (
  id: string,
  data: Partial<Subject>
): Promise<Subject> => {
  const response = await api.patch<Subject>(`/subjects/${id}`, data)
  return response.data
}

/**
 * Delete a subject
 */
export const deleteSubject = async (id: string): Promise<void> => {
  await api.delete(`/subjects/${id}`)
}

/**
 * Get teachers assigned to a subject
 */
export const getTeachers = async (subjectId: string) => {
  const response = await api.get(`/subjects/${subjectId}/teachers`)
  return response.data
}

/**
 * Get classes where this subject is taught
 */
export const getClasses = async (subjectId: string) => {
  const response = await api.get(`/subjects/${subjectId}/classes`)
  return response.data
}

/**
 * Service for managing subject-related operations
 */
export const subjectService = {
  getAll,
  getById,
  create: createSubject,
  update: updateSubject,
  delete: deleteSubject,
  getTeachers,
  getClasses,
}

export default subjectService
