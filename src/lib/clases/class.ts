// Import class types
import type { Class, CreateClassDTO } from '@/interface/types'
import { api } from '@/lib/axios'

/**
 * Creates a new class in the system
 */
export async function createClass(classData: CreateClassDTO): Promise<Class> {
  try {
    const response = await api.post<Class>('/class', classData)
    return response.data
  } catch (error) {
    console.error('Error creating class:', error)
    throw error
  }
}
