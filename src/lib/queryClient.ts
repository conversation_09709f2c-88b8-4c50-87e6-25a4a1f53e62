import { QueryClient } from '@tanstack/react-query'

// Create a singleton QueryClient instance that can be imported anywhere
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes - keep unused data in cache for 10 minutes
      refetchOnWindowFocus: false, // Don't refetch on window focus by default
      retry: 1, // Only retry once on failure
      networkMode: 'always', // Always try to fetch even if offline
    },
    mutations: {
      networkMode: 'always', // Always try to mutate even if offline
      retry: 2, // Retry mutations twice on failure
    },
  },
})
