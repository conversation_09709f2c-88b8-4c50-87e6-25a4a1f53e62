import { redirectToPath } from '../router/routerUtils'

// Function to check if a user is authenticated
const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('access_token')
}

// Function to get a user's role from JWT or localStorage
export const getUserRole = (): string => {
  // First try to get the role from JWT if possible
  const token = localStorage.getItem('access_token')
  if (token) {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const payload = JSON.parse(atob(base64))

      // Return the role from the JWT payload
      return payload.role || payload.roles?.[0] || 'GUEST'
    } catch (e) {
      console.error('Error decoding token', e)
    }
  }

  // Fallback to localStorage role
  return localStorage.getItem('role') || 'GUEST'
}

// Function to check if the user has one of the allowed roles
const hasAllowedRole = (allowedRoles: string[]): boolean => {
  const currentRole = getUserRole().toUpperCase()
  return allowedRoles.some((role) => currentRole === role.toUpperCase())
}

// Route guard for authenticated users
export const guardAuthenticated = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }
  return {}
}

// Route guard for admin users
export const guardAdminOnly = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['ADMIN', 'SuperAdmin'])) {
    console.log('User not admin, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  return {}
}

// Route guard for teacher users
export const guardTeacherOnly = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['TEACHER'])) {
    console.log('User not teacher, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  return {}
}

// Route guard for student users
export const guardStudentOnly = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['STUDENT'])) {
    console.log('User not student, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  console.log('Student route guard passed')
  return {}
}

// Route guard for parent users
export const guardParentOnly = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['PARENT'])) {
    console.log('User not parent, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  return {}
}

// Route guard for teacher, student, or parent users
export const guardTeacherStudentParent = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['TEACHER', 'STUDENT', 'PARENT'])) {
    console.log('User not teacher/student/parent, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  return {}
}

// Route guard for student or B2C_Client users
export const guardStudentOrB2CClient = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['STUDENT', 'B2C_CLIENT'])) {
    console.log('User not student or B2C_Client, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  console.log('Student or B2C_Client route guard passed')
  return {}
}

// Route guard for super admin users
export const guardSuperAdminOnly = () => {
  if (!isAuthenticated()) {
    console.log('User not authenticated, redirecting to login')
    return redirectToPath('/login')
  }

  if (!hasAllowedRole(['SuperAdmin'])) {
    console.log('User not super admin, redirecting to unauthorized')
    return redirectToPath('/unauthorized')
  }
  return {}
}

// Function to check if user has all required data in localStorage
const hasRequiredUserData = (): boolean => {
  const hasToken = !!localStorage.getItem('access_token');
  const hasRole = !!localStorage.getItem('role');
  const hasId = !!localStorage.getItem('id');
  const hasEmail = !!localStorage.getItem('email');
  const hasLastname = !!localStorage.getItem('lastname');
  const hasFirstname = !!localStorage.getItem('firstname');
  
  console.log('Auth check:', { hasToken, hasRole, hasId, hasEmail, hasLastname, hasFirstname });
  
  return hasToken && hasRole && hasId && hasEmail && hasLastname && hasFirstname;
}

// Route guard for quizz
export const guardQuizz = () => {
  if (!hasRequiredUserData()) {
    console.log('Missing required user data, redirecting to login');
    return redirectToPath('/login');
  }
  return {};
}

// Route guard for notes
export const guardNotes = () => {
  if (!hasRequiredUserData()) {
    console.log('Missing required user data, redirecting to login');
    return redirectToPath('/login');
  }
  return {};
}

// Route guard for focus-room
export const guardFocusRoom = () => {
  if (!hasRequiredUserData()) {
    console.log('Missing required user data, redirecting to login');
    return redirectToPath('/login');
  }
  return {};
}

// Route guard for whiteboard
export const guardWhiteboard = () => {
  if (!hasRequiredUserData()) {
    console.log('Missing required user data, redirecting to login');
    return redirectToPath('/login');
  }
  return {};
}
