/**
 * Utility functions for user authentication
 */

/**
 * Gets the currently authenticated user from the JWT token
 * @returns The user object or null if not authenticated
 */
export function getUser() {
  const token = localStorage.getItem('access_token')
  if (!token) return null

  try {
    // Try to decode the JWT
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join('')
    )

    return JSON.parse(jsonPayload)
  } catch (e) {
    console.error('Error decoding token', e)
    // If token is invalid, return a simple object with role
    return localStorage.getItem('role')
      ? { role: localStorage.getItem('role') }
      : null
  }
}

/**
 * Gets the user's role from the JWT token or localStorage
 * @returns The user's role or 'GUEST' if not found
 */
export function getUserRole(): string {
  const user = getUser()

  if (!user) return 'GUEST'

  // Try to get the role from the user object
  const role = user.role || user.roles?.[0] || localStorage.getItem('role')

  return role || 'GUEST'
}
