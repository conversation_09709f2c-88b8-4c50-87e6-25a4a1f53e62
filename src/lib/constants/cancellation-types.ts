/**
 * Cancellation category types
 * cancel session for super Admin 
 * related with attendance
 */
export enum CancellationCategory {
  TEACHER_ABSENCE = 'TEACHER_ABSENCE',
  SCHOOL_HOLIDAY = 'SCHOOL_HOLIDAY',
  EMERGENCY = 'EMERGENCY',
  WEATHER = 'WEATHER',
  ADMINISTRATIVE = 'ADMINISTRATIVE',
  OTHER = 'OTHER',
}

/**
 * Cancellation category labels
 */
export const CANCELLATION_CATEGORY_LABELS: Record<CancellationCategory, string> = {
  [CancellationCategory.TEACHER_ABSENCE]: 'Teacher Absence',
  [CancellationCategory.SCHOOL_HOLIDAY]: 'School Holiday',
  [CancellationCategory.EMERGENCY]: 'Emergency',
  [CancellationCategory.WEATHER]: 'Weather',
  [CancellationCategory.ADMINISTRATIVE]: 'Administrative',
  [CancellationCategory.OTHER]: 'Other',
};

/**
 * Cancellation category colors
 */
export const CANCELLATION_CATEGORY_COLORS: Record<CancellationCategory, string> = {
  [CancellationCategory.TEACHER_ABSENCE]: 'bg-orange-100 text-orange-800 border-orange-400 border-2',
  [CancellationCategory.SCHOOL_HOLIDAY]: 'bg-blue-100 text-blue-800 border-blue-400 border-2',
  [CancellationCategory.EMERGENCY]: 'bg-red-100 text-red-800 border-red-300',
  [CancellationCategory.WEATHER]: 'bg-cyan-100 text-cyan-800 border-cyan-300',
  [CancellationCategory.ADMINISTRATIVE]: 'bg-purple-100 text-purple-800 border-purple-300',
  [CancellationCategory.OTHER]: 'bg-gray-200 text-gray-800 border-gray-400',
};

/**
 * Cancellation category icons (using Lucide icon names)
 */
export const CANCELLATION_CATEGORY_ICONS: Record<CancellationCategory, string> = {
  [CancellationCategory.TEACHER_ABSENCE]: 'UserX',
  [CancellationCategory.SCHOOL_HOLIDAY]: 'Calendar',
  [CancellationCategory.EMERGENCY]: 'AlertTriangle',
  [CancellationCategory.WEATHER]: 'Cloud',
  [CancellationCategory.ADMINISTRATIVE]: 'ClipboardList',
  [CancellationCategory.OTHER]: 'HelpCircle',
};

/**
 * Get the appropriate color class for a cancellation category
 */
export const getCancellationCategoryColor = (category: CancellationCategory | string | null | undefined): string => {
  if (!category) return CANCELLATION_CATEGORY_COLORS[CancellationCategory.OTHER];

  // Handle string values by converting to enum
  const categoryEnum = typeof category === 'string'
    ? Object.values(CancellationCategory).find(c => c === category)
    : category;

  return categoryEnum && CANCELLATION_CATEGORY_COLORS[categoryEnum]
    ? CANCELLATION_CATEGORY_COLORS[categoryEnum]
    : CANCELLATION_CATEGORY_COLORS[CancellationCategory.OTHER];
};

/**
 * Get the label for a cancellation category
 */
export const getCancellationCategoryLabel = (category: CancellationCategory | string | null | undefined): string => {
  if (!category) return CANCELLATION_CATEGORY_LABELS[CancellationCategory.OTHER];

  // Handle string values by converting to enum
  const categoryEnum = typeof category === 'string'
    ? Object.values(CancellationCategory).find(c => c === category)
    : category;

  return categoryEnum && CANCELLATION_CATEGORY_LABELS[categoryEnum]
    ? CANCELLATION_CATEGORY_LABELS[categoryEnum]
    : CANCELLATION_CATEGORY_LABELS[CancellationCategory.OTHER];
};

/**
 * Check if a session was canceled due to teacher absence
 */
export const isTeacherAbsence = (category: CancellationCategory | string | null | undefined): boolean => {
  if (!category) return false;

  // Handle string values by converting to enum
  const categoryEnum = typeof category === 'string'
    ? Object.values(CancellationCategory).find(c => c === category)
    : category;

  return categoryEnum === CancellationCategory.TEACHER_ABSENCE;
};

/**
 * Check if a session was canceled due to school holiday
 */
export const isSchoolHoliday = (category: CancellationCategory | string | null | undefined): boolean => {
  if (!category) return false;

  // Handle string values by converting to enum
  const categoryEnum = typeof category === 'string'
    ? Object.values(CancellationCategory).find(c => c === category)
    : category;

  return categoryEnum === CancellationCategory.SCHOOL_HOLIDAY;
};

/**
 * Get a more descriptive message for a cancellation category
 */
export const getCancellationMessage = (category: CancellationCategory | string | null | undefined): string => {
  if (!category) return "This session was canceled.";

  // Handle string values by converting to enum
  const categoryEnum = typeof category === 'string'
    ? Object.values(CancellationCategory).find(c => c === category)
    : category;

  switch (categoryEnum) {
    case CancellationCategory.TEACHER_ABSENCE:
      return "This session was canceled due to teacher absence.";
    case CancellationCategory.SCHOOL_HOLIDAY:
      return "This session was canceled due to a school holiday.";
    case CancellationCategory.EMERGENCY:
      return "This session was canceled due to an emergency.";
    case CancellationCategory.WEATHER:
      return "This session was canceled due to weather conditions.";
    case CancellationCategory.ADMINISTRATIVE:
      return "This session was canceled due to administrative reasons.";
    default:
      return "This session was canceled.";
  }
};
