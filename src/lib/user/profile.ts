// Define a more comprehensive user profile type
export interface Etablissement {
  id: string
  name: string
  address?: string
  logo?: string
  url?: string
  CMSContent?: string
  description?: string
  heroImage?: string
  galleryImages?: string[]
  contactEmail?: string
  contactPhone?: string
  socialLinks?: {
    twitter?: string
    facebook?: string
    linkedin?: string
    instagram?: string
    [key: string]: string | undefined
  }
  stats?: {
    awards?: number
    courses?: number
    students?: number
    teachers?: number
    [key: string]: number | undefined
  }
  services?: any[]
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
  createdBy?: string | null
  updatedBy?: string | null
}

export interface UserProfile {
  id: string
  role: string
  email: string
  firstname?: string
  lastname?: string
  fullname?: string
  avatar?: string
  gender?: string
  birthday?: string
  phone?: string
  address?: string
  cin?: string
  createdAt?: string
  lastLogin?: string
  isActive?: boolean
  updatedAt?: string
  etablissement?: Etablissement
  [key: string]: any // Allow dynamic property access for extra fields
}

// Fetch user profile from API using JWT
export const fetchUserProfile = async (jwt: string): Promise<UserProfile> => {
  if (!jwt) {
    throw new Error('No JWT token provided')
  }
  try {
    // Get API URL from environment variables with fallback
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'
    console.log('Fetching user profile from:', `${API_URL}/user-controller/getme`)

    const response = await fetch(
      `${API_URL}/user-controller/getme`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${jwt}`,
          'Content-Type': 'application/json',
        },
      }
    )
    if (!response.ok) {
      throw new Error(`Failed to fetch user profile: ${response.statusText}`)
    }
    const data = await response.json()
    // Optionally, generate fullname if not present
    if (!data.fullname) {
      data.fullname = `${data.firstname || ''} ${data.lastname || ''}`.trim()
    }
    return data as UserProfile
  } catch (error) {
    console.error('Error fetching user profile:', error)
    throw error
  }
}

// Update user profile data
export const updateUserProfile = async (
  jwt: string,
  profileData: Partial<UserProfile>
): Promise<UserProfile> => {
  if (!jwt) {
    throw new Error('No JWT token provided')
  }

  try {
    // Get the current user's role from the profile data or localStorage
    const role = profileData.role || localStorage.getItem('role') || ''

    // Determine the appropriate endpoint based on user role
    let endpoint = ''

    switch (role.toLowerCase()) {
      case 'admin':
        endpoint = '/admin'
        break
      case 'teacher':
        endpoint = '/teacher'
        break
      case 'student':
        endpoint = '/student'
        break
      case 'parent':
        endpoint = '/parent'
        break
      default:
        throw new Error(`Unsupported role for profile update: ${role}`)
    }

    // Get the user ID from profile data or localStorage
    const userId = profileData.id || localStorage.getItem('id')
    if (!userId) {
      throw new Error('User ID not found')
    }

    // Clean the data to remove any undefined or null values
    const cleanData = Object.fromEntries(
      Object.entries(profileData).filter(
        ([_, value]) => value !== undefined && value !== null
      )
    )

    // Make the API call to update the profile
    const response = await fetch(
      `http://localhost:3000${endpoint}/${userId}`,
      {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${jwt}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanData),
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        `Failed to update profile: ${response.statusText} - ${JSON.stringify(errorData)}`
      )
    }

    // Refresh the user data to get the latest profile
    return await fetchUserProfile(jwt)
  } catch (error) {
    throw error
  }
}

// Upload avatar image and return the URL
export const uploadAvatar = async (file: File): Promise<string> => {
  try {
    // Create a FormData object to send the file
    const formData = new FormData()
    formData.append('file', file)

    // Get the CDN token from environment variables
    const cdnToken =
      import.meta.env.VITE_CDN_TOKEN ||
      '5ffaccd4ae0ee9afc5077f30ee32264dab8ca247d86eaed2b42d4dac1e954d9d'

    // Make the API call to upload the file
    const response = await fetch('https://cdn.jeridschool.tech/upload', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${cdnToken}`,
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`Failed to upload avatar: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.success || !data.data || !data.data.url) {
      throw new Error('Invalid response from CDN server')
    }

    // Ensure HTTPS for CDN URLs
    let url = data.data.url
    if (url.startsWith('http://')) {
      url = url.replace('http://', 'https://')
    }

    return url
  } catch (error) {
    throw error
  }
}

// Update user profile with avatar
export const updateProfileWithAvatar = async (
  jwt: string,
  profileData: Partial<UserProfile>,
  avatarFile?: File
): Promise<UserProfile> => {
  try {
    // If an avatar file is provided, upload it first
    if (avatarFile) {
      const avatarUrl = await uploadAvatar(avatarFile)
      profileData.avatar = avatarUrl
    }

    // Then update the profile with the avatar URL
    return await updateUserProfile(jwt, profileData)
  } catch (error) {
    console.error('Error updating profile with avatar:', error)
    throw error
  }
}

// Update password using forgot-password flow since there's no update password endpoint
export const updateUserPassword = async (): Promise<any> => {
  try {
    // We need to get the user's email from localStorage or sessionStorage
    const jwt =
      localStorage.getItem('jwt') || sessionStorage.getItem('jwt') || ''
    const payload = jwt ? JSON.parse(atob(jwt.split('.')[1])) : null
    const email = payload?.email || localStorage.getItem('email')

    if (!email) {
      throw new Error('No email found in token or localStorage')
    }

    // Step 1: Trigger a forgot password email
    // In a real implementation, you'd call an API endpoint here

    return {
      success: true,
      message:
        'Password update initiated. Check your email to complete the process.',
    }
  } catch (error) {
    throw new Error('Failed to update password')
  }
}
