import { Parent } from '@/interface/types'
import { api } from '../axios'

/**
 * Data transfer object for creating a new parent
 */
export interface CreateParentDTO {
  cin: string
  firstname: string
  lastname: string
  email: string
  password: string
  role: 'parent'
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  studentIds: string[] // Array of student IDs to associate with the parent
}

/**
 * Create a new parent with student associations
 */
export const createParent = async (data: CreateParentDTO): Promise<Parent> => {
  // Send studentIds directly in the body as required by backend
  const response = await api.post<Parent>('/parent', data)
  return response.data
}

/**
 * Get all parents
 */
export const getAllParents = async (): Promise<Parent[]> => {
  const response = await api.get<Parent[]>('/parents')
  return response.data
}

/**
 * Get a parent by ID
 */
export const getParentById = async (id: string): Promise<Parent> => {
  const response = await api.get<Parent>(`/parents/${id}`)
  return response.data
}

/**
 * Update a parent
 */
export const updateParent = async (
  id: string,
  data: Partial<Parent>
): Promise<Parent> => {
  const response = await api.patch<Parent>(`/parents/${id}`, data)
  return response.data
}

/**
 * Delete a parent
 */
export const deleteParent = async (id: string): Promise<void> => {
  await api.delete(`/parents/${id}`)
}

/**
 * Get students of a parent
 */
export const getParentStudents = async (parentId: string) => {
  const response = await api.get(`/parents/${parentId}/students`)
  return response.data
}

/**
 * Service for managing parent-related operations
 */
export const parentService = {
  create: createParent,
  getAll: getAllParents,
  getById: getParentById,
  update: updateParent,
  delete: deleteParent,
  getStudents: getParentStudents,
}

export default parentService
