import axios from 'axios'

import { AutoCorrectionResult } from '@/interface/types/autoCorrection'
import type {
  Teacher,
  CreateTeacherDTO,
  UpdateTeacherDTO,
} from '@/interface/types'
import { RequestLogin, RequestRegister } from '@/interface/types/auth'

// API base URL
const BASE_URL = 'http://localhost:3000'
// AI Service base URL (from ai.md)

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Increase timeout to 60 seconds to handle slow connections
  timeout: 60000,
  // Add withCredentials to handle CORS properly
  withCredentials: false,
})

// Add interceptor to include auth token in requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Add response interceptor for better error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Create more user-friendly error messages
    if (error.code === 'ERR_NETWORK') {
      error.message =
        'Unable to connect to server. Please check your internet connection or try again later.'
    } else if (error.response) {
      // The server responded with a status other than 2xx
      error.message =
        error.response.data.message || 'An error occurred with the request.'
    } else if (error.request) {
      // The request was made but no response was received
      error.message = 'Server did not respond. Please try again later.'
    }
    return Promise.reject(error)
  }
)

// Create a generic service creator function
const createService = <T, CreateDTO, UpdateDTO>(basePath: string) => ({
  getAll: async () => {
    const response = await apiClient.get<T[]>(basePath)
    return response.data
  },
  getById: async (id: string) => {
    const response = await apiClient.get<T>(`${basePath}/${id}`)
    return response.data
  },
  create: async (data: CreateDTO) => {
    const response = await apiClient.post<T>(basePath, data)
    return response.data
  },
  update: async (id: string, data: UpdateDTO) => {
    const response = await apiClient.patch<T>(`${basePath}/${id}`, data)
    return response.data
  },
  delete: async (id: string) => {
    const response = await apiClient.delete(`${basePath}/${id}`)
    return response.data
  },
})

export const services = {
  auth: {
    login: async (data: { email: string; password: string }) => {
      try {
        console.log('Attempting login with:', data.email)
        const response = await apiClient.post('/auth/login', data)
        return response.data
      } catch (error) {
        console.error('Login API error:', error)

        if (axios.isAxiosError(error)) {
          if (error.response?.status === 401) {
            // Extract the exact error message from the API response
            const errorMessage =
              error.response.data?.message || 'Invalid credentials'
            const enhancedError = new Error(errorMessage)
            // @ts-ignore - Adding custom properties to the error
            enhancedError.response = error.response
            throw enhancedError
          } else if (error.code === 'ECONNABORTED') {
            throw new Error(
              'Connection timed out. The server is taking too long to respond. Please try again later.'
            )
          } else if (error.code === 'ERR_NETWORK') {
            throw new Error(
              'Network error. Please check your internet connection and try again.'
            )
          }
        }
        throw error
      }
    },
    register: async (data: {
      name: string
      email: string
      password: string
      workspaceName: string
    }) => {
      const response = await apiClient.post('/auth/register', data)
      return response.data
    },
    forgotPassword: async (email: string) => {
      try {
        console.log('Sending password reset request for:', email)
        const response = await apiClient.post('/auth/forgot-password', {
          email,
        })
        return response.data
      } catch (error) {
        console.error('Forgot password API error:', error)
        if (axios.isAxiosError(error)) {
          if (error.code === 'ECONNABORTED') {
            throw new Error(
              'Connection timed out. The server is taking too long to respond. Please try again later.'
            )
          } else if (error.code === 'ERR_NETWORK') {
            throw new Error(
              'Network error. Please check your internet connection and try again.'
            )
          }
          throw new Error(
            error.response?.data?.message ||
              'Failed to send password reset email'
          )
        }
        throw error
      }
    },
    resetPassword: async (token: string, password: string) => {
      try {
        const response = await apiClient.post('/auth/reset-password', {
          token,
          password,
        })
        return response.data
      } catch (error) {
        console.error('Reset password request failed:', error)
        throw error
      }
    },
  },
  // Teacher service with extended functionality
  teacher: {
    ...createService<Teacher, CreateTeacherDTO, UpdateTeacherDTO>('/teacher'),
    getSubjects: async (teacherId: string) => {
      const response = await apiClient.get<Subject[]>(
        `/teacher/${teacherId}/subjects`
      )
      return response.data
    },
    getClasses: async (teacherId: string) => {
      const response = await apiClient.get<Class[]>(
        `/teacher/${teacherId}/classes`
      )
      return response.data
    },
    // Auto-correction service
    uploadForCorrection: async (formData: FormData) => {
      const response = await apiClient.post<AutoCorrectionResult>(
        '/teacher/auto-correction',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )
      return response.data
    },
    getCorrectionHistory: async (teacherId: string) => {
      const response = await apiClient.get<AutoCorrectionResult[]>(
        `/teacher/${teacherId}/auto-correction-history`
      )
      return response.data
    },
  },
}

// Local interfaces for types not imported from elsewhere
interface Subject {
  id: string
  // Add other properties
}

interface Class {
  id: string
  // Add other properties
}

// Export the server status component
// Ensure the ServerStatus module exists and is correctly named
// export { default as ServerStatus } from './ServerStatus';

// Register Service
export async function registerService(value: RequestRegister) {
  try {
    console.log('Attempting registration with:', value.email)
    const res = await apiClient.post('/auth/signup', value)
    return res.data
  } catch (e) {
    if (axios.isAxiosError(e)) {
      console.error('Registration error:', {
        message: e.message,
        response: e.response?.data,
        status: e.response?.status,
        code: e.code,
      })

      if (e.code === 'ECONNABORTED') {
        throw new Error(
          'Connection timed out. The server is taking too long to respond. Please try again later.'
        )
      } else if (e.code === 'ERR_NETWORK') {
        throw new Error(
          'Network error. Please check your internet connection and try again.'
        )
      }

      throw new Error(e.response?.data?.message || 'Registration failed')
    }
    console.error('Failed to connect to backend:', e)
    throw new Error('Server unreachable. Please try again later.')
  }
}

// Login Service
export async function loginService(value: RequestLogin) {
  try {
    console.log('Attempting login with:', value.email)
    const res = await apiClient.post('/auth/login', value)
    return res.data
  } catch (e) {
    if (axios.isAxiosError(e)) {
      console.error('Login error:', {
        message: e.message,
        response: e.response?.data,
        status: e.response?.status,
        code: e.code,
      })

      if (e.code === 'ECONNABORTED') {
        throw new Error(
          'Connection timed out. The server is taking too long to respond. Please try again later.'
        )
      } else if (e.code === 'ERR_NETWORK') {
        throw new Error(
          'Network error. Please check your internet connection and try again.'
        )
      }

      throw new Error(e.response?.data?.message || 'Login failed')
    }
    console.error('Failed to connect to backend:', e)
    throw new Error('Server unreachable. Please try again later.')
  }
}

// Interface for quiz generation parameters
interface QuizGenerationParams {
  language: string
  inputText: string
  pdfContent?: string
  questionCount: number
  quizType: string
}

// Generate Quiz Prompt for Gemini API
export const generateGeminiPrompt = (params: QuizGenerationParams) => {
  return `Generate a ${params.language} quiz based on the following content. Please follow these specifications:

Content to base the quiz on:
${params.inputText}
${params.pdfContent ? `Additional content from PDF: ${params.pdfContent}` : ''}

Requirements:
- Create exactly ${params.questionCount} ${params.quizType} questions
- For each question, provide:
  * A clear, concise question
  * ${
    params.quizType === 'multiple-choice'
      ? '4 possible answers (1 correct, 3 plausible distractors)'
      : params.quizType === 'true-false'
        ? '2 options (True/False)'
        : 'A specific expected short answer'
  }
  * The correct answer clearly marked
- Ensure questions test understanding rather than mere recall
- Vary the difficulty level
- Use clear, unambiguous language
- Avoid any culturally biased content

Please format the response as a JSON array with the following structure:
{
  "questions": [
    {
      "question": "string",
      "options": ["string", "string", ...],
      "correctAnswer": "string"
    }
  ]
}`
}

// Get All Teachers
export const getAllTeachers = async () => {
  try {
    const response = await apiClient.get('/teacher')
    return response.data
  } catch (error) {
    console.error('Error fetching teachers:', error)
    throw new Error('Failed to fetch teachers')
  }
}

// Add Teacher
export const addTeacher = async (teacherData: any) => {
  try {
    const response = await apiClient.post('/teacher', teacherData)
    return response.data
  } catch (error) {
    console.error('Error adding teacher:', error)
    throw new Error('Failed to add teacher')
  }
}

// Generic Add Function
const addEntity = async (entity: string, data: any) => {
  try {
    const response = await apiClient.post(`/${entity}`, data)
    return response.data
  } catch (error) {
    console.error(`Error adding ${entity}:`, error)
    throw new Error(`Failed to add ${entity}`)
  }
}

// Add Student
export const addStudent = async (studentData: any) =>
  addEntity('student', studentData)

// Add Parent
export const addParent = async (parentData: any) =>
  addEntity('parent', parentData)

// Add Class
export const addClass = async (classData: any) => addEntity('class', classData)

// Add Subject
export const addSubject = async (subjectData: any) =>
  addEntity('subject', subjectData)

export const forgotPasswordService = async (data: { email: string }) => {
  try {
    console.log('Sending request to:', `${BASE_URL}/auth/forgot-password`)
    const res = await apiClient.post('/auth/forgot-password', data)
    return res.data
  } catch (error) {
    console.error('Password reset error details:', error)
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        throw new Error(
          'Connection timed out. The server is taking too long to respond. Please try again later.'
        )
      } else if (error.code === 'ERR_NETWORK') {
        throw new Error(
          'Network error. Please check your internet connection and try again.'
        )
      }
      throw new Error(
        error.response?.data?.message || 'Failed to process request'
      )
    }
    throw error
  }
}

// Reset password service
export const resetPasswordService = async (data: {
  token: string
  password: string
}) => {
  try {
    console.log('Sending reset password request with token:', data.token)
    const res = await apiClient.post('/auth/reset-password', data)
    return res.data
  } catch (error) {
    console.error('Password reset error details:', error)
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        throw new Error(
          'Connection timed out. The server is taking too long to respond. Please try again later.'
        )
      } else if (error.code === 'ERR_NETWORK') {
        throw new Error(
          'Network error. Please check your internet connection and try again.'
        )
      }
      // Handle 401 specifically for invalid tokens
      if (error.response?.status === 401) {
        throw new Error(
          'Invalid or expired reset token. Please request a new password reset.'
        )
      }
      throw new Error(
        error.response?.data?.message || 'Failed to reset password'
      )
    }
    throw error
  }
}
