import { api } from '../api/axios-instance'

/**
 * Verifies a Cloudflare Turnstile token by sending it to our backend API
 * @param token - The token to verify
 * @param secret - The Cloudflare Turnstile secret key (not used in backend verification)
 * @returns Promise<boolean> - Whether the token is valid
 */
export async function verifyCloudflareToken(token: string, _secret: string): Promise<boolean> {
  if (!token) {
    console.error('No token provided for Cloudflare verification')
    return false
  }

  console.log('Verifying Cloudflare token:', token.substring(0, 10) + '...')

  try {
    // Instead of calling Cloudflare directly (which causes CORS issues),
    // we'll send the token to our backend API which will verify it
    const response = await api.post('/b2c-client/verify-captcha', {
      token: token
    })

    const { success } = response.data
    console.log('Cloudflare verification response from backend:', response.data)

    if (!success) {
      console.error('Cloudflare verification failed:', response.data.message)
      return false
    }

    console.log('Cloudflare verification successful')
    return true
  } catch (error) {
    console.error('Error verifying Cloudflare token:', error)
    return false
  }
}