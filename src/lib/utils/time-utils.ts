/**
 * Utility functions for handling time-related operations
 */

/**
 * Parse a timeSlot string into startTime and endTime
 * @param timeSlot - The timeSlot string (e.g., "09:00 - 10:30" or "Period 1 (08:30-09:15)")
 * @returns An object with startTime and endTime strings
 */
export function parseTimeSlot(timeSlot: string | undefined): { startTime: string; endTime: string } {
  if (!timeSlot) {
    return { startTime: '', endTime: '' };
  }

  // Try to extract times using different patterns
  
  // Pattern 1: "09:00 - 10:30" or "09:00-10:30"
  const simplePattern = /(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})/;
  const simpleMatch = timeSlot.match(simplePattern);
  
  if (simpleMatch) {
    return {
      startTime: simpleMatch[1],
      endTime: simpleMatch[2]
    };
  }
  
  // Pattern 2: "Period 1 (08:30-09:15)" or similar with parentheses
  const parenthesesPattern = /\((\d{1,2}:\d{2})-(\d{1,2}:\d{2})\)/;
  const parenthesesMatch = timeSlot.match(parenthesesPattern);
  
  if (parenthesesMatch) {
    return {
      startTime: parenthesesMatch[1],
      endTime: parenthesesMatch[2]
    };
  }
  
  // If no pattern matches, return the original timeSlot as both values
  return {
    startTime: timeSlot,
    endTime: ''
  };
}

/**
 * Format a time string to ensure consistent display
 * @param timeStr - The time string to format
 * @param use24Hour - Whether to use 24-hour format (default: true)
 * @returns Formatted time string
 */
export function formatTime(timeStr: string, use24Hour: boolean = true): string {
  if (!timeStr) return '';

  // If already in the right format, return as is
  if (/^\d{1,2}:\d{2}$/.test(timeStr)) {
    if (use24Hour) {
      return timeStr;
    } else {
      // Convert to 12-hour format
      const [hours, minutes] = timeStr.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM
      return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
    }
  }

  return timeStr;
}

/**
 * Convert time string to minutes for easier comparison
 * @param timeStr - Time string in HH:MM format
 * @returns Number of minutes since midnight, or -1 if invalid
 */
export function timeToMinutes(timeStr: string): number {
  if (!timeStr || !/^\d{1,2}:\d{2}$/.test(timeStr)) {
    return -1;
  }

  const [hours, minutes] = timeStr.split(':').map(Number);

  // Validate hours and minutes
  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return -1;
  }

  return hours * 60 + minutes;
}

/**
 * Compare two time strings
 * @param timeA - First time string
 * @param timeB - Second time string
 * @returns -1 if timeA < timeB, 1 if timeA > timeB, 0 if equal
 */
export function compareTime(timeA: string, timeB: string): number {
  const minutesA = timeToMinutes(timeA);
  const minutesB = timeToMinutes(timeB);

  if (minutesA === -1 && minutesB === -1) return 0;
  if (minutesA === -1) return 1; // Invalid times go to end
  if (minutesB === -1) return -1;

  return minutesA - minutesB;
}
