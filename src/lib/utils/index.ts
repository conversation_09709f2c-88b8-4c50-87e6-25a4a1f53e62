import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Combines class names using clsx and tailwind-merge
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formats a date string to a localized format
 */
export function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

/**
 * Formats a number to a fixed decimal places
 */
export function formatNumber(number: number, decimals = 2) {
  return Number(number).toFixed(decimals)
}

/**
 * Calculates the average of an array of numbers
 */
export function calculateAverage(numbers: number[]) {
  if (numbers.length === 0) return 0
  const sum = numbers.reduce((acc, curr) => acc + curr, 0)
  return sum / numbers.length
}
