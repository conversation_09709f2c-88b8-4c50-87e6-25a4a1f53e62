/**
 * Environment variables utility
 * 
 * This file provides a centralized way to access environment variables
 * with proper fallbacks and type checking.
 */

/**
 * Cloudflare Turnstile site key (public)
 * This is used in the frontend to render the captcha widget
 */
export const CLOUDFLARE_SITE_KEY = 
  import.meta.env.VITE_CLOUDFLARE_SITE_KEY || '0x4AAAAAABd-mE5vPUgrVDgc'

/**
 * Cloudflare Turnstile secret key (private)
 * This is used in the backend to verify the captcha token
 * Note: In production, this should be set as an environment variable
 */
export const CLOUDFLARE_SECRET_KEY = 
  import.meta.env.VITE_CLOUDFLARE_SECRET_KEY || '0x4AAAAAABd-mFgQUv2RLdMjG_szYKVosxg'

/**
 * API URL
 * The base URL for API requests
 */
export const API_URL = 
  import.meta.env.VITE_API_URL || 'http://localhost:3000'

/**
 * CDN URL
 * The base URL for CDN requests
 */
export const CDN_BASE_URL = 
  import.meta.env.VITE_CDN_BASE_URL || 'https://cdn.jeridschool.tech/cdn'

/**
 * CDN Upload URL
 * The URL for uploading files to the CDN
 */
export const CDN_UPLOAD_URL = 
  import.meta.env.VITE_CDN_UPLOAD_URL || 'https://cdn.jeridschool.tech/upload'

/**
 * CDN Token
 * The authentication token for CDN requests
 */
export const CDN_TOKEN = 
  import.meta.env.VITE_CDN_TOKEN || '5ffaccd4ae0ee9afc5077f30ee32264dab8ca247d86eaed2b42d4dac1e954d9d'

/**
 * Development mode flag
 */
export const IS_DEV = import.meta.env.DEV === true

/**
 * Production mode flag
 */
export const IS_PROD = import.meta.env.PROD === true
