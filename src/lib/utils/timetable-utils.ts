import { ExternalTimetableParams } from '../api/services/timetable-service'

/**
 * Converts time from HH:MM format to minutes
 */
export const convertTimeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

/**
 * Formats minutes back to HH:MM format
 */
export const formatMinutesToTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * Converts time slots from the UI format to the external API format
 */
export const convertTimeSlotsForExternalAPI = (
  timeslots: any[]
): ExternalTimetableParams['timeSlots'] => {
  // Group timeslots by day
  const groupedByDay = timeslots.reduce(
    (acc, slot) => {
      if (!acc[slot.day]) {
        acc[slot.day] = {
          studyTimes: [],
          restTime: '',
        }
      }

      // Only include slots with valid start and end times
      if (slot.start && slot.end && slot.start.trim() && slot.end.trim()) {
        // Format time as "HH:MM => HH:MM"
        const timeRange = `${slot.start} => ${slot.end}`

        if (slot.isBreak) {
          // Use the longest break as the rest time
          if (
            !acc[slot.day].restTime ||
            convertTimeToMinutes(slot.end) - convertTimeToMinutes(slot.start) >
              convertTimeToMinutes(
                acc[slot.day].restTime.split(' => ')[1] || '0'
              ) -
                convertTimeToMinutes(
                  acc[slot.day].restTime.split(' => ')[0] || '0'
                )
          ) {
            acc[slot.day].restTime = timeRange
          }
        } else {
          acc[slot.day].studyTimes.push(timeRange)
        }
      }

      return acc
    },
    {} as ExternalTimetableParams['timeSlots']
  )

  // Ensure all days have a rest time
  Object.keys(groupedByDay).forEach((day) => {
    if (!groupedByDay[day].restTime) {
      // If no break is defined, use a default rest time
      groupedByDay[day].restTime = '13:30 => 15:30'
    }
  })

  return groupedByDay
}

/**
 * Prepares data for the external timetable API
 */
export const prepareExternalTimetableData = (
  timeslots: any[],
  classes: any[],
  teachers: any[],
  rooms: string[]
): ExternalTimetableParams => {
  return {
    timeSlots: convertTimeSlotsForExternalAPI(timeslots),
    salles: rooms.map((room) => ({ name: room, type: 'classroom' })),
    classes: classes.map((cls) => ({
      className: cls.name,
      subjects: cls.subjects.map((subj: any) => ({
        subject: subj.name,
        hours: subj.periodsPerWeek || 2,
        teacherID: parseInt(subj.teacherId) || 1,
        divide: subj.divide,
      })),
    })),
    teachers: teachers.map((teacher) => ({
      teacherId: parseInt(teacher.id) || 1,
      teacherName: teacher.name,
      subjects: teacher.subjects || [],
      unavailableTimes: teacher.unavailableTimes || {},
      minimumHours: teacher.minimumHours || 8,
      maximumHours: teacher.maximumHours || 20,
    })),
  }
}
