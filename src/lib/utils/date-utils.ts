import { format, parseISO, isValid } from 'date-fns'

/**
 * Format a date for API requests (YYYY-MM-DD)
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDateForApi(date: Date | null | undefined): string {
  if (!date || !isValid(date)) return ''
  return format(date, 'yyyy-MM-dd')
}

/**
 * Format a date with time for API requests (YYYY-MM-DD HH:mm:ss)
 * @param date - Date to format
 * @returns Formatted date-time string
 */
export function formatDateTimeForApi(date: Date | null | undefined): string {
  if (!date || !isValid(date)) return ''
  return format(date, 'yyyy-MM-dd HH:mm:ss')
}

/**
 * Format a date for display (Month Day, Year)
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDateForDisplay(date: Date | string | null | undefined): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return ''
  
  return format(dateObj, 'MMMM d, yyyy')
}

/**
 * Format a date with time for display (Month Day, Year at HH:MM AM/PM)
 * @param date - Date to format
 * @returns Formatted date-time string
 */
export function formatDateTimeForDisplay(date: Date | string | null | undefined): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return ''
  
  return format(dateObj, 'MMMM d, yyyy \'at\' h:mm a')
}

/**
 * Format a date as a short date (MM/DD/YYYY)
 * @param date - Date to format
 * @returns Formatted short date string
 */
export function formatShortDate(date: Date | string | null | undefined): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return ''
  
  return format(dateObj, 'MM/dd/yyyy')
}

/**
 * Parse a date string to a Date object
 * @param dateString - Date string to parse
 * @returns Date object or null if invalid
 */
export function parseDate(dateString: string | null | undefined): Date | null {
  if (!dateString) return null
  
  const date = parseISO(dateString)
  return isValid(date) ? date : null
}

/**
 * Get the current date formatted for API
 * @returns Current date formatted for API
 */
export function getCurrentDateForApi(): string {
  return formatDateForApi(new Date())
}

/**
 * Check if a date is in the past
 * @param date - Date to check
 * @returns True if date is in the past
 */
export function isDateInPast(date: Date | string | null | undefined): boolean {
  if (!date) return false
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return false
  
  return dateObj < new Date()
}

/**
 * Check if a date is in the future
 * @param date - Date to check
 * @returns True if date is in the future
 */
export function isDateInFuture(date: Date | string | null | undefined): boolean {
  if (!date) return false
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return false
  
  return dateObj > new Date()
}

/**
 * Check if a date is today
 * @param date - Date to check
 * @returns True if date is today
 */
export function isDateToday(date: Date | string | null | undefined): boolean {
  if (!date) return false
  
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  if (!isValid(dateObj)) return false
  
  const today = new Date()
  return (
    dateObj.getDate() === today.getDate() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getFullYear() === today.getFullYear()
  )
}
