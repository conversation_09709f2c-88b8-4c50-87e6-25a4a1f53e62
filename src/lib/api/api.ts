import axios from 'axios'
const BASE_URL = 'http://localhost:3000'

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add timeout to prevent hanging requests
  timeout: 15000,
})
const USE_MOCK_API = false
const mockEmailStore = new Set()

export const services = {
  auth: {
    login: async (data: { email: string; password: string }) => {
      const response = await apiClient.post('/auth/login', data)
      return response.data
    },
    register: async (data: {
      name: string
      email: string
      password: string
      workspaceName: string
    }) => {
      const response = await apiClient.post('/auth/register', data)
      return response.data
    },
    forgotPassword: async (email: string) => {
      if (USE_MOCK_API) {
        console.log('Using mock service for password reset:', email)
        mockEmailStore.add(email)
        await new Promise((resolve) => setTimeout(resolve, 800))
        return {
          success: true,
          message:
            'If your email is registered, you will receive a password reset link',
        }
      }

      // Add actual API implementation
      try {
        const response = await apiClient.post('/auth/forgot-password', {
          email,
        })
        return response.data
      } catch (error) {
        console.error('Forgot password API error:', error)
        if (axios.isAxiosError(error)) {
          if (error.code === 'ERR_NETWORK') {
            throw new Error(
              'Server is currently unavailable. Please try again later.'
            )
          }
          throw new Error(
            error.response?.data?.message ||
              'Failed to send password reset email'
          )
        }
        throw error
      }
    },
    resetPassword: async (token: string, password: string) => {
      try {
        const response = await apiClient.post('/auth/reset-password', {
          token,
          password,
        })
        return response.data
      } catch (error) {
        console.error('Reset password request failed:', error)
        throw error
      }
    },
  },
}

// For the reset password service, also use the multi-URL approach
export const resetPasswordService = async (data: {
  token: string
  password: string
}) => {
  try {
    console.log('Sending reset password request with token:', data.token)
    const res = await apiClient.post('/auth/reset-password', data)
    return res.data
  } catch (error) {
    console.error('Password reset error details:', error)
    if (axios.isAxiosError(error)) {
      if (error.code === 'ERR_NETWORK') {
        throw new Error(
          'Server is currently unavailable. Please try again later.'
        )
      }
      // Handle 401 specifically for invalid tokens
      if (error.response?.status === 401) {
        throw new Error(
          'Invalid or expired reset token. Please request a new password reset.'
        )
      }
      throw new Error(
        error.response?.data?.message || 'Failed to reset password'
      )
    }
    throw error
  }
}
