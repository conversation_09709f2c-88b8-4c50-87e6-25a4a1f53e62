import axios from 'axios'

export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    console.log('Request interceptor - token exists:', !!token)
    console.log('Request URL:', config.url)

    if (token) {
      // Make sure to format the Authorization header correctly
      config.headers.Authorization = `Bearer ${token}`
      console.log(
        'Setting Authorization header:',
        `Bearer ${token.substring(0, 10)}...`
      )
    } else {
      console.warn(
        'No token found in localStorage. User might not be authenticated.'
      )
      // Only warn for protected routes, not for public routes like registration
      const publicRoutes = [
        '/auth/',
        '/b2c-client/register',
        '/b2c-client/verify-captcha',
        '/student/register'
      ]

      const isPublicRoute = publicRoutes.some(route => config.url?.includes(route))

      if (!isPublicRoute) {
        console.warn(
          'Attempting to access protected route without authentication'
        )
      }
    }

    // Log the full request for debugging
    console.log('Full request config:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      baseURL: config.baseURL,
    })

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Helper to recursively fix HTTP/HTTPS URLs
const fixHttpsUrls = (obj: any): any => {
  if (!obj) return obj

  if (typeof obj === 'string') {
    // Special case 1: CDN URLs should use HTTPS
    if (obj.startsWith('http://46.101.199.181')) {
      console.log('Converting HTTP to HTTPS for CDN URL:', obj)
      return obj.replace('http://', 'https://')
    }

    // Special case 2: Dicebear URLs should use HTTPS
    if (obj.includes('api.dicebear.com') && obj.startsWith('http://')) {
      console.log('Converting HTTP to HTTPS for Dicebear URL:', obj)
      return obj.replace('http://', 'https://')
    }
  }

  if (typeof obj === 'object') {
    // Process arrays
    if (Array.isArray(obj)) {
      return obj.map((item) => fixHttpsUrls(item))
    }

    // Process objects
    const newObj = { ...obj }
    for (const key in newObj) {
      newObj[key] = fixHttpsUrls(newObj[key])
    }
    return newObj
  }

  return obj
}

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log('Response successful:', response.status)

    // Fix any HTTPS CDN URLs in the response data
    if (response.data) {
      response.data = fixHttpsUrls(response.data)
    }

    return response
  },
  (error) => {
    console.error('Response error:', error.response?.status, error.message)
    console.error('Error response data:', error.response?.data)
    console.error('Error request URL:', error.config?.url)

    if (error.response?.status === 401) {
      console.log('Unauthorized error detected, redirecting to login')
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    } else if (error.response?.status === 403) {
      console.log('Forbidden error detected, user lacks permission')
      // Check if token exists
      const token = localStorage.getItem('access_token')
      if (!token) {
        console.log('No token found, redirecting to login')
        window.location.href = '/login'
      } else {
        console.log('Token exists but user lacks permission')
        // You could show a toast notification here
      }
    }

    return Promise.reject(error)
  }
)

export default api
