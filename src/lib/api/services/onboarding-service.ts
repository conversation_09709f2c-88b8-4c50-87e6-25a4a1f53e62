import { api } from '@/lib/api/axios-instance'

/**
 * Service for handling onboarding-related API calls
 */
export const onboardingService = {
  /**
   * Update the current user's onboarding status
   * @param isCompleted Whether onboarding is completed (true = completed, false = needs onboarding)
   * @returns Promise with the response data
   */
  updateOnboardingStatus: async (isCompleted: boolean): Promise<any> => {
    try {
      console.log(
        'onboardingService: Updating onboarding status in database:',
        isCompleted
      )

      // Use the correct endpoint and field name as per documentation
      // Note: API expects userOnboarding: false to mark onboarding as complete
      const response = await api.patch('/user-controller/update-onboarding', {
        userOnboarding: !isCompleted,
      })

      console.log(
        'onboardingService: Onboarding status updated successfully:',
        response.data
      )

      return response.data
    } catch (error) {
      console.error(
        'onboardingService: Failed to update onboarding status:',
        error
      )
      throw error
    }
  },
}
