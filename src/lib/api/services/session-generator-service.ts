import { api } from '../axios-instance'
import { Session } from './admin-session-service'
import { attendanceTimetableService, AttendanceTimetable } from './attendance-timetable-service'

export interface GenerateSessionsRequest {
  startDate: string
  endDate: string
  timetableId?: string
  useTeacherNames?: boolean
}

export interface GenerateTeacherSessionsRequest {
  teacherId: string
  startDate: string
  endDate: string
  timetableId?: string
}

/**
 * Session Generator Service
 * Handles generating sessions from timetables
 */
export const sessionGeneratorService = {
  /**
   * Generate sessions for all teachers
   * @param {GenerateSessionsRequest} request - Generation request parameters
   * @returns {Promise<Session[]>} Generated sessions
   */
  generateForAllTeachers: async (request: GenerateSessionsRequest): Promise<Session[]> => {
    try {
      // Explicitly set useTeacherNames to false to avoid metadata issues
      const requestWithTeacherNames = {
        ...request,
        useTeacherNames: false
      };

      const response = await api.post<Session[]>('/sessions/generate-all', requestWithTeacherNames)
      return response.data
    } catch (error) {
      console.error('Error generating sessions for all teachers:', error)
      throw error
    }
  },

  /**
   * Generate sessions for a specific teacher
   * @param {GenerateTeacherSessionsRequest} request - Generation request parameters
   * @returns {Promise<Session[]>} Generated sessions
   */
  generateForTeacher: async (request: GenerateTeacherSessionsRequest): Promise<Session[]> => {
    try {
      const response = await api.post<Session[]>('/sessions/generate', {
        teacherId: request.teacherId,
        startDate: request.startDate,
        endDate: request.endDate,
        timetableId: request.timetableId
      })
      return response.data
    } catch (error) {
      console.error(`Error generating sessions for teacher ${request.teacherId}:`, error)
      throw error
    }
  },

  /**
   * Get active timetables
   * @returns {Promise<AttendanceTimetable[]>} Active timetables
   */
  getActiveTimetables: async (): Promise<AttendanceTimetable[]> => {
    try {
      // Use the attendanceTimetableService to fetch the active timetable
      const activeTimetable = await attendanceTimetableService.fetchActiveTimetable();
      return [activeTimetable];
    } catch (error) {
      console.error('Error fetching active timetable:', error);

      // Try the old endpoint as a fallback
      try {
        const response = await api.get<AttendanceTimetable[]>('/timetable/active');
        return Array.isArray(response.data) ? response.data : [response.data];
      } catch (fallbackError) {
        console.error('Error fetching from fallback endpoint:', fallbackError);
        return [];
      }
    }
  },

  /**
   * Get all available timetables
   * @returns {Promise<AttendanceTimetable[]>} All timetables
   */
  getAllTimetables: async (): Promise<AttendanceTimetable[]> => {
    try {
      return await attendanceTimetableService.fetchTimetables();
    } catch (error) {
      console.error('Error fetching all timetables:', error);
      return [];
    }
  }
}
