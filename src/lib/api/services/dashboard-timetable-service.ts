import { api } from '../axios-instance'
import { AxiosError } from 'axios'

export interface Timetable {
  id: string
  data: any
  description?: string
  academicYear?: string
  isActive: boolean
  etablissementId: string
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export const dashboardTimetableService = {
  // Get all timetables
  getAllTimetables: async (): Promise<Timetable[]> => {
    try {
      console.log('Fetching timetables for current establishment')
      // Get the token for logging purposes
      const token = localStorage.getItem('access_token')
      console.log(
        'Using token for fetching:',
        token ? 'Token exists' : 'No token'
      )

      // Try the establishment-specific endpoint first (works for ADMIN and TEACHER roles)
      try {
        console.log('Trying /timetable/etablissement endpoint')
        const response = await api.get('/timetable/etablissement')
        console.log(
          'Timetables fetched successfully from etablissement endpoint'
        )

        // If the response is a single object, wrap it in an array
        const timetables = Array.isArray(response.data)
          ? response.data
          : [response.data]

        console.log(`Found ${timetables.length} timetables`)
        return timetables
      } catch (etablissementError) {
        console.log(
          'Failed with /timetable/etablissement, trying other endpoints'
        )
        console.error('Error details:', etablissementError)

        // Try with /api prefix next
        try {
          console.log('Trying /api/timetable endpoint')
          const response = await api.get('/api/timetable')
          console.log('Timetables fetched successfully with /api prefix')
          return Array.isArray(response.data) ? response.data : [response.data]
        } catch (prefixError) {
          console.log('Failed with /api prefix, trying without prefix')
          // If that fails, try without the /api prefix
          const response = await api.get('/timetable')
          console.log('Timetables fetched successfully without /api prefix')
          return Array.isArray(response.data) ? response.data : [response.data]
        }
      }
    } catch (error) {
      console.error('Error fetching timetables:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get timetable for current establishment
  getCurrentEstablishmentTimetables: async (): Promise<Timetable[]> => {
    try {
      console.log('Fetching timetables for current establishment')

      // Get the token for logging purposes
      const token = localStorage.getItem('access_token')
      console.log(
        'Using token for fetching:',
        token ? 'Token exists' : 'No token'
      )

      try {
        // Try the /timetable/etablissement endpoint first
        const response = await api.get('/timetable/etablissement')
        console.log(
          'Timetables fetched successfully, count:',
          Array.isArray(response.data) ? response.data.length : 1
        )

        // If the response is a single object, wrap it in an array
        const timetables = Array.isArray(response.data)
          ? response.data
          : [response.data]

        // Log the timetable IDs for debugging
        console.log('Timetable IDs:', timetables.map((t) => t.id).join(', '))

        return timetables
      } catch (etablissementError) {
        console.log(
          'Failed with /timetable/etablissement, trying /timetable endpoint'
        )

        // If the first endpoint fails, try the /timetable endpoint
        try {
          const response = await api.get('/timetable')
          console.log(
            'Timetables fetched successfully from /timetable endpoint'
          )

          // If the response is a single object, wrap it in an array
          const timetables = Array.isArray(response.data)
            ? response.data
            : [response.data]

          return timetables
        } catch (timetableError) {
          // If both endpoints fail, return an empty array instead of throwing
          console.log(
            'Failed to fetch timetables from both endpoints, returning empty array'
          )
          return []
        }
      }
    } catch (error) {
      console.error('Error fetching timetables:', error)
      if (error instanceof AxiosError) {
        console.error('Server response:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        })
      }
      // Return empty array instead of throwing
      return []
    }
  },

  // Get timetable by ID
  getTimetableById: async (id: string): Promise<Timetable> => {
    try {
      const response = await api.get(`/timetable/${id}`)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Delete timetable
  deleteTimetable: async (id: string): Promise<void> => {
    try {
      console.log('Deleting timetable with ID:', id)

      // Get the token for logging purposes
      const token = localStorage.getItem('access_token')
      console.log(
        'Using token for deletion:',
        token ? 'Token exists' : 'No token'
      )

      // Make the delete request to the NestJS endpoint
      const response = await api.delete(`/timetable/${id}`)
      console.log(
        'Timetable deleted successfully, server response:',
        response.status
      )
    } catch (error) {
      console.error('Error deleting timetable:', error)
      if (error instanceof AxiosError) {
        console.error('Server response:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        })
      }
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Update timetable
  updateTimetable: async (
    id: string,
    data: Partial<Timetable>
  ): Promise<Timetable> => {
    try {
      const response = await api.patch(`/timetable/${id}`, data)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Save timetable according to CreateTimetableDto
  saveTimetable: async (timetableData: {
    data: any
    description?: string
    academicYear?: string
    isActive?: boolean
    inputData?: any // This is the input data sent to timetable.jeridschool.tech
  }): Promise<Timetable> => {
    try {
      console.log(
        'Saving timetable with description:',
        timetableData.description
      )
      console.log(
        'Timetable data size:',
        JSON.stringify(timetableData.data).length,
        'characters'
      )

      // Get the token for logging purposes
      const token = localStorage.getItem('access_token')
      console.log(
        'Using token for saving timetable:',
        token ? 'Token exists' : 'No token'
      )

      // Make sure inputData is explicitly included in the request
      if (timetableData.inputData) {
        console.log(
          'InputData is present in the request, ensuring it will be saved'
        )
        // Log a sample of the inputData to verify its content
        console.log(
          'InputData sample:',
          JSON.stringify(timetableData.inputData).substring(0, 100) + '...'
        )
      } else if (timetableData.data && timetableData.data.data) {
        console.log('InputData found in data.data, will use this as inputData')
        // Create inputData from data.data
        timetableData.inputData = timetableData.data.data
        console.log(
          'Created inputData sample:',
          JSON.stringify(timetableData.inputData).substring(0, 100) + '...'
        )
      } else {
        console.log(
          'No inputData found in the request, this may cause issues with the analyze feature'
        )
      }

      // First, try to get existing timetables
      try {
        const existingTimetables =
          await dashboardTimetableService.getCurrentEstablishmentTimetables()
        const existingTimetable = existingTimetables[0] // Get the first one if it exists

        if (existingTimetable) {
          console.log(
            'Found existing timetable, updating instead of creating new one'
          )
          // Update the existing timetable - ensure inputData is included
          const requestData = {
            ...timetableData,
            // Make sure inputData is explicitly included even for PATCH requests
            inputData:
              timetableData.inputData ||
              (timetableData.data && timetableData.data.data),
          }
          const response = await api.patch(
            `/timetable/${existingTimetable.id}`,
            requestData
          )
          console.log('Timetable updated successfully, response:', {
            id: response.data.id,
            status: response.status,
            statusText: response.statusText,
            // Check if inputData was included in the response
            hasInputData: response.data.inputData ? 'YES' : 'NO',
          })

          // Verify that inputData was included in the request
          console.log(
            'InputData was included in the PATCH request:',
            requestData.inputData ? 'YES' : 'NO'
          )
          return response.data
        }
      } catch (error) {
        console.log('Error checking for existing timetables:', error)
        // Continue with create attempt even if check fails
      }

      // If no existing timetable or check failed, try to create new one
      // Ensure inputData is included in the request
      const requestData = {
        ...timetableData,
        // Make sure inputData is explicitly included for POST requests
        inputData:
          timetableData.inputData ||
          (timetableData.data && timetableData.data.data),
      }
      const response = await api.post('/timetable', requestData)

      console.log('Timetable saved successfully, response:', {
        id: response.data.id,
        status: response.status,
        statusText: response.statusText,
        // Check if inputData was included in the response
        hasInputData: response.data.inputData ? 'YES' : 'NO',
      })

      // Verify that inputData was included in the request
      console.log(
        'InputData was included in the request:',
        requestData.inputData ? 'YES' : 'NO'
      )

      return response.data
    } catch (error) {
      console.error('Error saving timetable:', error)

      if (error instanceof AxiosError) {
        console.error('Server response details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
        })

        // Try to provide more specific error information
        if (error.response?.status === 401) {
          console.error('Authentication error: Token may be invalid or expired')
        } else if (error.response?.status === 403) {
          console.error(
            'Authorization error: User may not have permission to create timetables'
          )
        } else if (error.response?.status === 400) {
          console.error('Bad request error: The timetable data may be invalid')
          // If it's a duplicate error, try to update instead
          if (error.response?.data?.message?.includes('already exists')) {
            console.log(
              'Timetable already exists, attempting to update instead'
            )
            // Get existing timetables and update the first one
            const existingTimetables =
              await dashboardTimetableService.getCurrentEstablishmentTimetables()
            const existingTimetable = existingTimetables[0]
            if (existingTimetable) {
              // Ensure inputData is included in the request
              const requestData = {
                ...timetableData,
                // Make sure inputData is explicitly included for PATCH requests
                inputData:
                  timetableData.inputData ||
                  (timetableData.data && timetableData.data.data),
              }
              const updateResponse = await api.patch(
                `/timetable/${existingTimetable.id}`,
                requestData
              )

              // Verify that inputData was included in the fallback update
              console.log('Fallback update successful, response:', {
                id: updateResponse.data.id,
                hasInputData: updateResponse.data.inputData ? 'YES' : 'NO',
              })
              console.log(
                'InputData was included in the fallback PATCH request:',
                requestData.inputData ? 'YES' : 'NO'
              )
              return updateResponse.data
            }
          }
        }

        throw error.response?.data || error
      }

      throw error
    }
  },
}
