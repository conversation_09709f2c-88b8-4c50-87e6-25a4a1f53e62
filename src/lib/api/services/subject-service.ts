import { Subject, CreateSubjectDTO, UpdateSubjectDTO } from '@/interface/types'
import { api } from '../axios-instance'

export const subjectService = {
  getAll: async () => {
    try {
      const response = await api.get<Subject[]>('/subject')
      return response.data
    } catch (error) {
      console.error('Error fetching subjects:', error)
      return []
    }
  },

  getById: async (id: string) => {
    const response = await api.get<Subject>(`/subject/${id}`)
    return response.data
  },

  create: async (data: CreateSubjectDTO | Partial<Subject>) => {
    try {
      console.log('Creating subject with data:', JSON.stringify(data, null, 2))

      // Try with singular endpoint first
      try {
        const response = await api.post<Subject>('/subject', data)
        return response.data
      } catch (firstError) {
        console.log('First attempt failed, trying plural endpoint:', firstError)
        // If that fails, try with plural endpoint
        const response = await api.post<Subject>('/subjects', data)
        return response.data
      }
    } catch (error) {
      console.error('Error creating subject:', error)
      throw error
    }
  },

  update: async (id: string, data: UpdateSubjectDTO | Partial<Subject>) => {
    try {
      const response = await api.patch<Subject>(`/subject/${id}`, data)
      return response.data
    } catch (error) {
      console.error('Error updating subject:', error)
      throw error
    }
  },

  delete: async (id: string) => {
    try {
      await api.delete(`/subject/${id}`)
    } catch (error) {
      console.error('Error deleting subject:', error)
      throw error
    }
  },

  updateFormulas: async (subjects: Subject[]) => {
    const response = await api.patch<Subject[]>('/subject/formulas', subjects)
    return response.data
  },
}
