import { api } from '@/lib/axios'
import { format } from 'date-fns'

// Types based on the backend API documentation
export interface TeacherSession {
  id: string
  teacherId: string
  teacherName: string
  className: string
  subjectName: string
  day: string
  timeSlot: string
  date: string
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled' | 'pending' | 'not_reported'
  location?: string
  notes?: string
  totalStudents?: number
  presentStudents?: number
  absentStudents?: number
  attendanceRate?: number
}

export interface SessionStudent {
  id: string
  firstName: string
  lastName: string
  rollNumber?: string
  status: 'present' | 'absent' | 'late' | 'excused'
  notes?: string
}

export interface AttendanceRecord {
  studentId: string
  status: 'present' | 'absent' | 'late' | 'excused'
  notes?: string
}

export interface SessionAttendanceData {
  sessionId: string
  className: string
  subject: string
  day: string
  timeSlot: string
  date: string
  records: AttendanceRecord[]
}

export interface TeacherDashboardStats {
  todaySessions: number
  totalSessions: number
  averageAttendance: number
  studentsPresent: number
  studentsAbsent: number
  upcomingSessions: TeacherSession[]
  recentSessions: TeacherSession[]
}

export interface AttendanceHistoryRecord {
  id: string
  sessionId: string
  className: string
  subjectName: string
  date: string
  timeSlot: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  lateStudents: number
  attendanceRate: number
  status: 'completed' | 'cancelled'
}

export interface ReportsData {
  period: string
  totalSessions: number
  averageAttendance: number
  bestPerformingClass: string
  worstPerformingClass: string
  totalStudents: number
  classReports: ClassReport[]
  monthlyTrends: MonthlyTrend[]
}

export interface ClassReport {
  className: string
  totalSessions: number
  averageAttendance: number
  totalStudents: number
  presentStudents: number
  absentStudents: number
  trend: 'up' | 'down' | 'stable'
}

export interface MonthlyTrend {
  month: string
  attendance: number
  sessions: number
}

/**
 * Teacher Attendance Service
 * Handles all teacher attendance-related API calls
 */
export const teacherAttendanceService = {
  /**
   * Get all sessions for the current teacher
   */
  getSessions: async (): Promise<TeacherSession[]> => {
    try {
      console.log('🚀 teacherAttendanceService.getSessions called - making API call to /sessions')
      const response = await api.get<TeacherSession[]>('/sessions')
      console.log('🚀 teacherAttendanceService.getSessions response:', response.data)

      // Ensure we have an array and filter out any invalid entries
      const sessions = Array.isArray(response.data) ? response.data : []
      const validSessions = sessions.filter(session => session && typeof session === 'object')

      console.log('🚀 teacherAttendanceService.getSessions returning:', validSessions)
      return validSessions
    } catch (error) {
      console.error('🚀 Error fetching teacher sessions:', error)
      throw error
    }
  },

  /**
   * Get sessions by date for the current teacher
   */
  getSessionsByDate: async (date: string): Promise<TeacherSession[]> => {
    try {
      const formattedDate = format(new Date(date), 'yyyy-MM-dd')
      const response = await api.get<TeacherSession[]>(`/sessions/date/${formattedDate}`)
      return response.data || []
    } catch (error) {
      console.error('Error fetching sessions by date:', error)
      throw error
    }
  },

  /**
   * Get current active session for the teacher
   */
  getCurrentSession: async (): Promise<TeacherSession | null> => {
    try {
      const response = await api.get<TeacherSession>('/sessions/current')
      return response.data || null
    } catch (error) {
      console.error('Error fetching current session:', error)
      return null
    }
  },

  /**
   * Get session by ID
   */
  getSessionById: async (sessionId: string): Promise<TeacherSession> => {
    try {
      const response = await api.get<TeacherSession>(`/sessions/${sessionId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching session by ID:', error)
      throw error
    }
  },

  /**
   * Get students for a session
   */
  getSessionStudents: async (sessionId: string): Promise<SessionStudent[]> => {
    try {
      const response = await api.get<SessionStudent[]>(`/sessions/${sessionId}/students`)
      return response.data || []
    } catch (error) {
      console.error('Error fetching session students:', error)
      throw error
    }
  },

  /**
   * Get attendance records for a session
   */
  getSessionAttendance: async (sessionId: string): Promise<AttendanceRecord[]> => {
    try {
      const response = await api.get<AttendanceRecord[]>(`/attendance/by-session/${sessionId}`)
      return response.data || []
    } catch (error) {
      console.error('Error fetching session attendance:', error)
      throw error
    }
  },

  /**
   * Submit attendance for a session
   */
  submitSessionAttendance: async (data: SessionAttendanceData): Promise<any> => {
    try {
      // Use the session-with-session endpoint since we have a sessionId
      const response = await api.post('/attendance/session-with-session', data)
      return response.data
    } catch (error) {
      console.error('Error submitting session attendance:', error)
      throw error
    }
  },

  /**
   * Get dashboard statistics for the teacher
   */
  getDashboardStats: async (): Promise<TeacherDashboardStats> => {
    // Calculate stats directly from sessions since that's the working endpoint
    try {
      const sessions = await teacherAttendanceService.getSessions()
      const today = format(new Date(), 'yyyy-MM-dd')

      const todaySessions = sessions.filter(s => s.date === today)
      const completedSessions = sessions.filter(s => s.status === 'completed')

      const totalPresent = completedSessions.reduce((sum, s) => sum + (s.presentStudents || 0), 0)
      const totalAbsent = completedSessions.reduce((sum, s) => sum + (s.absentStudents || 0), 0)
      const totalStudents = totalPresent + totalAbsent
      const averageAttendance = totalStudents > 0 ? Math.round((totalPresent / totalStudents) * 100) : 0

      const upcomingSessions = sessions
        .filter(s => s.status === 'upcoming')
        .slice(0, 5)

      const recentSessions = sessions
        .filter(s => s.status === 'completed')
        .slice(0, 5)

      return {
        todaySessions: todaySessions.length,
        totalSessions: sessions.length,
        averageAttendance,
        studentsPresent: totalPresent,
        studentsAbsent: totalAbsent,
        upcomingSessions,
        recentSessions
      }
    } catch (error) {
      console.error('Error calculating dashboard stats from sessions:', error)
      throw error
    }
  },

  /**
   * Get attendance history for the teacher
   */
  getAttendanceHistory: async (startDate?: string, endDate?: string): Promise<AttendanceHistoryRecord[]> => {
    try {
      console.log('🔥 teacherAttendanceService.getAttendanceHistory - Making API call to /attendance/teacher/history')

      const params = new URLSearchParams()
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)

      const queryString = params.toString()
      const url = `/attendance/teacher/history${queryString ? `?${queryString}` : ''}`

      const response = await api.get<AttendanceHistoryRecord[]>(url)
      console.log('✅ teacherAttendanceService.getAttendanceHistory - API response:', response.data)

      return response.data
    } catch (error) {
      console.error('❌ Error getting attendance history:', error)
      throw error
    }
  },

  /**
   * Get reports data for the teacher
   */
  getReportsData: async (timeRange: string, classFilter: string): Promise<ReportsData> => {
    try {
      console.log('🔥 teacherAttendanceService.getReportsData - Making API call to /attendance/teacher/reports')

      const params = new URLSearchParams()
      if (timeRange) params.append('timeRange', timeRange)
      if (classFilter && classFilter !== 'all') params.append('classFilter', classFilter)

      const queryString = params.toString()
      const url = `/attendance/teacher/reports${queryString ? `?${queryString}` : ''}`

      const response = await api.get<ReportsData>(url)
      console.log('✅ teacherAttendanceService.getReportsData - API response:', response.data)

      return response.data
    } catch (error) {
      console.error('❌ Error getting reports data:', error)
      throw error
    }
  }
}
