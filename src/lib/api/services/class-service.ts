import { Class, Student, Teacher } from '@/interface/types'
import { api } from '../axios-instance'

export const classService = {
  getAll: async () => {
    const response = await api.get<Class[]>('/class')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<Class>(`/class/${id}`)
    return response.data
  },

  create: async (data: Partial<Class>) => {
    try {
      // Log the data being sent to the API
      console.log('Creating class with data:', JSON.stringify(data, null, 2))

      // Make sure we're only sending the required fields according to the API documentation
      const createData = {
        name: data.name,
        gradeId: data.gradeId,
        supervisorId: data.supervisorId,
      }

      const response = await api.post<Class>('/class', createData)
      console.log('Class created successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error creating class:', error)
      throw error
    }
  },

  update: async (id: string, data: Partial<Class>) => {
    try {
      // Log the data being sent to the API
      console.log(
        `Updating class ${id} with data:`,
        JSON.stringify(data, null, 2)
      )

      // Make sure we're only sending the required fields according to the API documentation
      const updateData = {
        name: data.name,
        gradeId: data.gradeId,
        supervisorId: data.supervisorId,
      }

      // According to the documentation, we should use PATCH instead of PUT
      const response = await api.patch<Class>(`/class/${id}`, updateData)
      console.log('Class updated successfully:', response.data)
      return response.data
    } catch (error) {
      console.error(`Error updating class ${id}:`, error)
      throw error
    }
  },

  delete: async (id: string) => {
    try {
      console.log(`Deleting class with ID: ${id}`)
      await api.delete(`/class/${id}`)
      console.log(`Class ${id} deleted successfully`)
    } catch (error) {
      console.error(`Error deleting class ${id}:`, error)
      throw error
    }
  },

  // Additional class-specific operations
  assignTeacher: async (classId: string, teacherId: string) => {
    const response = await api.post<Class>(
      `/class/${classId}/teacher/${teacherId}`
    )
    return response.data
  },

  removeTeacher: async (classId: string, teacherId: string) => {
    const response = await api.delete(`/class/${classId}/teacher/${teacherId}`)
    return response.data
  },

  getTeachers: async (classId: string) => {
    const response = await api.get<Teacher[]>(`/class/${classId}/teachers`)
    return response.data
  },

  getStudents: async (classId: string) => {
    try {
      // First, check if the classId is a UUID or a string like 'class-4a'
      const isUUID =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          classId
        )

      if (isUUID) {
        try {
          // First try to get the class to get its name
          console.log('Fetching class details to get class name')
          const classResponse = await api.get<Class>(`/class/${classId}`)
          const className = classResponse.data.name

          // Then use the timetable endpoint with the class name
          console.log(
            'Fetching students using /timetable/class-students/:className endpoint'
          )
          const response = await api.get<Student[]>(
            `/timetable/class-students/${encodeURIComponent(className)}`
          )
          return response.data
        } catch (error) {
          console.error('Error fetching class details or students:', error)
          return []
        }
      } else {
        // If it's not a UUID, it's likely a class name ID like 'class-4a'
        // Extract the class name from the ID (e.g., 'class-4a' -> 'Class 4A')
        let className = classId.replace(/-/g, ' ')
        // Capitalize first letter of each word
        className = className.replace(/\b\w/g, (c) => c.toUpperCase())

        console.log(
          'Fetching students using timetable endpoint with class name:',
          className
        )

        try {
          // Use the timetable endpoint with the class name
          const response = await api.get<Student[]>(
            `/timetable/class-students/${encodeURIComponent(className)}`
          )
          return response.data
        } catch (error) {
          console.error('Error fetching students by class name:', error)
          return []
        }
      }
    } catch (error) {
      console.error('Error fetching students by class ID:', error)
      return []
    }
  },
}
