import { api } from '../axios-instance'

export const userService = {
  /**
   * Update the current user's profile
   * @param data The user data to update
   * @returns Promise with the response data
   */
  updateProfile: async (data: any): Promise<any> => {
    try {
      const response = await api.patch('/users/profile', data)
      return response.data
    } catch (error) {
      console.error('Failed to update user profile:', error)
      throw error
    }
  },

  /**
   * Update the current user's profile using the new updateprofile endpoint
   * @param data The user data to update including address and phone
   * @returns Promise with the updated user data
   */
  updateUserProfile: async (data: {
    firstname?: string
    lastname?: string
    email?: string
    cin?: string
    birthday?: string
    gender?: 'MALE' | 'FEMALE'
    address?: string
    phone?: string
    avatar?: string
    password?: string
  }): Promise<any> => {
    try {
      console.log('Updating user profile with data:', data)
      const response = await api.patch('/user-controller/updateprofile', data)
      console.log('Profile update response:', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to update user profile:', error)
      throw error
    }
  },

  /**
   * Update the current user's onboarding status
   * @param isCompleted Whether onboarding is completed
   * @returns Promise with the response data
   */
  updateOnboardingStatus: async (isCompleted: boolean): Promise<any> => {
    try {
      console.log(
        'userService: Updating onboarding status in database:',
        isCompleted
      )

      // Use the correct endpoint and field name as per documentation
      // Note: API uses opposite logic (true = needs onboarding, false = completed onboarding)
      // Database stores boolean values as uppercase "TRUE" or "FALSE" strings
      const response = await api.patch('/user-controller/update-onboarding', {
        userOnboarding: !isCompleted,
      })

      console.log(
        'userService: Onboarding status updated successfully:',
        response.data
      )

      // Get the user ID from localStorage
      const userId = localStorage.getItem('id')

      // Also update the user profile to ensure consistency
      if (userId) {
        try {
          await api.patch('/user-controller/updateme', {
            userOnboarding: !isCompleted,
          })
          console.log(
            'userService: User profile onboarding status also updated'
          )
        } catch (profileError) {
          console.error(
            'userService: Failed to update profile onboarding status:',
            profileError
          )
          // Continue even if this fails
        }
      }

      return response.data
    } catch (error) {
      console.error('userService: Failed to update onboarding status:', error)
      throw error
    }
  },

  /**
   * Update the current user's profile using the updateme endpoint
   * @param data The user data to update
   * @returns Promise with the updated user data
   */
  updateMe: async (data: {
    firstname?: string
    lastname?: string
    email?: string
    cin?: string
    birthday?: string
    gender?: 'MALE' | 'FEMALE'
    address?: string
    phone?: string
    avatar?: string
    password?: string
    userOnboarding?: boolean
  }): Promise<any> => {
    try {
      console.log('Updating user profile with data:', data)
      const response = await api.patch('/user-controller/updateme', data)
      console.log('Profile update response:', response.data)
      return response.data
    } catch (error) {
      console.error('Failed to update user profile:', error)
      throw error
    }
  },
}

export default userService
