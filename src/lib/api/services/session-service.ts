import { api } from '../axios-instance'

export enum SessionStatus {
  ONGOING = 'ongoing',
  REPORTED = 'reported',
  CANCELED = 'canceled',
}

export interface Session {
  id: string
  teacherId: string
  teacherName: string
  className: string
  classId?: string
  subjectName: string
  subjectId?: string
  day: string
  timeSlot: string
  date: string
  status: SessionStatus
  notes?: string
  timetableEntryId?: string
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export interface GenerateSessionsRequest {
  timetableId: string
  startDate: string
  endDate: string
  useTeacherNames?: boolean
}

export const sessionService = {
  // Get all sessions (admin)
  getAll: async () => {
    try {
      const response = await api.get<Session[]>('/sessions')
      return response.data
    } catch (error) {
      console.error('Error fetching sessions:', error)
      throw error
    }
  },

  // Get sessions by status
  getByStatus: async (status: SessionStatus) => {
    try {
      const response = await api.get<Session[]>(`/sessions/status/${status}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching ${status} sessions:`, error)
      throw error
    }
  },

  // Get sessions for a date range
  getByDateRange: async (startDate: string, endDate: string) => {
    try {
      const response = await api.get<Session[]>(
        `/sessions/date-range?startDate=${startDate}&endDate=${endDate}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching sessions by date range:', error)
      throw error
    }
  },

  // Get sessions for a specific date
  getByDate: async (date: string) => {
    try {
      const response = await api.get<Session[]>(`/sessions/date/${date}`)
      return response.data
    } catch (error) {
      console.error('Error fetching sessions for date:', error)
      throw error
    }
  },

  // Get sessions for a specific class
  getByClass: async (classId: string) => {
    try {
      const response = await api.get<Session[]>(`/sessions/class/${classId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching sessions for class:', error)
      throw error
    }
  },

  // Get sessions for a specific class in a date range
  getByClassAndDateRange: async (
    classId: string,
    startDate: string,
    endDate: string
  ) => {
    try {
      const response = await api.get<Session[]>(
        `/sessions/class/${classId}/date-range?startDate=${startDate}&endDate=${endDate}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching sessions for class by date range:', error)
      throw error
    }
  },

  // Update session status
  updateStatus: async (id: string, status: SessionStatus) => {
    try {
      const response = await api.patch<Session>(`/sessions/${id}/status`, {
        status,
      })
      return response.data
    } catch (error) {
      console.error('Error updating session status:', error)
      throw error
    }
  },

  // Generate sessions from timetable for all teachers
  generateSessions: async (data: GenerateSessionsRequest) => {
    try {
      const response = await api.post<Session[]>('/sessions/generate-all', data)
      return response.data
    } catch (error) {
      console.error('Error generating sessions:', error)
      throw error
    }
  },

  // Get current active session for teacher
  getCurrentSession: async () => {
    try {
      const response = await api.get<Session>('/sessions/current')
      return response.data
    } catch (error) {
      console.error('Error fetching current session:', error)
      return null
    }
  },

  // Create a new session
  create: async (sessionData: Partial<Session>) => {
    try {
      const response = await api.post<Session>('/sessions', sessionData)
      return response.data
    } catch (error) {
      console.error('Error creating session:', error)
      throw error
    }
  },

  // Update a session
  update: async (id: string, sessionData: Partial<Session>) => {
    try {
      const response = await api.patch<Session>(`/sessions/${id}`, sessionData)
      return response.data
    } catch (error) {
      console.error('Error updating session:', error)
      throw error
    }
  },

  // Delete a session
  delete: async (id: string) => {
    try {
      await api.delete(`/sessions/${id}`)
    } catch (error) {
      console.error('Error deleting session:', error)
      throw error
    }
  },

  // Generate sessions for the current teacher
  generateSessionsForTeacher: async (startDate: string, endDate: string) => {
    try {
      console.log(
        `Generating sessions for current teacher from ${startDate} to ${endDate}`
      )
      const response = await api.post<Session[]>(
        '/sessions/generate-for-teacher',
        {
          startDate,
          endDate,
        }
      )
      console.log(
        `Generated ${response.data.length} sessions for current teacher`
      )
      return response.data
    } catch (error) {
      console.error('Error generating sessions for teacher:', error)
      throw error
    }
  },

  // Get sessions for a student
  getSessionsForStudent: async (classId: string, date?: string) => {
    try {
      let url = `/sessions/student?classId=${classId}`
      if (date) {
        url += `&date=${date}`
      }
      console.log(
        `Fetching sessions for student in class ${classId}${date ? ` on ${date}` : ''}`
      )
      const response = await api.get<Session[]>(url)
      console.log(`Found ${response.data.length} sessions for student`)
      return response.data
    } catch (error) {
      console.error('Error fetching sessions for student:', error)
      throw error
    }
  },
}
