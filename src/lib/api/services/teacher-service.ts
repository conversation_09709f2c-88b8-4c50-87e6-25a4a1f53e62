import {
  Teacher,
  CreateTeacherDTO,
  UpdateTeacherDTO,
} from '@/interface/types/user'
import { api } from '../axios-instance'

export const teacherService = {
  getAll: async () => {
    const response = await api.get<Teacher[]>('/teacher')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<Teacher>(`/teacher/${id}`)
    return response.data
  },

  create: async (data: CreateTeacherDTO) => {
    const response = await api.post<Teacher>('/teacher', data)
    return response.data
  },

  update: async (id: string, data: UpdateTeacherDTO) => {
    const response = await api.patch<Teacher>(`/teacher/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/teacher/${id}`)
  },

  // Additional teacher-specific operations
  getSessions: async () => {
    try {
      // Use the sessions endpoint which returns all sessions for the current teacher
      const response = await api.get('/sessions')
      console.log('Fetched sessions for teacher:', response.data)
      return response.data || []
    } catch (error) {
      console.error('Error fetching sessions for teacher:', error)
      return [] // Return empty array on error
    }
  },

  getClasses: async (_teacherId: string) => {
    // Parameter not used but kept for API compatibility
    try {
      // Use the sessions endpoint which returns all sessions for the current teacher
      const response = await api.get('/sessions')
      console.log('Fetched sessions for teacher:', response.data)

      // Extract unique class information from the sessions
      const classesMap = new Map()

      if (Array.isArray(response.data)) {
        response.data.forEach((session: any) => {
          // Check for className directly in the session
          if (session.className) {
            // Generate a unique ID for the class based on the className
            // This is needed because the API doesn't provide classId
            const classId = session.className.replace(/\s+/g, '-').toLowerCase()

            classesMap.set(classId, {
              id: classId,
              name: session.className,
              // Add other properties as needed
            })
          } else if (session.class && session.class.id) {
            // If the session has a class object
            classesMap.set(session.class.id, {
              id: session.class.id,
              name: session.class.name,
              grade_id: session.class.grade_id || '',
              grade_name: session.class.grade_name || '',
              // Add other properties as needed
            })
          } else if (session.classId) {
            // If the session has classId property
            classesMap.set(session.classId, {
              id: session.classId,
              name: session.className || `Class ${session.classId}`,
              // Add other properties as needed
            })
          }
        })
      }

      // Convert Map to array
      const classes = Array.from(classesMap.values())
      console.log('Extracted classes from sessions:', classes)

      return classes
    } catch (error) {
      console.error('Error fetching classes for teacher:', error)
      return [] // Return empty array on error
    }
  },

  assignSubject: async (teacherId: string, subjectId: string) => {
    const response = await api.post<Teacher>(
      `/teacher/${teacherId}/subject/${subjectId}`
    )
    return response.data
  },

  removeSubject: async (teacherId: string, subjectId: string) => {
    const response = await api.delete(
      `/teacher/${teacherId}/subject/${subjectId}`
    )
    return response.data
  },
}
