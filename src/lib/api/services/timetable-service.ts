import { api } from '../axios-instance'
import axios, { AxiosError } from 'axios'

// External timetable API URLs
const EXTERNAL_TIMETABLE_API =
  'https://timetable.jeridschool.tech/generate-schedule'
const EXTERNAL_COLOR_SCHEDULE_API =
  'https://timetable.jeridschool.tech/color_schedule'
const EXTERNAL_VALIDATE_HARD_API =
  'https://timetable.jeridschool.tech/validate_hard'
const EXTERNAL_ANALYZE_SOFT_API =
  'https://timetable.jeridschool.tech/analyze_soft'
const EXTERNAL_TIME_SLOTS_API = '/timeslots'

// Save timetable API URL is handled by the axios-instance

export interface TimetableData {
  id?: string
  name: string
  data: any
  constraints: string
  createdAt?: Date
  updatedAt?: Date
}

export interface GenerateTimetableParams {
  name: string
  periodCount: number
  schoolDays: string[]
  subjects: Array<{
    id: string
    name: string
  }>
  teachers: Array<{
    id: string
    name: string
    subjects: string[]
    availability?: any
  }>
  classes: Array<{
    id: string
    name: string
  }>
  lessons: Array<{
    classId: string
    subjectId: string
    teacherId: string
    periodsPerWeek: number
  }>
  constraints: {
    avoidBackToBack: boolean
    optimizeFreeTime: boolean
    balanceTeacherLoads: boolean
    maxDailyPeriodsPerTeacher?: number
    preferConsecutivePeriods?: boolean
  }
}

export interface TimetableResult {
  schedule?: Record<
    string,
    Array<{
      day: string
      period: number
      subject: string
      teacher: string
      room: string
    }>
  >
  scheduleData?: Array<{
    class: string
    [day: string]: any
  }>
  stats?: any
  conflicts?: Array<{
    type: string
    description: string
    entities: string[]
  }>
}

export interface ColorScheduleResult {
  class_name: string
  subject: string
  uncompleted_hours: number
  teacherID: string | number
  teacherName: string
  green: Array<{
    day: string
    time: string
    available_rooms: string[]
  }>
  red: Array<{
    day: string
    time: string
    available_rooms: string[]
  }>
  black: Array<{
    day: string
    time: string
    available_rooms: string[]
  }>
  blue: Array<{
    day: string
    time: string
    available_rooms: string[]
  }>
}

export interface HardConstraintResult {
  errors: string[]
}

export interface SoftConstraintResult {
  errors: {
    teacher_workload?: string[]
    teacher_consecutive_hours?: string[]
    [key: string]: string[] | undefined
  }
  error_counts: {
    [key: string]: number
  }
}

export interface TimeSlot {
  period: number
  start: string
  end: string
  isBreak: boolean
}

// Interface for the external timetable API
export interface ExternalTimetableParams {
  timeSlots: {
    [day: string]: {
      studyTimes: string[]
      restTime: string
    }
  }
  salles: Array<{ name: string; type: string }>
  classes: Array<{
    className: string
    subjects: Array<{
      subject: string
      hours: number
      teacherID: string | number
      divide?: number[]
      alternateWith?: string
      alternateWithTeacherID?: string | number
      alternationPattern?: string
      room?: string
    }>
  }>
  teachers: Array<{
    teacherId: string | number
    teacherName: string
    subjects: string[]
    unavailableTimes?: {
      [day: string]: string[]
    }
    minimumHours?: number
    maximumHours?: number
  }>
}

export const timetableService = {
  // Generate a new timetable using the internal API
  generateTimetable: async (
    params: GenerateTimetableParams
  ): Promise<TimetableResult> => {
    try {
      const response = await api.post('/api/api/timetable/generate', params)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get color-coded schedule for unscheduled lessons
  getColorSchedule: async (params: {
    data: ExternalTimetableParams
    result: TimetableResult
  }): Promise<ColorScheduleResult[]> => {
    try {
      const response = await axios.post(EXTERNAL_COLOR_SCHEDULE_API, params, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
        },
      })
      return response.data
    } catch (error) {
      console.error('Error getting color schedule:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Validate hard constraints
  validateHardConstraints: async (params: {
    data: ExternalTimetableParams
    result: TimetableResult
  }): Promise<HardConstraintResult> => {
    try {
      const response = await axios.post(EXTERNAL_VALIDATE_HARD_API, params, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
        },
      })
      return response.data
    } catch (error) {
      console.error('Error validating hard constraints:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Analyze soft constraints
  analyzeSoftConstraints: async (params: {
    data: ExternalTimetableParams
    result: TimetableResult
  }): Promise<SoftConstraintResult> => {
    try {
      const response = await axios.post(EXTERNAL_ANALYZE_SOFT_API, params, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
        },
      })
      return response.data
    } catch (error) {
      console.error('Error analyzing soft constraints:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Generate a timetable using the external API and automatically save it
  generateExternalTimetable: async (
    params: ExternalTimetableParams
  ): Promise<any> => {
    try {
      // Format time slots to match the expected API format ("8 => 9" instead of "08:00 => 08:45")
      Object.keys(params.timeSlots).forEach((day) => {
        // Filter out empty time slots and format study times
        params.timeSlots[day].studyTimes = params.timeSlots[day].studyTimes
          .filter((time) => {
            // Check if the time string has valid start and end times
            const [start, end] = time.split('=>').map((t) => t.trim())

            // Skip if start or end is empty
            if (!start || !end || start === ' ' || end === ' ') {
              return false
            }

            // Skip if start and end are the same (e.g., "8 => 8")
            if (start === end) {
              console.log(
                `Filtering out invalid time slot: ${time} (start and end times are the same)`
              )
              return false
            }

            return true
          })
          .map((time) => {
            if (time.includes(':')) {
              const [start, end] = time.split('=>').map((t) => t.trim())

              // Extract just the hour part for simple cases
              const startHour = parseInt(start.split(':')[0], 10)
              const endHour = parseInt(end.split(':')[0], 10)

              // If hours are different, just use the hours
              if (startHour !== endHour) {
                return `${startHour} => ${endHour}`
              }

              // If we get here, the hours are the same but minutes are different
              // Just keep the original format instead of converting to decimals
              return time
            }
            return time
          })

        // Format rest time if it's not empty
        if (
          params.timeSlots[day].restTime &&
          params.timeSlots[day].restTime.includes('=>')
        ) {
          const [start, end] = params.timeSlots[day].restTime
            .split('=>')
            .map((t) => t.trim())

          // Only format if both start and end are valid
          if (start && end && start !== ' ' && end !== ' ') {
            // Skip if start and end are the same
            if (start === end) {
              console.log(
                `Filtering out invalid rest time: ${params.timeSlots[day].restTime} (start and end times are the same)`
              )
              params.timeSlots[day].restTime = ''
            } else if (start.includes(':') || end.includes(':')) {
              // Format time with colons
              const startHour = start.includes(':')
                ? parseInt(start.split(':')[0], 10)
                : parseInt(start, 10)
              const endHour = end.includes(':')
                ? parseInt(end.split(':')[0], 10)
                : parseInt(end, 10)

              // If hours are different, just use the hours
              if (startHour !== endHour) {
                params.timeSlots[day].restTime = `${startHour} => ${endHour}`
              } else {
                // If hours are the same, keep the original format
                // This preserves the minutes without converting to decimals
                params.timeSlots[day].restTime = params.timeSlots[day].restTime
              }
            }
          } else {
            // Clear invalid rest time
            params.timeSlots[day].restTime = ''
          }
        }
      })

      // Final check to remove any days with empty study times
      Object.keys(params.timeSlots).forEach((day) => {
        if (params.timeSlots[day].studyTimes.length === 0) {
          console.log(
            `Warning: Day ${day} has no valid study times. Adding a default time slot.`
          )
          // Add a default time slot to avoid API errors
          params.timeSlots[day].studyTimes = ['8 => 9']
        }
      })

      // Clean up the console and only show the important data
      console.clear()
      console.log('\n\n===== DATA SENT TO API =====\n')
      console.log(JSON.stringify(params, null, 2))
      console.log('\n===========================\n')

      // Add a timeout to ensure the request doesn't hang indefinitely
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

      try {
        const response = await axios.post(EXTERNAL_TIMETABLE_API, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
          },
          signal: controller.signal,
        })

        clearTimeout(timeoutId)
        console.log('\n\n===== DATA RECEIVED FROM API =====\n')
        console.log(JSON.stringify(response.data, null, 2))
        console.log('\n=================================\n')

        // Automatically save the timetable data
        try {
          console.log('Automatically saving timetable data...')
          const savedData = await timetableService.saveTimetable({
            name: 'Generated Timetable',
            data: response.data,
            originalInputData: params,
            // Explicitly include the inputData field to ensure it's saved
            inputData: params,
          })
          console.log('Timetable data saved successfully:', savedData.id)
          return {
            ...response.data,
            savedTimetableId: savedData.id,
          }
        } catch (saveError) {
          console.error('Error saving timetable data:', saveError)
          // Return the original data even if saving fails
          return response.data
        }
      } catch (requestError) {
        clearTimeout(timeoutId)
        if (axios.isCancel(requestError)) {
          console.error('Request timed out after 30 seconds')
          throw new Error(
            'Request timed out. The timetable generation server might be busy or unavailable.'
          )
        }
        throw requestError
      }
    } catch (error) {
      console.error('API ERROR:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Save generated timetable to the internal API
  saveTimetable: async (timetableData: any): Promise<any> => {
    try {
      // Process the data to ensure it's in the correct format for port 3000
      let processedData = timetableData.data

      // If the data has scheduleData, process it to ensure time formats are consistent
      if (
        processedData &&
        processedData.scheduleData &&
        Array.isArray(processedData.scheduleData)
      ) {
        console.log('Processing scheduleData before saving to port 3000')

        // Define the correct day order
        const dayOrder = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ]

        // Process each class schedule
        processedData.scheduleData = processedData.scheduleData.map(
          (classSchedule: any) => {
            const result = { ...classSchedule }

            // Process each day's schedule
            dayOrder.forEach((day) => {
              if (classSchedule[day] && Array.isArray(classSchedule[day])) {
                result[day] = classSchedule[day].map((period: any) => {
                  // Transform the period data to match the database schema
                  return {
                    id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    day: day,
                    subject: period.subject,
                    timeSlot: period.time,
                    time: period.time,
                    className: classSchedule.class,
                    teacherId: period.teacherID,
                    teacherID: period.teacherID,
                    teacher: period.teacher,
                    classroomId: period.salle,
                    salle: period.salle,
                    group1: period.group1 || null,
                    group2: period.group2 || null,
                  }
                })
              } else {
                result[day] = []
              }
            })

            return result
          }
        )

        // Flatten the schedule data into entries
        const entries = processedData.scheduleData.flatMap(
          (classSchedule: any) => {
            return dayOrder.flatMap((day) => {
              return classSchedule[day].map((entry: any) => ({
                id: entry.id,
                day: entry.day,
                subject: entry.subject,
                timeSlot: entry.timeSlot,
                time: entry.time || entry.timeSlot,
                className: entry.className,
                teacherId: entry.teacherId,
                teacherID: entry.teacherID || entry.teacherId,
                teacher: entry.teacher,
                classroomId: entry.classroomId,
                salle: entry.salle || entry.classroomId,
                group1: entry.group1,
                group2: entry.group2,
              }))
            })
          }
        )

        // Update the processed data with the flattened entries
        processedData = {
          ...processedData,
          entries: entries,
        }
      }

      // Prepare the data structure for saving
      // We need to format it according to what the analyze feature expects
      const formattedData = {
        data: timetableData.inputData || timetableData.originalInputData,
        result: {
          scheduleData: processedData.scheduleData,
        },
      }

      // Store the original input data separately to ensure it's saved properly
      const originalInputData =
        timetableData.inputData || timetableData.originalInputData

      console.log(
        'Original input data for saving:',
        originalInputData ? 'PRESENT' : 'MISSING'
      )
      console.log('Formatted data for saving:', formattedData)

      // Check if there's an existing timetable for the establishment
      try {
        const checkResponse = await api.get('/timetable/etablissement')

        if (
          checkResponse.status === 200 &&
          checkResponse.data &&
          checkResponse.data.id
        ) {
          // We found an existing timetable, use PATCH instead
          console.log(
            'Found existing timetable, using PATCH instead with ID:',
            checkResponse.data.id
          )

          // For PATCH, update both data and result fields, and include inputData
          const patchData = {
            data: formattedData,
            description: timetableData.description || 'Generated Timetable',
            academicYear:
              timetableData.academicYear || new Date().getFullYear().toString(),
            isActive: true,
            // Explicitly include the inputData field at the top level for PATCH requests too
            inputData:
              timetableData.inputData || timetableData.originalInputData,
          }

          console.log(
            'Updating timetable with inputData:',
            patchData.inputData ? 'PRESENT' : 'MISSING'
          )

          const response = await api.patch(
            `/timetable/${checkResponse.data.id}`,
            patchData
          )

          return response.data
        }
      } catch (checkError) {
        console.error('Error checking for existing timetable:', checkError)
        // Continue with POST if check fails
      }

      // If no existing timetable or check failed, use POST
      // Make sure we include all the necessary data including inputData
      const requestData = {
        data: formattedData,
        description: timetableData.description || 'Generated Timetable',
        academicYear:
          timetableData.academicYear || new Date().getFullYear().toString(),
        isActive: true,
        // Explicitly include the inputData field at the top level
        inputData: timetableData.inputData || timetableData.originalInputData,
      }

      console.log(
        'Saving timetable with inputData:',
        requestData.inputData ? 'PRESENT' : 'MISSING'
      )

      const response = await api.post('/timetable', requestData)

      return response.data
    } catch (error) {
      console.error('Error saving timetable:', error)
      throw error
    }
  },

  // Get all timetables
  getTimetables: async (): Promise<TimetableData[]> => {
    try {
      const response = await api.get('/api/api/timetable')
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get a timetable by ID
  getTimetableById: async (id: string): Promise<TimetableData> => {
    try {
      const response = await api.get(`/api/api/timetable/${id}`)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Update a timetable
  updateTimetable: async (
    id: string,
    data: Partial<TimetableData>
  ): Promise<TimetableData> => {
    try {
      const response = await api.patch(`/api/api/timetable/${id}`, data)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Delete a timetable
  deleteTimetable: async (id: string): Promise<void> => {
    try {
      await api.delete(`/api/api/timetable/${id}`)
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Manual adjustments to timetable
  adjustTimetable: async (
    id: string,
    adjustments: Array<{
      classId: string
      day: string
      period: number
      subjectId?: string
      teacherId?: string
      roomId?: string
    }>
  ): Promise<TimetableResult> => {
    try {
      const response = await api.post(`/api/api/timetable/${id}/adjust`, {
        adjustments,
      })
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Export timetable to PDF
  exportToPdf: async (
    id: string,
    viewType: 'class' | 'teacher' | 'room'
  ): Promise<Blob> => {
    try {
      const response = await api.get(
        `/api/api/timetable/${id}/export/pdf?viewType=${viewType}`,
        {
          responseType: 'blob',
        }
      )
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Export timetable to Excel
  exportToExcel: async (
    id: string,
    viewType: 'class' | 'teacher' | 'room'
  ): Promise<Blob> => {
    try {
      const response = await api.get(
        `/api/api/timetable/${id}/export/excel?viewType=${viewType}`,
        {
          responseType: 'blob',
        }
      )
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get timetable for the current teacher
  getTeacherTimetable: async (): Promise<any> => {
    try {
      console.log('Fetching timetable for current teacher')

      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      if (!token) {
        console.error('No authentication token found')
        throw new Error('Authentication required to fetch teacher timetable')
      }

      // Make the request with the token
      const response = await api.get('/timetable/teacher', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      console.log('Teacher timetable fetched successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching teacher timetable:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get timetable for a specific student class
  getStudentTimetable: async (className?: string): Promise<any> => {
    try {
      console.log(
        'Fetching timetable for student class:',
        className || 'current student'
      )

      // Get the token from localStorage
      const token = localStorage.getItem('access_token')

      if (!token) {
        console.error('No authentication token found')
        throw new Error('Authentication required to fetch student timetable')
      }

      let url = '/timetable/student'
      if (className) {
        url += `?className=${encodeURIComponent(className)}`
      }

      // Make the request with the token
      const response = await api.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      console.log('Student timetable fetched successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching student timetable:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get students for a specific class
  getStudentsForClass: async (className: string): Promise<any> => {
    try {
      console.log('Fetching students for class:', className)
      const response = await api.get(
        `/timetable/class-students/${encodeURIComponent(className)}`
      )
      console.log('Students fetched successfully for class:', className)
      return response.data
    } catch (error) {
      console.error('Error fetching students for class:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get time slots from the external API
  getTimeSlots: async (): Promise<{ timeSlots: TimeSlot[] }> => {
    try {
      console.log('Fetching time slots from external API (GET method)')
      const response = await axios.get(EXTERNAL_TIME_SLOTS_API, {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer 1d03360f4bcd533a1481905ed6bb9ae77bd650b901ee9c28bcf4dbffcc568575`,
        },
      })
      console.log('Time slots fetched successfully:', response.data)

      // Format 1: Array of time slots
      if (
        response.data &&
        response.data.timeslots &&
        Array.isArray(response.data.timeslots)
      ) {
        console.log('Processing time slots in array format')
        return {
          timeSlots: response.data.timeslots.map((slot: any) => ({
            period: slot.period,
            start: slot.start,
            end: slot.end,
            isBreak: slot.isBreak || false,
          })),
        }
      }

      // Direct format: timeSlots array
      if (
        response.data &&
        response.data.timeSlots &&
        Array.isArray(response.data.timeSlots)
      ) {
        console.log('Processing direct timeSlots array format')
        return {
          timeSlots: response.data.timeSlots,
        }
      }

      // Format 2: Nested structure by days
      if (
        response.data &&
        response.data.timeSlots &&
        typeof response.data.timeSlots === 'object'
      ) {
        console.log('Processing time slots in nested day format')
        const timeSlots: TimeSlot[] = []
        let periodCounter = 1

        // Process each day's time slots
        Object.entries(response.data.timeSlots).forEach(
          ([_day, dayData]: [string, any]) => {
            // Process study times
            if (dayData.studyTimes && Array.isArray(dayData.studyTimes)) {
              dayData.studyTimes.forEach((timeStr: string) => {
                const [start, end] = timeStr.split(' => ')

                // Check if this time slot already exists
                const existingSlot = timeSlots.find(
                  (slot) =>
                    slot.start === start && slot.end === end && !slot.isBreak
                )

                if (!existingSlot) {
                  timeSlots.push({
                    period: periodCounter++,
                    start,
                    end,
                    isBreak: false,
                  })
                }
              })
            }

            // Process rest time (break)
            if (dayData.restTime) {
              const [start, end] = dayData.restTime.split(' => ')

              // Check if this break already exists
              const existingBreak = timeSlots.find(
                (slot) =>
                  slot.start === start && slot.end === end && slot.isBreak
              )

              if (!existingBreak) {
                timeSlots.push({
                  period: periodCounter++,
                  start,
                  end,
                  isBreak: true,
                })
              }
            }
          }
        )

        // Sort time slots by start time
        timeSlots.sort((a, b) => {
          const getMinutes = (time: string) => {
            const [hours, minutes] = time.split(':').map(Number)
            return hours * 60 + minutes
          }
          return getMinutes(a.start) - getMinutes(b.start)
        })

        // Reassign period numbers after sorting
        timeSlots.forEach((slot, index) => {
          slot.period = index + 1
        })

        return { timeSlots }
      }

      // Format 3: Already in the expected format
      if (
        response.data &&
        response.data.timeSlots &&
        Array.isArray(response.data.timeSlots)
      ) {
        console.log('Time slots already in expected format')
        return response.data
      }

      // If we can't process the data, return an empty array
      console.warn('Could not process time slots data, returning empty array')
      return { timeSlots: [] }
    } catch (error) {
      console.error('Error fetching time slots:', error)
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },
}
