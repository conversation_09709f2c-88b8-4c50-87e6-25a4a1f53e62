import { api } from '../axios-instance'
import { io, Socket } from 'socket.io-client'

// Define types for the dashboard statistics
export interface DashboardStats {
  students: {
    total: number
    active: number
    inactive: number
    recentlyAdded: number
  }
  teachers: {
    total: number
    active: number
    inactive: number
  }
  parents: {
    total: number
    active: number
    inactive: number
  }
  classes: {
    total: number
  }
  subjects: {
    total: number
  }
  classrooms: {
    total: number
  }
  grades: {
    total: number
  }
  admins: {
    total: number
    active: number
    inactive: number
  }
  attendance: {
    rate: number
  }
  schools?: {
    total: number
  }
}

export interface SystemHealth {
  storage: {
    used: number
    total: number
    unit: string
  }
  records: {
    total: number
    students: number
    teachers: number
    parents: number
    other: number
  }
  backups: {
    last: string
    next: string
    status: string
  }
  performance: {
    responseTime: number
    unit: string
    status: string
  }
  scheduledTasks?: {
    name: string
    description: string
    frequency: string
    lastRun?: string
    nextRun?: string
  }[]
  schools?: {
    total: number
  }
}

export interface RecentActivity {
  id: string
  type: 'STUDENT' | 'TEACHER' | 'PARENT' | 'CLASS' | 'SUBJECT' | 'ADMIN'
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  entityId: string
  entityName: string
  timestamp: string
  details?: string
  performedBy?: {
    id: string
    name: string
  }
}

export interface DashboardData {
  stats: DashboardStats
  activities: RecentActivity[]
}

// WebSocket connection singleton
let socket: Socket | null = null

// Get auth token from localStorage is handled in connectWebSocket

// Dashboard service with REST and WebSocket support
const dashboardService = {
  // REST API methods
  getStats: async (): Promise<DashboardStats> => {
    try {
      const response = await api.get<DashboardStats>('/dashboard/stats')
      return response.data
    } catch (error) {
      console.error('Error fetching dashboard statistics:', error)
      throw error
    }
  },

  getRecentActivities: async (limit = 10): Promise<RecentActivity[]> => {
    try {
      const response = await api.get<RecentActivity[]>(
        `/dashboard/activities?limit=${limit}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching recent activities:', error)
      throw error
    }
  },

  getSystemHealth: async (): Promise<SystemHealth> => {
    try {
      const response = await api.get<SystemHealth>('/dashboard/system')
      return response.data
    } catch (error) {
      console.error('Error fetching system health:', error)
      throw error
    }
  },

  // WebSocket methods
  connectWebSocket: (): Socket | null => {
    // If we already have a socket, return it
    if (socket) return socket

    // Try to get the token
    const token = localStorage.getItem('token')
    if (!token) {
      console.warn(
        'Authentication token not found, WebSocket features will be disabled'
      )
      return null
    }

    // Create a new socket connection
    try {
      socket = io(
        import.meta.env.VITE_API_URL || 'http://localhost:3000',
        {
          path: '/dashboard',
          transports: ['websocket'],
          auth: {
            token,
          },
        }
      )
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
      return null
    }

    // Set up event listeners
    socket.on('connect', () => {
      console.log('Connected to dashboard WebSocket')
      socket?.emit('subscribeToDashboard')
    })

    socket.on('disconnect', () => {
      console.log('Disconnected from dashboard WebSocket')
    })

    socket.on('error', (error) => {
      console.error('WebSocket error:', error)
    })

    return socket
  },

  disconnectWebSocket: () => {
    if (socket) {
      socket.disconnect()
      socket = null
    }
  },

  // Subscription methods for components to use
  subscribeToStats: (
    callback: (stats: DashboardStats) => void
  ): (() => void) => {
    const ws = dashboardService.connectWebSocket()
    if (!ws) {
      // If WebSocket connection failed, return a no-op cleanup function
      return () => {}
    }

    const handleStatsUpdate = (stats: DashboardStats) => {
      callback(stats)
    }

    ws.on('statsUpdate', handleStatsUpdate)

    // Return unsubscribe function
    return () => {
      ws.off('statsUpdate', handleStatsUpdate)
    }
  },

  subscribeToActivities: (
    callback: (activity: RecentActivity) => void
  ): (() => void) => {
    const ws = dashboardService.connectWebSocket()
    if (!ws) {
      // If WebSocket connection failed, return a no-op cleanup function
      return () => {}
    }

    const handleActivityUpdate = (activity: RecentActivity) => {
      callback(activity)
    }

    ws.on('activityUpdate', handleActivityUpdate)

    // Return unsubscribe function
    return () => {
      ws.off('activityUpdate', handleActivityUpdate)
    }
  },

  subscribeToSystemHealth: (
    callback: (health: SystemHealth) => void
  ): (() => void) => {
    const ws = dashboardService.connectWebSocket()
    if (!ws) {
      // If WebSocket connection failed, return a no-op cleanup function
      return () => {}
    }

    const handleHealthUpdate = (health: SystemHealth) => {
      callback(health)
    }

    ws.on('systemHealthUpdate', handleHealthUpdate)

    // Return unsubscribe function
    return () => {
      ws.off('systemHealthUpdate', handleHealthUpdate)
    }
  },

  subscribeToInitialData: (
    callback: (data: DashboardData) => void
  ): (() => void) => {
    const ws = dashboardService.connectWebSocket()
    if (!ws) {
      // If WebSocket connection failed, return a no-op cleanup function
      return () => {}
    }

    const handleInitialData = (data: DashboardData) => {
      callback(data)
    }

    ws.on('dashboardData', handleInitialData)

    // Return unsubscribe function
    return () => {
      ws.off('dashboardData', handleInitialData)
    }
  },

  // Get teacher-specific dashboard data
  getTeacherDashboard: async (teacherId: string) => {
    try {
      const response = await api.get(`/dashboard/teacher/${teacherId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching teacher dashboard data:', error)
      throw error
    }
  },
}

export default dashboardService
