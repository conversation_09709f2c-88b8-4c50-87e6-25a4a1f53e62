import { Student } from '@/interface/types'
import { api } from '../axios-instance'

export const studentService = {
  getAll: async () => {
    const response = await api.get<Student[]>('/student')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<Student>(`/student/${id}`)
    return response.data
  },

  create: async (data: Partial<Student>) => {
    try {
      console.log('Creating student with data:', JSON.stringify(data, null, 2))
      const response = await api.post<Student>('/student', data)
      console.log('Student created successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error creating student:', error)
      throw error
    }
  },

  // Using patch instead of put for updates as per backend controller
  update: async (id: string, data: Partial<Student>) => {
    const response = await api.patch<Student>(`/student/${id}`, data)
    return response.data
  },

  // Specific method for updating payment status
  updatePaymentStatus: async (id: string, isPaid: boolean) => {
    console.log(`Updating payment status for student ${id} to ${isPaid}`)
    try {
      // Use PATCH as per the backend controller
      const response = await api.patch<Student>(`/student/${id}`, { isPaid })
      console.log('Payment status update response:', response.data)

      // Import queryClient dynamically to avoid circular dependencies
      try {
        const { queryClient } = await import('@/lib/queryClient')
        // Invalidate all relevant caches
        queryClient.invalidateQueries({ queryKey: ['students'] })
        queryClient.invalidateQueries({ queryKey: ['student', id] })
        // Also invalidate any specific student query
        queryClient.invalidateQueries({ queryKey: ['student'] })
        console.log(`Cache invalidated for student ${id}`)
      } catch (error) {
        console.error('Failed to invalidate cache:', error)
      }

      return response.data
    } catch (error) {
      console.error('Error updating payment status:', error)
      throw error // Re-throw to allow proper error handling
    }
  },

  delete: async (id: string) => {
    await api.delete(`/student/${id}`)
  },
}
