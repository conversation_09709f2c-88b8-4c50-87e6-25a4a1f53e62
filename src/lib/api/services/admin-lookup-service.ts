import { api } from '../axios-instance'

export interface Class {
  id: string
  name: string
  gradeId?: string
  gradeName?: string
  classroomId?: string
  classroomName?: string
  createdAt?: string
  updatedAt?: string
}

export interface Teacher {
  id: string
  firstname: string
  lastname: string
  email: string
  phone?: string
  gender?: string
  createdAt?: string
  updatedAt?: string
}

/**
 * Admin Lookup Service
 * Handles fetching reference data like classes and teachers for the admin interface
 */
export const adminLookupService = {
  /**
   * Fetch all classes
   * @returns {Promise<Class[]>} Classes array
   */
  fetchClasses: async (): Promise<Class[]> => {
    try {
      // Try different endpoints in order of preference
      try {
        const response = await api.get<Class[]>('/class')
        return response.data
      } catch (error1) {
        console.warn('Error fetching from /class endpoint:', error1)
        try {
          const response = await api.get<Class[]>('/classes')
          return response.data
        } catch (error2) {
          console.warn('Error fetching from /classes endpoint:', error2)
          // Last resort
          const response = await api.get<Class[]>('/classrooms')
          return response.data
        }
      }
    } catch (error) {
      console.error('Error fetching classes:', error)
      // Return empty array to prevent UI errors
      return []
    }
  },

  /**
   * Fetch all teachers
   * @returns {Promise<Teacher[]>} Teachers array
   */
  fetchTeachers: async (): Promise<Teacher[]> => {
    try {
      // Try different endpoints in order of preference
      try {
        const response = await api.get<Teacher[]>('/teacher')
        return response.data
      } catch (error1) {
        console.warn('Error fetching from /teacher endpoint:', error1)
        try {
          const response = await api.get<Teacher[]>('/teachers')
          return response.data
        } catch (error2) {
          console.warn('Error fetching from /teachers endpoint:', error2)
          // Last resort - try admin endpoint
          const response = await api.get<Teacher[]>('/admin/teachers')
          return response.data
        }
      }
    } catch (error) {
      console.error('Error fetching teachers:', error)
      // Return empty array to prevent UI errors
      return []
    }
  }
}
