import { api } from '../axios-instance'
import {
  Classroom,
  ClassroomListResponse,
  CreateClassroomDTO,
  UpdateClassroomDTO,
} from '@/interface/types/classroom'

export const classroomService = {
  getAll: async (page = 1, limit = 10) => {
    try {
      // First try to get paginated data
      const response = await api.get<ClassroomListResponse>(
        `/classrooms?page=${page}&limit=${limit}`
      )

      // Check if the response is an array (non-paginated) or an object (paginated)
      if (Array.isArray(response.data)) {
        // If it's an array, create a paginated response structure
        const classrooms = response.data

        // Calculate pagination values
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        const paginatedClassrooms = classrooms.slice(startIndex, endIndex)

        return {
          data: paginatedClassrooms,
          meta: {
            totalItems: classrooms.length,
            itemCount: paginatedClassrooms.length,
            itemsPerPage: limit,
            totalPages: Math.ceil(classrooms.length / limit),
            currentPage: page,
          },
        }
      }

      // If it's already a paginated response, return it as is
      return response.data
    } catch (error) {
      console.error('Error fetching classrooms:', error)
      // Return empty data with pagination structure in case of error
      return {
        data: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: limit,
          totalPages: 0,
          currentPage: page,
        },
      }
    }
  },

  getById: async (id: string) => {
    try {
      const response = await api.get<Classroom>(`/classrooms/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching classroom ${id}:`, error)
      throw new Error('Failed to fetch classroom')
    }
  },

  create: async (data: CreateClassroomDTO | Partial<Classroom>) => {
    try {
      // Log the data being sent to the API
      console.log(
        'Creating classroom with data:',
        JSON.stringify(data, null, 2)
      )

      // Try with singular endpoint first (matches other API patterns)
      try {
        const response = await api.post<Classroom>('/classroom', data)
        return response.data
      } catch (firstError) {
        console.log('First attempt failed, trying plural endpoint:', firstError)
        // If that fails, try with plural endpoint
        const response = await api.post<Classroom>('/classrooms', data)
        return response.data
      }
    } catch (error) {
      console.error('Error creating classroom:', error)
      throw new Error('Failed to create classroom')
    }
  },

  update: async (id: string, data: UpdateClassroomDTO | Partial<Classroom>) => {
    try {
      // Log the data being sent to the API
      console.log(
        `Updating classroom ${id} with data:`,
        JSON.stringify(data, null, 2)
      )

      // Try with singular endpoint first (matches other API patterns)
      try {
        const response = await api.patch<Classroom>(`/classroom/${id}`, data)
        return response.data
      } catch (firstError) {
        console.log('First attempt failed, trying plural endpoint:', firstError)
        // If that fails, try with plural endpoint
        const response = await api.patch<Classroom>(`/classrooms/${id}`, data)
        return response.data
      }
    } catch (error) {
      console.error(`Error updating classroom ${id}:`, error)
      throw new Error('Failed to update classroom')
    }
  },

  delete: async (id: string) => {
    try {
      console.log(`Deleting classroom with ID: ${id}`)

      // Try with singular endpoint first (matches other API patterns)
      try {
        await api.delete(`/classroom/${id}`)
      } catch (firstError) {
        console.log('First attempt failed, trying plural endpoint:', firstError)
        // If that fails, try with plural endpoint
        await api.delete(`/classrooms/${id}`)
      }
    } catch (error) {
      console.error(`Error deleting classroom ${id}:`, error)
      throw new Error('Failed to delete classroom')
    }
  },
}
