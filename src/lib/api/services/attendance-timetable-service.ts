import { api } from '../axios-instance';

/**
 * Timetable interface for attendance module
 */
export interface AttendanceTimetable {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  data?: AttendanceTimetableData;
}

/**
 * Timetable data structure
 */
export interface AttendanceTimetableData {
  entries?: AttendanceTimetableEntry[];
  days?: string[];
  timeSlots?: string[];
  [key: string]: any;
}

/**
 * Timetable entry interface
 */
export interface AttendanceTimetableEntry {
  id: string;
  className: string;
  subject: string;
  day: string;
  timeSlot: string;
  teacherId: string;
  teacherName?: string;
  classroomId?: string;
}

/**
 * Teacher interface for timetable
 */
export interface AttendanceTimetableTeacher {
  id: string;
  firstname: string;
  lastname: string;
  email?: string;
  subjects?: string[];
}

/**
 * Attendance Timetable service for the attendance module
 */
export const attendanceTimetableService = {
  /**
   * Fetch all timetables for the current establishment
   * @returns {Promise<AttendanceTimetable[]>} Array of timetables
   */
  fetchTimetables: async (): Promise<AttendanceTimetable[]> => {
    try {
      // Try different endpoints in order of preference
      try {
        const response = await api.get<AttendanceTimetable[]>('/timetables');
        return response.data;
      } catch (error1) {
        console.warn('Error fetching from /timetables endpoint:', error1);
        try {
          const response = await api.get<AttendanceTimetable[]>('/timetable');
          return response.data;
        } catch (error2) {
          console.warn('Error fetching from /timetable endpoint:', error2);
          // Last resort - try admin endpoint
          const response = await api.get<AttendanceTimetable[]>('/admin/timetables');
          return response.data;
        }
      }
    } catch (error) {
      console.error('Error fetching timetables:', error);
      throw error;
    }
  },

  /**
   * Fetch active timetable
   * @returns {Promise<AttendanceTimetable>} Active timetable
   */
  fetchActiveTimetable: async (): Promise<AttendanceTimetable> => {
    try {
      // Try different endpoints in order of preference
      try {
        const response = await api.get<AttendanceTimetable>('/timetables/active');
        return response.data;
      } catch (error1) {
        console.warn('Error fetching from /timetables/active endpoint:', error1);
        try {
          const response = await api.get<AttendanceTimetable>('/timetable/active');
          return response.data;
        } catch (error2) {
          console.warn('Error fetching from /timetable/active endpoint:', error2);
          // Last resort - try admin endpoint
          const response = await api.get<AttendanceTimetable>('/admin/timetables/active');
          return response.data;
        }
      }
    } catch (error) {
      console.error('Error fetching active timetable:', error);
      throw error;
    }
  },

  /**
   * Fetch timetable by ID
   * @param {string} id - Timetable ID
   * @returns {Promise<AttendanceTimetable>} Timetable object
   */
  fetchTimetableById: async (id: string): Promise<AttendanceTimetable> => {
    try {
      const response = await api.get<AttendanceTimetable>(`/timetables/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching timetable ${id}:`, error);
      throw error;
    }
  },

  /**
   * Fetch teachers for a specific timetable
   * @param {string} timetableId - Timetable ID
   * @returns {Promise<AttendanceTimetableTeacher[]>} Array of teachers
   */
  fetchTeachersForTimetable: async (timetableId: string): Promise<AttendanceTimetableTeacher[]> => {
    try {
      const response = await api.get<AttendanceTimetableTeacher[]>(`/timetables/${timetableId}/teachers`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching teachers for timetable ${timetableId}:`, error);
      throw error;
    }
  },

  /**
   * Fetch entries for a specific timetable
   * @param {string} timetableId - Timetable ID
   * @returns {Promise<AttendanceTimetableEntry[]>} Array of timetable entries
   */
  fetchTimetableEntries: async (timetableId: string): Promise<AttendanceTimetableEntry[]> => {
    try {
      const response = await api.get<AttendanceTimetableEntry[]>(`/timetables/${timetableId}/entries`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching entries for timetable ${timetableId}:`, error);
      throw error;
    }
  },

  /**
   * Fetch entries for a specific teacher in a timetable
   * @param {string} timetableId - Timetable ID
   * @param {string} teacherId - Teacher ID
   * @returns {Promise<AttendanceTimetableEntry[]>} Array of timetable entries for the teacher
   */
  fetchTeacherTimetableEntries: async (timetableId: string, teacherId: string): Promise<AttendanceTimetableEntry[]> => {
    try {
      const response = await api.get<AttendanceTimetableEntry[]>(`/timetables/${timetableId}/teachers/${teacherId}/entries`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching entries for teacher ${teacherId} in timetable ${timetableId}:`, error);
      throw error;
    }
  },

  /**
   * Fetch entries for a specific class in a timetable
   * @param {string} timetableId - Timetable ID
   * @param {string} className - Class name
   * @returns {Promise<AttendanceTimetableEntry[]>} Array of timetable entries for the class
   */
  fetchClassTimetableEntries: async (timetableId: string, className: string): Promise<AttendanceTimetableEntry[]> => {
    try {
      const response = await api.get<AttendanceTimetableEntry[]>(`/timetables/${timetableId}/classes/${encodeURIComponent(className)}/entries`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching entries for class ${className} in timetable ${timetableId}:`, error);
      throw error;
    }
  },

  /**
   * Get available days from a timetable
   * @param {AttendanceTimetable} timetable - Timetable object
   * @returns {string[]} Array of days
   */
  getAvailableDays: (timetable: AttendanceTimetable): string[] => {
    if (timetable.data?.days && Array.isArray(timetable.data.days)) {
      return timetable.data.days;
    }
    
    if (timetable.data?.entries && Array.isArray(timetable.data.entries)) {
      // Extract unique days from entries
      const daysSet = new Set<string>();
      timetable.data.entries.forEach(entry => {
        if (entry.day) {
          daysSet.add(entry.day);
        }
      });
      return Array.from(daysSet);
    }
    
    // Default days if none found
    return ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  },

  /**
   * Get available time slots from a timetable
   * @param {AttendanceTimetable} timetable - Timetable object
   * @returns {string[]} Array of time slots
   */
  getAvailableTimeSlots: (timetable: AttendanceTimetable): string[] => {
    if (timetable.data?.timeSlots && Array.isArray(timetable.data.timeSlots)) {
      return timetable.data.timeSlots;
    }
    
    if (timetable.data?.entries && Array.isArray(timetable.data.entries)) {
      // Extract unique time slots from entries
      const timeSlotsSet = new Set<string>();
      timetable.data.entries.forEach(entry => {
        if (entry.timeSlot) {
          timeSlotsSet.add(entry.timeSlot);
        }
      });
      return Array.from(timeSlotsSet);
    }
    
    // Return empty array if no time slots found
    return [];
  }
};

export default attendanceTimetableService;
