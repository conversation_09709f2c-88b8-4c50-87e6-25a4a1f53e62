import { RequestLogin, RequestRegister } from '@/interface/types'
import { api } from '../axios-instance'

// Extended registration interfaces
interface ExtendedRegisterRequest extends RequestRegister {
  captchaToken?: string
  csrfToken?: string
  gender?: string
  birthday?: Date
  address?: string
  phone?: string
  avatar?: string
  confirmPassword?: string
}

interface StudentRegisterRequest {
  cin: string
  firstname: string
  lastname: string
  email: string
  password: string
  gender: 'male' | 'female'
  birthday?: Date
  address?: string
  phone?: string
  role: string
  csrfToken?: string
  captchaToken?: string
  confirmPassword?: string
  avatar?: string
  schoolName?: string
  enrolledDate?: Date
}

export const authService = {
  login: async (data: RequestLogin) => {
    const response = await api.post('/auth/login', data)
    return response.data
  },

  register: async (data: ExtendedRegisterRequest) => {
    // Check if captcha token is provided
    if (!data.captchaToken) {
      console.warn('No captcha token provided')
      throw new Error('Captcha verification is required')
    }

    console.log('Captcha token provided, sending to backend for verification')

    // Create a new object with ONLY the properties expected by the backend DTO
    // This ensures properties like confirmPassword and csrfToken are not included
    const registrationData = {
      firstname: data.firstname,
      lastname: data.lastname,
      email: data.email,
      password: data.password,
      gender: data.gender || 'MALE', // Provide default if missing
      birthday: data.birthday,
      address: data.address || '',
      phone: data.phone || '',
      avatar: data.avatar || '',
      captchaToken: data.captchaToken,
      // Required boolean fields
      isActive: false, // Required by BaseUserDto
      userOnboarding: true // Optional but included for completeness
      // Note: role and etablissementId will be set by the backend
    }

    // Send the registration request with the captcha token
    try {
      const response = await api.post('/b2c-client/register', registrationData)
      return response.data
    } catch (error) {
      console.error('Registration API error:', error)
      throw error
    }
  },

  // Student registration endpoint
  registerStudent: async (data: StudentRegisterRequest) => {
    // Check if captcha token is provided
    if (!data.captchaToken) {
      console.warn('No captcha token provided for student registration')
      throw new Error('Captcha verification is required')
    }

    console.log('Captcha token provided for student registration, sending to backend for verification')

    // Create a new object with ONLY the properties expected by the backend DTO
    // This ensures properties like confirmPassword and csrfToken are not included
    const registrationData = {
      cin: data.cin,
      firstname: data.firstname,
      lastname: data.lastname,
      email: data.email,
      password: data.password,
      gender: data.gender,
      birthday: data.birthday,
      address: data.address || '',
      phone: data.phone || '',
      avatar: data.avatar || '',
      captchaToken: data.captchaToken,
      // Student-specific fields
      schoolName: data.schoolName || '',
      enrolledDate: data.enrolledDate || new Date(),
      // Required boolean fields
      isActive: false, // Required by BaseUserDto
      userOnboarding: true // Optional but included for completeness
      // Note: role and etablissementId will be set by the backend
    }

    // Send the registration request with the captcha token
    try {
      const response = await api.post('/student/register', registrationData)
      return response.data
    } catch (error) {
      console.error('Student registration API error:', error)
      throw error
    }


  },

  logout: () => {
    localStorage.removeItem('access_token')
    window.location.href = '/login'
  },

  checkAuth: async () => {
    try {
      const response = await api.get('/auth/me')

      // Store user onboarding status in localStorage if available
      if (response.data && response.data.userOnboarding !== undefined) {
        console.log(
          'Setting userOnboarding in localStorage from checkAuth:',
          response.data.userOnboarding
        )
        localStorage.setItem(
          'userOnboarding',
          response.data.userOnboarding.toString()
        )
      }

      return response.data
    } catch {
      return null
    }
  },
}
