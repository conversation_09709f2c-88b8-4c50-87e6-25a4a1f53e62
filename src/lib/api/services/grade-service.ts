import { Grade, CreateGradeDTO, UpdateGradeDTO } from '@/interface/types'
import { api } from '../axios-instance'

export const gradeService = {
  getAll: async () => {
    try {
      // Use the exact URL from the screenshot: http://localhost:3000/grade
      console.log('Fetching grades from http://localhost:3000/grade')
      const response = await api.get<any>('/grade')
      console.log('Raw grade response:', response.data)

      // Handle different response formats
      if (Array.isArray(response.data)) {
        console.log('Response is an array with', response.data.length, 'items')
        return response.data
      } else if (response.data && typeof response.data === 'object') {
        // Check if it's a paginated response with data property
        if (Array.isArray(response.data.data)) {
          console.log(
            'Response has data array with',
            response.data.data.length,
            'items'
          )
          return response.data.data
        }

        // If it's a single object, wrap it in an array
        console.log('Response is a single object, wrapping in array')
        return [response.data]
      } else {
        console.warn('Unexpected response format from /grade:', response.data)
        return []
      }
    } catch (error) {
      console.error('Error fetching grades:', error)
      return []
    }
  },

  getById: async (id: string) => {
    try {
      // Try the singular endpoint first (as per backend controller)
      try {
        const response = await api.get<Grade>(`/grade/${id}`)
        return response.data
      } catch (firstError) {
        console.log('First attempt failed, trying plural endpoint:', firstError)
        // If that fails, try with plural endpoint
        const response = await api.get<Grade>(`/grades/${id}`)
        return response.data
      }
    } catch (error) {
      console.error(`Error fetching grade ${id}:`, error)
      throw error
    }
  },

  create: async (data: CreateGradeDTO) => {
    try {
      // Create a simplified data object with just the name as shown in the example
      // The API expects { "name": "Grade 10" }
      const cleanData = {
        name: data.name,
        // Do not include etablissementId as per user request
      }

      console.log(
        'Creating grade with data:',
        JSON.stringify(cleanData, null, 2)
      )

      // Send the request to the exact endpoint shown in the screenshot
      const response = await api.post<Grade>('/grade', cleanData)
      console.log('Grade created successfully:', response.data)

      // Explicitly invalidate the grades cache in React Query
      // This is a workaround since we can't directly access queryClient here
      console.log('Grade created - any component using grades should refetch')

      return response.data
    } catch (error) {
      console.error('Error creating grade:', error)
      throw error
    }
  },

  update: async (id: string, data: UpdateGradeDTO) => {
    try {
      // Create a clean data object with just the name
      const cleanData = {
        name: data.name,
        // Do not include etablissementId as per user request
      }

      console.log(
        `Updating grade ${id} with data:`,
        JSON.stringify(cleanData, null, 2)
      )

      // Use the correct endpoint with PATCH method
      const response = await api.patch<Grade>(`/grade/${id}`, cleanData)
      console.log('Grade updated successfully:', response.data)

      // Explicitly log that cache should be invalidated
      console.log('Grade updated - any component using grades should refetch')

      return response.data
    } catch (error) {
      console.error(`Error updating grade ${id}:`, error)
      throw error
    }
  },

  delete: async (id: string) => {
    try {
      console.log(`Deleting grade with id: ${id}`)
      // Use the correct endpoint
      await api.delete(`/grade/${id}`)
      console.log(`Grade ${id} deleted successfully`)

      // Explicitly log that cache should be invalidated
      console.log('Grade deleted - any component using grades should refetch')

      return { success: true, message: 'Grade deleted successfully' }
    } catch (error) {
      console.error(`Error deleting grade ${id}:`, error)
      throw error
    }
  },
}
