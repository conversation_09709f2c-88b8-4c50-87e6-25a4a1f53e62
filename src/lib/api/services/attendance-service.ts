import { api } from '../axios-instance'

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LEFT_EARLY = 'left_early',
}

export interface Student {
  id: string
  firstname: string
  lastname: string
  email: string
  gender?: string
  parentId?: string
  classId?: string
}

export interface Attendance {
  subject: string
  id: string
  studentId: string
  student?: Student
  sessionId?: string
  timetableEntryId?: string
  className: string
  subjectName: string
  day: string
  timeSlot: string
  date: string
  status: AttendanceStatus
  notes?: string
  reason?: string
  createdAt: string
  updatedAt: string
  createdBy?: string
  updatedBy?: string
}

export interface StudentAttendanceRecord {
  studentId: string
  status: AttendanceStatus
  notes?: string
}

export interface SessionAttendanceDto {
  date: string
  className: string
  subject: string
  day: string
  timeSlot: string
  records: StudentAttendanceRecord[]
}

export interface SessionAttendanceWithSessionDto extends SessionAttendanceDto {
  sessionId: string
}

export const attendanceService = {
  // Get all attendance records
  getAll: async () => {
    try {
      const response = await api.get<Attendance[]>('/attendance')
      return response.data
    } catch (error) {
      console.error('Error fetching attendance records:', error)
      throw error
    }
  },

  // Get attendance record by ID
  getById: async (id: string) => {
    try {
      const response = await api.get<Attendance>(`/attendance/${id}`)
      return response.data
    } catch (error) {
      console.error('Error fetching attendance record:', error)
      throw error
    }
  },

  // Get attendance records for a student
  getByStudent: async (studentId: string) => {
    try {
      const response = await api.get<Attendance[]>(
        `/attendance/student/${studentId}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching attendance for student:', error)
      throw error
    }
  },

  // Get attendance records for a session
  getBySession: async (sessionId: string) => {
    try {
      const response = await api.get<Attendance[]>(
        `/attendance/by-session/${sessionId}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching attendance for session:', error)
      throw error
    }
  },

  // Create a single attendance record
  create: async (attendanceData: Partial<Attendance>) => {
    try {
      const response = await api.post<Attendance>('/attendance', attendanceData)
      return response.data
    } catch (error) {
      console.error('Error creating attendance record:', error)
      throw error
    }
  },

  // Create attendance records for a session (without session ID)
  createSessionAttendance: async (
    sessionAttendanceDto: SessionAttendanceDto
  ) => {
    try {
      console.log('Creating session attendance without session ID')
      const response = await api.post<Attendance[]>(
        '/attendance/session',
        sessionAttendanceDto
      )
      return response.data
    } catch (error: any) {
      console.error('Error creating session attendance records:', error)
      if (error.response) {
        console.error('Error response data:', error.response.data)
        console.error('Error response status:', error.response.status)
        console.error('Error response headers:', error.response.headers)
      } else if (error.request) {
        console.error('Error request:', error.request)
      } else {
        console.error('Error message:', error.message)
      }
      throw error
    }
  },

  // Create attendance records for a session with session ID
  createSessionAttendanceWithSession: async (
    sessionAttendanceDto: SessionAttendanceWithSessionDto
  ) => {
    try {
      console.log(
        'Creating session attendance with session ID:',
        sessionAttendanceDto.sessionId
      )
      const response = await api.post<Attendance[]>(
        '/attendance/session-with-session',
        sessionAttendanceDto
      )
      return response.data
    } catch (error: any) {
      console.error(
        'Error creating session attendance records with session ID:',
        error
      )
      if (error.response) {
        console.error('Error response data:', error.response.data)
        console.error('Error response status:', error.response.status)
        console.error('Error response headers:', error.response.headers)
      } else if (error.request) {
        console.error('Error request:', error.request)
      } else {
        console.error('Error message:', error.message)
      }
      throw error
    }
  },

  // Update an attendance record
  update: async (id: string, attendanceData: Partial<Attendance>) => {
    try {
      const response = await api.patch<Attendance>(
        `/attendance/${id}`,
        attendanceData
      )
      return response.data
    } catch (error) {
      console.error('Error updating attendance record:', error)
      throw error
    }
  },

  // Delete an attendance record
  delete: async (id: string) => {
    try {
      await api.delete(`/attendance/${id}`)
    } catch (error) {
      console.error('Error deleting attendance record:', error)
      throw error
    }
  },

  // Get attendance report for a student
  getStudentReport: async (
    studentId: string,
    startDate?: string,
    endDate?: string
  ) => {
    try {
      let url = `/attendance/report/student/${studentId}`
      if (startDate && endDate) {
        url += `?startDate=${startDate}&endDate=${endDate}`
      }
      const response = await api.get(url)
      return response.data
    } catch (error) {
      console.error('Error fetching student attendance report:', error)
      throw error
    }
  },

  // Get attendance records for a class
  getByClass: async (className: string, date?: string) => {
    try {
      let url = `/attendance/class/${encodeURIComponent(className)}`
      if (date) {
        url += `?date=${date}`
      }
      console.log(
        `Fetching attendance for class ${className}${date ? ` on ${date}` : ''}`
      )
      const response = await api.get<Attendance[]>(url)
      console.log(
        `Found ${response.data.length} attendance records for class ${className}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching attendance for class:', error)
      throw error
    }
  },

  // Get attendance records for a class in a date range
  getByClassAndDateRange: async (
    className: string,
    startDate: string,
    endDate: string
  ) => {
    try {
      const url = `/attendance/class/${encodeURIComponent(className)}/date-range?startDate=${startDate}&endDate=${endDate}`
      console.log(
        `Fetching attendance for class ${className} from ${startDate} to ${endDate}`
      )
      const response = await api.get<Attendance[]>(url)
      console.log(
        `Found ${response.data.length} attendance records for class ${className} in date range`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching attendance for class by date range:', error)
      throw error
    }
  },

  // Get attendance summary for a class
  getClassSummary: async (
    className: string,
    startDate?: string,
    endDate?: string
  ) => {
    try {
      let url = `/attendance/summary/class/${encodeURIComponent(className)}`
      if (startDate && endDate) {
        url += `?startDate=${startDate}&endDate=${endDate}`
      }
      console.log(`Fetching attendance summary for class ${className}`)
      const response = await api.get(url)
      return response.data
    } catch (error) {
      console.error('Error fetching class attendance summary:', error)
      throw error
    }
  },
}
