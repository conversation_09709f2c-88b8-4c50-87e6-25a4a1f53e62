import { Admin, CreateAdminDTO, UpdateAdminDTO } from '@/interface/types/user'
import { api } from '../axios-instance'

export const adminService = {
  getAll: async () => {
    const response = await api.get<Admin[]>('/admin')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<Admin>(`/admin/${id}`)
    return response.data
  },

  getByEtablismentId: async () => {
    const response = await api.get<Admin>(`/admin/etablissement`)
    return response.data
  },

  create: async (data: CreateAdminDTO) => {
    try {
      // Make sure the avatar URL is included in the data
      console.log('Creating admin with data:', {
        ...data,
        password: '***HIDDEN***',
      })

      // Log the avatar URL specifically
      console.log('Avatar URL being sent:', data.avatar)

      // Send the data directly to the server without modification
      const response = await api.post<Admin>('/admin', data)

      // Log the response from the server
      console.log('Server response for admin creation:', response.data)

      return response.data
    } catch (error) {
      console.error('Error in admin service create:', error)
      throw error
    }
  },

  update: async (id: string, data: UpdateAdminDTO) => {
    try {
      // Remove any undefined or null values to prevent validation errors
      const cleanData = Object.fromEntries(
        Object.entries(data).filter(
          ([_, value]) => value !== undefined && value !== null
        )
      ) as UpdateAdminDTO

      // Log the data being sent to the server
      console.log('Updating admin with data:', {
        ...cleanData,
        id,
      })

      // Log the avatar URL specifically if it exists
      if (cleanData.avatar) {
        console.log('Avatar URL being sent in update:', cleanData.avatar)
      }

      // Send the data directly to the server without modification
      // Using PATCH instead of PUT as per the backend controller
      const response = await api.patch<Admin>(`/admin/${id}`, cleanData)

      // Log the response from the server
      console.log('Server response for admin update:', response.data)

      return response.data
    } catch (error) {
      console.error('Error in admin service update:', error)
      throw error
    }
  },

  delete: async (id: string) => {
    await api.delete(`/admin/${id}`)
  },

  toggleActive: async (id: string) => {
    const response = await api.patch<Admin>(`/admin/toggle/${id}`)
    return response.data
  },
}
