import { api } from '../axios-instance'
import { AxiosError } from 'axios'

export interface Timetable {
  id: string
  data: any
  description: string
  academicYear: string
  isActive: boolean
  etablissementId: string
  createdAt: Date
  updatedAt: Date
  createdBy?: string
  updatedBy?: string
}

export const timetableDashboardService = {
  // Get all timetables
  getAllTimetables: async (): Promise<Timetable[]> => {
    try {
      const response = await api.get('/api/timetable')
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get timetables for current establishment
  getCurrentEstablishmentTimetables: async (): Promise<Timetable[]> => {
    try {
      const response = await api.get('/api/timetable/etablissement')
      // If the response is a single object, wrap it in an array
      const data = response.data
      return Array.isArray(data) ? data : [data]
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Get timetable by ID
  getTimetableById: async (id: string): Promise<Timetable> => {
    try {
      const response = await api.get(`/api/timetable/${id}`)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Save timetable
  saveTimetable: async (timetableData: {
    data: any
    description: string
    academicYear?: string
    isActive?: boolean
  }): Promise<Timetable> => {
    try {
      // Use the direct URL to port 3000 without the /api prefix
      const response = await api.post('/timetable', timetableData)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Update timetable
  updateTimetable: async (
    id: string,
    timetableData: Partial<Timetable>
  ): Promise<Timetable> => {
    try {
      const response = await api.patch(`/api/timetable/${id}`, timetableData)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },

  // Delete timetable
  deleteTimetable: async (id: string): Promise<void> => {
    try {
      await api.delete(`/api/timetable/${id}`)
    } catch (error) {
      const axiosError = error as AxiosError
      throw axiosError.response?.data || error
    }
  },
}
