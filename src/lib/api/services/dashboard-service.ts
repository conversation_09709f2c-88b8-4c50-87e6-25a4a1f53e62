import { api } from '../axios-instance'

// Define types for the dashboard statistics
export interface DashboardStats {
  students: {
    total: number
    active: number
    inactive: number
    recentlyAdded: number
  }
  teachers: {
    total: number
    active: number
    inactive: number
  }
  parents: {
    total: number
    active: number
    inactive: number
  }
  classes: {
    total: number
  }
  subjects: {
    total: number
  }
  classrooms: {
    total: number
  }
  grades: {
    total: number
  }
  admins: {
    total: number
    active: number
    inactive: number
  }
  attendance: {
    rate: number
  }
}

export interface SystemHealth {
  storage: {
    used: number
    total: number
    unit: string
  }
  records: {
    total: number
    students: number
    teachers: number
    parents: number
    other: number
  }
  backups: {
    last: string
    next: string
    status: string
  }
  performance: {
    responseTime: number
    unit: string
    status: string
  }
}

export interface RecentActivity {
  id: string
  type: 'STUDENT' | 'TEACHER' | 'PARENT' | 'CLASS' | 'SUBJECT' | 'ADMIN'
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  entityId: string
  entityName: string
  timestamp: string
  details?: string
  performedBy?: {
    id: string
    name: string
  }
}

export interface DashboardData {
  stats: DashboardStats
  activities: RecentActivity[]
}

// No WebSocket connection in this service

export default {
  // Get all dashboard statistics
  getStats: async (): Promise<DashboardStats> => {
    try {
      const response = await api.get<DashboardStats>('/dashboard/stats')
      return response.data
    } catch (error) {
      console.error('Error fetching dashboard statistics:', error)
      // Return default values in case of error
      return {
        students: { total: 0, active: 0, inactive: 0, recentlyAdded: 0 },
        teachers: { total: 0, active: 0, inactive: 0 },
        parents: { total: 0, active: 0, inactive: 0 },
        classes: { total: 0 },
        subjects: { total: 0 },
        classrooms: { total: 0 },
        grades: { total: 0 },
        admins: { total: 0, active: 0, inactive: 0 },
        attendance: { rate: 0 },
      }
    }
  },

  // Get recent activities
  getRecentActivities: async (limit = 10): Promise<RecentActivity[]> => {
    try {
      const response = await api.get<RecentActivity[]>(
        `/dashboard/activities?limit=${limit}`
      )
      return response.data
    } catch (error) {
      console.error('Error fetching recent activities:', error)
      return []
    }
  },

  // Get system health data
  getSystemHealth: async (): Promise<SystemHealth> => {
    try {
      const response = await api.get<SystemHealth>('/dashboard/system')
      return response.data
    } catch (error) {
      console.error('Error fetching system health:', error)
      // Return default values in case of error
      return {
        storage: { used: 0, total: 100, unit: 'GB' },
        records: { total: 0, students: 0, teachers: 0, parents: 0, other: 0 },
        backups: {
          last: new Date().toISOString(),
          next: new Date().toISOString(),
          status: 'unknown',
        },
        performance: { responseTime: 0, unit: 'ms', status: 'unknown' },
      }
    }
  },

  // Get teacher-specific dashboard data
  getTeacherDashboard: async (teacherId: string) => {
    try {
      const response = await api.get(`/dashboard/teacher/${teacherId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching teacher dashboard:', error)
      return {
        classes: 0,
        students: 0,
        subjects: 0,
        assignments: 0,
      }
    }
  },
}
