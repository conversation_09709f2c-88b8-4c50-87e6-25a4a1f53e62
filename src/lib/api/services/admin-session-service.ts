import { api } from '../axios-instance'
import { format } from 'date-fns'
import { CancellationCategory } from '../../constants/cancellation-types'

// Types
export enum SessionStatus {
  ONGOING = 'ongoing',
  REPORTED = 'reported',
  NOT_REPORTED = 'not_reported',
  CANCELED = 'canceled',
  PENDING = 'pending'
}

export interface Session {
  id: string
  teacherId: string
  teacherName: string
  className: string
  classId?: string
  subjectName: string
  subjectId?: string
  day: string
  timeSlot: string
  date: string
  status: SessionStatus
  notes?: string
  timetableEntryId?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  studentCount?: number
  presentCount?: number
  absentCount?: number
  lateCount?: number
  leftEarlyCount?: number
  attendanceTaken?: boolean
  cancelReason?: string
  cancelCategory?: CancellationCategory | string
}

export interface SessionStatusMetrics {
  total: number
  reported: number
  notReported: number
  ongoing: number
  canceled: number
  pending?: number
}

export interface SessionFilters {
  dateRange?: {
    from: Date
    to: Date
  }
  classId?: string
  teacherId?: string
  status?: string
  searchTerm?: string
  cancelCategory?: CancellationCategory | string
}

export interface AttendanceSummary {
  present: number
  absent: number
  late: number
  leftEarly: number
  total: number
}

export interface TeacherAbsenceRequest {
  reason: string
  substituteTeacherId?: string
  substituteTeacherName?: string
}

export interface TeacherAbsenceRecordRequest {
  teacherId: string;
  date: string;
  reason: string;
  cancelAllSessions: boolean;
}

// Add type definition for error object
interface SessionError {
  sessionId: string;
  error: string;
}

/**
 * Format date for API requests
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string (YYYY-MM-DD)
 */
const formatDateForApi = (date: Date): string => {
  if (!date) return ''
  return format(new Date(date), 'yyyy-MM-dd')
}

/**
 * Admin Session Service
 * Handles all session-related API calls for the admin interface
 */
export const adminSessionService = {
  /**
   * Fetch sessions with filters
   * @param {SessionFilters} filters - Filter parameters
   * @returns {Promise<Session[]>} Sessions array
   */
  fetchSessions: async (filters: SessionFilters): Promise<Session[]> => {
    try {
      // Log the filters for debugging
      console.log('Fetching sessions with filters:', JSON.stringify(filters, null, 2));

      let params: Record<string, any> = {}

      // Format dates for date range filtering
      if (filters.dateRange) {
        params.startDate = formatDateForApi(filters.dateRange.from)
        params.endDate = formatDateForApi(filters.dateRange.to)
      }

      // Add other filters - ensure we're checking for both undefined and 'all'
      if (filters.classId && filters.classId !== 'all') {
        params.classId = filters.classId
      }

      if (filters.teacherId && filters.teacherId !== 'all') {
        params.teacherId = filters.teacherId
      }

      if (filters.status && filters.status !== 'all') {
        params.status = filters.status
      }

      if (filters.searchTerm && filters.searchTerm.trim() !== '') {
        params.search = filters.searchTerm.trim()
      }

      if (filters.cancelCategory && filters.cancelCategory !== 'all') {
        params.cancelCategory = filters.cancelCategory
      }

      // Log the final params being sent to the API
      console.log('API request params:', params);

      // Try the admin-specific endpoint first
      try {
        const response = await api.get<Session[]>('/admin/sessions', { params })
        console.log(`Fetched ${response.data.length} sessions from admin endpoint`);
        return response.data
      } catch (adminError) {
        console.warn('Admin sessions endpoint failed, trying regular sessions endpoint:', adminError)

        // Fall back to the regular sessions endpoint
        try {
          const response = await api.get<Session[]>('/sessions', { params })
          console.log(`Fetched ${response.data.length} sessions from regular endpoint`);
          return response.data
        } catch (regularError) {
          console.error('Regular sessions endpoint also failed:', regularError);
          throw regularError;
        }
      }
    } catch (error) {
      console.error('Error fetching sessions:', error)

      // Return empty array to prevent UI errors
      return []
    }
  },

  /**
   * Fetch session by ID
   * @param {string} id - Session ID
   * @returns {Promise<Session>} Session object
   */
  fetchSessionById: async (id: string): Promise<Session> => {
    try {
      const response = await api.get<Session>(`/sessions/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching session ${id}:`, error)
      throw error
    }
  },

  /**
   * Fetch session status metrics
   * @param {Object} dateRange - Date range for metrics
   * @returns {Promise<SessionStatusMetrics>} Status metrics
   */
  fetchSessionStatusMetrics: async (dateRange: { from: Date, to: Date }): Promise<SessionStatusMetrics> => {
    try {
      const params = {
        startDate: formatDateForApi(dateRange.from),
        endDate: formatDateForApi(dateRange.to)
      }

      try {
        // Try the admin endpoint first
        const response = await api.get<any>('/admin/sessions/statistics', { params })

        // Extract the relevant metrics from the response
        const data = response.data;

        // Map the response to the expected format
        return {
          total: data.total || 0,
          reported: (data.byStatus?.reported || 0),
          notReported: (data.byStatus?.not_reported || 0),
          ongoing: (data.byStatus?.ongoing || 0),
          canceled: (data.byStatus?.canceled || 0),
          pending: (data.byStatus?.pending || 0)
        };
      } catch (adminError) {
        console.error('Error fetching session status metrics from admin endpoint:', adminError)

        // If admin endpoint fails, try to calculate metrics from sessions
        try {
          // Fetch sessions and calculate metrics manually
          const sessions = await adminSessionService.fetchSessions({ dateRange })

          return {
            total: sessions.length,
            reported: sessions.filter(s => s.status === 'reported').length,
            notReported: sessions.filter(s => s.status === 'not_reported').length,
            ongoing: sessions.filter(s => s.status === 'ongoing').length,
            canceled: sessions.filter(s => s.status === 'canceled').length,
            pending: sessions.filter(s => s.status === 'pending').length
          }
        } catch (fallbackError) {
          console.error('Fallback method also failed:', fallbackError)
          throw fallbackError
        }
      }
    } catch (error) {
      console.error('Error fetching session status metrics:', error)
      // Return a default empty structure to prevent UI errors
      return {
        total: 0,
        reported: 0,
        notReported: 0,
        ongoing: 0,
        canceled: 0,
        pending: 0
      }
    }
  },

  /**
   * Fetch session attendance summary
   * @param {string} sessionId - Session ID
   * @returns {Promise<AttendanceSummary>} Attendance summary
   */
  fetchSessionAttendanceSummary: async (sessionId: string): Promise<AttendanceSummary> => {
    try {
      const response = await api.get<AttendanceSummary>(`/sessions/${sessionId}/attendance-summary`)
      return response.data
    } catch (error) {
      console.error(`Error fetching attendance summary for session ${sessionId}:`, error)
      throw error
    }
  },

  /**
   * Update session status
   * @param {string} sessionId - Session ID
   * @param {SessionStatus} status - New status
   * @returns {Promise<Session>} Updated session
   */
  updateSessionStatus: async (sessionId: string, status: SessionStatus): Promise<Session> => {
    try {
      const response = await api.patch<Session>(`/sessions/${sessionId}/status`, { status })
      return response.data
    } catch (error) {
      console.error(`Error updating status for session ${sessionId}:`, error)
      throw error
    }
  },

  /**
   * Mark teacher as absent
   * @param {string} sessionId - Session ID
   * @param {TeacherAbsenceRequest} request - Teacher absence request
   * @returns {Promise<Session>} Updated session
   */
  markTeacherAbsent: async (sessionId: string, request: TeacherAbsenceRequest): Promise<Session> => {
    try {
      const response = await api.patch<Session>(`/sessions/${sessionId}/teacher-absent`, request)
      return response.data
    } catch (error) {
      console.error(`Error marking teacher absent for session ${sessionId}:`, error)
      throw error
    }
  },

  /**
   * Cancel session
   * @param {string} sessionId - Session ID
   * @param {string} reason - Cancellation reason
   * @param {CancellationCategory} category - Cancellation category
   * @returns {Promise<Session>} Updated session
   */
  cancelSession: async (
    sessionId: string,
    reason: string,
    category: CancellationCategory = CancellationCategory.OTHER
  ): Promise<Session> => {
    try {
      // Log the request for debugging
      console.log(`Attempting to cancel session ${sessionId} with reason: ${reason}, category: ${category}`);

      // Try the admin-specific endpoint first
      try {
        const response = await api.patch<Session>(`/admin/sessions/${sessionId}/cancel`, {
          reason,
          cancelCategory: category
        });
        console.log('Session canceled successfully using admin endpoint');
        return response.data;
      } catch (adminError) {
        console.warn('Admin cancel endpoint failed, trying regular endpoint:', adminError);

        // Try the regular endpoint
        try {
          const response = await api.patch<Session>(`/sessions/${sessionId}/cancel`, {
            reason,
            cancelCategory: category
          });
          console.log('Session canceled successfully using regular endpoint');
          return response.data;
        } catch (regularError) {
          console.warn('Regular cancel endpoint failed, trying status update endpoint:', regularError);

          // Fallback: Try to update the session status to CANCELED
          try {
            const response = await api.patch<Session>(`/sessions/${sessionId}/status`, {
              status: SessionStatus.CANCELED,
              cancelReason: reason,
              cancelCategory: category
            });
            console.log('Session canceled successfully using status update endpoint');
            return response.data;
          } catch (statusError) {
            console.error('Status update endpoint also failed:', statusError);

            // Final fallback: Try to use the batch endpoint with a single session
            const response = await api.post(`/sessions/cancel-batch`, {
              sessionIds: [sessionId],
              reason,
              cancelCategory: category
            });

            if (response.data && response.data.updated > 0) {
              console.log('Session canceled successfully using batch endpoint');
              // Fetch the updated session to return
              const updatedSession = await adminSessionService.fetchSessionById(sessionId);
              return updatedSession;
            } else {
              throw new Error('Failed to cancel session using all available methods');
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error canceling session ${sessionId}:`, error);
      throw error;
    }
  },

  /**
   * Update session notes
   * @param {string} sessionId - Session ID
   * @param {string} notes - Session notes
   * @returns {Promise<Session>} Updated session
   */
  updateSessionNotes: async (sessionId: string, notes: string): Promise<Session> => {
    try {
      // Log the request for debugging
      console.log(`Updating notes for session ${sessionId}:`, notes);

      // Try the admin-specific endpoint first
      try {
        const response = await api.patch<Session>(`/admin/sessions/${sessionId}/notes`, { notes });
        console.log('Notes updated successfully using admin endpoint');
        return response.data;
      } catch (adminError) {
        console.warn('Admin notes endpoint failed, trying regular endpoint:', adminError);

        // Try the regular endpoint
        try {
          const response = await api.patch<Session>(`/sessions/${sessionId}/notes`, { notes });
          console.log('Notes updated successfully using regular endpoint');
          return response.data;
        } catch (regularError) {
          console.warn('Regular notes endpoint failed, trying update endpoint:', regularError);

          // Try the session update endpoint
          try {
            const response = await api.patch<Session>(`/sessions/${sessionId}`, { notes });
            console.log('Notes updated successfully using session update endpoint');
            return response.data;
          } catch (updateError) {
            console.warn('Session update endpoint failed, trying direct PUT method:', updateError);

            // Final fallback: Try a PUT request to update the entire session
            // First, get the current session data
            const currentSession = await adminSessionService.fetchSessionById(sessionId);

            // Then update it with the new notes
            const updatedSession = {
              ...currentSession,
              notes
            };

            // Send the PUT request
            const response = await api.put<Session>(`/sessions/${sessionId}`, updatedSession);
            console.log('Notes updated successfully using PUT method');
            return response.data;
          }
        }
      }
    } catch (error) {
      console.error(`Error updating notes for session ${sessionId}:`, error);

      throw error;
    }
  },

  /**
   * Update session statuses in batch
   * @param {string[]} sessionIds - Session IDs
   * @returns {Promise<any>} Update result
   */
  updateSessionStatuses: async (sessionIds: string[]): Promise<any> => {
    try {
      // Log the request for debugging
      console.log(`Attempting to update statuses for ${sessionIds.length} sessions in batch`);
      console.log('Session IDs:', sessionIds);

      // Try the admin-specific endpoint first
      try {
        const response = await api.post(`/admin/sessions/update-statuses-batch`, { sessionIds });
        console.log('Session statuses updated successfully using admin batch endpoint');
        return response.data;
      } catch (adminError) {
        console.warn('Admin batch update endpoint failed, trying regular endpoint:', adminError);

        // Try the regular endpoint
        try {
          const response = await api.post(`/sessions/update-statuses-batch`, { sessionIds });
          console.log('Session statuses updated successfully using regular batch endpoint');
          return response.data;
        } catch (regularError) {
          console.warn('Regular batch update endpoint failed, trying alternative endpoint:', regularError);

          // Try another possible endpoint format
          try {
            const response = await api.post(`/sessions/batch/update-statuses`, { sessionIds });
            console.log('Session statuses updated successfully using alternative batch endpoint');
            return response.data;
          } catch (alternativeError) {
            console.warn('Alternative batch update endpoint failed, trying individual updates:', alternativeError);

            // Fallback: Try to update each session individually
            const results = {
              processed: sessionIds.length,
              updated: 0,
              failed: 0,
              errors: [] as SessionError[]
            };

            for (const sessionId of sessionIds) {
              try {
                // Try to update the session status to COMPLETED
                // This is a reasonable default for most cases
                await api.patch(`/sessions/${sessionId}`, { status: 'COMPLETED' });
                results.updated++;
              } catch (error) {
                results.failed++;
                results.errors.push({ sessionId, error: (error as Error).message });
              }
            }

            console.log(`Individual status updates completed: ${results.updated} succeeded, ${results.failed} failed`);
            return results;
          }
        }
      }
    } catch (error) {
      console.error('Error updating session statuses in batch:', error);

      throw error;
    }
  },

  /**
   * Cancel sessions in batch
   * @param {string[]} sessionIds - Session IDs
   * @param {string} reason - Cancellation reason
   * @param {CancellationCategory} category - Cancellation category
   * @returns {Promise<any>} Update result
   */
  cancelSessions: async (
    sessionIds: string[],
    reason: string,
    category: CancellationCategory = CancellationCategory.OTHER
  ): Promise<any> => {
    try {
      // Log the request for debugging
      console.log(`Attempting to cancel ${sessionIds.length} sessions in batch with reason: ${reason}, category: ${category}`);

      // Try the admin-specific endpoint first
      try {
        const response = await api.post(`/admin/sessions/cancel-batch`, {
          sessionIds,
          reason,
          cancelCategory: category
        });
        console.log('Sessions canceled successfully using admin batch endpoint');
        return response.data;
      } catch (adminError) {
        console.warn('Admin batch cancel endpoint failed, trying regular endpoint:', adminError);

        // Try the regular endpoint
        try {
          const response = await api.post(`/sessions/cancel-batch`, {
            sessionIds,
            reason,
            cancelCategory: category
          });
          console.log('Sessions canceled successfully using regular batch endpoint');
          return response.data;
        } catch (regularError) {
          console.warn('Regular batch cancel endpoint failed, trying individual cancellations:', regularError);

          // Fallback: Try to cancel each session individually
          const results = {
            processed: sessionIds.length,
            updated: 0,
            failed: 0,
            errors: [] as SessionError[]
          };

          for (const sessionId of sessionIds) {
            try {
              await adminSessionService.cancelSession(sessionId, reason, category);
              results.updated++;
            } catch (error) {
              results.failed++;
              results.errors.push({ sessionId, error: (error as Error).message });
            }
          }

          console.log(`Individual cancellations completed: ${results.updated} succeeded, ${results.failed} failed`);
          return results;
        }
      }
    } catch (error) {
      console.error('Error canceling sessions in batch:', error);
      throw error;
    }
  },

  /**
   * Mark teachers absent in batch
   * @param {string[]} sessionIds - Session IDs
   * @param {string} reason - Absence reason
   * @returns {Promise<any>} Update result
   */
  markTeachersAbsent: async (sessionIds: string[], reason: string): Promise<any> => {
    try {
      // Log the request for debugging
      console.log(`Attempting to mark teachers absent for ${sessionIds.length} sessions in batch`);
      console.log('Session IDs:', sessionIds);
      console.log('Reason:', reason);

      // Try the admin-specific endpoint first
      try {
        const response = await api.post(`/admin/sessions/mark-teachers-absent-batch`, {
          sessionIds,
          reason,
          cancelCategory: CancellationCategory.TEACHER_ABSENCE // Explicitly set the category
        });
        console.log('Teachers marked absent successfully using admin batch endpoint');
        return response.data;
      } catch (adminError) {
        console.warn('Admin batch teacher absence endpoint failed, trying regular endpoint:', adminError);

        // Try the regular endpoint
        try {
          const response = await api.post(`/sessions/mark-teachers-absent-batch`, {
            sessionIds,
            reason,
            cancelCategory: CancellationCategory.TEACHER_ABSENCE
          });
          console.log('Teachers marked absent successfully using regular batch endpoint');
          return response.data;
        } catch (regularError) {
          console.warn('Regular batch teacher absence endpoint failed, trying alternative endpoint:', regularError);

          // Try another possible endpoint format
          try {
            const response = await api.post(`/sessions/batch/teacher-absence`, {
              sessionIds,
              reason,
              cancelCategory: CancellationCategory.TEACHER_ABSENCE
            });
            console.log('Teachers marked absent successfully using alternative batch endpoint');
            return response.data;
          } catch (alternativeError) {
            console.warn('Alternative batch teacher absence endpoint failed, trying individual updates:', alternativeError);

            // Fallback: Try to mark each session's teacher as absent individually
            const results = {
              processed: sessionIds.length,
              updated: 0,
              failed: 0,
              errors: [] as SessionError[]
            };

            for (const sessionId of sessionIds) {
              try {
                // First try the session-specific teacher absence endpoint
                try {
                  await api.patch(`/sessions/${sessionId}/teacher-absent`, {
                    reason,
                    cancelCategory: CancellationCategory.TEACHER_ABSENCE
                  });
                  results.updated++;
                } catch (sessionError) {
                  console.warn(`Session-specific teacher absence endpoint failed for session ${sessionId}, trying cancellation:`, sessionError);

                  // If that fails, try to cancel the session with teacher absence as the reason
                  await adminSessionService.cancelSession(
                    sessionId,
                    reason,
                    CancellationCategory.TEACHER_ABSENCE
                  );
                  results.updated++;
                }
              } catch (error) {
                results.failed++;
                results.errors.push({ sessionId, error: (error as Error).message });
              }
            }

            console.log(`Individual teacher absence updates completed: ${results.updated} succeeded, ${results.failed} failed`);
            return results;
          }
        }
      }
    } catch (error) {
      console.error('Error marking teachers absent in batch:', error);

      throw error;
    }
  },

  /**
   * Record teacher absence for a specific date
   * @param {TeacherAbsenceRecordRequest} request - Teacher absence record request
   * @returns {Promise<any>} Result of the operation
   */
  recordTeacherAbsence: async (request: TeacherAbsenceRecordRequest): Promise<any> => {
    try {
      // Log the request for debugging
      console.log('Recording teacher absence with request:', request);

      // Use the correct endpoint based on the API documentation
      const response = await api.post(
        `/admin/sessions/teacher/${request.teacherId}/absent`,
        {
          reason: request.reason,
          date: request.date,
          // The backend forces this to true anyway
          cancelAllDaySessions: request.cancelAllSessions
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error recording teacher absence:', error);

      // If the specific endpoint fails, try to find a session for this teacher and date
      // and mark the teacher as absent for that session
      try {
        console.warn('Trying alternative approach - finding a session for this teacher and date');

        // First, get sessions for this teacher on this date
        const sessionsResponse = await api.get('/sessions', {
          params: {
            teacherId: request.teacherId,
            date: request.date
          }
        });

        const sessions = sessionsResponse.data;

        if (sessions && sessions.length > 0) {
          // Use the first session to mark the teacher as absent
          const sessionId = sessions[0].id;

          const absenceResponse = await api.patch(`/sessions/${sessionId}/teacher-absent`, {
            reason: request.reason,
            cancelAllDaySessions: request.cancelAllSessions
          });

          return absenceResponse.data;
        } else {
          throw new Error('No sessions found for this teacher on the specified date');
        }
      } catch (fallbackError) {
        console.error('All approaches failed:', fallbackError);
        throw fallbackError;
      }
    }
  }
}
