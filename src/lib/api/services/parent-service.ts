import { Parent, Student } from '@/interface/types'
import { api } from '../axios-instance'

export const parentService = {
  getAll: async () => {
    const response = await api.get<Parent[]>('/parent')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<Parent>(`/parent/${id}`)
    return response.data
  },

  create: async (data: Partial<Parent>) => {
    const response = await api.post<Parent>('/parent', data)
    return response.data
  },

  update: async (id: string, data: Partial<Parent>) => {
    const response = await api.patch<Parent>(`/parent/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/parent/${id}`)
  },

  // Additional parent-specific operations
  getChildren: async (parentId: string) => {
    const response = await api.get<Student[]>(`/parent/${parentId}/children`)
    return response.data
  },

  addChild: async (parentId: string, studentId: string) => {
    const response = await api.post<Parent>(
      `/parent/${parentId}/child/${studentId}`
    )
    return response.data
  },

  removeChild: async (parentId: string, studentId: string) => {
    const response = await api.delete(`/parent/${parentId}/child/${studentId}`)
    return response.data
  },
}
