import { api } from './axios-instance'
import type {
  // User types
  Teacher,
  Student,
  Parent,
  Admin,
  CreateTeacherDTO,
  UpdateTeacherDTO,
  CreateStudentDTO,
  UpdateStudentDTO,
  CreateParentDTO,
  UpdateParentDTO,
  LoginDTO,
  AuthResponse,
  Subject,
  Class,
  GradeLevel,
  Lesson,
  Attendance,
  CreateSubjectDTO,
  UpdateSubjectDTO,
  CreateClassDTO,
  UpdateClassDTO,
  CreateGradeLevelDTO,
  UpdateGradeLevelDTO,
  CreateLessonDTO,
  UpdateLessonDTO,
  CreateAttendanceDTO,
  UpdateAttendanceDTO,
  // Establishment types
  Establishment,
  CreateEstablishmentDTO,
  UpdateEstablishmentDTO,
} from '@/interface/types'

import { adminService } from './services/admin-service'

/**
 * Generic CRUD service creator
 */
const createService = <T, CreateDTO, UpdateDTO>(basePath: string) => ({
  getAll: async () => {
    const response = await api.get<T[]>(basePath)
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get<T>(`${basePath}/${id}`)
    return response.data
  },

  create: async (data: CreateDTO) => {
    const response = await api.post<T>(basePath, data)
    return response.data
  },

  update: async (id: string, data: UpdateDTO) => {
    // Use PATCH instead of PUT as per the backend controller
    const response = await api.patch<T>(`${basePath}/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`${basePath}/${id}`)
  },
})

/**
 * Authentication service
 */
export const authService = {
  login: async (data: LoginDTO) => {
    const response = await api.post<AuthResponse>('/auth/login', data)
    return response.data
  },
}

/**
 * Teacher service
 */
export const teacherService = {
  ...createService<Teacher, CreateTeacherDTO, UpdateTeacherDTO>('/teacher'),
  getSubjects: async (teacherId: string) => {
    const response = await api.get<Subject[]>(`/teacher/${teacherId}/subjects`)
    return response.data
  },
  getClasses: async (teacherId: string) => {
    const response = await api.get<Class[]>(`/teacher/${teacherId}/classes`)
    return response.data
  },
}

/**
 * Student service
 */
export const studentService = {
  ...createService<Student, CreateStudentDTO, UpdateStudentDTO>('/student'),
  getGrades: async (studentId: string) => {
    const response = await api.get(`/student/${studentId}/grades`)
    return response.data
  },
  getAttendance: async (studentId: string) => {
    const response = await api.get<Attendance[]>(
      `/student/${studentId}/attendance`
    )
    return response.data
  },
}

/**
 * Parent service
 */
export const parentService = {
  ...createService<Parent, CreateParentDTO, UpdateParentDTO>('/parent'),
  getStudents: async (parentId: string) => {
    const response = await api.get<Student[]>(`/parent/${parentId}/students`)
    return response.data
  },
}

/**
 * Subject service
 */
export const subjectService = {
  ...createService<Subject, CreateSubjectDTO, UpdateSubjectDTO>('/subject'),
  getTeachers: async (subjectId: string) => {
    const response = await api.get<Teacher[]>(`/subject/${subjectId}/teachers`)
    return response.data
  },
  getClasses: async (subjectId: string) => {
    const response = await api.get<Class[]>(`/subject/${subjectId}/classes`)
    return response.data
  },
}

/**
 * Class service
 */
export const classService = {
  ...createService<Class, CreateClassDTO, UpdateClassDTO>('/class'),
  getStudents: async (classId: string) => {
    const response = await api.get<Student[]>(`/class/${classId}/students`)
    return response.data
  },
  getSubjects: async (classId: string) => {
    const response = await api.get<Subject[]>(`/class/${classId}/subjects`)
    return response.data
  },
}

/**
 * Grade level service
 */
export const gradeLevelService = {
  ...createService<GradeLevel, CreateGradeLevelDTO, UpdateGradeLevelDTO>(
    '/grade'
  ),
  getClasses: async (gradeId: string) => {
    const response = await api.get<Class[]>(`/grade/${gradeId}/classes`)
    return response.data
  },
}

/**
 * Lesson service
 */
export const lessonService = {
  ...createService<Lesson, CreateLessonDTO, UpdateLessonDTO>('/lesson'),
  getAttendance: async (lessonId: string) => {
    const response = await api.get<Attendance[]>(
      `/lesson/${lessonId}/attendance`
    )
    return response.data
  },
}

/**
 * Attendance service
 */
export const attendanceService = {
  ...createService<Attendance, CreateAttendanceDTO, UpdateAttendanceDTO>(
    '/attendance'
  ),
}

/**
 * Establishment service
 */
export const etablissementService = {
  ...createService<
    Establishment,
    CreateEstablishmentDTO,
    UpdateEstablishmentDTO
  >('/etablissement'),
  getTeachers: async (establishmentId: string) => {
    const response = await api.get<Teacher[]>(
      `/etablissement/${establishmentId}/teachers`
    )
    return response.data
  },
  getStudents: async (establishmentId: string) => {
    const response = await api.get<Student[]>(
      `/etablissement/${establishmentId}/students`
    )
    return response.data
  },
  getAdmins: async (establishmentId: string) => {
    const response = await api.get<Admin[]>(
      `/etablissement/${establishmentId}/admins`
    )
    return response.data
  },
}

/**
 * Import services
 */
import { classroomService } from './services/classroom-service'
import { gradeService } from './services/grade-service'
import dashboardService from './services/dashboard-service'

/**
 * Centralized API services
 */
export const services = {
  auth: authService,
  teacher: teacherService,
  student: studentService,
  parent: parentService,
  admin: adminService,
  subject: subjectService,
  class: classService,
  classroom: classroomService,
  grade: gradeService,
  gradeLevel: gradeLevelService,
  lesson: lessonService,
  attendance: attendanceService,
  etablissement: etablissementService,
  dashboard: dashboardService,
}

export default services
