import { redirect as tanStackRedirect } from '@tanstack/react-router'

/**
 * A wrapper around TanStack's redirect function that allows for string paths
 * This helps with cases where we need to redirect to routes that aren't
 * fully typed in the route tree (like dynamic routes or external URLs)
 */
export function redirectToPath(path: string) {
  return tanStackRedirect({
    to: path as any,
  })
}
