import { jsPDF } from 'jspdf'
import { arabicFontBase64, arabicFontName } from './fonts'

export type QuestionType = 'multiple-choice' | 'true-false' | 'open-answer'

export interface Question {
  id: string
  question: string
  options: string[]
  correctAnswer?: string | string[] // Modified to allow array of correct answers
  type: QuestionType
}

export const downloadQuiz = (
  quiz: Question[],
  withAnswers: boolean = false,
  language: 'en' | 'fr' | 'ar' = 'en'
) => {
  const doc = new jsPDF({
    orientation: 'p',
    unit: 'mm',
    format: 'a4',
  })

  // Add the Arabic font to jsPDF's Virtual File System (VFS)
  doc.addFileToVFS(`${arabicFontName}.ttf`, arabicFontBase64);
  doc.addFont(`${arabicFontName}.ttf`, arabicFontName, 'normal');

  // Language-specific labels
  const labels = {
    en: {
      title: withAnswers ? 'Quiz (With Answers)' : 'Quiz',
      question: (i: number) => `Question ${i + 1}:`,
      option: (i: number) => `${String.fromCharCode(97 + i)}.`,
      correct: '✓ Correct Answer',
      defaultName: withAnswers ? 'Quiz_With_Answers' : 'Quiz',
      x: 25,
      align: 'left' as const,
    },
    fr: {
      title: withAnswers ? 'Quiz (Avec Réponses)' : 'Quiz',
      question: (i: number) => `Question ${i + 1} :`,
      option: (i: number) => `${String.fromCharCode(97 + i)}.`,
      correct: '✓ Bonne réponse',
      defaultName: withAnswers ? 'Quiz_Avec_Reponses' : 'Quiz',
      x: 25,
      align: 'left' as const,
    },
    ar: {
      title: withAnswers ? 'الاختبار (مع الإجابات)' : 'الاختبار',
      question: (i: number) => `السؤال ${i + 1}:`,
      option: (i: number) => ['أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح'][i] + '.',
      correct: '✓ الإجابة الصحيحة',
      defaultName: withAnswers ? 'الاختبار_مع_الإجابات' : 'الاختبار',
      x: 190,
      align: 'right' as const,
    },
  }[language]

  // Set document properties
  doc.setFont(arabicFontName);
  doc.setFontSize(16)
  doc.text(labels.title, labels.x, 25, { align: labels.align })
  doc.setLineWidth(0.5)
  doc.line(25, 30, 190, 30)

  const maxQuestionsPerPage = 4
  let currentPage = 1

  quiz.forEach((question, index) => {
    const yPosition = 40 + (index % maxQuestionsPerPage) * 60

    // Question text
    doc.setFontSize(12)
    const questionLabel = `${labels.question(index)} ${question.question}`
    const splitQuestion = doc.splitTextToSize(questionLabel, 180)
    doc.text(splitQuestion, labels.x, yPosition, { align: labels.align })

    // Options
    doc.setFontSize(11)
    question.options.forEach((option, optIndex) => {
      const optionLabel = `${labels.option(optIndex)} ${option}`
      const yOpt = yPosition + 15 + optIndex * 10
      if (withAnswers) {
        if (Array.isArray(question.correctAnswer)) {
          if (question.correctAnswer.includes(option)) {
            doc.setTextColor(0, 128, 0) // Green color
            doc.text(
              optionLabel + ' ' + labels.correct,
              labels.x + (language === 'ar' ? -5 : 5),
              yOpt,
              { align: labels.align }
            )
            doc.setTextColor(0, 0, 0) // Reset to black
          } else {
            doc.text(optionLabel, labels.x + (language === 'ar' ? -5 : 5), yOpt, { align: labels.align })
          }
        } else if (question.correctAnswer === option) {
          doc.setTextColor(0, 128, 0) // Green color
          doc.text(
            optionLabel + ' ' + labels.correct,
            labels.x + (language === 'ar' ? -5 : 5),
            yOpt,
            { align: labels.align }
          )
          doc.setTextColor(0, 0, 0) // Reset to black
        } else {
          doc.text(optionLabel, labels.x + (language === 'ar' ? -5 : 5), yOpt, { align: labels.align })
        }
      } else {
        doc.text(optionLabel, labels.x + (language === 'ar' ? -5 : 5), yOpt, { align: labels.align })
      }
    })

    // Add new page if needed
    if ((index + 1) % maxQuestionsPerPage === 0 && index < quiz.length - 1) {
      currentPage++
      doc.addPage()
      doc.setFont(arabicFontName); // Set font on new page
      doc.setFontSize(16)
      doc.text(labels.title, labels.x, 25, { align: labels.align })
      doc.setLineWidth(0.5)
      doc.line(25, 30, 190, 30)
    }
  })

  const filename = window.prompt('Enter a filename:', labels.defaultName)

  if (filename) {
    const sanitizedFilename = filename.replace(/[^\w\u0600-\u06FF]/gi, '_').toLowerCase()
    doc.save(`${sanitizedFilename}.pdf`)
  }
}

export const generateQuiz = (
  numberOfQuestions: number = 5
): Promise<Question[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const generatedQuiz: Question[] = Array.from(
        { length: numberOfQuestions },
        (_, index) => {
          const sampleQuestions = [
            {
              question: 'What is the capital of France?',
              options: ['Paris', 'London', 'Rome', 'Berlin'],
              correctAnswer: 'Paris',
            },
          ]
          const selectedQuestion =
            sampleQuestions[index % sampleQuestions.length]
          return {
            id:
              typeof crypto !== 'undefined' && crypto.randomUUID
                ? crypto.randomUUID()
                : `${Date.now()}-${index}`,
            question: selectedQuestion.question,
            options: selectedQuestion.options.sort(() => Math.random() - 0.5),
            correctAnswer: selectedQuestion.correctAnswer,
            type: 'multiple-choice',
          }
        }
      )
      resolve(generatedQuiz)
    }, 2000)
  })
}

export const submitQuiz = (
  quiz: Question[],
  answers: Record<number, string[]>
) => {
  const score = quiz.reduce((count, question, index) => {
    if (!question.correctAnswer || !answers[index]) return count

    if (Array.isArray(question.correctAnswer)) {
      // Check if arrays have the same elements (order doesn't matter)
      const correctSet = new Set(question.correctAnswer)
      const answerSet = new Set(answers[index])
      return (
        count +
        (correctSet.size === answerSet.size &&
        [...correctSet].every((value) => answerSet.has(value))
          ? 1
          : 0)
      )
    }

    return count + (answers[index].includes(question.correctAnswer) ? 1 : 0)
  }, 0)

  return {
    score,
    total: quiz.length,
  }
}
