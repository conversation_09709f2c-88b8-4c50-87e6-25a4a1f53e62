/* Tiptap Editor Styles */

.tiptap-editor .ProseMirror {
  min-height: 300px;
  outline: none;
}

.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Heading styles */
.tiptap-editor .ProseMirror h1 {
  font-size: 2em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.tiptap-editor .ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.tiptap-editor .ProseMirror h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

/* List styles */
.tiptap-editor .ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5em;
  margin: 1em 0;
}

.tiptap-editor .ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin: 1em 0;
}

.tiptap-editor .ProseMirror li {
  margin-bottom: 0.5em;
}

/* Blockquote styles */
.tiptap-editor .ProseMirror blockquote {
  border-left: 3px solid #e9ecef;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #6c757d;
}

/* Code block styles */
.tiptap-editor .ProseMirror pre {
  background-color: #f8f9fa;
  border-radius: 0.3em;
  padding: 0.75em 1em;
  margin: 1em 0;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

.tiptap-editor .ProseMirror code {
  background-color: #f8f9fa;
  border-radius: 0.2em;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

/* Image styles */
.tiptap-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.3em;
  margin: 1em 0;
}

/* Link styles */
.tiptap-editor .ProseMirror a {
  color: #0d6efd;
  text-decoration: underline;
  cursor: pointer;
}

/* Dark mode adjustments */
.dark .tiptap-editor .ProseMirror pre,
.dark .tiptap-editor .ProseMirror code {
  background-color: #2d3748;
  color: #e2e8f0;
}

.dark .tiptap-editor .ProseMirror blockquote {
  border-left-color: #4a5568;
  color: #a0aec0;
}

.dark .tiptap-editor .ProseMirror a {
  color: #63b3ed;
}

/* Bubble menu styles */
.tiptap-editor .bubble-menu {
  display: flex;
  background-color: white;
  padding: 0.2em;
  border-radius: 0.3em;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .tiptap-editor .bubble-menu {
  background-color: #2d3748;
}

/* Placeholder styles */
.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.dark .tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: #6c757d;
}

/* Text color styles */
.tiptap-editor .ProseMirror [style*="color"],
.tiptap-content .ProseMirror [style*="color"] {
  color: var(--tw-prose-body);
}

.tiptap-editor .ProseMirror [style*="color: #000000"],
.tiptap-content .ProseMirror [style*="color: #000000"] {
  color: #000000 !important;
}

.tiptap-editor .ProseMirror [style*="color: #ef4444"],
.tiptap-content .ProseMirror [style*="color: #ef4444"] {
  color: #ef4444 !important;
}

.tiptap-editor .ProseMirror [style*="color: #f97316"],
.tiptap-content .ProseMirror [style*="color: #f97316"] {
  color: #f97316 !important;
}

.tiptap-editor .ProseMirror [style*="color: #eab308"],
.tiptap-content .ProseMirror [style*="color: #eab308"] {
  color: #eab308 !important;
}

.tiptap-editor .ProseMirror [style*="color: #22c55e"],
.tiptap-content .ProseMirror [style*="color: #22c55e"] {
  color: #22c55e !important;
}

.tiptap-editor .ProseMirror [style*="color: #3b82f6"],
.tiptap-content .ProseMirror [style*="color: #3b82f6"] {
  color: #3b82f6 !important;
}

.tiptap-editor .ProseMirror [style*="color: #8b5cf6"],
.tiptap-content .ProseMirror [style*="color: #8b5cf6"] {
  color: #8b5cf6 !important;
}

.tiptap-editor .ProseMirror [style*="color: #ec4899"],
.tiptap-content .ProseMirror [style*="color: #ec4899"] {
  color: #ec4899 !important;
}

/* Dark mode color adjustments */
.dark .tiptap-editor .ProseMirror [style*="color: #000000"],
.dark .tiptap-content .ProseMirror [style*="color: #000000"] {
  color: #ffffff !important;
}

/* Simple Rich Editor Placeholder */
[contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #adb5bd;
  pointer-events: none;
  display: block;
}

.dark [contenteditable]:empty:before {
  color: #6c757d;
}
