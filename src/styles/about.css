/* About section specific styles */
.about-section {
  position: relative;
  z-index: 1;
  contain: layout;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  /* Ensure the entire section captures wheel events */
  touch-action: none;
}

.about-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: transparent;
  z-index: -1;
}

/* Remove the school image container styles as we're using a fixed background div now */
/* The fixed background is handled directly in the component */

/* Image styles are now handled directly in the component */

/* Content container styles */
@media (min-width: 1024px) {
  .about-section .content-container {
    width: 40%;
    max-width: 500px;
    margin-left: 5%;
  }
}

/* Fix for horizontal scroll container */
.about-section .horizontal-scroll-container {
  position: relative;
  z-index: 10;
  height: 100%;
  width: 100vw;
}

/* We're now using a fixed background div instead of the school-image-container */

/* Ensure the image doesn't appear outside the about section */
.about-section {
  isolation: isolate;
}

/* Make sure everything fits in the viewport */
.about-section .content-wrapper h2 {
  margin-bottom: 0.75rem;
}

.about-section .content-wrapper p {
  margin-bottom: 1.5rem;
}

/* Adjust for different screen sizes */
@media (max-height: 800px) {
  .about-section .content-wrapper h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .about-section .content-wrapper p {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }
}

/* Ensure the horizontal scroll area captures all wheel events */
.horizontal-scroll-area {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
}

/* When the scroll down indicator is shown, allow vertical scrolling */
.about-section.can-scroll-vertical {
  touch-action: auto;
  overflow-y: auto;
}
