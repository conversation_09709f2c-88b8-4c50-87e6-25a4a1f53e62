import { useState, useEffect } from 'react'
import Joyride, { Step, CallBackProps, STATUS } from 'react-joyride'
import { useOnboarding } from './OnboardingProvider'
import { RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SuperAdminOnboardingTourProps {
  currentPage: string
}

export function SuperAdminOnboardingTour({
  currentPage,
}: SuperAdminOnboardingTourProps) {
  const { isOnboarding } = useOnboarding()
  const [run, setRun] = useState(false)
  const [steps, setSteps] = useState<Step[]>([])
  const [showRestartButton, setShowRestartButton] = useState(true)

  // Define steps for the services page only - no navigation to other pages
  useEffect(() => {
    if (!isOnboarding) return

    // Only show the services page tour regardless of the current page
    const pageSteps: Step[] = [
      {
        target: 'body',
        content:
          'Welcome to the Super Admin Dashboard! This is where you can manage your school system.',
        placement: 'center',
        disableBeacon: true,
        title: 'Super Admin Dashboard',
        isFixed: true,
      },
      {
        target: '.super-admin-dashboard-header, h2',
        content:
          'This is the main dashboard where you can access various services.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Services Overview',
        spotlightPadding: 15,
      },
      {
        target: '.card, .service-card',
        content:
          'These cards represent different services available to you. Click on any card to access that service.',
        placement: 'bottom',
        disableBeacon: true,
        title: 'Service Cards',
        spotlightPadding: 15,
      },
      {
        target: 'body',
        content:
          "That's it! You now know the basics of the Super Admin dashboard.",
        placement: 'center',
        disableBeacon: true,
        title: 'Tour Complete',
        isFixed: true,
      },
    ]

    setSteps(pageSteps)

    // Start the tour if we have steps and onboarding is active
    if (pageSteps.length > 0) {
      // Check if the tour has already been completed
      const tourCompleted =
        localStorage.getItem('tour_completed_super_admin') === 'true'

      // Only auto-start the tour if it hasn't been completed yet
      if (!tourCompleted) {
        // Add a small delay to ensure the DOM is ready
        setTimeout(() => {
          setRun(true)
          setShowRestartButton(false)
          localStorage.setItem('super_admin_tour_active', 'true')
        }, 500)
      }
    }
  }, [currentPage, isOnboarding])

  // Handle tour events
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, type, index, action } = data

    // Log the callback for debugging
    console.log(
      `SuperAdminOnboardingTour: ${type} - ${action} - Step ${index + 1}`
    )

    // If the tour is finished or skipped, mark onboarding as completed
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      setRun(false)
      setShowRestartButton(true)
      localStorage.setItem('super_admin_tour_active', 'false')

      // Store that this tour was completed
      localStorage.setItem('tour_completed_super_admin', 'true')
      localStorage.setItem('userOnboarding', 'false')

      // Call the API to update onboarding status in the backend
      try {
        const token = localStorage.getItem('token')

        console.log(
          'SuperAdminOnboardingTour: Updating onboarding status in backend'
        )

        // First try the new endpoint
        fetch('http://localhost:3000/users/onboarding', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ userOnboarding: false }), // Backend expects false to mark as complete
        })
          .then((response) => {
            if (response.ok) {
              console.log(
                'SuperAdminOnboardingTour: Onboarding status updated in backend',
                response.status
              )
            } else {
              // If the first endpoint fails, try the alternative endpoint
              return fetch('http://localhost:3000/user/onboarding', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ userOnboarding: false }),
              })
            }
          })
          .then((response) => {
            if (response && response.ok) {
              console.log(
                'SuperAdminOnboardingTour: Onboarding status updated with alternative endpoint',
                response.status
              )
            } else {
              // Try the user-controller endpoint as a last resort
              return fetch(
                'http://localhost:3000/user-controller/update-onboarding',
                {
                  method: 'PATCH',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                  },
                  body: JSON.stringify({ userOnboarding: false }),
                }
              )
            }
          })
          .then((response) => {
            if (response && response.ok) {
              console.log(
                'SuperAdminOnboardingTour: Onboarding status updated with user-controller endpoint',
                response.status
              )
            }
          })
          .catch((error) => {
            console.error(
              'SuperAdminOnboardingTour: Failed to update onboarding status',
              error
            )
          })
      } catch (error) {
        console.error(
          'SuperAdminOnboardingTour: Error updating onboarding status',
          error
        )
      }

      // Dispatch an event to notify other components
      window.dispatchEvent(new CustomEvent('tour-status-change'))
    }

    // Auto-advance to the next step after a delay
    if (type === 'step:after' && action === 'next') {
      const nextIndex = index + 1
      if (nextIndex < steps.length) {
        console.log(
          `SuperAdminOnboardingTour: Auto-advancing to step ${nextIndex + 1} after delay`
        )
      }
    }
  }

  // Handle restart button click
  const handleRestartClick = () => {
    console.log('SuperAdminOnboardingTour: Restart button clicked')

    // Reset tour state but don't change the onboarding completion status
    localStorage.removeItem('tour_completed_super_admin')

    // Only set userOnboarding to true for the tour to show
    // but don't update the backend - this is just for viewing the tour again
    localStorage.setItem('super_admin_tour_active', 'true')

    // Start the tour
    setRun(true)
    setShowRestartButton(false)

    // Dispatch an event to notify other components
    window.dispatchEvent(new CustomEvent('tour-status-change'))
  }

  // If not in onboarding mode or no steps, don't render the tour
  if (!isOnboarding || steps.length === 0) {
    return null
  }

  return (
    <>
      <Joyride
        steps={steps}
        run={run}
        continuous={true}
        showProgress={true}
        showSkipButton={true}
        disableOverlayClose={false}
        disableCloseOnEsc={false}
        spotlightClicks={false}
        callback={handleJoyrideCallback}
        styles={{
          options: {
            zIndex: 10000,
            primaryColor: '#007bff',
            backgroundColor: '#ffffff',
            arrowColor: '#ffffff',
            overlayColor: 'rgba(0, 0, 0, 0.5)',
          },
          tooltip: {
            borderRadius: '8px',
            fontSize: '16px',
            padding: '20px',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
          },
          tooltipContainer: {
            textAlign: 'center',
          },
          tooltipTitle: {
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '12px',
            color: '#007bff',
          },
          tooltipContent: {
            fontSize: '16px',
            lineHeight: '1.5',
            marginBottom: '15px',
          },
          buttonNext: {
            backgroundColor: '#007bff',
            fontSize: '16px',
            padding: '10px 20px',
            borderRadius: '4px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
          },
          buttonBack: {
            color: '#555',
            marginRight: '15px',
            fontSize: '16px',
            padding: '10px 20px',
          },
          buttonSkip: {
            color: '#666',
            fontSize: '16px',
            fontWeight: 'bold',
            padding: '10px',
          },
          spotlight: {
            backgroundColor: 'transparent',
            borderRadius: 8,
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.6)',
          },
        }}
      />

      {/* Restart button */}
      {showRestartButton && (
        <div
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 10001,
          }}
        >
          <Button
            onClick={handleRestartClick}
            variant="outline"
            size="icon"
            className="bg-white hover:bg-gray-100 text-blue-500 rounded-full shadow-md h-10 w-10"
            title="Restart Tour"
          >
            <RefreshCw className="h-5 w-5" />
          </Button>
        </div>
      )}
    </>
  )
}
