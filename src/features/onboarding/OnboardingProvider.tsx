import React, { createContext, useContext, useState, useEffect } from 'react'
import { isOnboardingNeeded } from './useOnboardingTracker'

// Create a context for onboarding
interface OnboardingContextType {
  isOnboarding: boolean
  setIsOnboarding: React.Dispatch<React.SetStateAction<boolean>>
  currentPage: string
  setCurrentPage: React.Dispatch<React.SetStateAction<string>>
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
)

// Provider component
export function OnboardingProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [isOnboarding, setIsOnboarding] = useState(false)
  const [currentPage, setCurrentPage] = useState('')

  // Check if onboarding is needed on mount
  useEffect(() => {
    const userId = localStorage.getItem('id')
    const userRole = localStorage.getItem('role')

    if (userId && userRole) {
      const needsOnboarding = isOnboardingNeeded(userId, userRole)
      setIsOnboarding(needsOnboarding)

      // Log onboarding status
      console.log(
        `Onboarding status: ${needsOnboarding ? 'Needed' : 'Not needed'}`
      )
    }

    // Listen for onboarding completion events
    const handleOnboardingCompleted = () => {
      setIsOnboarding(false)
      console.log('Onboarding completed event received')
    }

    // Listen for onboarding reset events
    const handleOnboardingReset = () => {
      setIsOnboarding(true)
      console.log('Onboarding reset event received')
    }

    window.addEventListener('onboarding-completed', handleOnboardingCompleted)
    window.addEventListener('onboarding-reset', handleOnboardingReset)

    return () => {
      window.removeEventListener(
        'onboarding-completed',
        handleOnboardingCompleted
      )
      window.removeEventListener('onboarding-reset', handleOnboardingReset)
    }
  }, [])

  return (
    <OnboardingContext.Provider
      value={{ isOnboarding, setIsOnboarding, currentPage, setCurrentPage }}
    >
      {children}
    </OnboardingContext.Provider>
  )
}

// Hook to use the onboarding context
export function useOnboarding() {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider')
  }
  return context
}

// Component to wrap the app and track the current page
export function Onboarding({ children }: { children: React.ReactNode }) {
  const { setCurrentPage } = useOnboarding()
  const userId = localStorage.getItem('id')
  const userRole = localStorage.getItem('role')

  // Update the current page based on the URL
  useEffect(() => {
    const updateCurrentPage = () => {
      const path = window.location.pathname

      // Extract the current page from the URL
      let currentPage = 'unknown'

      if (path.includes('/super_admin')) {
        if (path === '/super_admin') currentPage = 'dashboard'
        else if (path.includes('/etablisments')) currentPage = 'establishments'
        else if (path.includes('/database')) currentPage = 'database'
        else if (path.includes('/profile')) currentPage = 'profile'
      }

      // Set the current page
      setCurrentPage(currentPage)
      console.log(`Current page set to: ${currentPage}`)

      // Track the page visit if needed
      if (userId && userRole && currentPage !== 'unknown') {
        // Use a regular function instead of a hook
        import('./useOnboardingTracker').then(({ trackPageVisit }) => {
          trackPageVisit(currentPage, userId, userRole)
        })
      }
    }

    // Update on mount and when the URL changes
    updateCurrentPage()
    window.addEventListener('popstate', updateCurrentPage)

    return () => {
      window.removeEventListener('popstate', updateCurrentPage)
    }
  }, [setCurrentPage, userId, userRole])

  return <>{children}</>
}
