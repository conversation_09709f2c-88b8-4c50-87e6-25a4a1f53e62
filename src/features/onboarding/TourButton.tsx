import React from 'react'
import { Button } from '@/components/ui/button'
import { Play } from 'lucide-react'
import { useTourStore } from '@/store/tourStore'

interface TourButtonProps {
  tourType: string
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
}

export function TourButton({
  tourType,
  variant = 'outline',
  size = 'default',
  className = '',
}: TourButtonProps) {
  // Use Zustand for tours
  const {
    // Establishment tour
    establishmentTourCompleted,
    resetEstablishmentTour,
    startEstablishmentTour,

    // Admin timetable tour
    adminTimetableTourCompleted,
    resetAdminTimetableTour,
    startAdminTimetableTour,
  } = useTourStore()

  // Check if the tour has been completed
  const [tourCompleted, setTourCompleted] = React.useState(() => {
    if (tourType === 'establishment') {
      return establishmentTourCompleted
    } else if (tourType === 'admin_timetable') {
      return adminTimetableTourCompleted
    }
    return localStorage.getItem(`tour_completed_${tourType}`) === 'true'
  })

  // Listen for tour status changes
  React.useEffect(() => {
    const handleTourStatusChange = () => {
      if (tourType === 'establishment') {
        setTourCompleted(establishmentTourCompleted)
      } else if (tourType === 'admin_timetable') {
        setTourCompleted(adminTimetableTourCompleted)
      } else {
        setTourCompleted(
          localStorage.getItem(`tour_completed_${tourType}`) === 'true'
        )
      }
    }

    window.addEventListener('tour-status-change', handleTourStatusChange)

    return () => {
      window.removeEventListener('tour-status-change', handleTourStatusChange)
    }
  }, [tourType, establishmentTourCompleted, adminTimetableTourCompleted])

  const handleStartTour = () => {
    // Set onboarding flag to true to ensure tour works properly
    localStorage.setItem('userOnboarding', 'true')

    if (tourType === 'establishment') {
      // Use Zustand for establishment tour
      resetEstablishmentTour()
      startEstablishmentTour()

      // For backward compatibility
      localStorage.removeItem('tour_completed_establishment')
      localStorage.removeItem('tour_skipped_establishment')
    } else if (tourType === 'admin_timetable') {
      // Use Zustand for admin timetable tour
      resetAdminTimetableTour()
      startAdminTimetableTour()

      // For backward compatibility
      localStorage.removeItem('tour_completed_admin_timetable')
      localStorage.removeItem('tour_skipped_admin_timetable')

      // Set trigger flag
      localStorage.setItem('trigger_admin_timetable_tour', 'true')

      // Directly dispatch the event to start the tour
      window.dispatchEvent(new CustomEvent('start-admin-timetable-tour'))
    } else {
      // Use the old approach for other tours
      localStorage.removeItem(`tour_completed_${tourType}`)
      localStorage.removeItem(`tour_skipped_${tourType}`)

      // Dispatch event to start the tour
      const eventName =
        tourType === 'admin_timetable'
          ? 'start-admin-timetable-tour'
          : `start-${tourType}-tour`
      window.dispatchEvent(new CustomEvent(eventName))
    }

    // Update the tour completed state
    setTourCompleted(false)
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={`flex items-center gap-2 ${className}`}
      onClick={handleStartTour}
    >
      <Play className="h-4 w-4" />
      <span>{tourCompleted ? 'Restart Tour' : 'Take Tour'}</span>
    </Button>
  )
}
