import { useEffect } from 'react'
import { api } from '@/lib/api/axios-instance'

// Define the required pages for each role
const requiredPagesByRole: Record<string, string[]> = {
  SuperAdmin: ['dashboard', 'establishments', 'database', 'profile'],
  // We can add other roles later
  Admin: [],
  Teacher: [],
  Student: [],
  Parent: [],
}

/**
 * Custom hook to track page visits for onboarding
 * @param currentPage The current page being visited
 * @param userId The user's ID
 * @param userRole The user's role
 */
export function useOnboardingTracker(
  currentPage: string,
  userId: string,
  userRole: string
) {
  useEffect(() => {
    trackPageVisit(currentPage, userId, userRole)
  }, [currentPage, userId, userRole])
}

/**
 * Regular function to track page visits for onboarding
 * @param currentPage The current page being visited
 * @param userId The user's ID
 * @param userRole The user's role
 */
export function trackPageVisit(
  currentPage: string,
  userId: string,
  userRole: string
) {
  // Skip if no user ID or role
  if (!userId || !userRole) return

  // Get the required pages for this role
  const requiredPages = requiredPagesByRole[userRole] || []
  if (requiredPages.length === 0) return // No required pages for this role

  // Get visited pages from localStorage
  const key = `onboarding-pages-${userId}`
  const visitedPages = JSON.parse(localStorage.getItem(key) || '[]')

  // If this page hasn't been visited yet, add it
  if (
    !visitedPages.includes(currentPage) &&
    requiredPages.includes(currentPage)
  ) {
    console.log(`Onboarding: Marking page ${currentPage} as visited`)
    const updatedPages = [...visitedPages, currentPage]
    localStorage.setItem(key, JSON.stringify(updatedPages))

    // Check if all required pages have been visited
    const allVisited = requiredPages.every((page) =>
      updatedPages.includes(page)
    )

    if (allVisited) {
      console.log(
        'Onboarding: All required pages visited, completing onboarding'
      )
      completeOnboarding(userId)
    } else {
      // Log progress
      console.log(
        `Onboarding progress: ${updatedPages.length}/${requiredPages.length} pages visited`
      )
    }
  }
}

/**
 * Function to notify the backend that onboarding is complete
 * @param userId The user's ID
 */
async function completeOnboarding(userId: string) {
  try {
    // Update the backend
    await api.patch(`/api/users/${userId}/onboarding`, {
      userOnboarding: false,
    })

    // Update localStorage to indicate onboarding is complete
    localStorage.setItem('userOnboarding', 'false')

    // Dispatch an event to notify other components
    window.dispatchEvent(new CustomEvent('onboarding-completed'))

    console.log('Onboarding completed successfully')
  } catch (error) {
    console.error('Failed to complete onboarding:', error)
  }
}

/**
 * Function to reset the onboarding process
 * @param userId The user's ID
 */
export async function resetOnboarding(userId: string) {
  try {
    // Clear localStorage
    const key = `onboarding-pages-${userId}`
    localStorage.removeItem(key)
    localStorage.setItem('userOnboarding', 'true')

    // Update the backend
    await api.patch(`/api/users/${userId}/onboarding`, {
      userOnboarding: true,
    })

    // Dispatch an event to notify other components
    window.dispatchEvent(new CustomEvent('onboarding-reset'))

    console.log('Onboarding reset successfully')

    // Reload the page to start fresh
    window.location.reload()
  } catch (error) {
    console.error('Failed to reset onboarding:', error)
  }
}

/**
 * Function to check if onboarding is needed
 * @param userId The user's ID
 * @param userRole The user's role
 * @returns Whether onboarding is needed
 */
export function isOnboardingNeeded(userId: string, userRole: string): boolean {
  // For new users, we need to set userOnboarding to true
  if (localStorage.getItem('userOnboarding') === null) {
    console.log(
      'isOnboardingNeeded: New user detected, setting userOnboarding to true'
    )
    localStorage.setItem('userOnboarding', 'true')
    return true
  }

  // If userOnboarding is explicitly set to false, no onboarding is needed
  if (localStorage.getItem('userOnboarding') === 'false') {
    return false
  }

  // If userOnboarding is explicitly set to true, onboarding is needed
  if (localStorage.getItem('userOnboarding') === 'true') {
    return true
  }

  // Get the required pages for this role
  const requiredPages = requiredPagesByRole[userRole] || []
  if (requiredPages.length === 0) return false // No required pages for this role

  // Get visited pages from localStorage
  const key = `onboarding-pages-${userId}`
  const visitedPages = JSON.parse(localStorage.getItem(key) || '[]')

  // Check if all required pages have been visited
  return !requiredPages.every((page) => visitedPages.includes(page))
}
