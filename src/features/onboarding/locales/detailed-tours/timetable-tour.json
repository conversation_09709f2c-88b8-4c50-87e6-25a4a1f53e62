{"timetable": {"intro": {"target": ".service-card-1", "title": "Timetable Management", "content": "Let's explore the Timetable Management system in detail. This powerful tool helps you create and manage comprehensive schedules for your entire school. You'll learn how to create timetables, assign teachers, manage classes, and resolve scheduling conflicts."}, "overview": {"target": ".timetable-header", "title": "Timetable Dashboard", "content": "This is the main timetable dashboard where you can see all your created timetables and their status. From here, you can:<br/><br/>1. <b>View existing timetables</b> and their status<br/>2. <b>Create new timetables</b> for different academic periods<br/>3. <b>Activate or deactivate</b> specific timetables<br/>4. <b>View statistics</b> about your current scheduling<br/>5. <b>Access quick views</b> of class and teacher schedules"}, "createNew": {"target": ".create-timetable-button", "title": "Creating a New Timetable", "content": "To create a new timetable:<br/><br/>1. Click the 'Generate New Timetable' button<br/>2. Enter a descriptive name for your timetable<br/>3. Select the academic year or term<br/>4. Choose whether this should be the active timetable<br/>5. Click 'Create' to proceed to the timetable editor"}, "timetableSetup": {"target": ".timetable-grid", "title": "Timetable Setup", "content": "Before creating your schedule, you'll need to configure:<br/><br/>1. <b>School days</b> - Select which days of the week to include<br/>2. <b>Time slots</b> - Define periods and their duration<br/>3. <b>Break times</b> - Set lunch and other break periods<br/>4. <b>Classes</b> - Select which classes to include<br/>5. <b>Subjects</b> - Define which subjects need scheduling"}, "dragDrop": {"target": ".timetable-grid", "title": "Using the Drag and Drop Interface", "content": "To schedule classes:<br/><br/>1. <b>Select a subject</b> from the left panel<br/>2. <b>Drag it</b> to an empty time slot in the grid<br/>3. <b>Assign a teacher</b> from the dropdown that appears<br/>4. <b>Select a classroom</b> for the session<br/>5. <b>Adjust duration</b> if needed by dragging the bottom edge"}, "classView": {"target": ".timetable-grid", "title": "Class View", "content": "To view and manage class schedules:<br/><br/>1. Select 'Class View' from the view selector<br/>2. Choose a specific class from the dropdown<br/>3. View all scheduled subjects for that class<br/>4. Make adjustments by dragging subjects to different slots<br/>5. Add new subjects by dragging from the subject panel"}, "teacherView": {"target": ".timetable-grid", "title": "Teacher View", "content": "To view and manage teacher schedules:<br/><br/>1. Select 'Teacher View' from the view selector<br/>2. Choose a specific teacher from the dropdown<br/>3. View all classes assigned to that teacher<br/>4. Check for overloaded schedules or gaps<br/>5. Make adjustments to balance teaching loads"}, "roomView": {"target": ".timetable-grid", "title": "Room View", "content": "To manage classroom assignments:<br/><br/>1. Select 'Room View' from the view selector<br/>2. Choose a specific classroom from the dropdown<br/>3. View all classes scheduled in that room<br/>4. Check for double-bookings or unused periods<br/>5. Reassign classes to different rooms if needed"}, "conflicts": {"target": ".conflict-detection", "title": "Resolving Scheduling Conflicts", "content": "The system automatically detects conflicts such as:<br/><br/>1. <b>Teacher double-booking</b> - When a teacher is assigned to two classes at once<br/>2. <b>Room conflicts</b> - When a room is scheduled for multiple classes<br/>3. <b>Class overlaps</b> - When a class has two subjects at the same time<br/>4. <b>Availability conflicts</b> - When teachers are scheduled outside their available hours<br/><br/>Click on any highlighted conflict to see resolution options."}, "teacherAvailability": {"target": ".teacher-availability", "title": "Managing Teacher Availability", "content": "To set teacher availability:<br/><br/>1. Click on a teacher's name in the list<br/>2. Use the availability calendar to mark when they can teach<br/>3. Set recurring availability patterns for each day<br/>4. Add exceptions for specific dates<br/>5. Save changes to apply these constraints to scheduling"}, "autoSchedule": {"target": ".create-timetable-button", "title": "Automatic Scheduling", "content": "Let the system create a schedule automatically:<br/><br/>1. Click the 'Auto-Schedule' button<br/>2. Set your scheduling priorities (minimize gaps, balance teacher loads, etc.)<br/>3. Select which classes and subjects to include<br/>4. Click 'Generate Schedule'<br/>5. Review and make manual adjustments as needed"}, "savePublish": {"target": ".export-options", "title": "Saving and Publishing", "content": "When your timetable is ready:<br/><br/>1. Click 'Save' to store your current progress<br/>2. Use 'Save As' to create a new version<br/>3. Click 'Publish' to make it visible to teachers and students<br/>4. Set the activation date for when the schedule goes live<br/>5. Send notifications to inform users about the new schedule"}, "export": {"target": ".export-options", "title": "Export Options", "content": "Export your timetable in various formats:<br/><br/>1. <b>PDF</b> - For printing physical copies<br/>2. <b>Excel</b> - For further analysis or modifications<br/>3. <b>Google Calendar</b> - For integration with personal calendars<br/>4. <b>iCal</b> - For compatibility with most calendar applications<br/>5. <b>JSON/API</b> - For integration with other systems"}, "finish": {"target": ".back-to-dashboard", "title": "Return to Dashboard", "content": "When you're done, click here to return to the main dashboard. Remember that you can access the timetable management system anytime from the main services menu. If you need help with any feature, look for the '?' icon next to each section header for detailed instructions."}}}