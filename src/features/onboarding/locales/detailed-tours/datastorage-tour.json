{"dataStorage": {"intro": {"target": ".service-card-2", "title": "Data Storage System", "content": "Let's explore the Data Storage system in detail. This comprehensive system allows you to manage all school data in one centralized location. You'll learn how to create and manage students, teachers, parents, classes, and more."}, "overview": {"target": ".data-storage-header", "title": "Data Storage Overview", "content": "This is the main data storage dashboard where you can access different categories of school data. Each card below represents a different type of data you can manage. Click on any card to access that section."}, "studentRecords": {"target": ".student-records-section", "title": "Student Management", "content": "Click on the Students card to access the Student Management section. Here you can: <br/><br/>1. <b>Create new students</b> by clicking the 'Add Student' button<br/>2. <b>Edit existing students</b> by clicking on a student's row<br/>3. <b>Delete students</b> by using the delete icon<br/>4. <b>Search and filter</b> students using the search bar<br/>5. <b>Manage payment status</b> with the toggle button"}, "teacherRecords": {"target": ".database-card-teachers", "title": "Teacher Management", "content": "Click on the Teachers card to access the Teacher Management section. Here you can: <br/><br/>1. <b>Add new teachers</b> with the 'Add New Teacher' button at the top of the page<br/>2. <b>Fill in required fields</b> including CIN, name, email, password, and title (Professor, Dr., etc.)<br/>3. <b>Set optional fields</b> like birthday and upload a profile avatar<br/>4. <b>View teacher profiles</b> by clicking the Profile button on any teacher row<br/>5. <b>Send messages</b> to teachers using the Chat button"}, "parentRecords": {"target": ".database-card-parents", "title": "Parent Management", "content": "Click on the Parents card to manage parent accounts. Here you can: <br/><br/>1. <b>Create parent accounts</b> with the 'Add New Parent' button<br/>2. <b>Link parents to students</b> using the student assignment feature<br/>3. <b>Update contact information</b> for emergency contacts<br/>4. <b>Manage access permissions</b> for the parent portal<br/>5. <b>Send notifications</b> to specific parents or groups"}, "classManagement": {"target": ".database-card-classes", "title": "Class Management", "content": "Click on the Classes card to manage class information. Here you can: <br/><br/>1. <b>Create new classes</b> with the 'Add Class' button<br/>2. <b>Enter class name</b> and select the grade level<br/>3. <b>Assign a supervisor</b> from your teacher list<br/>4. <b>Assign students to classes</b> using the student roster feature<br/>5. <b>View and edit</b> existing classes by clicking on them in the list"}, "subjectManagement": {"target": ".database-card-subjects", "title": "Subject Management", "content": "Click on the Subjects card to manage academic subjects. Here you can: <br/><br/>1. <b>Create new subjects</b> with the 'Add Subject' button<br/>2. <b>Define subject requirements</b> and prerequisites<br/>3. <b>Assign teachers</b> to subjects<br/>4. <b>Set grading scales</b> for each subject<br/>5. <b>Organize subjects</b> by department or grade level"}, "classroomManagement": {"target": ".database-card-classrooms", "title": "Classroom Management", "content": "Click on the Classrooms card to manage physical spaces. Here you can: <br/><br/>1. <b>Add new classrooms</b> with the 'Add Classroom' button in the top right<br/>2. <b>Enter required details</b> including name, type, capacity, floor, and building<br/>3. <b>Add special features</b> like 'projector, whiteboard, computers' as a comma-separated list<br/>4. <b>Add optional notes</b> about the classroom<br/>5. <b>Set active status</b> to control whether the room appears in scheduling options"}, "gradeManagement": {"target": ".database-card-grades", "title": "Grade Management", "content": "Click on the Grades card to manage academic grades. Here you can: <br/><br/>1. <b>Create grade levels</b> with the 'Add Grade' button<br/>2. <b>Define grade requirements</b> and promotion criteria<br/>3. <b>View student performance</b> by grade level<br/>4. <b>Generate grade reports</b> for administration<br/>5. <b>Track grade progression</b> across academic years"}, "dataCreation": {"target": ".database-overview", "title": "Creating New Records", "content": "To create any new record (student, teacher, class, etc.):<br/><br/>1. <PERSON>lick on the appropriate card to navigate to that section<br/>2. Look for the 'Add' or 'Create' button at the top of the page<br/>3. Fill in all required fields in the form that appears<br/>4. Click 'Save' or 'Submit' to create the record<br/>5. The new record will appear in the list immediately"}, "addingTeacher": {"target": ".database-overview", "title": "How to Add a Teacher", "content": "To add a new teacher:<br/><br/>1. Navigate to the Teacher Management section<br/>2. Click the 'Add New Teacher' button at the top right<br/>3. Fill in required fields: CIN (ID number), First Name, Last Name, Email, Password, and Title<br/>4. Select gender and add birthday using the date picker<br/>5. Optionally upload a profile avatar<br/>6. Click 'Add Teacher' to create the account"}, "addingClassroom": {"target": ".database-overview", "title": "How to Add a Classroom", "content": "To add a new classroom:<br/><br/>1. Navigate to the Classroom Management section<br/>2. Click the 'Add Classroom' button at the top right<br/>3. Enter the classroom name (e.g., 'Room 101')<br/>4. Select the type (e.g., 'Classroom', 'Laboratory', 'Auditorium')<br/>5. Enter capacity, floor, and building information<br/>6. Add features as a comma-separated list (e.g., 'projector, whiteboard, computers')<br/>7. Click 'Add Classroom' to create the new room"}, "dataEditing": {"target": ".database-overview", "title": "Editing Existing Records", "content": "To edit any existing record:<br/><br/>1. Find the record in the list view<br/>2. Click on the record row or the edit icon<br/>3. Update the information in the form that appears<br/>4. Click 'Save' to update the record<br/>5. Changes will be reflected immediately in the system"}, "dataSearch": {"target": ".database-overview", "title": "Searching and Filtering", "content": "Each section includes powerful search and filter capabilities:<br/><br/>1. Use the search bar to find specific records by name or ID<br/>2. Use the filter dropdown to narrow results by specific criteria<br/>3. Sort columns by clicking on column headers<br/>4. Use advanced filters for complex queries<br/>5. Export filtered results using the export tools"}, "exportTools": {"target": ".export-tools", "title": "Export and Reporting Tools", "content": "Generate custom reports and export data in various formats:<br/><br/>1. Click the 'Export' button in any data section<br/>2. Select your preferred format (PDF, Excel, CSV)<br/>3. Choose which fields to include in the export<br/>4. Apply any filters to limit the exported data<br/>5. Click 'Generate' to create and download your report"}, "dataBackup": {"target": ".backup-section", "title": "Data Backup and Recovery", "content": "Protect your school's data with our backup tools:<br/><br/>1. Schedule automatic backups using the 'Schedule' button<br/>2. Create manual backups before major changes<br/>3. View backup history and status<br/>4. Restore data from previous backups if needed<br/>5. Export backups for offline storage"}, "finish": {"target": ".back-to-dashboard", "title": "Return to Dashboard", "content": "When you're done, click here to return to the main dashboard. Remember that you can access the Data Storage system anytime from the main services menu. If you need help with any feature, look for the '?' icon next to each section header for detailed instructions."}}}