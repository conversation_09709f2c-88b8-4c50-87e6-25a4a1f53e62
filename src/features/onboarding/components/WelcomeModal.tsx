import React from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Database, Calendar, UserCheck, School, BookOpen } from 'lucide-react'

interface WelcomeModalProps {
  isOpen: boolean
  onClose: () => void
  onStartTour: () => void
}

export const WelcomeModal: React.FC<WelcomeModalProps> = ({
  isOpen,
  onClose,
  onStartTour,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-primary">
            Welcome to JeridSchool Admin Dashboard
          </DialogTitle>
          <DialogDescription className="text-base pt-2">
            Your complete school management solution
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-6">
          <p className="text-lg">
            Let's take a comprehensive tour to help you get the most out of the
            system. You'll learn how to:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
              <Database className="h-6 w-6 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium">Data Management</h3>
                <p className="text-sm text-gray-600">
                  Create and manage students, teachers, parents, classes, and
                  more
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
              <Calendar className="h-6 w-6 text-green-600 mt-0.5" />
              <div>
                <h3 className="font-medium">Timetable Creation</h3>
                <p className="text-sm text-gray-600">
                  Build comprehensive schedules with our drag-and-drop interface
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-purple-50 rounded-lg">
              <UserCheck className="h-6 w-6 text-purple-600 mt-0.5" />
              <div>
                <h3 className="font-medium">User Management</h3>
                <p className="text-sm text-gray-600">
                  Manage permissions, roles, and access controls
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 bg-amber-50 rounded-lg">
              <School className="h-6 w-6 text-amber-600 mt-0.5" />
              <div>
                <h3 className="font-medium">Academic Tools</h3>
                <p className="text-sm text-gray-600">
                  Track grades, attendance, and student performance
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium flex items-center">
              <BookOpen className="h-5 w-5 text-primary mr-2" />
              How the tour works:
            </h3>
            <ul className="list-disc pl-6 space-y-1 mt-2 text-sm">
              <li>
                The tour will guide you through each section of the dashboard
              </li>
              <li>You can pause the tour at any time and resume later</li>
              <li>Your progress is automatically saved</li>
              <li>
                Each section has its own detailed tour for in-depth learning
              </li>
              <li>You can restart any tour from the help menu</li>
            </ul>
          </div>

          <p className="text-sm text-muted-foreground mt-2 italic">
            This tour is designed to help both new and experienced
            administrators get the most out of JeridSchool's powerful features.
          </p>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between pt-2">
          <Button variant="outline" onClick={onClose} className="px-5">
            Skip for Now
          </Button>
          <Button onClick={onStartTour} className="bg-primary px-5">
            Start Interactive Tour
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
