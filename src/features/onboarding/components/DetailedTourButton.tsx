import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Info } from 'lucide-react'
import { DetailedTourType } from '../services/detailedTourService'

interface DetailedTourButtonProps {
  tourType: DetailedTourType
  onStartTour: () => void
  label?: string
  className?: string
}

export const DetailedTourButton: React.FC<DetailedTourButtonProps> = ({
  tourType,
  onStartTour,
  label,
  className = '',
}) => {
  // Get the appropriate label based on tour type
  const getDefaultLabel = () => {
    switch (tourType) {
      case 'timetable':
        return 'Explore Timetable Features'
      case 'dataStorage':
        return 'Explore Data Storage Features'
      default:
        return 'Take a Tour'
    }
  }

  const buttonLabel = label || getDefaultLabel()

  return (
    <Button
      variant="outline"
      size="sm"
      className={`flex items-center gap-2 bg-white/90 hover:bg-primary/10 text-primary border-primary/30 shadow-sm transition-all duration-200 tour-button ${className}`}
      onClick={onStartTour}
    >
      <Info className="h-4 w-4" />
      <span>{buttonLabel}</span>
    </Button>
  )
}
