import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Function to check if we're on the login page
const isOnLoginPage = (): boolean => {
  // Check if window is defined (for SSR)
  if (typeof window !== 'undefined') {
    return (
      window.location.pathname === '/login' ||
      window.location.href.includes('/login')
    )
  }
  return false
}

// Query keys for onboarding
// Commented out to fix TypeScript build errors
// const ONBOARDING_KEYS = {
//   progress: ['onboarding', 'progress'],
//   status: ['onboarding', 'status'],
// }

// Onboarding progress interface
export interface OnboardingProgress {
  isActive: boolean
  completedSteps: string[]
  currentStep: number
  totalSteps: number
  lastVisited: string
  currentSection?: string
}

interface OnboardingContextType {
  needsOnboarding: boolean
  isOnboardingActive: boolean
  onboardingProgress: OnboardingProgress | null
  startOnboarding: () => void
  completeOnboarding: () => Promise<void>
  resetOnboarding: () => Promise<void>
  skipOnboarding: () => Promise<void>
  updateOnboardingProgress: (progress: Partial<OnboardingProgress>) => void
  getSectionProgress: (section: string) => boolean
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
)

interface OnboardingProviderProps {
  children: ReactNode
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({
  children,
}) => {
  const queryClient = useQueryClient()
  const [isOnboardingActive, setIsOnboardingActive] = useState<boolean>(false)

  // Function to get onboarding progress from localStorage
  const getOnboardingProgress = (): OnboardingProgress | null => {
    try {
      const savedProgress = localStorage.getItem('onboardingProgress')
      if (savedProgress) {
        return JSON.parse(savedProgress) as OnboardingProgress
      }

      // Initialize with default progress if none exists
      const initialProgress: OnboardingProgress = {
        isActive: false,
        completedSteps: [],
        currentStep: 0,
        totalSteps: 8, // Default total steps
        lastVisited: new Date().toISOString(),
      }
      localStorage.setItem(
        'onboardingProgress',
        JSON.stringify(initialProgress)
      )
      return initialProgress
    } catch (error) {
      console.error('Error loading onboarding progress:', error)
      return null
    }
  }

  // Function to check if user needs onboarding
  const checkNeedsOnboarding = (): boolean => {
    // Don't show onboarding on login page
    if (isOnLoginPage()) {
      return false
    }

    // Get user role from localStorage
    const role = localStorage.getItem('role')

    // Check if user has completed onboarding
    const hasCompletedOnboarding =
      localStorage.getItem('onboardingCompleted') === 'true'

    // Check if user is logged in (has a role and token)
    const isLoggedIn = !!role && !!localStorage.getItem('access_token')

    // Only return true if the user is logged in and hasn't completed onboarding
    return !hasCompletedOnboarding && isLoggedIn
  }

  // Query for onboarding progress
  const { data: onboardingProgress } = useQuery({
    queryKey: ['onboarding', 'progress'],
    queryFn: getOnboardingProgress,
    staleTime: Infinity, // Don't refetch automatically
    initialData: getOnboardingProgress,
  })

  // Query for onboarding status
  const { data: needsOnboarding = false } = useQuery({
    queryKey: ['onboarding', 'status'],
    queryFn: checkNeedsOnboarding,
    staleTime: Infinity, // Don't refetch automatically
    initialData: checkNeedsOnboarding,
  })

  // Set active state based on progress when component mounts
  useEffect(() => {
    if (onboardingProgress?.isActive) {
      setIsOnboardingActive(true)
    }
  }, [onboardingProgress?.isActive])

  // Mutation to update onboarding progress
  const updateProgressMutation = useMutation({
    mutationFn: async (
      progress: Partial<OnboardingProgress>
    ): Promise<OnboardingProgress | null> => {
      const current = queryClient.getQueryData<OnboardingProgress | null>([
        'onboarding',
        'progress',
      ])
      if (!current) return Promise.resolve(null)

      const updated = { ...current, ...progress }

      // Save to localStorage
      localStorage.setItem('onboardingProgress', JSON.stringify(updated))

      return Promise.resolve(updated)
    },
    onSuccess: (data) => {
      if (data) {
        // Update the query cache with the new data
        queryClient.setQueryData(['onboarding', 'progress'], data)
      }
    },
  })

  // Update onboarding progress
  const updateOnboardingProgress = (progress: Partial<OnboardingProgress>) => {
    updateProgressMutation.mutate(progress)
  }

  // Check if a specific section has been completed
  const getSectionProgress = (section: string): boolean => {
    if (!onboardingProgress) return false

    // Check if the section is in the completed steps
    return onboardingProgress.completedSteps.includes(section)
  }

  // Start the onboarding process
  const startOnboarding = () => {
    // Don't start onboarding on login page
    if (isOnLoginPage()) {
      return
    }

    setIsOnboardingActive(true)

    // Update progress to mark as active
    updateOnboardingProgress({
      isActive: true,
      lastVisited: new Date().toISOString(),
    })
  }

  // Mutation to complete onboarding
  const completeOnboardingMutation = useMutation({
    mutationFn: async () => {
      // Store in localStorage that onboarding is completed
      localStorage.setItem('onboardingCompleted', 'true')

      const current = queryClient.getQueryData<OnboardingProgress | null>([
        'onboarding',
        'progress',
      ])

      // Update progress
      const updated = {
        ...(current || {}),
        isActive: false,
        completedSteps: current?.completedSteps || [],
        lastVisited: new Date().toISOString(),
      } as OnboardingProgress

      // Save to localStorage
      localStorage.setItem('onboardingProgress', JSON.stringify(updated))

      return updated
    },
    onSuccess: (data) => {
      // Update the query cache with the new data
      queryClient.setQueryData(['onboarding', 'progress'], data)
      queryClient.setQueryData(['onboarding', 'status'], false)

      setIsOnboardingActive(false)
      console.log('Onboarding completed successfully')
    },
    onError: (error) => {
      console.error('Failed to complete onboarding:', error)
    },
  })

  // Mark onboarding as complete
  const completeOnboarding = async (): Promise<void> => {
    await completeOnboardingMutation.mutateAsync()
  }

  // Mutation to reset onboarding
  const resetOnboardingMutation = useMutation({
    mutationFn: async () => {
      // Remove the onboarding completed flag from localStorage
      localStorage.removeItem('onboardingCompleted')

      // Reset progress
      const initialProgress: OnboardingProgress = {
        isActive: false,
        completedSteps: [],
        currentStep: 0,
        totalSteps: 8,
        lastVisited: new Date().toISOString(),
      }

      // Save to localStorage
      localStorage.setItem(
        'onboardingProgress',
        JSON.stringify(initialProgress)
      )

      return initialProgress
    },
    onSuccess: (data) => {
      // Update the query cache with the new data
      queryClient.setQueryData(['onboarding', 'progress'], data)
      queryClient.setQueryData(['onboarding', 'status'], true)

      setIsOnboardingActive(false)
      console.log('Onboarding reset successfully')
    },
    onError: (error) => {
      console.error('Failed to reset onboarding:', error)
    },
  })

  // Reset onboarding (for testing)
  const resetOnboarding = async (): Promise<void> => {
    await resetOnboardingMutation.mutateAsync()
  }

  // Skip onboarding but mark as complete
  const skipOnboarding = async (): Promise<void> => {
    await completeOnboarding()
  }

  return (
    <OnboardingContext.Provider
      value={{
        needsOnboarding,
        isOnboardingActive,
        onboardingProgress,
        startOnboarding,
        completeOnboarding,
        resetOnboarding,
        skipOnboarding,
        updateOnboardingProgress,
        getSectionProgress,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  )
}

export const useOnboarding = (): OnboardingContextType => {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider')
  }
  return context
}
