import { Button } from '@/components/ui/button'
import { RefreshCcw } from 'lucide-react'

interface ResetTourButtonProps {
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
}

export function ResetTourButton({
  variant = 'outline',
  size = 'sm',
  className = '',
}: ResetTourButtonProps) {
  const handleResetTour = async () => {
    const userId = localStorage.getItem('id')
    const userRole = localStorage.getItem('role')

    console.log('Reset Tour button clicked', { userId, userRole })

    if (userId && userRole) {
      // Force set userOnboarding to true
      localStorage.setItem('userOnboarding', 'true')

      // Clear all tour completion flags
      localStorage.removeItem('tour_completed_super_admin')
      localStorage.removeItem('tour_completed_establishment')
      localStorage.removeItem('tour_completed_database')
      localStorage.removeItem('tour_completed_profile')

      // Clear visited pages
      const key = `onboarding-pages-${userId}`
      localStorage.removeItem(key)

      // Trigger the tour on the current page
      if (window.location.pathname.includes('/super_admin')) {
        console.log('Triggering SuperAdmin tour')
        window.dispatchEvent(new CustomEvent('start-super-admin-tour'))
      }

      // Reload the page to restart everything
      window.location.reload()
    } else {
      console.error('Cannot reset tour: User ID or role not found')
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={`flex items-center gap-1 ${className}`}
      onClick={handleResetTour}
      data-testid="reset-tour-button"
    >
      <RefreshCcw className="h-4 w-4" />
      Reset Tour
    </Button>
  )
}
