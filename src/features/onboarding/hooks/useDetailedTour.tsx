import { useState, useEffect } from 'react'
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride'
import {
  DetailedTourType,
  getDetailedTourSteps,
  getDetailedTourOptions,
} from '../services/detailedTourService'

interface UseDetailedTourProps {
  tourType: DetailedTourType
  onComplete?: () => void
  onSkip?: () => void
}

export const useDetailedTour = ({
  tourType,
  onComplete,
  onSkip,
}: UseDetailedTourProps) => {
  const [steps, setSteps] = useState<Step[]>([])
  const [run, setRun] = useState(false)
  const [tourReady, setTourReady] = useState(false)

  // Load steps for the specified tour type
  useEffect(() => {
    const tourSteps = getDetailedTourSteps(tourType)
    setSteps(tourSteps)
    setTourReady(tourSteps.length > 0)
  }, [tourType])

  const startTour = () => {
    if (tourReady) {
      setRun(true)
    } else {
      console.warn(`Tour ${tourType} is not ready or has no steps`)
    }
  }

  const stopTour = () => {
    setRun(false)
  }

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, type } = data

    if (status === STATUS.FINISHED) {
      setRun(false)
      onComplete?.()
    } else if (status === STATUS.SKIPPED) {
      setRun(false)
      onSkip?.()
    }

    // Log step changes for debugging
    if (type === 'step:after') {
      console.log(
        `Detailed tour step completed: ${data.index + 1}/${steps.length}`
      )
    }
  }

  const joyrideOptions = getDetailedTourOptions()

  const TourComponent = () => (
    <Joyride
      steps={steps}
      run={run}
      callback={handleJoyrideCallback}
      {...joyrideOptions} // Use the spread first to allow overrides
    />
  )

  return {
    startTour,
    stopTour,
    TourComponent,
    isRunning: run,
    isReady: tourReady,
  }
}
