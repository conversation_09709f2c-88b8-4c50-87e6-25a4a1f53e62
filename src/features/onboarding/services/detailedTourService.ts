import { Step } from 'react-joyride'

// Define the types of detailed tours available in the application
export type DetailedTourType = 'timetable' | 'dataStorage' | 'general'

// Service for managing detailed tours
export const detailedTourService = {
  // Start a specific tour
  startTour: (tourType: DetailedTourType): void => {
    // Dispatch a custom event to start the tour
    window.dispatchEvent(new CustomEvent(`start-${tourType}-tour`))

    // Log that the tour has started
    console.log(`Starting ${tourType} tour`)
  },

  // Check if a tour is completed
  isTourCompleted: (tourType: DetailedTourType): boolean => {
    return localStorage.getItem(`tour_completed_${tourType}`) === 'true'
  },

  // Mark a tour as completed
  completeTour: (tourType: DetailedTourType): void => {
    localStorage.setItem(`tour_completed_${tourType}`, 'true')
    console.log(`Completed ${tourType} tour`)
  },

  // Reset a tour (mark as not completed)
  resetTour: (tourType: DetailedTourType): void => {
    localStorage.removeItem(`tour_completed_${tourType}`)
    console.log(`Reset ${tourType} tour`)
  },
}

// Get tour steps based on tour type
export const getDetailedTourSteps = (tourType: DetailedTourType): Step[] => {
  switch (tourType) {
    case 'timetable':
      return [
        {
          target: 'body',
          content: 'Welcome to the Timetable feature tour!',
          placement: 'center',
          disableBeacon: true,
          title: 'Timetable Tour',
        },
        {
          target: '.timetable-generator',
          content: 'This is where you can generate new timetables.',
          placement: 'bottom',
          title: 'Timetable Generator',
        },
        {
          target: '.timetable-display',
          content: 'Here you can view and edit your timetables.',
          placement: 'bottom',
          title: 'Timetable Display',
        },
      ]
    case 'dataStorage':
      return [
        {
          target: 'body',
          content: 'Welcome to the Data Storage feature tour!',
          placement: 'center',
          disableBeacon: true,
          title: 'Data Storage Tour',
        },
        {
          target: '.data-storage-section',
          content: 'This is where you can manage your data storage.',
          placement: 'bottom',
          title: 'Data Storage Section',
        },
      ]
    default:
      return []
  }
}

// Get Joyride options for detailed tours
export const getDetailedTourOptions = () => {
  return {
    continuous: true,
    showProgress: true,
    showSkipButton: true,
    disableOverlayClose: true,
    disableCloseOnEsc: true,
    spotlightClicks: true,
    styles: {
      options: {
        zIndex: 10000,
        primaryColor: '#007bff',
      },
    },
  }
}
