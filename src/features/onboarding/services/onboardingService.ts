import { Step } from 'react-joyride'
import enLocale from '../locales/en.json'
import frLocale from '../locales/fr.json'

// Type for the onboarding steps
export interface OnboardingStep {
  target: string
  title: string
  content: string
  disableBeacon?: boolean
}

// Type for the locale data
export interface LocaleData {
  [role: string]: {
    [key: string]: OnboardingStep
  }
}

// Get the current locale (could be expanded to use the app's locale system)
export const getCurrentLocale = (): 'en' | 'fr' => {
  // This could be connected to your app's locale system
  // For now, we'll default to English
  return 'en'
}

// Get the locale data based on the current locale
export const getLocaleData = (): LocaleData => {
  const locale = getCurrentLocale()
  return locale === 'fr' ? frLocale : enLocale
}

// Get the onboarding steps for a specific role
export const getOnboardingSteps = (role: string): Step[] => {
  const localeData = getLocaleData()
  const roleData = localeData[role.toLowerCase()]

  if (!roleData) {
    console.warn(`No onboarding steps found for role: ${role}`)
    return []
  }

  // Convert the role data to Joyride steps
  return Object.keys(roleData).map((key) => {
    const step = roleData[key]
    return {
      target: step.target,
      title: step.title,
      content: step.content,
      disableBeacon: step.disableBeacon || false,
      placement: 'auto' as const,
      disableOverlay: key === 'welcome', // Disable overlay for welcome step (modal)
    }
  })
}

// Configure Joyride options
export const getJoyrideOptions = () => {
  return {
    continuous: true,
    showProgress: true,
    showSkipButton: true,
    spotlightClicks: false,
    disableOverlayClose: true,
    disableScrolling: false,
    hideBackButton: false,
    hideCloseButton: false,
    scrollOffset: 20,
    scrollDuration: 300,
    floaterProps: {
      disableAnimation: false,
    },
    styles: {
      options: {
        primaryColor: '#525FE1', // Primary brand color
        textColor: '#1f2937', // gray-800
        zIndex: 10000,
        arrowColor: '#fff',
        backgroundColor: '#fff',
        overlayColor: 'rgba(0, 0, 0, 0.7)',
        width: 400, // Wider tooltips for better readability
      },
      spotlight: {
        backgroundColor: 'transparent',
        borderRadius: 8,
        boxShadow: '0 0 0 4px rgba(82, 95, 225, 0.3)', // Subtle highlight
      },
      tooltipContainer: {
        textAlign: 'left' as const,
        padding: '20px',
      },
      buttonNext: {
        backgroundColor: '#525FE1',
        borderRadius: '4px',
        color: '#fff',
        fontSize: '14px',
        padding: '8px 16px',
      },
      buttonBack: {
        color: '#6b7280',
        fontSize: '14px',
        marginRight: '10px',
      },
      buttonSkip: {
        color: '#6b7280',
        fontSize: '14px',
      },
      tooltip: {
        borderRadius: 8,
        fontSize: '15px',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
      },
      tooltipTitle: {
        fontSize: '18px',
        fontWeight: 'bold',
        marginBottom: '10px',
      },
      tooltipContent: {
        fontSize: '15px',
        lineHeight: '1.5',
      },
      buttonClose: {
        color: '#94a3b8',
        fontSize: '14px',
      },
    },
    locale: {
      back: 'Previous',
      close: 'Close',
      last: 'Finish',
      next: 'Next',
      skip: 'Skip Tour',
    },
  }
}
