import axios from 'axios'

/**
 * File upload response format from the CDN server
 */
export interface FileUploadResponse {
  success: boolean
  message: string
  data?: {
    id: string
    filename: string
    url: string
    size: number
    mime_type: string
    created_at: string
  }
}

/**
 * Class for handling file uploads to the CDN server
 */
export default class FileUploadService {
  private cdnUrl: string
  private cdnBaseUrl: string
  private cdnToken: string

  constructor() {
    // Always use HTTPS endpoint for CDN (works in browser and Postman)
    this.cdnBaseUrl = 'https://cdn.jeridschool.tech'
    this.cdnUrl = `${this.cdnBaseUrl}/upload`

    // Get the CDN token from environment variables
    this.cdnToken =
      import.meta.env.VITE_CDN_TOKEN ||
      '5ffaccd4ae0ee9afc5077f30ee32264dab8ca247d86eaed2b42d4dac1e954d9d'

    // Log CDN configuration
    console.log('CDN URLs configured:', {
      uploadUrl: this.cdnUrl,
      baseUrl: this.cdnBaseUrl,
      hasToken: !!this.cdnToken,
    })
  }

  /**
   * Upload a file to the CDN server
   * @param file The file to upload
   * @returns The response from the CDN server with the file URL
   */
  async uploadFile(file: File): Promise<FileUploadResponse> {
    try {
      console.log('Starting file upload to CDN...')
      
      // Check file size - maximum 20MB
      const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB in bytes
      if (file.size > MAX_FILE_SIZE) {
        console.warn(`File too large: ${file.name} (${file.size} bytes). Maximum size is 20MB.`);
        return {
          success: false,
          message: "File too large. Maximum size is 20MB"
        };
      }

      const formData = new FormData()
      formData.append('file', file)

      console.log('Uploading file to CDN URL:', this.cdnUrl)

      // Check if we have a token
      if (!this.cdnToken) {
        console.warn(
          'No CDN token found. Upload may fail with 401 Unauthorized.'
        )
      } else {
        console.log(
          'Using CDN token for authentication (first 8 chars):',
          this.cdnToken.substring(0, 8) + '...'
        )
      }

      const response = await axios.post(this.cdnUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${this.cdnToken}`,
        },
        timeout: 30000,
        validateStatus: null,
      })

      // Handle authentication error (401)
      if (response.status === 401) {
        console.error('Authentication failed: CDN token is invalid or missing')
        return {
          success: false,
          message:
            'Authentication failed: CDN token is invalid or missing (401 Unauthorized)',
        }
      }

      if (response.status >= 200 && response.status < 300 && response.data) {
        console.log('Upload successful, processing response:', response.data)

        // The CDN server should return a response in the format:
        // {
        //   success: true,
        //   message: "File uploaded successfully",
        //   data: {
        //     id: "5fd42108-495b-49f4-a664-7db3b7027b6b",
        //     filename: "example.png",
        //     url: "http://**************/cdn/files/5fd42108-495b-49f4-a664-7db3b7027b6b",
        //     size: 83193,
        //     mime_type: "image/png",
        //     created_at: "2025-03-23T01:47:08.425248177Z"
        //   }
        // }

        if (response.data.success && response.data.data) {
          return response.data
        }

        return {
          success: true,
          message: 'File uploaded successfully',
          data: response.data,
        }
      }

      console.error(
        'Error response from CDN server:',
        response.status,
        response.data
      )
      return {
        success: false,
        message: `CDN server responded with status ${response.status}: ${response.data?.message || 'Unknown error'}`,
      }
    } catch (error: any) {
      console.error('Error uploading file to CDN:', error)

      // Handle network errors and provide more information for debugging
      const errorMessage =
        error.message || 'Unknown error occurred during file upload'

      if (errorMessage.includes('Network Error')) {
        console.error('Network error details:', {
          url: this.cdnUrl,
          hasToken: !!this.cdnToken,
          errorType: error.code || 'Unknown',
        })
        return {
          success: false,
          message:
            'Network Error: Could not connect to CDN server. Check your internet connection and CDN service status.',
        }
      }

      return {
        success: false,
        message: errorMessage,
      }
    }
  }

  /**
   * Get the URL from a successful file upload response
   * @param response The response from the CDN server
   * @returns The file URL
   */
  getFileUrl(response: FileUploadResponse): string {
    if (!response.success || !response.data || !response.data.url) {
      throw new Error('Invalid file upload response')
    }

    let url = response.data.url

    // Enhanced logging for better visibility
    console.log(
      '%c File URL from response:',
      'background: #673AB7; color: white; padding: 2px 4px; border-radius: 2px; font-weight: bold;',
      url
    )

    // Display URL for easy copying
    console.log('\n------- UPLOADED IMAGE URL -------')
    console.log(url)
    console.log('----------------------------------\n')

    // Ensure HTTPS for CDN URLs
    if (url.startsWith('http://**************')) {
      url = url.replace('http://', 'https://')
      console.log('Converted HTTP to HTTPS for CDN URL:', url)
    }

    // Always proxy CDN URLs to ensure they work in the preview
    if (
      url.includes('cdn.jeridschool.tech') ||
      url.includes('**************')
    ) {
      // If it's not already proxied, proxy it
      if (!url.includes('/health/proxy/image')) {
        console.log('Proxying CDN URL for better compatibility:', url)
        url = `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(url)}`
      }
    }

    return url
  }

  /**
   * Create a direct URL to a file on the CDN server
   * @param fileId The ID of the file
   * @returns The URL to the file
   */
  createFileUrl(fileId: string): string {
    const url = `${this.cdnBaseUrl}/files/${fileId}`
    return this.ensureHttpsProtocol(url)
  }

  /**
   * Transform a CDN URL to ensure it can be loaded
   * @param url The URL to transform
   * @returns The transformed URL
   */
  transformUrl(url: string | null | undefined): string {
    if (!url) return ''

    console.log('Transforming URL:', url)

    // No transformation for blob URLs
    if (url.startsWith('blob:')) {
      console.log('Blob URL detected, not transforming:', url)
      return url
    }

    // Ensure HTTPS for CDN URLs
    if (url.startsWith('http://**************')) {
      url = url.replace('http://', 'https://')
      console.log('Converted HTTP to HTTPS for CDN URL:', url)
    }

    // Always proxy CDN URLs to ensure they work in the preview
    if (
      url.includes('cdn.jeridschool.tech') ||
      url.includes('**************')
    ) {
      // If it's not already proxied, proxy it
      if (!url.includes('/health/proxy/image')) {
        console.log(
          'Proxying CDN URL for better compatibility in transformUrl:',
          url
        )
        url = `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(url)}`
      }
    }

    return url
  }

  /**
   * Ensure a URL uses the HTTPS protocol
   * @param url The URL to check/convert
   * @returns The URL with HTTPS protocol
   */
  private ensureHttpsProtocol(url: string): string {
    if (url.startsWith('http:')) {
      return url.replace('http:', 'https:')
    }

    // If it's a relative URL or already uses HTTPS, return as is
    return url
  }
}
