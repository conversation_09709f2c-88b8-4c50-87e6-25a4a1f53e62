import {
  TimetableData,
  TimetableAnalysisResponse,
  TimetableAssignment,
} from '@/types/timetable'

const API_BASE_URL = 'https://timetable.jeridschool.tech'

export async function analyzeSchedule(
  data: any,
  result: TimetableData
): Promise<TimetableAnalysisResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/analyze-schedule`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data,
        result,
      }),
    })

    if (!response.ok) {
      throw new Error(
        `Error analyzing schedule: ${response.status} ${response.statusText}`
      )
    }

    return await response.json()
  } catch (error) {
    console.error('Error analyzing schedule:', error)
    return { unscheduledSubjects: [] }
  }
}

export function convertTimetableDataToAssignments(
  timetableData: TimetableData
): TimetableAssignment[] {
  const assignments: TimetableAssignment[] = []

  if (!timetableData || !timetableData.scheduleData) {
    return assignments
  }

  timetableData.scheduleData.forEach((classData) => {
    const className = classData.class

    // Process each day
    Object.keys(classData).forEach((day) => {
      // Skip the class property
      if (day === 'class') return

      // Make sure the day property is an array
      if (Array.isArray(classData[day])) {
        classData[day].forEach((lesson: any) => {
          if (lesson && lesson.subject) {
            assignments.push({
              ...lesson,
              day,
              class: className,
            })
          }
        })
      }
    })
  })

  return assignments
}

export function removeAssignmentFromTimetableData(
  timetableData: TimetableData,
  assignment: TimetableAssignment
): TimetableData {
  console.log('Removing assignment from timetable data:', assignment)

  // Create a deep copy of the timetable data
  const updatedData = JSON.parse(JSON.stringify(timetableData))

  // Find the class in the scheduleData
  const classIndex = updatedData.scheduleData.findIndex(
    (item: any) => item.class === assignment.class
  )

  if (classIndex === -1) {
    console.warn('Class not found in scheduleData:', assignment.class)
    return timetableData
  }

  // Find the day array
  const dayArray = updatedData.scheduleData[classIndex][assignment.day]

  if (!Array.isArray(dayArray)) {
    console.warn('Day array not found or not an array:', assignment.day)
    return timetableData
  }

  // Find the lesson index
  const lessonIndex = dayArray.findIndex(
    (lesson: any) =>
      lesson.time === assignment.time &&
      lesson.subject === assignment.subject &&
      lesson.teacherID === assignment.teacherID
  )

  if (lessonIndex === -1) {
    console.warn('Lesson not found in day array:', assignment)
    return timetableData
  }

  console.log('Found lesson at index:', lessonIndex, 'in day:', assignment.day)

  // Remove the lesson
  dayArray.splice(lessonIndex, 1)

  // Ensure the day key remains, even if empty
  // (Do NOT delete the day key, just leave it as an empty array if no lessons remain)

  return updatedData
}

export function addAssignmentToTimetableData(
  timetableData: TimetableData,
  assignment: TimetableAssignment,
  availableRoom?: string
): TimetableData {
  console.log(
    'Adding assignment to timetable data:',
    assignment,
    'with room:',
    availableRoom
  )

  // Create a deep copy of the timetable data
  const updatedData = JSON.parse(JSON.stringify(timetableData))

  // Find the class in the scheduleData
  const classIndex = updatedData.scheduleData.findIndex(
    (item: any) => item.class === assignment.class
  )

  if (classIndex === -1) {
    console.warn('Class not found in scheduleData:', assignment.class)
    return timetableData
  }

  // Make sure the day array exists
  if (!updatedData.scheduleData[classIndex][assignment.day]) {
    console.log('Creating new day array for:', assignment.day)
    updatedData.scheduleData[classIndex][assignment.day] = []
  }

  // Check if there's already a lesson at this time slot
  const existingLessonIndex = updatedData.scheduleData[classIndex][
    assignment.day
  ].findIndex((lesson: any) => lesson.time === assignment.time)

  // Create a new lesson object
  const newLesson = {
    time: assignment.time,
    subject: assignment.subject,
    teacherID: assignment.teacherID,
    teacher: assignment.teacher,
    salle: availableRoom || assignment.salle,
    group1: assignment.group1,
    group2: assignment.group2,
  }

  // If there's already a lesson at this time, replace it
  if (existingLessonIndex !== -1) {
    console.log('Replacing existing lesson at time:', assignment.time)
    updatedData.scheduleData[classIndex][assignment.day][existingLessonIndex] =
      newLesson
  } else {
    // Otherwise, add the new lesson to the day array
    console.log('Adding new lesson to day array')
    updatedData.scheduleData[classIndex][assignment.day].push(newLesson)
  }

  return updatedData
}
