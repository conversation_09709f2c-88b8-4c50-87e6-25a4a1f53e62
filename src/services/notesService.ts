import axios from 'axios'

// Use localhost:3000 for development
const BASE_URL = 'http://localhost:3000'

// Types for Notes Collections
export interface NotesCollection {
  id: string
  name: string
  visibility: 'private' | 'public'
  topic: string
  ownerId: string
  noteCount: number
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
  etablissementId: string
  notes?: Note[]
}

export interface CreateCollectionDto {
  name: string
  visibility: 'private' | 'public'
  topic: string
}

export interface UpdateCollectionDto {
  name?: string
  visibility?: 'private' | 'public'
  topic?: string
}

// Types for Notes
export interface Note {
  id: string
  title: string
  content: string
  order: number
  positionX?: number
  positionY?: number
  collectionId: string
  createdBy: string
  updatedBy: string
  createdAt: string
  updatedAt: string
}

export interface CreateNoteDto {
  title: string
  content: string
  collectionId: string
  positionX?: number
  positionY?: number
}

export interface UpdateNoteDto {
  title?: string
  content?: string
  positionX?: number
  positionY?: number
}

// Helper to get token from localStorage
const getAuthHeader = (token: string) => ({
  Authorization: `Bearer ${token}`
})

// API Service
export const notesService = {
  // Collections Endpoints
  getCollections: async (token: string): Promise<NotesCollection[]> => {
    try {
      const response = await axios.get(`${BASE_URL}/notes/collections`, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error('Error fetching collections:', error.message)
      throw error
    }
  },

  getCollectionById: async (id: string, token: string): Promise<NotesCollection> => {
    try {
      const response = await axios.get(`${BASE_URL}/notes/collections/${id}`, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error(`Error fetching collection ${id}:`, error.message)
      throw error
    }
  },

  createCollection: async (data: CreateCollectionDto, token: string): Promise<NotesCollection> => {
    try {
      const response = await axios.post(`${BASE_URL}/notes/collections`, data, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error('Error creating collection:', error.message)
      throw error
    }
  },

  updateCollection: async (id: string, data: UpdateCollectionDto, token: string): Promise<NotesCollection> => {
    try {
      const response = await axios.patch(`${BASE_URL}/notes/collections/${id}`, data, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error(`Error updating collection ${id}:`, error.message)
      throw error
    }
  },

  deleteCollection: async (id: string, token: string): Promise<void> => {
    try {
      await axios.delete(`${BASE_URL}/notes/collections/${id}`, {
        headers: getAuthHeader(token)
      })
    } catch (error: any) {
      console.error(`Error deleting collection ${id}:`, error.message)
      throw error
    }
  },

  // Notes Endpoints
  getNoteById: async (id: string, token: string): Promise<Note> => {
    try {
      const response = await axios.get(`${BASE_URL}/notes/notes/${id}`, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error(`Error fetching note ${id}:`, error.message)
      throw error
    }
  },

  createNote: async (data: CreateNoteDto, token: string): Promise<Note> => {
    try {
      const response = await axios.post(`${BASE_URL}/notes/notes`, data, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error('Error creating note:', error.message)
      throw error
    }
  },

  updateNote: async (id: string, data: UpdateNoteDto, token: string): Promise<Note> => {
    try {
      const response = await axios.patch(`${BASE_URL}/notes/notes/${id}`, data, {
        headers: getAuthHeader(token)
      })
      return response.data
    } catch (error: any) {
      console.error(`Error updating note ${id}:`, error.message)
      throw error
    }
  },

  deleteNote: async (id: string, token: string): Promise<void> => {
    try {
      await axios.delete(`${BASE_URL}/notes/notes/${id}`, {
        headers: getAuthHeader(token)
      })
    } catch (error: any) {
      console.error(`Error deleting note ${id}:`, error.message)
      throw error
    }
  }
}

export default notesService
