import axios from 'axios'
import { API_URL } from '@/config/constants'

// Get API URL from environment variables with fallback
const BASE_URL = import.meta.env.VITE_API_URL || API_URL || 'http://localhost:3000'
console.log('Flashcard service using API URL:', BASE_URL)

// Helper function to ensure URLs have HTTPS protocol and use API proxy for CDN URLs
const ensureHttpsUrl = (url: string): string => {
  if (!url) return url

  // If it's already a proxy URL, don't proxy it again
  if (url.includes('/health/proxy/image')) {
    console.log('URL is already proxied:', url)
    return url
  }

  // If it's a CDN URL, use the health proxy endpoint
  if (url.includes('cdn.jeridschool.tech')) {
    // Use the health proxy endpoint that works with the profile component
    return `http://localhost:3000/health/proxy/image?url=${encodeURIComponent(url)}`
  }

  // If URL doesn't start with http/https, add https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }

  // Convert http:// to https://
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://')
  }

  return url
}

// Types
export interface Flashcard {
  id: string
  frontText: string
  frontImage?: string
  backText: string
  backImage?: string
  order: number
  createdAt: string
  updatedAt: string
  deckId: string
}

export interface FlashcardDeck {
  id: string
  name: string
  visibility: 'public' | 'private'
  ownerId: string
  cardCount: number
  createdAt: string
  updatedAt: string
  cards: Flashcard[]
}

export interface CreateDeckDto {
  name: string
  visibility: 'public' | 'private'
}

export interface UpdateDeckDto {
  name?: string
  visibility?: 'public' | 'private'
}

export interface CreateCardDto {
  frontText: string
  frontImage?: string
  backText: string
  backImage?: string
  deckId: string
}

export interface UpdateCardDto {
  frontText?: string
  frontImage?: string
  backText?: string
  backImage?: string
  forceCreate?: boolean // Flag to indicate if we should create a new card from a temporary one
}

export interface ReorderCardsDto {
  cards: {
    id: string
    order: number
  }[]
}

// API Service
export const flashcardService = {
  // Decks
  getDecks: async (token: string): Promise<FlashcardDeck[]> => {
    try {
      console.log('Fetching flashcard decks with token:', token ? 'Token provided' : 'No token')

      if (!token) {
        console.error('No token provided to getDecks function')
        return []
      }

      const response = await axios.get(`${BASE_URL}/flashcards/decks`, {
        headers: { Authorization: `Bearer ${token}` },
      })

      console.log('Successfully fetched flashcard decks:', response.data.length)
      return response.data
    } catch (error: any) {
      console.error('Error fetching flashcard decks:', error.message)
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)

        // If token is invalid, clear it from localStorage to force re-login
        if (error.response.status === 401) {
          console.warn('Invalid token detected, user may need to re-login')
        }
      }
      return []
    }
  },

  // Get all cards for a specific deck
  getDeckCards: async (deckId: string, token: string): Promise<Flashcard[]> => {
    try {
      const response = await axios.get(
        `${BASE_URL}/flashcards/decks/${deckId}/cards`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      )
      console.log(`Retrieved ${response.data.length} cards for deck ${deckId}`)
      return response.data
    } catch (error: any) {
      console.error(
        'Error fetching deck cards:',
        error.response?.data || error.message
      )
      throw error
    }
  },

  getPublicDecks: async (token: string): Promise<FlashcardDeck[]> => {
    const response = await axios.get(`${BASE_URL}/flashcards/public-decks`, {
      headers: { Authorization: `Bearer ${token}` },
    })
    return response.data
  },

  getDeckById: async (
    deckId: string,
    token: string
  ): Promise<FlashcardDeck> => {
    const response = await axios.get(`${BASE_URL}/flashcards/decks/${deckId}`, {
      headers: { Authorization: `Bearer ${token}` },
    })
    return response.data
  },

  createDeck: async (
    deckData: CreateDeckDto,
    token: string
  ): Promise<FlashcardDeck> => {
    const response = await axios.post(
      `${BASE_URL}/flashcards/decks`,
      deckData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  updateDeck: async (
    deckId: string,
    deckData: UpdateDeckDto,
    token: string
  ): Promise<FlashcardDeck> => {
    const response = await axios.patch(
      `${BASE_URL}/flashcards/decks/${deckId}`,
      deckData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  deleteDeck: async (
    deckId: string,
    token: string
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `${BASE_URL}/flashcards/decks/${deckId}`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  reorderCards: async (
    deckId: string,
    reorderData: ReorderCardsDto,
    token: string
  ): Promise<Flashcard[]> => {
    const response = await axios.patch(
      `${BASE_URL}/flashcards/decks/${deckId}/reorder`,
      reorderData,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  // Cards
  createCard: async (
    cardData: CreateCardDto,
    token: string
  ): Promise<Flashcard> => {
    // Ensure image URLs are properly formatted
    const formattedCardData = {
      ...cardData,
      frontImage: cardData.frontImage
        ? ensureHttpsUrl(cardData.frontImage)
        : cardData.frontImage,
      backImage: cardData.backImage
        ? ensureHttpsUrl(cardData.backImage)
        : cardData.backImage,
    }

    console.log('Creating card with formatted data:', formattedCardData)

    try {
      const response = await axios.post(
        `${BASE_URL}/flashcards/cards`,
        formattedCardData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      )
      return response.data
    } catch (error: any) {
      console.error(
        'Error creating card:',
        error.response?.data || error.message
      )
      throw error
    }
  },

  updateCard: async (
    cardId: string,
    cardData: UpdateCardDto,
    token: string
  ): Promise<Flashcard> => {
    // Ensure image URLs are properly formatted
    const formattedCardData = {
      ...cardData,
      frontImage: cardData.frontImage
        ? ensureHttpsUrl(cardData.frontImage)
        : cardData.frontImage,
      backImage: cardData.backImage
        ? ensureHttpsUrl(cardData.backImage)
        : cardData.backImage,
    }

    console.log(
      `Updating card ${cardId} with formatted data:`,
      formattedCardData
    )

    try {
      // Make sure we're using the correct endpoint for card updates
      const response = await axios.patch(
        `${BASE_URL}/flashcards/cards/${cardId}`,
        formattedCardData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      )

      console.log('Card update successful, received response:', response.data)
      return response.data
    } catch (error: any) {
      console.error(
        'Error updating card:',
        error.response?.data || error.message
      )

      // Log more detailed error information
      if (error.response) {
        console.error('Error response status:', error.response.status)
        console.error('Error response data:', error.response.data)
      }

      throw error
    }
  },

  deleteCard: async (
    cardId: string,
    token: string
  ): Promise<{ message: string }> => {
    const response = await axios.delete(
      `${BASE_URL}/flashcards/cards/${cardId}`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  // Helper function to migrate from localStorage to backend
  migrateFromLocalStorage: async (token: string): Promise<boolean> => {
    try {
      const savedDecks = localStorage.getItem('flashcard_decks')
      if (!savedDecks) return false

      const decks = JSON.parse(savedDecks)

      // Create each deck and its cards on the backend
      for (const deck of decks) {
        // Create the deck
        const newDeck = await flashcardService.createDeck(
          {
            name: deck.name,
            visibility: deck.visibility,
          },
          token
        )

        // Create each card in the deck
        for (const card of deck.cards) {
          await flashcardService.createCard(
            {
              frontText: card.front.text,
              frontImage: card.front.image,
              backText: card.back.text,
              backImage: card.back.image,
              deckId: newDeck.id,
            },
            token
          )
        }
      }

      // Clear localStorage after successful migration
      localStorage.removeItem('flashcard_decks')
      return true
    } catch (error) {
      console.error('Error migrating flashcards from localStorage:', error)
      return false
    }
  },
}
