import axios from 'axios'
import { SchoolData } from '@/routes/school/$schoolName'
import {  getProxiedPath } from '@/lib/corsProxy'

const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:3000'
const IS_DEV = import.meta.env.DEV
// Get the current port from window.location if available, otherwise use default
const DEV_PORT =
  typeof window !== 'undefined' ? window.location.port || '5173' : '5173'
const PRODUCTION_DOMAIN = 'jeridschool.tech'

// Enable this for debugging only
const DEBUG = true

// Check for subdomain in URL query parameters (for Vercel middleware support)
const getSubdomainFromQuery = (): string | null => {
  if (typeof window === 'undefined') return null

  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('subdomain')
}

/**
 * Service for handling school-related API calls
 */
export const schoolService = {
  /**
   * Redirects from a path-based school URL to a subdomain-based URL
   * @param schoolName - The school name to redirect to
   * @returns boolean - Whether the redirection was successful
   */
  redirectToSubdomain(schoolName: string): boolean {
    if (!schoolName || schoolName.trim() === '') {
      console.error('Cannot redirect: School name is empty')
      return false
    }

    try {
      // Normalize the school name
      const normalizedSchoolName = schoolName.trim().toLowerCase()

      // Get the current protocol
      const protocol = window.location.protocol

      // Get the current hostname without any subdomains
      let domain
      if (IS_DEV) {
        domain = `localhost:${DEV_PORT}`
      } else {
        domain = PRODUCTION_DOMAIN
      }

      // Construct the new URL
      const newUrl = `${protocol}//${normalizedSchoolName}.${domain}`

      // Perform the redirect
      window.location.href = newUrl
      return true
    } catch (error) {
      console.error('Error during redirection:', error)
      return false
    }
  },

  /**
   * Fetch a school by its URL
   * @param url The school's URL identifier
   * @returns The school data
   */
  async getSchoolByUrl(url: string): Promise<SchoolData> {
    try {
      if (!url || url.trim() === '') {
        throw new Error('School URL is required and cannot be empty')
      }

      // Normalize the URL (trim whitespace, convert to lowercase)
      const normalizedUrl = url.trim().toLowerCase()

      // Try the public endpoint with standard headers first
      try {
        // Construct the endpoint URL
        const endpointPath = `/public/etablissement/url/${normalizedUrl}`

        // Use the proxy in development mode
        const requestUrl = IS_DEV
          ? getProxiedPath(endpointPath, 'api')
          : `${API_BASE_URL}${endpointPath}`

        console.log('Making request to:', requestUrl)

        const response = await axios.get(requestUrl, {
          // Add timeout for better performance
          timeout: 5000,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          }
        })

        const data = response.data

        // Ensure we have the required fields from the database
        if (!data.id || !data.name) {
          throw new Error('Invalid school data received from server')
        }

        // Return the data
        return this.formatSchoolData(data, normalizedUrl)
      } catch (publicError: any) {
        if (DEBUG)
          console.error('Error fetching from public endpoint:', publicError)

        // If we encounter a CORS error or any network error, try again without Cache-Control
        if (
          publicError.message === 'Network Error' ||
          publicError.code === 'ERR_NETWORK'
        ) {
          try {
            // Construct the endpoint URL for retry
            const endpointPath = `/public/etablissement/url/${normalizedUrl}`

            // Use the proxy in development mode
            const requestUrl = IS_DEV
              ? getProxiedPath(endpointPath, 'api')
              : `${API_BASE_URL}${endpointPath}`

            console.log('Retrying request to:', requestUrl)

            const response = await axios.get(requestUrl, {
              timeout: 8000, // Longer timeout for fallback
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
              }
            })

            const data = response.data

            // Ensure we have the required fields from the database
            if (!data.id || !data.name) {
              throw new Error('Invalid school data received from server')
            }

            // Return the data
            return this.formatSchoolData(data, normalizedUrl)
          } catch (retryError) {
            if (DEBUG) console.error('Retry also failed:', retryError)
          }
        }

        // Check if we have an auth token in localStorage
        const token = localStorage.getItem('access_token')

        if (token) {
          // If we have a token, try the protected endpoint with authentication

          try {
            // Construct the endpoint URL for protected endpoint
            const endpointPath = `/etablissement/url/${normalizedUrl}`

            // Use the proxy in development mode
            const requestUrl = IS_DEV
              ? getProxiedPath(endpointPath, 'api')
              : `${API_BASE_URL}${endpointPath}`

            console.log('Making authenticated request to:', requestUrl)

            const response = await axios.get(requestUrl, {
              headers: {
                Authorization: `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
              },
              timeout: 5000,
            })

            const data = response.data

            // Ensure we have the required fields from the database
            if (!data.id || !data.name) {
              throw new Error('Invalid school data received from server')
            }

            // Return the data
            return this.formatSchoolData(data, normalizedUrl)
          } catch (protectedError) {
            if (DEBUG)
              console.error(
                'Error fetching from protected endpoint:',
                protectedError
              )
            // Re-throw the original public endpoint error
            throw publicError
          }
        } else {
          // If we don't have a token, just throw the original error
          throw publicError
        }
      }
    } catch (error: any) {
      console.error('Error fetching school data:', error)

      // Always throw an error for invalid schools
      if (error.response && error.response.status === 404) {
        throw new Error(`School "${url}" not found or is not active`)
      } else if (error.response && error.response.status === 401) {
        throw new Error(`Authentication required to access school "${url}"`)
      }

      // For CORS or Network errors, provide a clearer message
      if (error.message === 'Network Error' || error.code === 'ERR_NETWORK') {
        throw new Error(
          `Unable to connect to school server. Please check your connection or try again later.`
        )
      }

      throw new Error(error.message || 'School not found or inactive')
    }
  },

  // Helper method to format school data
  formatSchoolData(data: any, normalizedUrl: string): SchoolData {
    return {
      id: data.id,
      name: data.name,
      address: data.address || 'School Address',
      logo:
        data.logo ||
        `https://placehold.co/200x100?text=${encodeURIComponent(data.name)}`,
      url: data.url || normalizedUrl,
      isActive: data.isActive !== undefined ? data.isActive : true,
      // Add default values for fields that might not be in the API response
      description: data.description || `Welcome to ${data.name}`,
      services: data.services || [
        'Primary Education',
        'Secondary Education',
        'After-School Programs',
      ],
      heroImage:
        data.heroImage ||
        `https://placehold.co/1200x400?text=${encodeURIComponent(data.name)}`,
      galleryImages: data.galleryImages || [
        `https://placehold.co/600x400?text=${encodeURIComponent(data.name)}+Image+1`,
        `https://placehold.co/600x400?text=${encodeURIComponent(data.name)}+Image+2`,
      ],
      contactEmail: data.contactEmail || `contact@${normalizedUrl}.example.com`,
      contactPhone: data.contactPhone || '****** 567 890',
      socialLinks: data.socialLinks || {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: '',
      },
      stats: data.stats || {
        students: 500,
        teachers: 30,
        courses: 20,
        awards: 10,
      },
      banner:
        data.banner ||
        `https://placehold.co/1200x600?text=${encodeURIComponent(data.name)}`,
      primaryColor: data.primaryColor || '#007bff',
      secondaryColor: data.secondaryColor || '#e6f2ff',
      superAdminId: data.superAdminId || null,
    }
  },

  /**
   * Get all schools
   * @returns Array of school data
   */
  async getAllSchools(): Promise<SchoolData[]> {
    try {
      // Construct the endpoint URL
      const endpointPath = '/etablissement'

      // Use the proxy in development mode
      const requestUrl = IS_DEV
        ? getProxiedPath(endpointPath, 'api')
        : `${API_BASE_URL}${endpointPath}`

      console.log('Fetching all schools from:', requestUrl)

      // Fetch all schools from the backend API
      const response = await axios.get(requestUrl, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      })

      // Map the response to SchoolData objects
      return response.data.map((school: any) => ({
        id: school.id,
        name: school.name,
        address: school.address,
        logo:
          school.logo ||
          `https://placehold.co/200x100?text=${encodeURIComponent(school.name)}`,
        url: school.url,
        isActive: school.isActive,
        // Add minimal default values for display in the schools list
        description: school.description || `About ${school.name}`,
        banner:
          school.banner ||
          `https://placehold.co/1200x600?text=${encodeURIComponent(school.name)}`,
        primaryColor: school.primaryColor || '#007bff',
        secondaryColor: school.secondaryColor || '#e6f2ff',
        services: school.services || ['Education Services'],
      }))
    } catch (error) {
      console.error('Error fetching all schools:', error)

      // In development, return an empty array instead of throwing
      if (IS_DEV) {
        console.warn('Returning empty array for schools list')
        return []
      }

      throw new Error('Failed to fetch schools')
    }
  },

  /**
   * Get the full URL for a school's subdomain
   * @param schoolName The school's name/identifier
   * @returns The full URL for the school
   */
  getSchoolUrl(schoolName: string): string {
    if (!schoolName || schoolName.trim() === '') {
      console.warn('getSchoolUrl called with empty schoolName')
      return '#'
    }

    // Normalize the URL (trim whitespace, convert to lowercase)
    const normalizedUrl = schoolName.trim().toLowerCase()

    if (IS_DEV) {
      // In development, use subdomain-based approach for testing
      return `http://${normalizedUrl}.localhost:${DEV_PORT}`
    }

    // In production, use the subdomain-based approach
    return `https://${normalizedUrl}.${PRODUCTION_DOMAIN}`
  },

  /**
   * Extract the school name from a URL
   * @param url The full URL
   * @returns The school name/identifier
   */
  extractSchoolNameFromUrl(url: string): string | null {
    if (!url) return null

    // For legacy path-based URLs like /school/dreamschool
    const pathMatch = url.match(/\/school\/([^\/]+)/)
    if (pathMatch) {
      return pathMatch[1]
    }

    // For development URLs like http://dreamschool.localhost:5173
    const devSubdomainMatch = url.match(/https?:\/\/([^\.]+)\.localhost/)
    if (devSubdomainMatch) {
      return devSubdomainMatch[1]
    }

    // For production URLs like https://dreamschool.jeridschool.tech
    const prodSubdomainMatch = url.match(
      new RegExp(
        `https?://([^\\.]+)\\.${PRODUCTION_DOMAIN.replace(/\./g, '\\.')}`
      )
    )
    if (prodSubdomainMatch) {
      return prodSubdomainMatch[1]
    }

    return null
  },

  /**
   * Check if the current URL is using a subdomain format
   * @returns boolean indicating if we're on a subdomain
   */
  isSubdomainUrl(): boolean {
    if (typeof window === 'undefined') return false

    // First check if we have a subdomain in the query parameters (from middleware)
    const querySubdomain = getSubdomainFromQuery()
    if (querySubdomain) {
      return true
    }

    const hostname = window.location.hostname

    // Special handling for 'www' subdomain
    if (hostname.startsWith('www.')) {
      // We don't redirect anymore, just indicate it's not a school subdomain
      // This allows the www subdomain to work with the main application
      return false
    }

    // Check for development subdomain (localhost with subdomain)
    if (IS_DEV) {
      // For development, consider any hostname that starts with something other than 'localhost'
      // and contains 'localhost' as a subdomain
      const isDevSubdomain =
        hostname !== 'localhost' && hostname.includes('localhost')
      return isDevSubdomain
    }

    // Check for production subdomain
    if (hostname.includes(`.${PRODUCTION_DOMAIN}`)) {
      return true
    }

    return false
  },

  /**
   * Get the school name from the current URL (if it's a subdomain)
   * @returns The school name or null if not on a school subdomain
   */
  getCurrentSchoolFromSubdomain(): string | null {
    // First check if we have a subdomain in the query parameters (from middleware)
    const querySubdomain = getSubdomainFromQuery()
    if (querySubdomain) {
      if (DEBUG) return querySubdomain
    }

    // If not in query parameters, check if we're on a subdomain URL
    if (!this.isSubdomainUrl()) {
      // Remove or make debug-only log

      return null
    }

    const hostname = window.location.hostname
    // Remove or make debug-only log

    // Special handling for 'www' subdomain - it's not a school
    if (hostname.startsWith('www.')) {
      return null
    }

    if (IS_DEV && hostname.includes('.localhost')) {
      const schoolName = hostname.split('.')[0]
      // Skip 'www' as a school name
      if (schoolName === 'www') {
        return null
      }
      return schoolName
    }

    if (hostname.includes(`.${PRODUCTION_DOMAIN}`)) {
      const schoolName = hostname.split('.')[0]
      // Skip 'www' as a school name
      if (schoolName === 'www') {
        return null
      }

      return schoolName
    }

    return null
  },
}
