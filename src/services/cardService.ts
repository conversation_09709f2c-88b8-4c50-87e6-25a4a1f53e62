// Define a simple fetch wrapper to replace apiClient
export const apiClient = {
  get: async (url: string, options?: any) => {
    const token = localStorage.getItem('access_token')
    const response = await fetch(url, {
      ...options,
      method: 'GET',
      headers: {
        ...options?.headers,
        Authorization: token ? `Bearer ${token}` : undefined
      }
    })
    if (!response.ok) throw new Error(`API error: ${response.status}`)
    return { data: await response.json() }
  },
  post: async (url: string, data: any, options?: any) => {
    const token = localStorage.getItem('access_token')
    const response = await fetch(url, {
      ...options,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
        Authorization: token ? `Bearer ${token}` : undefined
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) throw new Error(`API error: ${response.status}`)
    return { data: await response.json() }
  },
  patch: async (url: string, data: any, options?: any) => {
    const token = localStorage.getItem('access_token')
    const response = await fetch(url, {
      ...options,
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
        Authorization: token ? `Bearer ${token}` : undefined
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) throw new Error(`API error: ${response.status}`)
    return { data: await response.json() }
  },
  delete: async (url: string, options?: any) => {
    const token = localStorage.getItem('access_token')
    const response = await fetch(url, {
      ...options,
      method: 'DELETE',
      headers: {
        ...options?.headers,
        Authorization: token ? `Bearer ${token}` : undefined
      }
    })
    if (!response.ok) throw new Error(`API error: ${response.status}`)
    return { data: await response.json() }
  },
}
import {
  CardEntity,
  CreateCardDto,
  UpdateCardDto,
} from '@/interface/types/establishment'

// Re-export types from establishment.ts
export type CardTemplate = CardEntity
export type { CreateCardDto, UpdateCardDto }

const cardService = {
  // Get all cards (filtered by role)
  getAllCards: async (): Promise<CardTemplate[]> => {
    const response = await apiClient.get(
      'http://localhost:3000/card-templates'
    )
    return response.data
  },

  // Get card for a specific establishment
  getEstablishmentCards: async (): Promise<CardTemplate[]> => {
    const response = await apiClient.get(`http://localhost:3000/card-templates`)
    // If the response is a single card object, wrap it in an array
    const data = response.data
    return Array.isArray(data) ? data : [data]
  },

  // Create a new card for an establishment
  createCard: async (cardData: CreateCardDto): Promise<CardTemplate> => {
    const { etablissementId, ...data } = cardData
    if (!etablissementId) {
      throw new Error('Establishment ID is required')
    }
    const response = await apiClient.post(
      `http://localhost:3000/card-templates`,
      data
    )
    return response.data
  },

  // Update a card for an establishment
  updateCard: async (updateData: UpdateCardDto): Promise<CardTemplate> => {
    const response = await apiClient.patch(
      `http://localhost:3000/card-templates`,
      updateData
    )
    return response.data
  },

  // Delete a card for an establishment
  deleteCard: async (cardId: string): Promise<void> => {
    // Extract the establishment ID from the card ID (assuming format: 'establishment-id_card-id')
    const establishmentId = cardId.split('_')[0]
    await apiClient.delete(
      `http://localhost:3000/${establishmentId}/card`
    )
  },

  // Generate card for a user
  generateUserCard: async (userId: string): Promise<string> => {
    const response = await apiClient.get(
      `http://localhost:3000/users/${userId}/card`,
      {
        responseType: 'text',
      }
    )
    return response.data
  },

  // Download card as PDF for a user
  downloadUserCardPDF: (userId: string): string => {
    return `http://localhost:3000/users/${userId}/card/download`
  },
}

export default cardService
