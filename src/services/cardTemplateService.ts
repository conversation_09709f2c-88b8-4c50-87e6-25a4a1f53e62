// hooks/useCardTemplate.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from './cardService'
import { CardTemplate } from '../interface/types/card'

// GET all templates
export const useCardTemplates = () => {
  return useQuery<CardTemplate[]>({
    queryKey: ['cardTemplates'],
    queryFn: async () => {
      const response = await apiClient.get('http://localhost:3000/card-templates')
      return response.data
    },
  })
}

// GET single template by ID
export const useCardTemplate = (id: string) => {
  return useQuery<CardTemplate>({
    queryKey: ['cardTemplate', id],
    queryFn: async () => {
      const response = await apiClient.get(`http://localhost:3000/card-templates/${id}`)
      return response.data
    },
    enabled: !!id
  })
}

// CREATE template
export const useCreateCardTemplate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: async ({
      name,
      config,
      description,
      previewImageUrl
    }: {
      name: string
      config: {
        html: string
      }
      description: string
      previewImageUrl: string
    }) => {
      const etablissementId = localStorage.getItem('etablissementId')
      if (!etablissementId) throw new Error('No etablissementId found in localStorage')

      const response = await apiClient.post('http://localhost:3000/card-templates', {
        name,
        config,
        description,
        previewImageUrl,
        etablissementId,
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cardTemplates'] })
    }
  })
}

// UPDATE template
export const useUpdateCardTemplate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: async ({
      id,
      name,
      config // as string
    }: {
      id: string
      name: string
      config: string
    }) => {
      const etablissementId = localStorage.getItem('etablissementId')
      if (!etablissementId) throw new Error('No etablissementId found in localStorage')

      const response = await apiClient.patch(`http://localhost:3000/card-templates/${id}`, {
        name,
        config,
        etablissementId,
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cardTemplates'] })
    }
  })
}

// DELETE template
export const useDeleteCardTemplate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: async (id: string) => {
      await apiClient.delete(`http://localhost:3000/card-templates/${id}`)
      return id
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cardTemplates'] })
    }
  })
}

// GENERATE user card
export const useGenerateUserCard = (userId: string) => {
  return useQuery<string>({
    queryKey: ['userCard', userId],
    queryFn: async () => {
      const response = await apiClient.get(`http://localhost:3000/users/${userId}/card`)
      return response.data
    },
    enabled: !!userId
  })
}
