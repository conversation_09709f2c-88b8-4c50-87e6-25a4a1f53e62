import axios from 'axios'

// AI Service base URL (from ai.md)
const AI_BASE_URL = 'https://ai.jeridschool.tech'

// Create AI API client
const aiClient = axios.create({
  baseURL: AI_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000, // 2 minutes for AI processing
  withCredentials: false,
})

// Add interceptor to include auth token in requests
aiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Add response interceptor for better error handling
aiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.code === 'ERR_NETWORK') {
      error.message = 'Unable to connect to AI service. Please check if the service is running.'
    } else if (error.response) {
      error.message = error.response.data.message || 'An error occurred with the AI request.'
    } else if (error.request) {
      error.message = 'AI service did not respond. Please try again later.'
    }
    return Promise.reject(error)
  }
)

// Types for AI service
export interface QuizItem {
  question: string
  options: string[]
  answer: string
}

export interface FlashcardItem {
  front: string
  back: string
}

export interface ProcessAIRequest {
  youtube_url?: string
  custom_prompt?: string
  user_id: string
}

export interface ProcessAIResponse {
  quiz: QuizItem[]
  flashcards: FlashcardItem[]
  notes: string
  status: string
  message?: string
}

export interface ChatbotRequest {
  user_id: string
  message: string
  session_id?: string
}

export interface ChatbotResponse {
  user_id(user_id: any): readonly ["ai", "chat", "sessions", string] | undefined
  reply: string
  session_id: string
  status: string
  message?: string
}

export interface TranscribeRequest {
  youtube_url: string
  save_transcript: boolean
}

export interface TranscribeResponse {
  text: string
  success: boolean
  video_id: string
  json_file?: string
  pdf_file?: string
  notes?: string
}

export interface HealthCheckResponse {
  message: string
  version: string
  status: string
}

// AI Service functions
export const aiService = {
  // Health check
  healthCheck: async (): Promise<HealthCheckResponse> => {
    const response = await aiClient.get('/')
    return response.data
  },

  // Process YouTube URL with AI
  processYouTubeContent: async (data: ProcessAIRequest): Promise<ProcessAIResponse> => {
    const response = await aiClient.post('/api/process_ai', data)
    return response.data
  },

  // Process uploaded file with AI
  processFile: async (file: File, customPrompt?: string, userId?: string): Promise<ProcessAIResponse> => {
    console.log('AI Service: Starting file processing', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      userId,
      customPrompt: customPrompt ? 'provided' : 'none'
    })

    const formData = new FormData()
    formData.append('file', file)
    if (userId) formData.append('user_id', userId)
    if (customPrompt) formData.append('custom_prompt', customPrompt)

    try {
      const response = await aiClient.post('/api/process_ai/file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 180000, // 3 minutes timeout
      })

      console.log('AI Service: File processing successful', response.data)
      return response.data
    } catch (error: any) {
      console.error('AI Service: File processing failed', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      })
      throw error
    }
  },

  // Chatbot interaction
  sendChatMessage: async (data: ChatbotRequest): Promise<ChatbotResponse> => {
    const response = await aiClient.post('/api/chatbot', data)
    return response.data
  },

  // Transcribe YouTube video with notes
  transcribeWithNotes: async (data: TranscribeRequest): Promise<TranscribeResponse> => {
    const response = await aiClient.post('/api/transcribe/with_notes', data)
    return response.data
  },

  // Basic transcription
  transcribe: async (data: TranscribeRequest): Promise<TranscribeResponse> => {
    const response = await aiClient.post('/api/transcribe/', data)
    return response.data
  },

  // Get chat sessions for user
  getChatSessions: async (userId: string) => {
    const response = await aiClient.get(`/api/chatbot/sessions/${userId}`)
    return response.data
  },

  // Delete chat session
  deleteChatSession: async (sessionId: string) => {
    const response = await aiClient.delete(`/api/chatbot/sessions/${sessionId}`)
    return response.data
  },
}

export default aiService
