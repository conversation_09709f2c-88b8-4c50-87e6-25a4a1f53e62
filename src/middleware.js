// Middleware to handle subdomain routing
export default function middleware(req) {
  const url = new URL(req.url)
  const hostname = url.hostname

  // Skip for API and CDN requests
  if (
    url.pathname.startsWith('/api') ||
    url.pathname.startsWith('/cdn-proxy')
  ) {
    return
  }

  // Check if we're on a subdomain
  const isSubdomain =
    hostname.includes('.jeridschool.tech') && !hostname.startsWith('www.')

  if (isSubdomain) {
    // Extract the subdomain
    const subdomain = hostname.split('.')[0]

    // Add the subdomain as a query parameter
    url.searchParams.set('subdomain', subdomain)

    // Rewrite to the index page with the subdomain parameter
    return Response.redirect(url)
  }
}

export const config = {
  matcher: [
    // Skip all internal paths
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
