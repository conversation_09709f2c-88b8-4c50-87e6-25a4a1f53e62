import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/components/ui/use-toast'
import { api } from '@/lib/api/axios-instance'
import { adminSessionService, TeacherAbsenceRecordRequest } from '@/lib/api/services/admin-session-service'
import { adminLookupService } from '@/lib/api/services/admin-lookup-service'
import { sessionService } from '@/lib/api/services/session-service'
import { classService } from '@/lib/api/services/class-service'
import { studentService } from '@/lib/api/services/student-service'
import { attendanceTimetableService } from '@/lib/api/services/attendance-timetable-service'
import { parentService } from '@/lib/api/services/parent-service'
import { format } from 'date-fns'

// Define query keys for caching
export const ATTENDANCE_KEYS = {
  // Session related keys
  sessions: ['attendance', 'sessions'],
  sessionsByDate: (date: string) => ['attendance', 'sessions', 'date', date],
  sessionsByDateRange: (startDate: string, endDate: string) =>
    ['attendance', 'sessions', 'dateRange', startDate, endDate],
  sessionById: (id: string) => ['attendance', 'sessions', 'id', id],
  sessionStatistics: (startDate?: string, endDate?: string) =>
    ['attendance', 'sessions', 'statistics', startDate, endDate],

  // Teacher related keys
  teachers: ['attendance', 'teachers'],
  teacherById: (id: string) => ['attendance', 'teachers', 'id', id],

  // Class related keys
  classes: ['attendance', 'classes'],
  classById: (id: string) => ['attendance', 'classes', 'id', id],
  classStudents: (classId: string) => ['attendance', 'classes', 'students', classId],

  // Student related keys
  students: ['attendance', 'students'],
  studentById: (id: string) => ['attendance', 'students', 'id', id],
  studentAttendance: (studentId: string, startDate?: string, endDate?: string) =>
    ['attendance', 'students', 'attendance', studentId, startDate, endDate],

  // Parent related keys
  parentChildren: (parentId: string) => ['parent', parentId, 'children'],
  childAttendance: (childId: string, startDate?: string, endDate?: string) =>
    ['parent', 'children', childId, 'attendance', startDate, endDate],

  // Timetable related keys
  timetables: ['attendance', 'timetables'],
  timetableById: (id: string) => ['attendance', 'timetables', 'id', id],
}

/**
 * Custom hook for fetching and managing attendance-related data
 */
export function useAttendance() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Session related queries

  // Get sessions by date
  const useSessionsByDate = (date: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.sessionsByDate(date),
      queryFn: () => sessionService.getByDate(date),
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Get sessions by date range
  const useSessionsByDateRange = (startDate: string, endDate: string, options = {}) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.sessionsByDateRange(startDate, endDate),
      queryFn: () => sessionService.getByDateRange(startDate, endDate),
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  // Get sessions with filters (for admin)
  const useAdminSessions = (filters: any) => {
    // Create a stable query key by extracting values from filters
    // Use a stringified version of the filters to ensure the query key changes when any filter changes
    const filtersKey = JSON.stringify({
      dateFrom: filters.dateRange?.from ? filters.dateRange.from.toISOString() : null,
      dateTo: filters.dateRange?.to ? filters.dateRange.to.toISOString() : null,
      classId: filters.classId || 'all',
      teacherId: filters.teacherId || 'all',
      status: filters.status || 'all',
      searchTerm: filters.searchTerm || '',
      cancelCategory: filters.cancelCategory || 'all'
    });

    return useQuery({
      queryKey: [...ATTENDANCE_KEYS.sessions, 'filtered', filtersKey],
      queryFn: () => adminSessionService.fetchSessions({
        dateRange: filters.dateRange,
        classId: filters.classId || 'all',
        teacherId: filters.teacherId || 'all',
        status: filters.status || 'all',
        searchTerm: filters.searchTerm || '',
        cancelCategory: filters.cancelCategory || 'all'
      }),
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Get session statistics
  const useSessionStatistics = (dateRange?: { from: Date; to: Date }) => {
    const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined
    const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined

    return useQuery({
      queryKey: ATTENDANCE_KEYS.sessionStatistics(startDate, endDate),
      queryFn: () => dateRange ? adminSessionService.fetchSessionStatusMetrics(dateRange) : null,
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Teacher related queries

  // Get all teachers
  const useTeachers = () => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.teachers,
      queryFn: async () => {
        const teachers = await adminLookupService.fetchTeachers();
        // Transform the data to ensure consistent structure
        return teachers.map((teacher: any) => ({
          id: teacher.id,
          firstname: teacher.firstname || '',
          lastname: teacher.lastname || '',
          email: teacher.email || '',
          // Add fullName if it doesn't exist
          fullName: teacher.fullName || teacher.name || `${teacher.firstname || ''} ${teacher.lastname || ''}`.trim()
        }));
      },
      staleTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Record teacher absence mutation
  const useRecordTeacherAbsence = () => {
    return useMutation({
      mutationFn: (data: TeacherAbsenceRecordRequest) => {
        // Log the request for debugging
        console.log('useRecordTeacherAbsence - Request data:', data);

        // Validate the data
        if (!data.teacherId) {
          throw new Error('Teacher ID is required');
        }

        if (!data.date) {
          throw new Error('Date is required');
        }

        if (!data.reason) {
          throw new Error('Reason is required');
        }

        return adminSessionService.recordTeacherAbsence(data);
      },
      onSuccess: (data) => {
        // Log the success response
        console.log('useRecordTeacherAbsence - Success response:', data);

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ATTENDANCE_KEYS.sessions });

        // Show success message based on the response
        let successMessage = "Teacher absence recorded successfully.";

        // If the response includes a message about canceled sessions, use that
        if (data && data.message && data.canceled !== undefined) {
          successMessage = `${data.message}`;
        }

        toast({
          title: "Success",
          description: successMessage,
        });
      },
      onError: (error: any) => {
        // Log detailed error information
        console.error('useRecordTeacherAbsence - Error:', error);

        if (error.response) {
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);
          console.error('Error response headers:', error.response.headers);
        } else if (error.request) {
          console.error('Error request:', error.request);
        }

        // Determine the most appropriate error message
        let errorMessage = "Failed to record teacher absence.";

        if (error.message) {
          errorMessage = error.message;
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    });
  }

  // Class related queries

  // Get all classes
  const useClasses = () => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.classes,
      queryFn: () => adminLookupService.fetchClasses(),
      staleTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Get students by class ID
  const useStudentsByClass = (classId: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.classStudents(classId),
      queryFn: () => classService.getStudents(classId),
      enabled: !!classId,
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Student related queries

  // Get student attendance records
  const useStudentAttendance = (studentId: string, startDate?: string, endDate?: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.studentAttendance(studentId, startDate, endDate),
      queryFn: async () => {
        const url = `/attendance/student/${studentId}${startDate ? `?startDate=${startDate}` : ''}${endDate ? `&endDate=${endDate}` : ''}`
        const response = await api.get(url)
        return response.data
      },
      enabled: !!studentId,
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Get student by ID
  const useStudent = (studentId: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.studentById(studentId),
      queryFn: () => studentService.getById(studentId),
      enabled: !!studentId,
      staleTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Parent related queries

  // Get parent's children
  const useParentChildren = (parentId: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.parentChildren(parentId),
      queryFn: async () => {
        const parent = await parentService.getById(parentId)
        return parent.students || []
      },
      enabled: !!parentId,
      staleTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Get child's attendance records
  const useChildAttendance = (childId: string, startDate?: string, endDate?: string) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.childAttendance(childId, startDate, endDate),
      queryFn: async () => {
        const url = `/attendance/student/${childId}${startDate ? `?startDate=${startDate}` : ''}${endDate ? `&endDate=${endDate}` : ''}`
        const response = await api.get(url)
        return response.data
      },
      enabled: !!childId,
      staleTime: 1000 * 60 * 5, // 5 minutes
    })
  }

  // Timetable related queries

  // Get all timetables
  const useTimetables = (options = {}) => {
    return useQuery({
      queryKey: ATTENDANCE_KEYS.timetables,
      queryFn: () => attendanceTimetableService.fetchTimetables(),
      staleTime: 1000 * 60 * 10, // 10 minutes
      ...options
    })
  }

  // Session generation mutation
  const useGenerateSessions = () => {
    return useMutation<any[], Error, any>({
      mutationFn: async (data: any) => {
        // Import the service directly to avoid circular dependencies
        const { sessionGeneratorService } = await import('@/lib/api/services/session-generator-service');

        // Call the appropriate method based on teacherId
        if (data.teacherId) {
          return sessionGeneratorService.generateForTeacher({
            teacherId: data.teacherId,
            startDate: data.startDate,
            endDate: data.endDate,
            timetableId: data.timetableId
          });
        } else {
          return sessionGeneratorService.generateForAllTeachers({
            startDate: data.startDate,
            endDate: data.endDate,
            timetableId: data.timetableId
          });
        }
      },
      onSuccess: (data) => {
        // Invalidate sessions queries
        queryClient.invalidateQueries({ queryKey: ATTENDANCE_KEYS.sessions })

        toast({
          title: "Success",
          description: `Generated ${data.length || 0} sessions successfully.`,
        })

        return data
      },
      onError: (error: Error) => {
        console.error('Error generating sessions:', error);
        toast({
          title: "Error",
          description: error.message || "Failed to generate sessions.",
          variant: "destructive",
        })
      }
    })
  }

  // Create attendance records mutation
  const useCreateAttendance = () => {
    return useMutation({
      mutationFn: async (data: any) => {
        const response = await api.post('/sessions/attendance', data);
        return response.data;
      },
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ATTENDANCE_KEYS.sessions })

        toast({
          title: "Success",
          description: "Attendance records saved successfully.",
        })
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to save attendance records.",
          variant: "destructive",
        })
      }
    })
  }

  // Get sessions by teacher ID
  const useSessionsByTeacher = (teacherId: string, options = {}) => {
    return useQuery({
      queryKey: [...ATTENDANCE_KEYS.sessions, 'teacher', teacherId],
      queryFn: async () => {
        try {
          const response = await api.get(`/sessions/teacher/${teacherId}`)
          return response.data
        } catch (error) {
          console.error('Error fetching teacher sessions:', error)
          throw error
        }
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  // Get session by ID
  const useSessionById = (sessionId: string, options = {}) => {
    return useQuery({
      queryKey: [...ATTENDANCE_KEYS.sessions, sessionId],
      queryFn: async () => {
        try {
          const response = await api.get(`/sessions/${sessionId}`)
          return response.data
        } catch (error) {
          console.error(`Error fetching session ${sessionId}:`, error)
          throw error
        }
      },
      enabled: !!sessionId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  // Get attendance records for a session
  const useSessionAttendance = (sessionId: string, options = {}) => {
    return useQuery({
      queryKey: [...ATTENDANCE_KEYS.sessions, sessionId, 'attendance'],
      queryFn: async () => {
        try {
          const response = await api.get(`/attendance/by-session/${sessionId}`)
          return response.data
        } catch (error) {
          console.error(`Error fetching attendance for session ${sessionId}:`, error)
          throw error
        }
      },
      enabled: !!sessionId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  // Get teacher's attendance history
  const useTeacherAttendanceHistory = (teacherId: string, startDate?: string, endDate?: string, options = {}) => {
    return useQuery({
      queryKey: [...ATTENDANCE_KEYS.sessions, 'teacher', teacherId, 'history', startDate, endDate],
      queryFn: async () => {
        try {
          let url = `/attendance/teacher/${teacherId}`
          if (startDate && endDate) {
            url += `?startDate=${startDate}&endDate=${endDate}`
          }
          const response = await api.get(url)
          return response.data
        } catch (error) {
          console.error('Error fetching teacher attendance history:', error)
          throw error
        }
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  // Add student note mutation
  const useAddStudentNote = () => {
    return useMutation({
      mutationFn: async (data: { studentId: string, sessionId: string, content: string }) => {
        const response = await api.post('/student-notes', data)
        return response.data
      },
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['student-notes'] })
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to add student note.",
          variant: "destructive",
        })
      }
    })
  }

  // Update student note mutation
  const useUpdateStudentNote = () => {
    return useMutation({
      mutationFn: async (data: { id: string, content: string }) => {
        const response = await api.patch(`/student-notes/${data.id}`, { content: data.content })
        return response.data
      },
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['student-notes'] })
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to update student note.",
          variant: "destructive",
        })
      }
    })
  }

  // Delete student note mutation
  const useDeleteStudentNote = () => {
    return useMutation({
      mutationFn: async (noteId: string) => {
        await api.delete(`/student-notes/${noteId}`)
      },
      onSuccess: () => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['student-notes'] })
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to delete student note.",
          variant: "destructive",
        })
      }
    })
  }

  // Get student notes
  const useStudentNotes = (studentId: string, sessionId?: string, options = {}) => {
    return useQuery({
      queryKey: ['student-notes', studentId, sessionId],
      queryFn: async () => {
        let url = `/student-notes/${studentId}`
        if (sessionId) {
          url += `?sessionId=${sessionId}`
        }
        const response = await api.get(url)
        return response.data
      },
      enabled: !!studentId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      ...options
    })
  }

  return {
    // Session queries
    useSessionsByDate,
    useSessionsByDateRange,
    useAdminSessions,
    useSessionStatistics,
    useSessionById,
    useSessionAttendance,
    useSessionsByTeacher,

    // Teacher queries
    useTeachers,
    useRecordTeacherAbsence,
    useTeacherAttendanceHistory,

    // Class queries
    useClasses,
    useStudentsByClass,

    // Student queries
    useStudentAttendance,
    useStudent,
    useStudentNotes,

    // Parent queries
    useParentChildren,
    useChildAttendance,

    // Timetable queries
    useTimetables,
    useGenerateSessions,

    // Attendance mutations
    useCreateAttendance,
    useAddStudentNote,
    useUpdateStudentNote,
    useDeleteStudentNote,
  }
}
