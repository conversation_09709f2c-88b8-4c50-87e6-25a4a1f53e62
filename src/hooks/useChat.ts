import { useState, useEffect, useCallback, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import axios from 'axios'
import { toast } from 'sonner'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

type UserRole = 'Teacher' | 'Student' | 'Parent'

interface User {
  id: string
  firstname: string
  lastname: string
  role: UserRole
  avatarUrl?: string
}

interface Message {
  id: string
  content: string
  senderId: string
  conversationId: string
  createdAt: string
  isRead: boolean
  sender?: User
  temporary?: boolean
  error?: boolean
}

interface Conversation {
  id: string
  name?: string
  participants: User[]
  messages: Message[]
  createdAt: string
  updatedAt: string
  createdBy: string
}

// Define API URL with fallback options
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

// Add a utility function to check server availability
const checkServerAvailability = async () => {
  try {
    // Use a simple endpoint that doesn't require authentication
    const response = await axios.get(`${API_URL}/health`, { timeout: 5000 })
    return response.status === 200
  } catch (error) {
    console.error('Server availability check failed:', error)
    return false
  }
}

// API functions for React Query
const api = {
  getConversations: async (token: string) => {
    const response = await axios.get(`${API_URL}/chat/conversations`, {
      headers: { Authorization: `Bearer ${token}` },
    })
    return response.data
  },

  getConversation: async ({
    conversationId,
    token,
  }: {
    conversationId: string
    token: string
  }) => {
    const response = await axios.get(
      `${API_URL}/chat/conversations/${conversationId}`,
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    )
    return response.data
  },

  sendMessage: async ({
    content,
    conversationId,
    token,
  }: {
    content: string
    conversationId: string
    token: string
  }) => {
    const response = await axios.post(
      `${API_URL}/chat/messages`,
      { content, conversationId }, // Only send fields expected by SendMessageDto
      { headers: { Authorization: `Bearer ${token}` } }
    )
    return response.data
  },

  markMessagesAsRead: async ({
    conversationId,
    token,
  }: {
    conversationId: string
    token: string
  }) => {
    await axios.patch(
      `${API_URL}/chat/conversations/${conversationId}/read`,
      {},
      { headers: { Authorization: `Bearer ${token}` } }
    )
  },

  createConversation: async ({
    participantIds,
    isGroupChat,
    name,
    token,
  }: {
    participantIds: string[]
    isGroupChat: boolean
    name?: string
    token: string
  }) => {
    const response = await axios.post(
      `${API_URL}/chat/create-conversation`,
      { participantIds, isGroupChat, name },
      { headers: { Authorization: `Bearer ${token}` } }
    )
    return response.data
  },

  createTeacherStudentChat: async ({
    studentId,
    initialMessage,
    token,
  }: {
    studentId: string
    initialMessage: string
    token: string
  }) => {
    const response = await axios.post(
      `${API_URL}/chat/teacher-student`,
      { studentId, initialMessage },
      { headers: { Authorization: `Bearer ${token}` } }
    )
    return response.data
  },

  searchUsers: async ({
    term,
    userRole,
    token,
  }: {
    term: string
    userRole: UserRole
    token: string
  }) => {
    let url = ''
    if (userRole === 'Teacher') {
      url = `${API_URL}/student/search-by-name?term=${encodeURIComponent(term)}`
    } else if (userRole === 'Parent' || userRole === 'Student') {
      url = `${API_URL}/teacher/search-by-name?term=${encodeURIComponent(term)}`
    }

    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: 10000,
    })
    return response.data
  },
}

export const useChat = () => {
  // Query client for React Query
  const queryClient = useQueryClient()

  // User state
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)

  // Socket state
  const [socket, setSocket] = useState<Socket | null>(null)

  // UI state
  const [selectedConversationId, setSelectedConversationId] = useState<
    string | null
  >(null)
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Search functionality
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const [isSearchLoading, setIsSearchLoading] = useState<boolean>(false)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Conversation filtering
  const [conversationFilter, setConversationFilter] = useState<string>('')

  // Add messagesEndRef for scrolling to bottom of messages
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Add a state to track server availability
  const [isServerAvailable, setIsServerAvailable] = useState<boolean | null>(
    null
  )

  // Check server availability on mount
  useEffect(() => {
    const checkServer = async () => {
      setIsLoading(true)
      const isAvailable = await checkServerAvailability()
      setIsServerAvailable(isAvailable)
      if (!isAvailable) {
        setIsLoading(false)
        toast.error(
          'Unable to connect to the server. Please check your connection and try again.'
        )
      }
    }

    checkServer()
  }, [])

  // Initialize user and token
  useEffect(() => {
    const userId = localStorage.getItem('id')
    const userRole = localStorage.getItem('role')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')
    const storedToken = localStorage.getItem('access_token')

    if (userId && userRole && firstname && lastname && storedToken) {
      const user: User = {
        id: userId,
        firstname,
        lastname,
        role: userRole as UserRole,
      }
      setCurrentUser(user)
      setToken(storedToken)
      console.log('User data loaded from localStorage')
    } else {
      // If user data is missing, clear loading state to prevent infinite loading
      setIsLoading(false)
      console.warn('User data not found in localStorage')
      toast.error('User data not found. Please log in again.')
    }
  }, [])

  // Initialize socket connection with better error handling
  useEffect(() => {
    if (!currentUser || !token || !isServerAvailable) {
      // If no user or token or server is not available, we're not in a loading state
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    console.log('Initializing socket connection...')

    // Create socket connection with auth
    const newSocket = io(API_URL, {
      auth: {
        token,
        userData: { userId: currentUser.id, role: currentUser.role },
      },
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      transports: ['websocket'],
      forceNew: false,
      timeout: 10000, // 10 second timeout for connection
    })

    // Add connection timeout
    const connectionTimeout = setTimeout(() => {
      if (newSocket.connected) return

      console.error('Socket connection timeout')
      setIsLoading(false)
      toast.error(
        'Connection timeout. Please check your internet connection and try again.'
      )

      // Clean up socket
      newSocket.removeAllListeners()
      newSocket.disconnect()
    }, 15000) // 15 seconds timeout

    // Socket event handlers
    newSocket.on('connect', () => {
      console.log('Socket connected successfully')
      clearTimeout(connectionTimeout)

      // Request conversations via socket for real-time updates
      newSocket.emit('getConversations')
      setIsLoading(false)

      // For students, request any pending conversations
      if (currentUser.role === 'Student') {
        newSocket.emit('getPendingConversations')
      }

      // For teachers, request any pending student conversations
      if (currentUser.role === 'Teacher') {
        newSocket.emit('getPendingStudentConversations')
      }
    })

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      clearTimeout(connectionTimeout)
      setIsLoading(false)
      toast.error(
        `Connection error: ${error.message || 'Failed to connect to server'}`
      )
    })

    newSocket.on('reconnect', () => {
      console.log('Socket reconnected')
      // Invalidate queries to reload data
      queryClient.invalidateQueries({ queryKey: ['conversations'] })

      // For students, request any pending conversations after reconnect
      if (currentUser.role === 'Student') {
        newSocket.emit('getPendingConversations')
      }

      // For teachers, request any pending student conversations after reconnect
      if (currentUser.role === 'Teacher') {
        newSocket.emit('getPendingStudentConversations')
      }
    })

    newSocket.on('error', (error) => {
      console.error('Socket error:', error)
      toast.error(error.message || 'Connection error')
      // Don't set loading to false here as this might be a transient error
    })

    newSocket.on('disconnect', () => {
      console.log('Socket disconnected')
      // Don't set loading to false here as we might reconnect
    })

    // Add listener for new conversations
    newSocket.on('newConversation', (conversation) => {
      console.log('New conversation received:', conversation)

      // Update the conversations list
      queryClient.setQueryData(['conversations'], (oldData: any) => {
        if (!oldData) return [conversation]

        // Check if conversation already exists
        const exists = oldData.some(
          (c: Conversation) => c.id === conversation.id
        )
        if (exists) return oldData

        // Add the new conversation to the list and sort
        return [conversation, ...oldData].sort(
          (a: Conversation, b: Conversation) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )
      })

      // Automatically select the new conversation for all users if no conversation is selected
      if (!selectedConversationId) {
        setSelectedConversationId(conversation.id)
      }
    })

    setSocket(newSocket)

    return () => {
      console.log('Cleaning up socket connection')
      clearTimeout(connectionTimeout)
      newSocket.removeAllListeners()
      newSocket.disconnect()
    }
  }, [
    currentUser,
    token,
    queryClient,
    isServerAvailable,
    selectedConversationId,
  ])

  // React Query for conversations
  const { data: conversations = [] } = useQuery({
    queryKey: ['conversations'],
    queryFn: () => (token ? api.getConversations(token) : []),
    enabled: !!token,
    select: (data) => {
      // Sort conversations by updatedAt
      const sortedConversations = [...data].sort(
        (a: Conversation, b: Conversation) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )

      // Sort messages within each conversation by createdAt
      return sortedConversations.map((conv: Conversation) => ({
        ...conv,
        messages: [...conv.messages].sort(
          (a: Message, b: Message) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        ),
      }))
    },
    // More frequent updates for all users to ensure they see new messages
    refetchInterval: 10000,
    // Shorter stale time for all users to ensure fresher data
    staleTime: 5000,
    // Always refetch on window focus for all users
    refetchOnWindowFocus: true,
  })

  // React Query for selected conversation - optimize for faster loading
  const { data: selectedConversation } = useQuery({
    queryKey: ['conversation', selectedConversationId],
    queryFn: () =>
      token && selectedConversationId
        ? api.getConversation({ conversationId: selectedConversationId, token })
        : null,
    enabled: !!token && !!selectedConversationId,
    select: (data) => {
      if (!data) return null

      // Sort messages by createdAt
      return {
        ...data,
        messages: [...data.messages].sort(
          (a: Message, b: Message) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        ),
      }
    },
    refetchInterval: 5000, // More frequent updates for faster message delivery
    staleTime: 2000, // Very short stale time to ensure fresh data
    gcTime: Infinity, // Keep data in cache longer
    refetchOnWindowFocus: true, // Refetch on window focus for all users
  })

  // React Query for search results
  const { data: searchResults = [], refetch: refetchSearch } = useQuery({
    queryKey: ['searchUsers', searchTerm],
    queryFn: () => {
      if (!token || !currentUser || !searchTerm.trim()) return []
      return api.searchUsers({
        term: searchTerm.trim(),
        userRole: currentUser.role,
        token,
      })
    },
    enabled: false, // Don't run automatically, we'll trigger it manually
  })

  // Mutation for marking messages as read

  // Mutation for creating a new conversation
  const createConversationMutation = useMutation({
    mutationFn: api.createConversation,
    onSuccess: (data) => {
      // Set the new conversation as selected
      setSelectedConversationId(data.id)

      // Invalidate conversations query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['conversations'] })

      // Exit search mode
      setIsSearching(false)
      setSearchTerm('')
    },
  })

  // Mutation for creating a teacher-student chat
  const createTeacherStudentChatMutation = useMutation({
    mutationFn: api.createTeacherStudentChat,
    onSuccess: (data) => {
      // Set the new conversation as selected
      setSelectedConversationId(data.id)

      // Invalidate conversations query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['conversations'] })

      // Exit search mode
      setIsSearching(false)
      setSearchTerm('')
    },
  })

  // Add a function to scroll to the bottom of messages
  const scrollToBottom = useCallback(() => {
    // Use a smaller timeout to make scrolling feel more immediate
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'auto', // Changed from 'smooth' to 'auto' for faster scrolling
          block: 'end',
          inline: 'nearest',
        })
      }
    }, 10) // Reduced from 50ms to 10ms for faster response
  }, [messagesEndRef])

  // Scroll to bottom whenever selected conversation messages change
  useEffect(() => {
    if (selectedConversation) {
      scrollToBottom()
    }
  }, [selectedConversation?.messages, scrollToBottom])

  // Set up socket event listeners for real-time updates
  useEffect(() => {
    if (!socket) return

    // Handle new message event - optimized for speed and improved for all user roles
    socket.on('newMessage', (data: any) => {
      // Handle different message formats
      const message = data.message || data
      const conversationId = message.conversationId

      if (!message || !conversationId) {
        console.warn('Received invalid message format:', data)
        return
      }

      console.log('Received new message:', message)

      // STEP 1: Update the conversation in the conversations list
      queryClient.setQueryData(['conversations'], (oldData: any) => {
        if (!oldData) return oldData

        // Find the conversation
        const conversationIndex = oldData.findIndex(
          (c: Conversation) => c.id === conversationId
        )

        // If conversation not found, we need to refresh the conversations list
        if (conversationIndex === -1) {
          // Trigger a refresh of conversations
          setTimeout(() => {
            queryClient.invalidateQueries({ queryKey: ['conversations'] })
          }, 100)
          return oldData
        }

        // Check if message already exists
        const messageExists = oldData[conversationIndex].messages?.some(
          (msg: Message) => msg.id === message.id
        )

        if (messageExists) return oldData

        // Create a new array with the updated conversation
        const newData = [...oldData]

        // Determine if this is a message from another user and if the conversation is not selected
        const isFromOtherUser = message.senderId !== currentUser?.id
        const isConversationSelected = selectedConversationId === conversationId

        // If the message is from another user and the conversation is not selected, increment unread count
        const unreadCount =
          isFromOtherUser && !isConversationSelected
            ? (newData[conversationIndex].unreadCount || 0) + 1
            : newData[conversationIndex].unreadCount || 0

        // Add the message to the conversation's messages array
        if (!newData[conversationIndex].messages) {
          newData[conversationIndex].messages = []
        }

        newData[conversationIndex].messages.push({
          ...message,
          isRead:
            isConversationSelected || message.senderId === currentUser?.id,
        })

        // Mark the message as read if the conversation is currently selected
        const updatedMessage = {
          ...message,
          isRead:
            isConversationSelected || message.senderId === currentUser?.id,
        }

        newData[conversationIndex] = {
          ...newData[conversationIndex],
          lastMessage: updatedMessage,
          updatedAt: message.createdAt,
          unreadCount: unreadCount,
        }

        // Sort conversations by updatedAt
        return newData.sort(
          (a: Conversation, b: Conversation) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )
      })

      // STEP 2: If this is for the selected conversation, update it
      if (selectedConversationId === conversationId) {
        queryClient.setQueryData(
          ['conversation', selectedConversationId],
          (oldData: any) => {
            if (!oldData) return oldData

            // Check if message already exists or is a temporary message with the same content
            const existingMsgIndex = oldData.messages.findIndex(
              (msg: Message) =>
                msg.id === message.id ||
                (msg.temporary &&
                  msg.content === message.content &&
                  msg.senderId === message.senderId)
            )

            let updatedMessages

            if (existingMsgIndex >= 0) {
              // Replace the temporary message with the real one
              updatedMessages = [...oldData.messages]
              updatedMessages[existingMsgIndex] = {
                ...message,
                isRead: true, // Mark as read since we're viewing this conversation
              }
            } else {
              // Add as a new message and mark as read since we're viewing this conversation
              updatedMessages = [
                ...oldData.messages,
                {
                  ...message,
                  isRead: true,
                },
              ]
            }

            // Sort messages by timestamp
            updatedMessages.sort(
              (a: Message, b: Message) =>
                new Date(a.createdAt).getTime() -
                new Date(b.createdAt).getTime()
            )

            return {
              ...oldData,
              messages: updatedMessages,
              updatedAt: message.createdAt,
              unreadCount: 0, // Reset unread count since we're viewing this conversation
            }
          }
        )

        // Automatically mark messages as read on the server when we receive them in the selected conversation
        if (message.senderId !== currentUser?.id && token) {
          try {
            axios.patch(
              `${API_URL}/chat/conversations/${conversationId}/read`,
              {},
              {
                headers: { Authorization: `Bearer ${token}` },
              }
            )
          } catch (error) {
            console.error('Error marking messages as read:', error)
          }
        }

        // Scroll to bottom immediately
        scrollToBottom()
      } else {
        // STEP 3: If this is a new message for a non-selected conversation,
        // we need to make sure the conversation data is loaded in the cache
        const cachedData = queryClient.getQueryData([
          'conversation',
          conversationId,
        ])

        // If we don't have the data or it's stale, fetch it
        if (!cachedData) {
          // Only fetch if we have a token
          if (token) {
            // Fetch the conversation data in the background
            api
              .getConversation({ conversationId, token })
              .then((data) => {
                // Update the cache with the fetched data
                queryClient.setQueryData(['conversation', conversationId], data)

                // If this is a new message and no conversation is selected,
                // automatically select this conversation
                if (
                  !selectedConversationId &&
                  message.senderId !== currentUser?.id
                ) {
                  setSelectedConversationId(conversationId)
                }
              })
              .catch((error) => {
                console.error('Error fetching conversation data:', error)
              })
          }
        } else {
          // If we have the data and this is a new message from another user,
          // consider selecting this conversation
          if (message.senderId !== currentUser?.id) {
            // If no conversation is selected, select this one
            if (!selectedConversationId) {
              setSelectedConversationId(conversationId)
            }
            // If the user hasn't interacted with the current conversation recently,
            // consider switching to this new conversation with a notification
            else {
              // Show a notification about the new message
              toast.info(
                `New message from ${message.sender?.firstname || 'someone'}`,
                {
                  action: {
                    label: 'View',
                    onClick: () => setSelectedConversationId(conversationId),
                  },
                }
              )
            }
          }
        }
      }
    })

    // Handle message received event - same handler for consistency
    socket.on('messageReceived', (data: any) => {
      // Reuse the same handler for consistency
      socket.emit('newMessage', data)
    })

    // Handle errors with better error messages
    socket.on('error', (error: any) => {
      console.error('Socket error:', error)

      const errorMessage =
        typeof error === 'string' ? error : error.message || 'Connection error'

      if (errorMessage.includes('not a participant')) {
        toast.error('You are not a participant in this conversation')
      } else {
        toast.error(errorMessage)
      }
    })

    // Clean up event listeners
    return () => {
      socket.off('newMessage')
      socket.off('messageReceived')
      socket.off('error')
    }
  }, [
    socket,
    selectedConversationId,
    queryClient,
    scrollToBottom,
    currentUser,
    token,
  ])

  // Handle search input effect
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    if (searchTerm && isSearching) {
      setIsSearchLoading(true)
      searchTimeoutRef.current = setTimeout(() => {
        refetchSearch().finally(() => setIsSearchLoading(false))
      }, 300)
    } else if (!searchTerm) {
      setIsSearchLoading(false)
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchTerm, isSearching, refetchSearch])

  // Toggle search
  const toggleSearch = () => {
    setIsSearching(!isSearching)
    if (!isSearching && searchTerm) {
      refetchSearch()
    }
  }

  // Handle sending a message with ultra-fast optimistic updates
  const handleSendMessage = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault()

      if (
        !message.trim() ||
        !selectedConversationId ||
        !token ||
        !currentUser
      ) {
        return
      }

      try {
        // Store message content and clear input field immediately
        const messageContent = message.trim()
        setMessage('')

        // Create a temporary message with a unique ID
        const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
        const tempMessage: Message = {
          id: tempId,
          content: messageContent,
          senderId: currentUser.id,
          conversationId: selectedConversationId,
          createdAt: new Date().toISOString(),
          isRead: false,
          temporary: true,
          sender: currentUser, // Include sender info for better display
        }

        // STEP 1: Optimistically update the selected conversation immediately
        queryClient.setQueryData(
          ['conversation', selectedConversationId],
          (oldData: any) => {
            if (!oldData) return null

            // Add the temporary message to the conversation
            const updatedMessages = [...oldData.messages, tempMessage].sort(
              (a: Message, b: Message) =>
                new Date(a.createdAt).getTime() -
                new Date(b.createdAt).getTime()
            )

            return {
              ...oldData,
              messages: updatedMessages,
              updatedAt: tempMessage.createdAt,
            }
          }
        )

        // STEP 2: Optimistically update the conversations list
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData) return []

          // Find and update the conversation in the list
          const updatedConversations = oldData.map((conv: Conversation) => {
            if (conv.id === selectedConversationId) {
              return {
                ...conv,
                lastMessage: tempMessage,
                updatedAt: tempMessage.createdAt,
              }
            }
            return conv
          })

          // Sort conversations by updatedAt
          return updatedConversations.sort(
            (a: Conversation, b: Conversation) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          )
        })

        // STEP 3: Scroll to bottom immediately after optimistic update
        scrollToBottom()

        // STEP 4: Send the message to the server in the background
        // Only send the fields expected by SendMessageDto
        const response = await axios.post(
          `${API_URL}/chat/messages`,
          {
            content: messageContent,
            conversationId: selectedConversationId,
          },
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        )

        // STEP 5: Update with the real message from the server
        const serverMessage = response.data

        // Update the selected conversation with the real message
        queryClient.setQueryData(
          ['conversation', selectedConversationId],
          (oldData: any) => {
            if (!oldData) return null

            // Replace the temporary message with the real one
            // Find and remove any temporary messages with matching content
            const updatedMessages = oldData.messages
              .filter(
                (msg: Message) =>
                  !(msg.temporary && msg.content === messageContent)
              )
              .concat(serverMessage)
              .sort(
                (a: Message, b: Message) =>
                  new Date(a.createdAt).getTime() -
                  new Date(b.createdAt).getTime()
              )

            return {
              ...oldData,
              messages: updatedMessages,
              updatedAt: serverMessage.createdAt,
            }
          }
        )

        // Update the conversations list with the real message
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData) return []

          const updatedConversations = oldData.map((conv: Conversation) => {
            if (conv.id === selectedConversationId) {
              return {
                ...conv,
                lastMessage: serverMessage,
                updatedAt: serverMessage.createdAt,
              }
            }
            return conv
          })

          return updatedConversations.sort(
            (a: Conversation, b: Conversation) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          )
        })

        // Scroll to bottom again after server update
        scrollToBottom()
      } catch (error: any) {
        console.error('Error sending message:', error)

        // Mark the temporary message as error
        queryClient.setQueryData(
          ['conversation', selectedConversationId],
          (oldData: any) => {
            if (!oldData) return null

            // Find the temporary message and mark it as error
            const updatedMessages = oldData.messages.map((msg: Message) => {
              if (msg.temporary) {
                return { ...msg, error: true }
              }
              return msg
            })

            return {
              ...oldData,
              messages: updatedMessages,
            }
          }
        )

        // Show error toast with specific message
        if (
          error.response?.status === 403 ||
          (error.message && error.message.includes('not a participant'))
        ) {
          toast.error('You are not a participant in this conversation')
        } else {
          toast.error(
            'Failed to send message: ' +
              (error.response?.data?.message ||
                error.message ||
                'Unknown error')
          )
        }
      }
    },
    [
      currentUser,
      message,
      selectedConversationId,
      token,
      queryClient,
      scrollToBottom,
    ]
  )

  // Start new conversation
  const startNewConversation = useCallback(
    async (userId: string) => {
      if (!currentUser || !socket || !token) return

      const selectedUser = searchResults.find(
        (user: User) => user.id === userId
      )
      if (!selectedUser) {
        console.error('Selected user not found in search results')
        return
      }

      // Use the teacher-student specific endpoint if the current user is a teacher
      // and the selected user is a student
      if (currentUser.role === 'Teacher' && selectedUser.role === 'Student') {
        createTeacherStudentChatMutation.mutate(
          {
            studentId: userId,
            initialMessage: 'Hello,',
            token,
          },
          {
            onSuccess: (data) => {
              // Join the conversation room via socket
              socket.emit('joinConversation', {
                conversationId: data.id,
              })
            },
            onError: (error) => {
              console.error('Error creating teacher-student chat:', error)
              toast.error('Failed to create conversation')
            },
          }
        )
      } else {
        // Regular conversation creation
        createConversationMutation.mutate(
          {
            participantIds: [userId, currentUser.id],
            isGroupChat: false,
            name: `${currentUser.firstname} ${currentUser.lastname} - ${selectedUser.firstname} ${selectedUser.lastname}`,
            token,
          },
          {
            onSuccess: (data) => {
              // Join the conversation room via socket
              socket.emit('joinConversation', {
                conversationId: data.id,
              })
            },
            onError: (error) => {
              console.error('Error creating conversation:', error)
              toast.error('Failed to create conversation')
            },
          }
        )
      }
    },
    [
      currentUser,
      socket,
      token,
      searchResults,
      createTeacherStudentChatMutation,
      createConversationMutation,
    ]
  )

  // Handle selecting a conversation
  const handleSelectConversation = useCallback(
    async (conversationId: string) => {
      if (!conversationId) return

      // Immediately set the selected conversation ID for UI responsiveness
      setSelectedConversationId(conversationId)

      // Immediately scroll to bottom for better UX
      scrollToBottom()

      try {
        // Fetch the latest conversation data from the server
        const { data } = await axios.get(
          `${API_URL}/chat/conversations/${conversationId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        )

        if (!data) {
          throw new Error('Failed to fetch conversation')
        }

        // Ensure messages are sorted by timestamp
        const sortedMessages = [...(data.messages || [])].sort(
          (a: Message, b: Message) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )

        // Update the conversation with the latest data
        const updatedConversation = {
          ...data,
          messages: sortedMessages,
        }

        // Update the cache with the latest data
        queryClient.setQueryData(
          ['conversation', conversationId],
          updatedConversation
        )

        // Scroll to bottom after updating with new data
        scrollToBottom()

        // Mark conversation as read - both in the backend and locally in the cache
        if (
          data.unreadCount > 0 ||
          data.messages.some(
            (msg: Message) => !msg.isRead && msg.senderId !== currentUser?.id
          )
        ) {
          // Update on the server
          await axios.patch(
            `${API_URL}/chat/conversations/${conversationId}/read`,
            {},
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          )

          // Update the selected conversation in the cache to mark all messages as read
          queryClient.setQueryData(
            ['conversation', conversationId],
            (oldData: any) => {
              if (!oldData) return oldData

              // Mark all messages from other users as read
              const updatedMessages = oldData.messages.map((msg: Message) => {
                if (msg.senderId !== currentUser?.id && !msg.isRead) {
                  return { ...msg, isRead: true }
                }
                return msg
              })

              return {
                ...oldData,
                messages: updatedMessages,
                unreadCount: 0,
              }
            }
          )

          // Update the conversations list to reflect read status
          queryClient.setQueryData(['conversations'], (oldData: any) => {
            if (!oldData) return oldData

            return oldData.map((conv: Conversation) => {
              if (conv.id === conversationId) {
                // Mark all messages in this conversation as read
                const updatedMessages = conv.messages.map((msg: Message) => {
                  if (msg.senderId !== currentUser?.id && !msg.isRead) {
                    return { ...msg, isRead: true }
                  }
                  return msg
                })

                return {
                  ...conv,
                  messages: updatedMessages,
                  unreadCount: 0,
                }
              }
              return conv
            })
          })
        }
      } catch (error: any) {
        console.error('Error selecting conversation:', error)

        // Handle permission errors specifically
        if (
          error.response?.status === 403 ||
          (error.message && error.message.includes('not a participant'))
        ) {
          toast.error('You are not a participant in this conversation')

          // Remove this conversation from the selected conversation
          setSelectedConversationId(null)

          // Refresh conversations list to get accurate data
          queryClient.invalidateQueries({ queryKey: ['conversations'] })
        } else {
          // Handle other errors
          toast.error(
            'Failed to load conversation: ' +
              (error.response?.data?.message ||
                error.message ||
                'Unknown error')
          )
        }
      }
    },
    [queryClient, scrollToBottom, token, currentUser]
  )

  // Get the other participant in a conversation
  const getOtherParticipant = useCallback(
    (conversation: Conversation): User | undefined => {
      if (!currentUser || !conversation.participants) return undefined
      return conversation.participants.find((p) => p.id !== currentUser.id)
    },
    [currentUser]
  )

  // Filter conversations based on the conversation filter
  const filteredConversations = conversations.filter((conversation) => {
    if (!conversationFilter) return true

    const otherUser = getOtherParticipant(conversation)
    if (!otherUser) return false

    const fullName =
      `${otherUser.firstname} ${otherUser.lastname}`.toLowerCase()
    return fullName.includes(conversationFilter.toLowerCase())
  })

  return {
    conversations: filteredConversations,
    selectedConversation,
    selectedConversationId,
    message,
    setMessage,
    isLoading,
    searchTerm,
    setSearchTerm,
    searchResults,
    isSearching,
    isSearchLoading,
    messagesEndRef,
    handleSendMessage,
    startNewConversation,
    handleSelectConversation,
    getOtherParticipant,
    toggleSearch,
    conversationFilter,
    setConversationFilter,
  }
}
