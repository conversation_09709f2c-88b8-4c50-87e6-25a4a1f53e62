import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

interface WebSocketConfig {
  url?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

interface SessionStatusUpdate {
  sessionId: string;
  oldStatus: string;
  newStatus: string;
  timestamp: string;
  sessionData: {
    id: string;
    className: string;
    subjectName: string;
    date: string;
    timeSlot: string;
    status: string;
    teacherId: string;
  };
}

interface BatchSessionStatusUpdate {
  updates: SessionStatusUpdate[];
}

export const useWebSocket = (config: WebSocketConfig = {}) => {
  const {
    url = import.meta.env.VITE_WS_URL || import.meta.env.VITE_API_URL || 'http://localhost:3000',
    autoConnect = false, // Disable auto-connect by default to prevent infinite loops
    reconnectAttempts = 3, // Reduce reconnect attempts
    reconnectDelay = 2000 // Increase delay between attempts
  } = config;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [reconnectCount, setReconnectCount] = useState(0);
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Event listeners storage
  const eventListenersRef = useRef<Map<string, Function[]>>(new Map());

  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      console.log('🔌 WebSocket already connected');
      return;
    }

    console.log('🔌 Connecting to WebSocket...', url);
    
    try {
      const socket = io(url, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
      });

      socket.on('connect', () => {
        console.log('✅ WebSocket connected successfully');
        setIsConnected(true);
        setConnectionError(null);
        setReconnectCount(0);
        
        // Clear any pending reconnect timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      });

      socket.on('disconnect', (reason) => {
        console.log('❌ WebSocket disconnected:', reason);
        setIsConnected(false);
        
        // Attempt to reconnect if not manually disconnected
        if (reason !== 'io client disconnect' && reconnectCount < reconnectAttempts) {
          scheduleReconnect();
        }
      });

      socket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error);
        setConnectionError(error.message);
        setIsConnected(false);

        // Only attempt to reconnect if we haven't exceeded the limit
        if (reconnectCount < reconnectAttempts) {
          scheduleReconnect();
        } else {
          console.warn('⚠️ Max reconnection attempts reached. Stopping reconnection.');
          setConnectionError('Failed to connect after multiple attempts. WebSocket features disabled.');
        }
      });

      socketRef.current = socket;
    } catch (error) {
      console.error('❌ Error creating WebSocket connection:', error);
      setConnectionError(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [url, reconnectCount, reconnectAttempts]);

  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = reconnectDelay * Math.pow(2, reconnectCount); // Exponential backoff
    console.log(`🔄 Scheduling reconnect in ${delay}ms (attempt ${reconnectCount + 1}/${reconnectAttempts})`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      setReconnectCount(prev => prev + 1);
      connect();
    }, delay);
  }, [reconnectDelay, reconnectCount, reconnectAttempts, connect]);

  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting WebSocket...');
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionError(null);
    setReconnectCount(0);
  }, []);

  const on = useCallback((event: string, callback: Function) => {
    if (!socketRef.current) {
      console.warn(`⚠️ Cannot add listener for '${event}': WebSocket not connected`);
      return;
    }

    // Store the callback for cleanup
    const listeners = eventListenersRef.current.get(event) || [];
    listeners.push(callback);
    eventListenersRef.current.set(event, listeners);

    socketRef.current.on(event, callback as any);
    console.log(`👂 Added listener for '${event}'`);
  }, []);

  const off = useCallback((event: string, callback?: Function) => {
    if (!socketRef.current) {
      return;
    }

    if (callback) {
      socketRef.current.off(event, callback as any);
      
      // Remove from stored listeners
      const listeners = eventListenersRef.current.get(event) || [];
      const filteredListeners = listeners.filter(cb => cb !== callback);
      eventListenersRef.current.set(event, filteredListeners);
    } else {
      socketRef.current.off(event);
      eventListenersRef.current.delete(event);
    }
    
    console.log(`🔇 Removed listener for '${event}'`);
  }, []);

  const emit = useCallback((event: string, data?: any) => {
    if (!socketRef.current?.connected) {
      console.warn(`⚠️ Cannot emit '${event}': WebSocket not connected`);
      return;
    }

    socketRef.current.emit(event, data);
    console.log(`📤 Emitted '${event}'`, data);
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      // Cleanup on unmount
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Cleanup event listeners on unmount
  useEffect(() => {
    return () => {
      eventListenersRef.current.clear();
    };
  }, []);

  return {
    isConnected,
    connectionError,
    reconnectCount,
    connect,
    disconnect,
    on,
    off,
    emit,
    socket: socketRef.current
  };
};

// Specialized hook for session status updates
export const useSessionStatusUpdates = (onStatusUpdate?: (update: SessionStatusUpdate) => void, onBatchUpdate?: (updates: SessionStatusUpdate[]) => void) => {
  const webSocket = useWebSocket();

  useEffect(() => {
    if (!webSocket.isConnected) {
      return;
    }

    // Listen for individual session status updates
    const handleStatusUpdate = (update: SessionStatusUpdate) => {
      console.log('📊 Received session status update:', update);
      onStatusUpdate?.(update);
    };

    // Listen for batch session status updates
    const handleBatchUpdate = (data: BatchSessionStatusUpdate) => {
      console.log('📊 Received batch session status update:', data.updates.length, 'sessions');
      onBatchUpdate?.(data.updates);
    };

    webSocket.on('sessionStatusUpdate', handleStatusUpdate);
    webSocket.on('batchSessionStatusUpdate', handleBatchUpdate);

    return () => {
      webSocket.off('sessionStatusUpdate', handleStatusUpdate);
      webSocket.off('batchSessionStatusUpdate', handleBatchUpdate);
    };
  }, [webSocket, onStatusUpdate, onBatchUpdate]);

  return webSocket;
};
