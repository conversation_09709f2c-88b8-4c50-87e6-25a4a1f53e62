import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  notesService,
  CreateCollectionDto,
  UpdateCollectionDto,
  CreateNoteDto,
  UpdateNoteDto
} from '@/services/notesService'
import { useToast } from '@/components/ui/use-toast'

// Helper to get token from localStorage
const getToken = () => {
  const token = localStorage.getItem('access_token')
  if (!token) {
    console.error('No access_token found in localStorage for notes API')
    return ''
  }
  return token
}

// Query keys for caching
const NOTES_KEYS = {
  collections: ['notes', 'collections'],
  collection: (id: string) => ['notes', 'collections', id],
  notes: ['notes', 'notes'],
  note: (id: string) => ['notes', 'notes', id],
}

export const useNotes = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const token = getToken()

  // Collections Queries and Mutations
  
  // Get all collections
  const useCollections = () => {
    return useQuery({
      queryKey: NOTES_KEYS.collections,
      queryFn: () => notesService.getCollections(token),
      enabled: !!token,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    })
  }

  // Get collection by ID
  const useCollection = (id: string) => {
    return useQuery({
      queryKey: NOTES_KEYS.collection(id),
      queryFn: () => notesService.getCollectionById(id, token),
      enabled: !!token && !!id,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    })
  }

  // Create collection
  const useCreateCollection = () => {
    return useMutation({
      mutationFn: (data: CreateCollectionDto) => 
        notesService.createCollection(data, token),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collections })
        toast({
          title: 'Success',
          description: 'Collection created successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to create collection',
          variant: 'destructive',
        })
      },
    })
  }

  // Update collection
  const useUpdateCollection = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: UpdateCollectionDto }) =>
        notesService.updateCollection(id, data, token),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collections })
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collection(variables.id) })
        toast({
          title: 'Success',
          description: 'Collection updated successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to update collection',
          variant: 'destructive',
        })
      },
    })
  }

  // Delete collection
  const useDeleteCollection = () => {
    return useMutation({
      mutationFn: (id: string) => notesService.deleteCollection(id, token),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collections })
        toast({
          title: 'Success',
          description: 'Collection deleted successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete collection',
          variant: 'destructive',
        })
      },
    })
  }

  // Notes Queries and Mutations
  
  // Get note by ID
  const useNote = (id: string) => {
    return useQuery({
      queryKey: NOTES_KEYS.note(id),
      queryFn: () => notesService.getNoteById(id, token),
      enabled: !!token && !!id,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    })
  }

  // Create note
  const useCreateNote = () => {
    return useMutation({
      mutationFn: (data: CreateNoteDto) => 
        notesService.createNote(data, token),
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collections })
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collection(data.collectionId) })
        toast({
          title: 'Success',
          description: 'Note created successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to create note',
          variant: 'destructive',
        })
      },
    })
  }

  // Update note
  const useUpdateNote = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: UpdateNoteDto; collectionId: string }) =>
        notesService.updateNote(id, data, token),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.note(variables.id) })
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collection(variables.collectionId) })
        toast({
          title: 'Success',
          description: 'Note updated successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to update note',
          variant: 'destructive',
        })
      },
    })
  }

  // Delete note
  const useDeleteNote = () => {
    return useMutation({
      mutationFn: ({ id }: { id: string; collectionId: string }) => 
        notesService.deleteNote(id, token),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: NOTES_KEYS.collection(variables.collectionId) })
        toast({
          title: 'Success',
          description: 'Note deleted successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete note',
          variant: 'destructive',
        })
      },
    })
  }

  return {
    // Collection hooks
    useCollections,
    useCollection,
    useCreateCollection,
    useUpdateCollection,
    useDeleteCollection,
    
    // Note hooks
    useNote,
    useCreateNote,
    useUpdateNote,
    useDeleteNote,
  }
}

export default useNotes
