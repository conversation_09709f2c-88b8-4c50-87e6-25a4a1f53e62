import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

// Get API URL from environment variables
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

/**
 * Custom hook to fetch and track unread message counts
 * @param userId The ID of the user to check unread messages for
 * @returns Object containing unread message count and loading state
 */
export function useUnreadMessages(userId?: string) {
  const [unreadCount, setUnreadCount] = useState<number>(0)

  // Get auth token from localStorage
  const token = localStorage.getItem('access_token')
  const currentUserId = localStorage.getItem('id')

  // Fetch unread message count
  const { data, isLoading, error } = useQuery({
    queryKey: ['unreadMessages', userId],
    queryFn: async () => {
      if (!token || !currentUserId) {
        return { count: 0 }
      }

      try {
        // If a specific userId is provided, get unread messages for that conversation
        if (userId) {
          const response = await axios.get(
            `${API_URL}/chat/conversations/unread/${userId}`,
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          )
          return response.data
        }
        // Otherwise, get total unread messages across all conversations
        else {
          const response = await axios.get(
            `${API_URL}/chat/conversations/unread`,
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          )
          return response.data
        }
      } catch (error) {
        console.error('Error fetching unread messages:', error)
        return { count: 0 }
      }
    },
    // Refresh every 30 seconds
    refetchInterval: 30000,
    // Don't run if we don't have a token or current user ID
    enabled: !!token && !!currentUserId,
  })

  // Update unread count when data changes
  useEffect(() => {
    if (data) {
      setUnreadCount(data.count || 0)
    }
  }, [data])

  // Fallback to localStorage if API call fails
  useEffect(() => {
    if (error) {
      // Try to get unread count from localStorage as fallback
      const storedCount = localStorage.getItem('unreadMessageCount')
      if (storedCount) {
        setUnreadCount(parseInt(storedCount, 10))
      }
    }
  }, [error])

  return {
    unreadCount,
    isLoading,
    error,
  }
}
