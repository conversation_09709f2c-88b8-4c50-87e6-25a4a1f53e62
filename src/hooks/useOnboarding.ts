import { useMutation, useQueryClient } from '@tanstack/react-query'
import { onboardingService } from '@/lib/api/services/onboarding-service'
import { useToast } from '@/components/ui/use-toast'

/**
 * Hook for managing onboarding status
 * @returns Object with functions to update onboarding status
 */
export const useOnboarding = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Mutation to complete onboarding
  const completeOnboardingMutation = useMutation({
    mutationFn: () => onboardingService.updateOnboardingStatus(true),
    onSuccess: () => {
      // Update local storage
      localStorage.setItem('userOnboarding', 'false')

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['user'] })

      toast({
        title: 'Onboarding completed',
        description: 'Your onboarding status has been updated.',
      })
    },
    onError: (error: any) => {
      console.error('Failed to complete onboarding:', error)
      toast({
        title: 'Error',
        description: 'Failed to update onboarding status. Please try again.',
        variant: 'destructive',
      })
    },
  })

  // Mutation to reset onboarding
  const resetOnboardingMutation = useMutation({
    mutationFn: () => onboardingService.updateOnboardingStatus(false),
    onSuccess: () => {
      // Update local storage
      localStorage.setItem('userOnboarding', 'true')

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['user'] })

      toast({
        title: 'Onboarding reset',
        description: 'Your onboarding status has been reset.',
      })
    },
    onError: (error: any) => {
      console.error('Failed to reset onboarding:', error)
      toast({
        title: 'Error',
        description: 'Failed to reset onboarding status. Please try again.',
        variant: 'destructive',
      })
    },
  })

  return {
    completeOnboarding: completeOnboardingMutation.mutate,
    resetOnboarding: resetOnboardingMutation.mutate,
    isUpdating:
      completeOnboardingMutation.isPending || resetOnboardingMutation.isPending,
  }
}
