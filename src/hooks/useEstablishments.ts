import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'
import { z } from 'zod'
import { directPatchRequest, xhrPatchRequest } from '@/utils/directFetch'

// Define the schema for the establishment form
export const establishmentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().min(1, 'Address is required'),
  logo: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  url: z.string().min(1, 'URL is required').optional().or(z.literal('')),
  CMSContent: z.string().optional(),
  services: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  // Additional fields for the landing page
  description: z.string().optional().or(z.literal('')),
  heroImage: z.string().url('Must be a valid URL').optional().or(z.literal('')),
  galleryImages: z.array(z.string()).optional(),
  contactEmail: z
    .string()
    .email('Must be a valid email')
    .optional()
    .or(z.literal('')),
  contactPhone: z.string().optional().or(z.literal('')),
  socialLinks: z
    .object({
      facebook: z
        .string()
        .url('Must be a valid URL')
        .optional()
        .or(z.literal('')),
      twitter: z
        .string()
        .url('Must be a valid URL')
        .optional()
        .or(z.literal('')),
      instagram: z
        .string()
        .url('Must be a valid URL')
        .optional()
        .or(z.literal('')),
      linkedin: z
        .string()
        .url('Must be a valid URL')
        .optional()
        .or(z.literal('')),
    })
    .optional(),
  stats: z
    .object({
      students: z.number().optional(),
      teachers: z.number().optional(),
      courses: z.number().optional(),
      awards: z.number().optional(),
    })
    .optional(),
  // Student card design settings
  cardDesign: z
    .object({
      theme: z.object({
        id: z.string(),
        name: z.string(),
        primaryColor: z.string(),
        secondaryColor: z.string(),
        textColor: z.string(),
        accentColor: z.string(),
      }),
      layout: z.enum(['horizontal', 'vertical']),
      validUntilDate: z.string(),
      showQrCode: z.boolean(),
      showLogo: z.boolean(),
      customFields: z.object({
        enabled: z.boolean(),
        field1Label: z.string(),
        field2Label: z.string(),
      }),
    })
    .optional(),
})

export type EstablishmentFormValues = z.infer<typeof establishmentSchema>

// Type for API calls that matches what works in Postman
export type EstablishmentApiData = {
  name: string
  address: string
  logo?: string
  url?: string
  CMSContent?: string
  description?: string
  heroImage?: string
  galleryImages?: string[]
  contactEmail?: string
  contactPhone?: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    instagram?: string
    linkedin?: string
  }
  stats?: {
    students?: number
    teachers?: number
    courses?: number
    awards?: number
  }
  services?: string[]
  isActive?: boolean
  cardDesign?: {
    theme: {
      id: string
      name: string
      primaryColor: string
      secondaryColor: string
      textColor: string
      accentColor: string
    }
    layout: 'horizontal' | 'vertical'
    validUntilDate: string
    showQrCode: boolean
    showLogo: boolean
    customFields: {
      enabled: boolean
      field1Label: string
      field2Label: string
    }
  }
  superAdminId?: string
}

export interface Establishment {
  id: string
  name: string
  address: string
  logo?: string
  url?: string
  CMSContent?: string
  services?: string[]
  isActive: boolean
  createdAt?: Date
  updatedAt?: Date
  createdBy?: string
  updatedBy?: string
  // Additional fields for the landing page
  description?: string
  heroImage?: string
  galleryImages?: string[]
  contactEmail?: string
  contactPhone?: string
  socialLinks?: {
    facebook?: string
    twitter?: string
    instagram?: string
    linkedin?: string
  }
  stats?: {
    students?: number
    teachers?: number
    courses?: number
    awards?: number
  }
  // Student card design settings
  cardDesign?: {
    theme: {
      id: string
      name: string
      primaryColor: string
      secondaryColor: string
      textColor: string
      accentColor: string
    }
    layout: 'horizontal' | 'vertical'
    validUntilDate: string
    showQrCode: boolean
    showLogo: boolean
    customFields: {
      enabled: boolean
      field1Label: string
      field2Label: string
    }
  }
}

// Get user data from localStorage exactly as login.tsx stores it
const getUserFromStorage = () => {
  try {
    // Get the ID directly from localStorage - this is how login.tsx stores it
    const id = localStorage.getItem('id')
    const role = localStorage.getItem('role')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')

    if (id) {
      console.log('Found user ID in localStorage:', id)

      // Create user object with the exact format from login.tsx
      return {
        id,
        role,
        firstname,
        lastname,
      }
    }
  } catch (error) {
    console.error('Error getting user data from localStorage:', error)
  }



  console.warn('No user data found in localStorage')
  return null
}

// Get token from localStorage exactly as login.tsx stores it
const getTokenFromStorage = () => {
  try {
    // Get the access_token directly - this is how login.tsx stores it
    const accessToken = localStorage.getItem('access_token')

    if (accessToken) {
      console.log('Found access_token in localStorage')
      return accessToken
    }
  } catch (error) {
    console.error('Error getting token from localStorage:', error)
  }

  // Development fallback
  if (import.meta.env.DEV) {
    console.log('Using development fallback token')
    return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwZGQwYTk0Mi03NGY4LTRkMzktOTAyNy03YWM0NTk3MGE2ZDkiLCJyb2xlIjoiU3VwZXJBZG1pbiIsImlhdCI6MTYxNjE2MjM5MCwiZXhwIjoxNjE2MTY1OTkwfQ.dev-token-signature'
  }

  console.warn('No token found in localStorage')
  return null
}

// API base URL
const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'http://localhost:3000'

// Custom hook for establishment data operations
export function useEstablishments() {
  const user = getUserFromStorage()
  const token = getTokenFromStorage()
  const queryClient = useQueryClient()
  const isDev = import.meta.env.DEV

  // Format token correctly for the backend
  const getFormattedToken = () => {
    if (!token) return null
    return token.startsWith('Bearer ') ? token : `Bearer ${token}`
  }

  // Clean data before sending to API
  const cleanApiData = (data: EstablishmentApiData): EstablishmentApiData => {
    // Create a copy to avoid modifying the original
    const cleanedData = { ...data }

    // List of properties that should not be sent to the API
    const propertiesToRemove = [
      'superAdminId',
      'superAdminProfileId',
      'etablissementId',
      'id',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'adminProfiles',
      'teacherProfiles',
      'studentProfiles',
      'superAdminProfile',
      'cardDesign' // This property is causing the 400 error
    ]

    // Remove properties that should not be sent
    propertiesToRemove.forEach(prop => {
      if (prop in cleanedData) {
        delete (cleanedData as any)[prop]
        console.log(`Removed ${prop} from API data`)
      }
    })

    return cleanedData
  }

  // Fetch a single establishment by ID
  const fetchEstablishmentById = async (id: string) => {
    if (!token && !isDev) {
      console.error('No authentication token available')
      throw new Error('Authentication required')
    }

    const formattedToken = getFormattedToken()
    if (!formattedToken && !isDev) {
      throw new Error('Invalid token')
    }

    console.log('Fetching establishment with ID:', id)

    try {
      const url = `${API_BASE_URL}/etablissement/${id}`
      console.log('Fetching from URL:', url)

      const response = await axios
        .get(url, {
          headers: {
            Authorization: formattedToken,
          },
        })
        .catch((error) => {
          if (isDev) {
            console.warn(
              'API call failed in development mode, returning mock data:',
              error
            )
            return { data: null }
          }
          throw error
        })

      console.log('Successfully fetched establishment:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching establishment:', error)

      if (isDev) {
        console.warn('Returning mock data in development mode')
        return null
      }

      throw error
    }
  }

  // Fetch all establishments
  const fetchEstablishments = async () => {
    if (!user?.id) {
      console.error('No user ID available for fetching establishments')
      throw new Error('User ID required')
    }

    if (!token && !isDev) {
      console.error('No authentication token available')
      throw new Error('Authentication required')
    }

    const formattedToken = getFormattedToken()
    if (!formattedToken && !isDev) {
      throw new Error('Invalid token')
    }

    console.log('Fetching establishments for user ID:', user.id)

    try {
      // First try to get establishments by super admin ID
      // This matches the endpoint in etablissement.controller.ts
      const superAdminUrl = `${API_BASE_URL}/etablissement/super-admin/${user.id}`
      console.log('Fetching from URL:', superAdminUrl)

      const response = await axios
        .get(superAdminUrl, {
          headers: {
            Authorization: formattedToken,
          },
        })
        .catch((error) => {
          if (isDev) {
            console.warn(
              'API call failed in development mode, returning mock data:',
              error
            )
            return { data: [] }
          }
          throw error
        })

      console.log('Successfully fetched establishments:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching from super-admin endpoint:', error)

      // If it's a 404 or 401, try the general endpoint
      if (
        axios.isAxiosError(error) &&
        (error.response?.status === 404 || error.response?.status === 401)
      ) {
        try {
          const generalUrl = `${API_BASE_URL}/etablissement`
          console.log('Trying general endpoint:', generalUrl)

          const response = await axios
            .get(generalUrl, {
              headers: {
                Authorization: formattedToken,
              },
            })
            .catch((error) => {
              if (isDev) {
                console.warn(
                  'API call failed in development mode, returning mock data:',
                  error
                )
                return { data: [] }
              }
              throw error
            })

          console.log(
            'Successfully fetched from general endpoint:',
            response.data
          )

          // Filter establishments for this super admin
          const filteredData = response.data.filter(
            (est: any) => est.superAdminId === user.id
          )

          console.log('Filtered establishments for user:', filteredData)
          return filteredData
        } catch (innerError) {
          console.error('Error fetching from general endpoint:', innerError)

          if (isDev) {
            console.warn('Returning mock data in development mode')
            return []
          }

          if (
            axios.isAxiosError(innerError) &&
            innerError.response?.status === 401
          ) {
            throw new Error('Authentication failed - please log in again')
          }

          throw innerError
        }
      }

      if (isDev) {
        console.warn('Returning mock data in development mode')
        return []
      }

      throw error
    }
  }

  // Create establishment
  const createEstablishment = async (
    data: EstablishmentFormValues | EstablishmentApiData
  ): Promise<Establishment> => {
    // Convert to API data format that works with the server
    const apiData: EstablishmentApiData = {
      name: data.name,
      address: data.address,
      logo: data.logo && data.logo.trim() !== '' ? data.logo : undefined,
      url: data.url && data.url.trim() !== '' ? data.url : undefined,
      CMSContent: data.CMSContent,
      description: data.description,
      heroImage: data.heroImage,
      galleryImages: data.galleryImages,
      contactEmail: data.contactEmail,
      contactPhone: data.contactPhone,
      socialLinks: data.socialLinks,
      stats: data.stats,
      services: data.services,
      isActive: data.isActive !== undefined ? data.isActive : true,
      // superAdminId is handled by the backend based on the JWT token
      // Do not include cardDesign as it causes a 400 error
    }
    if (!user?.id) {
      console.error('No user ID available for creating establishment')
      throw new Error('User ID required')
    }

    if (!token && !isDev) {
      console.error('No authentication token available')
      throw new Error('Authentication required')
    }

    const formattedToken = getFormattedToken()
    if (!formattedToken && !isDev) {
      throw new Error('Invalid token')
    }

    // Check if the user already has an establishment
    try {
      const existingEstablishments = await fetchEstablishments()
      if (existingEstablishments && existingEstablishments.length >= 1) {
        console.warn('User already has an establishment')
        throw new Error('You can only create one establishment')
      }
    } catch (error) {
      // If the error is not about having too many establishments, we can proceed
      if (
        error instanceof Error &&
        error.message === 'You can only create one establishment'
      ) {
        throw error
      }
      console.warn(
        'Error checking existing establishments, proceeding with creation:',
        error
      )
    }

    // Clean the data before sending to the API
    const cleanedData = cleanApiData(apiData)

    console.log('Creating establishment with data:', cleanedData)

    try {
      // Use the endpoint as defined in the controller
      const url = `${API_BASE_URL}/etablissement`
      console.log('Posting to URL:', url)
      console.log('Sending data:', JSON.stringify(cleanedData, null, 2))

      const response = await axios
        .post(url, cleanedData, {
          headers: {
            Authorization: formattedToken,
            'Content-Type': 'application/json',
          },
        })
        .catch((error) => {
          if (isDev) {
            console.warn(
              'API call failed in development mode, returning mock data:',
              error
            )
            return {
              data: {
                id: 'mock-establishment-id',
                ...apiData,
                isActive: apiData.isActive ?? true,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            }
          }
          // Log detailed error information
          if (axios.isAxiosError(error) && error.response) {
            console.error('Server responded with error:', {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data,
              headers: error.response.headers,
            })
          }
          throw error
        })

      console.log('Establishment created successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error creating establishment:', error)

      // Handle specific error cases
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed - please log in again')
        }
        if (error.response?.status === 403) {
          throw new Error(
            'You do not have permission to create an establishment'
          )
        }
        if (error.response?.status === 400) {
          const errorMessage =
            error.response.data?.message || 'Invalid establishment data'
          console.error('Bad request error (400):', {
            message: errorMessage,
            responseData: error.response.data,
            sentData: apiData,
          })
          throw new Error(`Bad request: ${errorMessage}`)
        }
        if (error.response?.status === 409) {
          throw new Error('You already have an establishment')
        }

        // Generic error with response details
        if (error.response) {
          throw new Error(
            `Server error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`
          )
        }
      }

      // In development mode, return a mock response
      if (isDev) {
        console.warn('Creating mock establishment in development mode')
        return {
          id: 'mock-establishment-id',
          ...apiData,
          isActive: apiData.isActive ?? true,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      }

      throw new Error(
        'Failed to create establishment: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      )
    }
  }

  // Update establishment
  const updateEstablishment = async ({
    id,
    data,
  }: {
    id: string
    data: EstablishmentFormValues | EstablishmentApiData
  }): Promise<Establishment> => {
    // Convert to API data format that works with the server
    const apiData: EstablishmentApiData = {
      name: data.name,
      address: data.address,
      logo: data.logo && data.logo.trim() !== '' ? data.logo : undefined,
      url: data.url && data.url.trim() !== '' ? data.url : undefined,
      CMSContent: data.CMSContent,
      description: data.description,
      heroImage: data.heroImage,
      galleryImages: data.galleryImages,
      contactEmail: data.contactEmail,
      contactPhone: data.contactPhone,
      socialLinks: data.socialLinks,
      stats: data.stats,
      services: data.services,
      isActive: data.isActive !== undefined ? data.isActive : true,
      // Do not include superAdminId as it will be automatically handled by the backend
      // Do not include cardDesign as it causes a 400 error
    }
    if (!user?.id) {
      console.error('No user ID available for updating establishment')
      throw new Error('User ID required')
    }

    if (!token && !isDev) {
      console.error('No authentication token available')
      throw new Error('Authentication required')
    }

    const formattedToken = getFormattedToken()
    if (!formattedToken && !isDev) {
      throw new Error('Invalid token')
    }

    // Clean the data before sending to the API
    const cleanedData = cleanApiData(apiData)

    console.log('Updating establishment with ID:', id)
    console.log('Update data:', cleanedData)

    try {
      // Use the endpoint as defined in the controller
      const url = `${API_BASE_URL}/etablissement/${id}`
      console.log('Sending PATCH to URL:', url)

      // Try using XHR first (most compatible with Postman)
      try {
        console.log('Attempting update with XHR in hook...');
        const xhrResult = await xhrPatchRequest(url, cleanedData);
        console.log('XHR update successful in hook:', xhrResult);
        return xhrResult;
      } catch (xhrError) {
        console.error('XHR update failed in hook, trying direct fetch:', xhrError);

        // Try direct fetch as a fallback
        try {
          console.log('Attempting update with direct fetch in hook...');
          const directResult = await directPatchRequest(url, cleanedData);
          console.log('Direct fetch update successful in hook:', directResult);
          return directResult;
        } catch (directFetchError) {
          console.error('Direct fetch update failed in hook, falling back to axios:', directFetchError);
        }
      }

      // Fall back to axios if direct fetch fails
      const response = await axios
        .patch(url, cleanedData, {
          headers: {
            Authorization: formattedToken,
            'Content-Type': 'application/json',
          },
        })
        .catch((error) => {
          if (isDev) {
            console.warn(
              'API call failed in development mode, returning mock data:',
              error
            )
            return {
              data: {
                id,
                ...apiData,
                isActive: apiData.isActive ?? true,
                updatedAt: new Date(),
              },
            }
          }
          // Log detailed error information
          if (axios.isAxiosError(error) && error.response) {
            console.error('Server responded with error:', {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data,
              headers: error.response.headers,
            })
          }
          throw error
        })

      console.log('Establishment updated successfully:', response.data)
      return response.data
    } catch (error) {
      console.error('Error updating establishment:', error)

      // Handle specific error cases
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed - please log in again')
        }
        if (error.response?.status === 403) {
          throw new Error(
            'You do not have permission to update this establishment'
          )
        }
        if (error.response?.status === 404) {
          throw new Error('Establishment not found')
        }
        if (error.response?.status === 400) {
          const errorMessage =
            error.response.data?.message || 'Invalid establishment data'
          console.error('Bad request error (400):', {
            message: errorMessage,
            responseData: error.response.data,
            sentData: apiData,
          })
          throw new Error(`Bad request: ${errorMessage}`)
        }

        // Generic error with response details
        if (error.response) {
          throw new Error(
            `Server error (${error.response.status}): ${error.response.data?.message || 'Unknown error'}`
          )
        }
      }

      // In development mode, return a mock response
      if (isDev) {
        console.warn('Updating mock establishment in development mode')
        return {
          id,
          ...apiData,
          isActive: apiData.isActive ?? true,
          updatedAt: new Date(),
        }
      }

      throw new Error(
        'Failed to update establishment: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      )
    }
  }

  // React Query hooks
  const establishmentsQuery = useQuery({
    queryKey: ['establishments', user?.id],
    queryFn: fetchEstablishments,
    enabled: !!user?.id,
    retry: isDev ? 0 : 1, // Don't retry in development mode
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const createMutation = useMutation({
    mutationFn: createEstablishment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['establishments', user?.id] })
    },
  })

  const updateMutation = useMutation({
    mutationFn: updateEstablishment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['establishments', user?.id] })
    },
  })

  // Create a query for fetching a single establishment
  const getEstablishmentQuery = (id: string) => {
    return useQuery({
      queryKey: ['establishment', id],
      queryFn: () => fetchEstablishmentById(id),
      enabled: !!id && !!user?.id,
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }

  return {
    establishments: establishmentsQuery.data || [],
    isLoading: establishmentsQuery.isLoading,
    isError: establishmentsQuery.isError,
    error: establishmentsQuery.error,
    createEstablishment: createMutation.mutate,
    updateEstablishment: updateMutation.mutate,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    fetchEstablishmentById,
    getEstablishmentQuery,
  }
}
