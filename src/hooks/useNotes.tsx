import { useMutation, useQuery } from '@tanstack/react-query'
import notesService, { 
  Note, 
  NotesCollection, 
  CreateNoteDto, 
  UpdateNoteDto, 
  CreateCollectionDto, 
  UpdateCollectionDto 
} from '@/services/notesService'

export function useNotes() {
  // Get token from localStorage
  const getToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token') || ''
    }
    return ''
  }

  // Collections hooks
  const useCollections = () => {
    return useQuery({
      queryKey: ['notes', 'collections'],
      queryFn: () => notesService.getCollections(getToken()),
    })
  }

  const useCollection = (id: string) => {
    return useQuery({
      queryKey: ['notes', 'collection', id],
      queryFn: () => notesService.getCollectionById(id, getToken()),
      enabled: !!id,
    })
  }

  const useCreateCollection = () => {
    return useMutation({
      mutationFn: (data: CreateCollectionDto) => 
        notesService.createCollection(data, getToken()),
    })
  }

  const useUpdateCollection = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: UpdateCollectionDto }) => 
        notesService.updateCollection(id, data, getToken()),
    })
  }

  const useDeleteCollection = () => {
    return useMutation({
      mutationFn: (id: string) => notesService.deleteCollection(id, getToken()),
    })
  }

  // Notes hooks
  const useNote = (id: string) => {
    return useQuery({
      queryKey: ['notes', 'note', id],
      queryFn: () => notesService.getNoteById(id, getToken()),
      enabled: !!id,
    })
  }

  const useCreateNote = () => {
    return useMutation({
      mutationFn: (data: CreateNoteDto) => notesService.createNote(data, getToken()),
    })
  }

  const useUpdateNote = () => {
    return useMutation({
      mutationFn: ({ id, data, collectionId }: { id: string; data: UpdateNoteDto; collectionId: string }) => 
        notesService.updateNote(id, data, getToken()),
    })
  }

  const useDeleteNote = () => {
    return useMutation({
      mutationFn: ({ id, collectionId }: { id: string; collectionId: string }) => 
        notesService.deleteNote(id, getToken()),
    })
  }

  return {
    useCollections,
    useCollection,
    useCreateCollection,
    useUpdateCollection,
    useDeleteCollection,
    useNote,
    useCreateNote,
    useUpdateNote,
    useDeleteNote,
  }
}

export default useNotes
