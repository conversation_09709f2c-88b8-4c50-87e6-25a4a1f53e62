import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Student } from '@/interface/types'
import { api } from '@/lib/axios'

const getAuthHeader = () => {
  const token = localStorage.getItem('access_token')
  if (!token) throw new Error('Access token not found')
  return { Authorization: `Bearer ${token}` }
}

/**
 * Custom hook for managing student data
 * @returns Object containing student data and mutation functions
 */
export default function useStudent() {
  const queryClient = useQueryClient()

  const {
    data: students = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['students'],
    queryFn: async () => {
      const response = await api.get<Student[]>('/student', {
        headers: getAuthHeader(),
      })
      return response.data
    },
  })

  const createStudent = useMutation({
    mutationFn: async (newStudent: Partial<Student>) => {
      const response = await api.post<Student>('/student', newStudent, {
        headers: getAuthHeader(),
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
    },
  })

  const updateStudent = useMutation({
    mutationFn: async ({ id, ...data }: Partial<Student> & { id: string }) => {
      const response = await api.patch<Student>(`/student/${id}`, data, {
        headers: getAuthHeader(),
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
    },
  })

  const deleteStudent = useMutation({
    mutationFn: async (id: string) => {
      await api.delete(`/student/${id}`, {
        headers: getAuthHeader(),
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['students'] })
    },
  })

  return {
    students,
    isLoading,
    isError,
    error,
    createStudent: createStudent.mutate,
    updateStudent: updateStudent.mutate,
    deleteStudent: deleteStudent.mutate,
    isCreating: createStudent.isPending,
    isUpdating: updateStudent.isPending,
    isDeleting: deleteStudent.isPending,
  }
}
