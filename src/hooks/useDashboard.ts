import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import dashboardService, {
  RecentActivity,
} from '@/lib/api/services/dashboard-service-new'

// Dashboard query keys
const DASHBOARD_KEYS = {
  stats: ['dashboard', 'stats'],
  activities: ['dashboard', 'activities'],
  systemHealth: ['dashboard', 'systemHealth'],
  teacherDashboard: (teacherId: string) => ['dashboard', 'teacher', teacherId],
}

export function useDashboardStats() {
  const queryClient = useQueryClient()

  // Fetch stats with React Query
  const {
    data: stats,
    isLoading,
    error,
  } = useQuery({
    queryKey: DASHBOARD_KEYS.stats,
    queryFn: dashboardService.getStats,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retrying
  })

  // Set up WebSocket subscription for real-time updates
  useEffect(() => {
    try {
      // Subscribe to real-time stats updates
      const unsubscribe = dashboardService.subscribeToStats((newStats) => {
        // Update the query cache with the new data
        queryClient.setQueryData(DASHBOARD_KEYS.stats, newStats)
      })

      // Clean up subscription when component unmounts
      return () => {
        unsubscribe()
      }
    } catch (error) {
      console.warn('Failed to subscribe to stats updates:', error)
      // Return a no-op cleanup function
      return () => {}
    }
  }, [queryClient])

  return {
    stats,
    isLoading,
    error,
  }
}

export function useRecentActivities(limit = 10) {
  const queryClient = useQueryClient()
  const [activities, setActivities] = useState<RecentActivity[]>([])

  // Fetch activities with React Query
  const {
    data: initialActivities,
    isLoading,
    error,
  } = useQuery({
    queryKey: [...DASHBOARD_KEYS.activities, limit],
    queryFn: () => dashboardService.getRecentActivities(limit),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retrying
  })

  // Update local state when initial data loads
  useEffect(() => {
    if (initialActivities && Array.isArray(initialActivities)) {
      setActivities(initialActivities)
    }
  }, [initialActivities])

  // Set up WebSocket subscription for real-time updates
  useEffect(() => {
    try {
      // Subscribe to real-time activity updates
      const unsubscribe = dashboardService.subscribeToActivities(
        (newActivity) => {
          // Add the new activity to the beginning of the list
          setActivities((prev) => {
            // Create a new array with the new activity at the beginning
            const updated = [newActivity, ...prev]
            // Limit the array to the specified limit
            return updated.slice(0, limit)
          })

          // Also update the query cache
          queryClient.setQueryData(
            [...DASHBOARD_KEYS.activities, limit],
            (oldData: RecentActivity[] | undefined) => {
              if (!oldData) return [newActivity]
              const updated = [newActivity, ...oldData]
              return updated.slice(0, limit)
            }
          )
        }
      )

      // Clean up subscription when component unmounts
      return () => {
        unsubscribe()
      }
    } catch (error) {
      console.warn('Failed to subscribe to activity updates:', error)
      // Return a no-op cleanup function
      return () => {}
    }
  }, [queryClient, limit])

  return {
    activities,
    isLoading,
    error,
  }
}

export function useSystemHealth() {
  const queryClient = useQueryClient()

  // Fetch system health with React Query
  const {
    data: systemHealth,
    isLoading,
    error,
  } = useQuery({
    queryKey: DASHBOARD_KEYS.systemHealth,
    queryFn: dashboardService.getSystemHealth,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retrying
  })

  // Set up WebSocket subscription for real-time updates
  useEffect(() => {
    try {
      // Subscribe to real-time system health updates
      const unsubscribe = dashboardService.subscribeToSystemHealth(
        (newHealth) => {
          // Update the query cache with the new data
          queryClient.setQueryData(DASHBOARD_KEYS.systemHealth, newHealth)
        }
      )

      // Clean up subscription when component unmounts
      return () => {
        unsubscribe()
      }
    } catch (error) {
      console.warn('Failed to subscribe to system health updates:', error)
      // Return a no-op cleanup function
      return () => {}
    }
  }, [queryClient])

  return {
    systemHealth,
    isLoading,
    error,
  }
}

export function useTeacherDashboard(teacherId: string) {
  // Fetch teacher dashboard with React Query
  const {
    data: teacherStats,
    isLoading,
    error,
  } = useQuery({
    queryKey: DASHBOARD_KEYS.teacherDashboard(teacherId),
    queryFn: () => dashboardService.getTeacherDashboard(teacherId),
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retrying
  })

  return {
    teacherStats,
    isLoading,
    error,
  }
}

// Combined hook for dashboard data
export function useDashboard(
  role: 'admin' | 'super_admin' | 'teacher',
  teacherId?: string
) {
  const {
    stats,
    isLoading: statsLoading,
    error: statsError,
  } = useDashboardStats()
  const {
    activities,
    isLoading: activitiesLoading,
    error: activitiesError,
  } = useRecentActivities()
  const {
    systemHealth,
    isLoading: healthLoading,
    error: healthError,
  } = useSystemHealth()

  // Only fetch teacher dashboard if role is teacher and teacherId is provided
  const {
    teacherStats,
    isLoading: teacherLoading,
    error: teacherError,
  } = role === 'teacher' && teacherId
    ? useTeacherDashboard(teacherId)
    : { teacherStats: null, isLoading: false, error: null }

  const isLoading =
    statsLoading || activitiesLoading || healthLoading || teacherLoading

  // Convert error objects to strings to avoid React rendering issues
  const errorMessage =
    statsError || activitiesError || healthError || teacherError
      ? 'Failed to load dashboard data'
      : null
  const error = errorMessage

  return {
    stats,
    activities,
    systemHealth,
    teacherStats,
    isLoading,
    error,
  }
}

export default useDashboard
