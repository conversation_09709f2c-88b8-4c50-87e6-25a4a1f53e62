import { useQuery } from '@tanstack/react-query'
import { studentService } from '@/lib/api/services/student-service'
import { useState, useEffect } from 'react'

interface CurrentStudent {
  id: string
  firstname: string
  lastname: string
  isPaid: boolean
  isLoading: boolean
  error: Error | null
  refetch: () => Promise<void> // Add refetch function
}

// Helper function to get student data from localStorage
function getStudentFromLocalStorage() {
  try {
    const id = localStorage.getItem('id')
    const firstname = localStorage.getItem('firstname')
    const lastname = localStorage.getItem('lastname')
    const isPaidStr = localStorage.getItem('isPaid')

    // Check if we have the minimum required data
    if (id && firstname && lastname) {
      return {
        id,
        firstname,
        lastname,
        isPaid: isPaidStr === 'true',
        email: localStorage.getItem('email') || '',
        role: localStorage.getItem('role') || 'Student',
      }
    }
    return null
  } catch (error) {
    console.error('Error getting student data from localStorage:', error)
    return null
  }
}

/**
 * Hook to get the current student's information including payment status
 * Uses React Query for better caching and automatic refetching
 */
export function useCurrentStudent(): CurrentStudent {
  // Get student ID from localStorage
  const studentId = localStorage.getItem('id')

  // State to track if we're using fallback data
  const [usingFallback, setUsingFallback] = useState(false)

  // Get fallback data from localStorage
  const fallbackData = getStudentFromLocalStorage()

  // Check if isPaid is stored in localStorage
  useEffect(() => {
    // If we have a payment status from the admin update, use it
    const storedIsPaid = localStorage.getItem('isPaid')
    if (storedIsPaid !== null) {
      console.log(
        `Using stored payment status from localStorage: ${storedIsPaid}`
      )
    }
  }, [])

  // Use React Query to fetch and cache student data
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['student', studentId],
    queryFn: async () => {
      if (!studentId) {
        throw new Error('Student ID not found in localStorage')
      }
      console.log('Fetching student data for ID:', studentId)
      try {
        const studentData = await studentService.getById(studentId)
        // Store the payment status in localStorage for fallback
        if (studentData && studentData.isPaid !== undefined) {
          localStorage.setItem('isPaid', studentData.isPaid.toString())
        }
        setUsingFallback(false)
        return studentData
      } catch (error) {
        console.error('Error fetching student data, using fallback:', error)
        setUsingFallback(true)
        // If we have fallback data, use it
        if (fallbackData) {
          return fallbackData
        }
        throw error
      }
    },
    enabled: !!studentId,
    staleTime: 60000, // Consider data stale after 1 minute
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchInterval: false, // Don't automatically refetch
    refetchOnMount: true, // Refetch when component mounts
    retry: 1, // Only retry once to avoid too many failed requests
  })

  // Use fallback data if API request failed
  const effectiveData = data || fallbackData

  // Only log payment status on first load or changes
  useEffect(() => {
    if (effectiveData) {
      console.log(
        `Student payment status for ${effectiveData.firstname} ${effectiveData.lastname}: ` +
          `${effectiveData.isPaid ? 'PAID' : 'NOT PAID'} ${usingFallback ? '(using fallback data)' : ''}`
      )
    }
  }, [effectiveData?.isPaid, usingFallback])

  // Return formatted student data
  return {
    id: effectiveData?.id || '',
    firstname: effectiveData?.firstname || '',
    lastname: effectiveData?.lastname || '',
    isPaid:
      effectiveData?.isPaid ||
      localStorage.getItem('isPaid') === 'true' ||
      false,
    isLoading: isLoading && !effectiveData,
    error:
      !effectiveData && error
        ? error instanceof Error
          ? error
          : new Error('Failed to fetch student data')
        : null,
    // Add manual refetch function
    refetch: async () => {
      console.log('Manually refetching student data...')
      try {
        await refetch()
      } catch (error) {
        console.error('Error during manual refetch:', error)
      }
    },
  }
}
