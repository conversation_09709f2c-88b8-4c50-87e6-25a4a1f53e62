import { useState } from 'react'
import { useFlashcards } from './useFlashcards'
import { LegacyFlashcard, LegacyFlashcardDeck } from './useFlashcardAdapter'
import { useFlashcardImageUpload } from './useFlashcardImageUpload'

export const useFlashcardDeck = (
  deck: LegacyFlashcardDeck,
  onUpdate: (updatedDeck: LegacyFlashcardDeck) => void
) => {
  const [editingCard, setEditingCard] = useState<LegacyFlashcard | null>(null)
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [deckName, setDeckName] = useState(deck.name)
  const [deckVisibility, setDeckVisibility] = useState(deck.visibility)

  const { uploadImage, isUploading } = useFlashcardImageUpload()
  const { useCreateCard, useUpdateCard, useDeleteCard, useReorderCards } =
    useFlashcards()

  // Mutations
  const createCardMutation = useCreateCard()
  const updateCardMutation = useUpdateCard(editingCard?.id || '', deck.id)
  const deleteCardMutation = useDeleteCard(deck.id)
  const reorderCardsMutation = useReorderCards(deck.id)

  // Handle image upload
  const handleImageUpload = async (side: 'front' | 'back', file: File) => {
    if (!file || !editingCard) return

    try {
      const imageUrl = await uploadImage(file)

      if (imageUrl) {
        const updatedCard = {
          ...editingCard,
          [side]: {
            ...editingCard[side],
            image: imageUrl,
          },
        }
        setEditingCard(updatedCard)
        handleUpdateCard(updatedCard)
      }
    } catch (error) {
      console.error('Error uploading image:', error)
    }
  }

  // Add a new card
  const handleAddCard = () => {
    const newCard: LegacyFlashcard = {
      id: crypto.randomUUID(),
      front: { text: '' },
      back: { text: '' },
    }

    createCardMutation.mutate(
      {
        frontText: newCard.front.text,
        backText: newCard.back.text,
        deckId: deck.id,
      },
      {
        onSuccess: (apiCard) => {
          const updatedDeck = {
            ...deck,
            cards: [
              ...deck.cards,
              {
                id: apiCard.id,
                front: { text: apiCard.frontText, image: apiCard.frontImage },
                back: { text: apiCard.backText, image: apiCard.backImage },
              },
            ],
          }

          onUpdate(updatedDeck)
          setEditingCard(updatedDeck.cards[updatedDeck.cards.length - 1])
          setEditingIndex(updatedDeck.cards.length - 1)
        },
      }
    )
  }

  // Delete a card
  const handleDeleteCard = (index: number) => {
    const cardId = deck.cards[index].id

    deleteCardMutation.mutate(cardId, {
      onSuccess: () => {
        const updatedCards = [...deck.cards]
        updatedCards.splice(index, 1)

        const updatedDeck = {
          ...deck,
          cards: updatedCards,
        }

        onUpdate(updatedDeck)

        if (editingIndex === index) {
          setEditingCard(null)
          setEditingIndex(null)
        }
      },
    })
  }

  // Update a card
  const handleUpdateCard = (cardToUpdate = editingCard) => {
    if (cardToUpdate && editingIndex !== null) {
      updateCardMutation.mutate(
        {
          frontText: cardToUpdate.front.text,
          frontImage: cardToUpdate.front.image,
          backText: cardToUpdate.back.text,
          backImage: cardToUpdate.back.image,
        },
        {
          onSuccess: () => {
            const updatedCards = [...deck.cards]
            updatedCards[editingIndex] = cardToUpdate

            const updatedDeck = {
              ...deck,
              cards: updatedCards,
            }

            onUpdate(updatedDeck)
          },
        }
      )
    }
  }

  // Move card (for drag and drop)
  const moveCard = (dragIndex: number, hoverIndex: number) => {
    const dragCard = deck.cards[dragIndex]
    const updatedCards = [...deck.cards]
    updatedCards.splice(dragIndex, 1)
    updatedCards.splice(hoverIndex, 0, dragCard)

    const updatedDeck = {
      ...deck,
      cards: updatedCards,
    }

    onUpdate(updatedDeck)

    // Update editing index if we're currently editing a card that was moved
    if (editingIndex === dragIndex) {
      setEditingIndex(hoverIndex)
    }

    // Prepare reorder data
    const reorderData = {
      cards: updatedCards.map((card, index) => ({
        id: card.id,
        order: index,
      })),
    }

    // Call reorder API
    reorderCardsMutation.mutate(reorderData)
  }

  // Update deck name and visibility
  const handleUpdateDeckInfo = () => {
    const updatedDeck = {
      ...deck,
      name: deckName,
      visibility: deckVisibility,
    }

    onUpdate(updatedDeck)
  }

  // Remove image from card
  const handleRemoveImage = (side: 'front' | 'back') => {
    if (editingCard) {
      const updatedCard = {
        ...editingCard,
        [side]: {
          ...editingCard[side],
          image: undefined,
        },
      }
      setEditingCard(updatedCard)
      handleUpdateCard(updatedCard)
    }
  }

  return {
    editingCard,
    setEditingCard,
    editingIndex,
    setEditingIndex,
    deckName,
    setDeckName,
    deckVisibility,
    setDeckVisibility,
    isUploading,
    handleImageUpload,
    handleAddCard,
    handleDeleteCard,
    handleUpdateCard,
    moveCard,
    handleUpdateDeckInfo,
    handleRemoveImage,
  }
}
