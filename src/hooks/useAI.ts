import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import {
  aiService,
  ProcessAIRequest,
  ChatbotRequest,
  TranscribeRequest,
} from '@/services/aiService'

// Query keys for AI service
export const AI_QUERY_KEYS = {
  healthCheck: ['ai', 'health'] as const,
  chatSessions: (userId: string) => ['ai', 'chat', 'sessions', userId] as const,
}

// Health check hook
export const useAIHealthCheck = () => {
  return useQuery({
    queryKey: AI_QUERY_KEYS.healthCheck,
    queryFn: aiService.healthCheck,
    staleTime: 30000, // 30 seconds
    retry: 2,
    refetchOnWindowFocus: false,
  })
}

// Process YouTube content hook
export const useProcessYouTubeContent = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ProcessAIRequest) => aiService.processYouTubeContent(data),
    onSuccess: () => {
      toast.success('Content processed successfully!')
      // Invalidate related queries if needed
      queryClient.invalidateQueries({ queryKey: ['ai'] })
    },
    onError: (error: Error) => {
      console.error('YouTube processing error:', error)
      toast.error(error.message || 'Failed to process YouTube content')
    },
  })
}

// Process file hook
export const useProcessFile = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ file, customPrompt, userId }: { 
      file: File
      customPrompt?: string
      userId?: string 
    }) => aiService.processFile(file, customPrompt, userId),
    onSuccess: () => {
      toast.success('File processed successfully!')
      queryClient.invalidateQueries({ queryKey: ['ai'] })
    },
    onError: (error: Error) => {
      console.error('File processing error:', error)
      toast.error(error.message || 'Failed to process file')
    },
  })
}

// Chat message hook
export const useSendChatMessage = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ChatbotRequest) => aiService.sendChatMessage(data),
    onSuccess: (_, variables) => {
      // Update chat sessions cache if needed
      queryClient.invalidateQueries({ 
        queryKey: AI_QUERY_KEYS.chatSessions(variables.user_id)
      })
    },
    onError: (error: Error) => {
      console.error('Chat message error:', error)
      toast.error(error.message || 'Failed to send message')
    },
  })
}
// Transcribe with notes hook
export const useTranscribeWithNotes = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: TranscribeRequest) => aiService.transcribeWithNotes(data),
    onSuccess: () => {
      toast.success('Video transcribed successfully!')
      queryClient.invalidateQueries({ queryKey: ['ai'] })
    },
    onError: (error: Error) => {
      console.error('Transcription error:', error)
      toast.error(error.message || 'Failed to transcribe video')
    },
  })
}

// Basic transcribe hook
export const useTranscribe = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: TranscribeRequest) => aiService.transcribe(data),
    onSuccess: () => {
      toast.success('Video transcribed successfully!')
      queryClient.invalidateQueries({ queryKey: ['ai'] })
    },
    onError: (error: Error) => {
      console.error('Transcription error:', error)
      toast.error(error.message || 'Failed to transcribe video')
    },
  })
}

// Get chat sessions hook
export const useChatSessions = (userId: string) => {
  return useQuery({
    queryKey: AI_QUERY_KEYS.chatSessions(userId),
    queryFn: () => aiService.getChatSessions(userId),
    enabled: !!userId,
    staleTime: 60000, // 1 minute
    refetchOnWindowFocus: false,
  })
}

// Delete chat session hook
export const useDeleteChatSession = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (sessionId: string) => aiService.deleteChatSession(sessionId),
    onSuccess: () => {
      toast.success('Chat session deleted')
      // Invalidate all chat sessions queries
      queryClient.invalidateQueries({ queryKey: ['ai', 'chat', 'sessions'] })
    },
    onError: (error: Error) => {
      console.error('Delete session error:', error)
      toast.error(error.message || 'Failed to delete session')
    },
  })
}

// Custom hook for managing AI processing state
export const useAIProcessing = () => {
  const processYouTube = useProcessYouTubeContent()
  const processFile = useProcessFile()
  const transcribeWithNotes = useTranscribeWithNotes()

  const isProcessing = processYouTube.isPending || processFile.isPending || transcribeWithNotes.isPending

  return {
    processYouTube,
    processFile,
    transcribeWithNotes,
    isProcessing,
  }
}

export default {
  useAIHealthCheck,
  useProcessYouTubeContent,
  useProcessFile,
  useSendChatMessage,
  useTranscribeWithNotes,
  useTranscribe,
  useChatSessions,
  useDeleteChatSession,
  useAIProcessing,
}
