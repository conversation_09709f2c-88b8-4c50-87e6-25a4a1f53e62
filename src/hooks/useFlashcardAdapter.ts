import {
  Flashcard as Api<PERSON>lashcard,
  FlashcardDeck as ApiFlashcardDeck,
} from '@/services/flashcardService'

// Legacy types from the localStorage implementation
export interface LegacyFlashcard {
  id: string
  front: {
    text: string
    image?: string
  }
  back: {
    text: string
    image?: string
  }
}

export interface LegacyFlashcardDeck {
  id: string
  name: string
  visibility: 'private' | 'public'
  createdAt: string
  updatedAt: string
  cards: LegacyFlashcard[]
}

/**
 * Adapter to convert between the legacy flashcard format and the API format
 */
export const useFlashcardAdapter = () => {
  // Convert API flashcard to legacy format
  const apiToLegacyCard = (apiCard: ApiFlashcard): LegacyFlashcard => {
    return {
      id: apiCard.id,
      front: {
        text: apiCard.frontText,
        image: apiCard.frontImage,
      },
      back: {
        text: apiCard.backText,
        image: apiCard.backImage,
      },
    }
  }

  // Convert legacy flashcard to API format
  const legacyToApiCard = (
    legacyCard: LegacyFlashcard,
    deckId: string,
    order: number
  ): ApiFlashcard => {
    return {
      id: legacyCard.id,
      frontText: legacyCard.front.text,
      frontImage: legacyCard.front.image,
      backText: legacyCard.back.text,
      backImage: legacyCard.back.image,
      order,
      deckId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  }

  // Convert API deck to legacy format
  const apiToLegacyDeck = (apiDeck: ApiFlashcardDeck): LegacyFlashcardDeck => {
    return {
      id: apiDeck.id,
      name: apiDeck.name,
      visibility: apiDeck.visibility,
      createdAt: apiDeck.createdAt,
      updatedAt: apiDeck.updatedAt,
      cards: Array.isArray(apiDeck.cards)
        ? apiDeck.cards.map(apiToLegacyCard)
        : [],
    }
  }

  // Convert legacy deck to API format
  const legacyToApiDeck = (
    legacyDeck: LegacyFlashcardDeck
  ): ApiFlashcardDeck => {
    return {
      id: legacyDeck.id,
      name: legacyDeck.name,
      visibility: legacyDeck.visibility,
      ownerId: '', // This will be set by the server
      cardCount: legacyDeck.cards.length,
      createdAt: legacyDeck.createdAt,
      updatedAt: legacyDeck.updatedAt,
      cards: legacyDeck.cards.map((card, index) =>
        legacyToApiCard(card, legacyDeck.id, index)
      ),
    }
  }

  // Ensure a deck always has a cards array
  const ensureCardsArray = (
    deck: ApiFlashcardDeck | LegacyFlashcardDeck
  ): ApiFlashcardDeck | LegacyFlashcardDeck => {
    return {
      ...deck,
      cards: (deck as any).cards || [],
    }
  }

  return {
    apiToLegacyCard,
    legacyToApiCard,
    apiToLegacyDeck,
    legacyToApiDeck,
    ensureCardsArray,
  }
}
