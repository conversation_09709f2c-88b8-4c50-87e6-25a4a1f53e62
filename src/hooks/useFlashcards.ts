import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  flashcardService,
  CreateDeckDto,
  UpdateDeckDto,
  CreateCardDto,
  UpdateCardDto,
  ReorderCardsDto,
} from '@/services/flashcardService'
import { useToast } from '@/components/ui/use-toast'

// Helper to get token from localStorage with better error handling
const getToken = () => {
  const token = localStorage.getItem('access_token')
  if (!token) {
    console.error('No access_token found in localStorage for flashcard API')
    return ''
  }
  console.log('Using token for flashcard API:', token.substring(0, 10) + '...')
  return token
}

export const useFlashcards = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Get all decks
  const useDecks = () => {
    return useQuery({
      queryKey: ['flashcards', 'decks'],
      queryFn: async () => {
        try {
          // Check if token exists
          const token = getToken()
          if (!token) {
            throw new Error('No authentication token available. Please log in again.')
          }

          // Attempt to fetch decks
          const decks = await flashcardService.getDecks(token)
          return decks
        } catch (error: any) {
          console.error('Error in useDecks query function:', error)

          // Enhance error message for better debugging
          if (error.response?.status === 401) {
            throw new Error('401: Authentication failed. Your session may have expired.')
          } else if (error.response?.status === 400) {
            throw new Error('400: Bad Request. The server rejected the request.')
          }

          // Rethrow the original error with more context
          throw error
        }
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: 1, // Only retry once to avoid excessive failed requests
    })
  }

  // Get public decks
  const usePublicDecks = () => {
    return useQuery({
      queryKey: ['flashcards', 'public-decks'],
      queryFn: () => flashcardService.getPublicDecks(getToken()),
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Get deck by ID
  const useDeck = (deckId: string) => {
    return useQuery({
      queryKey: ['flashcards', 'deck', deckId],
      queryFn: async () => {
        try {
          // Get the deck details
          const deck = await flashcardService.getDeckById(deckId, getToken())

          // Get all cards for this deck using the dedicated endpoint
          const cards = await flashcardService.getDeckCards(deckId, getToken())

          // Combine the data
          return {
            ...deck,
            cards: cards,
          }
        } catch (error) {
          console.error('Error fetching deck with cards:', error)
          throw error
        }
      },
      enabled: !!deckId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
    })
  }

  // Create deck
  const useCreateDeck = () => {
    return useMutation({
      mutationFn: (deckData: CreateDeckDto) =>
        flashcardService.createDeck(deckData, getToken()),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
        // Removed toast notification
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to create deck',
          variant: 'destructive',
        })
      },
    })
  }

  // Update deck
  const useUpdateDeck = (deckId: string) => {
    return useMutation({
      mutationFn: (deckData: UpdateDeckDto) =>
        flashcardService.updateDeck(deckId, deckData, getToken()),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'deck', deckId],
        })
        // Removed toast notification
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to update deck',
          variant: 'destructive',
        })
      },
    })
  }

  // Delete deck
  const useDeleteDeck = () => {
    return useMutation({
      mutationFn: (deckId: string) =>
        flashcardService.deleteDeck(deckId, getToken()),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
        // Removed toast notification
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to delete deck',
          variant: 'destructive',
        })
      },
    })
  }

  // Reorder cards
  const useReorderCards = (deckId: string) => {
    return useMutation({
      mutationFn: (reorderData: ReorderCardsDto) =>
        flashcardService.reorderCards(deckId, reorderData, getToken()),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'deck', deckId],
        })
        toast({
          title: 'Success',
          description: 'Cards reordered successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description:
            error.response?.data?.message || 'Failed to reorder cards',
          variant: 'destructive',
        })
      },
    })
  }

  // Create card
  const useCreateCard = () => {
    return useMutation({
      mutationFn: (cardData: CreateCardDto) => {
        // Clean up the card data to ensure it's valid
        const cleanedCardData: CreateCardDto = {
          ...cardData,
          // Ensure frontImage and backImage are properly formatted
          frontImage: cardData.frontImage
            ? typeof cardData.frontImage === 'string' &&
              cardData.frontImage.includes('/health/proxy/image')
              ? // Extract the original URL from the proxy URL
                decodeURIComponent(cardData.frontImage.split('url=')[1])
              : cardData.frontImage
            : undefined,
          backImage: cardData.backImage
            ? typeof cardData.backImage === 'string' &&
              cardData.backImage.includes('/health/proxy/image')
              ? // Extract the original URL from the proxy URL
                decodeURIComponent(cardData.backImage.split('url=')[1])
              : cardData.backImage
            : undefined,
        }

        console.log('Creating card with cleaned data:', cleanedCardData)
        return flashcardService.createCard(cleanedCardData, getToken())
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'deck', data.deckId],
        })
        queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
        // Removed toast notification
      },
      onError: (error: any) => {
        console.error('Error creating card:', error.response?.data || error)
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to create card',
          variant: 'destructive',
        })
      },
    })
  }

  // Update card
  const useUpdateCard = (cardId: string, deckId: string) => {
    return useMutation({
      mutationFn: async (cardData: UpdateCardDto) => {
        try {
          // Log the update attempt for debugging
          console.log(
            `Attempting to update card ${cardId} in deck ${deckId} with data:`,
            cardData
          )

          // Clean up the card data to ensure it's valid
          const cleanedCardData: UpdateCardDto = {
            ...cardData,
            // Ensure frontImage and backImage are properly formatted
            frontImage: cardData.frontImage
              ? typeof cardData.frontImage === 'string' &&
                cardData.frontImage.includes('/health/proxy/image')
                ? // Extract the original URL from the proxy URL
                  decodeURIComponent(cardData.frontImage.split('url=')[1])
                : cardData.frontImage
              : undefined,
            backImage: cardData.backImage
              ? typeof cardData.backImage === 'string' &&
                cardData.backImage.includes('/health/proxy/image')
                ? // Extract the original URL from the proxy URL
                  decodeURIComponent(cardData.backImage.split('url=')[1])
                : cardData.backImage
              : undefined,
          }

          console.log('Cleaned card data for API:', cleanedCardData)

          // If the card ID is missing, empty, or starts with "temp-", it's a temporary card
          // BUT only create it if forceCreate is true (explicitly saving)
          if (
            (!cardId || cardId === '' || cardId.startsWith('temp-')) &&
            cleanedCardData.forceCreate === true
          ) {
            console.log('Creating new card from temporary card')
            // Create a new card instead of updating
            // Remove the forceCreate flag before sending to API
            const { forceCreate, ...dataToSend } = cleanedCardData
            return await flashcardService.createCard(
              {
                ...dataToSend,
                deckId: deckId,
              } as CreateCardDto,
              getToken()
            )
          } else if (!cardId || cardId === '' || cardId.startsWith('temp-')) {
            console.log(
              'Skipping API call for temporary card - not explicitly saving'
            )
            // Return a mock response for temporary cards when not explicitly saving
            return {
              id: cardId || `temp-${Date.now()}`,
              ...cleanedCardData,
              deckId: deckId,
            }
          }

          // Otherwise, proceed with normal update
          console.log(`Sending update request for card ID: ${cardId}`)
          return await flashcardService.updateCard(
            cardId,
            cleanedCardData,
            getToken()
          )
        } catch (error: any) {
          console.error('Error updating card:', error)

          // If card not found, try to create it instead
          if (
            error.response?.status === 404 ||
            error.message?.includes('not found')
          ) {
            console.log('Card not found, creating new card instead')

            // Clean up the card data to ensure it's valid
            const cleanedCardData = {
              ...cardData,
              // Ensure frontImage and backImage are properly formatted
              frontImage: cardData.frontImage
                ? typeof cardData.frontImage === 'string' &&
                  cardData.frontImage.includes('/health/proxy/image')
                  ? // Extract the original URL from the proxy URL
                    decodeURIComponent(cardData.frontImage.split('url=')[1])
                  : cardData.frontImage
                : undefined,
              backImage: cardData.backImage
                ? typeof cardData.backImage === 'string' &&
                  cardData.backImage.includes('/health/proxy/image')
                  ? // Extract the original URL from the proxy URL
                    decodeURIComponent(cardData.backImage.split('url=')[1])
                  : cardData.backImage
                : undefined,
            }

            return await flashcardService.createCard(
              {
                ...cleanedCardData,
                deckId: deckId,
              } as CreateCardDto,
              getToken()
            )
          }

          throw error
        }
      },
      onSuccess: (data) => {
        console.log('Card update/create successful:', data)
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'deck', deckId],
        })
        // Also invalidate the decks query to update the left panel
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'decks'],
        })
      },
      onError: (error: any) => {
        console.error('Card update error:', error)
      },
    })
  }

  // Delete card
  const useDeleteCard = (deckId: string) => {
    return useMutation({
      mutationFn: (cardId: string) =>
        flashcardService.deleteCard(cardId, getToken()),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ['flashcards', 'deck', deckId],
        })
        queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
        toast({
          title: 'Success',
          description: 'Card deleted successfully',
        })
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to delete card',
          variant: 'destructive',
        })
      },
    })
  }

  // Migrate from localStorage
  const useMigrateFromLocalStorage = () => {
    return useMutation({
      mutationFn: () => flashcardService.migrateFromLocalStorage(getToken()),
      onSuccess: (success) => {
        if (success) {
          queryClient.invalidateQueries({ queryKey: ['flashcards', 'decks'] })
          // Removed toast notification
        }
      },
      onError: () => {
        toast({
          title: 'Error',
          description: 'Failed to migrate flashcards from local storage',
          variant: 'destructive',
        })
      },
    })
  }

  return {
    useDecks,
    usePublicDecks,
    useDeck,
    useCreateDeck,
    useUpdateDeck,
    useDeleteDeck,
    useReorderCards,
    useCreateCard,
    useUpdateCard,
    useDeleteCard,
    useMigrateFromLocalStorage,
  }
}
