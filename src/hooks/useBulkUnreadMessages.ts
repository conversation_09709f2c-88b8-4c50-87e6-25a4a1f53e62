import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

// Get API URL from environment variables
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'

/**
 * Custom hook to fetch and track unread message counts for multiple users at once
 * @param userIds Array of user IDs to check unread messages for
 * @returns Object containing unread message counts mapped by user ID
 */
export function useBulkUnreadMessages(userIds: string[]) {
  const [unreadCounts, setUnreadCounts] = useState<Record<string, number>>({})

  // Get auth token from localStorage
  const token = localStorage.getItem('access_token')
  const currentUserId = localStorage.getItem('id')

  // Fetch unread message counts for all users in a single query
  const { data, isLoading, error } = useQuery({
    queryKey: ['bulkUnreadMessages', userIds.join(',')],
    queryFn: async () => {
      if (!token || !currentUserId || userIds.length === 0) {
        return { counts: {} }
      }

      try {
        // Make a single request to get unread counts for all users
        const response = await axios.post(
          `${API_URL}/chat/conversations/unread/bulk`,
          { userIds },
          { headers: { Authorization: `Bearer ${token}` } }
        )
        return response.data
      } catch (error) {
        console.error('Error fetching bulk unread messages:', error)

        // Fallback: Create an empty counts object
        const emptyCounts: Record<string, number> = {}
        userIds.forEach((id) => {
          emptyCounts[id] = 0
        })

        return { counts: emptyCounts }
      }
    },
    // Refresh every 30 seconds
    refetchInterval: 30000,
    // Don't run if we don't have a token, current user ID, or user IDs
    enabled: !!token && !!currentUserId && userIds.length > 0,
  })

  // Update unread counts when data changes
  useEffect(() => {
    if (data?.counts) {
      setUnreadCounts(data.counts)
    } else {
      // If the API doesn't return the expected format, initialize with zeros
      const emptyCounts: Record<string, number> = {}
      userIds.forEach((id) => {
        emptyCounts[id] = 0
      })
      setUnreadCounts(emptyCounts)
    }
  }, [data, userIds])

  // Fallback to individual counts if bulk API fails
  useEffect(() => {
    if (error && token && currentUserId) {
      // Create an empty counts object
      const newCounts: Record<string, number> = { ...unreadCounts }
      let hasUpdates = false

      // Try to get counts from localStorage as fallback
      userIds.forEach((userId) => {
        const storedCount = localStorage.getItem(`unreadMessageCount_${userId}`)
        if (storedCount) {
          newCounts[userId] = parseInt(storedCount, 10)
          hasUpdates = true
        } else if (!newCounts[userId]) {
          newCounts[userId] = 0
          hasUpdates = true
        }
      })

      if (hasUpdates) {
        setUnreadCounts(newCounts)
      }
    }
  }, [error, token, currentUserId, userIds, unreadCounts])

  return {
    unreadCounts,
    isLoading,
    error,
  }
}
