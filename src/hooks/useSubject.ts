import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Subject } from '@/interface/types'
import { api } from '@/lib/axios'

const getAuthHeader = () => {
  const token = localStorage.getItem('access_token')
  if (!token) throw new Error('Access token not found')
  return { Authorization: `Bearer ${token}` }
}

/**
 * Custom hook for managing subject data
 * @returns Object containing subject data and mutation functions
 */
export default function useSubject() {
  const queryClient = useQueryClient()

  const {
    data: subjects = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      const response = await api.get<Subject[]>('/subject', {
        headers: getAuthHeader(),
      })
      return response.data
    },
  })

  const createSubject = useMutation({
    mutationFn: async (newSubject: Partial<Subject>) => {
      const response = await api.post<Subject>('/subject', newSubject, {
        headers: getAuthHeader(),
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
    },
  })

  const updateSubject = useMutation({
    mutationFn: async ({ id, ...data }: Partial<Subject> & { id: string }) => {
      const response = await api.patch<Subject>(`/subject/${id}`, data, {
        headers: getAuthHeader(),
      })
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
    },
  })

  const deleteSubject = useMutation({
    mutationFn: async (id: string) => {
      await api.delete(`/subject/${id}`, {
        headers: getAuthHeader(),
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
    },
  })

  const updateFormulas = useMutation({
    mutationFn: async (updatedSubjects: Subject[]) => {
      const response = await api.patch<Subject[]>(
        '/subject/formulas',
        updatedSubjects,
        {
          headers: getAuthHeader(),
        }
      )
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subjects'] })
    },
  })

  return {
    subjects,
    isLoading,
    isError,
    error,
    createSubject: createSubject.mutate,
    updateSubject: updateSubject.mutate,
    deleteSubject: deleteSubject.mutate,
    updateFormulas: updateFormulas.mutate,
    isCreating: createSubject.isPending,
    isUpdating: updateSubject.isPending,
    isDeleting: deleteSubject.isPending,
  }
}
