import { useState } from 'react'
import FileUploadService from '@/services/fileUploadService'

export const useFlashcardImageUpload = () => {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const uploadImage = async (file: File): Promise<string | null> => {
    setIsUploading(true)
    setError(null)

    try {
      const fileUploadService = new FileUploadService()
      const response = await fileUploadService.uploadFile(file)

      if (response.success && response.data) {
        const fileUrl = fileUploadService.getFileUrl(response)
        return fileUrl
      } else {
        throw new Error(response.message || 'Failed to upload image')
      }
    } catch (err: any) {
      console.error('Error uploading flashcard image:', err)
      setError(err.message || 'An error occurred during upload')
      return null
    } finally {
      setIsUploading(false)
    }
  }

  return {
    uploadImage,
    isUploading,
    error,
  }
}
