import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useToast } from '@/components/ui/use-toast'
import {
  teacherAttendanceService,
  TeacherSession,
  SessionAttendanceData,
} from '@/lib/api/services/teacher-attendance-service'
import { useSessionStatusUpdates } from './useWebSocket'

// Query keys for React Query
export const TEACHER_ATTENDANCE_KEYS = {
  all: ['teacher-attendance'] as const,
  sessions: ['teacher-attendance', 'sessions'] as const,
  sessionsByDate: (date: string) => ['teacher-attendance', 'sessions', 'by-date', date] as const,
  currentSession: ['teacher-attendance', 'sessions', 'current'] as const,
  sessionById: (sessionId: string) => ['teacher-attendance', 'sessions', sessionId] as const,
  sessionStudents: (sessionId: string) => ['teacher-attendance', 'sessions', sessionId, 'students'] as const,
  sessionAttendance: (sessionId: string) => ['teacher-attendance', 'sessions', sessionId, 'attendance'] as const,
  dashboardStats: ['teacher-attendance', 'dashboard', 'stats'] as const,
  attendanceHistory: (startDate?: string, endDate?: string) =>
    ['teacher-attendance', 'history', startDate, endDate] as const,
  reportsData: (timeRange: string, reportType: string, classFilter: string) =>
    ['teacher-attendance', 'reports', timeRange, reportType, classFilter] as const,
}

/**
 * Custom hook for teacher attendance management
 * Provides React Query hooks for all teacher attendance operations with real-time updates
 */
export function useTeacherAttendance() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Real-time session status updates
  const handleSessionStatusUpdate = useCallback((update: any) => {
    console.log('📊 Processing session status update:', update);

    // Update the specific session in the cache
    queryClient.setQueryData(
      TEACHER_ATTENDANCE_KEYS.sessionById(update.sessionId),
      (oldData: TeacherSession | undefined) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          status: update.newStatus,
        };
      }
    );

    // Update the sessions list
    queryClient.setQueryData(
      TEACHER_ATTENDANCE_KEYS.sessions,
      (oldData: TeacherSession[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(session =>
          session.id === update.sessionId
            ? { ...session, status: update.newStatus }
            : session
        );
      }
    );

    // Show toast notification for status changes
    if (update.oldStatus !== update.newStatus) {
      const statusMessages = {
        'PENDING': 'Scheduled',
        'ONGOING': 'In Progress',
        'NOT_REPORTED': 'Needs Attendance',
        'REPORTED': 'Completed',
        'CANCELED': 'Canceled'
      };

      toast({
        title: "Session Status Updated",
        description: `${update.sessionData.className} - ${update.sessionData.subjectName} is now ${statusMessages[update.newStatus as keyof typeof statusMessages] || update.newStatus}`,
      });
    }
  }, [queryClient, toast]);

  const handleBatchStatusUpdate = useCallback((updates: any[]) => {
    console.log('📊 Processing batch session status update:', updates.length, 'sessions');

    // Update multiple sessions at once
    updates.forEach(update => {
      // Update individual session cache
      queryClient.setQueryData(
        TEACHER_ATTENDANCE_KEYS.sessionById(update.sessionId),
        (oldData: TeacherSession | undefined) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            status: update.newStatus,
          };
        }
      );
    });

    // Update the sessions list with all changes
    queryClient.setQueryData(
      TEACHER_ATTENDANCE_KEYS.sessions,
      (oldData: TeacherSession[] | undefined) => {
        if (!oldData) return oldData;

        const updateMap = new Map(updates.map(update => [update.sessionId, update.newStatus]));

        return oldData.map(session => {
          const newStatus = updateMap.get(session.id);
          return newStatus ? { ...session, status: newStatus } : session;
        });
      }
    );

    // Show summary notification for batch updates
    const changedCount = updates.filter(update => update.oldStatus !== update.newStatus).length;
    if (changedCount > 0) {
      toast({
        title: "Sessions Updated",
        description: `${changedCount} session${changedCount > 1 ? 's' : ''} status updated automatically`,
      });
    }
  }, [queryClient, toast]);

  // Initialize WebSocket connection for real-time updates
  const webSocket = useSessionStatusUpdates(handleSessionStatusUpdate, handleBatchStatusUpdate);

  // Dashboard queries
  const useDashboardStats = () => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.dashboardStats,
      queryFn: teacherAttendanceService.getDashboardStats,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  // Session queries
  const useTeacherSessions = () => {
    console.log('🔥 useTeacherSessions hook called')
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.sessions,
      queryFn: () => {
        console.log('🔥 useTeacherSessions queryFn called - about to call teacherAttendanceService.getSessions')
        return teacherAttendanceService.getSessions()
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  const useSessionsByDate = (date: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.sessionsByDate(date),
      queryFn: () => teacherAttendanceService.getSessionsByDate(date),
      enabled: !!date,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  const useCurrentSession = () => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.currentSession,
      queryFn: teacherAttendanceService.getCurrentSession,
      staleTime: 1000 * 60 * 2, // 2 minutes (more frequent for current session)
      retry: 2,
    })
  }

  const useSessionById = (sessionId: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.sessionById(sessionId),
      queryFn: () => teacherAttendanceService.getSessionById(sessionId),
      enabled: !!sessionId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  const useSessionStudents = (sessionId: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.sessionStudents(sessionId),
      queryFn: () => teacherAttendanceService.getSessionStudents(sessionId),
      enabled: !!sessionId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  // Attendance queries
  const useSessionAttendance = (sessionId: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.sessionAttendance(sessionId),
      queryFn: () => teacherAttendanceService.getSessionAttendance(sessionId),
      enabled: !!sessionId,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  const useAttendanceHistory = (startDate?: string, endDate?: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.attendanceHistory(startDate, endDate),
      queryFn: () => teacherAttendanceService.getAttendanceHistory(startDate, endDate),
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    })
  }

  // Reports queries
  const useReportsData = (timeRange: string, reportType: string, classFilter: string) => {
    return useQuery({
      queryKey: TEACHER_ATTENDANCE_KEYS.reportsData(timeRange, reportType, classFilter),
      queryFn: () => teacherAttendanceService.getReportsData(timeRange, reportType),
      staleTime: 1000 * 60 * 10, // 10 minutes (reports can be cached longer)
      retry: 2,
    })
  }

  // Mutations
  const useSubmitAttendance = () => {
    return useMutation({
      mutationFn: (data: SessionAttendanceData) =>
        teacherAttendanceService.submitSessionAttendance(data),
      onSuccess: (variables) => {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.sessions })
        queryClient.invalidateQueries({
          queryKey: TEACHER_ATTENDANCE_KEYS.sessionAttendance(variables.sessionId)
        })
        queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.dashboardStats })
        queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.attendanceHistory() })

        toast({
          title: "Success",
          description: "Attendance submitted successfully.",
        })
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to submit attendance.",
          variant: "destructive",
        })
      }
    })
  }

  // Utility functions
  const invalidateAllQueries = () => {
    queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.all })
  }

  const invalidateSessionQueries = () => {
    queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.sessions })
  }

  const invalidateDashboardQueries = () => {
    queryClient.invalidateQueries({ queryKey: TEACHER_ATTENDANCE_KEYS.dashboardStats })
  }

  return {
    // Dashboard
    useDashboardStats,

    // Sessions
    useTeacherSessions,
    useSessionsByDate,
    useCurrentSession,
    useSessionById,
    useSessionStudents,

    // Attendance
    useSessionAttendance,
    useAttendanceHistory,

    // Reports
    useReportsData,

    // Mutations
    useSubmitAttendance,

    // Utilities
    invalidateAllQueries,
    invalidateSessionQueries,
    invalidateDashboardQueries,

    // Real-time connection status
    isWebSocketConnected: webSocket.isConnected,
    webSocketError: webSocket.connectionError,
  }
}
