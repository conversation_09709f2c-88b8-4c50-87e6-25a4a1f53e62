import { useMutation, useQueryClient } from '@tanstack/react-query'
import { userService } from '@/lib/api/services/user-service'
import { useToast } from '@/components/ui/use-toast'

interface UpdateProfileData {
  firstname?: string
  lastname?: string
  email?: string
  cin?: string
  birthday?: string
  gender?: 'MALE' | 'FEMALE'
  address?: string
  phone?: string
  avatar?: string
  password?: string
  userOnboarding?: boolean
}

export function useUpdateProfile() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (data: UpdateProfileData) => {
      // Try the new updateUserProfile endpoint first
      return userService.updateUserProfile(data)
        .catch(error => {
          console.log('Failed to use updateUserProfile endpoint, falling back to updateMe:', error);
          // Fall back to the old updateMe endpoint if the new one fails
          return userService.updateMe(data);
        });
    },
    onSuccess: () => {
      // Invalidate and refetch the user profile query
      queryClient.invalidateQueries({ queryKey: ['userProfile'] })
      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update profile',
        variant: 'destructive',
      })
    },
  })
}
