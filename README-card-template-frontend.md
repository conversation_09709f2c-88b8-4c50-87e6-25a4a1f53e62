# Card Template System Frontend Implementation

## Overview
The Card Template System allows establishments to create and manage custom card templates for their users (students and teachers). The system provides a way to generate profile cards with consistent styling and information.

## API Endpoints

### 1. Card Template Management
- **GET `/card-templates`**
  - Fetches all card templates for the current establishment
  - Returns an array of card template objects

- **GET `/card-templates/:id`**
  - Fetches a specific card template by ID
  - Returns card template object with configuration details

- **POST `/card-templates`**
  - Creates a new card template
  - Requires:
    - `name`: Template name
    - `config`: Template configuration object
    - `etablissementId`: Establishment ID

- **PUT `/card-templates/:id`**
  - Updates an existing card template
  - Requires same fields as POST

- **DELETE `/card-templates/:id`**
  - Deletes a card template by ID

### 2. Card Generation
- **GET `/users/:userId/card`**
  - Generates a profile card for a specific user
  - Returns HTML string of the card
  - Uses the establishment's card template configuration

## Template Configuration

### Card Template Object Structure
```typescript
interface CardTemplate {
  id: string; // UUID
  name: string; // Template name
  previewImageUrl?: string; // URL to preview image
  config: CardTemplateConfig;
  etablissementId: string; // UUID of the establishment
  etablissement: Etablissement;
}
```

### Card Template Configuration
```typescript
interface CardTemplateConfig {
  // Layout
  width?: number; // Card width in pixels (default: 300)
  height?: number; // Card height in pixels (default: 400)
  padding?: number; // Card padding in pixels (default: 20)

  // Colors
  backgroundColor?: string; // Card background color (default: #ffffff)
  textColor?: string; // Text color (default: #000000)
  borderColor?: string; // Border color (default: #ccc)

  // Typography
  fontFamily?: string; // Font family (default: Arial, sans-serif)
  fontSize?: number; // Base font size in pixels

  // Sections
  header?: {
    backgroundColor?: string;
    textColor?: string;
    padding?: number;
  };

  infoGroups?: {
    spacing?: number; // Vertical spacing between groups
    labelColor?: string;
  };
}
```

## Frontend Components

### 1. Template Management
- **TemplateList Component**
  - Displays list of available templates
  - Shows template preview
  - Allows editing and deletion

- **TemplateEditor Component**
  - Visual editor for template configuration
  - Preview pane showing changes in real-time
  - Form for template metadata

### 2. Card Generation
- **CardPreview Component**
  - Shows generated card preview
  - Allows downloading as PDF
  - Shows user information

### 3. Template Preview
- **TemplatePreview Component**
  - Visual preview of template layout
  - Shows how different user roles (teacher/student) look
  - Interactive preview with sample data

## User Interface Features

### Template Editor
- Visual drag-and-drop interface
- Real-time preview of changes
- Preset color schemes
- Template preview with sample data
- Export/import template configuration

### Card Generator
- Automatic user information fetching
- Customizable layout options
- Download options (PDF, PNG)
- Print-friendly format

## Error Handling
- Template validation
- User permission checks
- Error messages for invalid configurations
- Loading states and progress indicators

## Security Considerations
- Template access restricted to establishment
- User information access control
- Template deletion confirmation
- Rate limiting for card generation

## Implementation Notes
- Uses HTML Canvas for PDF generation
- Responsive design for different screen sizes
- Optimized for mobile devices
- Caching for frequently used templates
- Error boundary for template rendering
