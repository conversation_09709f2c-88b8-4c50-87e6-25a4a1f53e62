// This file should be a configuration for the i18next-parser, not an i18n initialization
module.exports = {
  contextSeparator: '_',
  // Key separator used in your translation keys
  keySeparator: '.',
  // Save the missing keys to translation files
  createOldCatalogs: true,
  // Default namespace used in your i18next config
  defaultNamespace: 'translation',
  // Default value to give to empty keys
  defaultValue: '',
  // Indentation of the catalog files
  indentation: 2,
  // Keep keys from the catalog that are not in code
  keepRemoved: false,
  // Locales directory path
  lexers: {
    js: ['JsxLexer'],
    jsx: ['JsxLexer'],
    ts: ['Jsx<PERSON>exer'],
    tsx: ['Jsx<PERSON>exer'],
    default: ['Jsx<PERSON>ex<PERSON>'],
  },
  // Array of supported locales
  locales: ['en', 'fr'],
  // Where to write the locale files relative to process.cwd()
  output: 'src/locales/$LOCALE/$NAMESPACE.json',
  // An array of globs that describe where to look for source files
  input: [
    'src/**/*.{js,jsx,ts,tsx}',
  ],
  // Sort keys
  sort: true,
  // Display info about the parsing including some stats
  verbose: true,
  // How to handle missing keys
  failOnWarnings: false,
  // Fail if trying to use a key that doesn't exist
  failOnUpdate: false,
  // Namespace separator used in your translation keys
  namespaceSeparator: ':',
  // Plural separator used in your translation keys
  pluralSeparator: '_',
  // Add prefix for translation keys
  prefix: '',
}
