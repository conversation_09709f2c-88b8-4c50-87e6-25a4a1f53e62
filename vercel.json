{"version": 2, "rewrites": [{"source": "/api/:path*", "destination": "https://api.jeridschool./:path*"}, {"source": "/cdn-proxy/:path*", "destination": "https://cdn.jeridschool.tech/:path*"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Cache-Control", "value": "s-maxage=3600, stale-while-revalidate=60"}]}]}