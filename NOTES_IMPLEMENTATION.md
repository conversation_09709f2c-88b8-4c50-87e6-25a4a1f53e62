# Notes Implementation Guide

## Overview
This document explains how the notes system works in our web application, to help mobile developers implement a similar system in Expo.

## Core Features
- Collections of notes
- Individual notes with rich text content
- CRUD operations (Create, Read, Update, Delete)
- Public/Private visibility settings
- Topic categorization

## Data Structure

### Collection
```typescript
interface Collection {
  id: string;
  name: string;
  visibility: 'public' | 'private';
  topic?: string;
  notes: Note[];
  createdAt: string;
}
```

### Note
```typescript
interface Note {
  id: string;
  title: string;
  content: string; // Rich text content
  collectionId: string;
  createdAt: string;
  updatedAt: string;
}
```

## Key Features Implementation

### 1. Collections List
- Display all collections
- Show collection name, visibility status, and note count
- Allow creating new collections
- Support public/private visibility toggle

### 2. Notes Collection View
- Display all notes within a collection
- Grid layout (3 columns on desktop, 1 column on mobile)
- Each note card shows:
  - Title
  - Content preview (truncated)
  - Creation/update dates
  - Action buttons (View, Edit, Delete)

### 3. Note Operations
- **Create**: Form with title and rich text editor
- **View**: Full-screen view of note content
- **Edit**: Same form as create, pre-filled with note data
- **Delete**: Confirmation dialog before deletion

### 4. Rich Text Editor
- We use TipTap for rich text editing
- Supports basic formatting (bold, italic, lists, etc.)
- Content is stored as HTML/JSON

## API Endpoints Needed

```typescript
// Collections
GET /api/collections - List all collections
POST /api/collections - Create new collection
GET /api/collections/:id - Get single collection
PUT /api/collections/:id - Update collection
DELETE /api/collections/:id - Delete collection

// Notes
GET /api/collections/:collectionId/notes - List notes in collection
POST /api/collections/:collectionId/notes - Create new note
GET /api/notes/:id - Get single note
PUT /api/notes/:id - Update note
DELETE /api/notes/:id - Delete note
```

## API Request/Response Formats

### Collections

#### 1. Create Collection
```typescript
// Request
POST /api/collections
{
  "name": string;
  "visibility": "public" | "private";
  "topic": string; // optional
}

// Response (201 Created)
{
  "id": string;
  "name": string;
  "visibility": "public" | "private";
  "topic": string | null;
  "notes": [];
  "createdAt": string; // ISO date string
}
```

#### 2. Get Collections List
```typescript
// Request
GET /api/collections

// Response (200 OK)
{
  "collections": [
    {
      "id": string;
      "name": string;
      "visibility": "public" | "private";
      "topic": string | null;
      "notesCount": number;
      "createdAt": string;
    }
  ]
}
```

#### 3. Get Single Collection
```typescript
// Request
GET /api/collections/:id

// Response (200 OK)
{
  "id": string;
  "name": string;
  "visibility": "public" | "private";
  "topic": string | null;
  "notes": Note[];
  "createdAt": string;
}
```

### Notes

#### 1. Create Note
```typescript
// Request
POST /api/collections/:collectionId/notes
{
  "title": string;
  "content": string; // HTML/JSON content from rich text editor
}

// Response (201 Created)
{
  "id": string;
  "title": string;
  "content": string;
  "collectionId": string;
  "createdAt": string;
  "updatedAt": string;
}
```

#### 2. Get Notes in Collection
```typescript
// Request
GET /api/collections/:collectionId/notes

// Response (200 OK)
{
  "notes": [
    {
      "id": string;
      "title": string;
      "content": string;
      "collectionId": string;
      "createdAt": string;
      "updatedAt": string;
    }
  ]
}
```

#### 3. Update Note
```typescript
// Request
PUT /api/notes/:id
{
  "title": string;
  "content": string;
}

// Response (200 OK)
{
  "id": string;
  "title": string;
  "content": string;
  "collectionId": string;
  "createdAt": string;
  "updatedAt": string;
}
```

### Error Responses
All endpoints return errors in this format:
```typescript
// Response (4xx/5xx)
{
  "error": {
    "code": string; // e.g., "NOT_FOUND", "UNAUTHORIZED"
    "message": string; // Human-readable error message
  }
}
```

Common error codes:
- `400`: Bad Request (invalid input)
- `401`: Unauthorized (not logged in)
- `403`: Forbidden (no permission)
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error

### Authentication
- Include JWT token in Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Rate Limiting
- 100 requests per minute per user
- Response headers include:
  - `X-RateLimit-Limit`: Maximum requests per minute
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Time until limit resets

## UI Components Needed

1. **Collection Card**
   - Title
   - Visibility badge
   - Note count
   - Creation date

2. **Note Card**
   - Title
   - Content preview
   - Action buttons
   - Timestamps

3. **Note Form**
   - Title input
   - Rich text editor
   - Save/Cancel buttons

4. **Note Viewer**
   - Full-screen view
   - Back button
   - Edit/Delete options

## Mobile-Specific Considerations

1. **Layout**
   - Use single column layout for notes
   - Implement pull-to-refresh
   - Add bottom sheet for actions

2. **Navigation**
   - Stack navigation for collection → notes → note view
   - Modal for note creation/editing

3. **Performance**
   - Implement pagination for notes list
   - Cache collection data
   - Optimize rich text rendering

## State Management
- Use React Query or similar for data fetching
- Implement optimistic updates for better UX
- Handle offline capabilities

## Security
- Implement proper authentication
- Check permissions for private collections
- Validate user input

## Testing
- Test CRUD operations
- Verify rich text editing
- Check offline functionality
- Test permissions and access control

## Next Steps
1. Set up basic navigation structure
2. Implement collection list view
3. Add note creation/editing
4. Integrate rich text editor
5. Add authentication
6. Implement offline support 