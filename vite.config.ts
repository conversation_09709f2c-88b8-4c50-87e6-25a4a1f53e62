import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'

export default defineConfig({
  plugins: [react(), TanStackRouterVite()],
  server: {
    host: true,
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/cdn-proxy': {
        target: 'https://cdn.jeridschool.tech', // Changed to your CDN subdomain
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/cdn-proxy/, ''),
      },
    },
    hmr: {
      host: 'localhost',
      clientPort: 5173,
    },
    cors: true,
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true, // Recommended for production debugging
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  define: {
    'process.env': {},
  },
  assetsInclude: ["**/*.glb"],

})
