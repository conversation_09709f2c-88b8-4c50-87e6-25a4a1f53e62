# Image Handling and QR Codes Documentation

This document explains how images and QR codes are handled in the JeridSchool application.

## Image Handling

### CDN Image Loading

The application uses two main components for loading images from the CDN:

1. **CdnImage Component** (`src/components/shared/CdnImage.tsx`)

   - Handles URL transformation for CDN images
   - Provides fallback images when loading fails
   - Logs detailed information about image loading

2. **CorsProxyImage Component** (`src/components/shared/CorsProxyImage.tsx`)
   - Wrapper around CdnImage that provides consistent fallback behavior
   - Used in the ProfileCard component

### How Images Are Loaded

1. When a user uploads an image (avatar, etc.), it's sent to `https://cdn.jeridschool.tech/upload`
2. The CDN server returns a URL like `https://cdn.jeridschool.tech/cdn/files/{file-id}`
3. The `CdnImage` component handles loading this URL and provides fallbacks if needed

### CORS Issues

CORS (Cross-Origin Resource Sharing) issues occur when the browser blocks loading resources from a different domain. To fix these issues:

#### Backend Solution (Recommended)

Add the following headers to the CDN server responses:

```
Access-Control-Allow-Origin: https://jeridschool.tech
Access-Control-Allow-Methods: GET, OPTIONS
Access-Control-Allow-Headers: Content-Type
```

#### Frontend Workaround

The frontend currently uses the `CdnImage` component which:

1. Attempts to load the image directly
2. Falls back to a default avatar if loading fails
3. Uses proper error handling to ensure the UI doesn't break

## QR Codes

### QR Code Implementation

The application uses two components for QR codes:

1. **QRCodeSVG** from `qrcode.react` (used directly in ProfileCard)
2. **QRCodeGenerator** (`src/components/shared/QRCodeGenerator.tsx`) for more advanced use cases

### QR Code URLs

QR codes in the ProfileCard now point to:

```
https://jeridschool.tech/public/card/{userId}
```

### Frontend Requirements

The frontend should implement a public route at `/public/card/:id` that:

1. Is accessible without authentication
2. Displays a simplified version of the user's ID card
3. Fetches minimal user data from a public API endpoint

### Backend Requirements

The backend should implement a public endpoint at `/public/users/{userId}` that:

1. Accepts GET requests
2. Returns only basic, non-sensitive user information (name, role, establishment, photo)
3. Does not require authentication
4. Includes verification information like expiration date

This approach keeps sensitive user data protected while still allowing public verification of ID cards.

## Implementation Notes

1. The ProfileCard component uses the CorsProxyImage component for avatar display
2. The establishment logo is displayed in the top-left corner of the card
3. The QR code in the ProfileCard points to the API endpoint for public card viewing
4. All image loading has proper error handling to prevent UI breakage

### Card Header Structure

The card header contains:

1. **Establishment Logo**: Small circular logo in the top-left (falls back to establishment initials)
2. **Establishment Name**: Displayed next to the logo
3. **QR Code**: In the top-right, links to the public card endpoint

## Implementation Plan

### Frontend Tasks

1. Create a new public route at `/public/card/:id` that:

   - Doesn't require authentication
   - Displays a simplified version of the user's ID card
   - Uses a dedicated API endpoint for public user data

2. Implement a simplified card component for public viewing that:
   - Shows basic user information
   - Includes verification details
   - Has a "Verify" button to check authenticity

### Backend Tasks

1. Implement a public endpoint at `/public/users/{userId}` that:

   - Returns only non-sensitive user information
   - Doesn't require authentication
   - Includes verification data

2. Add proper CORS headers to the CDN server:

   ```
   Access-Control-Allow-Origin: https://jeridschool.tech
   Access-Control-Allow-Methods: GET, OPTIONS
   Access-Control-Allow-Headers: Content-Type
   ```

3. Consider implementing a proxy endpoint at `/proxy` that can fetch CDN resources server-side

### Security Considerations

1. The public endpoint should only expose non-sensitive information
2. Consider rate limiting to prevent scraping
3. Add a verification mechanism to validate card authenticity
