# CDN Proxy Endpoint Implementation Guide

This document provides implementation details for a proxy endpoint that can be used to fetch CDN resources server-side, avoiding CORS issues.

## Problem

The frontend is experiencing CORS errors when trying to load images from the CDN:

```
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at
https://cdn.jeridschool.tech/cdn/files/a78ef57a-e8ff-4e0b-b20d-47b6dad9ec4b.
(Reason: CORS header 'Access-Control-Allow-Origin' missing). Status code: 200.
```

## Solution Options

### Option 1: Add CORS Headers to CDN Server (Recommended)

The best solution is to add the following headers to the CDN server's responses:

```
Access-Control-Allow-Origin: https://jeridschool.tech
Access-Control-Allow-Methods: GET, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

This can be done in the Nginx or Apache configuration:

#### Nginx Example:

```nginx
location /cdn/ {
    # Existing configuration...

    # Add CORS headers
    add_header 'Access-Control-Allow-Origin' 'https://jeridschool.tech' always;
    add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
    add_header 'Access-Control-Max-Age' '86400' always;

    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' 'https://jeridschool.tech' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
        add_header 'Access-Control-Max-Age' '86400' always;
        add_header 'Content-Type' 'text/plain charset=UTF-8';
        add_header 'Content-Length' '0';
        return 204;
    }
}
```

### Option 2: Implement a Proxy Endpoint

If you can't modify the CDN server directly, implement a proxy endpoint in your API server:

#### Express.js Implementation:

```javascript
const express = require('express')
const axios = require('axios')
const router = express.Router()

/**
 * Proxy endpoint for CDN resources
 * GET /proxy?url=https://cdn.jeridschool.tech/cdn/files/123456
 */
router.get('/proxy', async (req, res) => {
  try {
    const { url } = req.query

    // Validate the URL to ensure it only points to your CDN
    if (!url || typeof url !== 'string') {
      return res.status(400).json({ error: 'URL parameter is required' })
    }

    // Only allow proxying from your CDN domain
    if (!url.startsWith('https://cdn.jeridschool.tech/')) {
      return res.status(403).json({ error: 'Only CDN URLs are allowed' })
    }

    // Fetch the resource
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
    })

    // Set appropriate headers
    res.set('Content-Type', response.headers['content-type'])
    res.set('Cache-Control', 'public, max-age=86400')

    // Send the response
    res.send(response.data)
  } catch (error) {
    console.error('Proxy error:', error)
    res.status(500).json({ error: 'Failed to proxy resource' })
  }
})

module.exports = router
```

#### NestJS Implementation:

```typescript
import {
  Controller,
  Get,
  Query,
  Res,
  HttpException,
  HttpStatus,
} from '@nestjs/common'
import { Response } from 'express'
import axios from 'axios'

@Controller()
export class ProxyController {
  @Get('proxy')
  async proxyResource(@Query('url') url: string, @Res() res: Response) {
    try {
      // Validate the URL
      if (!url) {
        throw new HttpException(
          'URL parameter is required',
          HttpStatus.BAD_REQUEST
        )
      }

      // Only allow proxying from your CDN domain
      if (!url.startsWith('https://cdn.jeridschool.tech/')) {
        throw new HttpException(
          'Only CDN URLs are allowed',
          HttpStatus.FORBIDDEN
        )
      }

      // Fetch the resource
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
      })

      // Set appropriate headers
      res.set('Content-Type', response.headers['content-type'])
      res.set('Cache-Control', 'public, max-age=86400')

      // Send the response
      res.send(response.data)
    } catch (error) {
      console.error('Proxy error:', error)
      throw new HttpException(
        'Failed to proxy resource',
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}
```

## Frontend Usage

Once the proxy endpoint is implemented, update the frontend code to use it:

```typescript
// In CdnImage.tsx
const getProxiedUrl = (url: string): string => {
  // Check if it's a CDN URL
  if (url.includes('cdn.jeridschool.tech')) {
    return `http://localhost:3000/proxy?url=${encodeURIComponent(url)}`
  }
  return url
}
```

## Security Considerations

1. **URL Validation**: Always validate the URL parameter to ensure it only points to your CDN
2. **Rate Limiting**: Implement rate limiting to prevent abuse
3. **Caching**: Consider implementing caching to improve performance
4. **Logging**: Log proxy requests for security auditing

## Performance Considerations

1. **Response Size**: Be aware that proxying large files can consume significant bandwidth
2. **Caching**: Implement proper caching headers to reduce load on your server
3. **CDN Headers**: Preserve CDN headers when proxying responses

## Conclusion

While adding CORS headers to the CDN server is the preferred solution, implementing a proxy endpoint provides a viable alternative when direct CDN configuration is not possible.
