# Card Template System Documentation

## Overview
The Card Template System enables educational establishments to create and manage custom card templates for their users (students and teachers). It provides a way to generate profile cards with consistent styling and information.

## Core Components

### 1. Card Template Structure
The card template system consists of the following interfaces:

#### CardTemplate Interface
```typescript
interface CardTemplate {
  id: string; // UUID
  name: string; // Template name
  previewImageUrl?: string; // URL to preview image
  config: CardTemplateConfig;
  etablissementId: string; // UUID of the establishment
  etablissement: Etablissement;
}
```

#### CardTemplateConfig Interface
```typescript
interface CardTemplateConfig {
  // Layout
  width?: number; // Card width in pixels (default: 300)
  height?: number; // Card height in pixels (default: 400)
  padding?: number; // Card padding in pixels (default: 20)

  // Colors
  backgroundColor?: string; // Card background color (default: #ffffff)
  textColor?: string; // Text color (default: #000000)
  borderColor?: string; // Border color (default: #ccc)

  // Typography
  fontFamily?: string; // Font family (default: Arial, sans-serif)
  fontSize?: number; // Base font size in pixels

  // Sections
  header?: {
    backgroundColor?: string;
    textColor?: string;
    padding?: number;
  };

  infoGroups?: {
    spacing?: number; // Vertical spacing between groups
    labelColor?: string;
  };
}
```

## API Services

### CardTemplateService
The system provides several key services:

#### Query Services
- `useCardTemplates()`: Fetches all card templates for the current establishment
- `useCardTemplate(id: string)`: Fetches a specific card template by ID

#### Mutation Services
- `useCreateCardTemplate()`: Creates a new card template
- `useUpdateCardTemplate()`: Updates an existing card template

## Frontend Components

### 1. Template Management
- **TemplateList Component**
  - Displays list of available templates
  - Shows template preview
  - Allows editing and deletion

- **TemplateEditor Component**
  - Visual editor for template configuration
  - Preview pane showing changes in real-time
  - Form for template metadata

### 2. Card Generation
- **CardPreview Component**
  - Shows generated card preview
  - Allows downloading as PDF
  - Shows user information

### 3. Template Preview
- Real-time preview generation
- Optional preview image URL
- PDF download capability

## Usage Flow
1. Establishments create card templates with desired configurations
2. Templates are associated with specific establishments
3. Users can generate profile cards using the configured templates
4. Cards can be previewed and downloaded as PDFs

This documentation provides a clear overview of the card template system's structure and functionality based on the actual implementation.
