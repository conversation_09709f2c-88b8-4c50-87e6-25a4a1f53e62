<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JeridSchool Subdomain Test</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #525fe1;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }
      .info-box {
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .info-item {
        margin-bottom: 10px;
      }
      .label {
        font-weight: bold;
        display: inline-block;
        width: 150px;
      }
      .value {
        font-family: monospace;
        background-color: #eee;
        padding: 2px 5px;
        border-radius: 3px;
      }
      .test-links {
        margin-top: 30px;
      }
      .test-links a {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 8px 15px;
        background-color: #525fe1;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-weight: 500;
      }
      .test-links a:hover {
        background-color: #4048b4;
      }
      button {
        background-color: #525fe1;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
      }
      button:hover {
        background-color: #4048b4;
      }
      .debug-section {
        margin-top: 30px;
        border-top: 2px solid #eee;
        padding-top: 20px;
      }
      .debug-log {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        height: 200px;
        overflow-y: auto;
        font-family: monospace;
        white-space: pre-wrap;
      }
      .success {
        color: green;
      }
      .error {
        color: red;
      }
    </style>
  </head>
  <body>
    <h1>JeridSchool Subdomain Test</h1>

    <div class="info-box">
      <div class="info-item">
        <span class="label">Full URL:</span>
        <span class="value" id="fullUrl"></span>
      </div>
      <div class="info-item">
        <span class="label">Hostname:</span>
        <span class="value" id="hostname"></span>
      </div>
      <div class="info-item">
        <span class="label">Port:</span>
        <span class="value" id="port"></span>
      </div>
      <div class="info-item">
        <span class="label">Is Subdomain:</span>
        <span class="value" id="isSubdomain"></span>
      </div>
      <div class="info-item">
        <span class="label">School Name:</span>
        <span class="value" id="schoolName"></span>
      </div>
      <div class="info-item">
        <span class="label">Path:</span>
        <span class="value" id="path"></span>
      </div>
      <div class="info-item">
        <span class="label">Query Parameters:</span>
        <span class="value" id="queryParams"></span>
      </div>
      <button onclick="refreshInfo()">Refresh Info</button>
    </div>

    <div class="test-links">
      <h2>Test Links</h2>
      <div id="subdomainLinks"></div>
    </div>

    <div class="debug-section">
      <h2>Debug Console</h2>
      <div class="debug-log" id="debugLog"></div>
      <button onclick="clearLog()">Clear Log</button>
      <button onclick="testSubdomainRedirect()">Test Subdomain Redirect</button>
    </div>

    <script>
      // Debug logging
      function log(message, type = 'info') {
        const logElement = document.getElementById('debugLog')
        const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
        const logEntry = document.createElement('div')
        logEntry.className = type
        logEntry.textContent = `[${timestamp}] ${message}`
        logElement.appendChild(logEntry)
        logElement.scrollTop = logElement.scrollHeight
      }

      function clearLog() {
        document.getElementById('debugLog').innerHTML = ''
        log('Log cleared')
      }

      // Check if current URL is a subdomain
      function isSubdomainUrl(hostname) {
        log(`Checking if ${hostname} is a subdomain`)

        // Check for subdomain in query parameters (from Vercel middleware)
        const urlParams = new URLSearchParams(window.location.search)
        const querySubdomain = urlParams.get('subdomain')
        if (querySubdomain) {
          log(`Detected subdomain from query parameter: ${querySubdomain}`)
          return true
        }

        // For development (localhost)
        if (hostname.includes('.localhost')) {
          log(`Detected development subdomain: ${hostname}`)
          return true
        }

        // For production (jeridschool.tech)
        if (
          hostname.includes('.jeridschool.tech') &&
          !hostname.startsWith('www.')
        ) {
          log(`Detected production subdomain: ${hostname}`)
          return true
        }

        log(`Not a subdomain: ${hostname}`)
        return false
      }

      // Extract school name from subdomain
      function getSchoolNameFromSubdomain(hostname) {
        log(`Extracting school name from: ${hostname}`)

        // Check for subdomain in query parameters (from Vercel middleware)
        const urlParams = new URLSearchParams(window.location.search)
        const querySubdomain = urlParams.get('subdomain')
        if (querySubdomain) {
          log(`Extracted school name from query parameter: ${querySubdomain}`)
          return querySubdomain
        }

        if (isSubdomainUrl(hostname)) {
          const schoolName = hostname.split('.')[0]
          log(`Extracted school name from hostname: ${schoolName}`)
          return schoolName
        }
        log('Not a subdomain, no school name extracted')
        return null
      }

      // Refresh the displayed information
      function refreshInfo() {
        log('Refreshing URL information')
        const url = window.location
        const hostname = url.hostname
        const port = url.port || (url.protocol === 'https:' ? '443' : '80')
        const subdomain = isSubdomainUrl(hostname)
        const school = getSchoolNameFromSubdomain(hostname)

        document.getElementById('fullUrl').textContent = url.href
        document.getElementById('hostname').textContent = hostname
        document.getElementById('port').textContent = port
        document.getElementById('isSubdomain').textContent = subdomain
          ? 'Yes'
          : 'No'
        document.getElementById('schoolName').textContent = school || 'N/A'
        document.getElementById('path').textContent = url.pathname
        document.getElementById('queryParams').textContent =
          url.search || 'None'

        generateTestLinks()
      }

      // Generate test links for subdomains
      function generateTestLinks() {
        log('Generating test links')
        const linksContainer = document.getElementById('subdomainLinks')
        linksContainer.innerHTML = ''

        const hostname = window.location.hostname
        const port = window.location.port ? `:${window.location.port}` : ''
        const protocol = window.location.protocol

        // Base domain (without subdomain)
        let baseDomain = hostname
        if (isSubdomainUrl(hostname)) {
          const parts = hostname.split('.')
          parts.shift() // Remove the subdomain part
          baseDomain = parts.join('.')
          log(`Determined base domain: ${baseDomain}`)
        }

        // Test schools
        const schools = ['myschool', 'dreamschool', 'brightfuture', 'ksqdsqd']

        // Add link to main domain
        const mainLink = document.createElement('a')
        mainLink.href = `${protocol}//${baseDomain}${port}`
        mainLink.textContent = `Main Domain (${baseDomain})`
        mainLink.target = '_blank'
        linksContainer.appendChild(mainLink)

        // Add links for each test school
        schools.forEach((school) => {
          const link = document.createElement('a')
          link.href = `${protocol}//${school}.${baseDomain}${port}`
          link.textContent = `${school}.${baseDomain}`
          link.target = '_blank'
          linksContainer.appendChild(link)

          // Also add a direct link to the school page
          const directLink = document.createElement('a')
          directLink.href = `${protocol}//${baseDomain}${port}/school/${school}`
          directLink.textContent = `${baseDomain}/school/${school}`
          directLink.target = '_blank'
          linksContainer.appendChild(directLink)
        })

        log(`Generated ${schools.length * 2 + 1} test links`)
      }

      // Test subdomain redirect functionality
      function testSubdomainRedirect() {
        log('Testing subdomain redirect functionality', 'info')

        const hostname = window.location.hostname
        if (!isSubdomainUrl(hostname)) {
          log('Not on a subdomain, cannot test redirect', 'error')
          return
        }

        const schoolName = getSchoolNameFromSubdomain(hostname)
        if (!schoolName) {
          log('Could not extract school name from subdomain', 'error')
          return
        }

        log(`Testing redirect for school: ${schoolName}`, 'info')

        // Check if we're already on a school route
        const path = window.location.pathname
        if (path.startsWith('/school/')) {
          log(`Already on a school route: ${path}`, 'success')
          return
        }

        // We should be redirected to the school page
        log(`Expecting redirect to: /school/${schoolName}`, 'info')

        // Check if the root route's beforeLoad redirect is working
        setTimeout(() => {
          const newPath = window.location.pathname
          if (newPath.startsWith('/school/')) {
            log(`Successfully redirected to: ${newPath}`, 'success')
          } else {
            log(`Redirect failed. Still at: ${newPath}`, 'error')
            log('Possible issues:', 'error')
            log('1. Root route beforeLoad not working', 'error')
            log('2. Redirect not being triggered', 'error')
            log('3. Router configuration issue', 'error')
          }
        }, 1000)
      }

      // Initialize on page load
      window.onload = function () {
        log('Page loaded')
        refreshInfo()
      }
    </script>
  </body>
</html>
