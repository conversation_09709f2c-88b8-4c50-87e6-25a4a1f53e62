import { useGLTF } from "@react-three/drei";
import { GroupProps } from "@react-three/fiber";
import { Mesh } from "three";
import modelPath from "./classroom.glb";

export function Classroom(props: GroupProps) {
  const { nodes, materials } = useGLTF(modelPath);

  return (
    <group {...props} dispose={null}>
      <group rotation={[-Math.PI / 2, 0, 0]}>
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_2 as Mesh).geometry}
          material={materials["Bag2Mat.001"]}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_3 as Mesh).geometry}
          material={materials.Bag1Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_4 as Mesh).geometry}
          material={materials.BoardMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_5 as Mesh).geometry}
          material={materials.Bag5Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_6 as Mesh).geometry}
          material={materials.Book1Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_7 as Mesh).geometry}
          material={materials["Bag3Mat.001"]}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_8 as Mesh).geometry}
          material={materials.BooksMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_9 as Mesh).geometry}
          material={materials.BookShelfMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_10 as Mesh).geometry}
          material={materials.CellingMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_11 as Mesh).geometry}
          material={materials.ClockMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_12 as Mesh).geometry}
          material={materials.CardboardBoxMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_13 as Mesh).geometry}
          material={materials.CracksMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_14 as Mesh).geometry}
          material={materials.DecalsMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_15 as Mesh).geometry}
          material={materials.FloorMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_16 as Mesh).geometry}
          material={materials.GlassMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_17 as Mesh).geometry}
          material={materials.LockerMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_18 as Mesh).geometry}
          material={materials.Lights}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_19 as Mesh).geometry}
          material={materials.GlobeMat}
        />
        <lineSegments
          geometry={(nodes.Object_20 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_21 as Mesh).geometry}
          material={materials.MapMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_22 as Mesh).geometry}
          material={materials.None}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_23 as Mesh).geometry}
          material={materials.Pencil1Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_24 as Mesh).geometry}
          material={materials.PencilsMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_25 as Mesh).geometry}
          material={materials.Plant3Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_26 as Mesh).geometry}
          material={materials.TeachersDeskMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_27 as Mesh).geometry}
          material={materials["TeachersDeskMat.001"]}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_28 as Mesh).geometry}
          material={materials.WinodwsMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_29 as Mesh).geometry}
          material={materials.WallPartsMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_30 as Mesh).geometry}
          material={materials.WallsMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_31 as Mesh).geometry}
          material={materials.TrashBinMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_32 as Mesh).geometry}
          material={materials.chalkboardMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_33 as Mesh).geometry}
          material={materials.Bag4Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_34 as Mesh).geometry}
          material={materials.CurtainMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_35 as Mesh).geometry}
          material={materials.Desk1Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_36 as Mesh).geometry}
          material={materials.Desk2Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_37 as Mesh).geometry}
          material={materials.Desk3Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_38 as Mesh).geometry}
          material={materials.Desk4Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_39 as Mesh).geometry}
          material={materials["DoorMat.002"]}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_40 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_41 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_42 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_43 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_44 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_45 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_46 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_47 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_48 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_49 as Mesh).geometry}
          material={materials.Outline}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_50 as Mesh).geometry}
          material={materials.PapersMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_51 as Mesh).geometry}
          material={materials.PapersMat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_52 as Mesh).geometry}
          material={materials.Plant1Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_53 as Mesh).geometry}
          material={materials.Plant2Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_54 as Mesh).geometry}
          material={materials.Plant4Mat}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={(nodes.Object_55 as Mesh).geometry}
          material={materials.TeachersChairMat}
        />
      </group>
    </group>
  );
}

useGLTF.preload(modelPath);
