import { useEffect, useRef } from "react";
import { useGLTF, useAnimations } from "@react-three/drei";
import { GroupProps } from "@react-three/fiber";
import { AnimationClip, Group, SkinnedMesh } from "three";
import modelPath from "./emilian-avatar.glb";

export function Emilian(props: GroupProps) {
  const group = useRef<Group>(null);
  const { nodes, materials, animations } = useGLTF(modelPath);
  const { mixer } = useAnimations(animations, group);
  console.log("ANIMATIONS", animations);

  useEffect(() => {
    const idleAnimation = animations.find((a: AnimationClip) => a.name === "IdleV4.2(maya_head)");
    if (idleAnimation) {
      mixer.clipAction(idleAnimation).play();
    }
  }, [mixer, animations]);

  return (
    <group ref={group} {...props} dispose={null}>
      <group name="Scene">
        <group name="emilian-ka<PERSON><PERSON>">
          <skinnedMesh
            name="avaturn_body"
            geometry={(nodes.avaturn_body as SkinnedMesh).geometry}
            material={materials.avaturn_body_material}
            skeleton={(nodes.avaturn_body as SkinnedMesh).skeleton}
          />
          <skinnedMesh
            name="avaturn_hair_0"
            geometry={(nodes.avaturn_hair_0 as SkinnedMesh).geometry}
            material={materials.avaturn_hair_0_material}
            skeleton={(nodes.avaturn_hair_0 as SkinnedMesh).skeleton}
          />
          <skinnedMesh
            name="avaturn_look_0"
            geometry={(nodes.avaturn_look_0 as SkinnedMesh).geometry}
            material={materials.avaturn_look_0_material}
            skeleton={(nodes.avaturn_look_0 as SkinnedMesh).skeleton}
          />
          <skinnedMesh
            name="avaturn_shoes_0"
            geometry={(nodes.avaturn_shoes_0 as SkinnedMesh).geometry}
            material={materials.avaturn_shoes_0_material}
            skeleton={(nodes.avaturn_shoes_0 as SkinnedMesh).skeleton}
          />
          <primitive object={nodes.Hips} />
        </group>
      </group>
    </group>
  );
}

useGLTF.preload(modelPath);
