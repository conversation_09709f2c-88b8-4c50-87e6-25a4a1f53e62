<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JeridSchool - Subdomain Redirect</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
          Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
        color: #333;
      }
      .container {
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-top: 40px;
      }
      h1 {
        color: #525fe1;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }
      .info-box {
        background-color: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .info-item {
        margin-bottom: 10px;
      }
      .label {
        font-weight: bold;
        display: inline-block;
        width: 150px;
      }
      .value {
        font-family: monospace;
        background-color: #f5f5f5;
        padding: 2px 5px;
        border-radius: 3px;
      }
      .button {
        display: inline-block;
        background-color: #525fe1;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 500;
        margin-top: 20px;
      }
      .button:hover {
        background-color: #4048b4;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>JeridSchool Subdomain Redirect</h1>

      <div class="info-box">
        <h2>URL Information</h2>
        <div class="info-item">
          <span class="label">Hostname:</span>
          <span class="value" id="hostname"></span>
        </div>
        <div class="info-item">
          <span class="label">Subdomain:</span>
          <span class="value" id="subdomain"></span>
        </div>
        <div class="info-item">
          <span class="label">Full URL:</span>
          <span class="value" id="fullUrl"></span>
        </div>
        <div class="info-item">
          <span class="label">Query Parameters:</span>
          <span class="value" id="queryParams"></span>
        </div>
      </div>

      <p>
        This page will redirect you to the appropriate school page based on the
        subdomain.
      </p>
      <p>Redirecting in <span id="countdown">5</span> seconds...</p>

      <a href="/" class="button" id="cancelButton">Cancel Redirect</a>
    </div>

    <script>
      // Get URL information
      const hostname = window.location.hostname
      const fullUrl = window.location.href
      const queryParams = window.location.search

      // Extract subdomain from hostname or query parameters
      let subdomain = null

      // Check query parameters first (for Vercel middleware)
      const urlParams = new URLSearchParams(queryParams)
      const querySubdomain = urlParams.get('subdomain')

      if (querySubdomain) {
        subdomain = querySubdomain
      } else if (hostname.includes('.jeridschool.tech')) {
        // Extract from hostname for direct subdomain access
        const parts = hostname.split('.')
        if (parts.length >= 2 && parts[0] !== 'www') {
          subdomain = parts[0]
        }
      } else if (hostname.includes('.localhost')) {
        // For local development
        const parts = hostname.split('.')
        if (parts.length >= 2 && parts[0] !== 'www') {
          subdomain = parts[0]
        }
      }

      // Update the UI
      document.getElementById('hostname').textContent = hostname
      document.getElementById('subdomain').textContent =
        subdomain || 'None detected'
      document.getElementById('fullUrl').textContent = fullUrl
      document.getElementById('queryParams').textContent = queryParams || 'None'

      // Set up countdown
      let countdown = 5
      const countdownElement = document.getElementById('countdown')

      // Cancel button
      document
        .getElementById('cancelButton')
        .addEventListener('click', function (e) {
          e.preventDefault()
          clearInterval(countdownInterval)
          countdownElement.textContent = 'Cancelled'
        })

      // Countdown interval
      const countdownInterval = setInterval(function () {
        countdown--
        countdownElement.textContent = countdown

        if (countdown <= 0) {
          clearInterval(countdownInterval)

          // Redirect to the appropriate page
          if (subdomain) {
            window.location.href = `/school/${subdomain}`
          } else {
            window.location.href = '/'
          }
        }
      }, 1000)
    </script>
  </body>
</html>
