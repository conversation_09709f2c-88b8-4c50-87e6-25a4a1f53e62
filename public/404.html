<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JeridSchool - Subdomain Routing</title>
    <script>
      // Extract subdomain from hostname
      function getSubdomain() {
        const hostname = window.location.hostname

        // Check if we're on a subdomain
        if (
          hostname.includes('.jeridschool.tech') &&
          !hostname.startsWith('www.')
        ) {
          return hostname.split('.')[0]
        }

        if (hostname.includes('.localhost') && !hostname.startsWith('www.')) {
          return hostname.split('.')[0]
        }

        return null
      }

      // Get the subdomain
      const subdomain = getSubdomain()

      // Store the current URL in sessionStorage
      sessionStorage.setItem('redirectUrl', window.location.href)
      sessionStorage.setItem('subdomain', subdomain || '')

      // Redirect to the main app with subdomain parameter if available
      if (subdomain) {
        window.location.href = `/?subdomain=${subdomain}`
      } else {
        window.location.href = '/'
      }
    </script>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
          Arial, sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        text-align: center;
        background-color: #f7f7f7;
      }
      .container {
        max-width: 500px;
        padding: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #525fe1;
        margin-bottom: 1rem;
      }
      p {
        color: #666;
        margin-bottom: 2rem;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>JeridSchool Subdomain Routing</h1>
      <p>Detecting subdomain and redirecting to the appropriate page...</p>
      <p id="subdomain-info">Subdomain: Detecting...</p>
      <p>Redirecting to the homepage with subdomain parameter...</p>
      <p>
        If you are not redirected automatically, <a href="/">click here</a>.
      </p>
    </div>

    <script>
      // Update the subdomain info
      const subdomainInfo = document.getElementById('subdomain-info')
      const subdomain = sessionStorage.getItem('subdomain')
      if (subdomain) {
        subdomainInfo.textContent = `Subdomain: ${subdomain}`
      } else {
        subdomainInfo.textContent = 'Subdomain: None detected'
      }
    </script>
  </body>
</html>
