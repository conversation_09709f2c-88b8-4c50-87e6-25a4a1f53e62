{"name": "jeridschool_main_interface", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . ", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.4", "@excalidraw/excalidraw": "^0.17.6", "@hookform/resolvers": "^5.0.1", "@lucide/lab": "^0.1.2", "@marsidev/react-turnstile": "^1.1.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.102.6", "@react-three/fiber": "^8.18.0", "@shadcn/ui": "^0.0.4", "@tanstack/react-form": "^0.39.0", "@tanstack/react-query": "^5.62.2", "@tanstack/react-query-devtools": "^5.62.2", "@tanstack/react-router": "^1.85.5", "@tanstack/react-table": "^8.20.6", "@tanstack/react-virtual": "^3.13.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/screenfull": "^4.0.0", "@types/uuid": "^10.0.0", "antd": "^5.24.5", "axios": "^1.7.9", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "gsap": "^3.12.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "jsbarcode": "^3.11.6", "jspdf": "^2.5.2", "lucide-react": "^0.484.0", "node-fetch": "^3.3.2", "process": "^0.11.10", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-joyride": "^2.9.3", "react-label": "^0.0.6", "react-markdown": "^10.1.0", "react-router-dom": "^7.0.2", "screenfull": "^6.0.2", "shadcn-ui": "^0.9.5", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.159.0", "uuid": "^11.1.0", "vercel": "^41.6.1", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tanstack/router-devtools": "^1.85.5", "@tanstack/router-plugin": "^1.85.3", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001707", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}